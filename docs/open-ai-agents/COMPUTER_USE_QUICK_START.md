# Computer Use Quick Start Guide

## Overview

Dante now includes advanced computer use capabilities powered by OpenAI's Computer-Using Agent (CUA) model. This allows <PERSON> to control browsers and desktop environments to perform visual tasks, form filling, and web interactions.

## Quick Start

### 1. Prerequisites

Ensure you have:
- OpenAI API key with access to `computer-use-preview` model
- Playwright installed (done automatically with `bun install`)
- Docker (optional, for desktop automation)

### 2. Enable Computer Use

Computer use is automatically available in Dante. Simply ask for browser automation tasks:

```
"Navigate to example.com and take a screenshot"
"Fill out the contact form on the website"
"Click the login button and show me what happens"
```

### 3. Example Usage

**Browser Automation:**
```
User: "Go to github.com and search for playwright"
Dante: I'll help you navigate to GitHub and search for <PERSON><PERSON> using the browser automation capabilities.

[<PERSON> automatically creates a computer use session, navigates to GitHub, performs the search, and provides screenshots]
```

**Form Filling:**
```
User: "Fill out the contact form at example.com with my information"
Dante: I can help you fill out the contact form. I'll need to access the website and locate the form fields.

[Safety check triggered for form data entry - user confirmation required]

Safety Check: The system detected you're about to enter information into a web form. Please confirm you want to proceed.

User: "Yes, proceed"
[<PERSON> fills out the form with provided information]
```

## Safety Features

### Automatic Safety Checks
- Domain validation (blocked/allowed lists)
- Sensitive content detection
- Form data analysis
- Human-in-the-loop confirmations

### User Controls
- Session monitoring dashboard
- Real-time screenshot viewing
- Safety check acknowledgments
- Emergency stop functionality

## Available Commands

### npm Scripts
```bash
# Build Docker environment (optional)
bun run computer-use:build

# Start Docker environment
bun run computer-use:run

# Stop Docker environment
bun run computer-use:stop
```

### Direct Tool Usage
```typescript
import { computerUseTool } from './src/tools/computerUse/computerUseTool';

// Start a session
const session = await computerUseTool.execute({
  action: 'start_session',
  environmentType: 'browser',
  userPrompt: 'Navigate to example.com'
});

// Execute task
const result = await computerUseTool.execute({
  action: 'execute_task',
  sessionId: session.sessionId,
  userPrompt: 'Click the login button'
});
```

## Environment Types

### Browser (Recommended)
- Uses Playwright for web automation
- Supports all modern browsers
- Handles JavaScript and dynamic content
- Most reliable for web-based tasks

### Docker (Advanced)
- Full desktop environment simulation
- System-level interactions
- Requires VNC for viewing
- Best for desktop application control

## Troubleshooting

### Common Issues

**"Browser won't start"**
```bash
bunx playwright install
```

**"OpenAI API errors"**
- Verify your API key has access to `computer-use-preview` model
- Check rate limits and quotas

**"Safety checks keep appearing"**
- Review domain configuration in safety settings
- Acknowledge checks explicitly when safe
- Check for sensitive content in forms

### Getting Help

1. Check the [full documentation](./computer-use-integration.md)
2. Review example usage in tests
3. Monitor console output for detailed errors
4. Use the UI monitoring components to debug sessions

## Advanced Features

### Custom Safety Configuration
```typescript
const safetyConfig = {
  allowedDomains: ['example.com'],
  blockedDomains: ['blocked-site.com'],
  enableContentAnalysis: true,
  maxActionsPerSession: 50
};
```

### Session Management
```typescript
import { useComputerUseStore } from '@/ui/stores/computerUseStore';

const {
  createSession,
  getActiveSession,
  closeSession
} = useComputerUseStore();
```

### UI Integration
```tsx
import { ComputerUseMonitor } from '@/ui/components/ComputerUse/ComputerUseMonitor';

<ComputerUseMonitor
  session={activeSession}
  onAcknowledgeChecks={handleSafetyChecks}
  onCloseSession={handleClose}
/>
```

## Best Practices

1. **Start Simple**: Begin with basic navigation tasks
2. **Use Safety Features**: Always review safety checks
3. **Monitor Sessions**: Keep the monitoring UI open during automation
4. **Respect Limits**: Be mindful of rate limits and terms of service
5. **Secure Credentials**: Never store credentials in automation scripts
6. **Test First**: Verify actions on test sites before production

## Next Steps

- Explore the [complete integration guide](./computer-use-integration.md)
- Review the [API reference](./computer-use-integration.md#api-reference)
- Try the Docker environment for desktop automation
- Integrate with your custom workflows

Computer use capabilities open up powerful automation possibilities while maintaining safety and user control. Start with simple tasks and gradually explore more complex workflows!