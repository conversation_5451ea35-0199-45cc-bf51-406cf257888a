Pricing
=======

### 

Text tokens

Prices per 1M tokens.

Batch

|Model|Input|Cached input|Output|
|---|---|---|---|
|gpt-5|$0.625|$0.0625|$5.00|
|gpt-5-mini|$0.125|$0.0125|$1.00|
|gpt-5-nano|$0.025|$0.0025|$0.20|
|gpt-4.1|$1.00|-|$4.00|
|gpt-4.1-mini|$0.20|-|$0.80|
|gpt-4.1-nano|$0.05|-|$0.20|
|gpt-4o|$1.25|-|$5.00|
|gpt-4o-2024-05-13|$2.50|-|$7.50|
|gpt-4o-mini|$0.075|-|$0.30|
|o1|$7.50|-|$30.00|
|o1-pro|$75.00|-|$300.00|
|o3-pro|$10.00|-|$40.00|
|o3|$1.00|-|$4.00|
|o3-deep-research|$5.00|-|$20.00|
|o4-mini|$0.55|-|$2.20|
|o4-mini-deep-research|$1.00|-|$4.00|
|o3-mini|$0.55|-|$2.20|
|o1-mini|$0.55|-|$2.20|
|computer-use-preview|$1.50|-|$6.00|

Flex

|Model|Input|Cached input|Output|
|---|---|---|---|
|gpt-5|$0.625|$0.0625|$5.00|
|gpt-5-mini|$0.125|$0.0125|$1.00|
|gpt-5-nano|$0.025|$0.0025|$0.20|
|o3|$1.00|$0.25|$4.00|
|o4-mini|$0.55|$0.138|$2.20|

Standard

|Model|Input|Cached input|Output|
|---|---|---|---|
|gpt-5|$1.25|$0.125|$10.00|
|gpt-5-mini|$0.25|$0.025|$2.00|
|gpt-5-nano|$0.05|$0.005|$0.40|
|gpt-5-chat-latest|$1.25|$0.125|$10.00|
|gpt-4.1|$2.00|$0.50|$8.00|
|gpt-4.1-mini|$0.40|$0.10|$1.60|
|gpt-4.1-nano|$0.10|$0.025|$0.40|
|gpt-4o|$2.50|$1.25|$10.00|
|gpt-4o-2024-05-13|$5.00|-|$15.00|
|gpt-4o-audio-preview|$2.50|-|$10.00|
|gpt-4o-realtime-preview|$5.00|$2.50|$20.00|
|gpt-4o-mini|$0.15|$0.075|$0.60|
|gpt-4o-mini-audio-preview|$0.15|-|$0.60|
|gpt-4o-mini-realtime-preview|$0.60|$0.30|$2.40|
|o1|$15.00|$7.50|$60.00|
|o1-pro|$150.00|-|$600.00|
|o3-pro|$20.00|-|$80.00|
|o3|$2.00|$0.50|$8.00|
|o3-deep-research|$10.00|$2.50|$40.00|
|o4-mini|$1.10|$0.275|$4.40|
|o4-mini-deep-research|$2.00|$0.50|$8.00|
|o3-mini|$1.10|$0.55|$4.40|
|o1-mini|$1.10|$0.55|$4.40|
|codex-mini-latest|$1.50|$0.375|$6.00|
|gpt-4o-mini-search-preview|$0.15|-|$0.60|
|gpt-4o-search-preview|$2.50|-|$10.00|
|computer-use-preview|$3.00|-|$12.00|
|gpt-image-1|$5.00|$1.25|-|

Priority

|Model|Input|Cached input|Output|
|---|---|---|---|
|gpt-5|$2.50|$0.25|$20.00|
|gpt-5-mini|$0.45|$0.05|$3.60|

For faster processing of API requests, try the [priority processing service tier](/docs/guides/priority-processing). For lower prices with higher latency, try the [flex processing tier](/docs/guides/flex-processing).

Large numbers of API requests which are not time-sensitive can use the [Batch API](/docs/guides/batch) for additional savings as well.

  

### 

Image tokens

Prices per 1M tokens.

|Model|Input|Cached Input|Output|
|---|---|---|---|
|gpt-image-1|$10.00|$2.50|$40.00|

  

### 

Audio tokens

Prices per 1M tokens.

|Model|Input|Cached Input|Output|
|---|---|---|---|
|gpt-4o-audio-preview|$40.00|-|$80.00|
|gpt-4o-mini-audio-preview|$10.00|-|$20.00|
|gpt-4o-realtime-preview|$40.00|$2.50|$80.00|
|gpt-4o-mini-realtime-preview|$10.00|$0.30|$20.00|

  

### 

Fine-tuning

Prices per 1M tokens.

|Model|Training|Input|Cached Input|Output|
|---|---|---|---|---|
|o4-mini-2025-04-16|$100.00 / hour|$4.00|$1.00|$16.00|
|o4-mini-2025-04-16with data sharing|$100.00 / hour|$2.00|$0.50|$8.00|
|gpt-4.1-2025-04-14|$25.00|$3.00|$0.75|$12.00|
|gpt-4.1-mini-2025-04-14|$5.00|$0.80|$0.20|$3.20|
|gpt-4.1-nano-2025-04-14|$1.50|$0.20|$0.05|$0.80|
|gpt-4o-2024-08-06|$25.00|$3.75|$1.875|$15.00|
|gpt-4o-mini-2024-07-18|$3.00|$0.30|$0.15|$1.20|
|gpt-3.5-turbo|$8.00|$3.00|-|$6.00|
|davinci-002|$6.00|$12.00|-|$12.00|
|babbage-002|$0.40|$1.60|-|$1.60|

Tokens used for model grading in reinforcement fine-tuning are billed at that model's per-token rate. Inference discounts are available if you enable data sharing when creating the fine-tune job. [Learn more](https://help.openai.com/en/articles/10306912-sharing-feedback-evaluation-and-fine-tuning-data-and-api-inputs-and-outputs-with-openai#h_c93188c569).

  

### 

Built-in tools

|Tool|Cost|
|---|---|
|Code Interpreter|$0.03 / container|
|File search storage|$0.10 / GB per day (1GB free)|
|File search tool callResponses API only|$2.50 / 2k calls|
|Web search (gpt-4o and gpt-4.1) [1]|$25.00 / 1k calls|
|Web search (gpt-5, o-series) [2]|$10.00 / 1k calls|

The tokens used for built-in tools are billed at the chosen model's per-token rates. GB refers to binary gigabytes of storage (also known as gibibyte), where 1GB is 2^30 bytes.

**Web search content tokens:** Search content tokens are the tokens retrieved from the search index and fed to the model alongside your prompt to generate an answer. For gpt-4o and gpt-4.1 models, these tokens are included in the $25/1K calls cost. For o3 and o4-mini models, you're billed for these tokens at input token rates on top of the $10/1K calls cost.

\[1\] includes all gpt-4o, gpt-4.1, gpt-4o-mini, and gpt-4.1-mini models. Search content tokens are free.

\[2\] includes all gpt-5, gpt-5-mini, gpt-5-nano, and o-series models. Search content tokens are billed at the model's text token rates.

  

### 

Transcription and speech generation

Prices per 1M tokens.

#### Text tokens

|Model|Input|Output|Estimated cost|
|---|---|---|---|
|gpt-4o-mini-tts|$0.60|-|$0.015 / minute|
|gpt-4o-transcribe|$2.50|$10.00|$0.006 / minute|
|gpt-4o-mini-transcribe|$1.25|$5.00|$0.003 / minute|

#### Audio tokens

|Model|Input|Output|Estimated cost|
|---|---|---|---|
|gpt-4o-mini-tts|-|$12.00|$0.015 / minute|
|gpt-4o-transcribe|$6.00|-|$0.006 / minute|
|gpt-4o-mini-transcribe|$3.00|-|$0.003 / minute|

#### Other models

|Model|Use case|Cost|
|---|---|---|
|Whisper|Transcription|$0.006 / minute|
|TTS|Speech generation|$15.00 / 1M characters|
|TTS HD|Speech generation|$30.00 / 1M characters|

  

### 

Image generation

Prices per image.

|Model|Quality|1024 x 1024|1024 x 1536|1536 x 1024|
|---|---|---|---|---|
|GPT Image 1|Low|$0.011|$0.016|$0.016|
|Medium|$0.042|$0.063|$0.063|
|High|$0.167|$0.25|$0.25|
||
||
|DALL·E 3|Standard|$0.04|$0.08|$0.08|
|HD|$0.08|$0.12|$0.12|
||
||
|DALL·E 2|Standard|$0.016|$0.018|$0.02|

  

### 

Embeddings

Prices per 1M tokens.

|Model|Cost|Batch cost|
|---|---|---|
|text-embedding-3-small|$0.01|$0.0001|
|text-embedding-3-large|$0.065|$0.00013|
|text-embedding-ada-002|$0.05|$0.0004|

  

### Moderation

Our `omni-moderation` models are made available free of charge ✌️

  

### 

Legacy models

Prices per 1M tokens.

Batch

|Model|Input|Output|
|---|---|---|
|gpt-4-turbo-2024-04-09|$5.00|$15.00|
|gpt-4-0125-preview|$5.00|$15.00|
|gpt-4-1106-preview|$5.00|$15.00|
|gpt-4-1106-vision-preview|$5.00|$15.00|
|gpt-4-0613|$15.00|$30.00|
|gpt-4-0314|$15.00|$30.00|
|gpt-4-32k|$30.00|$60.00|
|gpt-3.5-turbo-0125|$0.25|$0.75|
|gpt-3.5-turbo-1106|$1.00|$2.00|
|gpt-3.5-turbo-0613|$1.50|$2.00|
|gpt-3.5-0301|$1.50|$2.00|
|gpt-3.5-turbo-16k-0613|$1.50|$2.00|
|davinci-002|$1.00|$1.00|
|babbage-002|$0.20|$0.20|

Standard

|Model|Input|Output|
|---|---|---|
|chatgpt-4o-latest|$5.00|$15.00|
|gpt-4-turbo-2024-04-09|$10.00|$30.00|
|gpt-4-0125-preview|$10.00|$30.00|
|gpt-4-1106-preview|$10.00|$30.00|
|gpt-4-1106-vision-preview|$10.00|$30.00|
|gpt-4-0613|$30.00|$60.00|
|gpt-4-0314|$30.00|$60.00|
|gpt-4-32k|$60.00|$120.00|
|gpt-3.5-turbo|$0.50|$1.50|
|gpt-3.5-turbo-0125|$0.50|$1.50|
|gpt-3.5-turbo-1106|$1.00|$2.00|
|gpt-3.5-turbo-0613|$1.50|$2.00|
|gpt-3.5-0301|$1.50|$2.00|
|gpt-3.5-turbo-instruct|$1.50|$2.00|
|gpt-3.5-turbo-16k-0613|$3.00|$4.00|
|davinci-002|$2.00|$2.00|
|babbage-002|$0.40|$0.40|
