# Computer Use Integration Guide

This document provides a comprehensive guide to <PERSON>'s Computer Use capabilities, powered by OpenAI's Computer-Using Agent (CUA) model.

## Overview

Dante's Computer Use integration allows AI-driven automation of browser interactions and desktop environments. This feature enables <PERSON> to:

- Navigate websites and interact with web pages
- Fill out forms and submit data
- Click buttons, links, and UI elements
- Take screenshots for analysis and verification
- Perform multi-step web workflows
- Automate repetitive web-based tasks

## Architecture

### Core Components

1. **Computer Use Tool** (`src/tools/computerUse/computerUseTool.ts`)
   - Main interface to OpenAI's Responses API
   - Session management and coordination
   - Safety check handling

2. **Environment Manager** (`src/tools/computerUse/environmentManager.ts`)
   - Browser automation via Playwright
   - Docker container management for system-level control
   - Action execution and screenshot capture

3. **Safety Framework** (`src/tools/computerUse/safetyFramework.ts`)
   - Domain allowlists and blocklists
   - Content analysis and filtering
   - Action logging and audit trails

4. **Computer Use Agent** (`src/agents/ComputerUseAgent.ts`)
   - Specialized agent for computer automation tasks
   - Integrated with <PERSON>'s multi-agent architecture
   - Human-in-the-loop confirmations

### Integration Flow

```mermaid
graph TD
    A[User Request] --> B[Dante Core Agent]
    B --> C{Computer Use Task?}
    C -->|Yes| D[Computer Use Agent]
    C -->|No| E[Other Specialized Agent]
    D --> F[Computer Use Tool]
    F --> G[Environment Manager]
    G --> H{Environment Type}
    H -->|Browser| I[Playwright Browser]
    H -->|Desktop| J[Docker Container]
    I --> K[Execute Action]
    J --> K
    K --> L[Capture Screenshot]
    L --> M[Safety Validation]
    M --> N{Safety Checks?}
    N -->|Yes| O[Request User Confirmation]
    N -->|No| P[Continue Execution]
    O --> Q[User Decision]
    Q -->|Approve| P
    Q -->|Deny| R[Abort Task]
    P --> S[OpenAI CUA Model]
    S --> T[Next Action or Complete]
```

## Installation and Setup

### Prerequisites

1. **OpenAI API Key** with access to `computer-use-preview` model
2. **Playwright** for browser automation
3. **Docker** for desktop environment simulation (optional)

### Installation

Dependencies are automatically installed with the main project:

```bash
bun install  # Installs playwright, dockerode, and other dependencies
bunx playwright install  # Installs browser binaries
```

### Configuration

Update your `.env` file:

```env
OPENAI_API_KEY=your_openai_api_key_here
# Ensure the key has access to computer-use-preview model
```

## Usage

### Basic Browser Automation

```typescript
// Example: Navigate to a website and take a screenshot
const result = await computerUseTool.execute({
  action: 'execute_task',
  environmentType: 'browser',
  displayWidth: 1024,
  displayHeight: 768,
  initialUrl: 'https://example.com',
  userPrompt: 'Navigate to the homepage and take a screenshot'
});

console.log(result.screenshot); // Base64 encoded screenshot
```

### Multi-Step Workflow

```typescript
// Start a session
const session = await computerUseTool.execute({
  action: 'start_session',
  environmentType: 'browser',
  initialUrl: 'https://example.com',
  userPrompt: 'Fill out the contact form'
});

// Execute multiple steps
const step1 = await computerUseTool.execute({
  action: 'execute_task',
  sessionId: session.sessionId,
  userPrompt: 'Click on the Contact Us link'
});

const step2 = await computerUseTool.execute({
  action: 'execute_task',
  sessionId: session.sessionId,
  userPrompt: 'Fill in the name field with "John Doe"'
});

// Close session when done
await computerUseTool.execute({
  action: 'close_session',
  sessionId: session.sessionId
});
```

### Using with Dante Agent System

```typescript
// Through natural language - Dante will automatically route to Computer Use Agent
const response = await danteCore.process({
  messages: [{
    role: 'user',
    content: 'Please go to example.com and fill out their contact form with my information'
  }]
});
```

### Safety and Confirmation Workflows

```typescript
const result = await computerUseTool.execute({
  action: 'execute_task',
  environmentType: 'browser',
  userPrompt: 'Navigate to my bank website'
});

if (result.status === 'safety_check_required') {
  console.log('Safety checks required:', result.safetyChecks);
  
  // User reviews and approves safety checks
  const continuedResult = await computerUseTool.execute({
    action: 'acknowledge_safety',
    sessionId: result.sessionId,
    acknowledgedSafetyChecks: result.safetyChecks
  });
}
```

## Safety Features

### Domain Controls

Configure allowed and blocked domains in the safety framework:

```typescript
const safetyConfig = {
  allowedDomains: ['example.com', 'trusted-site.com'],
  blockedDomains: ['malicious-site.com', 'phishing-site.com'],
  enableDomainChecking: true
};
```

### Content Analysis

Automatic detection of:
- Credit card numbers
- SSNs and personal identifiers
- Passwords and API keys
- Sensitive keywords

### Action Logging

All actions are logged with:
- Timestamp
- Action details (click coordinates, typed text, etc.)
- Screenshots before/after
- URLs visited
- Safety check results

### Human-in-the-Loop

Critical actions require user confirmation:
- Accessing sensitive domains (banking, government)
- Entering potentially sensitive information
- Executing system-level commands
- Actions flagged by safety checks

## Docker Environment Setup

For desktop automation beyond browser tasks:

### Build the Container

```bash
cd docker/computer-use
docker-compose up -d
```

### Connect via VNC

- **Server:** `localhost:5900`
- **Password:** `dante`
- Use any VNC client to view/control the desktop

### Using Docker Environment

```typescript
const result = await computerUseTool.execute({
  action: 'execute_task',
  environmentType: 'docker',
  userPrompt: 'Open the file manager and create a new folder'
});
```

## UI Components

### Computer Use Monitor

React component for real-time monitoring:

```tsx
import { ComputerUseMonitor } from '@/ui/components/ComputerUse/ComputerUseMonitor';

function App() {
  const activeSession = useActiveSession();
  
  return (
    <ComputerUseMonitor
      session={activeSession}
      onAcknowledgeChecks={handleSafetyChecks}
      onCloseSession={handleCloseSession}
    />
  );
}
```

### State Management

Zustand store for session management:

```typescript
import { useComputerUseStore } from '@/ui/stores/computerUseStore';

const {
  createSession,
  updateSession,
  getActiveSession
} = useComputerUseStore();
```

## API Reference

### Computer Use Tool Actions

#### `start_session`
Create a new computer use session.

**Parameters:**
- `environmentType`: 'browser' | 'docker'
- `displayWidth?`: number (default: 1024)
- `displayHeight?`: number (default: 768)
- `initialUrl?`: string (browser only)
- `userPrompt`: string

#### `execute_task`
Execute a computer use task.

**Parameters:**
- `sessionId?`: string (optional, creates new session if not provided)
- `environmentType`: 'browser' | 'docker'
- `userPrompt`: string
- Additional session parameters...

#### `acknowledge_safety`
Acknowledge safety checks to continue execution.

**Parameters:**
- `sessionId`: string
- `acknowledgedSafetyChecks`: SafetyCheck[]

#### `continue_session`
Continue session with updated screenshot.

**Parameters:**
- `sessionId`: string
- `callId`: string

#### `close_session`
Close and cleanup a session.

**Parameters:**
- `sessionId`: string

### Response Format

```typescript
interface ComputerUseResponse {
  sessionId: string;
  status: 'success' | 'error' | 'safety_check_required' | 'confirmation_required';
  screenshot?: string; // base64
  currentUrl?: string;
  actions?: ComputerAction[];
  safetyChecks?: SafetyCheck[];
  message?: string;
  error?: string;
  reasoning?: string;
}
```

## Best Practices

### Security
1. Always use sandbox environments for untrusted websites
2. Configure domain allowlists for production usage
3. Regularly review action logs for suspicious activity
4. Implement rate limiting for automated tasks
5. Never store sensitive credentials in automation scripts

### Performance
1. Use appropriate display resolutions (1024x768 recommended)
2. Close sessions when complete to free resources
3. Implement timeout mechanisms for long-running tasks
4. Monitor memory usage with multiple concurrent sessions

### Reliability
1. Handle safety checks gracefully
2. Implement retry logic for transient failures
3. Validate action success through screenshots
4. Provide clear error messages to users

### User Experience
1. Show real-time progress with screenshots
2. Explain safety concerns clearly
3. Allow manual intervention when needed
4. Provide session history and logs

## Troubleshooting

### Common Issues

#### Browser Won't Start
```bash
# Install dependencies
bunx playwright install

# Check browser permissions
ls -la ~/.cache/ms-playwright/
```

#### Docker Container Issues
```bash
# Check container status
docker ps
docker logs dante-computer-use

# Restart container
docker-compose restart
```

#### OpenAI API Errors
- Verify API key has access to `computer-use-preview` model
- Check rate limits and quotas
- Ensure proper model availability

#### Safety Check Failures
- Review domain configuration
- Check content analysis settings
- Verify user has proper permissions

### Debug Mode

Enable debug logging:

```typescript
const session = await computerUseTool.execute({
  action: 'start_session',
  environmentType: 'browser',
  userPrompt: 'Debug session',
  // Enable debug mode in safety config
  safetyConfig: {
    ...defaultSafetyConfig,
    enableContentAnalysis: true,
    enableDomainChecking: true
  }
});
```

### Monitoring

Use the UI components to monitor:
- Active sessions
- Action history
- Safety violations
- Performance metrics

## Testing

### Unit Tests
```bash
bun test src/tests/computerUse.test.ts
```

### Integration Tests
```bash
# Start test environment
docker-compose -f docker/computer-use/docker-compose.yml up -d

# Run integration tests
bun test src/tests/computerUse.integration.test.ts
```

### Manual Testing
1. Start a browser session
2. Navigate to a test website
3. Perform various interactions
4. Verify screenshots and logs
5. Test safety check workflows

## Limitations

### Current Limitations
- Browser environment is more reliable than desktop
- Limited to predefined action types
- Requires manual safety check acknowledgment
- No multi-display support currently
- Rate limits apply based on OpenAI API usage

### Known Issues
- Some websites may block automation
- JavaScript-heavy sites may have timing issues
- VNC connection required for Docker environment
- Screenshot quality depends on display settings

## Future Enhancements

### Planned Features
- Mobile device simulation
- Advanced error recovery
- Workflow recording and playback
- Integration with external automation tools
- Enhanced AI reasoning capabilities

### Extension Points
- Custom action types
- Additional safety validators
- Environment plugins
- Integration with CI/CD pipelines

## Support

For issues and questions:
1. Check this documentation
2. Review test files for examples
3. Check Docker container logs
4. Verify OpenAI API configuration
5. Create issue in project repository

## License and Usage Policy

This integration must be used in compliance with:
- OpenAI Usage Policy
- OpenAI Business Terms
- Local laws and regulations
- Website terms of service

Always ensure proper authorization before automating interactions with external services.