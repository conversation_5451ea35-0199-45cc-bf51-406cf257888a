Create a project and initialize npm. You’ll only need to do this once.

Terminal window
mkdir my_project
cd my_project
npm init -y

Install the Agents SDK.

Terminal window
npm install @openai/agents 'zod@<=3.25.67'

Set an OpenAI API key. If you don’t have one, follow these instructions to create an OpenAI API key.

Terminal window
export OPENAI_API_KEY=sk-...

Alternatively you can call setDefaultOpenAIKey('<api key>') to set the key programmatically and use setTracingExportApiKey('<api key>') for tracing. See the config guide for more details.

Create your first agent
Agents are defined with instructions and a name.

import { Agent } from '@openai/agents';

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

Run your first agent
You can use the run method to run your agent. You trigger a run by passing both the agent you want to start on and the input you want to pass in.

This will return a result that contains the final output and any actions that were performed during that run.

import { Agent, run } from '@openai/agents';

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

const result = await run(agent, 'When did sharks first appear?');

console.log(result.finalOutput);

Give your agent tools
You can give an agent tools to use to look up information or perform actions.

import { Agent, tool } from '@openai/agents';

const historyFunFact = tool({
  // The name of the tool will be used by the agent to tell what tool to use.
  name: 'history_fun_fact',
  // The description is used to describe **when** to use the tool by telling it **what** it does.
  description: 'Give a fun fact about a historical event',
  // This tool takes no parameters, so we provide an empty Zod Object.
  parameters: z.object({}),
  execute: async () => {
    // The output will be returned back to the Agent to use
    return 'Sharks are older than trees.';
  },
});

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
  // Adding the tool to the agent
  tools: [historyFunFact],
});

Add a few more agents
Additional agents can be defined similarly to break down problems into smaller parts and have your agent be more focused on the task at hand. It also allows you to use different models for different problems by defining the model on the agent.

const historyTutorAgent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

const mathTutorAgent = new Agent({
  name: 'Math Tutor',
  instructions:
    'You provide help with math problems. Explain your reasoning at each step and include examples',
});

Define your handoffs
In order to orchestrate between multiple agents, you can define handoffs for an agent. This will enable the agent to pass the conversation on to the next agent. This will happen automatically during the course of a run.

// Using the Agent.create method to ensures type safety for the final output
const triageAgent = Agent.create({
  name: 'Triage Agent',
  instructions:
    "You determine which agent to use based on the user's homework question",
  handoffs: [historyTutorAgent, mathTutorAgent],
});

After your run you can see which agent generated the final response by looking at the finalAgent property on the result.

Run the agent orchestration
The Runner is in handling the execution of the invidiual agents, any potential handoffs and tool executions.

import { run } from '@openai/agents';

async function main() {
  const result = await run(triageAgent, 'What is the capital of France?');
  console.log(result.finalOutput);
}

main().catch((err) => console.error(err));

Putting it all together
Let’s put it all together into one full example. Place this into your index.js file and run it.

Quickstart
import { Agent, run } from '@openai/agents';

const historyTutorAgent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

const mathTutorAgent = new Agent({
  name: 'Math Tutor',
  instructions:
    'You provide help with math problems. Explain your reasoning at each step and include examples',
});

const triageAgent = new Agent({
  name: 'Triage Agent',
  instructions:
    "You determine which agent to use based on the user's homework question",
  handoffs: [historyTutorAgent, mathTutorAgent],
});

async function main() {
  const result = await run(triageAgent, 'What is the capital of France?');
  console.log(result.finalOutput);
}

main().catch((err) => console.error(err));

