# MCP Integration Guide

This guide explains how to set up and test Model Context Protocol (MCP) integration with Dante.

## Quick Start

### 1. Enable MCP Integration

Copy the example environment file:
```bash
cp .env.mcp.example .env.mcp
```

Edit `.env.mcp` to enable MCP:
```bash
MCP_ENABLED=true
MCP_AUTO_CONNECT=true
MCP_FILESYSTEM_ENABLED=true
MCP_GIT_ENABLED=true
```

### 2. Start Dante with MCP

```bash
# Start the API server
bun run dev:api

# Or start both API and UI
bun run dev:all
```

### 3. Test MCP Integration

Check MCP status:
```bash
curl http://localhost:3001/api/mcp/status
```

List available servers:
```bash
curl http://localhost:3001/api/mcp/servers
```

## Adding a New MCP Server

### Method 1: Using Environment Variables

Add to your `.env.mcp` file:
```bash
# Example: Add a custom filesystem server
MCP_SERVER_1_ID=my-filesystem
MCP_SERVER_1_NAME=My Custom Filesystem
MCP_SERVER_1_TYPE=stdio
MCP_SERVER_1_COMMAND=npx -y @modelcontextprotocol/server-filesystem /path/to/your/directory
MCP_SERVER_1_PRIORITY=90
MCP_SERVER_1_TAGS=filesystem,custom
MCP_SERVER_1_ENABLED=true
```

### Method 2: Using the API

```bash
# Register a new server
curl -X POST http://localhost:3001/api/mcp/servers \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-filesystem",
    "name": "Test Filesystem Server",
    "type": "stdio",
    "config": {
      "fullCommand": "npx -y @modelcontextprotocol/server-filesystem /tmp"
    },
    "enabled": true,
    "priority": 100,
    "tags": ["filesystem", "test"]
  }'

# Connect to the server
curl -X POST http://localhost:3001/api/mcp/servers/test-filesystem/connect
```

### Method 3: Using Server Templates

```bash
# Create a filesystem server from template
curl -X POST http://localhost:3001/api/mcp/templates/filesystem \
  -H "Content-Type: application/json" \
  -d '{
    "id": "template-fs",
    "name": "Template Filesystem Server",
    "rootPath": "/Users/<USER>/projects",
    "options": {
      "allowWrite": false,
      "priority": 95
    }
  }'
```

## Testing MCP Functionality

### 1. Check Server Status

```bash
# Get overall MCP status
curl http://localhost:3001/api/mcp/status

# Get specific server status
curl http://localhost:3001/api/mcp/servers/test-filesystem
```

### 2. Discover Available Tools

```bash
# List all tools from all servers
curl http://localhost:3001/api/mcp/tools

# List tools with metadata
curl "http://localhost:3001/api/mcp/tools?includeMetadata=true"

# List tools from specific server
curl "http://localhost:3001/api/mcp/tools?serverId=test-filesystem"

# List tools by tags
curl "http://localhost:3001/api/mcp/tools?tags=filesystem"
```

### 3. Execute MCP Operations

```bash
# Execute MCP operations via the MCP agent
curl -X POST http://localhost:3001/api/mcp/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "list_mcp_servers",
    "parameters": {
      "includeDisconnected": false
    }
  }'

# Discover tools
curl -X POST http://localhost:3001/api/mcp/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "discover_mcp_tools",
    "parameters": {
      "includeMetadata": true
    }
  }'
```

### 4. Test in Chat

Send a message to Dante with MCP enabled:
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "List the files in the current directory using MCP tools"
      }
    ],
    "useMCP": true
  }'
```

## Popular MCP Servers to Test

### 1. Filesystem Server (Official)
```bash
# Add to .env.mcp
MCP_SERVER_1_ID=official-filesystem
MCP_SERVER_1_NAME=Official Filesystem Server
MCP_SERVER_1_TYPE=stdio
MCP_SERVER_1_COMMAND=npx -y @modelcontextprotocol/server-filesystem /tmp
MCP_SERVER_1_PRIORITY=100
MCP_SERVER_1_TAGS=filesystem,official
MCP_SERVER_1_ENABLED=true
```

### 2. Git Server (Official)
```bash
# Add to .env.mcp
MCP_SERVER_2_ID=official-git
MCP_SERVER_2_NAME=Official Git Server
MCP_SERVER_2_TYPE=stdio
MCP_SERVER_2_COMMAND=npx -y @modelcontextprotocol/server-git .
MCP_SERVER_2_PRIORITY=90
MCP_SERVER_2_TAGS=git,version-control,official
MCP_SERVER_2_ENABLED=true
```

### 3. SQLite Server (Official)
```bash
# Add to .env.mcp (if you have a SQLite database)
MCP_SERVER_3_ID=official-sqlite
MCP_SERVER_3_NAME=Official SQLite Server
MCP_SERVER_3_TYPE=stdio
MCP_SERVER_3_COMMAND=npx -y @modelcontextprotocol/server-sqlite /path/to/your/database.sqlite
MCP_SERVER_3_PRIORITY=80
MCP_SERVER_3_TAGS=database,sqlite,official
MCP_SERVER_3_ENABLED=true
```

## Troubleshooting

### Server Won't Connect

1. Check server status:
```bash
curl http://localhost:3001/api/mcp/servers/your-server-id
```

2. Check the command is valid:
```bash
# Test the command directly
npx -y @modelcontextprotocol/server-filesystem /tmp
```

3. Check logs in the console where you started the server

### No Tools Discovered

1. Check if server is connected:
```bash
curl http://localhost:3001/api/mcp/servers
```

2. Try health check:
```bash
curl -X POST http://localhost:3001/api/mcp/health-check
```

3. Clear cache and retry:
```bash
curl -X POST http://localhost:3001/api/mcp/cache/clear
```

### Tool Execution Fails

1. Check tool safety validation:
```bash
# Some tools may be blocked for security
curl -X POST http://localhost:3001/api/mcp/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "execute_mcp_tool",
    "parameters": {
      "toolName": "your_tool_name",
      "toolArgs": {},
      "validateSafety": true
    }
  }'
```

2. Check tool input format matches the schema

## Environment Configuration Reference

| Variable | Description | Default |
|----------|-------------|---------|
| `MCP_ENABLED` | Enable MCP integration | `true` |
| `MCP_AUTO_CONNECT` | Auto-connect servers on startup | `true` |
| `MCP_HEALTH_CHECK_INTERVAL` | Health check interval (ms) | `60000` |
| `MCP_MAX_CONNECTIONS` | Max concurrent connections | `10` |
| `MCP_CONNECTION_TIMEOUT` | Connection timeout (ms) | `30000` |
| `MCP_FILESYSTEM_ENABLED` | Enable default filesystem server | `true` |
| `MCP_FILESYSTEM_ALLOW_WRITE` | Allow filesystem writes | `false` |
| `MCP_GIT_ENABLED` | Enable default git server | `true` |

## API Endpoints Reference

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/mcp/status` | Get MCP system status |
| `GET` | `/api/mcp/servers` | List all servers |
| `GET` | `/api/mcp/servers/:id` | Get server status |
| `POST` | `/api/mcp/servers/:id/connect` | Connect server |
| `POST` | `/api/mcp/servers/:id/disconnect` | Disconnect server |
| `POST` | `/api/mcp/servers` | Register new server |
| `DELETE` | `/api/mcp/servers/:id` | Remove server |
| `GET` | `/api/mcp/tools` | Discover tools |
| `POST` | `/api/mcp/execute` | Execute MCP operations |
| `POST` | `/api/mcp/health-check` | Health check servers |
| `GET` | `/api/mcp/cache/stats` | Get cache stats |
| `POST` | `/api/mcp/cache/clear` | Clear cache |
| `GET` | `/api/mcp/templates` | Get server templates |
| `POST` | `/api/mcp/templates/:type` | Create from template |

## Security Considerations

1. **Tool Filtering**: Dangerous tools are blocked by default
2. **Input Sanitization**: All tool inputs are sanitized
3. **Output Limits**: Tool outputs are size-limited
4. **Command Validation**: Filesystem access is restricted
5. **Authentication**: HTTP servers support bearer token auth

## Performance Tips

1. **Enable Caching**: Set `MCP_SERVER_X_CACHE_TOOLS=true`
2. **Limit Connections**: Adjust `MCP_MAX_CONNECTIONS`
3. **Health Monitoring**: Monitor via `/api/mcp/status`
4. **Clear Cache**: Periodically clear cache if needed