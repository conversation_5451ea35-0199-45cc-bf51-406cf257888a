# Autonomous Follow-Up Workflow

This adds a programmatic workflow that takes initiative when a request involves implementing a new feature or integrating/setting up technology that isn’t yet present in the repo.

## What it does
- Researches external documentation (produces a concise "Here’s what I found…" summary with sources)
- Maps findings to the local codebase using grep/read tools (returns relevant file paths/snippets)
- Produces a structured plan and delegates tasks to the appropriate agents

## How it’s exposed
- Orchestrator tool: `auto_follow_up_workflow`
  - Input: `{ request: string, context?: any }`
  - Output: `{ docSummaryText?, codebaseSummaryText?, plan?, stepResults?, finalMessage? }`

The server recognizes this tool’s output and emits SSE events so the UI shows:
- Friendly summaries ("Here’s what I found…")
- Plan/Delegation events visible in the Agent Activity panel

## Cancellation
The existing Stop button triggers `/api/run/cancel`, and the workflow checks cancellation between stages.

## Files changed
- `src/agents/AutoFollowUpManager.ts`: Implements the workflow.
- `src/agents/BuiltInAgents.ts`: Registers the `auto_follow_up_workflow` tool on the `TaskOrchestrator` agent and documents the follow-up guidance.
- `src/api/server.ts`: Bridges tool output to SSE events/messages.
