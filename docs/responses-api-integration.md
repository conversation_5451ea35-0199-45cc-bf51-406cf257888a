# OpenAI Responses API Integration Guide

## Overview

The `ResponsesAPIClient` is a production-ready, enterprise-grade client for integrating OpenAI's Responses API with MCP (Model Context Protocol) servers and OpenAI Connectors. This client provides comprehensive support for:

- **OpenAI Connectors**: Gmail, Google Calendar, Google Drive, Dropbox, Microsoft Teams, Outlook, SharePoint
- **Remote MCP Servers**: Any MCP-compliant server with HTTP/SSE transport
- **Approval Workflows**: Fine-grained control over tool execution with approval requirements
- **Session Management**: Stateful conversations with token storage and context preservation
- **Error Handling**: Robust retry logic, rate limiting, and error recovery
- **Batch Operations**: Execute multiple connector/MCP requests efficiently

## Installation & Setup

The ResponsesAPIClient is already integrated into the Dante GPT codebase. To use it:

```typescript
import { ResponsesAPIClient } from '../services/responsesAPIClient';

// Create a client instance
const client = new ResponsesAPIClient({
  apiKey: process.env.OPENAI_API_KEY,      // Optional, defaults to env var
  enableLogging: true,                      // Enable detailed logging
  defaultModel: 'gpt-5',                    // Default model to use
  maxRetries: 3,                           // Max retry attempts
  timeout: 120000                          // Request timeout in ms
});
```

## Core Features

### 1. Session Management

Sessions maintain state across multiple requests, including OAuth tokens, imported tools, and pending approvals:

```typescript
// Create a new session
const sessionId = client.createSession('my-session');

// Store OAuth tokens for reuse
client.storeOAuthToken(sessionId, 'connector_gmail', {
  access_token: 'ya29.your-token',
  token_type: 'Bearer',
  scope: 'https://www.googleapis.com/auth/gmail.modify',
  expires_at: Date.now() + 3600000 // 1 hour expiry
});

// Retrieve session info
const session = client.getSession(sessionId);

// Clear session when done
client.clearSession(sessionId);
```

### 2. Using OpenAI Connectors

#### Gmail Connector

```typescript
const response = await client.executeConnectorRequest({
  sessionId,
  connector_id: 'connector_gmail',
  oauth_token: 'ya29.gmail-token',
  input: 'Search for unread emails from this week',
  require_approval: 'never',
  allowed_tools: ['search_emails', 'read_email', 'get_recent_emails']
});

console.log(response.output_text); // AI-generated summary
console.log(response.tools_called); // Tools that were executed
```

#### Google Calendar Connector

```typescript
const response = await client.executeConnectorRequest({
  sessionId,
  connector_id: 'connector_googlecalendar',
  oauth_token: 'ya29.calendar-token',
  input: 'What meetings do I have tomorrow?',
  require_approval: 'never',
  allowed_tools: ['search_events', 'read_event']
});
```

#### Available Connectors

| Connector ID | Service | Key Tools |
|-------------|---------|-----------|
| `connector_gmail` | Gmail | search_emails, read_email, get_recent_emails |
| `connector_googlecalendar` | Google Calendar | search_events, read_event, get_profile |
| `connector_googledrive` | Google Drive | search, fetch, recent_documents |
| `connector_dropbox` | Dropbox | search, fetch, list_recent_files |
| `connector_microsoftteams` | Microsoft Teams | search, fetch, get_chat_members |
| `connector_outlookcalendar` | Outlook Calendar | search_events, fetch_event, list_events |
| `connector_outlookemail` | Outlook Email | search_messages, fetch_message, get_recent_emails |
| `connector_sharepoint` | SharePoint | search, fetch, get_site |

### 3. Using Remote MCP Servers

Connect to any MCP-compliant server:

```typescript
const response = await client.executeMCPServerRequest({
  sessionId,
  server_url: 'https://mcp.stripe.com',
  server_label: 'Stripe',
  server_description: 'Stripe payment processing',
  authorization: 'Bearer sk_test_your_key',
  input: 'Create a payment link for $99.99',
  require_approval: 'always',
  allowed_tools: ['create_payment_link', 'list_customers']
});
```

### 4. Approval Workflows

Control sensitive operations with approval requirements:

```typescript
// Request that requires approval
const response = await client.executeMCPServerRequest({
  sessionId,
  server_url: 'https://sensitive-api.com',
  server_label: 'Sensitive API',
  input: 'Delete all records',
  require_approval: 'always' // Always require approval
});

// Check for approval requests
if (response.approval_requests.length > 0) {
  const approval = response.approval_requests[0];
  console.log(`Approval needed for: ${approval.name}`);
  console.log(`Arguments: ${JSON.stringify(approval.arguments)}`);
  
  // Handle approval
  const approvedResponse = await client.handleApproval({
    sessionId,
    approval_request_id: approval.id,
    approve: true // or false to deny
  });
}
```

#### Approval Modes

- `'always'`: Always require approval for all tools
- `'never'`: Never require approval (use with caution)
- `{ never: { tool_names: ['safe_tool'] } }`: Selective approval

### 5. Batch Operations

Execute multiple requests efficiently:

```typescript
const results = await client.executeBatchRequests({
  sessionId,
  requests: [
    {
      tool: {
        type: 'mcp',
        server_label: 'Gmail',
        connector_id: 'connector_gmail',
        authorization: gmailToken
      },
      input: 'Count unread emails'
    },
    {
      tool: {
        type: 'mcp',
        server_label: 'Calendar',
        connector_id: 'connector_googlecalendar',
        authorization: calendarToken
      },
      input: 'List today\'s events'
    }
  ]
});
```

### 6. Error Handling

The client includes comprehensive error handling:

```typescript
try {
  const response = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_gmail',
    input: 'Search emails'
  });
} catch (error) {
  if (error.message.includes('OAuth token required')) {
    // Handle missing token
  } else if (error.message.includes('expired')) {
    // Handle expired token
  } else if (error.message.includes('Rate limit')) {
    // Handle rate limiting
  }
}
```

### 7. Streaming Responses

Support for streaming responses:

```typescript
const response = await client.executeConnectorRequest({
  sessionId,
  connector_id: 'connector_gmail',
  oauth_token: token,
  input: 'Analyze all emails from last month',
  stream: true // Enable streaming
});

// Response will be streamed incrementally
```

## Integration with Dante

The ResponsesAPIClient is fully integrated with Dante's connector service:

```typescript
import { connectorService } from '../services/connectorService';

// The connectorService now uses ResponsesAPIClient internally
const response = await connectorService.executeConnectorRequest({
  connector_id: 'connector_gmail',
  oauth_token: 'your-token',
  input: 'Your request',
  require_approval: 'never'
});
```

## OAuth Token Management

### Getting OAuth Tokens

For testing, you can use Google's OAuth Playground:

1. Visit https://developers.google.com/oauthplayground/
2. Enter the required scopes (e.g., `https://www.googleapis.com/auth/gmail.modify`)
3. Authorize and exchange for access token
4. Use the access token with the ResponsesAPIClient

### Token Storage

Tokens are stored securely in sessions:

```typescript
// Store token with expiration
client.storeOAuthToken(sessionId, 'connector_gmail', {
  access_token: 'ya29.token',
  refresh_token: 'optional-refresh-token',
  token_type: 'Bearer',
  scope: 'gmail.modify',
  expires_at: Date.now() + 3600000 // 1 hour
});

// Token is automatically used for subsequent requests
await client.executeConnectorRequest({
  sessionId,
  connector_id: 'connector_gmail',
  // No need to provide oauth_token - it's retrieved from session
  input: 'Your request'
});
```

## Security Best Practices

1. **Always validate MCP server URLs**: Only connect to trusted servers
2. **Use approval workflows**: Require approval for sensitive operations
3. **Token security**: Never log or expose OAuth tokens
4. **Session cleanup**: Always clear sessions when done
5. **Rate limiting**: The client includes automatic rate limiting
6. **Error handling**: Implement proper error recovery logic

## Testing

Run the test suite:

```bash
# Run all ResponsesAPIClient tests
bun test src/tests/responsesAPIClient.test.ts

# Run integration examples
bun run src/examples/connectorIntegration.ts
```

For integration testing with real services:

```bash
# Set OAuth tokens as environment variables
export GMAIL_OAUTH_TOKEN="ya29.your-gmail-token"
export CALENDAR_OAUTH_TOKEN="ya29.your-calendar-token"
export STRIPE_API_KEY="sk_test_your-stripe-key"

# Run integration examples
bun run src/examples/connectorIntegration.ts
```

## Performance Considerations

- **Session reuse**: Reuse sessions to avoid re-importing tools
- **Batch operations**: Use batch requests for multiple operations
- **Tool filtering**: Use `allowed_tools` to limit imported tools
- **Rate limiting**: The client automatically handles rate limiting
- **Token caching**: Tokens are cached in sessions to avoid repeated auth

## Troubleshooting

### Common Issues

1. **"OAuth token required"**: Ensure you've provided a valid OAuth token
2. **"Invalid server URL"**: Verify the MCP server URL is properly formatted
3. **"Rate limit exceeded"**: The client will automatically retry with backoff
4. **"Token expired"**: Implement token refresh logic in your application

### Debugging

Enable logging for detailed information:

```typescript
const client = new ResponsesAPIClient({
  enableLogging: true // Enables detailed console logging
});
```

## API Reference

### ResponsesAPIClient Constructor

```typescript
new ResponsesAPIClient(config?: {
  apiKey?: string;           // OpenAI API key
  organization?: string;      // OpenAI organization ID
  baseURL?: string;          // Custom API base URL
  maxRetries?: number;       // Max retry attempts (default: 3)
  timeout?: number;          // Request timeout in ms (default: 120000)
  defaultModel?: string;     // Default model (default: 'gpt-5')
  enableLogging?: boolean;   // Enable logging (default: false)
})
```

### Key Methods

- `createSession(sessionId?: string): string`
- `storeOAuthToken(sessionId: string, connectorId: ConnectorId, token: OAuthToken): void`
- `executeConnectorRequest(options: ConnectorRequestOptions): Promise<ProcessedResponse>`
- `executeMCPServerRequest(options: MCPServerRequestOptions): Promise<ProcessedResponse>`
- `handleApproval(options: ApprovalOptions): Promise<ProcessedResponse>`
- `executeBatchRequests(options: BatchRequestOptions): Promise<ProcessedResponse[]>`
- `getSession(sessionId: string): ResponseSession | undefined`
- `clearSession(sessionId: string): boolean`
- `clearAllSessions(): void`
- `getConnectorTools(connectorId: ConnectorId): string[]`
- `validateConnectorConfig(connectorId: ConnectorId, oauthToken?: string): ValidationResult`

## Support

For issues or questions about the ResponsesAPIClient:

1. Check the test files for usage examples
2. Review the integration examples in `src/examples/connectorIntegration.ts`
3. Enable logging for detailed debugging information
4. Ensure you're using valid OAuth tokens with correct scopes