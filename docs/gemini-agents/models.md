Gemini models

2.5 Pro 

Our most powerful thinking model with maximum response accuracy and state-of-the-art performance

Input audio, images, video, and text, get text responses
Tackle difficult problems, analyze large databases, and more
Best for complex coding, reasoning, and multimodal understanding
2.5 Flash 

Our best model in terms of price-performance, offering well-rounded capabilities.

Input audio, images, video, and text, and get text responses
Model thinks as needed; or, you can configure a thinking budget
Best for low latency, high volume tasks that require thinking
2.5 Flash-Lite 

A Gemini 2.5 Flash model optimized for cost efficiency and low latency.

Input audio, images, video, and text, and get text responses
Most cost-efficient model supporting high throughput
Best for real time, low latency use cases
Note: Gemini 2.5 Pro and 2.5 Flash come with thinking on by default. If you're migrating from a non-thinking model such as 2.0 Pro or Flash, we recommend you to review the Thinking guide first.
Model variants
The Gemini API offers different models that are optimized for specific use cases. Here's a brief overview of Gemini variants that are available:

Model variant	Input(s)	Output	Optimized for
Gemini 2.5 Pro
gemini-2.5-pro	Audio, images, videos, text, and PDF	Text	Enhanced thinking and reasoning, multimodal understanding, advanced coding, and more
Gemini 2.5 Flash
gemini-2.5-flash	Audio, images, videos, and text	Text	Adaptive thinking, cost efficiency
Gemini 2.5 Flash-Lite
gemini-2.5-flash-lite	Text, image, video, audio	Text	Most cost-efficient model supporting high throughput
Gemini 2.5 Flash Live
gemini-live-2.5-flash-preview	Audio, video, and text	Text, audio	Low-latency bidirectional voice and video interactions
Gemini 2.5 Flash Native Audio
gemini-2.5-flash-preview-native-audio-dialog &
gemini-2.5-flash-exp-native-audio-thinking-dialog	Audio, videos, and text	Text and audio, interleaved	High quality, natural conversational audio outputs, with or without thinking
Gemini 2.5 Flash Preview TTS
gemini-2.5-flash-preview-tts	Text	Audio	Low latency, controllable, single- and multi-speaker text-to-speech audio generation
Gemini 2.5 Pro Preview TTS
gemini-2.5-pro-preview-tts	Text	Audio	Low latency, controllable, single- and multi-speaker text-to-speech audio generation
Gemini 2.0 Flash
gemini-2.0-flash	Audio, images, videos, and text	Text	Next generation features, speed, and realtime streaming.
Gemini 2.0 Flash Preview Image Generation
gemini-2.0-flash-preview-image-generation	Audio, images, videos, and text	Text, images	Conversational image generation and editing
Gemini 2.0 Flash-Lite
gemini-2.0-flash-lite	Audio, images, videos, and text	Text	Cost efficiency and low latency
Gemini 2.0 Flash Live
gemini-2.0-flash-live-001	Audio, video, and text	Text, audio	Low-latency bidirectional voice and video interactions
Gemini 1.5 Flash
gemini-1.5-flash	Audio, images, videos, and text	Text	Fast and versatile performance across a diverse variety of tasks
Deprecated
Gemini 1.5 Flash-8B
gemini-1.5-flash-8b	Audio, images, videos, and text	Text	High volume and lower intelligence tasks
Deprecated
Gemini 1.5 Pro
gemini-1.5-pro	Audio, images, videos, and text	Text	Complex reasoning tasks requiring more intelligence
Deprecated
You can view the rate limits for each model on the rate limits page.

Gemini 2.5 Pro
Gemini 2.5 Pro is our state-of-the-art thinking model, capable of reasoning over complex problems in code, math, and STEM, as well as analyzing large datasets, codebases, and documents using long context.

Try in Google AI Studio

Model details
Property	Description
Model code	gemini-2.5-pro
Supported data types
Inputs

Audio, images, video, text, and PDF

Output

Text

Token limits[*]
Input token limit

1,048,576

Output token limit

65,536

Capabilities
Structured outputs

Supported

Caching

Supported

Function calling

Supported

Code execution

Supported

Search grounding

Supported

Image generation

Not supported

Audio generation

Not supported

Live API

Not supported

Thinking

Supported

Batch Mode

Supported

URL Context

Supported

Versions	
Read the model version patterns for more details.
Stable: gemini-2.5-pro
Latest update	June 2025
Knowledge cutoff	January 2025
Gemini 2.5 Flash
Our best model in terms of price-performance, offering well-rounded capabilities. 2.5 Flash is best for large scale processing, low-latency, high volume tasks that require thinking, and agentic use cases.

Try in Google AI Studio

Model details
Property	Description
Model code	models/gemini-2.5-flash
Supported data types
Inputs

Text, images, video, audio

Output

Text

Token limits[*]
Input token limit

1,048,576

Output token limit

65,536

Capabilities
Audio generation

Not supported

Caching

Supported

Code execution

Supported

Function calling

Supported

Image generation

Not supported

Search grounding

Supported

Structured outputs

Supported

Thinking

Supported

Batch Mode

Supported

URL Context

Supported

Versions	
Read the model version patterns for more details.
Stable: gemini-2.5-flash
Preview: gemini-2.5-flash-preview-05-20
Latest update	June 2025
Knowledge cutoff	January 2025
