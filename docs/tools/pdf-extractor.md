# PDF Extractor

A small Node.js utility that wraps the pdf-parse library with safe path enforcement and a stable output schema. It supports programmatic use and a simple CLI for ad-hoc extraction.

- Library module: scripts/lib/pdf-extractor.mjs
- CLI script: scripts/extract-pdf-text.mjs

## Installation

This tool depends on pdf-parse. Ensure it is installed in your project:

```bash
npm i pdf-parse
# or
pnpm add pdf-parse
# or
yarn add pdf-parse
```

Node version: Requires a modern Node.js runtime that supports ES Modules (.mjs).

## Programmatic API

Import and call extractPdf:

```js
// ESM
import { extractPdf } from './scripts/lib/pdf-extractor.mjs';

const result = await extractPdf({
  filePath: 'uploads/my.pdf',
  maxPages: 3,          // optional: positive integer
  password: 'secretpw', // optional: for encrypted PDFs
});

console.log(result.text);
```

### Signature

```ts
async function extractPdf(options?: {
  filePath: string;         // required; must resolve within allowed base (see Security)
  maxPages?: number;        // optional; positive integer -> pdf-parse option "max"
  password?: string;        // optional; for encrypted PDFs
}): Promise<{
  file: string;             // absolute path to the parsed file
  size: number;             // file size in bytes
  numpages: number;         // number of pages reported by pdf-parse
  info: any | null;         // document info (pdf-parse)
  metadata: any | null;     // XMP metadata if present
  text: string;             // extracted text (all pages or up to maxPages)
}>;
```

### Return shape example

```json
{
  "file": "/abs/path/to/uploads/my.pdf",
  "size": 123456,
  "numpages": 12,
  "info": { /* pdf info */ },
  "metadata": { /* xmp */ },
  "text": "...extracted text..."
}
```

## CLI Usage

A convenience script is provided for quick extraction and testing:

```bash
# Usage: node scripts/extract-pdf-text.mjs <pdf-file> [maxPages]
node scripts/extract-pdf-text.mjs uploads/sample.pdf 5
```

- Set PDF_PASSWORD env var to supply a password for encrypted PDFs:

```bash
PDF_PASSWORD="my-pass" node scripts/extract-pdf-text.mjs uploads/locked.pdf
```

- The CLI prints a JSON payload (same schema as the programmatic API) to stdout.

## Configuration

- Allowed base directory: By default, only files under `${CWD}/uploads` are allowed.
  - Override by setting environment variable UPLOADS_DIR to a safe absolute or relative path.
- pdf-parse options: The wrapper exposes a subset via options:
  - maxPages -> pdf-parse option `max`
  - password -> pdf-parse option `password`

## Important Security Notes

This utility includes guardrails to reduce common file-handling risks. You are still responsible for integrating it safely in your environment.

- Path confinement (default uploads/):
  - All input file paths are resolved to real paths (realpath) with symlinks normalized.
  - Access is denied if the resolved path is outside the allowed base directory.
  - Configure a dedicated uploads directory and ensure it is not world-writable beyond what your app requires.
- Override with care: If you set UPLOADS_DIR, choose a safe, non-system path owned by your application. Avoid pointing to sensitive system directories.
- Untrusted inputs: Never pass arbitrary user-supplied absolute paths. Accept only file names or keys that map to files you have stored under the allowed base directory.
- Symlink attacks: The realpath check prevents basic symlink escapes. Still, prefer storing uploads on a filesystem you control with appropriate permissions.
- Resource usage:
  - Large or malformed PDFs can be slow or memory-intensive to parse. Consider enforcing upload size limits and timeouts at higher layers.
  - Use maxPages to cap work when you only need the beginning of a document.
- Secrets and PII: Extracted text may contain sensitive data. Treat outputs as sensitive and restrict logs and downstream storage accordingly.
- Server-side only: This is a Node.js server-side utility. Do not expose it directly to browsers or untrusted clients.

## Errors and Troubleshooting

Common errors and remediation steps:

- EACCESS_OUTSIDE_UPLOADS: The provided file resolved outside the allowed base directory.
  - Move the file into uploads/ (or your UPLOADS_DIR) or adjust UPLOADS_DIR to a safe path.
- EPDF_PASSWORD_REQUIRED (message mentions password): The file is encrypted.
  - Provide the password via the password option (API) or PDF_PASSWORD env var (CLI).
- File not found: Path does not exist.
  - Double-check the path relative to CWD or provide an absolute path within the allowed base.
- Module not installed: "pdf-parse module is not installed or failed to load".
  - Install pdf-parse: npm i pdf-parse

Enable additional observability where needed (e.g., logging parse durations, memory usage) if processing large volumes.

## Limitations

- Text-only: This extracts text content; embedded images, forms, or complex layout are not preserved.
- Ordering: Text order can be approximate depending on PDF structure; do not rely on exact visual flow.
- Non-deterministic metadata: Some PDFs may lack or present inconsistent metadata fields.

## Related

- Library code: scripts/lib/pdf-extractor.mjs
- CLI script: scripts/extract-pdf-text.mjs
- Dependency: https://www.npmjs.com/package/pdf-parse
