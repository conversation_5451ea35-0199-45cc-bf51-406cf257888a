# file_edit Tool (Unified File Editing)

The `file_edit` tool replaces prior file editing tools (write_file, edit_file, apply_text_patch) with a single, consistent interface that supports:

- Whole-file writes (create/overwrite/append/prepend)
- Search/replace (literal or regex; one/all occurrences)
- Anchored multi-line patches (before -> after, literal or regex)
- Unified diff patching (single or multi-file)
- Batch operations with transactional semantics
- Atomic writes and optimistic concurrency (ifMatchHash)
- Optional idempotency and audit metadata
- Optional allowed-roots path guard

## Inputs

- `operation`: 'write' | 'replace' | 'patch'
- `filePath?`: string (optional for unified_diff when paths are embedded)
- Common options: `createDirs?`, `atomic?`, `ifMatchHash?`, `diagnostics?`, `idempotent?`, `audit?`
- Write: `content?`, `mode?` = 'overwrite' | 'append' | 'prepend'
- Replace: `pattern?`, `replacement?`, `useRegex?`, `scope?` = 'one' | 'all'
- Patch (anchored): `patchFormat?='anchored'`, `before?`, `after?`, `useRegex?`, `scope?`
- Patch (unified): `patchFormat='unified_diff'`, `diff`, `fuzzy?`
- Batch: `ops?: Array<...>`, `transaction?='best_effort' | 'all_or_nothing'`
- Optional Git commit (batch only): `gitCommit?: { message: string, addPaths?: string[] }`

## Outputs

- Single op: `{ path, operation, modified, existed?, bytesWritten?, replacements?, hashes, diagnostics?, audit? }`
- Multi-file/unified-diff/batch: `{ batch: true, results: [...], transaction? }`

## Allowed Roots Guard

Restrict file operations to specific directories by setting `FILE_EDIT_ALLOWED_ROOTS` (comma-separated absolute paths). If unset, all paths are allowed.

Example:

```
FILE_EDIT_ALLOWED_ROOTS=/Users/<USER>/project,/tmp/workspace
```

This can also be configured via `config.fileEdit.allowedRoots`.

## Examples

- Write: `{ operation:'write', filePath:'src/app.ts', content:'...', createDirs:true, atomic:true }`
- Replace: `{ operation:'replace', filePath:'src/app.ts', pattern:'foo', replacement:'bar' }`
- Anchored patch: `{ operation:'patch', patchFormat:'anchored', filePath:'src/app.ts', before:'old', after:'new' }`
- Unified diff: `{ operation:'patch', patchFormat:'unified_diff', diff:'--- a/src/a.ts\n+++ b/src/a.ts\n@@ ...' }`
- Batch: `{ ops:[...], transaction:'all_or_nothing' }`

## Notes

- Atomic writes use temp+rename; set `atomic:true`.
- `ifMatchHash` provides a compare-and-swap precondition based on sha256 of the previous content.
- Batch `all_or_nothing` supports write/replace/anchored patch and unified diffs in `ops` (see caveats below).
- Optional Git commit runs `git add` and `git commit` when `gitCommit` is provided and inside a git repo.

## Batch transactions and unified diffs

`transaction: 'all_or_nothing'` is supported for batches that include unified diffs. The implementation plans all edits in memory, then applies them with atomic per‑file writes; if any write fails, previously changed files are rolled back to their original content.

Current caveats for unified diffs inside `ops`:

- Merge strategy: supports `strict`, `fuzzy`, and `conflict_markers` in batch. `git_3way` is only supported for single, non‑batch unified diff operations (top‑level `operation:'patch', patchFormat:'unified_diff'`).
- File operations: rename and delete are not handled in batch. Diffs that imply renames or deletions are ignored; content‑only changes are applied. New files are supported when the diff hunks contain only additions (e.g., `@@ -0,0 +N,M @@`).
- Per‑op options: `ifMatchHash`, `createDirs`, `diagnostics`, `audit`, and `idempotent` are not applied per operation in batch mode today. Batch apply always uses atomic writes.
- Result shape: batch results for ops are "planned" summaries and do not include per‑file hashes/diagnostics.
- Paths: for unified diff ops, the target files come from the diff; any `filePath` field in that op is ignored.
