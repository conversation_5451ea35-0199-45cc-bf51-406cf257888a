# Browser-Use MCP Integration Setup Guide

## Overview

Browser-Use is a Python-based advanced browser automation MCP server that provides sophisticated browser control capabilities to <PERSON>. It offers features beyond standard Playwright including:

- **Tab Management**: Create, switch, and close browser tabs
- **AI-Powered Content Extraction**: Extract structured data using OpenAI
- **Session Persistence**: Maintain browser state across commands
- **Advanced Interaction**: Click elements by index, type in fields, scroll
- **Real Browser Control**: Uses actual Chromium browser for authentic interactions

## Prerequisites

- Python 3.8 or higher
- Bun runtime (already required for Dante)
- OpenAI API key (optional, for AI content extraction)

## Quick Setup

### 1. Run the Setup Script

```bash
# Install and configure browser-use
bun run browser-use:setup
```

This script will:
- Create a Python virtual environment at `~/.dante-gpt/browser-use-venv`
- Install browser-use with MCP support
- Install Playwright and Chromium browser
- Create necessary directories
- Generate a wrapper script for running the server

### 2. Enable in Environment

Add to your `.env` file:

```bash
# Enable browser-use MCP server
MCP_BROWSER_USE_ENABLED=true

# Optional: Add your OpenAI key for AI extraction
OPENAI_API_KEY=sk-your-key-here
```

### 3. Test the Integration

```bash
# Run the test suite
bun run browser-use:test
```

## Available Commands

### Package.json Scripts

```bash
# Initial setup (run once)
bun run browser-use:setup

# Manual server start (usually automatic)
bun run browser-use:start

# Run integration tests
bun run browser-use:test
```

## Browser-Use Tools in Dante

Once configured, the following tools become available:

### Navigation Tools
- `browser_use_navigate` - Navigate to URLs, open in new tabs
- `browser_use_go_back` - Navigate back in history

### State & Interaction
- `browser_use_get_state` - Get page state with indexed interactive elements
- `browser_use_click` - Click elements by index
- `browser_use_type` - Type text into input fields
- `browser_use_scroll` - Scroll page up or down

### Content Extraction
- `browser_use_extract` - AI-powered content extraction (requires OPENAI_API_KEY)

### Tab Management
- `browser_use_list_tabs` - List all open tabs
- `browser_use_switch_tab` - Switch to specific tab
- `browser_use_close_tab` - Close specific tab

## How It Works

### Architecture

```
Dante (TypeScript/Bun)
    ↓
MCP Protocol (stdio)
    ↓
Browser-Use MCP Server (Python)
    ↓
Playwright + Chromium
    ↓
Web Pages
```

### Element Interaction Model

Browser-Use uses an **index-based interaction model**:

1. Call `browser_use_get_state` to get all interactive elements
2. Each element has an index (0, 1, 2, ...)
3. Use the index to interact: `browser_use_click({ index: 5 })`

Example workflow:
```typescript
// 1. Navigate to page
await browserUseNavigate({ url: "https://example.com" });

// 2. Get state with elements
const state = await browserUseGetState({ include_screenshot: false });
// Returns: { interactive_elements: [{index: 0, tag: "button", text: "Submit"}, ...] }

// 3. Click element by index
await browserUseClick({ index: 0 }); // Clicks the Submit button
```

## Directory Structure

After setup, browser-use creates:

```
~/.dante-gpt/
├── browser-use-venv/       # Python virtual environment
└── run-browser-use-mcp.sh  # Wrapper script

~/.browser-use-mcp/         # Browser data and cache

~/Downloads/browser-use-mcp/ # Downloaded files
```

## Configuration

### Environment Variables

```bash
# Required to enable
MCP_BROWSER_USE_ENABLED=true

# Optional: AI extraction
OPENAI_API_KEY=sk-...

# Optional: Custom command path
BROWSER_USE_COMMAND=/custom/path/to/wrapper.sh
```

### MCP Server Configuration

The server is configured in `src/config/mcpConfig.ts`:

```typescript
{
  id: 'browser-use',
  name: 'Browser-Use Advanced Automation',
  type: 'stdio',
  priority: 96,  // Higher than Playwright
  tags: ['browser', 'automation', 'python', 'advanced']
}
```

## Troubleshooting

### Setup Issues

If setup fails:

1. **Check Python Version**
   ```bash
   python3 --version  # Should be 3.8+
   ```

2. **Manual Virtual Environment Setup**
   ```bash
   python3 -m venv ~/.dante-gpt/browser-use-venv
   source ~/.dante-gpt/browser-use-venv/bin/activate
   pip install "browser-use[cli]"
   playwright install chromium
   ```

3. **Permission Issues**
   ```bash
   chmod +x scripts/browser-use-setup.sh
   chmod +x ~/.dante-gpt/run-browser-use-mcp.sh
   ```

### Connection Issues

If browser-use won't connect:

1. **Check if enabled**
   ```bash
   grep MCP_BROWSER_USE_ENABLED .env
   # Should show: MCP_BROWSER_USE_ENABLED=true
   ```

2. **Test server directly**
   ```bash
   ~/.dante-gpt/run-browser-use-mcp.sh
   # Should start without errors
   ```

3. **Check logs**
   - Look for errors in Dante's output
   - MCP connection timeout is set to 45 seconds for Python startup

### Browser Launch Issues

If browser won't launch:

1. **Install Chromium**
   ```bash
   source ~/.dante-gpt/browser-use-venv/bin/activate
   playwright install chromium
   ```

2. **Check display (for headless mode)**
   ```bash
   # On servers without display
   export DISPLAY=:99
   Xvfb :99 -screen 0 1024x768x24 &
   ```

### AI Extraction Not Working

If `browser_use_extract` fails:

1. **Check OpenAI key**
   ```bash
   grep OPENAI_API_KEY .env
   ```

2. **Verify key is valid**
   ```bash
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer $OPENAI_API_KEY"
   ```

## Comparison: Browser-Use vs Playwright MCP

| Feature | Browser-Use | Playwright MCP |
|---------|------------|----------------|
| **Language** | Python | JavaScript |
| **Setup** | Requires Python env | npm package |
| **Tab Management** | ✅ Full support | ❌ Limited |
| **AI Extraction** | ✅ Built-in | ❌ Manual |
| **Element Selection** | Index-based | CSS selectors |
| **Session Persistence** | ✅ Maintained | ⚠️ Per-call |
| **Resource Usage** | Higher (Python) | Lower |
| **Startup Time** | Slower (45s timeout) | Faster |

## Best Practices

### When to Use Browser-Use

- **Multi-tab workflows**: Managing multiple browser tabs
- **AI extraction needed**: When you need intelligent content extraction
- **Session persistence**: Long-running browser sessions
- **Complex interactions**: Advanced browser automation scenarios

### When to Use Playwright MCP

- **Simple navigation**: Basic page visits and clicks
- **CSS selector preference**: When you know exact selectors
- **Fast startup needed**: Quick one-off browser tasks
- **Lower resource usage**: Constrained environments

## Integration with ResearchAgent

The ResearchAgent can use browser-use tools when available:

```typescript
// In ResearchAgent, tools are available as:
browserUseNavigateTool
browserUseGetStateTool
browserUseClickTool
// ... etc
```

Agents can check availability:
```typescript
import { isBrowserUseAvailable } from '../tools/browserUseTools';

if (await isBrowserUseAvailable()) {
  // Use browser-use tools
} else {
  // Fall back to other methods
}
```

## Security Considerations

1. **Domain Restrictions**: Currently allows all domains by default
2. **File Access**: Creates directories at `~/.browser-use-mcp`
3. **Downloads**: Files download to `~/Downloads/browser-use-mcp/`
4. **API Keys**: Store securely in `.env`, never commit

## Maintenance

### Updating Browser-Use

```bash
source ~/.dante-gpt/browser-use-venv/bin/activate
pip install --upgrade "browser-use[cli]"
```

### Cleaning Up

To remove browser-use completely:

```bash
# Remove virtual environment
rm -rf ~/.dante-gpt/browser-use-venv

# Remove wrapper script
rm ~/.dante-gpt/run-browser-use-mcp.sh

# Remove data directories (optional)
rm -rf ~/.browser-use-mcp
rm -rf ~/Downloads/browser-use-mcp

# Disable in .env
# Set MCP_BROWSER_USE_ENABLED=false
```

## Further Resources

- [Browser-Use GitHub](https://github.com/browser-use/browser-use)
- [Browser-Use Documentation](https://docs.browser-use.com)
- [MCP Protocol Spec](https://modelcontextprotocol.io)
- [Playwright Documentation](https://playwright.dev)