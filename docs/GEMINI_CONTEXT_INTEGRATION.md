# Gemini Context Integration (Vercel AI SDK)

## Overview

This document describes the integration of Google's Gemini models as a **context consolidation service** in the Dante GPT system using the Vercel AI SDK. Rather than running Gemini through the legacy OpenAI Agents SDK, we now use the `@ai-sdk/google` provider with `ai` core helpers to leverage Gemini's strengths:

- **1M+ token context window** for handling large files
- **Advanced reasoning capabilities** for strategic planning
- **Cost-effective processing** for large content analysis

## Architecture

### 1. GeminiContextService (`src/services/geminiContextService.ts`)

The core service that provides intelligent context consolidation:

```typescript
class GeminiContextService {
  // File consolidation
  summarizeFile(filePath, options)
  analyzeCodeStructure(filePaths, options)
  debugLargeFile(filePath, errorContext)
  extractRelevantSections(filePath, query)
  
  // Strategic planning
  createStrategicPlan(request, context)
  synthesizeProgress(completedSteps, remainingWork)
  
  // Comparison and refactoring
  compareFiles(filePaths, analysisType)
  generateRefactorPlan(filePath, requirements)
}
```

### 2. Gemini Context Tools (`src/tools/geminiContextTools.ts`)

Vercel AI SDK tools that agents can call via the unified orchestrator:

- `consolidate_large_file` - Intelligently summarize large files
- `analyze_with_context` - Deep analysis using Gemini's reasoning
- `debug_with_reasoning` - Complex debugging with reasoning
- `extract_relevant_code` - Find specific sections in large codebases
- `create_strategic_plan` - Generate detailed execution plans
- `analyze_progress` - Synthesize work done and plan next steps
- `compare_files` - Identify patterns and differences
- `generate_refactor_plan` - Create refactoring strategies

### 3. Enhanced File Operations

The `read_file` tool now automatically detects large files and uses Gemini for consolidation:

```typescript
// Automatic consolidation triggers when:
- File size > 50KB
- Line count > 500 lines
- Token count > 5000 tokens
```

### 4. PlanningAgent Integration

The PlanningAgent now leverages Gemini for strategic planning:

```typescript
// When Gemini is available:
- Uses create_strategic_plan for complex tasks
- Leverages Gemini's reasoning for dependencies
- Gets risk assessments and alternatives
- Uses analyze_progress for course correction
```

## Configuration

### Environment Variables

```bash
# Basic Gemini configuration
# Both variables are supported; if only GEMINI_API_KEY is set, the app maps it to GOOGLE_GENERATIVE_AI_API_KEY.
GEMINI_API_KEY=your-api-key
GOOGLE_GENERATIVE_AI_API_KEY=your-api-key
GEMINI_MODEL=gemini-2.5-flash

# Context consolidation settings
GEMINI_CONSOLIDATION_ENABLED=true
GEMINI_AUTO_CONSOLIDATE=true
GEMINI_FILE_SIZE_THRESHOLD=50000    # 50KB
GEMINI_LINE_COUNT_THRESHOLD=500
GEMINI_TOKEN_COUNT_THRESHOLD=5000

# Caching configuration
GEMINI_CACHE_ENABLED=true
GEMINI_CACHE_TTL=3600               # 1 hour

# Planning configuration
GEMINI_PLANNING_ENABLED=true
GEMINI_PLANNING_MODEL=gemini-2.5-pro

# Thinking budgets (mapped to providerOptions.google.thinkingConfig)
GEMINI_THINKING_SIMPLE=128
GEMINI_THINKING_MEDIUM=512
GEMINI_THINKING_COMPLEX=2048
GEMINI_THINKING_ORCHESTRATION=8192
```

## Usage Examples

### Example 1: Large File Refactoring

```typescript
// User request: "Refactor the 1000-line UserService.ts"

// 1. OpenAI agent detects large file
// 2. Automatically uses Gemini for consolidation
// 3. Gemini returns:
//    - Class overview with line numbers
//    - Method signatures and dependencies
//    - Suggested refactoring approach
// 4. OpenAI agent performs targeted edits
```

### Example 2: Complex Debugging

```typescript
// User request: "Debug the error in PaymentProcessor.ts"

// 1. OpenAI agent calls debug_with_reasoning tool
// 2. Gemini analyzes 2000-line file
// 3. Returns:
//    - Error location (line 1547)
//    - Root cause analysis
//    - Suggested fix with context
// 4. OpenAI agent applies the fix
```

### Example 3: Strategic Planning

```typescript
// User request: "Implement JWT authentication with refresh tokens"

// 1. PlanningAgent detects complex task
// 2. Uses create_strategic_plan tool
// 3. Gemini provides:
//    - Detailed step-by-step plan
//    - Risk assessment
//    - Agent assignments
//    - Token estimates
// 4. OpenAI executes plan with handoffs
```

## Benefits

1. **Token Efficiency**: OpenAI agents stay within token limits
2. **Cost Optimization**: Use cheaper Gemini tokens for large content
3. **Better Analysis**: Leverage Gemini's reasoning for complex files
4. **Vercel AI SDK Orchestration**: Unified tool calling and streaming with `generateText`/`streamText`
5. **Seamless Integration**: Transparent to end users and existing agents

## Testing

Run the integration test:

```bash
bun run test-gemini-context.ts
```

This tests:
- File consolidation
- Strategic planning
- Code structure analysis
- Debug analysis
- Progress synthesis

## Key Design Decisions

1. **Service Pattern**: Gemini is a service, not an agent
2. **Tool Integration**: Agents call Gemini via Vercel AI SDK tools
3. **Automatic Detection**: Large files trigger consolidation automatically
4. **Caching**: Results are cached to avoid redundant API calls
5. **Fallback**: If Gemini fails, system falls back to full content

## Future Enhancements

1. **Streaming Support**: Stream large consolidations
2. **Multi-modal Analysis**: Support for images and diagrams
3. **Custom Prompts**: Allow task-specific consolidation prompts
4. **Batch Processing**: Process multiple files in parallel
5. **Learning**: Learn from user feedback on consolidations
