# Exploration Delegation Fix

## Problem Solved

When users asked exploratory questions like "What can you tell me about this project?", the system would:
1. Classify it as a 'simple' task
2. Try to handle it directly in the orchestrator
3. Read 10+ files simultaneously (40,024 tokens)
4. Exceed GPT-5's 30,000 token limit
5. Cause rate limiting and server crashes

## Solution Implemented

### 1. Enhanced Task Classification (`src/api/server.ts`)
Added detection for exploratory question patterns:
- "tell me about"
- "what can you tell"
- "what is this"
- "describe"
- "explain"
- "overview"
- "what does"
- "how does"

These now trigger 'analysis' classification, which delegates to specialized agents.

### 2. Updated Orchestrator Instructions (`src/agents/DanteCore.ts`)
Added explicit delegation rules for exploratory questions:
```
- Exploratory questions (CRITICAL - ALWAYS DELEGATE):
  * "What can you tell me about..." → ResearchAgent
  * "Describe this project/codebase" → ResearchAgent
  * "What is this" → ResearchAgent
  * ANY question about the overall project → ResearchAgent
  * NEVER try to read multiple files directly for exploration
```

### 3. Enhanced ResearchAgent (`src/agents/ResearchAgent.ts`)
- Added Gemini context tools for large file consolidation
- Implemented smart project exploration strategy:
  1. Check directory structure first
  2. Read README and documentation
  3. Analyze package.json for tech stack
  4. Use Gemini to consolidate large files
  5. Synthesize findings without reading everything

### 4. Smart Exploration Strategy (`src/utils/explorationStrategy.ts`)
Created utility for efficient project exploration:
- Reads documentation first
- Analyzes configuration files
- Identifies key entry points
- Uses Gemini for large file consolidation
- Generates overview without token overflow

## Benefits

1. **No More Crashes**: Exploratory questions delegate to ResearchAgent with isolated context
2. **Faster Responses**: Smart exploration reads only essential files
3. **Better Quality**: Structured exploration provides comprehensive overviews
4. **Token Efficiency**: Large files are consolidated with Gemini
5. **Scalability**: Works with projects of any size

## Testing

Run the test script to verify:
```bash
bun run test-exploration-delegation.ts
```

This confirms:
- Exploratory questions are classified as 'analysis'
- Smart exploration works without token overflow
- Gemini consolidation is available for large files

## Example Flow

**Before:**
```
User: "What can you tell me about this project?"
→ Orchestrator tries to read all agent files
→ 40,024 tokens requested
→ Rate limit error
→ Server crash
```

**After:**
```
User: "What can you tell me about this project?"
→ Classified as 'analysis' task
→ Delegated to ResearchAgent
→ Smart exploration:
  - Reads README.md
  - Checks package.json
  - Lists directory structure
  - Consolidates key files with Gemini
→ Returns comprehensive overview
→ No token overflow
```

## Configuration

The system automatically:
- Detects exploratory questions
- Delegates to appropriate agents
- Uses Gemini when available for consolidation
- Falls back gracefully if Gemini is unavailable

No additional configuration needed - the improvements are built into the core delegation logic.