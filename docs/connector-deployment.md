# OpenAI Connector System - Production Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Security Configuration](#security-configuration)
4. [Database Setup](#database-setup)
5. [Service Deployment](#service-deployment)
6. [Performance Tuning](#performance-tuning)
7. [Monitoring Setup](#monitoring-setup)
8. [Backup and Recovery](#backup-and-recovery)
9. [Troubleshooting](#troubleshooting)
10. [API Documentation](#api-documentation)

## Prerequisites

### System Requirements
- Node.js 18.x or higher
- Redis 7.0 or higher
- PostgreSQL 14+ (for persistent storage)
- Docker & Docker Compose (optional but recommended)
- Minimum 4GB RAM for production
- SSL certificates for HTTPS

### Required Services
- Redis instance for caching and rate limiting
- PostgreSQL for persistent storage
- Prometheus for metrics collection (optional)
- Grafana for visualization (optional)

## Environment Setup

### 1. Clone and Install Dependencies

```bash
# Clone repository
git clone https://github.com/your-org/dante-gpt.git
cd dante-gpt

# Install dependencies with Bun
bun install

# Or with npm
npm install
```

### 2. Environment Variables

Create a `.env.production` file:

```env
# Core Configuration
NODE_ENV=production
PORT=3001
API_BASE_URL=https://api.yourdomain.com

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
DEFAULT_MODEL=gpt-5

# Redis Configuration
REDIS_HOST=redis.yourdomain.com
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_TLS=true

# PostgreSQL Configuration
DATABASE_URL=postgresql://user:<EMAIL>:5432/dante_production
DATABASE_POOL_SIZE=20

# Security
JWT_SECRET=your-very-long-random-jwt-secret
ENCRYPTION_KEY=your-32-character-encryption-key
WEBHOOK_SECRET=your-webhook-signing-secret
CORS_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_URL=https://grafana.yourdomain.com
SENTRY_DSN=your-sentry-dsn

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
QUOTA_ALERT_THRESHOLD=80

# Approval System
APPROVAL_WEBHOOK_URL=https://webhooks.yourdomain.com/approvals
APPROVAL_DEFAULT_TIMEOUT=3600000
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Health Checks
HEALTH_CHECK_INTERVAL=60000
AUTO_HEALING_ENABLED=true
STATUS_PAGE_URL=https://status.yourdomain.com
```

### 3. SSL Certificate Setup

```bash
# Using Let's Encrypt with Certbot
sudo certbot certonly --standalone -d api.yourdomain.com

# Copy certificates to application directory
sudo cp /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem ./certs/
sudo cp /etc/letsencrypt/live/api.yourdomain.com/privkey.pem ./certs/
sudo chown -R $USER:$USER ./certs/
```

## Security Configuration

### 1. Security Hardening

```bash
# Set proper file permissions
chmod 600 .env.production
chmod 700 ./certs/
chmod 600 ./certs/*.pem

# Create secure directories
mkdir -p ./logs ./data ./backups
chmod 700 ./logs ./data ./backups
```

### 2. Firewall Configuration

```bash
# Allow only necessary ports
sudo ufw allow 22/tcp  # SSH
sudo ufw allow 443/tcp # HTTPS
sudo ufw allow 3001/tcp # API (if not behind proxy)
sudo ufw allow 9090/tcp # Prometheus (restrict to monitoring server)
sudo ufw enable
```

### 3. Security Headers

Add to your nginx/Apache configuration:

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /path/to/fullchain.pem;
    ssl_certificate_key /path/to/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'" always;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Database Setup

### 1. PostgreSQL Setup

```sql
-- Create production database
CREATE DATABASE dante_production;
CREATE USER dante_user WITH ENCRYPTED PASSWORD 'secure-password';
GRANT ALL PRIVILEGES ON DATABASE dante_production TO dante_user;

-- Connect to the database
\c dante_production;

-- Create required tables
CREATE TABLE oauth_tokens (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    connector_id VARCHAR(255) NOT NULL,
    encrypted_token TEXT NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, connector_id)
);

CREATE TABLE approval_requests (
    id VARCHAR(255) PRIMARY KEY,
    connector_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

CREATE TABLE connector_metrics (
    id SERIAL PRIMARY KEY,
    connector_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latency_ms INTEGER,
    success BOOLEAN,
    error_type VARCHAR(100),
    metadata JSONB
);

-- Create indexes for performance
CREATE INDEX idx_oauth_tokens_user_connector ON oauth_tokens(user_id, connector_id);
CREATE INDEX idx_approval_requests_status ON approval_requests(status);
CREATE INDEX idx_connector_metrics_timestamp ON connector_metrics(timestamp);
CREATE INDEX idx_connector_metrics_connector ON connector_metrics(connector_id);
```

### 2. Redis Setup

```bash
# redis.conf
bind 0.0.0.0
protected-mode yes
port 6379
requirepass your-redis-password

# Persistence
save 900 1
save 300 10
save 60 10000

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru

# Enable AOF
appendonly yes
appendfsync everysec
```

### 3. Run Database Migrations

```bash
# Run migrations
bun run migrate:production

# Verify migrations
bun run migrate:status
```

## Service Deployment

### 1. Using Docker Compose

Create `docker-compose.production.yml`:

```yaml
version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    ports:
      - "3001:3001"
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./certs:/app/certs:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
      - redis-data:/data
    restart: unless-stopped

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: dante_production
      POSTGRES_USER: dante_user
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
    ports:
      - "3000:3000"
    restart: unless-stopped

volumes:
  redis-data:
  postgres-data:
  prometheus-data:
  grafana-data:
```

### 2. Using PM2

```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'dante-api',
    script: './dist/api/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/error.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    merge_logs: true,
    max_memory_restart: '1G',
    autorestart: true,
    watch: false
  }]
};
EOF

# Start services
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 3. Systemd Service

```bash
# Create service file
sudo cat > /etc/systemd/system/dante-api.service << EOF
[Unit]
Description=Dante API Server
After=network.target redis.service postgresql.service

[Service]
Type=simple
User=dante
WorkingDirectory=/opt/dante-gpt
ExecStart=/usr/local/bin/bun run start:production
Restart=on-failure
RestartSec=10
StandardOutput=append:/var/log/dante/api.log
StandardError=append:/var/log/dante/error.log

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable dante-api
sudo systemctl start dante-api
```

## Performance Tuning

### 1. Node.js Optimization

```bash
# Set Node.js options
export NODE_OPTIONS="--max-old-space-size=4096 --enable-source-maps"

# Enable cluster mode for multi-core utilization
export CLUSTER_WORKERS=4
```

### 2. Redis Optimization

```bash
# Tune kernel parameters
sudo sysctl -w vm.overcommit_memory=1
sudo sysctl -w net.core.somaxconn=65535
echo "vm.overcommit_memory=1" >> /etc/sysctl.conf
echo "net.core.somaxconn=65535" >> /etc/sysctl.conf
```

### 3. PostgreSQL Tuning

```sql
-- postgresql.conf adjustments
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
```

### 4. Application Caching

```javascript
// Configure caching in application
const cacheConfig = {
  defaultTTL: 300, // 5 minutes
  checkPeriod: 60, // Check for expired entries every 60 seconds
  maxKeys: 10000, // Maximum number of keys
  useClones: false // Don't clone objects for performance
};
```

## Monitoring Setup

### 1. Prometheus Configuration

Create `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'dante-api'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['localhost:9093']

rule_files:
  - 'alerts.yml'
```

### 2. Alert Rules

Create `alerts.yml`:

```yaml
groups:
  - name: dante_alerts
    interval: 30s
    rules:
      - alert: HighErrorRate
        expr: rate(connector_errors_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High error rate detected
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High memory usage
          description: "Memory usage is {{ $value }}MB"

      - alert: RateLimitExceeded
        expr: rate(rate_limit_exceeded_total[5m]) > 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: Rate limits being exceeded frequently
          description: "{{ $value }} rate limit violations per second"

      - alert: ConnectorUnhealthy
        expr: connector_health_status == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: Connector is unhealthy
          description: "Connector {{ $labels.connector }} is unhealthy"
```

### 3. Grafana Dashboards

Import or create dashboards for:
- API performance metrics
- Connector health status
- Rate limiting statistics
- Approval workflow metrics
- Error rates and types
- System resource usage

### 4. Logging Configuration

```javascript
// winston.config.js
const winston = require('winston');
require('winston-daily-rotate-file');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'dante-api' },
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d'
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error'
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

## Backup and Recovery

### 1. Automated Backups

Create backup script `backup.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/backups/dante"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Backup PostgreSQL
pg_dump dante_production | gzip > "$BACKUP_DIR/postgres_$TIMESTAMP.sql.gz"

# Backup Redis
redis-cli --rdb "$BACKUP_DIR/redis_$TIMESTAMP.rdb"

# Backup application data
tar -czf "$BACKUP_DIR/appdata_$TIMESTAMP.tar.gz" /opt/dante-gpt/data

# Keep only last 30 days of backups
find "$BACKUP_DIR" -type f -mtime +30 -delete

# Upload to S3 (optional)
aws s3 sync "$BACKUP_DIR" s3://your-backup-bucket/dante-backups/
```

### 2. Schedule Backups

```bash
# Add to crontab
0 2 * * * /opt/dante-gpt/scripts/backup.sh >> /var/log/dante/backup.log 2>&1
```

### 3. Recovery Procedures

```bash
# Restore PostgreSQL
gunzip < postgres_20240101_020000.sql.gz | psql dante_production

# Restore Redis
redis-cli --rdb /backups/dante/redis_20240101_020000.rdb

# Restore application data
tar -xzf appdata_20240101_020000.tar.gz -C /
```

## Troubleshooting

### Common Issues and Solutions

#### 1. High Memory Usage
```bash
# Check memory usage
pm2 monit

# Restart with memory limit
pm2 restart dante-api --max-memory-restart 1G

# Check for memory leaks
node --inspect dist/api/server.js
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connections
psql -c "SELECT count(*) FROM pg_stat_activity;"

# Kill idle connections
psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'idle' AND state_change < current_timestamp - INTERVAL '1 hour';"
```

#### 3. Redis Performance
```bash
# Monitor Redis
redis-cli monitor

# Check slow queries
redis-cli slowlog get 10

# Clear cache if needed
redis-cli FLUSHDB
```

#### 4. Rate Limiting Issues
```bash
# Check rate limit status
curl -X GET https://api.yourdomain.com/api/rate-limit/status

# Reset rate limits for user
redis-cli DEL "ratelimit:user-123:*"
```

#### 5. OAuth Token Issues
```bash
# Refresh all tokens
bun run scripts/refresh-tokens.ts

# Clear expired tokens
bun run scripts/cleanup-tokens.ts
```

### Debug Mode

Enable debug logging:
```bash
export DEBUG=dante:*
export LOG_LEVEL=debug
bun run start:production
```

### Health Check Endpoints

```bash
# Basic health check
curl https://api.yourdomain.com/health

# Detailed health check
curl https://api.yourdomain.com/health/detailed

# Connector-specific health
curl https://api.yourdomain.com/health/connector/google-calendar
```

## API Documentation

### Authentication

All API requests require authentication:

```bash
# Bearer token
curl -H "Authorization: Bearer YOUR_API_TOKEN" \
     https://api.yourdomain.com/api/connectors

# API Key
curl -H "X-API-Key: YOUR_API_KEY" \
     https://api.yourdomain.com/api/connectors
```

### Core Endpoints

#### List Connectors
```http
GET /api/connectors
```

#### Register Connector
```http
POST /api/connectors
Content-Type: application/json

{
  "id": "custom-connector",
  "name": "Custom Connector",
  "type": "oauth",
  "config": {
    "clientId": "...",
    "clientSecret": "...",
    "authorizationUrl": "...",
    "tokenUrl": "...",
    "scope": ["read", "write"]
  }
}
```

#### Execute Request
```http
POST /api/connectors/{connectorId}/execute
Content-Type: application/json

{
  "method": "GET",
  "endpoint": "/api/resource",
  "headers": {},
  "params": {}
}
```

#### Get Health Status
```http
GET /api/connectors/{connectorId}/health
```

#### Get Usage Metrics
```http
GET /api/metrics/usage?userId={userId}&connectorId={connectorId}&period=daily
```

### Webhook Events

Configure webhooks to receive real-time events:

```json
{
  "event": "approval.requested",
  "data": {
    "requestId": "...",
    "connectorId": "...",
    "userId": "...",
    "operation": "...",
    "timestamp": "..."
  },
  "signature": "sha256=..."
}
```

Verify webhook signatures:
```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  const digest = 'sha256=' + hmac.update(payload).digest('hex');
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
}
```

### Rate Limits

Default rate limits:
- 100 requests per minute per user
- 1000 requests per hour per user
- 10000 requests per day per user

Headers returned:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

### Error Responses

Standard error format:
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests",
    "details": {
      "retryAfter": 60
    }
  }
}
```

Common error codes:
- `AUTHENTICATION_FAILED` - Invalid credentials
- `AUTHORIZATION_FAILED` - Insufficient permissions
- `RATE_LIMIT_EXCEEDED` - Rate limit hit
- `QUOTA_EXCEEDED` - Usage quota exceeded
- `CONNECTOR_UNAVAILABLE` - Connector is down
- `VALIDATION_ERROR` - Invalid request data

## Maintenance

### Regular Tasks

Daily:
- Review error logs
- Check health status
- Monitor rate limit violations

Weekly:
- Review usage metrics
- Update connector configurations
- Test backup restoration

Monthly:
- Security updates
- Performance analysis
- Cost optimization review
- Certificate renewal check

### Upgrade Procedures

```bash
# 1. Backup current deployment
./scripts/backup.sh

# 2. Pull latest code
git pull origin main

# 3. Install dependencies
bun install

# 4. Run migrations
bun run migrate:production

# 5. Build application
bun run build

# 6. Restart services (zero-downtime)
pm2 reload dante-api

# 7. Verify deployment
curl https://api.yourdomain.com/health
```

## Support

For issues and questions:
- Documentation: https://docs.yourdomain.com
- Status Page: https://status.yourdomain.com
- Support Email: <EMAIL>
- Emergency Hotline: +1-XXX-XXX-XXXX

## License

Copyright (c) 2024 Your Organization. All rights reserved.