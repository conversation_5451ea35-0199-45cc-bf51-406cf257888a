# 2PC Coordinator and Concurrency Control

Authoritative, production-ready design and implementation plan for orchestrator-level two-phase commit (2PC) and end-to-end concurrency control across agents and tools.

Contents
- A) Target architecture and end-to-end protocol
- B) Coordinator state machine (authoritative)
- C) Concurrency model and guarantees
- D) Protocol APIs and message schemas (JSON)
- E) Persistence layouts
- F) Failure handling and recovery algorithms
- G) Observability
- H) Comprehensive test plan
- I) Rollout plan
- J) Security and compliance
- K) Defaults and invariants
- L) Concrete examples and pseudocode/skeletons
- M) Risks, mitigations, limitations, and follow-on work
- Implementation plan (code anchors, milestones, verification)

Related docs
- Resilient orchestration overview: [docs/resilient-orchestration.md](docs/resilient-orchestration.md)
- Top-level architecture: [architecture.md](architecture.md)


A) Target architecture and end-to-end protocol
Summary
- The orchestrator is the single-writer 2PC Coordinator. Agents are Participants. Tools (e.g., VCS, editors) execute via agents.
- We enforce: idempotency tokens, deduplication keys, correlation IDs, and W3C trace context propagation end-to-end.
- Durable writes must occur at specific points: coordinator journal, dedup indexes, and lock/lease store.

Participants
- Coordinator (Orchestrator process): owns transaction lifecycle, lock orchestration, decision persistence, and recovery.
- Agents (Participants): perform prepare validation and durable prepare, then later commit/abort; expose protocol handlers.
- Tools (invoked by agents): must be idempotent or guarded by agent-level idempotency using patchPlanHash and operationKey.

Identifiers and context propagation
- runId, taskId, stepId: orchestrator workflow identifiers.
- txnId: globally unique per distributed transaction (UUID v7 recommended).
- operationKey: client-chosen idempotency token for individual operations.
- contentFingerprint: stable hash of planned content/intent.
- patchPlanHash: canonical hash of patch plan; key idempotency primitive for external commits.
- dedupKey, idempotencyKey: explicit keys for dedup stores.
- correlationId: equal to txnId unless provided; used in logs/metrics.
- traceparent, tracestate: W3C trace context propagated on every message.
- seqNo: sender-local monotonic sequence number for reordering detection.

Durable write points (exact)
- Coordinator
  1) On send Prepare: journal append prepare_sent per participant; intent-to-lock and lock-grants written atomically with lock store snapshot entry.
  2) On every PrepareAck: journal append prepare_ack with vote.
  3) Decision: persist decision_persisted (commit|abort) with full participant set before any Commit/Abort is sent.
  4) On send Commit/Abort: journal append commit_sent/abort_sent.
  5) On CommitAck/AbortAck: journal append commit_ack/abort_ack.
  6) Periodic checkpoint records to bound replay horizon.
  7) Dedup index writes on first-seen (taskId, contentFingerprint) and (runId, taskId, patchPlanHash).
- Agents
  1) On onPrepare vote=yes: perform durable prepare record in agent WAL before replying yes.
  2) On onCommit: apply idempotently, then durable commit marker in agent WAL before ack.
  3) On onAbort: rollback or no-op idempotently, durable abort marker before ack.

Protocol sequence (textual)
- Coordinator builds participant set with resourceKeys; acquires coordinator-held write locks under strict 2PL.
- Coordinator sends Prepare to all participants.
- Participants validate and durably prepare; vote yes/no/unknown.
- Coordinator collects votes until all received or prepare timeout; non-acks become no after timeout.
- If all yes: Coordinator persists commit decision (decision_persisted_commit), then sends Commit to all.
- Else: Coordinator persists abort decision (decision_persisted_abort), then sends Abort to all.
- Coordinator awaits acks (commit/abort). Missing acks after timeout are logged; decision is final regardless.

Mermaid sequence diagram (happy path)
```mermaid
sequenceDiagram
    autonumber
    participant C as Coordinator
    participant A as Agent A
    participant B as Agent B

    C->>C: Lock keys + journal prepare_sent(A,B)
    C->>A: Prepare{txnId, resourceKeys, ...}
    C->>B: Prepare{txnId, resourceKeys, ...}
    A-->>C: PrepareAck{vote:yes}
    B-->>C: PrepareAck{vote:yes}
    C->>C: Journal decision_persisted{commit}
    C->>A: Commit{txnId}
    C->>B: Commit{txnId}
    A-->>C: CommitAck
    B-->>C: CommitAck
    C->>C: Journal committed, release locks
```

Error/timeout branch (textual)
- Any no or timeout -> persist abort decision -> send Abort -> collect AbortAck -> release locks.


B) Coordinator state machine (authoritative)
States
- new, planning, locking, prepared, prepare_timeout, decision_persisted_commit, decision_persisted_abort, committing, committed, aborting, aborted, recovering, failed

Timers (defaults; see K)
- prepareTimeoutMs, commitAckTimeoutMs, heartbeatIntervalMs, lockLeaseTtlMs

Transitions (deterministic)
- new -> planning: transaction accepted; journal checkpoint.
- planning -> locking: resourceKeys computed; enqueue lock acquisition.
- locking -> prepared: all locks granted; Prepare sent to all; journal prepare_sent per participant.
- prepared -> decision_persisted_commit: all votes yes before prepareTimeoutMs; journal decision.
- prepared -> decision_persisted_abort: any vote no OR prepare timeout; journal decision.
- decision_persisted_commit -> committing: Commit sent to all; journal commit_sent per participant.
- committing -> committed: all CommitAck received OR commitAckTimeoutMs elapsed (decision is final; missing acks logged).
- decision_persisted_abort -> aborting: Abort sent to all; journal abort_sent per participant.
- aborting -> aborted: all AbortAck received OR commitAckTimeoutMs elapsed.
- Any -> recovering: on process restart; rebuild from journal; re-drive to terminal state.
- Any -> failed: unrecoverable internal error; must persist final abort decision if not yet persisted; escalate.

Quorum/ack rules
- Single-writer Coordinator (no quorum among coordinators).
- All participants must vote (yes/no/unknown). Treat non-acks as no after prepareTimeoutMs.
- Commit decision requires all yes. Otherwise abort.
- After decision persisted, acks are best-effort: decision stands even if some acks never arrive. Retries continue until ack or timeout jittered.

Recovery principles
- Strict rule: persist decision before notify.
- Deterministic resumption: on restart, read journal tail to reconstruct in-flight txns; if decision missing, recompute from votes and timeouts deterministically; if decision present, re-notify any participants lacking ack; release/reclaim locks accordingly.
- Monotonic commit decisions: once decision_persisted_commit/abort exists, never retract.


C) Concurrency model and guarantees
Resource keys
- Keys represent lockable resources; format: repo:{name}|branch:{branch}|path:{relativePath}. Multi-key sets cover files and generated artifacts.
- Scope normalization includes branch and canonical forward-slash relative file paths.

Lock/lease semantics
- Coordinator-held write locks using strict two-phase locking (2PL). Locks acquired before Prepare and held until after Commit/Abort decision has been notified and acks processed or ack timeout.
- Leases: each lock has TTL = lockLeaseTtlMs; coordinator heartbeats extend leases. If coordinator crashes, leases expire and are recoverable on replay.

Acquisition ordering and deadlock prevention
- Order: global lexicographic order on resource keys to avoid cycles.
- Wound-wait by runTimestamp: if younger txn holds conflicting lock, older wounds (forces abort) the younger; if older holds, younger waits with backoff.
- Fairness/starvation: FIFO per-resource wait queue with aging; waiting txn increases priority with wait time to prevent starvation.

Limits and backpressure
- Max in-flight transactions per resource key = 1 (writes). Configurable concurrency per disjoint keys group.
- Backpressure: when queues exceed threshold, new txns are rejected with retryable status (HTTP 429 equivalent) and Retry-After.

Cross-resource transactions and isolation
- Multi-key atomicity via 2PC across all involved participants.
- Isolation level: Serializable over resource keys. Conflicts detected on overlapping {branch,filePath} sets.
- Conflict detection under load: on Prepare, if any key locked by other txn, apply wound-wait; message reordering tolerated via seqNo and idempotent handlers.

Message reordering handling
- Each message carries seqNo and txnVersion. Agents must be idempotent; out-of-order Commit/Abort before Prepare is ignored until Prepare applied; repeated messages are deduped using (txnId, messageType, seqNo).


D) Protocol APIs and message schemas (JSON)
Coordinator → Agent messages
- Prepare
```json
{
  "type": "Prepare",
  "runId": "r-123",
  "taskId": "t-456",
  "stepId": "s-1",
  "txnId": "tx-uuidv7",
  "resourceKeys": ["repo:x|branch:feat|path:src/a.ts"],
  "patchPlanHash": "sha256:...",
  "operationKey": "op-uuid",
  "contentFingerprint": "sha256:...",
  "dedupKey": "r-123:t-456:sha256:...",
  "idempotencyKey": "tx-uuidv7",
  "traceparent": "00-...-...-01",
  "tracestate": "vendor=kv",
  "seqNo": 1,
  "timestamp": "2025-09-18T12:00:00Z"
}
```

- Commit
```json
{
  "type": "Commit",
  "runId": "r-123",
  "taskId": "t-456",
  "stepId": "s-1",
  "txnId": "tx-uuidv7",
  "patchPlanHash": "sha256:...",
  "traceparent": "00-...-...-01",
  "seqNo": 2,
  "timestamp": "2025-09-18T12:00:30Z"
}
```

- Abort
```json
{
  "type": "Abort",
  "runId": "r-123",
  "taskId": "t-456",
  "stepId": "s-1",
  "txnId": "tx-uuidv7",
  "reason": "vote_no|timeout|coordinator_abort|wounded",
  "traceparent": "00-...-...-01",
  "seqNo": 2,
  "timestamp": "2025-09-18T12:00:30Z"
}
```

- StatusRequest
```json
{
  "type": "StatusRequest",
  "txnId": "tx-uuidv7",
  "traceparent": "00-...-...-01",
  "seqNo": 99,
  "timestamp": "2025-09-18T12:01:00Z"
}
```

- Heartbeat (optional)
```json
{
  "type": "Heartbeat",
  "txnId": "tx-uuidv7",
  "traceparent": "00-...-...-01",
  "seqNo": 50,
  "timestamp": "2025-09-18T12:00:10Z"
}
```

Agent → Coordinator messages
- PrepareAck
```json
{
  "type": "PrepareAck",
  "runId": "r-123",
  "taskId": "t-456",
  "stepId": "s-1",
  "txnId": "tx-uuidv7",
  "vote": "yes|no|unknown",
  "status": "ok|busy|conflict|invalid|error",
  "errorCode": "E_LOCK_CONFLICT|E_INVALID_PLAN|E_INTERNAL|E_TIMEOUT|null",
  "retryable": true,
  "agentWALPos": "lsn:12345",
  "traceparent": "00-...-...-01",
  "seqNo": 10,
  "timestamp": "2025-09-18T12:00:05Z"
}
```

- CommitAck
```json
{
  "type": "CommitAck",
  "txnId": "tx-uuidv7",
  "status": "ok|error",
  "errorCode": "E_COMMIT_FAILED|null",
  "retryable": false,
  "agentWALPos": "lsn:12399",
  "seqNo": 20,
  "timestamp": "2025-09-18T12:00:32Z"
}
```

- AbortAck
```json
{
  "type": "AbortAck",
  "txnId": "tx-uuidv7",
  "status": "ok|error",
  "errorCode": "E_ABORT_FAILED|null",
  "retryable": true,
  "agentWALPos": "lsn:12400",
  "seqNo": 21,
  "timestamp": "2025-09-18T12:00:33Z"
}
```

- Status
```json
{
  "type": "Status",
  "txnId": "tx-uuidv7",
  "agentState": "prepared|committed|aborted|none",
  "lastLSN": "lsn:12400",
  "preparedAt": "2025-09-18T12:00:04Z",
  "committedAt": null,
  "abortedAt": null,
  "seqNo": 22,
  "timestamp": "2025-09-18T12:01:00Z"
}
```

Status codes, retryability, error taxonomy
- Transient (retryable): E_TIMEOUT, E_LOCK_CONFLICT, E_BUSY, E_NETWORK, E_RATE_LIMIT
- Permanent (not retryable): E_INVALID_PLAN, E_UNAUTHORIZED, E_FORBIDDEN, E_UNSUPPORTED
- Idempotency/replay: All messages must be idempotent keyed by (txnId, type, seqNo) and internal agent WAL markers keyed by txnId.


E) Persistence layouts
Journal record types (Coordinator)
- checkpoint
- prepare_sent{participantId}
- prepare_ack{participantId, vote}
- decision_persisted{commit|abort, participants[], quorum:all}
- commit_sent{participantId}
- commit_ack{participantId}
- abort_sent{participantId}
- abort_ack{participantId}
- recovery_begin, recovery_end
- lock_snapshot{heldLocks[], leaseTtls}
- dedup_indexed{keys...}

Journal record fields (typical)
```json
{
  "recordType": "prepare_ack",
  "runId": "r-123",
  "taskId": "t-456",
  "stepId": "s-1",
  "txnId": "tx-uuidv7",
  "participantId": "agent-A",
  "vote": "yes|no|unknown",
  "ts": "2025-09-18T12:00:05Z",
  "seq": 101
}
```

Dedup indexes
- Primary: (taskId, contentFingerprint) -> canonical tracker/run pointer; retentionDays=30; compact daily.
- Secondary: (runId, taskId, patchPlanHash) -> committedTxnId/commitSha; retentionDays=90; compaction merges duplicates; used for idempotent external commits.

Lock table persistence/lease store
- Schema: {resourceKey, lockOwnerTxnId, ownerRunId, grantedAt, leaseUntil, waitQueue[], priority}
- Pruning: remove released/expired; snapshot/compaction every N operations or M minutes (configurable).
- On recovery: rebuild from lock_snapshot + journal delta, advance leases to now+ttl if coordinator retains ownership.


F) Failure handling and recovery algorithms
Journal replay algorithm (Coordinator)
1) Scan journal from last checkpoint to end; rebuild per-txn state: votes, decision, notifications, acks, held locks.
2) If decision missing:
   - If any vote=no or any participant timed out (now - prepare_sent.ts > prepareTimeoutMs): decide abort, persist decision, proceed to notify Abort.
   - Else if all yes: decide commit, persist, notify Commit.
   - Else: re-send Prepare to missing participants (idempotent) and set timer.
3) If decision present and not all acks:
   - Re-send Commit/Abort to non-acked participants with retry backoff until commitAckTimeoutMs.
4) Re-establish locks: reclaim locks for undecided txns (extend leases); release for decided txns.

Exactly-once externally visible effects
- External visibility (e.g., git commit/PR) must be idempotent:
  - Use patchPlanHash in commit message metadata; check VCS for existing commit with hash; if found, skip re-apply and ack.
  - At-least-once internal messaging allowed; guard with dedup store; agents verify prior commit by querying VCS/PR state.

Monotonic commit decisions
- Once decision_persisted_commit/abort exists, never overwritten. Subsequent processing only retries notifications or acks.

Partial participation and restarts
- If some agents never prepared, Coordinator aborts after timeout; agents receiving late messages must check txn state by StatusRequest and reply idempotently.
- Network partitions: messages retried with exponential backoff + jitter; seqNo enables reordering tolerance.


G) Observability
Metrics (dimensions: agent, resourceKey, repo, branch, outcome, errorCode)
- prepare_latency (histogram)
- prepare_timeouts (counter)
- votes_no (counter), votes_unknown (counter)
- decision_latency (histogram)
- commit_ack_latency (histogram)
- aborts (counter)
- lock_wait_time (histogram)
- contention_rate (gauge/counter)
- dedup_hits (counter)
- retry_attempts (counter)
- txn_recovery_count (counter)
- locks_held (gauge), locks_expired (counter)
- in_flight_txns (gauge)

Structured logs
- Include correlationId, runId, taskId, txnId, traceparent, resourceKeys, decision, vote map, durations, retries.

Tracing
- Spans: coordinator.prepare, agent.onPrepare, coordinator.decision, coordinator.notifyCommit/Abort, agent.onCommit/Abort, lock.acquire, journal.append.
- Attributes: txnId, resourceKeys, participantId, patchPlanHash, vote, decision, timeouts.


H) Comprehensive test plan
Unit tests
- State machine transitions: each legal/illegal transition enforced, timers trigger expected transitions.
- Lock manager: order, wound-wait, FIFO with aging, TTL expiry, reentrancy.
- Dedup: index inserts, hits, retention/compaction.
- Backoff/timeouts: jittered backoff boundaries.

Integration tests
- 2PC happy path: all yes -> commit -> acks.
- Negative vote: one no -> abort -> acks.
- Prepare timeout: non-ack -> abort decision after timeout.
- Commit ack timeout: decision persisted; missing acks tolerated.
- Idempotent retries: duplicate Prepare/Commit handled without side effects.
- Resumption from persisted decision: restart after decision_persisted_*; ensure re-notify only.

Chaos/crash-recovery tests
- Coordinator crash before decision_persisted -> deterministic abort/commit based on votes/timeouts on replay.
- Coordinator crash after decision_persisted before notify -> re-notify on restart; no duplicate external effects.
- Agent crash during prepare/commit -> WAL recovery ensures consistent ack state.
- Network partitions and reordering -> seqNo handling verified.

Load/perf tests
- High contention on shared files; measure lock_wait_time, throughput, p95/p99.
- Fairness and starvation: long queue with aging; ensure eventual service within SLAs.
- Journal write latency/throughput; compaction impact.

Acceptance criteria (atomicity, isolation, forward progress)
- Atomicity: no partial external commits; either all-or-nothing across participants.
- Isolation: serializable at resource key granularity; conflicting txns never commit concurrently.
- Forward progress: no starvation with FIFO+aging; wounded txns are aborted promptly; recovery reaches terminal state without operator intervention.


I) Rollout plan
Feature flags
- coordinator_2pc_enforced (default off)
- concurrency_manager_enabled (default off)

Staged rollout
- Shadow mode: run coordinator logic/journal without enforcing locks; compare outcomes.
- Canary: enable flags for small % of tasks/repos; monitor SLOs (prepare_latency, aborts, contention_rate).
- Gradual ramp: 5% → 25% → 50% → 100% per environment.

Rollback procedure
- Flip flags off; stop taking new txns; allow in-flight to complete; if urgent, abort in-flight and release locks via runbook.
- Restore previous lockless scheduler behavior if configured.

Data migrations
- Create lock table store and dedup indexes; backfill from recent journal if needed.
- Ensure compatibility with existing RunJournal records; additive types only.

Runbook and operational readiness
- On-call playbook for timeouts, contention storms, lock leaks.
- Dashboards for metrics above; alerts on prepare_timeouts, contention_rate spikes, txn_recovery_count anomalies.


J) Security and compliance
AuthN/AuthZ
- Mutual TLS or signed tokens between coordinator and agents. Each message includes tenantId; policy enforces per-tenant isolation.
- Agent identity validated; allowed resourceKeys subset enforced.

Auditability
- Immutable audit log stream: transaction begin, decision_persisted, external effect identifiers (commit SHA/PR URL), actor identities.
- PII redaction: logs scrubbed by policy; configurable retention windows.

Multitenancy
- Tenant scoping for lock table keys and dedup indexes (prefix with tenantId).
- Rate limits per tenant to protect against noisy neighbors.


K) Defaults and invariants
Defaults (recommended initial)
- prepareTimeoutMs = 30000
- commitAckTimeoutMs = 15000
- heartbeatIntervalMs = 3000
- lockLeaseTtlMs = 15000 (renewed on heartbeat)
- retryBackoff: exponential, base=250ms, max=10s, jitter=full
- maxWaitQueuePerKey = 100
- single-writer coordinator (process-level) with optional future leader lease

Invariants (must-hold)
- Persist decision before notify.
- Unique txnId per transaction.
- No lock acquisition without journal intent (prepare_sent/lock_snapshot).
- Serializable isolation per resource keys.
- Idempotent external commits via patchPlanHash and VCS checks.
- Monotonic decision: never retract decision_persisted_*.


L) Concrete examples and pseudocode
Example journal entries (commit path)
```json
{"recordType":"checkpoint","runId":"r1","txnId":"tx1","ts":"2025-09-18T12:00:00Z"}
{"recordType":"prepare_sent","txnId":"tx1","participantId":"agent-A","ts":"2025-09-18T12:00:01Z"}
{"recordType":"prepare_sent","txnId":"tx1","participantId":"agent-B","ts":"2025-09-18T12:00:01Z"}
{"recordType":"prepare_ack","txnId":"tx1","participantId":"agent-A","vote":"yes","ts":"2025-09-18T12:00:03Z"}
{"recordType":"prepare_ack","txnId":"tx1","participantId":"agent-B","vote":"yes","ts":"2025-09-18T12:00:04Z"}
{"recordType":"decision_persisted","txnId":"tx1","decision":"commit","participants":["agent-A","agent-B"],"ts":"2025-09-18T12:00:05Z"}
{"recordType":"commit_sent","txnId":"tx1","participantId":"agent-A","ts":"2025-09-18T12:00:06Z"}
{"recordType":"commit_sent","txnId":"tx1","participantId":"agent-B","ts":"2025-09-18T12:00:06Z"}
{"recordType":"commit_ack","txnId":"tx1","participantId":"agent-A","ts":"2025-09-18T12:00:10Z"}
{"recordType":"commit_ack","txnId":"tx1","participantId":"agent-B","ts":"2025-09-18T12:00:11Z"}
```

Example journal entries (abort path)
```json
{"recordType":"prepare_sent","txnId":"tx2","participantId":"agent-A","ts":"2025-09-18T12:00:01Z"}
{"recordType":"prepare_sent","txnId":"tx2","participantId":"agent-B","ts":"2025-09-18T12:00:01Z"}
{"recordType":"prepare_ack","txnId":"tx2","participantId":"agent-A","vote":"yes","ts":"2025-09-18T12:00:03Z"}
{"recordType":"prepare_ack","txnId":"tx2","participantId":"agent-B","vote":"no","ts":"2025-09-18T12:00:04Z"}
{"recordType":"decision_persisted","txnId":"tx2","decision":"abort","participants":["agent-A","agent-B"],"ts":"2025-09-18T12:00:05Z"}
{"recordType":"abort_sent","txnId":"tx2","participantId":"agent-A","ts":"2025-09-18T12:00:06Z"}
{"recordType":"abort_sent","txnId":"tx2","participantId":"agent-B","ts":"2025-09-18T12:00:06Z"}
{"recordType":"abort_ack","txnId":"tx2","participantId":"agent-A","ts":"2025-09-18T12:00:10Z"}
{"recordType":"abort_ack","txnId":"tx2","participantId":"agent-B","ts":"2025-09-18T12:00:11Z"}
```

Example protocol messages
- Prepare: see D
- PrepareAck: see D
- Commit/Abort/CommitAck/AbortAck: see D

Coordinator pseudocode (schedule, 2PC, recovery)
```typescript
class Coordinator {
  constructor(
    private journal: Journal,
    private lockManager: LockManager,
    private transport: Transport,
    private clock: Clock
  ) {}

  async startTxn(ctx: Ctx, txn: TxnSpec) {
    await this.journal.append({ type: "checkpoint", txnId: txn.txnId });
    const keys = txn.resourceKeys.sort(); // lexicographic
    await this.lockManager.acquire(txn.txnId, keys, ctx.runId);

    // send Prepare to all
    for (const p of txn.participants) {
      await this.journal.append({ type: "prepare_sent", txnId: txn.txnId, participantId: p.id });
      this.transport.send(p.endpoint, makePrepare(txn, ctx));
    }

    const votes = await this.collectVotes(txn, ctx.prepareTimeoutMs);
    const allYes = txn.participants.every(p => votes.get(p.id) === "yes");
    const decision = allYes ? "commit" : "abort";
    await this.journal.append({ type: "decision_persisted", txnId: txn.txnId, decision });

    // notify
    const msg = decision === "commit" ? makeCommit(txn, ctx) : makeAbort(txn, ctx);
    for (const p of txn.participants) {
      await this.journal.append({ type: decision === "commit" ? "commit_sent" : "abort_sent", txnId: txn.txnId, participantId: p.id });
      this.transport.send(p.endpoint, msg);
    }

    // wait for acks (best effort)
    await this.waitForAcks(txn, decision, ctx.commitAckTimeoutMs);

    await this.lockManager.release(txn.txnId);
  }

  async recover() {
    const state = await this.journal.replay();
    for (const txn of state.inFlight) {
      if (!txn.decision) {
        // recompute deterministically
        const decision = this.decide(txn);
        await this.journal.append({ type: "decision_persisted", txnId: txn.txnId, decision });
      }
      // re-notify missing acks
      this.retryNotifications(txn);
    }
  }
}
```

Agent handlers pseudocode
```typescript
class AgentParticipant {
  async onPrepare(msg: Prepare): Promise<PrepareAck> {
    if (this.dedup.has(msg.txnId, "Prepare")) return this.dedup.reply(msg.txnId, "PrepareAck");
    if (this.conflicts(msg.resourceKeys)) return { ...ackBase(msg), vote: "no", status: "conflict", retryable: true };
    await this.wal.append({ type: "prepared", txnId: msg.txnId, patchPlanHash: msg.patchPlanHash });
    return { ...ackBase(msg), vote: "yes", status: "ok", retryable: false };
  }

  async onCommit(msg: Commit): Promise<CommitAck> {
    if (await this.hasCommitted(msg.txnId, msg.patchPlanHash)) return { txnId: msg.txnId, status: "ok", retryable: false };
    await this.applyIdempotent(msg.patchPlanHash); // includes VCS commit dedup by patchPlanHash
    await this.wal.append({ type: "committed", txnId: msg.txnId });
    return { txnId: msg.txnId, status: "ok", retryable: false };
  }

  async onAbort(msg: Abort): Promise<AbortAck> {
    await this.rollbackIfNeeded(msg.txnId);
    await this.wal.append({ type: "aborted", txnId: msg.txnId });
    return { txnId: msg.txnId, status: "ok", retryable: false };
  }
}
```


M) Risks, mitigations, limitations, follow-on work
- Long-held locks: Detect via lock TTL + alerting; auto-abort wounded txns; operator runbook to release.
- Mis-sized resource keys: Too coarse reduces throughput; too fine risks lost invariants. Provide utilities to derive keys consistently.
- Cross-branch operations: Enforce branch scoping in keys; prevent atomicity across branches unless explicitly supported.
- PR race conditions: Use server-side compare-and-swap or branch update checks; include patchPlanHash in commit messages.
- Journal growth: Periodic checkpoints and compaction; cold storage archive.
- Single-writer coordinator availability: Start with single process; plan leader election with a lease (future).
- Clock skew: Rely on monotonic timers for timeouts; avoid strict wall-clock comparisons across processes.


Implementation plan
Code anchors to modify/add
- Coordinator enforcement:
  - [src/agents/orchestration/OrchestrationEngine.ts](src/agents/orchestration/OrchestrationEngine.ts)
  - [src/agents/orchestration/scheduler/WorkScheduler.ts](src/agents/orchestration/scheduler/WorkScheduler.ts)
- Journal/persistence:
  - [src/agents/orchestration/journal/RunJournal.ts](src/agents/orchestration/journal/RunJournal.ts)
- Locks (new):
  - [src/agents/orchestration/locks/LockManager.ts](src/agents/orchestration/locks/LockManager.ts)
    - Proposed API:
      - acquire(txnId: string, keys: string[], runId: string): Promise<void>
      - release(txnId: string): Promise<void>
      - extend(txnId: string): Promise<void>
      - snapshot(): Promise<LockSnapshot>
      - stats(): Promise<LockStats>
- Agent handlers:
  - [src/agents/vercel/codeGeneration.ts](src/agents/vercel/codeGeneration.ts) (add prepare/commit/abort handlers and idempotency guards)
- Config flags:
  - [src/utils/config.ts](src/utils/config.ts)

Stepwise milestones, acceptance, verification
- M1: Journal and schema extensions
  - Add record types: prepare_sent, prepare_ack, decision_persisted, commit_sent/ack, abort_sent/ack, recovery markers.
  - Acceptance: bun run typecheck; unit tests for journal encode/decode pass.
  - Verify: bun test src/tests/retry.test.ts and new journal tests.
- M2: LockManager
  - Implement strict 2PL with lexicographic order, wound-wait, FIFO+aging, TTL leases.
  - Acceptance: unit tests for lock fairness, deadlock prevention, TTL expiry.
  - Verify: bun test src/tests/**/lock*.test.ts (new).
- M3: Coordinator 2PC happy path
  - Implement Prepare→Ack→Decision persisted→Commit notify.
  - Acceptance: integration test: all yes → commit, decision persisted before notify.
  - Verify: bun test src/tests/**/twoPhaseCommit.integration.test.ts (new).
- M4: Negative paths and timeouts
  - Any no → abort; prepare timeout; commit ack timeout tolerance.
  - Acceptance: integration and chaos tests simulate timeouts; decisions monotonic.
  - Verify: bun test src/tests/**/twoPhaseCommit.integration.test.ts -t "timeout"
- M5: Agent idempotency and external effect dedup
  - Implement patchPlanHash-based VCS dedup; WAL-based idempotent Commit/Abort.
  - Acceptance: idempotent retries produce single commit; replay safe.
  - Verify: bun test src/tests/**/idempotency.integration.test.ts (new).
- M6: Recovery and replay
  - Deterministic replay drives all in-flight to terminal decisions; re-notify missing acks.
  - Acceptance: crash-before/after decision persistence scenarios pass.
  - Verify: bun test src/tests/**/crashRecovery.integration.test.ts (new).
- M7: Concurrency under load
  - Backpressure, fairness, contention metrics; p95/p99 targets.
  - Acceptance: load test meets SLOs; no starvation.
  - Verify: bun test src/tests/**/loadContention.test.ts (new).
- M8: Security and observability
  - mTLS/JWT between coordinator and agents; full tracing and metrics.
  - Acceptance: AuthN/AuthZ enforced in tests; dashboards show metrics.
  - Verify: bun run build; manual smoke via src/scripts/geminiSmoke.ts

Rollback and toggles
- Flags: coordinator_2pc_enforced, concurrency_manager_enabled in [src/utils/config.ts](src/utils/config.ts).
- Safe disable path documented in runbook; journal remains compatible.

Verification commands (examples)
- bun run typecheck
- bun test
- bun test src/tests/twoPhaseCommit.integration.test.ts
- bun test src/tests/crashRecovery.integration.test.ts
- bun test src/tests/loadContention.test.ts
- bun run build


References and glossary
- 2PC: Two-phase commit protocol
- WAL: Write-ahead log
- 2PL: Two-phase locking
- W3C Trace Context: traceparent/tracestate
