# Resilient Multi-Agent Orchestration Architecture

## 1. Overview and Goals

This document specifies the architecture for a resilient multi-agent orchestration engine. The primary goal is to ensure that complex, multi-step workflows involving concurrent and sequential agent execution can proceed reliably, withstanding failures and ensuring that state changes are applied atomically and idempotently.

### Goals
- **Resumability:** Workflows must be resumable from the last known good state after a process restart or transient failure.
- **Atomicity:** All file modifications within a single logical step must be applied as a single, atomic unit (two-phase commit).
- **Idempotency:** Retrying a step (e.g., due to `rs_*` errors) must not result in duplicate operations or corrupted state.
- **Reliability:** The system must gracefully handle transient stream failures, network errors, and circular agent handoffs.
- **Traceability:** Every action, decision, and state transition must be recorded in a durable, append-only log for auditing and debugging.
- **Concurrency:** The system should support parallel execution of agents on disjoint sets of files to maximize throughput.

### Non-Goals
- This design does not cover distributed transaction coordination across multiple microservices. The focus is on a single orchestration engine's state and its interaction with the local file system and version control.
- Real-time, low-latency collaboration between agents is not a primary goal. The model is asynchronous and checkpoint-based.

### Problems Addressed
- **Circular Handoffs:** Agents repeatedly handing work back and forth (e.g., A -> B -> A) without making progress.
- **`rs_*` Streaming Aborts:** Transient errors from streaming services causing entire workflows to fail.
- **Lack of a Commit Phase:** File edits are applied directly, with no validation or rollback mechanism, leading to partially completed work.
- **Duplicate Trackers:** Multiple redundant trackers are created for the same logical task, causing confusion and wasted effort.
- **Non-Resumable State:** The orchestrator's state is held in memory, making recovery from a crash impossible without manual intervention.

---

## 2. Run Journal and Checkpoints

The core of the resilient system is the **Run Journal**, a durable, append-only log of all events and state transitions. The journal's primary purpose is to allow for the complete reconstruction of the system's state.

### Run Journal
The `RunJournal` will be implemented as a sequence of immutable, timestamped entries. From these entries, a "current checkpoint" view can be derived for any given `runId`. This ensures that we have both a complete audit trail and an efficient way to get the current state for active runs.

### Checkpoint Schema
Each checkpoint represents a snapshot of a task's state at a specific point in time. It is the unit of handoff and resumption.

```json
{
  "runId": "string",
  "taskId": "string",
  "stepId": "string",
  "parentStepId": "string | null",
  "agent": "string",
  "timestamps": {
    "createdAt": "ISO8601",
    "startedAt": "ISO8601 | null",
    "planReadyAt": "ISO8601 | null",
    "applyReadyAt": "ISO8601 | null",
    "committedAt": "ISO8601 | null",
    "failedAt": "ISO8601 | null"
  },
  "state": "queued | running | plan_ready | apply_pending | applying | committed | failed | blocked | review",
  "lastCompletedStep": "string | null",
  "nextAction": "plan | apply | review | handoff | retry | fail",
  "acceptanceCriteria": "string",
  "handoffDepth": {
    "current": "number",
    "max": "number"
  },
  "coolingWindows": {
    "duplicateContent": "number (seconds)"
  },
  "vcs": {
    "workingBranch": "string",
    "baseBranch": "string",
    "baseCommit": "string",
    "headCommit": "string",
    "mergeBase": "string"
  },
  "plan": {
    "patchPlan": "object (canonical JSON)",
    "previewDiff": "string (unified diff format)",
    "patchPlanHash": "string (SHA-256)",
    "idempotencyKey": "string"
  },
  "scope": {
    "fileScopes": ["path/to/file.ts"],
    "fileLocksAcquired": ["path/to/file.ts"],
    "concurrencyGroupId": "string"
  },
  "diagnostics": {
    "build": { "status": "success | failure", "logs": "string" },
    "typecheck": { "status": "success | failure", "logs": "string" },
    "test": { "status": "success | failure", "logs": "string" },
    "lint": { "status": "success | failure", "logs": "string" },
    "failureSummary": "string | null",
    "lastStackTrace": "string | null"
  },
  "artifacts": {
    "diffFiles": ["path/to/diff"],
    "logFiles": ["path/to/log"],
    "prUrl": "string | null",
    "commitSha": "string | null",
    "commitMessage": "string | null"
  },
  "retryState": {
    "rsRetryCount": "number",
    "backoff": {
      "strategy": "exponential",
      "initialDelay": "number (ms)",
      "maxRetries": "number"
    },
    "lastRetryAt": "ISO8601 | null",
    "lastResponseHeaders": {
      "Retry-After": "string | null"
    }
  },
  "loopGuard": {
    "lastNTools": ["tool_name_1", "tool_name_2"],
    "lastTransitions": ["state_A -> state_B"],
    "pingPongSignatures": ["agent_A:agent_B:scope_hash"]
  }
}
```

### Canonicalization Rules
To ensure deterministic hashing and plans, the following rules must be applied:
- **JSON Canonicalization:** Keys in all JSON objects (especially `patchPlan`) must be sorted alphabetically before serialization. Whitespace should be normalized (e.g., no indentation, compact printing).
- **Path Normalization:** All file paths must be normalized to use forward slashes (`/`) and be relative to the project root.

---

## 3. Handoff and Resume Protocol

- **Checkpoint-based Handoff:** Every handoff between agents or between an agent and the orchestrator *must* include a complete, valid checkpoint.
- **Receiver Validation:** The receiving agent *must* validate the checkpoint for required fields (`runId`, `taskId`, `nextAction`, `fileScopes`). If validation fails, the agent must refuse the handoff and signal an error to the orchestrator.
- **Strict Resumption:** When resuming a task, the orchestrator or agent *must* continue from the `checkpoint.nextAction`. It is forbidden to regenerate or re-run prior completed steps.
- **Depth Limiting:** The `handoffDepth` is incremented on each handoff. If `current` exceeds `max`, the workflow is escalated to a human or a specialized diagnostic agent.
- **Ping-Pong Detection:** The system must detect repeated `A -> B -> A` handoffs on the same `fileScopes`. This is tracked via `loopGuard.pingPongSignatures`. On detection, the task is blocked, and a summary with a proposed fix is generated.

---

## 4. Two-Phase Commit (2PC) via `file_edit`

To guarantee atomicity of file changes, all mutations must go through a strict two-phase commit process orchestrated via the `file_edit` tool. Direct file system access is forbidden for agents.

### Phase 1: Plan
1.  **Generate Plan:** The agent produces a deterministic `patchPlan` (the canonical JSON representation of the changes) and a human-readable `previewDiff`.
2.  **Dry-Run (Optional):** If the `file_edit` tool supports a dry-run mode, it is used to generate the `previewDiff`. Otherwise, the diff is computed manually and stored in the checkpoint.
3.  **Store Hash:** The SHA-256 hash of the canonical `patchPlan` is computed and stored as `patchPlanHash`. This serves as a critical idempotency key.
4.  **Run Pre-flight Checks:** The orchestrator runs the configured verification commands (defaults: `bun run typecheck`, `bun run build`, `bun test`) against the proposed changes in a temporary branch or state.
    - Configure the exact commands via `ORCH_VERIFICATION_STEPS` (JSON or comma-separated `id:command` entries). For quick overrides, `ORCH_TYPECHECK_CMD`, `ORCH_BUILD_CMD`, and `ORCH_TEST_CMD` adjust the defaults individually.
5.  **Record Diagnostics:** The structured results and logs from the checks are captured in the `diagnostics` section of the checkpoint.
6.  **Update State:** The checkpoint state is moved to `plan_ready`.

### Phase 2: Apply
1.  **Apply Patch:** The orchestrator invokes `file_edit` to apply the `patchPlan`.
2.  **Re-run Checks:** The same suite of verification commands is re-run on the modified files.
3.  **Atomic Commit:** If all checks pass, a single, atomic commit is made to the `workingBranch`. The commit message must follow the Conventional Commits standard and include structured metadata for traceability:
    `feat(orchestrator): <summary> [run:{runId} task:{taskId} step:{stepId} patch:{patchPlanHash}]`
4.  **Update Checkpoint:** The `commitSha` is recorded in the checkpoint. If configured, a Pull Request is opened/updated, and the `prUrl` is stored. The state is moved to `committed`.
5.  **Rollback on Failure:** If any check fails during this phase, the `workingBranch` is reset to the previous `headCommit` (effectively rolling back the change). The failure details are attached to the checkpoint's `diagnostics`, and the state is moved to `failed`. No handoff occurs without this evidence.

### Idempotency
- The `patchPlanHash` is used as the idempotency key for the `apply` phase. If the orchestrator sees a request to apply a plan with a hash that has already been successfully committed, it will skip the operation and return the existing result.

---

## 5. Robust `rs_*` Retry Policy

- **Classification:** Errors matching the pattern "Item with id 'rs_*' not found" are classified as transient and are eligible for retry unless an accompanying header or flag explicitly marks the failure as permanent.
- **In-Place Retries:** Retries are handled within the agent's turn, without handing off to the orchestrator.
- **Backoff Strategy:** An exponential backoff algorithm with jitter is used. The agent must respect the `Retry-After` HTTP header if provided. A maximum of 5 retries will be attempted for any single operation.
- **Idempotent Operations:** All streaming requests that could lead to an `rs_*` error must include a stable, client-generated operation key to ensure idempotency on the server side. For file edits, the `patchPlanHash` serves this purpose.
- **Final Failure:** If all retries are exhausted, the agent must record the error class, final message, stack trace, and last known response headers in the checkpoint. It then surfaces the failure to the orchestrator without attempting another handoff.

---

## 6. Tracker Deduplication

- **Canonical Tracker:** A single canonical tracker is maintained per logical task. The key for this tracker is a composite of `taskId` and a `contentFingerprint`.
- **Content Fingerprint:** The fingerprint is a SHA-256 hash computed from the normalized goal description, file scopes, and `patchPlanHash` (if available).
- **Check-and-Set:** Before initiating work or handing off, an agent or the orchestrator must check for an existing tracker with the same key. If found, the existing tracker's state is updated, and the new, duplicate request is marked as `superseded` with a pointer to the canonical tracker.
- **Cooling Window:** A cooling window is enforced for identical `contentFingerprint` submissions. A new tracker for the same content will be rejected unless a specified time has passed, or it is submitted with new evidence (e.g., a change in the base commit).

---

## 7. Concurrency Model

- **File Scopes:** Work is partitioned into disjoint sets of files (`fileScopes`).
- **File Locking:** The orchestrator acquires locks on a per-file basis before assigning work to an agent. Lock information (owner, TTL) is stored in the Run Journal.
- **Parallel Execution:** Agents can execute in parallel on tasks with non-overlapping file locks.
- **Join Step:** For workflows that require merging parallel work, a dedicated `join` step is used. This step deterministically merges diffs. If conflicts arise, a single designated resolver agent is tasked with producing a new, unified `patchPlan` and checkpoint before the workflow can proceed.

### Coordinator 2PC and Concurrency

For a detailed specification of the distributed transaction protocol, coordinator state machine, and advanced concurrency control, see the [2PC Coordinator and Concurrency](docs/2pc-coordinator-and-concurrency.md) documentation.
---

## 8. Sequential Flow Controls

- **Checkpoint-driven Flow:** After every logical step, a checkpoint *must* be written to the journal.
- **Acceptance Criteria:** The orchestrator evaluates the `acceptanceCriteria` defined in the checkpoint against the step's output before proceeding.
- **Explicit Handoffs:** Handoffs are only initiated after successful evaluation, and they must include a precise resume point (`nextAction`) and a clear reason for the handoff.
- **Loop Guards:** For implementation and refactoring intents the orchestrator enforces a "must-make-progress" rule. Each agent turn must either deliver a `plan_ready` checkpoint (via `file_edit`), satisfy verification heuristics (clean diagnostics/assertions), or demonstrate new context gathering. Read-only turns that introduce new evidence (new file reads, directory inspections, or other non-mutating tool activity) are tracked and capped (default: two consecutive turns, configurable via `NO_WRITE_ANALYSIS_GRACE`). Exceeding this allowance or repeating the same context loop without progress triggers a `loop-detected` failure.

---

## 9. Recovery and Resumption

- **Journal-based Reconstruction:** On a process restart, the orchestrator reconstructs the state of all active runs by replaying the `RunJournal`.
- **Lock Re-acquisition:** The orchestrator re-acquires any file locks that were held at the time of the crash.
- **Resume from Checkpoint:** Each task is resumed precisely at its `checkpoint.nextAction`. No previously completed steps are re-executed.
- **Operational Runbook:** A runbook will be created (`docs/runbook/orchestration-recovery.md`) detailing manual steps for resolving inconsistent states, such as a commit being made without the corresponding checkpoint update.

---

## 10. Configuration and Telemetry

- **Configuration:** Key parameters will be exposed via configuration files, including branch naming conventions, PR creation behavior, retry/backoff settings, and cooling windows.
- **Metrics & Telemetry:** The system will emit structured logs and metrics for:
  - `rs_*` retry attempts and final outcomes.
  - Loop-guard activations.
  - Two-phase commit outcomes (success, rollback).
  - Journal write latency and throughput.
  - File lock contention rates.

---

## 11. Security and Auditing

- **Secrets Management:** All secrets used for interacting with external services (e.g., Git hosting) must be handled via a secure vault.
- **PII Redaction:** Logs and artifacts will be scanned for and redacted of any potential Personally Identifiable Information (PII).
- **Artifact Retention:** A clear policy will define the retention period for artifacts like diffs, logs, and old branches.

---

## 12. Testing Strategy

- **Unit Tests:**
  - `RunJournal` schema validation and checkpoint serialization.
  - Canonicalization rules for hashing.
  - Retry and backoff logic.
  - Tracker deduplication and fingerprinting logic.
  - Lock manager logic.
- **Integration Tests:**
  - The full two-phase commit lifecycle (happy path and rollback on failure).
  - Loop-guard triggering and task failure.
  - Successful resumption from a saved checkpoint.
- **End-to-End (E2E) Tests:**
  - Simulate `rs_*` stream aborts during the `plan` phase and verify that the subsequent `apply` is idempotent and results in a single, clean commit.
