# Offline Model (Ollama) Configuration and Usage

This project supports running non-interactive/background LLM tasks on a local Ollama server. This helps reduce cloud usage, improve privacy, and keep core automation working when the internet is unavailable.

Key points:
- Ollama runs locally and serves models over HTTP.
- DanteOrchestrator can route specific background tasks to a local provider (e.g., Ollama).
- The Ollama base URL and orchestrator offline routing are controlled via environment variables.

## Prerequisites
- Install Ollama: https://ollama.com
- Pull the model you plan to use (default shown below):
  - `ollama pull gemma3:12b`
- Start the server:
  - `ollama serve`

## Environment variables
The following environment variables control Ollama usage and offline orchestration:

- OLLAMA_BASE_URL
  - Description: Base URL to your local Ollama server.
  - Default: http://localhost:11434
  - Examples:
    - `export OLLAMA_BASE_URL=http://localhost:11434`
    - From Docker containers hitting host: `export OLLAMA_BASE_URL=http://host.docker.internal:11434`

- ORCHESTRATOR_OFFLINE_PROVIDER
  - Description: Which provider to use for offline/background tasks.
  - Default: ollama
  - Examples:
    - `export ORCHESTRATOR_OFFLINE_PROVIDER=ollama`

- ORCHESTRATOR_OFFLINE_MODEL
  - Description: The model slug used for offline/background LLM work (interpreted by the chosen provider).
  - Default: gemma3:12b
  - Examples:
    - `export ORCHESTRATOR_OFFLINE_MODEL=gemma3:12b`
    - `export ORCHESTRATOR_OFFLINE_MODEL=llama3.1:8b`

You can copy these into your `.env` or `.env.local` as needed. See `.env.example` for reference.

## How DanteOrchestrator uses Ollama
DanteOrchestrator decides which model/provider to use for a task. For certain background operations (e.g., summarization, classification, non-interactive analysis) it can prefer a locally available model via Ollama.

At a high level, the selection logic looks like this (illustrative snippet):

```ts
// Defaults if env vars are not provided
const DEFAULT_OLLAMA_BASE_URL = 'http://localhost:11434';
const DEFAULT_OFFLINE_PROVIDER = 'ollama';
const DEFAULT_OFFLINE_MODEL = 'gemma3:12b';

const baseURL = process.env.OLLAMA_BASE_URL ?? DEFAULT_OLLAMA_BASE_URL;
const offlineProvider = process.env.ORCHESTRATOR_OFFLINE_PROVIDER ?? DEFAULT_OFFLINE_PROVIDER;
const offlineModel = process.env.ORCHESTRATOR_OFFLINE_MODEL ?? DEFAULT_OFFLINE_MODEL;

// Somewhere in DanteOrchestrator when routing a background task:
// if (offlineProvider === 'ollama') {
//   provider = 'ollama';
//   model = offlineModel;
//   // use baseURL for the Ollama client
// }
```

Notes:
- The exact routing is handled in DanteOrchestrator so that interactive user-facing tasks can still use your primary cloud model while non-interactive or batch tasks can run locally.
- Ensure the model specified by `ORCHESTRATOR_OFFLINE_MODEL` is pulled and available in your Ollama install when using `ORCHESTRATOR_OFFLINE_PROVIDER=ollama`.

## Quick start
1) Install and start Ollama locally.
2) Pull a model: `ollama pull gemma3:12b` (or your preferred model).
3) Set env vars:
   - `export OLLAMA_BASE_URL=http://localhost:11434`
   - `export ORCHESTRATOR_OFFLINE_PROVIDER=ollama`
   - `export ORCHESTRATOR_OFFLINE_MODEL=gemma3:12b`
4) Start the app. Background tasks will now use your local model when applicable.

## Verification
- Verify Ollama is reachable: `curl $OLLAMA_BASE_URL/api/tags`
- Check logs on app start for something like: `Using offline model: gemma3:12b via ollama @ http://localhost:11434` (message may vary).

## Docker notes
If your app runs in Docker and Ollama runs on the host:
- Use `OLLAMA_BASE_URL=http://host.docker.internal:11434` on macOS/Windows.
- On Linux, consider network options (e.g., `--network=host`) or exposing port 11434 and using your host IP.

Example Ollama container (optional):

```bash
docker run --name ollama -d --restart unless-stopped \
  -p 11434:11434 \
  -v ollama:/root/.ollama \
  ollama/ollama:latest
```

Then pull models inside the container:

```bash
docker exec -it ollama ollama pull gemma3:12b
```

## Troubleshooting
- Connection refused: Ensure `ollama serve` is running and the port matches `OLLAMA_BASE_URL`.
- Model not found: Run `ollama pull <model>` first.
- Slow responses: Try a smaller model (e.g., `llama3.1:8b`) or ensure you have enough CPU/GPU resources.

## Security considerations
- Bind Ollama to localhost if you do not intend to expose it: use `http://localhost:11434`.
- Do not expose Ollama publicly without TLS and proper network controls.
