# Fine-Grained Orchestration Design Document

## 1. Rationale

The existing orchestrator delegates tasks at a coarse, category-level. This design introduces a fine-grained, dependency-aware orchestration system to enable parallel execution of sub-tasks, incremental progress tracking, strict context isolation, and robust error recovery. By partitioning large requests into smaller, independent `WorkUnit`s and managing them via a DAG, we can improve efficiency, resilience, and observability.

## 2. Architecture Overview

The proposed architecture introduces a new `OrchestrationEngine` that replaces the core delegation logic of the existing `TaskOrchestrator` when enabled. It operates on a DAG of `WorkUnit`s, managing their lifecycle from partitioning to completion.

```mermaid
graph TD
    A[OrchestrationRequest] --> B(HeuristicPartitioner);
    B --> C{PartitionPlan (DAG)};
    C --> D(WorkScheduler);
    D --> E{Ready WorkUnits};
    E --> F(AssignmentManager);
    F --> G[Assignment];
    G --> H(ContextPackager);
    H --> I[ContextPackage];
    I --> J(Agent);
    J --> K[ProgressEvent];
    K --> D;
    J --> L(ArtifactStore);
    L --> M[ArtifactRef];
    M --> D;
    D --> N(JsonLedger);
    N --> O((data/ledger/*.jsonl));

    subgraph OrchestrationEngine
        B; C; D; E; F; G; H; I; L; M; N;
    end

    subgraph External
        A; J; O;
    end
```

## 3. Module & File Layout

The new orchestration core will reside in `src/agents/orchestration/`.

- **[`src/agents/orchestration/types.ts`](src/agents/orchestration/types.ts:1):** Data models and enums.
- **[`src/agents/orchestration/ledger/JsonLedger.ts`](src/agents/orchestration/ledger/JsonLedger.ts:1):** Durable event ledger.
- **[`src/agents/orchestration/partitioning/HeuristicPartitioner.ts`](src/agents/orchestration/partitioning/HeuristicPartitioner.ts:1):** DAG construction logic.
- **[`src/agents/orchestration/context/ContextPackager.ts`](src/agents/orchestration/context/ContextPackager.ts:1):** Minimal context packaging.
- **[`src/agents/orchestration/agents/AgentRegistry.ts`](src/agents/orchestration/agents/AgentRegistry.ts:1):** Agent profile management.
- **[`src/agents/orchestration/scheduler/WorkScheduler.ts`](src/agents/orchestration/scheduler/WorkScheduler.ts:1):** Topological scheduler.
- **[`src/agents/orchestration/assign/AssignmentManager.ts`](src/agents/orchestration/assign/AssignmentManager.ts:1):** Agent selection and assignment.
- **[`src/agents/orchestration/pipelines/index.ts`](src/agents/orchestration/pipelines/index.ts:1):** Category-specific logic.
- **[`src/agents/orchestration/artifacts/ArtifactStore.ts`](src/agents/orchestration/artifacts/ArtifactStore.ts:1):** Artifact management.
- **[`src/agents/orchestration/OrchestrationEngine.ts`](src/agents/orchestration/OrchestrationEngine.ts:1):** Public API surface.
- **[`src/config/orchestrationConfig.ts`](src/config/orchestrationConfig.ts:1):** Configuration values.

## 4. Data Model Interfaces (`types.ts`)

All types will be defined in [`src/agents/orchestration/types.ts`](src/agents/orchestration/types.ts:1).

```typescript
// src/agents/orchestration/types.ts

export enum WorkCategory { CODING, DEBUG, RESEARCH, REVIEW }
export enum Intent { ADD, FIX, REFACTOR, TEST, DOCS, EXPLORE }
export enum ProgressEventType { STARTED, HEARTBEAT, BLOCKER, ARTIFACT, REVIEW_REQUEST, DONE, FAILED, CANCELLED }
export enum ArtifactType { DIFF, FILE, TEST_RESULT, LOG, DOC, UNKNOWN }

export interface OrchestrationRequest {
  id: string;
  intent: Intent;
  scopeHints: string[]; // file paths, component names, etc.
  userQuery: string;
  options?: {
    forceDelegation?: boolean;
    fineGrainedOrchestration?: boolean;
    [key: string]: any;
  };
}

export interface WorkUnit {
  id: string;
  category: WorkCategory;
  intent: Intent;
  scope: {
    files: string[];
    components: string[];
  };
  dependencies: string[]; // WorkUnit IDs
  acceptanceCriteria: string[];
  effort: {
    estimatedTokens: number;
    estimatedTimeMs: number;
  };
}

export interface PartitionPlan {
  runId: string;
  dag: { [id: string]: WorkUnit };
  entryPoints: string[]; // WorkUnit IDs with no dependencies
}

export interface AgentProfile {
  id: string;
  name: string;
  capabilities: WorkCategory[];
  concurrencyLimit: number;
  sla: {
    timeToFirstUpdateMs: number;
  };
  stats: {
    successRate: number;
    avgTokensPerTask: number;
  };
}

export interface Assignment {
  workUnitId: string;
  agentId: string;
  instructions: string; // Templated prompt
  contextPackageKey: string;
  budget: {
    maxTokens: number;
    maxTimeMs: number;
  };
}

export interface ContextPackage {
  key: string;
  content: string; // Minimal, stringified context
  tokenCount: number;
  artifactRefs: ArtifactRef[];
}

export interface ArtifactRef {
  id: string;
  workUnitId: string;
  type: ArtifactType;
  location: string; // e.g., file path, URL
  checksum: string;
}

export interface ProgressEvent {
  eventId: string;
  workUnitId: string;
  agentId: string;
  timestamp: number;
  type: ProgressEventType;
  payload: {
    message?: string;
    percentComplete?: number;
    risks?: string[];
    artifactRef?: ArtifactRef;
    [key: string]: any;
  };
}

export interface Ledger {
  runId: string;
  plan: PartitionPlan;
  events: ProgressEvent[];
}
```

## 5. `OrchestrationEngine` API

The public API surface will be exported from [`src/agents/orchestration/OrchestrationEngine.ts`](src/agents/orchestration/OrchestrationEngine.ts:1).

```typescript
// src/agents/orchestration/OrchestrationEngine.ts

import {
  OrchestrationRequest, PartitionPlan, WorkUnit, AgentProfile,
  Assignment, ContextPackage, ProgressEvent, Ledger, WorkCategory
} from './types';

export class OrchestrationEngine {
  plan(request: OrchestrationRequest): PartitionPlan;
  execute(plan: PartitionPlan, opts?: any): AsyncGenerator<ProgressEvent>;
  assign(workUnit: WorkUnit, agents: AgentProfile[]): Assignment;
  packageContext(workUnit: WorkUnit): ContextPackage;
  onProgress(evt: ProgressEvent): void;
  summarize(target: WorkUnit | { category: WorkCategory }): string;
  persistLedger(runId: string): Promise<void>;
  loadLedger(runId: string): Promise<Ledger>;
  metrics(): {
    timeToFirstUpdate: number;
    throughput: number;
    successRate: number;
    cost: number;
    tokenUsage: number;
  };
}
```

## 6. Core Components

### Ledger (`JsonLedger.ts`)

- **Storage:** Append-only JSONL files at [`data/ledger/{runId}.jsonl`](data/ledger/README.md).
- **Idempotency:** Records will have a composite key `(workUnitId, eventId)` to prevent duplicates.
- **Resume:** On load, the ledger reconstructs the last known state of the DAG, allowing the scheduler to restart incomplete or timed-out units.

### Partitioner (`HeuristicPartitioner.ts`)

- **Strategy:** Analyzes `scopeHints`, codebase dependencies (imports), and file ownership to create a DAG of `WorkUnit`s.
- **Boundaries:** Enforces configurable limits like `maxLOCPerUnit` and `maxFilesPerUnit`.
- **Dependencies:** Creates explicit dependencies between units (e.g., a `REVIEW` unit depends on a `CODING` unit).

### Scheduler (`WorkScheduler.ts`)

- **Algorithm:** Uses a topological sort of the DAG to execute units in order.
- **Concurrency:** Manages global, per-category, and per-agent concurrency limits.
- **Resilience:** Implements heartbeat monitoring, timeouts, and a retry mechanism with exponential backoff.

### Context Packager (`ContextPackager.ts`)

- **Isolation:** Creates minimal context packages for each `WorkUnit`, including only relevant file snippets and artifact references.
- **Constraints:** Enforces strict token caps to manage cost and improve agent focus.

## 7. Integration Plan

- The main entry point in [`TaskOrchestrator.executeTaskOrchestrator()`](src/agents/TaskOrchestrator.ts:518) will be modified to use the `OrchestrationEngine` when `options.context?.fineGrainedOrchestration` is true.
- An adapter will convert the existing inputs into an [`OrchestrationRequest`](src/agents/orchestration/types.ts:1).
- The [`AssignmentManager`](src/agents/orchestration/assign/AssignmentManager.ts:1) will wrap existing agent calls, formatting the `Assignment` into a structured prompt.

## 8. Configuration (`orchestrationConfig.ts`)

A new configuration file at [`src/config/orchestrationConfig.ts`](src/config/orchestrationConfig.ts:1) will centralize all orchestration settings.

```typescript
// src/config/orchestrationConfig.ts
export const orchestrationConfig = {
  maxLOCPerUnit: 200,
  maxFilesPerUnit: 5,
  tokenCaps: {
    contextPackage: 4096,
    summary: 1024,
  },
  concurrency: {
    global: 10,
    perCategory: { [WorkCategory.CODING]: 5 },
    perAgent: {},
  },
  retryPolicy: {
    maxRetries: 3,
    backoffMs: 1000,
  },
  heartbeatIntervalMs: 5000,
};
```

## 9. Testing Plan

Tests will be added in `src/tests/orchestration/`.

- **Unit Tests:**
  - [`partitioning.test.ts`](src/tests/orchestration/partitioning.test.ts:1): Verify correct DAG creation and unit sizing.
  - [`scheduler.test.ts`](src/tests/orchestration/scheduler.test.ts:1): Test topological execution, concurrency, and retries.
  - [`assignment.test.ts`](src/tests/orchestration/assignment.test.ts:1): Validate agent selection logic.
  - [`ledger.test.ts`](src/tests/orchestration/ledger.test.ts:1): Ensure idempotency and correct state resumption.
  - [`artifacts.test.ts`](src/tests/orchestration/artifacts.test.ts:1): Test artifact referencing and versioning.
- **Integration Tests:**
  - [`finegrained.e2e.test.ts`](src/tests/e2e/finegrained.e2e.test.ts:1): An end-to-end test partitioning a real task, executing it with multiple agents, and verifying the output.

## 10. Implementation Plan

1.  **Setup:** Create the directory structure and empty files outlined in Section 3.
2.  **Types:** Implement all interfaces and enums in [`src/agents/orchestration/types.ts`](src/agents/orchestration/types.ts:1).
3.  **Configuration:** Create [`src/config/orchestrationConfig.ts`](src/config/orchestrationConfig.ts:1).
4.  **Ledger:** Implement the `JsonLedger` with append, load, and resume logic.
5.  **Partitioner:** Implement the `HeuristicPartitioner`.
6.  **Context Packager:** Implement the `ContextPackager`.
7.  **Assignment & Registry:** Implement the `AgentRegistry` and `AssignmentManager`.
8.  **Scheduler:** Implement the `WorkScheduler`.
9.  **Engine:** Assemble all components in the `OrchestrationEngine` and expose the public API.
10. **Integration:** Modify `TaskOrchestrator` to use the new engine behind a feature flag.
11. **Testing:** Write unit and integration tests as specified.
