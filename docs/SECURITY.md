# Security Documentation - OpenAI Connectors

## 🔒 Security Features

### User Authentication & Authorization

#### ✅ Fixed: Critical User ID Validation Issue
**Issue**: The system was using 'default' as a fallback userId when authentication wasn't available, leading to potential token collision where multiple users could share the same token storage.

**Resolution**: Implemented strict user validation that:
- Never allows fallback to 'default' or other reserved IDs
- Validates email format when email is used as userId
- Prevents injection attacks by sanitizing user IDs
- Throws errors when authentication is missing rather than silently failing

**Implementation**: See `src/utils/userValidation.ts`

### Token Security

1. **Encryption at Rest**
   - All OAuth tokens are encrypted using AES-256-GCM
   - Encryption keys are derived using PBKDF2 with 100,000 iterations
   - Each token has a unique salt and initialization vector

2. **Token Isolation**
   - Tokens are strictly isolated by user ID
   - No shared token storage between users
   - Session keys use format: `{validatedUserId}:{connectorId}`

3. **Token Refresh**
   - Automatic refresh before expiration
   - Exponential backoff for retry logic
   - Secure storage of refresh tokens

### OAuth Security

1. **PKCE Implementation**
   - Proof Key for Code Exchange prevents authorization code interception
   - Code verifier and challenge generated using cryptographically secure random

2. **State Parameter Validation**
   - CSRF protection through state parameter
   - State includes user ID and connector ID
   - Validated on callback to prevent session fixation

3. **Redirect URI Validation**
   - Strict validation of redirect URIs
   - Only whitelisted domains allowed
   - No open redirects

### API Security

1. **Rate Limiting**
   - Per-user rate limiting with sliding window
   - Distributed rate limiting using Redis
   - Automatic blocking of suspicious activity

2. **Input Validation**
   - All user inputs are validated and sanitized
   - Email format validation for email-based IDs
   - Prevention of SQL injection and XSS attacks

3. **Approval Workflows**
   - Sensitive operations require approval
   - Role-based access control (RBAC)
   - Audit logging of all approvals

### Database Security

1. **Parameterized Queries**
   - All database queries use parameterized statements
   - No string concatenation for SQL queries
   - Protection against SQL injection

2. **Connection Security**
   - TLS/SSL for database connections
   - Connection pooling with secure credentials
   - Environment-based configuration

### Monitoring & Auditing

1. **Audit Logging**
   - All authentication attempts logged
   - Token access and refresh logged
   - Failed authentication tracking for rate limiting

2. **Security Monitoring**
   - Real-time alerting for suspicious activity
   - Failed authentication threshold monitoring
   - Token expiration tracking

3. **Error Handling**
   - Secure error messages (no sensitive data exposure)
   - Different messages for authentication vs authorization failures
   - Stack traces hidden in production

## 🚨 Security Best Practices

### For Developers

1. **Never Use Default Values for User IDs**
   ```typescript
   // ❌ WRONG - Security vulnerability
   const userId = req.userId || 'default';
   
   // ✅ CORRECT - Proper validation
   const userId = validateUserId(req.userId, 'operation name');
   ```

2. **Always Validate User Input**
   ```typescript
   // Always use the validation utilities
   import { validateUserId, requireAuthenticatedUser } from '@/utils/userValidation';
   
   // In your route handler
   const userId = requireAuthenticatedUser(req, 'connector access');
   ```

3. **Use Parameterized Queries**
   ```typescript
   // ❌ WRONG - SQL injection risk
   db.query(`SELECT * FROM tokens WHERE user_id = '${userId}'`);
   
   // ✅ CORRECT - Parameterized query
   db.query('SELECT * FROM tokens WHERE user_id = ?', [userId]);
   ```

4. **Handle Errors Securely**
   ```typescript
   // ❌ WRONG - Exposes internal details
   catch (error) {
     res.status(500).json({ error: error.stack });
   }
   
   // ✅ CORRECT - Safe error message
   catch (error) {
     console.error('Internal error:', error); // Log full error
     res.status(500).json({ error: 'An error occurred processing your request' });
   }
   ```

### For System Administrators

1. **Environment Variables**
   - Never commit `.env` files to version control
   - Use strong, unique values for `DANTE_MASTER_KEY`
   - Rotate encryption keys regularly
   - Use different keys for different environments

2. **Network Security**
   - Always use HTTPS in production
   - Configure proper CORS policies
   - Use firewall rules to restrict database access
   - Enable rate limiting at the reverse proxy level

3. **Regular Updates**
   - Keep all dependencies updated
   - Monitor security advisories
   - Perform regular security audits
   - Use automated vulnerability scanning

4. **Backup & Recovery**
   - Regular encrypted backups
   - Test recovery procedures
   - Store backups in secure locations
   - Maintain audit logs separately from main system

## 🔐 Security Checklist

### Pre-Deployment

- [ ] All user IDs validated using `validateUserId()`
- [ ] No hardcoded credentials in code
- [ ] Environment variables properly configured
- [ ] Database using parameterized queries
- [ ] Rate limiting enabled
- [ ] HTTPS configured
- [ ] CORS properly configured
- [ ] Error messages don't expose sensitive data

### Post-Deployment

- [ ] Monitor authentication failures
- [ ] Review audit logs regularly
- [ ] Check for unusual token usage patterns
- [ ] Verify rate limiting is working
- [ ] Test token refresh mechanism
- [ ] Validate approval workflows
- [ ] Check for expired sessions
- [ ] Review access patterns for anomalies

## 📊 Security Metrics to Monitor

1. **Authentication Metrics**
   - Failed login attempts per user
   - Token refresh frequency
   - Session duration patterns
   - Concurrent sessions per user

2. **Authorization Metrics**
   - Approval request patterns
   - Rejected approvals
   - Privilege escalation attempts
   - Cross-user access attempts

3. **System Metrics**
   - API rate limit hits
   - Database query patterns
   - Error rates by endpoint
   - Response time anomalies

## 🚫 Common Vulnerabilities Prevented

1. **Token Collision** - Fixed by proper user validation
2. **Session Fixation** - Prevented by state validation
3. **CSRF Attacks** - Mitigated by state parameter
4. **SQL Injection** - Prevented by parameterized queries
5. **XSS Attacks** - Input sanitization and validation
6. **Timing Attacks** - Constant-time comparisons
7. **Brute Force** - Rate limiting and account lockout
8. **Information Disclosure** - Secure error handling

## 📞 Security Contact

For security issues or vulnerabilities:
- Email: <EMAIL>
- Use responsible disclosure practices
- Allow 90 days for patch development
- Security patches will be prioritized

---

Last Updated: December 2024
Version: 1.0.0