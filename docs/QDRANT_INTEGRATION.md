# Qdrant Vector Database Integration

This document describes the Qdrant vector database integration for Dante GPT's memory system.

## Overview

Qdrant provides a high-performance, persistent vector database that enables:
- Shared memory between CLI and web applications
- Semantic search with sub-second latency
- Scalability to millions of vectors
- Advanced filtering and hybrid search capabilities
- Data persistence with Docker volumes

## Quick Start

### 1. Start Qdrant with Docker

```bash
# Start Qdrant and the optional web UI
bun run docker:up

# Check logs
bun run docker:logs

# Stop Qdrant
bun run docker:down
```

### 2. Configure Environment

Add to your `.env` file:

```env
# Enable Qdrant (optional - auto-detected if QDRANT_URL is set)
QDRANT_URL=http://localhost:6333
# QDRANT_API_KEY=your-api-key  # Only if authentication is enabled
USE_QDRANT=true
```

### 3. Migrate Existing Memories

If you have existing memories in file or local storage:

```bash
# Migrate from file storage
bun run migrate-memories --source file

# Migrate from JSON export
bun run migrate-memories --source json --source-path ./memories.json

# Dry run to preview migration
bun run migrate-memories --source file --dry-run

# Verify migration
bun run migrate-memories --source file --verify
```

## Architecture

### Storage Hierarchy

1. **QdrantMemoryStore** (Primary)
   - Persistent vector storage
   - Shared between all clients
   - Optimized for similarity search

2. **LocalMemoryStore** (Cache)
   - In-memory cache for fast access
   - Session-specific data
   - Reduced latency for frequent queries

3. **FileMemoryStore** (Backup)
   - Project-specific memories
   - JSON file storage
   - Disaster recovery

### Data Flow

```
User Query
    ↓
MemoryManager
    ↓
QdrantMemoryStore ← → Qdrant Docker Container
    ↓                     ↓
LocalStore (cache)   Persistent Volume
```

## API Endpoints

### Vector-Specific Endpoints

```typescript
// Find related memories
GET /api/memory/related/:id?topK=5&threshold=0.8

// Cluster memories
POST /api/memory/cluster
Body: { k: 5 }

// Get vector store statistics
GET /api/memory/vector-stats

// Hybrid search with filters
POST /api/memory/hybrid-search
Body: {
  query: "search text",
  filters: {
    type: "SEMANTIC",
    tags: ["project"],
    minConfidence: 0.7
  },
  topK: 10
}

// Reindex vectors
POST /api/memory/reindex

// Create snapshot
POST /api/memory/snapshot
```

## Memory Types

The system stores various memory types with embeddings:

- **Semantic**: Facts, knowledge, concepts
- **Episodic**: Events, interactions, experiences  
- **Procedural**: How-to knowledge, solutions
- **Working**: Temporary session data

## Performance Optimization

### Qdrant Configuration

The system uses optimized settings in `qdrant_config/config.yaml`:

- **HNSW Index**: M=16, ef_construct=100 for balance of speed/accuracy
- **Memory Mapping**: Enabled for large datasets
- **Batch Processing**: 100 vectors per batch
- **Segment Optimization**: Auto-merging and vacuum

### Embedding Strategy

- Embeddings generated using OpenAI's text-embedding-ada-002
- 1536-dimensional vectors
- Cosine similarity for distance metric
- Automatic re-embedding on content updates

## Monitoring

### Qdrant Web UI

Access at http://localhost:6335 when running:
- View collections and statistics
- Test searches manually
- Monitor performance metrics

### CLI Commands

```bash
# View Qdrant logs
bun run docker:logs

# Check container status
docker ps | grep dante-qdrant

# Access Qdrant directly
curl http://localhost:6333/collections
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure Docker is running: `docker ps`
   - Check Qdrant is up: `bun run docker:up`
   - Verify port 6333 is not in use

2. **Migration Failures**
   - Check for memories without embeddings
   - Verify OpenAI API key for embedding generation
   - Use `--dry-run` to preview issues

3. **Performance Issues**
   - Monitor with `docker stats dante-qdrant`
   - Check index status via API
   - Consider reindexing: `POST /api/memory/reindex`

### Reset and Recovery

```bash
# Stop and remove containers
docker-compose down

# Remove volumes (WARNING: deletes all data)
docker-compose down -v

# Restart fresh
bun run docker:up

# Re-migrate memories
bun run migrate-memories --source file
```

## Advanced Usage

### Custom Collection Configuration

```typescript
// In QdrantMemoryStore constructor
new QdrantMemoryStore({
  collectionName: 'custom_memories',
  dimension: 1536,
  metric: 'cosine'
});
```

### Bulk Operations

```typescript
// Bulk import memories
const memories = [...]; // Array of Memory objects
await qdrantStore.bulkStore(memories);

// Bulk delete
const idsToDelete = ['mem_1', 'mem_2', ...];
await qdrantStore.bulkDelete(idsToDelete);
```

### Clustering Analysis

```typescript
// Cluster memories into groups
const clusters = await qdrantStore.clusterMemories(5);
clusters.forEach((memories, clusterId) => {
  console.log(`Cluster ${clusterId}: ${memories.length} memories`);
});
```

## Production Deployment

For production environments:

1. **Enable Authentication**:
   ```yaml
   # docker-compose.yml
   environment:
     - QDRANT__SERVICE__API_KEY=your-secure-key
   ```

2. **Configure Backups**:
   ```bash
   # Create snapshot
   curl -X POST http://localhost:6333/collections/dante_memories/snapshots
   ```

3. **Scale Resources**:
   - Adjust Docker memory limits
   - Configure multiple Qdrant nodes for HA
   - Use SSD storage for volumes

4. **Monitor Performance**:
   - Set up Prometheus/Grafana
   - Track query latencies
   - Monitor memory usage

## Future Enhancements

Planned improvements:
- Multi-tenancy support for user isolation
- Real-time memory synchronization via WebSockets
- Advanced memory consolidation algorithms
- Integration with other vector databases (Pinecone, Weaviate)
- Memory versioning and rollback capabilities