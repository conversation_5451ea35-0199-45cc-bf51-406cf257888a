# Playwright MCP Browser Navigation Integration Guide

## Overview

Dante now has advanced browser automation capabilities through Playwright MCP integration. This enables intelligent web navigation, element discovery, and interaction with dynamic web content without relying on screenshot analysis or vision models.

## Key Features

### 1. **Intelligent Element Discovery**
- Automatically finds interactive elements using CSS selectors
- Uses <PERSON><PERSON>'s accessibility tree for reliable element targeting
- No pixel-based interactions - all selector-based for precision

### 2. **Dynamic Web Interaction**
- Navigate to URLs and handle redirects
- Click buttons, links, and interactive elements
- Fill and submit forms
- Scroll to find content
- Extract structured data from pages
- Handle JavaScript-rendered content

### 3. **Multi-Page Navigation**
- Navigate through pagination
- Follow links across pages
- Maintain session state
- Handle browser history (back/forward)

## Architecture

### Components

1. **Playwright MCP Server** (`@playwright/mcp`)
   - Provides browser automation via MCP protocol
   - Manages browser instances
   - Executes Playwright commands

2. **Browser Navigation Tools** (`src/tools/browserNavigationTools.ts`)
   - Wrapper tools for Playwright operations
   - Simplified interfaces for common tasks
   - Error handling and retry logic

3. **Selector Discovery Utilities** (`src/utils/selectorDiscovery.ts`)
   - Intelligent selector generation
   - Multiple selector strategies with fallbacks
   - Stability assessment for selectors

4. **Browser Navigation Agent** (`src/agents/BrowserNavigationAgent.ts`)
   - Specialized agent for complex navigation tasks
   - Multi-step workflow execution
   - Adaptive navigation strategies

## Configuration

### Environment Variables

```bash
# Enable Playwright MCP
MCP_PLAYWRIGHT_ENABLED=true

# Browser mode (false for visible browser, true for headless)
PLAYWRIGHT_HEADLESS=false
```

### MCP Server Configuration

The Playwright server is configured in `src/config/mcpConfig.ts`:

```typescript
{
  id: 'playwright-browser',
  name: 'Playwright Browser Automation',
  type: 'stdio',
  config: {
    fullCommand: 'npx @playwright/mcp --caps vision',
    cacheToolsList: true,
    connectionTimeout: 30000
  },
  priority: 95,
  tags: ['browser', 'automation', 'web', 'research']
}
```

## Available Tools

### Core Navigation Tools

1. **browser_navigate** - Navigate to URLs
2. **browser_find_elements** - Discover interactive elements
3. **browser_click** - Click elements by selector or text
4. **browser_fill_form** - Fill form fields
5. **browser_scroll** - Scroll to elements or positions
6. **browser_extract_data** - Extract structured data
7. **browser_wait_for** - Wait for content to appear
8. **browser_page_info** - Get page information

## Usage Examples

### Example 1: Basic Navigation

```typescript
// Navigate and extract information
await browser_navigate({ url: "https://example.com" });
const info = await browser_page_info();
const data = await browser_extract_data({
  selectors: [
    { name: "title", selector: "h1" },
    { name: "links", selector: "a", multiple: true }
  ]
});
```

### Example 2: Form Interaction

```typescript
// Fill and submit a form
await browser_navigate({ url: "https://example.com/login" });
await browser_fill_form({
  fields: [
    { selector: "input[name='email']", value: "<EMAIL>" },
    { selector: "input[name='password']", value: "password" }
  ],
  submitSelector: "button[type='submit']"
});
```

### Example 3: Content Search

```typescript
// Find and click specific content
await browser_navigate({ url: "https://example.com" });
await browser_click({ text: "Learn More" });
await browser_wait_for({ selector: ".content-section" });
const content = await browser_extract_data({
  selectors: [{ name: "details", selector: ".content-section" }]
});
```

## Selector Strategies

### Priority Order (Most to Least Stable)

1. **ID Selectors** (`#elementId`) - Most reliable
2. **Data Attributes** (`[data-testid="..."]`) - Very stable
3. **Semantic HTML** (`button`, `nav`, `main`) - Stable
4. **Class Combinations** (`.class1.class2`) - Moderately stable
5. **Text Content** (`:has-text("...")`) - Content-dependent
6. **Position-based** (`:nth-child(n)`) - Least stable

### Best Practices

- Prefer semantic selectors over generic ones
- Use text content for buttons and links
- Combine multiple attributes for uniqueness
- Test selectors for stability before relying on them
- Use fallback selectors for critical elements

## Integration with Research Agent

The Research Agent now includes browser navigation tools for enhanced web research:

```typescript
// Research Agent can now:
1. Navigate to specific pages
2. Search within websites
3. Extract data from JavaScript-heavy sites
4. Follow pagination to gather all results
5. Interact with dynamic content
```

## Advantages Over Previous System

### Comparison with computerUse Tools

| Feature | Previous (computerUse) | New (Playwright MCP) |
|---------|------------------------|---------------------|
| Interaction Method | Screenshot + Coordinates | CSS Selectors |
| Speed | Slow (screenshot analysis) | Fast (direct DOM access) |
| Reliability | Variable | High |
| Token Usage | High | Low |
| Dynamic Content | Limited | Full Support |
| Session Management | Complex | Built-in |
| Debugging | Difficult | Easy (visible selectors) |

## Troubleshooting

### Common Issues

1. **MCP Server Not Connecting**
   - Check if Playwright is installed: `npm list @playwright/mcp`
   - Verify MCP_PLAYWRIGHT_ENABLED is true
   - Check server logs for connection errors

2. **Elements Not Found**
   - Verify selector syntax
   - Use browser_wait_for before interacting
   - Try alternative selectors (text-based, role-based)
   - Check if element is in an iframe

3. **Dynamic Content Issues**
   - Add explicit waits for content to load
   - Use JavaScript evaluation for complex checks
   - Handle loading states and spinners

## Testing

Run the browser navigation test:

```bash
bun run test-browser-navigation.ts
```

This will test:
- Basic navigation
- Element discovery
- Form interaction
- Data extraction
- Multi-page navigation

## Future Enhancements

- [ ] Cookie and storage management
- [ ] Multi-tab handling
- [ ] File upload/download support
- [ ] Browser profile persistence
- [ ] Parallel browser sessions
- [ ] Visual regression testing
- [ ] Network request interception

## Migration from computerUse

To migrate existing computerUse workflows:

1. Replace `computer_use_dispatcher` calls with browser navigation tools
2. Convert coordinate-based interactions to selector-based
3. Remove screenshot analysis logic
4. Use structured data extraction instead of OCR
5. Simplify error handling with deterministic selectors

## Conclusion

The Playwright MCP integration provides Dante with powerful, reliable browser automation capabilities. By using CSS selectors and the accessibility tree instead of vision-based approaches, Dante can now navigate websites more efficiently, extract data more accurately, and handle dynamic content with ease.