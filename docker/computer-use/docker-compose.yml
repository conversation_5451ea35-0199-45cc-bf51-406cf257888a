version: '3.8'

services:
  computer-use-env:
    build:
      context: .
      dockerfile: Dockerfile
    platform: linux/amd64
    container_name: dante-computer-use
    ports:
      - "5900:5900"  # VNC Server
    environment:
      - DISPLAY=:99
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "pgrep", "-f", "Xvfb"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
