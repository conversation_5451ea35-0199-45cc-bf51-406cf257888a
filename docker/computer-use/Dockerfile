# Dockerfile for Computer Use Environment
FROM ubuntu:22.04

ENV DEBIAN_FRONTEND=noninteractive

# Install desktop environment and VNC server
RUN apt-get update && apt-get install -y \
    ca-certificates \
    gnupg \
    xfce4 \
    xfce4-goodies \
    x11vnc \
    xvfb \
    xdotool \
    imagemagick \
    x11-apps \
    sudo \
    software-properties-common \
    wget \
    curl \
    git \
    vim \
    && apt-get remove -y light-locker xfce4-screensaver xfce4-power-manager || true \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install additional browsers
# Install Google Chrome using keyring (avoid deprecated apt-key and Snap Firefox)
RUN install -m 0755 -d /etc/apt/keyrings \
    && curl -fsSL https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /etc/apt/keyrings/google-chrome.gpg \
    && chmod a+r /etc/apt/keyrings/google-chrome.gpg \
    && echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/google-chrome.gpg] https://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -ms /bin/bash dante \
    && echo "dante ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

USER dante
WORKDIR /home/<USER>

# Set VNC password
RUN x11vnc -storepasswd dante /home/<USER>/.vncpass

# Create startup script
RUN echo '#!/bin/bash\n\
Xvfb :99 -screen 0 1280x800x24 >/dev/null 2>&1 &\n\
x11vnc -display :99 -forever -rfbauth /home/<USER>/.vncpass -listen 0.0.0.0 -rfbport 5900 >/dev/null 2>&1 &\n\
export DISPLAY=:99\n\
startxfce4 >/dev/null 2>&1 &\n\
sleep 2\n\
echo "Computer Use Environment Ready!"\n\
echo "VNC Server: localhost:5900 (password: dante)"\n\
echo "Display: :99"\n\
tail -f /dev/null\n\
' > /home/<USER>/start.sh && chmod +x /home/<USER>/start.sh

# Expose VNC port
EXPOSE 5900

# Set environment variables
ENV DISPLAY=:99

# Start the environment
CMD ["/home/<USER>/start.sh"]
