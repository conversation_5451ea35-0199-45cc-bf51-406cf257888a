# Email Sending Configuration and Fallback Strategies

This document outlines the required environment variables for configuring email sending functionality and provides fallback strategies for different scenarios.

## Required Environment Variables

The `email_sender.py` script relies on the following environment variables for SMTP (Simple Mail Transfer Protocol) client configuration:

-   `SMTP_HOST`: The hostname of the SMTP server (e.g., `smtp.gmail.com`).
-   `SMTP_PORT`: The port number for the SMTP server (e.g., `587` for TLS/STARTTLS, `465` for SSL).
-   `SMTP_USERNAME`: The username for authenticating with the SMTP server (typically your email address).
-   `SMTP_PASSWORD`: The password for authenticating with the SMTP server.
-   `SENDER_EMAIL`: The email address from which the emails will be sent. This should generally match `SMTP_USERNAME`.

**Example `export` commands (for Bash/Zsh):**

```bash
export SMTP_HOST="smtp.gmail.com"
export SMTP_PORT="587" # or 465 for SSL
export SMTP_USERNAME="<EMAIL>"
export SMTP_PASSWORD="your_email_password" # Or App Password for Gmail
export SENDER_EMAIL="<EMAIL>"
```

## Fallback Strategies

It's crucial to have robust authentication for sending emails. Here are common strategies and fallbacks:

### 1. Using a Dedicated SMTP Service (Recommended)

For production applications, it's best to use a dedicated transactional email service (e.g., SendGrid, Mailgun, AWS SES). These services offer:
-   Higher deliverability rates.
-   Better analytics and logging.
-   Robust APIs and scaling.
-   Clear authentication mechanisms (often API keys rather than direct email passwords).

Configure `SMTP_HOST`, `SMTP_PORT`, `SMTP_USERNAME`, and `SMTP_PASSWORD` according to the credentials provided by your chosen service.

### 2. Gmail with an App Password (For Gmail Users)

If you are using a Gmail account to send emails, directly using your Gmail account password is **not recommended** due to security implications and Google's increased security measures (e.g., blocking "less secure apps"). Instead, you should use a **Gmail App Password**.

**How to generate a Gmail App Password:**

1.  **Enable 2-Step Verification:** Your Google Account must have 2-Step Verification turned on.
2.  Go to your [Google Account](https://myaccount.google.com/).
3.  Navigate to **Security** on the left-hand menu.
4.  Under "How you sign in to Google," select **App passwords**. You may need to sign in again.
5.  From the "Select app" dropdown, choose **Mail**.
6.  From the "Select device" dropdown, choose **Other (Custom name)** and enter a name (e.g., "Python Email Sender").
7.  Click **Generate**.
8.  A 16-character code will be displayed in the yellow bar. This is your App Password.
9.  Use this generated App Password for the `SMTP_PASSWORD` environment variable.

**Note:** Once you close the window, you won't see the App Password again. If you lose it, you'll need to generate a new one.

### 3. Using Other Email Providers

For other email providers (e.g., Outlook, Yahoo), consult their documentation for specific SMTP server settings and authentication requirements. Many providers also offer similar "app password" or "application-specific password" features if 2-Step Verification is enabled.

Always ensure your `SMTP_PASSWORD` (or App Password) is kept secure and never committed directly into your codebase. Use environment variables exclusively for sensitive credentials.
