module.exports = {
  apps: [{
    name: "daily-briefing-scheduler",
    cwd: "/Users/<USER>/dante-gpt",
    script: "/Users/<USER>/dante-gpt/.venv/bin/python",
    args: ["-u", "/Users/<USER>/dante-gpt/daily_briefing_scheduler.py"],
    exec_mode: "fork",
      env: {
        TZ: 'America/New_York',
        // Email settings (set these in your environment or via pm2 ecosystem overrides)
        // Required for email_sender.py: SMTP_HOST, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD, SENDER_EMAIL
        // Example (do NOT commit real credentials):
        SMTP_HOST: 'smtp.gmail.com',
        SMTP_PORT: '465',
        SMTP_USERNAME: '<EMAIL>',
        SMTP_PASSWORD: '3193468Dl*',
        SENDER_EMAIL: '<EMAIL>',
        TO_EMAIL: '<EMAIL>',
      },
      out_file: './logs/pm2/daily-briefing.out.log',
      error_file: './logs/pm2/daily-briefing.err.log',
    },
  ],
};
