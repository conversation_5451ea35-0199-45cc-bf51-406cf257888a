# JSONL Ledger Storage

This directory stores append-only JSONL ledgers for orchestration runs.

- File path: `data/ledger/{runId}.jsonl`
- Each line is a single JSON object (UTF-8), representing an immutable record.
- Latest `plan` snapshot wins during load; assignments/events are deduped by idempotency keys; artifacts are accumulated (last ref by id kept in memory).

Record format (one per line):
{
  "runId": "RUN_ID",
  "ts": 1700000000000, // ms since epoch
  "type": "plan" | "assignment" | "event" | "artifact" | "summary" | "rollup",
  "data": { ... } // shape depends on type
}

Types and data payloads:
- type = "plan": data = PartitionPlan
- type = "assignment": data = Assignment
- type = "event": data = ProgressEvent
- type = "artifact": data = ArtifactRef[]
- type = "summary": data = { "scope": string, "summary": string }
- type = "rollup": data = { "key": string, "value": any }

Idempotency keys:
- Assignments: dedupe by `assignment.id` (append is skipped if seen)
- Events: dedupe by `${workUnitId}:${eventId}` (append is skipped if seen)
- Artifacts: appended; in-memory index keeps last reference by `artifact.id`
- Plan: latest snapshot wins (no dedupe; simply append and load last)

Example lines:
{"runId":"run-123","ts":1700000001000,"type":"plan","data":{"id":"plan-1","requestId":"run-123","units":[],"createdAt":1700000000000}}
{"runId":"run-123","ts":1700000002000,"type":"assignment","data":{"id":"asg-1","workUnitId":"wu-1","agentId":"agent-A","contextPackage":{"id":"ctx-1","artifacts":[],"constraints":{"tokenCap":8192}},"status":"queued"}}
{"runId":"run-123","ts":1700000003000,"type":"event","data":{"eventId":"ev-1","type":"started","workUnitId":"wu-1","timestamp":1700000003000}}
{"runId":"run-123","ts":1700000004000,"type":"artifact","data":[{"id":"art-1","type":"file","path":"src/foo.ts"}]}
{"runId":"run-123","ts":1700000005000,"type":"summary","data":{"scope":"wu-1","summary":"Initial analysis"}}
{"runId":"run-123","ts":1700000006000,"type":"rollup","data":{"key":"costUSD","value":1.23}}

Notes:
- Files are append-only. Consumers must parse line-by-line and reconstruct state.
- Timestamps (`ts`) are write-time and used to compute `lastUpdated` in the in-memory Ledger.
