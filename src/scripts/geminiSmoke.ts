import { geminiClient, GeminiClient } from '../utils/geminiClient';
import { validateGeminiConfig, config } from '../utils/config';

async function runNonStreaming() {
  console.log('--- Gemini non-streaming smoke test ---');
  const res = await geminiClient.createChatCompletion({
    model: config.gemini.defaultModel || 'gemini-2.5-flash',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Say a short hello and name your model family.' },
    ],
    max_tokens: 200,
    thinking: GeminiClient.createThinkingConfig('simple', true),
  });

  const content = res.choices?.[0]?.message?.content || '';
  const thinking = res.choices?.[0]?.message?.thinking || null;
  console.log('Text length:', content.length);
  console.log('Includes "gemini" (case-insensitive):', /gemini/i.test(content));
  console.log('Reasoning present:', <PERSON><PERSON><PERSON>(thinking));
  if (res.usage) {
    console.log('Usage:', {
      prompt_tokens: res.usage.prompt_tokens,
      completion_tokens: res.usage.completion_tokens,
      total_tokens: res.usage.total_tokens,
    });
  }
}

async function runStreaming() {
  console.log('--- Gemini streaming smoke test ---');
  const stream = await geminiClient.createStreamingCompletion({
    model: config.gemini.defaultModel || 'gemini-2.5-flash',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Explain in two sentences what Vercel AI SDK is.' },
    ],
      max_tokens: 300,
    thinking: GeminiClient.createThinkingConfig('simple', false),
  });

  let text = '';
  if ((stream as any).textStream && typeof (stream as any).textStream[Symbol.asyncIterator] === 'function') {
    for await (const delta of (stream as any).textStream) {
      text += String(delta);
    }
  } else if (typeof (stream as any)[Symbol.asyncIterator] === 'function') {
    // Fallback: generic async iterator
    for await (const chunk of stream as any) {
      const str = String(chunk);
      text += str;
    }
  }
  console.log('Streamed text length:', text.length);
  console.log('Mentions SDK (case-insensitive):', /sdk/i.test(text));
}

async function main() {
  if (!validateGeminiConfig()) {
    console.error('GEMINI_API_KEY/GOOGLE_GENERATIVE_AI_API_KEY not configured. Aborting smoke test.');
    process.exit(1);
  }

  try {
    await runNonStreaming();
    await runStreaming();
  } catch (err) {
    console.error('Gemini smoke test failed:', err);
    process.exit(1);
  }
}

void main();
