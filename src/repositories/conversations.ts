import { Database as BunDatabase } from 'bun:sqlite';
import { Pool } from 'pg';
import { existsSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { toSQLiteTimestamp } from '../utils/date';
import { ensureConversationSchemaReady } from './schemaInit';

export interface Conversation {
  id: string;
  title: string;
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
}

type DBType = 'sqlite' | 'postgresql';

let dbType: DBType | null = null;
let sqliteDb: BunDatabase | null = null;
let pgPool: Pool | null = null;
let initPromise: Promise<void> | null = null;


function resolveDbType(): DBType {
  const envType = (process.env.DATABASE_TYPE || '').toLowerCase();
  if (envType === 'postgres' || envType === 'postgresql') return 'postgresql';
  // Detect Postgres via DATABASE_URL
  if ((process.env.DATABASE_URL || '').startsWith('postgres')) return 'postgresql';
  return 'sqlite';
}

async function ensureInitialized(): Promise<void> {
  if (initPromise) return initPromise;

  initPromise = (async () => {
    dbType = resolveDbType();

    // Ensure backing tables exist before any queries run
    await ensureConversationSchemaReady();

    if (dbType === 'sqlite') {
      const filename = process.env.SQLITE_DB_FILE || join(process.cwd(), 'data', 'tokens.db');
      const dir = dirname(filename);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      sqliteDb = new BunDatabase(filename);
      // Use exec to run PRAGMA statements consistently with other repositories
      sqliteDb.exec('PRAGMA journal_mode = WAL');
      sqliteDb.exec('PRAGMA foreign_keys = ON');
      return;
    }

    // PostgreSQL connection (no schema creation here; handled by centralized migrations)
    const connectionString =
      process.env.DATABASE_URL ||
      process.env.PG_CONNECTION_STRING ||
      'postgres://localhost:5432/postgres';

    pgPool = new Pool({ connectionString });
  })();

  return initPromise;
}

function mapRowToConversation(row: any): Conversation {
  return {
    id: row.id,
    title: row.title,
    summary: row.summary ?? undefined,
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at),
  };
}

export async function createConversation(conversation: Conversation): Promise<Conversation> {
  await ensureInitialized();
  const now = new Date();
  const createdAt = conversation.createdAt ?? now;
  const updatedAt = conversation.updatedAt ?? now;

  if (dbType === 'sqlite' && sqliteDb) {
    // Try update first, fallback to insert
    const update = sqliteDb.prepare(`
      UPDATE conversations
      SET title = ?, summary = ?, updated_at = ?
      WHERE id = ?
    `);
    const updateRes = update.run(conversation.title, conversation.summary ?? null, toSQLiteTimestamp(updatedAt), conversation.id);

    if ((updateRes as any).changes === 0) {
      const insert = sqliteDb.prepare(`
        INSERT INTO conversations (id, title, summary, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      insert.run(
        conversation.id,
        conversation.title,
        conversation.summary ?? null,
        toSQLiteTimestamp(createdAt),
        toSQLiteTimestamp(updatedAt),
      );
    }

    const row = sqliteDb.prepare(`SELECT * FROM conversations WHERE id = ?`).get(conversation.id) as any;
    return mapRowToConversation(row);
  }

  // PostgreSQL
  if (pgPool) {
    const result = await pgPool.query(
      `
      INSERT INTO conversations (id, title, summary, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (id) DO UPDATE SET
        title = EXCLUDED.title,
        summary = EXCLUDED.summary,
        updated_at = EXCLUDED.updated_at
      RETURNING *
    `,
      [conversation.id, conversation.title, conversation.summary ?? null, createdAt, updatedAt],
    );
    return mapRowToConversation(result.rows[0]);
  }

  throw new Error('Database not initialized');
}

export async function getConversationById(id: string): Promise<Conversation | undefined> {
  await ensureInitialized();

  if (dbType === 'sqlite' && sqliteDb) {
    const row = sqliteDb.prepare(`SELECT * FROM conversations WHERE id = ?`).get(id) as any;
    return row ? mapRowToConversation(row) : undefined;
  }

  if (pgPool) {
    const result = await pgPool.query(`SELECT * FROM conversations WHERE id = $1`, [id]);
    return result.rows[0] ? mapRowToConversation(result.rows[0]) : undefined;
  }

  throw new Error('Database not initialized');
}

export async function updateConversationSummary(conversationId: string, summary: string): Promise<void> {
  await ensureInitialized();

  if (dbType === 'sqlite' && sqliteDb) {
    const stmt = sqliteDb.prepare(`
      UPDATE conversations
      SET summary = ?, updated_at = ?
      WHERE id = ?
    `);
    stmt.run(summary, toSQLiteTimestamp(new Date()), conversationId);
    return;
  }

  if (pgPool) {
    await pgPool.query(
      `UPDATE conversations SET summary = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`,
      [summary, conversationId],
    );
    return;
  }

  throw new Error('Database not initialized');
}

// Optional helper to list recent conversations (useful for UIs/debug)
export async function listConversations(limit = 50): Promise<Conversation[]> {
  await ensureInitialized();

  if (dbType === 'sqlite' && sqliteDb) {
    const rows = sqliteDb
      .prepare(`SELECT * FROM conversations ORDER BY updated_at DESC LIMIT ?`)
      .all(limit) as any[];
    return rows.map(mapRowToConversation);
  }

  if (pgPool) {
    const result = await pgPool.query(
      `SELECT * FROM conversations ORDER BY updated_at DESC LIMIT $1`,
      [limit],
    );
    return result.rows.map(mapRowToConversation);
  }

  throw new Error('Database not initialized');
}
