import { Database as BunDatabase } from 'bun:sqlite';
import { Pool } from 'pg';
import { existsSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { toSQLiteTimestamp } from '../utils/date';
import { ensureConversationSchemaReady } from './schemaInit';

export interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  createdAt: Date;
}

type DBType = 'sqlite' | 'postgresql';

let dbType: DBType | null = null;
let sqliteDb: BunDatabase | null = null;
let pgPool: Pool | null = null;
let initPromise: Promise<void> | null = null;


function resolveDbType(): DBType {
  const envType = (process.env.DATABASE_TYPE || '').toLowerCase();
  if (envType === 'postgres' || envType === 'postgresql') return 'postgresql';
  if ((process.env.DATABASE_URL || '').startsWith('postgres')) return 'postgresql';
  return 'sqlite';
}

async function ensureInitialized(): Promise<void> {
  if (initPromise) return initPromise;

  initPromise = (async () => {
    dbType = resolveDbType();

    // Ensure the conversations/messages schema exists before opening connections
    await ensureConversationSchemaReady();

    if (dbType === 'sqlite') {
      const filename = process.env.SQLITE_DB_FILE || join(process.cwd(), 'data', 'tokens.db');
      const dir = dirname(filename);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      sqliteDb = new BunDatabase(filename);
      sqliteDb.exec('PRAGMA journal_mode = WAL');
      sqliteDb.exec('PRAGMA foreign_keys = ON');
      return;
    }

    const connectionString =
      process.env.DATABASE_URL ||
      process.env.PG_CONNECTION_STRING ||
      'postgres://localhost:5432/postgres';

    pgPool = new Pool({ connectionString });
  })();

  return initPromise;
}

function mapRowToMessage(row: any): Message {
  return {
    id: row.id,
    conversationId: row.conversation_id,
    role: row.role,
    content: row.content,
    createdAt: new Date(row.created_at),
  };
}

export async function getMessagesByConversationId(conversationId: string): Promise<Message[]> {
  await ensureInitialized();

  if (dbType === 'sqlite' && sqliteDb) {
    const rows = sqliteDb
      .prepare(`SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC`)
      .all(conversationId) as any[];
    return rows.map(mapRowToMessage);
  }

  if (pgPool) {
    const result = await pgPool.query(
      `SELECT * FROM messages WHERE conversation_id = $1 ORDER BY created_at ASC`,
      [conversationId],
    );
    return result.rows.map(mapRowToMessage);
  }

  throw new Error('Database not initialized');
}

export async function addMessage(message: Message): Promise<Message> {
  await ensureInitialized();
  const createdAt = message.createdAt ?? new Date();

  if (dbType === 'sqlite' && sqliteDb) {
    const insert = sqliteDb.prepare(`
      INSERT INTO messages (id, conversation_id, role, content, created_at)
      VALUES (?, ?, ?, ?, ?)
    `);
    insert.run(
      message.id,
      message.conversationId,
      message.role,
      message.content,
      toSQLiteTimestamp(createdAt),
    );

    const row = sqliteDb
      .prepare(`SELECT * FROM messages WHERE id = ?`)
      .get(message.id) as any;
    return mapRowToMessage(row);
  }

  if (pgPool) {
    const result = await pgPool.query(
      `
      INSERT INTO messages (id, conversation_id, role, content, created_at)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `,
      [message.id, message.conversationId, message.role, message.content, createdAt],
    );
    return mapRowToMessage(result.rows[0]);
  }

  throw new Error('Database not initialized');
}
