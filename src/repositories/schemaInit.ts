import { runMigrations } from '../db/migrate';

type InitState = 'pending' | 'done' | 'error';

let schemaInitPromise: Promise<void> | null = null;
let schemaInitState: InitState = 'pending';

/**
 * Ensure the lightweight conversations/messages schema exists before repositories run queries.
 * This leverages the centralized `runMigrations` helper which is idempotent and safe to call multiple times.
 */
export async function ensureConversationSchemaReady(): Promise<void> {
  if (schemaInitState === 'done') return;

  if (!schemaInitPromise) {
    schemaInitPromise = runMigrations()
      .then(() => {
        schemaInitState = 'done';
      })
      .catch((err) => {
        schemaInitState = 'error';
        schemaInitPromise = null;
        throw err;
      });
  }

  return schemaInitPromise;
}
