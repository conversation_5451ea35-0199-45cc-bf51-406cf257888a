// ai-news-fetcher.js
// Fetches AI-related news from a list of RSS/Atom feeds and returns structured article data.
// Usage (programmatic):
//   import { fetchAiNews, getDefaultAINewsFeedsFromEnv } from './ai-news-fetcher.js'
//   const feeds = getDefaultAINewsFeedsFromEnv();
//   const articles = await fetchAiNews({ feedUrls: feeds, maxPerFeed: 20 });
// Usage (CLI):
//   bun run src/connectors/ai-news-fetcher.js "https://example.com/rss,https://another.com/feed"

import Parser from 'rss-parser';
import fs from 'node:fs';
import path from 'node:path';
import crypto from 'node:crypto';

// Utility: basic hash for stable IDs without extra deps
function simpleHash(str) {
  let h = 2166136261; // FNV-1a 32-bit basis
  for (let i = 0; i < str.length; i++) {
    h ^= str.charCodeAt(i);
    h += (h << 1) + (h << 4) + (h << 7) + (h << 8) + (h << 24);
  }
  // Convert to unsigned 32-bit and base36 for compactness
  return (h >>> 0).toString(36);
}

function toArray(input) {
  if (!input) return [];
  if (Array.isArray(input)) return input;
  if (typeof input === 'string') {
    // split by comma or newline
    return input
      .split(/[\n,]/)
      .map(s => s.trim())
      .filter(Boolean);
  }
  return [];
}

export function getDefaultAINewsFeedsFromEnv() {
  const envVal = process.env.AI_NEWS_FEEDS || process.env.AI_TECH_FEEDS || '';
  const feeds = toArray(envVal);
  return feeds;
}

function normalizeContent(item) {
  // Prefer full encoded content if present
  const encoded = item['content:encoded'];
  const content = encoded || item.content || item.summary || item.contentSnippet || '';
  if (typeof content === 'string') return content;
  // rss-parser sometimes returns objects; fallback to JSON string
  try {
    return JSON.stringify(content);
  } catch {
    return String(content);
  }
}

function pickSourceName(feedUrl, feedTitle) {
  if (feedTitle && typeof feedTitle === 'string') return feedTitle.trim();
  try {
    const u = new URL(feedUrl);
    return u.hostname.replace(/^www\./, '');
  } catch {
    return feedUrl;
  }
}

function toISODate(item) {
  const d = item.isoDate || item.pubDate || item.published || item.updated;
  if (!d) return null;
  const dt = new Date(d);
  return isNaN(dt.getTime()) ? null : dt.toISOString();
}

function buildId(fields) {
  const base = `${fields.url || ''}\n${fields.title || ''}\n${fields.publishedAt || ''}`;
  return fields.guid || simpleHash(base);
}

export async function fetchAiNews({
  feedUrls,
  since,
  maxPerFeed = 50,
  timeoutMs = 20000
} = {}) {
  const urls = toArray(feedUrls && feedUrls.length ? feedUrls : getDefaultAINewsFeedsFromEnv());
  if (urls.length === 0) {
    throw new Error('No feed URLs provided. Set AI_NEWS_FEEDS env or pass { feedUrls: [...] }');
  }

  const sinceDate = since ? new Date(since) : null;
  const parser = new Parser({
    headers: { 'User-Agent': 'dante-gpt-ai-news-fetcher/1.0 (+https://github.com)' },
    timeout: timeoutMs
  });

  const all = [];
  for (const feedUrl of urls) {
    try {
      const feed = await parser.parseURL(feedUrl);
      const source = pickSourceName(feedUrl, feed?.title);
      const items = Array.isArray(feed?.items) ? feed.items : [];
      let count = 0;

      for (const item of items) {
        if (maxPerFeed && count >= maxPerFeed) break;

        const url = item.link || item.guid || null;
        const publishedAt = toISODate(item);

        if (sinceDate && publishedAt) {
          const pt = new Date(publishedAt);
          if (!isNaN(pt.getTime()) && pt < sinceDate) {
            continue; // skip older than since
          }
        }

        const rawContent = normalizeContent(item);
        const article = {
          source,
          feedUrl,
          title: item.title?.trim() || null,
          url,
          rawContent,
          publishedAt,
          guid: item.guid || null
        };
        article.id = buildId(article);

        all.push(article);
        count++;
      }
    } catch (err) {
      // Capture feed-level errors but continue other feeds
      all.push({
        source: pickSourceName(feedUrl),
        feedUrl,
        error: true,
        errorMessage: err?.message || String(err)
      });
    }
  }

  // Deduplicate by URL or GUID, preferring first occurrence
  const seen = new Set();
  const deduped = [];
  for (const a of all) {
    if (a.error) continue;
    const key = (a.url || a.guid || a.id);
    if (key && !seen.has(key)) {
      seen.add(key);
      deduped.push(a);
    }
  }

  return deduped;
}

// CLI for quick manual testing
if (import.meta.main) {
  const argFeeds = toArray(process.argv[2] || '');
  fetchAiNews({ feedUrls: argFeeds.length ? argFeeds : undefined, maxPerFeed: Number(process.env.MAX_PER_FEED) || 10 })
    .then(res => {
      // Print compact JSON
      console.log(JSON.stringify(res, null, 2));
    })
    .catch(err => {
      console.error('ai-news-fetcher error:', err?.message || err);
      process.exit(1);
    });
}
