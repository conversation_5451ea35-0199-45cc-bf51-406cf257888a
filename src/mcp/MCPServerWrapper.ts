import { MCPServer } from './MCPServerManager';
import { normalizePythonJsonSchema, coerceArgsToSchemaTypes } from './typeConverters';

/**
 * Wrapper for MCP servers that fixes Python-style type names in tool schemas
 * This ensures compatibility with Vercel AI SDK requirements
 */
export class MCPServerWrapper implements MCPServer {
  private server: MCPServer;
  private serverId: string;
  private toolSchemasByName: Map<string, any> = new Map();

  constructor(server: MCPServer, serverId: string) {
    this.server = server;
    this.serverId = serverId;
  }

  // Pass through properties from the wrapped server
  get cacheToolsList(): boolean {
    return (this.server as any).cacheToolsList || false;
  }

  get name(): string {
    return (this.server as any).name || `wrapped-${this.serverId}`;
  }

  /**
   * Fix Python type names to JSON Schema type names
   */
  private fixSchemaTypes(schema: any): any {
    return normalizePythonJsonSchema(schema);
  }

  /**
   * Connect to the MCP server
   */
  async connect(): Promise<void> {
    return this.server.connect();
  }

  /**
   * Close the connection
   */
  async close(): Promise<void> {
    return this.server.close();
  }

  /**
   * List available tools with fixed schemas
   */
  async listTools(): Promise<any[]> {
    const tools = await this.server.listTools();
    
    // Fix schemas for each tool
    const fixed = tools.map(tool => {
      if (tool && typeof tool === 'object' && tool.inputSchema) {
        const norm = this.fixSchemaTypes(tool.inputSchema);
        // Cache normalized schemas by name for argument coercion in callTool
        if (tool.name && typeof tool.name === 'string') {
          try { this.toolSchemasByName.set(tool.name, norm); } catch {}
        }
        return { ...tool, inputSchema: norm };
      }
      return tool;
    });
    return fixed;
  }

  /**
   * Call a tool
   */
  async callTool(name: string, args: any): Promise<any> {
    let prepared = args;
    try {
      // Ensure we have the latest schemas cached
      if (!this.toolSchemasByName.has(name)) {
        try { await this.listTools(); } catch {}
      }
      const schema = this.toolSchemasByName.get(name);
      if (schema && schema.type === 'object' && schema.properties) {
        prepared = coerceArgsToSchemaTypes(args || {}, schema);
      }
      // Special-case: tolerate tab_id numeric vs string
      if (name === 'browser_switch_tab' && prepared && prepared.tab_id != null) {
        prepared.tab_id = String(prepared.tab_id);
      }
    } catch {}
    return this.server.callTool(name, prepared);
  }

  /**
   * Get server metadata (if available)
   */
  get metadata(): any {
    return (this.server as any).metadata || {};
  }

  /**
   * Get server ID
   */
  get id(): string {
    return this.serverId;
  }

  /**
   * Invalidate tools cache
   */
  async invalidateToolsCache(): Promise<void> {
    if (typeof (this.server as any).invalidateToolsCache === 'function') {
      return (this.server as any).invalidateToolsCache();
    }
  }
}

/**
 * Wrap an MCP server to fix schema issues
 */
export function wrapMCPServer(server: MCPServer, serverId: string): MCPServer {
  // Only wrap if it's a browser-use server (known to have Python type names)
  if (serverId === 'browser-use') {
    return new MCPServerWrapper(server, serverId);
  }
  return server;
}
