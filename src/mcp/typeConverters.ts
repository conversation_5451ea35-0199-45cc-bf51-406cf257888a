/**
 * Python ↔ JSON Schema ↔ TypeScript converters and runtime coercion helpers.
 *
 * Goals:
 * - Normalize Python-style type names in JSON Schemas (e.g., 'str' → 'string').
 * - Generate lightweight TypeScript type definitions from JSON Schema.
 * - Coerce runtime arguments to match a (fixed) JSON Schema before tool calls.
 */

type Json = any;

const PY_TO_JSON_TYPE: Record<string, string> = {
  str: 'string',
  int: 'integer',
  float: 'number',
  bool: 'boolean',
  dict: 'object',
  list: 'array',
  tuple: 'array',
  none: 'null',
  None: 'null'
};

export function normalizePythonJsonSchema<T = any>(schema: T): T {
  if (!schema || typeof schema !== 'object') return schema;
  const fixed = JSON.parse(JSON.stringify(schema));

  const fix = (node: Json) => {
    if (!node || typeof node !== 'object') return;

    if (typeof node.type === 'string' && PY_TO_JSON_TYPE[node.type]) {
      node.type = PY_TO_JSON_TYPE[node.type];
    }

    // Handle array of types e.g., ['str', 'null']
    if (Array.isArray(node.type)) {
      node.type = node.type.map((t: string) => PY_TO_JSON_TYPE[t] || t);
    }

    if (node.properties && typeof node.properties === 'object') {
      for (const k of Object.keys(node.properties)) fix(node.properties[k]);
    }

    if (node.items) fix(node.items);
    if (node.additionalProperties) fix(node.additionalProperties);

    // Recurse common combinators
    ['anyOf', 'allOf', 'oneOf'].forEach(k => {
      if (Array.isArray(node[k])) node[k].forEach((sub: Json) => fix(sub));
    });
  };

  fix(fixed);
  return fixed;
}

function tsForSimpleType(t: string): string {
  switch (t) {
    case 'string': return 'string';
    case 'integer': return 'number';
    case 'number': return 'number';
    case 'boolean': return 'boolean';
    case 'null': return 'null';
    case 'object': return 'Record<string, unknown>';
    case 'array': return 'unknown[]';
    default: return 'unknown';
  }
}

function jsonSchemaNodeToTs(node: Json): string {
  if (!node || typeof node !== 'object') return 'unknown';

  // Enum literal union
  if (Array.isArray(node.enum) && node.enum.length > 0) {
    return node.enum.map((v: any) => (typeof v === 'string' ? `'${v.replace(/'/g, "\\'")}'` : String(v))).join(' | ');
  }

  // Unions
  if (Array.isArray(node.anyOf)) return node.anyOf.map(jsonSchemaNodeToTs).join(' | ');
  if (Array.isArray(node.oneOf)) return node.oneOf.map(jsonSchemaNodeToTs).join(' | ');

  // Type can be string or array
  if (Array.isArray(node.type)) return node.type.map(tsForSimpleType).join(' | ');

  switch (node.type) {
    case 'object': {
      const props = node.properties || {};
      const req: string[] = Array.isArray(node.required) ? node.required : [];
      const parts = Object.keys(props).map((k) => {
        const opt = req.includes(k) ? '' : '?';
        return `${k}${opt}: ${jsonSchemaNodeToTs(props[k])}`;
      });
      const addl = node.additionalProperties;
      if (addl) {
        const valTs = addl === true ? 'unknown' : jsonSchemaNodeToTs(addl);
        parts.push(`[key: string]: ${valTs}`);
      }
      return `{ ${parts.join('; ')} }`;
    }
    case 'array': {
      const itemTs = node.items ? jsonSchemaNodeToTs(node.items) : 'unknown';
      return `${itemTs}[]`;
    }
    case 'string':
    case 'integer':
    case 'number':
    case 'boolean':
    case 'null':
      return tsForSimpleType(node.type);
    default:
      return 'unknown';
  }
}

export function jsonSchemaToTypeScript(schema: Json, name = 'Schema'): string {
  const fixed = normalizePythonJsonSchema(schema);
  const ts = jsonSchemaNodeToTs(fixed);
  return `type ${name} = ${ts};`;
}

// Runtime coercion: attempt to coerce args to match the (fixed) schema
export function coerceArgsToSchemaTypes(args: Record<string, any>, schema: Json): Record<string, any> {
  if (!schema || typeof schema !== 'object' || !schema.properties) return args;
  const out: Record<string, any> = { ...args };
  const props = schema.properties as Record<string, any>;
  for (const key of Object.keys(props)) {
    const expected = props[key];
    const expType = Array.isArray(expected.type) ? expected.type[0] : expected.type;
    const val = out[key];
    if (val == null) continue;

    switch (expType) {
      case 'string':
        out[key] = typeof val === 'string' ? val : String(val);
        break;
      case 'integer':
        if (typeof val === 'number') out[key] = Math.trunc(val);
        else if (typeof val === 'string' && /^-?\d+$/.test(val)) out[key] = parseInt(val, 10);
        break;
      case 'number':
        if (typeof val === 'number') out[key] = val;
        else if (typeof val === 'string' && /^-?\d+(\.\d+)?$/.test(val)) out[key] = parseFloat(val);
        break;
      case 'boolean':
        if (typeof val === 'boolean') out[key] = val;
        else if (typeof val === 'string') out[key] = ['true', '1', 'yes', 'on'].includes(val.toLowerCase());
        else if (typeof val === 'number') out[key] = val !== 0;
        break;
      case 'array':
        if (!Array.isArray(val)) out[key] = [val];
        break;
      case 'object':
        if (typeof val === 'string') {
          try { out[key] = JSON.parse(val); } catch {}
        }
        break;
      default:
        break;
    }
  }
  return out;
}

