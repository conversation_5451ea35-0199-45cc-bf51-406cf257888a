import { MCPServerConfig } from './MCPServerManager';
import { MCPToolFactoryOptions } from './MCPToolFactory';

/**
 * Utility functions for MCP integration
 */

/**
 * Validate MCP server configuration
 */
export function validateMCPServerConfig(config: MCPServerConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Basic validation
  if (!config.id || typeof config.id !== 'string') {
    errors.push('Server ID is required and must be a string');
  }

  if (!config.name || typeof config.name !== 'string') {
    errors.push('Server name is required and must be a string');
  }

  if (!['stdio', 'streamable_http', 'hosted'].includes(config.type)) {
    errors.push('Server type must be one of: stdio, streamable_http, hosted');
  }

  if (typeof config.enabled !== 'boolean') {
    errors.push('Server enabled flag must be a boolean');
  }

  if (typeof config.priority !== 'number' || config.priority < 0) {
    errors.push('Server priority must be a non-negative number');
  }

  if (!Array.isArray(config.tags)) {
    errors.push('Server tags must be an array');
  }

  // Type-specific validation
  switch (config.type) {
    case 'stdio':
      if (!config.config.fullCommand || typeof config.config.fullCommand !== 'string') {
        errors.push('fullCommand is required for stdio servers');
      }
      break;

    case 'streamable_http':
      if (!config.config.url || typeof config.config.url !== 'string') {
        errors.push('url is required for streamable HTTP servers');
      }

      // Validate URL format
      if (config.config.url) {
        try {
          new URL(config.config.url);
        } catch {
          errors.push('url must be a valid URL for streamable HTTP servers');
        }
      } else {
        errors.push('url is required for streamable HTTP servers');
      }
      break;

    case 'hosted':
      // Add hosted-specific validation when supported
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Create a filesystem MCP server configuration
 */
export function createFilesystemMCPConfig(
  id: string,
  name: string,
  rootPath: string,
  options: {
    priority?: number;
    enabled?: boolean;
    tags?: string[];
    allowWrite?: boolean;
  } = {}
): MCPServerConfig {
  const {
    priority = 100,
    enabled = true,
    tags = ['filesystem', 'files'],
    allowWrite = false
  } = options;

  // Only quote the path if it contains spaces or special characters
  const needsQuoting = /[\s'"\\$`!*?[\]{}();|&<>]/.test(rootPath);
  const quotedPath = needsQuoting ? `"${rootPath.replace(/"/g, '\\"')}"` : rootPath;

  // Use the official MCP filesystem server
  // The allowWrite parameter is kept for future compatibility but currently ignored
  const command = `npx @modelcontextprotocol/server-filesystem ${quotedPath}`;

  return {
    id,
    name,
    type: 'stdio',
    config: {
      fullCommand: command,
      cacheToolsList: true,
      connectionTimeout: 10000 // 10 seconds for filesystem operations
    },
    enabled,
    priority,
    tags
  };
}

/**
 * Create a Git MCP server configuration
 */
export function createGitMCPConfig(
  id: string,
  name: string,
  repoPath: string,
  options: {
    priority?: number;
    enabled?: boolean;
    tags?: string[];
    remote?: string;
  } = {}
): MCPServerConfig {
  const {
    priority = 90,
    enabled = true,
    tags = ['git', 'version-control'],
    remote
  } = options;

  // Only quote the path if it contains spaces or special characters
  const needsQuoting = /[\s'"\\$`!*?[\]{}();|&<>]/.test(repoPath);
  const quotedPath = needsQuoting ? `"${repoPath.replace(/"/g, '\\"')}"` : repoPath;
  // Use the available git MCP server
  let command = `npx @cyanheads/git-mcp-server ${quotedPath}`;
  if (remote) {
    const needsRemoteQuoting = /[\s'"\\$`!*?[\]{}();|&<>]/.test(remote);
    const quotedRemote = needsRemoteQuoting ? `"${remote.replace(/"/g, '\\"')}"` : remote;
    command += ` --remote ${quotedRemote}`;
  }

  return {
    id,
    name,
    type: 'stdio',
    config: {
      fullCommand: command,
      cacheToolsList: true,
      connectionTimeout: 15000 // 15 seconds for git operations
    },
    enabled,
    priority,
    tags
  };
}

/**
 * Create a database MCP server configuration
 */
export function createDatabaseMCPConfig(
  id: string,
  name: string,
  connectionString: string,
  serverUrl: string,
  options: {
    priority?: number;
    enabled?: boolean;
    tags?: string[];
    authProvider?: any;
  } = {}
): MCPServerConfig {
  const {
    priority = 80,
    enabled = true,
    tags = ['database', 'sql'],
    authProvider
  } = options;

  return {
    id,
    name,
    type: 'streamable_http',
    config: {
      url: serverUrl,
      authProvider,
      cacheToolsList: true
    },
    enabled,
    priority,
    tags
  };
}

/**
 * Create a web search MCP server configuration
 */
export function createWebSearchMCPConfig(
  id: string,
  name: string,
  serverUrl: string,
  options: {
    priority?: number;
    enabled?: boolean;
    tags?: string[];
    apiKey?: string;
  } = {}
): MCPServerConfig {
  const {
    priority = 70,
    enabled = true,
    tags = ['web', 'search'],
    apiKey
  } = options;

  const authProvider = apiKey ? {
    type: 'bearer',
    token: apiKey
  } : undefined;

  return {
    id,
    name,
    type: 'streamable_http',
    config: {
      url: serverUrl,
      authProvider,
      cacheToolsList: true
    },
    enabled,
    priority,
    tags
  };
}

/**
 * Sanitize tool input parameters
 */
export function sanitizeToolInput(input: any): any {
  if (input === null || input === undefined) {
    return input;
  }

  if (typeof input === 'string') {
    // Basic string sanitization
    return input.trim().slice(0, 10000); // Limit length
  }

  if (typeof input === 'number') {
    // Ensure number is finite
    return Number.isFinite(input) ? input : 0;
  }

  if (typeof input === 'boolean') {
    return input;
  }

  if (Array.isArray(input)) {
    // Limit array size and sanitize elements
    return input.slice(0, 100).map(sanitizeToolInput);
  }

  if (typeof input === 'object') {
    const sanitized: any = {};
    let keyCount = 0;

    for (const [key, value] of Object.entries(input)) {
      if (keyCount >= 50) break; // Limit object size

      const sanitizedKey = String(key).slice(0, 100);
      sanitized[sanitizedKey] = sanitizeToolInput(value);
      keyCount++;
    }

    return sanitized;
  }

  return String(input).slice(0, 1000);
}

/**
 * Smart JSON truncation that preserves structure and essential data
 */
function smartTruncateJSON(output: any, maxLength: number): string {
  // For search results, prioritize preserving structure
  if (output.results && Array.isArray(output.results)) {
    // Start with essential fields and reduce results as needed
    let resultsToInclude = Math.min(20, output.results.length); // Start with more results

    while (resultsToInclude > 0) {
      const essential = {
        success: output.success,
        matches: output.matches,
        truncated: output.results.length > resultsToInclude,
        results: output.results.slice(0, resultsToInclude)
      };

      const jsonString = JSON.stringify(essential, null, 2);

      if (jsonString.length <= maxLength) {
        return jsonString;
      }

      // Reduce by half each time
      resultsToInclude = Math.floor(resultsToInclude / 2);
    }

    // Minimal fallback - just preserve the counts
    return JSON.stringify({
      success: output.success,
      matches: output.matches,
      truncated: true,
      results: []
    }, null, 2);
  }

  // For directory listings and other arrays
  if (output.files && Array.isArray(output.files)) {
    let filesToInclude = Math.min(50, output.files.length);

    while (filesToInclude > 0) {
      const essential = {
        success: output.success,
        totalFiles: output.files.length,
        files: output.files.slice(0, filesToInclude),
        truncated: output.files.length > filesToInclude
      };

      const jsonString = JSON.stringify(essential, null, 2);

      if (jsonString.length <= maxLength) {
        return jsonString;
      }

      filesToInclude = Math.floor(filesToInclude / 2);
    }
  }

  // For other objects, preserve key fields
  if (typeof output === 'object' && output !== null) {
    const essential: any = {};
    const priorityFields = ['success', 'error', 'matches', 'message', 'status', 'data'];

    for (const field of priorityFields) {
      if (field in output) {
        essential[field] = output[field];
      }
    }

    const jsonString = JSON.stringify(essential, null, 2);
    return jsonString; // Return valid JSON without malformed concatenation
  }

  // Fallback: just preserve success/error status
  const minimal = {
    success: output.success ?? false,
    error: output.error ?? 'Output truncated due to size',
    originalSize: JSON.stringify(output).length
  };

  return JSON.stringify(minimal, null, 2);
}

/**
 * Sanitize tool output with intelligent token limiting based on tool type and model
 */
export function sanitizeToolOutput(output: any, model?: string): string {
  // Dynamic limits based on model context window
  let MAX_LENGTH = 8000; // Default reasonable limit

  // Adjust limits based on model capabilities
  if (model) {
    if (model.startsWith('gemini-')) {
      // Gemini models with 1M context can handle much larger outputs
      MAX_LENGTH = 50000;
    } else if (model.includes('long-context') || model.startsWith('gpt-4.1')) {
      // Long context models (200K+)
      MAX_LENGTH = 20000;
    } else if (model.startsWith('gpt-5') || model.startsWith('gpt-4')) {
      // Standard GPT models
      MAX_LENGTH = 10000;
    }
  }

  // Further adjust based on output type
  if (typeof output === 'object' && output !== null) {
    // Search results need higher limits to preserve matches
    if (output.matches !== undefined || output.results !== undefined) {
      MAX_LENGTH = Math.min(MAX_LENGTH * 1.5, 75000); // Allow more for search
    }
    // Directory listings can be very large
    else if (output.files !== undefined) {
      MAX_LENGTH = Math.min(MAX_LENGTH * 1.2, 60000);
    }
    // Error responses should be preserved fully
    else if (output.success === false || output.error !== undefined) {
      MAX_LENGTH = Math.min(MAX_LENGTH, 5000); // Preserve error messages
    }
  }

  if (typeof output === 'string') {
    if (output.length > MAX_LENGTH) {
      const truncated = output.slice(0, MAX_LENGTH);
      return truncated + '\n\n... [Output truncated - exceeded token limit. Original length: ' + output.length + ' chars]';
    }
    return output;
  }

  if (output === null || output === undefined) {
    return '';
  }

  try {
    const jsonString = JSON.stringify(output, null, 2);
    if (jsonString.length > MAX_LENGTH) {
      // Smart truncation that preserves JSON structure
      return smartTruncateJSON(output, MAX_LENGTH);
    }
    return jsonString;
  } catch {
    const stringOutput = String(output);
    if (stringOutput.length > MAX_LENGTH) {
      const truncated = stringOutput.slice(0, MAX_LENGTH);
      return truncated + '\n\n... [String output truncated - exceeded token limit. Original length: ' + stringOutput.length + ' chars]';
    }
    return stringOutput;
  }
}

/**
 * Check if a tool call is potentially dangerous
 */
export function isToolCallSafe(toolName: string, args: any): { safe: boolean; reason?: string } {
  // Common dangerous patterns
  const dangerousPatterns = [
    /rm\s+-rf/i,
    /del\s+\/[sq]/i,
    /format\s+c:/i,
    /sudo/i,
    /passwd/i,
    /shutdown/i,
    /reboot/i,
    /kill\s+-9/i
  ];

  // Check tool name
  const dangerousTools = [
    'system_shutdown',
    'format_disk',
    'delete_all',
    'sudo_command',
    'root_access'
  ];

  if (dangerousTools.includes(toolName.toLowerCase())) {
    return { safe: false, reason: 'Tool ' + toolName + ' is potentially dangerous' };
  }

  // Check arguments for dangerous patterns
  const argsStr = JSON.stringify(args).toLowerCase();
  for (const pattern of dangerousPatterns) {
    if (pattern.test(argsStr)) {
      return { safe: false, reason: 'Arguments contain potentially dangerous pattern: ' + pattern.toString() };
    }
  }

  return { safe: true };
}

/**
 * Create default MCP tool factory options
 */
export function createDefaultMCPToolFactoryOptions(): MCPToolFactoryOptions {
  return {
    enableCaching: true,
    cacheExpiry: 5 * 60 * 1000, // 5 minutes
    maxCacheSize: 1000,
    toolFilters: {
      // Block potentially dangerous and high-token tools by default
      blockedTools: [
        'system_shutdown',
        'format_disk',
        'delete_all',
        'sudo_command',
        'root_access',
        'directory_tree', // Block directory_tree as it produces massive outputs
        'list_files_recursive' // Block recursive file listing
      ]
    }
  };
}

/**
 * Get MCP server recommendations based on task type
 */
export function getMCPServerRecommendations(taskType: string): {
  recommended: string[];
  optional: string[];
  description: string;
} {
  const recommendations: { [key: string]: { recommended: string[]; optional: string[]; description: string } } = {
    'file-operations': {
      recommended: ['filesystem'],
      optional: ['git'],
      description: 'File system operations require filesystem MCP server. Git server can provide version control capabilities.'
    },
    'code-analysis': {
      recommended: ['filesystem', 'git'],
      optional: ['database'],
      description: 'Code analysis benefits from file system access and git history. Database can provide dependency information.'
    },
    'web-research': {
      recommended: ['web-search'],
      optional: ['database'],
      description: 'Web research requires web search capabilities. Database can cache and organize findings.'
    },
    'data-analysis': {
      recommended: ['database', 'filesystem'],
      optional: ['web-search'],
      description: 'Data analysis needs database access and file operations. Web search can gather additional context.'
    },
    'development': {
      recommended: ['filesystem', 'git'],
      optional: ['web-search', 'database'],
      description: 'Development tasks need file system and version control. Web search and database can provide additional resources.'
    }
  };

  return recommendations[taskType] || {
    recommended: ['filesystem'],
    optional: ['git', 'web-search'],
    description: 'General tasks typically benefit from file system access with optional git and web search capabilities.'
  };
}

/**
 * Measure MCP server performance
 */
export async function measureMCPServerPerformance(serverId: string, sampleSize: number = 5): Promise<{
  averageResponseTime: number;
  successRate: number;
  errors: string[];
}> {
  const results: { success: boolean; responseTime: number; error?: string }[] = [];

  // This would need to be implemented with actual server testing
  // For now, return a placeholder implementation
  return {
    averageResponseTime: 0,
    successRate: 100,
    errors: []
  };
}

/**
 * Generate MCP integration report
 */
export function generateMCPIntegrationReport(
  serverStatuses: any[],
  toolMetadata: any[],
  performanceData: any[]
): string {
  const totalServers = serverStatuses.length;
  const connectedServers = serverStatuses.filter(s => s.status === 'connected').length;
  const totalTools = toolMetadata.length;

  const categoryMap = toolMetadata.reduce((acc: any, t) => {
    const category = t.tags[0] || 'uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  const serverDetails = serverStatuses.map(s => '- ' + s.name + ' (' + s.id + '): ' + s.status).join('\n');
  const toolCategories = Object.entries(categoryMap).map(([category, count]) => '- ' + category + ': ' + count + ' tools').join('\n');

  const report = [
    '# MCP Integration Report',
    '',
    '## Server Status',
    '- Total Servers: ' + totalServers,
    '- Connected: ' + connectedServers,
    '- Connection Rate: ' + ((connectedServers / totalServers) * 100).toFixed(1) + '%',
    '',
    '## Tool Availability',
    '- Total Tools: ' + totalTools,
    '- Unique Tool Names: ' + new Set(toolMetadata.map(t => t.toolName)).size,
    '',
    '## Server Details',
    serverDetails,
    '',
    '## Tool Categories',
    toolCategories,
    '',
    'Generated at: ' + new Date().toISOString()
  ].join('\n');

  return report.trim();
}
