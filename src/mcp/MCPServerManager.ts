import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import { Client as MCPClient } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

export interface MCPServer {
  connect(): Promise<void>;
  close(): Promise<void>;
  listTools(): Promise<any[]>;
  callTool(name: string, args: any): Promise<any>;
}

export class MCPServerStdio implements MCPServer {
  private client: MCPClient | null = null;
  private transport: StdioClientTransport | null = null;
  private config: any;

  constructor(config: any) {
    this.config = config;
    console.log('MCPServerStdio initialized with config:', config.name);
  }
  
  async connect(): Promise<void> {
    try {
      // Parse the full command to get command and args
      const parts = this.config.fullCommand.split(' ');
      const command = parts[0];
      const args = parts.slice(1);

      // Create transport with server parameters
      this.transport = new StdioClientTransport({
        command,
        args,
        env: { ...process.env, ...(this.config.env || {}) }
      });

      this.client = new MCPClient({
        name: this.config.name,
        version: '1.0.0'
      }, {
        capabilities: {
          tools: {}
        }
      });

      // Connect to the server
      await this.client.connect(this.transport);
      console.log(`✅ Connected to MCP stdio server: ${this.config.name}`);
    } catch (error) {
      console.error(`❌ Failed to connect to MCP stdio server ${this.config.name}:`, error);
      await this.close();
      throw error;
    }
  }
  
  async close(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.client = null;
      }

      if (this.transport) {
        await this.transport.close();
        this.transport = null;
      }
    } catch (error) {
      console.warn(`⚠️ Error closing MCP stdio server ${this.config.name}:`, error);
    }
  }
  
  async listTools(): Promise<any[]> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await this.client.listTools();
      return response.tools || [];
    } catch (error) {
      console.error(`❌ Failed to list tools from ${this.config.name}:`, error);
      throw error;
    }
  }
  
  async callTool(name: string, args: any): Promise<any> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await this.client.callTool({
        name,
        arguments: args || {}
      });
      return response;
    } catch (error) {
      console.error(`❌ Failed to call tool ${name} on ${this.config.name}:`, error);
      throw error;
    }
  }
}

export class MCPServerStreamableHttp implements MCPServer {
  private client: MCPClient | null = null;
  private transport: SSEClientTransport | null = null;
  private config: any;

  constructor(config: any) {
    this.config = config;
    console.log('MCPServerStreamableHttp initialized with config:', config.name);
  }
  
  async connect(): Promise<void> {
    try {
      // Create SSE transport for streamable HTTP
      this.transport = new SSEClientTransport(
        new URL(this.config.url)
      );

      this.client = new MCPClient({
        name: this.config.name,
        version: '1.0.0'
      }, {
        capabilities: {
          tools: {}
        }
      });

      // Connect to the server
      await this.client.connect(this.transport);
      console.log(`✅ Connected to MCP HTTP server: ${this.config.name}`);
    } catch (error) {
      console.error(`❌ Failed to connect to MCP HTTP server ${this.config.name}:`, error);
      await this.close();
      throw error;
    }
  }
  
  async close(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.client = null;
      }

      if (this.transport) {
        await this.transport.close();
        this.transport = null;
      }
    } catch (error) {
      console.warn(`⚠️ Error closing MCP HTTP server ${this.config.name}:`, error);
    }
  }
  
  async listTools(): Promise<any[]> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await this.client.listTools();
      return response.tools || [];
    } catch (error) {
      console.error(`❌ Failed to list tools from ${this.config.name}:`, error);
      throw error;
    }
  }
  
  async callTool(name: string, args: any): Promise<any> {
    if (!this.client) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await this.client.callTool({
        name,
        arguments: args || {}
      });
      return response;
    } catch (error) {
      console.error(`❌ Failed to call tool ${name} on ${this.config.name}:`, error);
      throw error;
    }
  }
}

export interface MCPServerConfig {
  id: string;
  name: string;
  type: 'stdio' | 'streamable_http' | 'hosted';
  config: {
    // For stdio servers
    fullCommand?: string;
    env?: Record<string, string>; // Environment variables for stdio servers
    // For streamable HTTP servers
    url?: string;
    authProvider?: any;
    requestInit?: RequestInit;
    // Common options
    cacheToolsList?: boolean;
    toolFilter?: any;
    connectionTimeout?: number;
    reconnectionOptions?: {
      maxRetries?: number;
      retryDelay?: number;
    };
  };
  enabled: boolean;
  priority: number; // Higher priority servers are preferred
  tags: string[]; // For categorization and filtering
}

export interface MCPServerStatus {
  id: string;
  name: string;
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: Date;
  lastError?: string;
  toolCount?: number;
  responseTime?: number;
}

export class MCPServerManager extends EventEmitter {
  private servers: Map<string, { config: MCPServerConfig; instance?: MCPServer; status: MCPServerStatus }> = new Map();
  private reconnectTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    super();
  }

  /**
   * Register a new MCP server configuration
   */
  registerServer(config: MCPServerConfig): void {
    if (this.servers.has(config.id)) {
      console.warn(`MCP server with id "${config.id}" already registered, skipping`);
      return;
    }

    const status: MCPServerStatus = {
      id: config.id,
      name: config.name,
      status: 'disconnected'
    };

    this.servers.set(config.id, { config, status });
    this.emit('serverRegistered', config);
  }

  /**
   * Unregister an MCP server
   */
  async unregisterServer(serverId: string): Promise<void> {
    const serverData = this.servers.get(serverId);
    if (!serverData) {
      throw new Error(`MCP server with id "${serverId}" not found`);
    }

    // Disconnect if connected
    if (serverData.instance && serverData.status.status === 'connected') {
      await this.disconnectServer(serverId);
    }

    // Clear reconnect timer
    const timer = this.reconnectTimers.get(serverId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(serverId);
    }

    this.servers.delete(serverId);
    this.emit('serverUnregistered', serverId);
  }

  /**
   * Connect to an MCP server
   */
  async connectServer(serverId: string): Promise<MCPServer> {
    const serverData = this.servers.get(serverId);
    if (!serverData) {
      throw new Error(`MCP server with id "${serverId}" not found`);
    }

    if (!serverData.config.enabled) {
      throw new Error(`MCP server "${serverId}" is disabled`);
    }

    // Return existing connection if already connected
    if (serverData.instance && serverData.status.status === 'connected') {
      return serverData.instance;
    }

    serverData.status.status = 'connecting';
    this.emit('serverStatusChanged', serverData.status);

    try {
      const startTime = Date.now();
      let instance = await this.createServerInstance(serverData.config);

      // Wrap the server to fix schema issues (e.g., Python type names)
      // Only wraps where needed (e.g., browser-use)
      const { wrapMCPServer } = await import('./MCPServerWrapper');
      instance = wrapMCPServer(instance, serverId);
      
      // Add connection timeout (30 seconds default)
      const connectionTimeout = serverData.config.config.connectionTimeout || 30000;
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Connection timeout after ${connectionTimeout}ms`)), connectionTimeout);
      });
      
      await Promise.race([instance.connect(), timeoutPromise]);
      
      const responseTime = Date.now() - startTime;
      
      // Update server data
      serverData.instance = instance;
      serverData.status = {
        ...serverData.status,
        status: 'connected',
        lastConnected: new Date(),
        responseTime,
        lastError: undefined
      };

      // Try to get tool count for monitoring
      try {
        const tools = await instance.listTools();
        serverData.status.toolCount = tools.length;
      } catch (error) {
        // Tool count is optional, don't fail connection for this
        console.warn(`Could not get tool count for server ${serverId}:`, error);
      }

      this.emit('serverConnected', serverData.status);
      this.emit('serverStatusChanged', serverData.status);
      
      return instance;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      serverData.status = {
        ...serverData.status,
        status: 'error',
        lastError: errorMessage
      };

      this.emit('serverConnectionFailed', serverData.status);
      this.emit('serverStatusChanged', serverData.status);

      // Schedule reconnection if enabled
      this.scheduleReconnection(serverId);
      
      throw error;
    }
  }

  /**
   * Disconnect from an MCP server
   */
  async disconnectServer(serverId: string): Promise<void> {
    const serverData = this.servers.get(serverId);
    if (!serverData || !serverData.instance) {
      return;
    }

    try {
      await serverData.instance.close();
    } catch (error) {
      console.warn(`Error closing MCP server ${serverId}:`, error);
    }

    serverData.instance = undefined;
    serverData.status.status = 'disconnected';
    
    // Clear reconnect timer
    const timer = this.reconnectTimers.get(serverId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(serverId);
    }

    this.emit('serverDisconnected', serverData.status);
    this.emit('serverStatusChanged', serverData.status);
  }

  /**
   * Get a connected MCP server instance
   */
  getServer(serverId: string): MCPServer | undefined {
    const serverData = this.servers.get(serverId);
    return serverData?.instance;
  }

  /**
   * Get all connected servers
   */
  getConnectedServers(): Array<{ id: string; config: MCPServerConfig; instance: MCPServer }> {
    const connected: Array<{ id: string; config: MCPServerConfig; instance: MCPServer }> = [];
    
    for (const [id, serverData] of this.servers) {
      if (serverData.instance && serverData.status.status === 'connected') {
        connected.push({
          id,
          config: serverData.config,
          instance: serverData.instance
        });
      }
    }

    // Sort by priority (higher priority first)
    return connected.sort((a, b) => b.config.priority - a.config.priority);
  }

  /**
   * Get servers by tags
   */
  getServersByTags(tags: string[]): Array<{ id: string; config: MCPServerConfig; instance?: MCPServer }> {
    const matching: Array<{ id: string; config: MCPServerConfig; instance?: MCPServer }> = [];
    
    for (const [id, serverData] of this.servers) {
      const hasMatchingTag = tags.some(tag => serverData.config.tags.includes(tag));
      if (hasMatchingTag) {
        matching.push({
          id,
          config: serverData.config,
          instance: serverData.instance
        });
      }
    }

    return matching.sort((a, b) => b.config.priority - a.config.priority);
  }

  /**
   * Get status of all servers
   */
  getAllServerStatuses(): MCPServerStatus[] {
    return Array.from(this.servers.values()).map(serverData => serverData.status);
  }

  /**
   * Get status of a specific server
   */
  getServerStatus(serverId: string): MCPServerStatus | undefined {
    return this.servers.get(serverId)?.status;
  }

  /**
   * Connect to all enabled servers
   */
  async connectAllServers(): Promise<void> {
    const enabledServers = Array.from(this.servers.entries())
      .filter(([_, serverData]) => serverData.config.enabled);

    // Sort by priority (higher priority first)
    enabledServers.sort((a, b) => b[1].config.priority - a[1].config.priority);

    for (const [id, _] of enabledServers) {
      try {
        await this.connectServer(id);
      } catch (error) {
        console.error(`Failed to connect to MCP server ${id}:`, error);
        // Continue with other servers
      }
    }
  }

  /**
   * Disconnect from all servers
   */
  async disconnectAllServers(): Promise<void> {
    const serverIds = Array.from(this.servers.keys());
    
    await Promise.all(
      serverIds.map(id => this.disconnectServer(id).catch(error => 
        console.error(`Failed to disconnect from MCP server ${id}:`, error)
      ))
    );
  }

  /**
   * Enable or disable a server
   */
  setServerEnabled(serverId: string, enabled: boolean): void {
    const serverData = this.servers.get(serverId);
    if (!serverData) {
      throw new Error(`MCP server with id "${serverId}" not found`);
    }

    serverData.config.enabled = enabled;
    
    if (!enabled && serverData.instance) {
      // Disconnect if being disabled
      this.disconnectServer(serverId);
    }

    this.emit('serverConfigChanged', serverData.config);
  }

  /**
   * Health check for all connected servers
   */
  async healthCheck(): Promise<{ [serverId: string]: boolean }> {
    const results: { [serverId: string]: boolean } = {};
    
    for (const [id, serverData] of this.servers) {
      if (serverData.instance && serverData.status.status === 'connected') {
        try {
          // Try to list tools as a health check
          await serverData.instance.listTools();
          results[id] = true;
        } catch (error) {
          results[id] = false;
          console.warn(`Health check failed for MCP server ${id}:`, error);
          
          // Update status
          serverData.status.status = 'error';
          serverData.status.lastError = error instanceof Error ? error.message : 'Health check failed';
          this.emit('serverStatusChanged', serverData.status);
        }
      } else {
        results[id] = false;
      }
    }

    return results;
  }

  /**
   * Create server instance based on configuration
   */
  private async createServerInstance(config: MCPServerConfig): Promise<MCPServer> {
    switch (config.type) {
      case 'stdio':
        if (!config.config.fullCommand) {
          throw new Error('fullCommand is required for stdio MCP servers');
        }
        return new MCPServerStdio({
          name: config.name,
          fullCommand: config.config.fullCommand,
          cacheToolsList: config.config.cacheToolsList,
          toolFilter: config.config.toolFilter
        });

      case 'streamable_http':
        if (!config.config.url) {
          throw new Error('url is required for streamable HTTP MCP servers');
        }
        return new MCPServerStreamableHttp({
          name: config.name,
          url: config.config.url,
          authProvider: config.config.authProvider,
          requestInit: config.config.requestInit,
          cacheToolsList: config.config.cacheToolsList,
          toolFilter: config.config.toolFilter
        });

      default:
        throw new Error(`Unsupported MCP server type: ${config.type}`);
    }
  }

  /**
   * Schedule reconnection for a failed server
   */
  private scheduleReconnection(serverId: string): void {
    const serverData = this.servers.get(serverId);
    if (!serverData || !serverData.config.config.reconnectionOptions) {
      return;
    }

    const { maxRetries = 3, retryDelay = 5000 } = serverData.config.config.reconnectionOptions;
    
    // Clear existing timer
    const existingTimer = this.reconnectTimers.get(serverId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Schedule reconnection
    const timer = setTimeout(async () => {
      try {
        await this.connectServer(serverId);
      } catch (error) {
        console.warn(`Reconnection failed for MCP server ${serverId}:`, error);
      }
    }, retryDelay);

    this.reconnectTimers.set(serverId, timer);
  }

  /**
   * Clean up all resources
   */
  async cleanup(): Promise<void> {
    // Clear all timers
    for (const timer of this.reconnectTimers.values()) {
      clearTimeout(timer);
    }
    this.reconnectTimers.clear();

    // Disconnect all servers
    await this.disconnectAllServers();

    // Remove all listeners
    this.removeAllListeners();
  }
}

// Global singleton instance
export const mcpServerManager = new MCPServerManager();
