import { tool } from 'ai';
import { mcp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MCPServerConfig, MCPServer } from './MCPServerManager';
import { sanitizeToolOutput } from './utils';
import { createValidatingToolWrapper } from '../utils/toolErrorHandler';
import { z } from 'zod';

// Vercel AI SDK tool type
type VercelAITool = ReturnType<typeof tool>;

export interface MCPToolMetadata {
  serverId: string;
  serverName: string;
  toolName: string;
  description: string;
  schema: any;
  tags: string[];
  cached: boolean;
  lastUpdated: Date;
}

export interface MCPToolFactoryOptions {
  enableCaching: boolean;
  cacheExpiry: number; // in milliseconds
  maxCacheSize: number;
  toolFilters: {
    allowedServers?: string[];
    blockedServers?: string[];
    allowedTools?: string[];
    blockedTools?: string[];
    requiredTags?: string[];
  };
}

export class MCPToolFactory {
  private toolCache: Map<string, { tools: VercelAITool[]; metadata: MCPToolMetadata[]; timestamp: number }> = new Map();
  private options: MCPToolFactoryOptions;
  // Cache statistics
  private cacheHits: number = 0;
  private cacheMisses: number = 0;

  constructor(options: Partial<MCPToolFactoryOptions> = {}) {
    this.options = {
      enableCaching: true,
      cacheExpiry: 5 * 60 * 1000, // 5 minutes
      maxCacheSize: 1000,
      toolFilters: {},
      ...options
    };
  }

  /**
   * Get all available tools from connected MCP servers
   */
  async getAllTools(): Promise<{ tools: VercelAITool[]; metadata: MCPToolMetadata[] }> {
    const connectedServers = mcpServerManager.getConnectedServers();
    const allTools: VercelAITool[] = [];
    const allMetadata: MCPToolMetadata[] = [];

    for (const { id, config, instance } of connectedServers) {
      if (!this.isServerAllowed(id)) {
        continue;
      }

      try {
        const { tools, metadata } = await this.getToolsFromServer(id, config, instance);
        allTools.push(...tools);
        allMetadata.push(...metadata);
      } catch (error) {
        console.error(`Failed to get tools from MCP server ${id}:`, error);
        // Continue with other servers
      }
    }

    return { tools: allTools, metadata: allMetadata };
  }

  /**
   * Get tools from a specific MCP server
   */
  async getToolsFromServer(
    serverId: string,
    config: MCPServerConfig,
    instance: MCPServer
  ): Promise<{ tools: VercelAITool[]; metadata: MCPToolMetadata[] }> {
    // Check cache first
    if (this.options.enableCaching) {
      const cached = this.getFromCache(serverId);
      if (cached) {
        return cached;
      }
    }

    try {
      const mcpTools = await instance.listTools();
      const tools: VercelAITool[] = [];
      const metadata: MCPToolMetadata[] = [];

      for (const mcpTool of mcpTools) {
        if (!this.isToolAllowed(mcpTool.name, config.tags)) {
          continue;
        }

        // Convert MCP tool to SDK tool with validation
        const baseTool = this.createToolFromMCPTool(mcpTool, instance, serverId);
        const wrappedTool = this.wrapToolWithValidation(baseTool, mcpTool.name);
        // Fix schema types for metadata too
        const fixedSchema = this.fixSchemaTypes(mcpTool.inputSchema);
        const toolMetadata: MCPToolMetadata = {
          serverId,
          serverName: config.name,
          toolName: mcpTool.name,
          description: mcpTool.description || '',
          schema: fixedSchema,
          tags: config.tags,
          cached: this.options.enableCaching,
          lastUpdated: new Date()
        };

        tools.push(wrappedTool);
        metadata.push(toolMetadata);
      }

      // Cache the results
      if (this.options.enableCaching) {
        this.addToCache(serverId, tools, metadata);
      }

      return { tools, metadata };
    } catch (error) {
      console.error(`Failed to list tools from MCP server ${serverId}:`, error);
      throw error;
    }
  }

  /**
   * Get tools by category/tags
   */
  async getToolsByTags(tags: string[]): Promise<{ tools: VercelAITool[]; metadata: MCPToolMetadata[] }> {
    const servers = mcpServerManager.getServersByTags(tags);
    const allTools: VercelAITool[] = [];
    const allMetadata: MCPToolMetadata[] = [];

    for (const { id, config, instance } of servers) {
      if (!instance || !this.isServerAllowed(id)) {
        continue;
      }

      try {
        const { tools, metadata } = await this.getToolsFromServer(id, config, instance);
        allTools.push(...tools);
        allMetadata.push(...metadata);
      } catch (error) {
        console.error(`Failed to get tools from MCP server ${id}:`, error);
      }
    }

    return { tools: allTools, metadata: allMetadata };
  }

  /**
   * Get a specific tool by name from any connected server
   */
  async getToolByName(toolName: string): Promise<{ tool: VercelAITool; metadata: MCPToolMetadata } | null> {
    const connectedServers = mcpServerManager.getConnectedServers();

    for (const { id, config, instance } of connectedServers) {
      if (!this.isServerAllowed(id)) {
        continue;
      }

      try {
        const { tools, metadata } = await this.getToolsFromServer(id, config, instance);
        const toolIndex = tools.findIndex(tool => (tool as any).name === toolName);
        
        if (toolIndex !== -1) {
          return {
            tool: tools[toolIndex],
            metadata: metadata[toolIndex]
          };
        }
      } catch (error) {
        console.error(`Failed to search for tool ${toolName} in server ${id}:`, error);
      }
    }

    return null;
  }

  /**
   * Invalidate cache for a specific server or all servers
   */
  invalidateCache(serverId?: string): void {
    if (serverId) {
      this.toolCache.delete(serverId);
    } else {
      this.toolCache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { totalEntries: number; totalTools: number; cacheHitRate: number } {
    let totalTools = 0;
    for (const cached of this.toolCache.values()) {
      totalTools += cached.tools.length;
    }

    const totalLookups = this.cacheHits + this.cacheMisses;
    return {
      totalEntries: this.toolCache.size,
      totalTools,
      cacheHitRate: totalLookups > 0 ? this.cacheHits / totalLookups : 0
    };
  }

  /**
   * Update tool filters
   */
  updateFilters(filters: Partial<MCPToolFactoryOptions['toolFilters']>): void {
    this.options.toolFilters = { ...this.options.toolFilters, ...filters };
    // Invalidate cache since filters changed
    this.invalidateCache();
  }

  /**
   * Fix Python type names to JSON Schema type names
   */
  private fixSchemaTypes(schema: any): any {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }
    
    // Clone the schema to avoid modifying the original
    const fixed = JSON.parse(JSON.stringify(schema));
    
    // Recursive function to fix types
    const fixTypes = (obj: any): void => {
      if (!obj || typeof obj !== 'object') return;
      
      // Fix type names
      if (obj.type === 'str') obj.type = 'string';
      if (obj.type === 'int') obj.type = 'integer';
      if (obj.type === 'bool') obj.type = 'boolean';
      if (obj.type === 'float') obj.type = 'number';
      if (obj.type === 'dict') obj.type = 'object';
      if (obj.type === 'list') obj.type = 'array';
      
      // Recursively fix nested schemas
      if (obj.properties) {
        for (const key in obj.properties) {
          fixTypes(obj.properties[key]);
        }
      }
      
      if (obj.items) {
        fixTypes(obj.items);
      }
      
      if (Array.isArray(obj)) {
        obj.forEach(item => fixTypes(item));
      }
      
      // Handle anyOf, oneOf, allOf
      ['anyOf', 'oneOf', 'allOf'].forEach(key => {
        if (obj[key] && Array.isArray(obj[key])) {
          obj[key].forEach((subSchema: any) => fixTypes(subSchema));
        }
      });
    };
    
    fixTypes(fixed);
    return fixed;
  }

  /**
   * Convert JSON Schema to Zod schema dynamically
   */
  private jsonSchemaToZod(schema: any): any {
    if (!schema) return z.any();
    
    if (schema.type === 'object' && schema.properties) {
      const shape: any = {};
      for (const [key, propSchema] of Object.entries(schema.properties as any)) {
        let zodType = this.jsonSchemaToZod(propSchema);
        
        // Check if the property is optional
        if (!schema.required || !schema.required.includes(key)) {
          zodType = zodType.optional();
        }
        
        shape[key] = zodType;
      }
      return z.object(shape);
    }
    
    // Handle basic types
    switch (schema.type) {
      case 'string':
        return z.string();
      case 'number':
      case 'integer':
        return z.number();
      case 'boolean':
        return z.boolean();
      case 'array':
        return z.array(schema.items ? this.jsonSchemaToZod(schema.items) : z.any());
      default:
        return z.any();
    }
  }

  /**
   * Wrap tool with validation for parameter checking and retry mechanism
   */
  private wrapToolWithValidation(tool: VercelAITool, toolName: string): VercelAITool {
    try {
      return createValidatingToolWrapper(tool, toolName) as VercelAITool;
    } catch (error) {
      console.warn(`⚠️ Failed to create validation wrapper for ${toolName}:`, error);
      return tool; // Return unwrapped tool if validation fails
    }
  }

  /**
   * Create SDK tool from MCP tool definition
   */
  private createToolFromMCPTool(
    mcpTool: any,
    instance: MCPServer,
    serverId: string
  ): VercelAITool {
    // Fix Python-style type names in the schema
    const fixedSchema = this.fixSchemaTypes(mcpTool.inputSchema) || { type: 'object', properties: {} };
    
    // Convert JSON Schema to Zod schema for the tool() function
    const zodSchema = this.jsonSchemaToZod(fixedSchema);
    
    return tool({
      name: mcpTool.name,
      description: mcpTool.description || `Tool from MCP server: ${serverId}`,
      inputSchema: zodSchema, // Vercel AI SDK uses inputSchema instead of parameters
      execute: async (args: any) => {
        try {
          const result = await instance.callTool(mcpTool.name, args);
          
          // Handle different result formats
          let output: string;
          if (result && typeof result === 'object') {
            if ('content' in result) {
              output = Array.isArray(result.content) 
                ? result.content.map((c: any) => c.text || c).join('\n')
                : String(result.content);
            } else if ('text' in result) {
              output = String(result.text);
            } else {
              output = JSON.stringify(result);
            }
          } else {
            output = String(result || '');
          }
          
          // Apply token limiting and sanitization
          return sanitizeToolOutput(output);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown MCP tool error';
          console.error(`MCP tool ${mcpTool.name} failed:`, error);
          return `Error executing MCP tool ${mcpTool.name}: ${errorMessage}`;
        }
      }
    }) as any;
  }

  /**
   * Check if server is allowed by filters
   */
  private isServerAllowed(serverId: string): boolean {
    const { allowedServers, blockedServers } = this.options.toolFilters;

    if (blockedServers?.includes(serverId)) {
      return false;
    }

    if (allowedServers && allowedServers.length > 0) {
      return allowedServers.includes(serverId);
    }

    return true;
  }

  /**
   * Check if tool is allowed by filters
   */
  private isToolAllowed(toolName: string, serverTags: string[]): boolean {
    const { allowedTools, blockedTools, requiredTags } = this.options.toolFilters;

    if (blockedTools?.includes(toolName)) {
      return false;
    }

    if (allowedTools && allowedTools.length > 0 && !allowedTools.includes(toolName)) {
      return false;
    }

    if (requiredTags && requiredTags.length > 0) {
      const hasRequiredTag = requiredTags.some(tag => serverTags.includes(tag));
      if (!hasRequiredTag) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get tools from cache
   */
  private getFromCache(serverId: string): { tools: VercelAITool[]; metadata: MCPToolMetadata[] } | null {
    const cached = this.toolCache.get(serverId);
    if (!cached) {
      this.cacheMisses++;
      return null;
    }

    // Check if cache is expired
    const now = Date.now();
    if (now - cached.timestamp > this.options.cacheExpiry) {
      this.toolCache.delete(serverId);
      this.cacheMisses++;
      return null;
    }

    this.cacheHits++;
    return { tools: cached.tools, metadata: cached.metadata };
  }

  /**
   * Add tools to cache
   */
  private addToCache(serverId: string, tools: VercelAITool[], metadata: MCPToolMetadata[]): void {
    // Check cache size limit
    if (this.toolCache.size >= this.options.maxCacheSize) {
      // Remove oldest entry
      const oldestKey = this.toolCache.keys().next().value;
      if (oldestKey) {
        this.toolCache.delete(oldestKey);
      }
    }

    this.toolCache.set(serverId, {
      tools,
      metadata,
      timestamp: Date.now()
    });
  }
}

// Global singleton instance
export const mcpToolFactory = new MCPToolFactory();
