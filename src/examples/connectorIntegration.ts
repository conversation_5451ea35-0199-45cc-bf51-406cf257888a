/**
 * Example integration of ResponsesAPIClient with Dante's agent system
 * Shows production-ready patterns for using connectors and MCP servers
 */

import { ResponsesAPIClient, ProcessedResponse } from '../services/responsesAPIClient';
import { connectorService } from '../services/connectorService';
import { ConnectorId, OAuthToken } from '../types/connectors';

/**
 * Example 1: Google Workspace Integration
 * Demonstrates reading emails, checking calendar, and searching files
 */
export async function googleWorkspaceIntegration(tokens: {
  gmail: string;
  calendar: string;
  drive: string;
}) {
  const client = new ResponsesAPIClient({
    enableLogging: true,
    defaultModel: 'gpt-5'
  });

  const sessionId = client.createSession('google-workspace');

  // Store all OAuth tokens
  const gmailToken: OAuthToken = {
    access_token: tokens.gmail,
    token_type: 'Bearer',
    scope: 'https://www.googleapis.com/auth/gmail.modify',
    expires_at: Date.now() + 3600000 // 1 hour
  };

  const calendarToken: OAuthToken = {
    access_token: tokens.calendar,
    token_type: 'Bearer',
    scope: 'https://www.googleapis.com/auth/calendar.events',
    expires_at: Date.now() + 3600000
  };

  const driveToken: OAuthToken = {
    access_token: tokens.drive,
    token_type: 'Bearer',
    scope: 'https://www.googleapis.com/auth/drive.readonly',
    expires_at: Date.now() + 3600000
  };

  client.storeOAuthToken(sessionId, 'connector_gmail', gmailToken);
  client.storeOAuthToken(sessionId, 'connector_googlecalendar', calendarToken);
  client.storeOAuthToken(sessionId, 'connector_googledrive', driveToken);

  console.log('📧 Checking recent emails...');
  const emailResponse = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_gmail',
    input: 'Get my 5 most recent unread emails with their subjects and senders',
    require_approval: 'never',
    allowed_tools: ['get_recent_emails', 'read_email']
  });

  console.log('Email Summary:', emailResponse.output_text);

  console.log('\n📅 Checking calendar...');
  const calendarResponse = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_googlecalendar',
    input: 'What meetings do I have today and tomorrow?',
    require_approval: 'never',
    allowed_tools: ['search_events', 'read_event']
  });

  console.log('Calendar Summary:', calendarResponse.output_text);

  console.log('\n📁 Searching for documents...');
  const driveResponse = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_googledrive',
    input: 'Find all documents modified in the last week',
    require_approval: 'never',
    allowed_tools: ['recent_documents', 'search']
  });

  console.log('Drive Summary:', driveResponse.output_text);

  // Clean up
  client.clearSession(sessionId);

  return {
    emails: emailResponse,
    calendar: calendarResponse,
    drive: driveResponse
  };
}

/**
 * Example 2: Payment Processing with Stripe MCP
 * Shows approval workflow for sensitive operations
 */
export async function stripePaymentProcessing(stripeApiKey: string) {
  const client = new ResponsesAPIClient({
    enableLogging: true,
    defaultModel: 'gpt-5'
  });

  const sessionId = client.createSession('stripe-payment');

  console.log('💳 Creating payment link with Stripe...');
  
  // Initial request that will require approval
  const initialResponse = await client.executeMCPServerRequest({
    sessionId,
    server_url: 'https://mcp.stripe.com',
    server_label: 'Stripe',
    server_description: 'Stripe payment processing',
    authorization: `Bearer ${stripeApiKey}`,
    input: 'Create a payment link for $99.99 for "Premium Subscription"',
    require_approval: 'always' // Require approval for payment operations
  });

  // Check if approval is needed
  if (initialResponse.approval_requests.length > 0) {
    console.log('\n⚠️  Approval required for the following operations:');
    
    for (const request of initialResponse.approval_requests) {
      console.log(`- ${request.name}: ${JSON.stringify(request.arguments)}`);
    }

    // Simulate user approval (in production, this would be a UI interaction)
    const userApproved = await getUserApproval();

    if (userApproved) {
      console.log('\n✅ Approving payment creation...');
      
      const approvedResponse = await client.handleApproval({
        sessionId,
        approval_request_id: initialResponse.approval_requests[0].id,
        approve: true
      });

      console.log('Payment Link Created:', approvedResponse.output_text);
      return approvedResponse;
    } else {
      console.log('\n❌ Payment creation cancelled by user');
      
      await client.handleApproval({
        sessionId,
        approval_request_id: initialResponse.approval_requests[0].id,
        approve: false
      });
    }
  }

  return initialResponse;
}

/**
 * Example 3: Multi-Connector Workflow
 * Coordinates between multiple services for complex tasks
 */
export async function multiConnectorWorkflow(tokens: {
  gmail: string;
  calendar: string;
  teams?: string;
}) {
  const client = new ResponsesAPIClient({
    enableLogging: true,
    defaultModel: 'gpt-5'
  });

  const sessionId = client.createSession('multi-connector');

  // Store tokens
  client.storeOAuthToken(sessionId, 'connector_gmail', {
    access_token: tokens.gmail,
    token_type: 'Bearer',
    scope: 'https://www.googleapis.com/auth/gmail.modify'
  });

  client.storeOAuthToken(sessionId, 'connector_googlecalendar', {
    access_token: tokens.calendar,
    token_type: 'Bearer',
    scope: 'https://www.googleapis.com/auth/calendar.events'
  });

  console.log('🔄 Starting multi-connector workflow...\n');

  // Step 1: Search for meeting-related emails
  console.log('Step 1: Searching for meeting invitations in email...');
  const emailSearch = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_gmail',
    input: 'Find all emails about meetings or calendar invites from the last 3 days',
    require_approval: 'never',
    allowed_tools: ['search_emails', 'read_email']
  });

  // Step 2: Check calendar for conflicts
  console.log('\nStep 2: Checking calendar for conflicts...');
  const calendarCheck = await client.executeConnectorRequest({
    sessionId,
    connector_id: 'connector_googlecalendar',
    input: 'Check my availability for the next week and identify any meeting conflicts',
    require_approval: 'never',
    allowed_tools: ['search_events']
  });

  // Step 3: Generate summary
  console.log('\nStep 3: Generating coordination summary...');
  const summary = {
    emailFindings: emailSearch.output_text,
    calendarStatus: calendarCheck.output_text,
    recommendations: generateRecommendations(emailSearch, calendarCheck)
  };

  console.log('\n📊 Workflow Summary:');
  console.log('- Email Findings:', summary.emailFindings);
  console.log('- Calendar Status:', summary.calendarStatus);
  console.log('- Recommendations:', summary.recommendations);

  // Clean up
  client.clearSession(sessionId);

  return summary;
}

/**
 * Example 4: Using the ConnectorService wrapper
 * Shows how to use the updated ConnectorService with ResponsesAPIClient
 */
export async function connectorServiceExample() {
  console.log('🔧 Using ConnectorService with ResponsesAPIClient...\n');

  // Example 1: Execute a Gmail request
  const gmailResponse = await connectorService.executeConnectorRequest({
    connector_id: 'connector_gmail',
    oauth_token: 'ya29.example-token',
    input: 'Search for important emails from this week',
    require_approval: 'never',
    allowed_tools: ['search_emails']
  });

  console.log('Gmail Response:', gmailResponse);

  // Example 2: Execute an MCP server request
  const mcpResponse = await connectorService.executeMCPServerRequest({
    server_url: 'https://example-mcp.com/api',
    server_label: 'Example MCP',
    server_description: 'Example MCP server for testing',
    input: 'Get server status',
    require_approval: 'always'
  });

  console.log('MCP Response:', mcpResponse);

  // Example 3: Batch requests
  const batchResponses = await connectorService.executeBatchRequests([
    {
      connector_id: 'connector_gmail',
      server_label: 'Gmail',
      authorization: 'ya29.gmail-token',
      input: 'Count unread emails'
    },
    {
      connector_id: 'connector_googlecalendar',
      server_label: 'Google Calendar',
      authorization: 'ya29.calendar-token',
      input: 'Count events this week'
    }
  ]);

  console.log('Batch Responses:', batchResponses);

  return {
    gmail: gmailResponse,
    mcp: mcpResponse,
    batch: batchResponses
  };
}

/**
 * Example 5: Error Handling and Recovery
 * Shows robust error handling patterns
 */
export async function errorHandlingExample() {
  const client = new ResponsesAPIClient({
    enableLogging: true,
    maxRetries: 3,
    timeout: 30000
  });

  const sessionId = client.createSession('error-handling');

  try {
    // Attempt to use an expired token
    client.storeOAuthToken(sessionId, 'connector_gmail', {
      access_token: 'expired-token',
      token_type: 'Bearer',
      scope: 'gmail.modify',
      expires_at: Date.now() - 1000 // Already expired
    });

    await client.executeConnectorRequest({
      sessionId,
      connector_id: 'connector_gmail',
      input: 'This will fail due to expired token'
    });
  } catch (error: any) {
    console.log('❌ Expected error caught:', error.message);
    
    // Refresh token logic would go here
    console.log('🔄 Refreshing token...');
    
    // Store new valid token
    client.storeOAuthToken(sessionId, 'connector_gmail', {
      access_token: 'new-valid-token',
      token_type: 'Bearer',
      scope: 'gmail.modify',
      expires_at: Date.now() + 3600000
    });

    console.log('✅ Token refreshed, retrying request...');
    
    // Retry with new token
    try {
      const retryResponse = await client.executeConnectorRequest({
        sessionId,
        connector_id: 'connector_gmail',
        oauth_token: 'new-valid-token',
        input: 'Retry after token refresh'
      });
      
      console.log('Success:', retryResponse.output_text);
    } catch (retryError) {
      console.log('❌ Retry failed:', retryError);
    }
  } finally {
    // Always clean up
    client.clearSession(sessionId);
  }
}

// Helper functions

async function getUserApproval(): Promise<boolean> {
  // In production, this would show a UI prompt
  // For demo purposes, we'll simulate approval
  console.log('🤔 Waiting for user approval...');
  await new Promise(resolve => setTimeout(resolve, 1000));
  return true; // Simulate approval
}

function generateRecommendations(
  emailResponse: ProcessedResponse,
  calendarResponse: ProcessedResponse
): string {
  // Analyze responses and generate recommendations
  const hasConflicts = calendarResponse.output_text.toLowerCase().includes('conflict');
  const hasPendingInvites = emailResponse.output_text.toLowerCase().includes('invite');

  if (hasConflicts && hasPendingInvites) {
    return 'You have pending meeting invites that conflict with existing calendar events. Review and reschedule as needed.';
  } else if (hasPendingInvites) {
    return 'You have pending meeting invites. Review and add to calendar.';
  } else if (hasConflicts) {
    return 'You have calendar conflicts that need resolution.';
  }
  
  return 'No immediate actions required. Calendar and emails are in sync.';
}

// Main execution example
if (require.main === module) {
  (async () => {
    console.log('🚀 Starting ResponsesAPIClient Examples\n');
    console.log('=' .repeat(50));

    // Example with mock tokens (replace with real tokens for actual testing)
    const mockTokens = {
      gmail: process.env.GMAIL_OAUTH_TOKEN || 'ya29.mock-gmail-token',
      calendar: process.env.CALENDAR_OAUTH_TOKEN || 'ya29.mock-calendar-token',
      drive: process.env.DRIVE_OAUTH_TOKEN || 'ya29.mock-drive-token',
      stripe: process.env.STRIPE_API_KEY || 'sk_test_mock-stripe-key'
    };

    try {
      // Run examples based on available tokens
      if (process.env.GMAIL_OAUTH_TOKEN) {
        console.log('\n📧 Running Google Workspace Integration...\n');
        await googleWorkspaceIntegration(mockTokens);
      }

      if (process.env.STRIPE_API_KEY) {
        console.log('\n💳 Running Stripe Payment Processing...\n');
        await stripePaymentProcessing(mockTokens.stripe);
      }

      // Always run these examples as they work with mock data
      console.log('\n🔧 Running ConnectorService Example...\n');
      await connectorServiceExample();

      console.log('\n❌ Running Error Handling Example...\n');
      await errorHandlingExample();

    } catch (error) {
      console.error('Example failed:', error);
    }

    console.log('\n' + '='.repeat(50));
    console.log('✅ Examples completed!');
  })();
}