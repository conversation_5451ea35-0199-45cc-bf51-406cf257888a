#!/usr/bin/env bun

import { runDante, AgentInputItem, getMCPStatus, mcpServerManager } from './index';
import { handleStream } from './utils/streamHandler';
import { extractReasoningFromResponse, formatReasoningForDisplay } from './utils/extractReasoning';
import { mcpAgent, cleanupMCP } from './agents';
// Removed OpenAI Agents SDK - using Vercel AI SDK
import { config } from './utils/config';
import chalk from 'chalk';
import ora from 'ora';
import readline from 'readline';

// Create readline interface for interactive CLI
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: chalk.blue('\nDante > '),
});

// Conversation history
let conversationHistory: Array<{role: string, content: string}> = [];

// Welcome message
console.log(chalk.cyan('═══════════════════════════════════════════════════════════════'));
console.log(chalk.cyan.bold('                    Welcome to Dante AI Assistant                  '));
console.log(chalk.cyan('═══════════════════════════════════════════════════════════════'));
console.log(chalk.gray('\nPowered by GPT-5 • Type "help" for commands • "exit" to quit\n'));
console.log(chalk.yellow('Capabilities:'));
console.log('  • Research and information gathering');
console.log('  • Code generation and website creation');
console.log('  • Code refactoring and optimization');
console.log('  • Debugging and problem solving');
console.log('  • Security analysis and auditing');
console.log('  • Code review and quality assessment');
console.log('  • Weather information and forecasts');
console.log('  • MCP (Model Context Protocol) integration');
console.log('');

// Show help
function showHelp() {
  console.log(chalk.yellow('\nAvailable Commands:'));
  console.log('  help        - Show this help message');
  console.log('  clear       - Clear conversation history');
  console.log('  history     - Show conversation history');
  console.log('  exit        - Exit Dante');
  console.log('  mcp status  - Show MCP server status');
  console.log('  mcp servers - List MCP servers');
  console.log('  mcp tools   - Discover MCP tools');
  console.log('  mcp connect <server-id> - Connect MCP server');
  console.log('  mcp disconnect <server-id> - Disconnect MCP server');
  console.log('');
  console.log(chalk.yellow('Example Queries:'));
  console.log('  "What\'s the weather in San Francisco?"');
  console.log('  "Create a React landing page for a startup"');
  console.log('  "Debug this Python code: [paste code]"');
  console.log('  "Review my JavaScript function for improvements"');
  console.log('  "Research the latest web development trends"');
  console.log('  "List files in the current directory using MCP"');
  console.log('');
}

// Show MCP status
async function showMCPStatus() {
  try {
    const status = getMCPStatus();
    console.log(chalk.cyan('\n=== MCP Status ==='));
    console.log(`Enabled: ${status.enabled ? chalk.green('Yes') : chalk.red('No')}`);
    console.log(`Total Servers: ${status.serverCount}`);
    console.log(`Connected Servers: ${status.connectedCount}`);
    console.log(`Cache Stats: ${status.toolFactory.totalTools} tools, ${status.toolFactory.totalEntries} entries`);
    console.log('');
  } catch (error) {
    console.log(chalk.red('Error getting MCP status:'), error);
  }
}

// List MCP servers
async function listMCPServers() {
  try {
    const servers = mcpServerManager.getAllServerStatuses();
    console.log(chalk.cyan('\n=== MCP Servers ==='));
    
    if (servers.length === 0) {
      console.log(chalk.gray('No MCP servers registered'));
      return;
    }
    
    for (const server of servers) {
      const statusColor = server.status === 'connected' ? 'green' : 
                         server.status === 'error' ? 'red' : 'yellow';
      console.log(`${chalk[statusColor]('●')} ${server.name} (${server.id})`);
      console.log(`   Status: ${chalk[statusColor](server.status)}`);
      console.log(`   Tools: ${server.toolCount || 'Unknown'}`);
      if (server.lastConnected) {
        console.log(`   Last Connected: ${server.lastConnected.toLocaleString()}`);
      }
      if (server.lastError) {
        console.log(`   Last Error: ${chalk.red(server.lastError)}`);
      }
      console.log('');
    }
  } catch (error) {
    console.log(chalk.red('Error listing MCP servers:'), error);
  }
}

// Discover MCP tools
async function discoverMCPTools() {
  const spinner = ora('Discovering MCP tools...').start();
  
  try {
    // Use runDante which now uses Vercel AI SDK
    const result = await runDante('discover_mcp_tools with includeMetadata=true', {
      stream: false,
      maxTurns: 3,
      useMCP: true,
      workingDirectory: process.cwd(),
    });

    spinner.stop();
    console.log(chalk.cyan('\n=== Available MCP Tools ==='));
    console.log(typeof result === 'string' ? result : result.text || result.content || JSON.stringify(result));
    console.log('');
  } catch (error) {
    spinner.stop();
    console.log(chalk.red('Error discovering MCP tools:'), error);
  }
}

// Connect to MCP server
async function connectMCPServer(serverId: string) {
  const spinner = ora(`Connecting to MCP server: ${serverId}...`).start();
  
  try {
    await mcpServerManager.connectServer(serverId);
    spinner.stop();
    console.log(chalk.green(`✓ Successfully connected to MCP server: ${serverId}`));
    
    const status = mcpServerManager.getServerStatus(serverId);
    if (status) {
      console.log(`  Response time: ${status.responseTime}ms`);
      console.log(`  Tools: ${status.toolCount || 'Unknown'}`);
    }
    console.log('');
  } catch (error) {
    spinner.stop();
    console.log(chalk.red(`✗ Failed to connect to MCP server: ${serverId}`));
    console.log(chalk.red(`  Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
    console.log('');
  }
}

// Disconnect from MCP server
async function disconnectMCPServer(serverId: string) {
  const spinner = ora(`Disconnecting from MCP server: ${serverId}...`).start();
  
  try {
    await mcpServerManager.disconnectServer(serverId);
    spinner.stop();
    console.log(chalk.green(`✓ Successfully disconnected from MCP server: ${serverId}`));
    console.log('');
  } catch (error) {
    spinner.stop();
    console.log(chalk.red(`✗ Failed to disconnect from MCP server: ${serverId}`));
    console.log(chalk.red(`  Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
    console.log('');
  }
}

// Process user input
async function processInput(input: string) {
  const trimmedInput = input.trim();
  const lcInput = trimmedInput.toLowerCase();

  // Handle MCP commands
  if (lcInput.startsWith('mcp ')) {
    const mcpCommand = trimmedInput.substring(4).trim();
    const parts = mcpCommand.split(' ');
    const command = parts[0].toLowerCase();
    const args = parts.slice(1);
    
    switch (command) {
      case 'status':
        await showMCPStatus();
        return;
      case 'servers':
        await listMCPServers();
        return;
      case 'tools':
        await discoverMCPTools();
        return;
      case 'connect':
        if (args.length === 0) {
          console.log(chalk.red('Error: Server ID required. Usage: mcp connect <server-id>'));
          return;
        }
        await connectMCPServer(args[0]);
        return;
      case 'disconnect':
        if (args.length === 0) {
          console.log(chalk.red('Error: Server ID required. Usage: mcp disconnect <server-id>'));
          return;
        }
        await disconnectMCPServer(args[0]);
        return;
      default:
        console.log(chalk.red(`Unknown MCP command: ${command}`));
        console.log(chalk.gray('Available MCP commands: status, servers, tools, connect <server-id>, disconnect <server-id>'));
        return;
    }
  }
  
  // Handle other special commands
  switch (lcInput) {
    case 'help':
      showHelp();
      return;
    case 'clear':
      conversationHistory = [];
      console.log(chalk.green('✓ Conversation history cleared'));
      return;
    case 'history':
      if (conversationHistory.length === 0) {
        console.log(chalk.gray('No conversation history'));
      } else {
        console.log(chalk.yellow('\nConversation History:'));
        conversationHistory.forEach((item, index) => {
          if (item.role === 'user') {
            console.log(chalk.blue(`  [${index}] User: ${item.content}`));
          } else {
            console.log(chalk.green(`  [${index}] Dante: ${item.content}`));
          }
        });
      }
      return;
    case 'exit':
    case 'quit':
    case 'bye':
      console.log(chalk.cyan('\nGoodbye! Thanks for using Dante AI Assistant.\n'));
      try {
        await cleanupMCP();
      } catch {}
      process.exit(0);
  }

  // Add user input to history
  conversationHistory.push({ role: 'user', content: input });

  // Show thinking spinner
  const spinner = ora({
    text: 'Dante is thinking...',
    color: 'cyan',
  }).start();

  try {
    // Convert to AgentInputItem format for the API
    const inputItems = conversationHistory.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      status: 'completed' as const,
    }));
    
    // Run Dante with streaming
    const stream = await runDante(inputItems as any, {
      stream: true,
      workingDirectory: process.cwd(),
    });

    spinner.stop();
    console.log(''); // New line before response

    // Handle the stream
    const result = await handleStream(stream as any, {
      onText: (text) => {
        process.stdout.write(chalk.white(text));
      },
      onAgentSwitch: (agentName) => {
        // Agent switch notifications are handled in streamHandler
      },
      onToolCall: (toolName) => {
        // Tool call notifications are handled in streamHandler
      },
      onReasoning: (reasoning) => {
        // Reasoning will be displayed after the main response
      },
      showProgress: false, // Disable inline progress for cleaner output
      captureReasoning: true, // Enable reasoning capture
    });

    // Check if there's reasoning to display (Vercel AI SDK compatible)
    try {
      let reasoningData = null as ReturnType<typeof extractReasoningFromResponse> | null;

      // Prefer final response object from Vercel AI SDK stream (if available)
      const maybeResponse = (stream as any)?.response;
      if (!reasoningData && maybeResponse) {
        try {
          const fullResp = await maybeResponse; // StreamTextResult.response
          reasoningData = extractReasoningFromResponse(fullResp) ||
            (fullResp?.thinking || fullResp?.reasoning
              ? { reasoning: fullResp.thinking || fullResp.reasoning, data: fullResp }
              : null);
        } catch {}
      }

      // Check stream state snapshot for reasoning fields
      if (!reasoningData && (stream as any)?.state) {
        const state = (stream as any).state;
        reasoningData = extractReasoningFromResponse(state) ||
          (state?.thinking || state?.reasoning
            ? { reasoning: state.thinking || state.reasoning, data: state }
            : null);
      }

      // Fallback: try to parse reasoning from the final text
      if (!reasoningData && result?.finalOutput) {
        reasoningData = extractReasoningFromResponse(result.finalOutput);
      }

      if (reasoningData) {
        console.log('\n' + chalk.gray('─'.repeat(60)));
        console.log(formatReasoningForDisplay(reasoningData));
      }
    } catch {}

    // Add assistant response to history
    if (result.finalOutput) {
      // Extract the actual text if it's structured output
      const responseText = typeof result.finalOutput === 'object' 
        ? ((result.finalOutput as any).data || (result.finalOutput as any).analysis || JSON.stringify(result.finalOutput))
        : result.finalOutput;
      
      conversationHistory.push({
        role: 'assistant',
        content: responseText,
      });
    }

    console.log(''); // New line after response
  } catch (error) {
    spinner.stop();
    console.error(chalk.red('\n✗ Error:'), error instanceof Error ? error.message : 'Unknown error');
    
    // Remove the failed user input from history
    conversationHistory.pop();
  }
}

// Handle line input
rl.on('line', async (input) => {
  if (input.trim()) {
    await processInput(input);
  }
  rl.prompt();
});

// Handle exit
rl.on('close', async () => {
  console.log(chalk.cyan('\nGoodbye! Thanks for using Dante AI Assistant.\n'));
  try {
    await cleanupMCP();
  } catch {}
  process.exit(0);
});

// Initial prompt
rl.prompt();
