import { 
  Memory, 
  MemoryType, 
  MemoryPriority, 
  <PERSON><PERSON><PERSON>y, 
  Semantic<PERSON><PERSON><PERSON>, 
  MemoryOperationResult, 
  MemorySearchResult,
  MemoryStats,
  MemoryMetadata,
  ErrorMemory,
  UserPreferenceMemory,
  ProjectMemory
} from './types';
import { VectorMemoryStore } from './stores/VectorMemoryStore';
import { FileMemoryStore } from './stores/FileMemoryStore';
import { QdrantMemoryStore } from './stores/QdrantMemoryStore';
import { EmbeddingService } from './services/EmbeddingService';
import { RecallEngine } from './services/RecallEngine';
import { IMemoryManager } from './interfaces/IMemoryManager';
import { IVectorStore } from './interfaces/IVectorStore';

export interface MemoryManagerConfig {
  enableVectorSearch?: boolean;
  enableFileStorage?: boolean;
  useQdrant?: boolean;  // Enable Qdrant as primary vector store
  qdrantUrl?: string;
  qdrantApiKey?: string;
  maxMemories?: number;
  autoConsolidate?: boolean;
  consolidationInterval?: number;  // in milliseconds
  defaultTTL?: number;  // default time to live
}

export class MemoryManager implements IMemoryManager {
  private static instance: MemoryManager;
  private vectorStore: IVectorStore;  // Primary storage - Qdrant or in-memory
  private fileStore?: FileMemoryStore;
  private embeddingService: EmbeddingService;
  private recallEngine: RecallEngine;
  private config: MemoryManagerConfig;
  private consolidationTimer?: NodeJS.Timeout;
  private memoryIndex: Map<string, MemoryMetadata>;
  private isInitialized: boolean = false;
  private initializationPromise?: Promise<void>;

  private constructor(config: MemoryManagerConfig = {}) {
    // Detect environment and disable file storage in browser
    const isBrowser = typeof window !== 'undefined';
    
    this.config = {
      enableVectorSearch: true,
      enableFileStorage: !isBrowser && (config.enableFileStorage ?? true), // Enable by default in Node.js
      useQdrant: config.useQdrant ?? (process.env.QDRANT_URL ? true : false), // Auto-detect from env
      qdrantUrl: config.qdrantUrl || process.env.QDRANT_URL,
      qdrantApiKey: config.qdrantApiKey || process.env.QDRANT_API_KEY,
      maxMemories: 10000,
      autoConsolidate: true,
      consolidationInterval: 7200000, // 2 hours (reduced frequency)
      defaultTTL: 30 * 24 * 60 * 60 * 1000, // 30 days
      ...config
    };

    this.memoryIndex = new Map();
    this.embeddingService = new EmbeddingService();
    this.recallEngine = new RecallEngine(this);

    // Choose vector store based on configuration
    if (this.config.useQdrant && !isBrowser) {
      // Use Qdrant as primary storage for server-side environments
      console.log('Using QdrantMemoryStore as primary storage');
      this.vectorStore = new QdrantMemoryStore({
        url: this.config.qdrantUrl,
        apiKey: this.config.qdrantApiKey,
      });
    } else {
      // Fall back to in-memory vector store for browser or when Qdrant is not configured
      console.log('Using in-memory VectorMemoryStore');
      this.vectorStore = new VectorMemoryStore();
    }

    if (this.config.enableFileStorage && !isBrowser) {
      this.fileStore = new FileMemoryStore();
    }
  }

  static getInstance(config?: MemoryManagerConfig): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager(config);
    }
    return MemoryManager.instance;
  }

  /**
   * Check if the memory manager is initialized
   */
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  async initialize(): Promise<void> {
    // If already initialized, return immediately
    if (this.isInitialized) {
      return;
    }

    // If initialization is in progress, wait for it to complete
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Start initialization and track the promise
    this.initializationPromise = this.performInitialization();
    
    try {
      await this.initializationPromise;
      this.isInitialized = true;
    } finally {
      // Clear the promise regardless of success/failure
      this.initializationPromise = undefined;
    }
  }

  private async performInitialization(): Promise<void> {
    await this.vectorStore.initialize();

    if (this.fileStore) {
      await this.fileStore.initialize();
    }

    await this.loadMemoryIndex();

    if (this.config.autoConsolidate) {
      this.startConsolidation();
    }
  }

  isReady(): boolean {
    return this.isInitialized;
  }

  // Core CRUD Operations

  async create<T = any>(
    type: MemoryType,
    content: T,
    options: Partial<MemoryMetadata> = {}
  ): Promise<MemoryOperationResult> {
    try {
      const memory: Memory<T> = {
        metadata: {
          id: this.generateMemoryId(),
          type,
          priority: options.priority || MemoryPriority.MEDIUM,
          createdAt: new Date(),
          lastAccessed: new Date(),
          accessCount: 0,
          confidence: options.confidence || 0.8,
          source: options.source || 'system',
          tags: options.tags || [],
          projectId: options.projectId,
          userId: options.userId,
          ttl: options.ttl || this.config.defaultTTL,
          ...options
        },
        content,
        related: []
      };

      // Generate embedding if semantic content
      if (this.shouldGenerateEmbedding(memory)) {
        const embedding = await this.embeddingService.generateEmbedding(
          this.extractTextContent(content)
        );
        memory.metadata.embedding = embedding;
      }

      // Store in vector store (primary storage)
      await this.vectorStore.store(memory);

      if (this.fileStore && memory.metadata.projectId) {
        await this.fileStore.store(memory);
      }

      // Update index
      this.memoryIndex.set(memory.metadata.id, memory.metadata);

      // Check memory limits
      await this.enforceMemoryLimits();

      return {
        success: true,
        memoryId: memory.metadata.id
      };
    } catch (error) {
      console.error('Failed to create memory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async retrieve(memoryId: string): Promise<Memory | null> {
    try {
      let memory = await this.vectorStore.retrieve(memoryId);
      
      if (!memory && this.fileStore) {
        memory = await this.fileStore.retrieve(memoryId);
      }

      if (memory) {
        // Update access metadata
        memory.metadata.lastAccessed = new Date();
        memory.metadata.accessCount++;
        
        // Ensure memory has embedding if it should have one
        if (this.shouldGenerateEmbedding(memory) && !memory.metadata.embedding) {
          const embedding = await this.embeddingService.generateEmbedding(
            this.extractTextContent(memory.content)
          );
          memory.metadata.embedding = embedding;
        }
        
        await this.vectorStore.update(memory);
      }

      return memory;
    } catch (error) {
      console.error('Failed to retrieve memory:', error);
      return null;
    }
  }

  async update<T = any>(
    memoryId: string,
    updates: Partial<Memory<T>>
  ): Promise<MemoryOperationResult> {
    try {
      const existing = await this.retrieve(memoryId);
      if (!existing) {
        return {
          success: false,
          error: 'Memory not found'
        };
      }

      const updated: Memory<T> = {
        ...existing,
        ...updates,
        metadata: {
          ...existing.metadata,
          ...updates.metadata,
          lastAccessed: new Date()
        }
      };

      // Regenerate embedding if content changed
      if (updates.content && this.shouldGenerateEmbedding(updated)) {
        const embedding = await this.embeddingService.generateEmbedding(
          this.extractTextContent(updates.content)
        );
        updated.metadata.embedding = embedding;
      }

      await this.vectorStore.update(updated);

      if (this.fileStore && updated.metadata.projectId) {
        await this.fileStore.update(updated);
      }

      this.memoryIndex.set(memoryId, updated.metadata);

      return {
        success: true,
        memoryId
      };
    } catch (error) {
      console.error('Failed to update memory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async delete(memoryId: string): Promise<MemoryOperationResult> {
    try {
      await this.vectorStore.delete(memoryId);

      if (this.fileStore) {
        await this.fileStore.delete(memoryId);
      }

      this.memoryIndex.delete(memoryId);

      return {
        success: true,
        memoryId
      };
    } catch (error) {
      console.error('Failed to delete memory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Search Operations

  async search(query: MemoryQuery | SemanticQuery): Promise<MemorySearchResult> {
    try {
      // If we have a query string, use semantic search
      if ('query' in query && query.query) {
        return this.semanticSearch({
          query: query.query,
          tags: query.tags,
          type: query.type,
          topK: query.limit || 10,
          threshold: 0.5
        });
      }
      
      // Otherwise use structured search on vector store
      const result = await this.vectorStore.search(query);
      return result;
    } catch (error) {
      console.error('Search failed:', error);
      return {
        memories: [],
        totalCount: 0
      };
    }
  }

  async semanticSearch(query: SemanticQuery): Promise<MemorySearchResult> {

    try {
      const queryEmbedding = await this.embeddingService.generateEmbedding(query.query);
      const results = await this.vectorStore.similaritySearch(
        queryEmbedding,
        query.topK || 10,
        query.threshold || 0.7
      );

      return {
        memories: results.memories,
        totalCount: results.memories.length,
        relevanceScores: results.scores
      };
    } catch (error) {
      console.error('Semantic search failed:', error);
      return {
        memories: [],
        totalCount: 0
      };
    }
  }

  // Specialized Memory Creation

  async rememberError(
    errorPattern: string,
    solution: string,
    context: any
  ): Promise<MemoryOperationResult> {
    const errorMemory: ErrorMemory['content'] = {
      errorPattern,
      errorMessage: errorPattern,
      solution,
      context,
      successRate: 1.0,
      occurrences: [{
        date: new Date(),
        resolved: true
      }]
    };

    return this.create(MemoryType.PROCEDURAL, errorMemory, {
      tags: ['error', 'solution', context.language, context.framework].filter(Boolean),
      priority: MemoryPriority.HIGH
    });
  }

  async rememberUserPreference(
    category: string,
    preference: any,
    explicitlyStated: boolean = false
  ): Promise<MemoryOperationResult> {
    const prefMemory: UserPreferenceMemory['content'] = {
      category,
      preference,
      explicitlyStated,
      inferredFrom: explicitlyStated ? [] : ['conversation']
    };

    return this.create(MemoryType.SEMANTIC, prefMemory, {
      tags: ['user-preference', category],
      priority: MemoryPriority.CRITICAL
    });
  }

  async rememberProject(
    projectPath: string,
    projectInfo: Partial<ProjectMemory['content']>
  ): Promise<MemoryOperationResult> {
    const projectMemory: ProjectMemory['content'] = {
      projectPath,
      projectName: projectInfo.projectName || projectPath.split('/').pop() || 'unknown',
      description: projectInfo.description,
      technology: projectInfo.technology || [],
      structure: projectInfo.structure || { mainFiles: [], directories: {} },
      commands: projectInfo.commands || {},
      dependencies: projectInfo.dependencies || [],
      patterns: projectInfo.patterns || [],
      conventions: projectInfo.conventions || [],
      lastWorkedOn: new Date()
    };

    return this.create(MemoryType.SEMANTIC, projectMemory, {
      projectId: projectPath,
      tags: ['project', ...projectInfo.technology || []],
      priority: MemoryPriority.HIGH
    });
  }

  // Recall Operations

  async recall(context: string, limit: number = 5): Promise<Memory[]> {
    const memories = await this.recallEngine.recall(context, limit);
    return memories;
  }

  /**
   * Enhanced recall that properly expands consolidated memories to show full details
   */
  async recallWithDetails(query: string, options: {
    limit?: number;
    includeConsolidated?: boolean;
    expandConsolidated?: boolean;
  } = {}): Promise<Memory[]> {
    return this.recallEngine.recallWithDetails(query, options);
  }

  async recallErrors(errorPattern: string): Promise<ErrorMemory[]> {
    const results = await this.semanticSearch({
      query: errorPattern,
      tags: ['error'],
      topK: 5
    });

    return results.memories as ErrorMemory[];
  }

  async recallUserPreferences(category?: string): Promise<UserPreferenceMemory[]> {
    const query: MemoryQuery = {
      tags: ['user-preference'],
      type: MemoryType.SEMANTIC
    };

    if (category) {
      query.tags!.push(category);
    }

    const results = await this.search(query);
    return results.memories as UserPreferenceMemory[];
  }

  async recallProjectKnowledge(projectPath: string): Promise<ProjectMemory | null> {
    const results = await this.search({
      projectId: projectPath,
      type: MemoryType.SEMANTIC,
      tags: ['project'],
      limit: 1
    });

    return results.memories[0] as ProjectMemory || null;
  }

  // Memory Management

  async consolidate(): Promise<void> {
    const startTime = Date.now();
    const initialCount = this.memoryIndex.size;
    console.log(`Starting memory consolidation... (${initialCount} memories in index)`);
    
    // Remove expired memories
    await this.removeExpiredMemories();
    
    // Merge similar memories
    const mergeResults = await this.mergeSimilarMemories();
    
    // Update memory relationships
    await this.updateMemoryRelationships();
    
    // Optimize storage
    await this.optimizeStorage();
    
    const endTime = Date.now();
    const finalCount = this.memoryIndex.size;
    console.log(`Memory consolidation complete in ${endTime - startTime}ms`);
    console.log(`  - Initial memories: ${initialCount}`);
    console.log(`  - Final memories: ${finalCount}`);
    console.log(`  - Memories reduced: ${initialCount - finalCount}`);
  }

  async getStats(): Promise<MemoryStats> {
    // Get memories to calculate stats
    const allMemories = await this.getAllMemories();
    const memories = allMemories.memories;
    
    if (memories.length === 0) {
      return {
        totalMemories: 0,
        byType: {
          [MemoryType.EPISODIC]: 0,
          [MemoryType.SEMANTIC]: 0,
          [MemoryType.PROCEDURAL]: 0
        },
        byPriority: {
          [MemoryPriority.CRITICAL]: 0,
          [MemoryPriority.HIGH]: 0,
          [MemoryPriority.MEDIUM]: 0,
          [MemoryPriority.LOW]: 0,
          [MemoryPriority.TRIVIAL]: 0
        },
        oldestMemory: new Date(),
        newestMemory: new Date(),
        totalAccessCount: 0,
        averageConfidence: 0,
        storageSize: 0
      };
    }
    
    // Calculate stats
    const byType = {
      [MemoryType.EPISODIC]: 0,
      [MemoryType.SEMANTIC]: 0,
      [MemoryType.PROCEDURAL]: 0
    };
    
    const byPriority = {
      [MemoryPriority.CRITICAL]: 0,
      [MemoryPriority.HIGH]: 0,
      [MemoryPriority.MEDIUM]: 0,
      [MemoryPriority.LOW]: 0,
      [MemoryPriority.TRIVIAL]: 0
    };
    
    let totalAccessCount = 0;
    let totalConfidence = 0;
    const dates = memories.map(m => m.metadata.createdAt.getTime());
    
    memories.forEach(memory => {
      byType[memory.metadata.type]++;
      byPriority[memory.metadata.priority]++;
      totalAccessCount += memory.metadata.accessCount;
      totalConfidence += memory.metadata.confidence;
    });
    
    const jsonSize = JSON.stringify(memories).length;
    
    return {
      totalMemories: memories.length,
      byType,
      byPriority,
      oldestMemory: new Date(Math.min(...dates)),
      newestMemory: new Date(Math.max(...dates)),
      totalAccessCount,
      averageConfidence: totalConfidence / memories.length,
      storageSize: jsonSize
    };
  }

  async exportMemories(query?: MemoryQuery): Promise<string> {
    const memories = query ? await this.search(query) : await this.getAllMemories();
    return JSON.stringify(memories, null, 2);
  }

  async importMemories(json: string): Promise<MemoryOperationResult> {
    try {
      const memories = JSON.parse(json) as Memory[];
      
      // Bulk import using batch operations
      const results = await this.bulkCreate(memories);
      
      return {
        success: true,
        affectedCount: results.filter(r => r.success).length
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Invalid JSON'
      };
    }
  }

  getVectorStore() {
    return this.vectorStore;
  }
  
  async bulkCreate(memories: Memory[]): Promise<MemoryOperationResult[]> {
    const results: MemoryOperationResult[] = [];
    
    // Process in batches to avoid overwhelming the system
    const batchSize = 50;
    for (let i = 0; i < memories.length; i += batchSize) {
      const batch = memories.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchResults = await Promise.all(
        batch.map(async (memory) => {
          try {
            // Store in vector store (primary storage)
            await this.vectorStore.store(memory);
            
            // Update index
            this.memoryIndex.set(memory.metadata.id, memory.metadata);
            
            return { success: true, memoryId: memory.metadata.id };
          } catch (error) {
            return {
              success: false,
              memoryId: memory.metadata.id,
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        })
      );
      
      results.push(...batchResults);
    }
    
    return results;
  }

  // Private helper methods

  private generateMemoryId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private shouldGenerateEmbedding(memory: Memory): boolean {
    return (
      memory.metadata.type === MemoryType.SEMANTIC ||
      memory.metadata.type === MemoryType.PROCEDURAL ||
      memory.metadata.tags?.includes('searchable')
    );
  }

  private extractTextContent(content: any): string {
    if (typeof content === 'string') return content;
    if (typeof content === 'object') {
      return JSON.stringify(content);
    }
    return String(content);
  }

  private async loadMemoryIndex(): Promise<void> {
    const result = await this.getAllMemories();
    for (const memory of result.memories) {
      this.memoryIndex.set(memory.metadata.id, memory.metadata);
    }
  }

  private async getAllMemories(): Promise<MemorySearchResult> {
    if (this.vectorStore && 'getAllMemories' in this.vectorStore) {
      // QdrantMemoryStore has a getAllMemories method
      const memories = await (this.vectorStore as any).getAllMemories();
      return {
        memories,
        totalCount: memories.length
      };
    }
    
    // Fallback: return empty result if no vector store
    return {
      memories: [],
      totalCount: 0
    };
  }

  private async enforceMemoryLimits(): Promise<void> {
    if (this.memoryIndex.size <= this.config.maxMemories!) return;

    // Remove lowest priority, least accessed memories
    const sortedMemories = Array.from(this.memoryIndex.values()).sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority;
      if (a.accessCount !== b.accessCount) return a.accessCount - b.accessCount;
      return a.createdAt.getTime() - b.createdAt.getTime();
    });

    const toDelete = sortedMemories.slice(0, this.memoryIndex.size - this.config.maxMemories!);
    const memoryIdsToDelete = toDelete.map(metadata => metadata.id);
    
    // Use bulk delete for efficiency
    if (memoryIdsToDelete.length > 0) {
      await this.bulkDelete(memoryIdsToDelete);
    }
  }

  private startConsolidation(): void {
    if (this.consolidationTimer) {
      clearInterval(this.consolidationTimer);
    }

    this.consolidationTimer = setInterval(
      () => this.consolidate(),
      this.config.consolidationInterval!
    );
  }

  private async removeExpiredMemories(): Promise<void> {
    const now = Date.now();
    const expired: string[] = [];

    for (const [id, metadata] of this.memoryIndex) {
      if (metadata.ttl) {
        const expiryTime = metadata.createdAt.getTime() + metadata.ttl;
        if (now > expiryTime) {
          expired.push(id);
        }
      }
    }

    // Bulk delete expired memories
    if (expired.length > 0) {
      await this.bulkDelete(expired);
    }
  }
  
  async bulkDelete(memoryIds: string[]): Promise<MemoryOperationResult> {
    if (memoryIds.length === 0) {
      return { success: true, memoryId: '' };
    }
    
    let deletedCount = 0;
    const errors: string[] = [];
    
    try {
      // Delete from all stores in parallel using bulk operations
      const deletePromises: Promise<void>[] = [];
      
      // Use bulk delete for vector store
      deletePromises.push(
        this.vectorStore.bulkDelete(memoryIds).catch(error => {
          errors.push(`VectorStore: ${error.message}`);
        })
      );
      
      // Use bulk delete for file store if available
      if (this.fileStore) {
        deletePromises.push(
          this.fileStore.bulkDelete(memoryIds).catch(error => {
            errors.push(`FileStore: ${error.message}`);
          })
        );
      }
      
      // Wait for all bulk deletions to complete
      await Promise.all(deletePromises);
      
      // Update memory index
      for (const id of memoryIds) {
        if (this.memoryIndex.delete(id)) {
          deletedCount++;
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Bulk delete failed:', errorMessage);
      errors.push(errorMessage);
    }
    
    return {
      success: true,
      affectedCount: deletedCount
    };
  }

  private async mergeSimilarMemories(): Promise<void> {
    // Find and merge semantically similar memories
    
    // Get all memories with embeddings
    const allMemories = await this.search({ limit: 1000 });
    const memoriesWithEmbeddings = allMemories.memories.filter(m => m.metadata.embedding);
    
    // Group similar memories (similarity > 0.85 - reduced from 0.95 to be less aggressive)
    const similarityThreshold = 0.85;
    const merged = new Set<string>();
    
    for (const memory of memoriesWithEmbeddings) {
      if (merged.has(memory.metadata.id)) continue;
      
      // Find highly similar memories
      const similar = await this.vectorStore.similaritySearch(
        memory.metadata.embedding!,
        5,
        similarityThreshold
      );
      
      if (similar.memories.length > 1) {
        // Merge similar memories
        const consolidatedMemory = await this.recallEngine.consolidateRelated(similar.memories);
        if (consolidatedMemory) {
          // Delete old memories
          for (const oldMemory of similar.memories) {
            merged.add(oldMemory.metadata.id);
            await this.delete(oldMemory.metadata.id);
          }
          
          // Store consolidated memory
          await this.create(
            consolidatedMemory.metadata.type,
            consolidatedMemory.content,
            consolidatedMemory.metadata
          );
        }
      }
    }
    
    console.log(`Merged ${merged.size} similar memories`);
  }

  private async updateMemoryRelationships(): Promise<void> {
    // Create relationships between related memories
    
    const memories = await this.search({ limit: 500 });
    
    for (const memory of memories.memories) {
      if (!memory.metadata.embedding) continue;
      
      // Find related memories
      const related = await this.vectorStore.findRelatedMemories(
        memory.metadata.id,
        3,
        0.7
      );
      
      if (related.length > 0) {
        // Update memory with related IDs
        memory.related = related.map(m => m.metadata.id);
        await this.update(memory.metadata.id, { related: memory.related });
      }
    }
  }

  private async optimizeStorage(): Promise<void> {
    // Optimize storage by archiving old memories and compressing data
    const stats = await this.getStats();
    
    // Archive memories not accessed in 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const oldMemories = await this.search({
      createdBefore: thirtyDaysAgo,
      limit: 1000
    });
    
    // Reduce priority of rarely accessed memories
    for (const memory of oldMemories.memories) {
      if (memory.metadata.accessCount < 2 && memory.metadata.priority > MemoryPriority.LOW) {
        await this.update(memory.metadata.id, {
          metadata: {
            ...memory.metadata,
            priority: MemoryPriority.LOW
          }
        });
      }
    }
    
    // Compact file storage if enabled
    if (this.fileStore) {
      // This triggers compaction in FileMemoryStore
      await this.fileStore.destroy();
      this.fileStore = new FileMemoryStore();
      await this.fileStore.initialize();
    }
    
    console.log(`Optimized storage: ${stats.totalMemories} total memories`);
  }

  async destroy(): Promise<void> {
    if (this.consolidationTimer) {
      clearInterval(this.consolidationTimer);
    }
    await this.vectorStore.destroy();
    if (this.fileStore) {
      await this.fileStore.destroy();
    }
  }
}

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();