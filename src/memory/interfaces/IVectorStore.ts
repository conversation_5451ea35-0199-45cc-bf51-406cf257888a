import { Memory, VectorSearchResult, VectorStoreConfig } from '../types';

export interface IVectorStore {
  // Core operations
  initialize(): Promise<void>;
  store(memory: Memory): Promise<void>;
  retrieve(memoryId: string): Promise<Memory | null>;
  update(memory: Memory): Promise<void>;
  delete(memoryId: string): Promise<void>;
  
  // Bulk operations
  bulkStore(memories: Memory[]): Promise<void>;
  bulkDelete(memoryIds: string[]): Promise<void>;
  
  // Search operations
  search(query: any): Promise<any>;  // Generic search for compatibility
  similaritySearch(
    queryEmbedding: number[],
    topK?: number,
    threshold?: number
  ): Promise<VectorSearchResult>;
  
  hybridSearch(
    textQuery: string,
    filters?: {
      type?: string;
      tags?: string[];
      minConfidence?: number;
      projectId?: string;
      userId?: string;
    },
    topK?: number
  ): Promise<VectorSearchResult>;
  
  // Related operations
  findRelatedMemories(
    memoryId: string,
    topK?: number,
    threshold?: number
  ): Promise<Memory[]>;
  
  // Analytics operations
  clusterMemories(k?: number): Promise<Map<number, Memory[]>>;
  getStats(): Promise<{
    totalVectors: number;
    collections?: string[];
    indexInfo?: any;
  }>;
  
  // Maintenance operations
  reindex(): Promise<void>;
  createSnapshot(): Promise<string>;
  restoreSnapshot(snapshotId: string): Promise<void>;
  destroy(): Promise<void>;
}