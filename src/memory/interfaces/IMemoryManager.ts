import {
  Memory,
  MemoryType,
  <PERSON><PERSON><PERSON>y,
  <PERSON>mantic<PERSON>uery,
  MemoryOperationResult,
  MemorySearchResult,
  MemoryMetadata,
  ErrorMemory,
  UserPreferenceMemory,
  ProjectMemory
} from '../types';
import { IVectorStore } from './IVectorStore';

/**
 * Interface for MemoryManager to avoid circular dependencies and improve type safety
 */
export interface IMemoryManager {
  // Core operations
  create<T = any>(
    type: MemoryType,
    content: T,
    options?: Partial<MemoryMetadata>
  ): Promise<MemoryOperationResult>;
  
  retrieve(memoryId: string): Promise<Memory | null>;
  
  update<T = any>(
    memoryId: string,
    updates: Partial<Memory<T>>
  ): Promise<MemoryOperationResult>;
  
  delete(memoryId: string): Promise<MemoryOperationResult>;
  
  // Search operations
  search(query: MemoryQuery): Promise<MemorySearchResult>;
  
  semanticSearch(query: SemanticQuery): Promise<MemorySearchResult>;
  
  // Recall operations
  recall(context: string, limit?: number): Promise<Memory[]>;
  
  recallErrors(errorPattern: string): Promise<ErrorMemory[]>;
  
  recallUserPreferences(category?: string): Promise<UserPreferenceMemory[]>;
  
  recallProjectKnowledge(projectPath: string): Promise<ProjectMemory | null>;
  
  // Specialized memory creation
  rememberError(
    errorPattern: string,
    solution: string,
    context: any
  ): Promise<MemoryOperationResult>;
  
  rememberUserPreference(
    category: string,
    preference: any,
    explicitlyStated?: boolean
  ): Promise<MemoryOperationResult>;
  
  rememberProject(
    projectPath: string,
    projectInfo: Partial<ProjectMemory['content']>
  ): Promise<MemoryOperationResult>;
  
  // Management
  initialize(): Promise<void>;
  isReady(): boolean;
  consolidate(): Promise<void>;
  exportMemories(query?: MemoryQuery): Promise<string>;
  importMemories(json: string): Promise<MemoryOperationResult>;
  
  // Store access (for internal use)
  getVectorStore(): IVectorStore | undefined;
}