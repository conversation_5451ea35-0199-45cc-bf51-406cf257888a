// Memory type definitions and interfaces

export enum MemoryType {
  EPISODIC = 'episodic',    // Short-term, contextual memories
  SEMANTIC = 'semantic',     // Long-term knowledge and facts
  PROCEDURAL = 'procedural'  // Skills, patterns, and how-to knowledge
}

export enum MemoryPriority {
  CRITICAL = 5,  // Must never forget (user preferences, critical errors)
  HIGH = 4,      // Important patterns and solutions
  MEDIUM = 3,    // Regular knowledge
  LOW = 2,       // Nice to have information
  TRIVIAL = 1    // Can be forgotten if needed
}

export interface MemoryMetadata {
  id: string;
  type: MemoryType;
  priority: MemoryPriority;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  confidence: number;  // 0-1 confidence score
  source: string;      // Where this memory came from
  tags: string[];      // For categorization
  projectId?: string;  // Associated project if any
  userId?: string;     // Associated user if any
  ttl?: number;        // Time to live in milliseconds
  embedding?: number[]; // Vector embedding for semantic search
}

export interface Memory<T = any> {
  metadata: MemoryMetadata;
  content: T;
  related?: string[];  // IDs of related memories
}

// Episodic Memory - Recent interactions and context
export interface EpisodicMemory extends Memory {
  metadata: MemoryMetadata & { type: MemoryType.EPISODIC };
  content: {
    conversation?: {
      messages: Array<{ role: string; content: string }>;
      summary: string;
      timestamp: Date;
    };
    context?: {
      task: string;
      outcome: string;
      duration: number;
    };
    state?: Record<string, any>;  // Current state snapshot
  };
}

// Semantic Memory - Long-term knowledge
export interface SemanticMemory extends Memory {
  metadata: MemoryMetadata & { type: MemoryType.SEMANTIC };
  content: {
    subject: string;
    knowledge: string;
    facts?: string[];
    relationships?: Array<{
      entity: string;
      relation: string;
      strength: number;
    }>;
    examples?: string[];
  };
}

// Procedural Memory - How to do things
export interface ProceduralMemory extends Memory {
  metadata: MemoryMetadata & { type: MemoryType.PROCEDURAL };
  content: {
    task: string;
    steps: string[];
    preconditions?: string[];
    postconditions?: string[];
    successRate?: number;
    lastSuccess?: Date;
    failures?: Array<{
      date: Date;
      reason: string;
    }>;
  };
}

// Error Memory - Specific type for error patterns and solutions
export interface ErrorMemory extends Memory {
  metadata: MemoryMetadata & { 
    type: MemoryType.PROCEDURAL;
    tags: string[];  // Will include 'error', 'solution'
  };
  content: {
    errorPattern: string;
    errorMessage: string;
    solution: string;
    context: {
      file?: string;
      line?: number;
      language?: string;
      framework?: string;
    };
    successRate: number;
    occurrences: Array<{
      date: Date;
      resolved: boolean;
      timeTaken?: number;
    }>;
  };
}

// User Preference Memory
export interface UserPreferenceMemory extends Memory {
  metadata: MemoryMetadata & {
    type: MemoryType.SEMANTIC;
    tags: string[];  // Will include 'user-preference'
  };
  content: {
    category: string;  // 'ui', 'color', 'coding-style', etc.
    preference: any;
    examples?: string[];
    explicitlyStated: boolean;
    inferredFrom?: string[];
  };
}

// Project Knowledge Memory
export interface ProjectMemory extends Memory {
  metadata: MemoryMetadata & {
    type: MemoryType.SEMANTIC;
    projectId: string;
  };
  content: {
    projectPath: string;
    projectName: string;
    description?: string;
    technology: string[];
    structure: {
      mainFiles: string[];
      directories: Record<string, string>;  // dir -> description
    };
    commands: Record<string, string>;  // command -> description
    dependencies: string[];
    patterns: string[];  // Common patterns used
    conventions: string[];  // Coding conventions
    lastWorkedOn: Date;
  };
}

// Memory Query interfaces
export interface MemoryQuery {
  type?: MemoryType | MemoryType[];
  tags?: string[];
  projectId?: string;
  userId?: string;
  minConfidence?: number;
  minPriority?: MemoryPriority;
  createdAfter?: Date;
  createdBefore?: Date;
  limit?: number;
  includeRelated?: boolean;
}

export interface SemanticQuery extends MemoryQuery {
  query: string;
  topK?: number;
  threshold?: number;  // Similarity threshold
}

// Memory operation results
export interface MemoryOperationResult {
  success: boolean;
  memoryId?: string;
  error?: string;
  affectedCount?: number;
}

export interface MemorySearchResult<T = any> {
  memories: Memory<T>[];
  totalCount: number;
  relevanceScores?: number[];
}

// Memory stats for monitoring
export interface MemoryStats {
  totalMemories: number;
  byType: Record<MemoryType, number>;
  byPriority: Record<MemoryPriority, number>;
  oldestMemory: Date;
  newestMemory: Date;
  totalAccessCount: number;
  averageConfidence: number;
  storageSize: number;  // in bytes
}

// Vector store types
export interface VectorSearchResult {
  memories: Memory[];
  scores: number[];
}

export interface VectorStoreConfig {
  url?: string;
  apiKey?: string;
  collectionName?: string;
  dimension?: number;
  metric?: 'cosine' | 'euclidean' | 'dot';
}