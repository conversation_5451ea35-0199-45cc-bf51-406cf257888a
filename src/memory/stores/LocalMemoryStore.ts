import { openDB, DBSchema, IDBPDatabase } from 'idb';
import {
  Memory,
  MemoryType,
  MemoryPriority,
  MemoryQuery,
  MemorySearchResult,
  MemoryStats
} from '../types';

interface MemoryDB extends DBSchema {
  memories: {
    key: string;
    value: Memory;
    indexes: {
      'by-type': MemoryType;
      'by-priority': MemoryPriority;
      'by-created': number;
      'by-accessed': number;
      'by-project': string;
      'by-user': string;
      'by-tags': string;
    };
  };
  metadata: {
    key: string;
    value: any;
  };
}

export class LocalMemoryStore {
  private db: IDBPDatabase<MemoryDB> | null = null;
  private readonly DB_NAME = 'DanteMemoryDB';
  private readonly DB_VERSION = 1;
  private inMemoryStore: Map<string, Memory> = new Map(); // Fallback for Node.js
  private isNodeEnvironment: boolean;

  constructor() {
    this.isNodeEnvironment = typeof window === 'undefined';
  }

  async initialize(): Promise<void> {
    // Use in-memory storage for Node.js environment
    if (this.isNodeEnvironment) {
      console.log('LocalMemoryStore: Using in-memory storage (Node.js environment)');
      return;
    }

    try {
      this.db = await openDB<MemoryDB>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          if (!db.objectStoreNames.contains('memories')) {
            const memoryStore = db.createObjectStore('memories', {
              keyPath: 'metadata.id'
            });
            
            // Create indexes for efficient querying
            memoryStore.createIndex('by-type', 'metadata.type');
            memoryStore.createIndex('by-priority', 'metadata.priority');
            memoryStore.createIndex('by-created', 'metadata.createdAt');
            memoryStore.createIndex('by-accessed', 'metadata.lastAccessed');
            memoryStore.createIndex('by-project', 'metadata.projectId');
            memoryStore.createIndex('by-user', 'metadata.userId');
            memoryStore.createIndex('by-tags', 'metadata.tags', { multiEntry: true });
          }

          if (!db.objectStoreNames.contains('metadata')) {
            db.createObjectStore('metadata');
          }
        },
      });
    } catch (error) {
      console.error('Failed to initialize LocalMemoryStore:', error);
      throw error;
    }
  }

  async store(memory: Memory): Promise<void> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      this.inMemoryStore.set(memory.metadata.id, memory);
      return;
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Convert Date objects to timestamps for storage
      const storable = this.prepareForStorage(memory);
      await this.db.put('memories', storable);
    } catch (error) {
      console.error('Failed to store memory:', error);
      throw error;
    }
  }

  async retrieve(memoryId: string): Promise<Memory | null> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      return this.inMemoryStore.get(memoryId) || null;
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const memory = await this.db.get('memories', memoryId);
      return memory ? this.restoreFromStorage(memory) : null;
    } catch (error) {
      console.error('Failed to retrieve memory:', error);
      return null;
    }
  }

  async update(memory: Memory): Promise<void> {
    return this.store(memory);
  }

  async delete(memoryId: string): Promise<void> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      this.inMemoryStore.delete(memoryId);
      return;
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      await this.db.delete('memories', memoryId);
    } catch (error) {
      console.error('Failed to delete memory:', error);
      throw error;
    }
  }

  async bulkDelete(memoryIds: string[]): Promise<void> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      memoryIds.forEach(id => this.inMemoryStore.delete(id));
      return;
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Use a transaction for atomic bulk delete
      const tx = this.db.transaction('memories', 'readwrite');
      const store = tx.objectStore('memories');
      
      // Delete all memories in the transaction
      await Promise.all(memoryIds.map(id => store.delete(id)));
      
      // Wait for transaction to complete
      await tx.done;
    } catch (error) {
      console.error('Failed to bulk delete memories:', error);
      throw error;
    }
  }

  async search(query: MemoryQuery): Promise<MemorySearchResult> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      return this.searchInMemory(query);
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      let memories: Memory[] = [];
      
      // Start with the most specific index if available
      if (query.type && !Array.isArray(query.type)) {
        memories = await this.db.getAllFromIndex('memories', 'by-type', query.type);
      } else if (query.projectId) {
        memories = await this.db.getAllFromIndex('memories', 'by-project', query.projectId);
      } else if (query.userId) {
        memories = await this.db.getAllFromIndex('memories', 'by-user', query.userId);
      } else {
        memories = await this.db.getAll('memories');
      }

      // Apply filters
      memories = this.applyFilters(memories, query);
      
      // Sort by relevance (most recently accessed first)
      memories.sort((a, b) => 
        b.metadata.lastAccessed.getTime() - a.metadata.lastAccessed.getTime()
      );

      // Apply limit
      if (query.limit) {
        memories = memories.slice(0, query.limit);
      }

      // Restore Date objects
      memories = memories.map(m => this.restoreFromStorage(m));

      return {
        memories,
        totalCount: memories.length
      };
    } catch (error) {
      console.error('Search failed:', error);
      return { memories: [], totalCount: 0 };
    }
  }

  async searchByTags(tags: string[]): Promise<Memory[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const allMemories: Memory[] = [];
      
      for (const tag of tags) {
        const memories = await this.db.getAllFromIndex('memories', 'by-tags', tag);
        allMemories.push(...memories);
      }

      // Remove duplicates
      const uniqueMemories = Array.from(
        new Map(allMemories.map(m => [m.metadata.id, m])).values()
      );

      return uniqueMemories.map(m => this.restoreFromStorage(m));
    } catch (error) {
      console.error('Tag search failed:', error);
      return [];
    }
  }

  async getStats(): Promise<MemoryStats> {
    if (this.isNodeEnvironment) {
      // Use in-memory store for Node.js
      return this.getStatsFromMemory();
    }

    if (!this.db) throw new Error('Database not initialized');
    
    try {
      // Use count for total memories (much more efficient)
      const totalMemories = await this.db.count('memories');
      
      const stats: MemoryStats = {
        totalMemories,
        byType: {
          [MemoryType.EPISODIC]: 0,
          [MemoryType.SEMANTIC]: 0,
          [MemoryType.PROCEDURAL]: 0
        },
        byPriority: {
          [MemoryPriority.CRITICAL]: 0,
          [MemoryPriority.HIGH]: 0,
          [MemoryPriority.MEDIUM]: 0,
          [MemoryPriority.LOW]: 0,
          [MemoryPriority.TRIVIAL]: 0
        },
        oldestMemory: new Date(),
        newestMemory: new Date(),
        totalAccessCount: 0,
        averageConfidence: 0,
        storageSize: 0
      };

      if (totalMemories === 0) {
        return stats;
      }

      // Use cursor to iterate without loading all data into memory
      const tx = this.db.transaction('memories', 'readonly');
      const store = tx.objectStore('memories');
      let cursor = await store.openCursor();
      
      let totalConfidence = 0;
      let oldestTime = Date.now();
      let newestTime = 0;
      let count = 0;

      while (cursor) {
        const memory = cursor.value;
        
        // Type counts
        stats.byType[memory.metadata.type]++;
        
        // Priority counts
        stats.byPriority[memory.metadata.priority]++;
        
        // Access count
        stats.totalAccessCount += memory.metadata.accessCount;
        
        // Confidence
        totalConfidence += memory.metadata.confidence;
        
        // Dates
        const createdTime = memory.metadata.createdAt;
        if (typeof createdTime === 'number') {
          if (createdTime < oldestTime) oldestTime = createdTime;
          if (createdTime > newestTime) newestTime = createdTime;
        } else {
          const createdTimeMs = createdTime.getTime();
          if (createdTimeMs < oldestTime) oldestTime = createdTimeMs;
          if (createdTimeMs > newestTime) newestTime = createdTimeMs;
        }
        
        // Estimate storage size (sample every 10th record for efficiency)
        if (count % 10 === 0) {
          stats.storageSize += JSON.stringify(memory).length * 10;
        }
        
        count++;
        cursor = await cursor.continue();
      }

      stats.averageConfidence = totalConfidence / totalMemories;
      stats.oldestMemory = new Date(oldestTime);
      stats.newestMemory = new Date(newestTime);

      return stats;
    } catch (error) {
      console.error('Failed to get stats:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const tx = this.db.transaction('memories', 'readwrite');
      await tx.objectStore('memories').clear();
    } catch (error) {
      console.error('Failed to clear memories:', error);
      throw error;
    }
  }

  async destroy(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  // Helper methods

  private applyFilters(memories: Memory[], query: MemoryQuery): Memory[] {
    return memories.filter(memory => {
      // Type filter
      if (query.type) {
        const types = Array.isArray(query.type) ? query.type : [query.type];
        if (!types.includes(memory.metadata.type)) return false;
      }

      // Tag filter
      if (query.tags && query.tags.length > 0) {
        const hasAllTags = query.tags.every(tag => 
          memory.metadata.tags.includes(tag)
        );
        if (!hasAllTags) return false;
      }

      // Priority filter
      if (query.minPriority !== undefined) {
        if (memory.metadata.priority < query.minPriority) return false;
      }

      // Confidence filter
      if (query.minConfidence !== undefined) {
        if (memory.metadata.confidence < query.minConfidence) return false;
      }

      // Date filters
      if (query.createdAfter) {
        if (memory.metadata.createdAt < query.createdAfter) return false;
      }

      if (query.createdBefore) {
        if (memory.metadata.createdAt > query.createdBefore) return false;
      }

      return true;
    });
  }

  private prepareForStorage(memory: Memory): any {
    // Convert Date objects to timestamps
    const stored = JSON.parse(JSON.stringify(memory));
    stored.metadata.createdAt = memory.metadata.createdAt.getTime();
    stored.metadata.lastAccessed = memory.metadata.lastAccessed.getTime();
    return stored;
  }

  private restoreFromStorage(stored: any): Memory {
    // Restore Date objects
    const memory = { ...stored };
    memory.metadata.createdAt = new Date(stored.metadata.createdAt);
    memory.metadata.lastAccessed = new Date(stored.metadata.lastAccessed);
    return memory;
  }

  private searchInMemory(query: MemoryQuery): MemorySearchResult {
    let memories = Array.from(this.inMemoryStore.values());

    // Apply filters
    if (query.type) {
      memories = memories.filter(m => m.metadata.type === query.type);
    }

    if (query.tags && query.tags.length > 0) {
      memories = memories.filter(m => 
        query.tags!.some(tag => m.metadata.tags.includes(tag))
      );
    }

    if (query.minPriority !== undefined) {
      memories = memories.filter(m => m.metadata.priority >= query.minPriority!);
    }

    if (query.minConfidence !== undefined) {
      memories = memories.filter(m => m.metadata.confidence >= query.minConfidence!);
    }

    if (query.projectId) {
      memories = memories.filter(m => m.metadata.projectId === query.projectId);
    }

    if (query.userId) {
      memories = memories.filter(m => m.metadata.userId === query.userId);
    }

    // Apply limit
    if (query.limit) {
      memories = memories.slice(0, query.limit);
    }

    return {
      memories,
      totalCount: memories.length
    };
  }

  private getStatsFromMemory(): MemoryStats {
    const memories = Array.from(this.inMemoryStore.values());
    
    const stats: MemoryStats = {
      totalMemories: memories.length,
      byType: {
        [MemoryType.EPISODIC]: 0,
        [MemoryType.SEMANTIC]: 0,
        [MemoryType.PROCEDURAL]: 0
      },
      byPriority: {
        [MemoryPriority.CRITICAL]: 0,
        [MemoryPriority.HIGH]: 0,
        [MemoryPriority.MEDIUM]: 0,
        [MemoryPriority.LOW]: 0,
        [MemoryPriority.TRIVIAL]: 0
      },
      totalAccessCount: 0,
      averageConfidence: 0,
      storageSize: 0,
      oldestMemory: new Date(),
      newestMemory: new Date()
    };

    let totalAccessCount = 0;
    let oldestDate: Date | null = null;
    let newestDate: Date | null = null;

    for (const memory of memories) {
      // Count by type
      stats.byType[memory.metadata.type]++;
      
      // Count by priority
      stats.byPriority[memory.metadata.priority]++;
      
      // Sum access counts
      totalAccessCount += memory.metadata.accessCount;
      
      // Track dates
      if (!oldestDate || memory.metadata.createdAt < oldestDate) {
        oldestDate = memory.metadata.createdAt;
      }
      if (!newestDate || memory.metadata.createdAt > newestDate) {
        newestDate = memory.metadata.createdAt;
      }
    }

    if (memories.length > 0) {
      stats.totalAccessCount = totalAccessCount;
      stats.oldestMemory = oldestDate || new Date();
      stats.newestMemory = newestDate || new Date();
    }

    return stats;
  }
}