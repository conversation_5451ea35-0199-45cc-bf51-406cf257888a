import { QdrantClient } from '@qdrant/js-client-rest';
import { Memory, MemoryType, MemoryPriority, VectorSearchResult, VectorStoreConfig } from '../types';
import { IVectorStore } from '../interfaces/IVectorStore';
import { EmbeddingService } from '../services/EmbeddingService';

export class QdrantMemoryStore implements IVectorStore {
  private client: QdrantClient;
  private embeddingService: EmbeddingService;
  private collectionName: string;
  private dimension: number;
  private initialized: boolean = false;
  private rebuildPromise?: Promise<void>;

  constructor(config: VectorStoreConfig = {}) {
    this.client = new QdrantClient({
      url: config.url || process.env.QDRANT_URL || 'http://localhost:6333',
      apiKey: config.apiKey || process.env.QDRANT_API_KEY,
    });

    this.embeddingService = new EmbeddingService();
    this.collectionName = config.collectionName || 'dante_memories';
    const inferredDimension = this.embeddingService.getEmbeddingDimensions();
    this.dimension = config.dimension || (Number.isFinite(inferredDimension) && inferredDimension > 0 ? Math.floor(inferredDimension) : 1536);
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.ensureCollection();
      this.initialized = true;
      console.log(`QdrantMemoryStore initialized with collection: ${this.collectionName}`);
    } catch (error) {
      console.error('Failed to initialize QdrantMemoryStore:', error);
      throw error;
    }
  }

  private async createPayloadIndexes(): Promise<void> {
    // Create indexes for commonly filtered fields
    const indexFields = [
      { field: 'type', type: 'keyword' },
      { field: 'priority', type: 'integer' },
      { field: 'projectId', type: 'keyword' },
      { field: 'userId', type: 'keyword' },
      { field: 'tags', type: 'keyword' },
      { field: 'source', type: 'keyword' },
      { field: 'confidence', type: 'float' },
      { field: 'createdAt', type: 'datetime' },
      { field: 'lastAccessed', type: 'datetime' },
    ];

    for (const { field, type } of indexFields) {
      try {
        await this.client.createPayloadIndex(this.collectionName, {
          field_name: field,
          field_schema: type as any,
        });
      } catch (error) {
        // Index might already exist, continue
        console.log(`Index for ${field} might already exist, continuing...`);
      }
    }
  }

  async search(query: any): Promise<any> {
    // Simple search implementation that returns all memories (with optional limit)
    const limit = query.limit || 100;
    const memories = await this.getAllMemories(limit);

    return {
      memories,
      totalCount: memories.length
    };
  }

  async store(memory: Memory): Promise<void> {
    if (!memory.metadata.embedding) {
      // Generate embedding if memory should have one
      if (this.shouldGenerateEmbedding(memory)) {
        const embedding = await this.embeddingService.generateEmbedding(
          this.extractTextContent(memory.content)
        );
        memory.metadata.embedding = embedding;
      } else {
        // Skip storing memories that don't need embeddings in vector store
        console.warn(`Skipping vector storage for memory ${memory.metadata.id} - no embedding required`);
        return;
      }
    }

    const point = this.memoryToPoint(memory);
    try {
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: [point],
      });
    } catch (error) {
      if (this.isVectorDimensionError(error)) {
        console.warn('Detected vector dimension mismatch when storing memory, attempting automatic recovery...');
        await this.handleVectorDimensionMismatch();
        await this.ensureEmbeddingMatchesCurrentDimension(memory);
        const retryPoint = this.memoryToPoint(memory);
        await this.client.upsert(this.collectionName, {
          wait: true,
          points: [retryPoint],
        });
      } else {
        throw error;
      }
    }
  }

  async bulkStore(memories: Memory[]): Promise<void> {
    const points = memories
      .filter(m => m.metadata.embedding)
      .map(m => this.memoryToPoint(m));

    if (points.length === 0) return;

    // Process in batches of 100 for optimal performance
    const batchSize = 100;
    for (let i = 0; i < points.length; i += batchSize) {
      const batch = points.slice(i, i + batchSize);
      try {
        await this.client.upsert(this.collectionName, {
          wait: true,
          points: batch,
        });
      } catch (error) {
        if (this.isVectorDimensionError(error)) {
          console.warn('Detected vector dimension mismatch during bulk store, attempting automatic recovery...');
          await this.handleVectorDimensionMismatch();
          for (const memory of memories) {
            await this.ensureEmbeddingMatchesCurrentDimension(memory);
          }
          const retryPoints = memories
            .filter(m => m.metadata.embedding)
            .map(m => this.memoryToPoint(m));
          await this.upsertPointsInBatches(retryPoints);
          break;
        }
        throw error;
      }
    }
  }

  private async ensureCollection(): Promise<void> {
    const desiredDimension = await this.determineDesiredDimension();
    const collections = await this.client.getCollections();
    const collectionExists = collections.collections.some(
      c => c.name === this.collectionName
    );

    if (!collectionExists) {
      await this.createCollectionWithDimension(desiredDimension);
      await this.createPayloadIndexes();
      this.dimension = desiredDimension;
      return;
    }

    const collectionInfo = await this.client.getCollection(this.collectionName);
    const currentDimension = collectionInfo?.config?.params?.vectors?.size;

    if (typeof currentDimension === 'number') {
      this.dimension = currentDimension;
    }

    if (typeof currentDimension === 'number' && currentDimension !== desiredDimension) {
      console.warn(
        `Qdrant collection ${this.collectionName} dimension mismatch detected (current: ${currentDimension}, desired: ${desiredDimension}). Rebuilding collection...`
      );
      await this.recreateCollection(desiredDimension);
    }
  }

  private async determineDesiredDimension(): Promise<number> {
    const dimensionFromService = this.embeddingService.getEmbeddingDimensions();
    if (Number.isFinite(dimensionFromService) && dimensionFromService > 0) {
      return Math.floor(dimensionFromService);
    }

    const probeEmbedding = await this.embeddingService.generateEmbedding('dimension-probe');
    if (probeEmbedding.length > 0) {
      return probeEmbedding.length;
    }

    return this.dimension || 1536;
  }

  private isVectorDimensionError(error: unknown): boolean {
    if (!error) return false;
    const message = error instanceof Error ? error.message : String(error);
    return message.includes('Vector dimension error');
  }

  private async handleVectorDimensionMismatch(): Promise<void> {
    if (this.rebuildPromise) {
      await this.rebuildPromise;
      return;
    }

    this.rebuildPromise = (async () => {
      const desiredDimension = await this.determineDesiredDimension();
      const collectionInfo = await this.safeGetCollection();
      const currentDimension = collectionInfo?.config?.params?.vectors?.size;

      if (typeof currentDimension === 'number' && currentDimension === desiredDimension) {
        // Collection might be corrupted; rebuild to ensure consistency
        await this.recreateCollection(desiredDimension);
        return;
      }

      await this.recreateCollection(desiredDimension);
    })();

    try {
      await this.rebuildPromise;
    } finally {
      this.rebuildPromise = undefined;
    }
  }

  private async safeGetCollection(): Promise<any | null> {
    try {
      return await this.client.getCollection(this.collectionName);
    } catch (error) {
      console.warn(`Failed to fetch collection info for ${this.collectionName}:`, error);
      return null;
    }
  }

  private async recreateCollection(targetDimension: number): Promise<void> {
    const existingMemories = await this.loadStoredMemories();

    try {
      await this.client.deleteCollection(this.collectionName);
    } catch (error) {
      console.error(`Failed to delete Qdrant collection ${this.collectionName}:`, error);
      throw error;
    }

    await this.createCollectionWithDimension(targetDimension);
    await this.createPayloadIndexes();
    this.dimension = targetDimension;

    if (existingMemories.length === 0) {
      console.log(`Qdrant collection ${this.collectionName} rebuilt with no existing memories to restore.`);
      return;
    }

    const restoredPoints: any[] = [];
    let failedCount = 0;

    for (const memory of existingMemories) {
      if (!this.shouldGenerateEmbedding(memory)) {
        continue;
      }

      try {
        const embedding = await this.embeddingService.generateEmbedding(
          this.extractTextContent(memory.content)
        );
        memory.metadata.embedding = embedding;
        restoredPoints.push(this.memoryToPoint(memory));
      } catch (error) {
        failedCount += 1;
        console.warn(`Failed to re-embed memory ${memory.metadata.id}:`, error);
      }
    }

    if (restoredPoints.length > 0) {
      await this.upsertPointsInBatches(restoredPoints);
    }

    const preserved = restoredPoints.length;
    const skipped = existingMemories.length - preserved - failedCount;
    console.log(
      `Rebuilt Qdrant collection ${this.collectionName}. Restored ${preserved} memories (${failedCount} failed re-embed, ${skipped} skipped).`
    );
  }

  private async loadStoredMemories(): Promise<Memory[]> {
    const memories: Memory[] = [];
    let offset: any = null;

    try {
      while (true) {
        const response = await this.client.scroll(this.collectionName, {
          limit: 100,
          offset,
          with_payload: true,
          with_vector: false,
        });

        if (!response.points || response.points.length === 0) break;

        for (const point of response.points) {
          try {
            memories.push(this.pointToMemory(point));
          } catch (error) {
            console.warn('Failed to parse memory during collection rebuild:', error);
          }
        }

        offset = response.next_page_offset;
        if (!offset) break;
      }
    } catch (error) {
      console.warn(`Failed to load existing memories from ${this.collectionName}:`, error);
    }

    return memories;
  }

  private async createCollectionWithDimension(dimension: number): Promise<void> {
    await this.client.createCollection(this.collectionName, {
      vectors: {
        size: dimension,
        distance: 'Cosine',
      },
      optimizers_config: {
        deleted_threshold: 0.2,
        vacuum_min_vector_number: 1000,
        default_segment_number: 2,
        memmap_threshold: 50000,
        indexing_threshold: 20000,
      },
      hnsw_config: {
        m: 16,
        ef_construct: 100,
        full_scan_threshold: 10000,
      },
    });
  }

  private async upsertPointsInBatches(points: any[]): Promise<void> {
    if (points.length === 0) return;

    const batchSize = 100;
    for (let i = 0; i < points.length; i += batchSize) {
      const batch = points.slice(i, i + batchSize);
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: batch,
      });
    }
  }

  private async ensureEmbeddingMatchesCurrentDimension(memory: Memory): Promise<void> {
    if (!this.shouldGenerateEmbedding(memory)) {
      memory.metadata.embedding = undefined;
      return;
    }

    if (!memory.metadata.embedding || memory.metadata.embedding.length !== this.dimension) {
      const text = this.extractTextContent(memory.content);
      for (let attempt = 0; attempt < 2; attempt++) {
        const embedding = await this.embeddingService.generateEmbedding(text);
        memory.metadata.embedding = embedding;

        if (embedding.length === this.dimension) {
          return;
        }

        console.warn(
          `Embedding dimension ${embedding.length} differs from collection dimension ${this.dimension}. Attempting to realign...`
        );
        await this.handleVectorDimensionMismatch();
      }

      throw new Error(
        `Unable to align embedding dimension with collection dimension for memory ${memory.metadata.id}`
      );
    }
  }

  async retrieve(memoryId: string): Promise<Memory | null> {
    try {
      const numericId = this.stringToNumericId(memoryId);
      const response = await this.client.retrieve(this.collectionName, {
        ids: [numericId],
        with_payload: true,
        with_vector: false,
      });

      if (response.length === 0) return null;

      return this.pointToMemory(response[0]);
    } catch (error) {
      console.error(`Failed to retrieve memory ${memoryId}:`, error);
      return null;
    }
  }

  async update(memory: Memory): Promise<void> {
    // Upsert handles both insert and update
    await this.store(memory);
  }

  async delete(memoryId: string): Promise<void> {
    const numericId = this.stringToNumericId(memoryId);
    await this.client.delete(this.collectionName, {
      wait: true,
      points: [numericId],
    });
  }

  async bulkDelete(memoryIds: string[]): Promise<void> {
    if (memoryIds.length === 0) return;

    const numericIds = memoryIds.map(id => this.stringToNumericId(id));
    await this.client.delete(this.collectionName, {
      wait: true,
      points: numericIds,
    });
  }

  async similaritySearch(
    queryEmbedding: number[],
    topK: number = 10,
    threshold: number = 0.7
  ): Promise<VectorSearchResult> {
    const searchParams: any = {
      vector: queryEmbedding,
      limit: topK,
      with_payload: true,
    };

    // Only add score_threshold if it's reasonable (cosine similarity can be low for valid matches)
    if (threshold && threshold < 0.5) {
      searchParams.score_threshold = threshold;
    }

    const response = await this.client.search(this.collectionName, searchParams);

    const memories = response.map(result => this.searchResultToMemory(result));
    const scores = response.map(result => result.score || 0);

    return { memories, scores };
  }

  async hybridSearch(
    textQuery: string,
    filters?: {
      type?: string;
      tags?: string[];
      minConfidence?: number;
      projectId?: string;
      userId?: string;
    },
    topK: number = 10
  ): Promise<VectorSearchResult> {
    // Generate embedding for text query
    const queryEmbedding = await this.embeddingService.generateEmbedding(textQuery);

    // Build filter conditions
    const filterConditions: any = { must: [] };

    if (filters) {
      if (filters.type) {
        filterConditions.must.push({
          key: 'type',
          match: { value: filters.type },
        });
      }

      if (filters.tags && filters.tags.length > 0) {
        filterConditions.must.push({
          key: 'tags',
          match: { any: filters.tags },
        });
      }

      if (filters.minConfidence !== undefined) {
        filterConditions.must.push({
          key: 'confidence',
          range: { gte: filters.minConfidence },
        });
      }

      if (filters.projectId) {
        filterConditions.must.push({
          key: 'projectId',
          match: { value: filters.projectId },
        });
      }

      if (filters.userId) {
        filterConditions.must.push({
          key: 'userId',
          match: { value: filters.userId },
        });
      }
    }

    const searchParams: any = {
      vector: queryEmbedding,
      limit: topK,
      with_payload: true,
    };

    // Only add filter if there are conditions
    if (filterConditions.must.length > 0) {
      searchParams.filter = filterConditions;
    }

    const response = await this.client.search(this.collectionName, searchParams);

    const memories = response.map(result => this.searchResultToMemory(result));
    const scores = response.map(result => result.score || 0);

    return { memories, scores };
  }

  async findRelatedMemories(
    memoryId: string,
    topK: number = 5,
    threshold: number = 0.8
  ): Promise<Memory[]> {
    // First retrieve the memory to get its embedding
    const memory = await this.retrieve(memoryId);
    if (!memory || !memory.metadata.embedding) return [];

    // Search for similar memories, excluding the original
    const response = await this.client.search(this.collectionName, {
      vector: memory.metadata.embedding,
      limit: topK + 1, // +1 to account for the original memory
      score_threshold: threshold,
      with_payload: true,
      filter: {
        must_not: [
          {
            key: 'id',
            match: { value: memoryId },  // Using string ID from payload
          },
        ],
      },
    });

    return response
      .slice(0, topK)
      .map(result => this.searchResultToMemory(result));
  }

  async clusterMemories(k: number = 5): Promise<Map<number, Memory[]>> {
    // Retrieve all memories with embeddings
    const allMemories = await this.getAllMemories();

    if (allMemories.length === 0) return new Map();

    // Extract embeddings
    const embeddings = allMemories
      .filter(m => m.metadata.embedding)
      .map(m => m.metadata.embedding!);

    // Perform clustering using embedding service
    const assignments = await this.embeddingService.clusterEmbeddings(embeddings, k);

    // Group memories by cluster
    const clusters = new Map<number, Memory[]>();

    for (let i = 0; i < assignments.length; i++) {
      const clusterId = assignments[i];
      if (!clusters.has(clusterId)) {
        clusters.set(clusterId, []);
      }
      clusters.get(clusterId)!.push(allMemories[i]);
    }

    return clusters;
  }

  async getStats(): Promise<{
    totalVectors: number;
    collections?: string[];
    indexInfo?: any;
  }> {
    const collectionInfo = await this.client.getCollection(this.collectionName);
    const collections = await this.client.getCollections();

    return {
      totalVectors: collectionInfo.points_count || 0,
      collections: collections.collections.map(c => c.name),
      indexInfo: {
        vectorsCount: collectionInfo.vectors_count,
        indexedVectorsCount: collectionInfo.indexed_vectors_count,
        pointsCount: collectionInfo.points_count,
        segmentsCount: collectionInfo.segments_count,
        status: collectionInfo.status,
        config: collectionInfo.config,
      },
    };
  }

  async reindex(): Promise<void> {
    // Trigger optimization of the collection
    await this.client.updateCollection(this.collectionName, {
      optimizers_config: {
        indexing_threshold: 10000,
      },
    });

    console.log(`Reindexing triggered for collection: ${this.collectionName}`);
  }

  async createSnapshot(): Promise<string> {
    const response = await this.client.createSnapshot(this.collectionName);
    return response?.name || `snapshot_${Date.now()}`;
  }

  async restoreSnapshot(snapshotId: string): Promise<void> {
    // Note: Snapshot restoration typically requires server-side operations
    // This is a placeholder for the actual implementation
    console.log(`Restoring snapshot: ${snapshotId}`);
    // Implementation would depend on Qdrant server configuration
  }

  async destroy(): Promise<void> {
    // Optionally delete the collection
    // await this.client.deleteCollection(this.collectionName);
    this.initialized = false;
  }


  // Helper methods

  private memoryToPoint(memory: Memory): any {
    // Generate a numeric ID for Qdrant (it prefers numeric IDs)
    const numericId = this.stringToNumericId(memory.metadata.id);

    return {
      id: numericId,
      vector: memory.metadata.embedding,
      payload: {
        // Metadata fields
        id: memory.metadata.id,  // Keep string ID in payload
        type: memory.metadata.type,
        priority: memory.metadata.priority,
        createdAt: memory.metadata.createdAt.toISOString(),
        lastAccessed: memory.metadata.lastAccessed.toISOString(),
        accessCount: memory.metadata.accessCount,
        confidence: memory.metadata.confidence,
        source: memory.metadata.source,
        tags: memory.metadata.tags,
        projectId: memory.metadata.projectId || '',  // Use empty string instead of null
        userId: memory.metadata.userId || '',  // Use empty string instead of null
        ttl: memory.metadata.ttl || 0,  // Use 0 instead of null

        // Content (serialized)
        content: JSON.stringify(memory.content),

        // Related memories
        related: memory.related || [],
      },
    };
  }

  private stringToNumericId(str: string): number {
    // Convert string ID to a numeric hash
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private pointToMemory(point: any): Memory {
    const payload = point.payload;

    return {
      metadata: {
        id: payload.id,
        type: payload.type as MemoryType,
        priority: payload.priority as MemoryPriority,
        createdAt: new Date(payload.createdAt),
        lastAccessed: new Date(payload.lastAccessed),
        accessCount: payload.accessCount,
        confidence: payload.confidence,
        source: payload.source,
        tags: payload.tags,
        projectId: payload.projectId,
        userId: payload.userId,
        ttl: payload.ttl,
        embedding: point.vector,
      },
      content: JSON.parse(payload.content),
      related: payload.related || [],
    };
  }

  private searchResultToMemory(result: any): Memory {
    return this.pointToMemory(result);
  }

  async getAllMemories(limit: number = 10000): Promise<Memory[]> {
    const memories: Memory[] = [];
    let offset: any = null;

    // Paginate through all memories
    while (true) {
      const response = await this.client.scroll(this.collectionName, {
        limit: 100,
        offset,
        with_payload: true,
        with_vector: true,
      });

      if (!response.points || response.points.length === 0) break;

      memories.push(...response.points.map(p => this.pointToMemory(p)));

      if (memories.length >= limit) break;

      offset = response.next_page_offset;
      if (!offset) break;
    }

    return memories.slice(0, limit);
  }

  private shouldGenerateEmbedding(memory: Memory): boolean {
    return (
      memory.metadata.type === MemoryType.SEMANTIC ||
      memory.metadata.type === MemoryType.PROCEDURAL ||
      memory.metadata.tags?.includes('searchable')
    );
  }

  private extractTextContent(content: any): string {
    if (typeof content === 'string') return content;
    if (typeof content === 'object') {
      return JSON.stringify(content);
    }
    return String(content);
  }
}
