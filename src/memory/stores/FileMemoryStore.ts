// File system operations - only available in Node.js environment
let fs: any = null;
let path: any = null;

// Check if we're in Node.js environment
if (typeof window === 'undefined') {
  try {
    fs = require('fs/promises');
    path = require('path');
  } catch (e) {
    console.warn('File system not available - FileMemoryStore will use fallback');
  }
}
import { Memory, MemoryQuery, MemorySearchResult } from '../types';

export class FileMemoryStore {
  private readonly MEMORY_DIR = '.dante';
  private readonly MEMORY_SUBDIR = 'memory';
  private readonly MEMORY_FILE = 'memories.json';
  private readonly MEMORY_LOG_FILE = 'memories.jsonl'; // Line-delimited JSON for efficient appends
  private memoryCache: Map<string, Memory[]>;

  constructor() {
    this.memoryCache = new Map();
  }

  async initialize(): Promise<void> {
    // Only load memories in Node.js environment
    if (typeof window === 'undefined' && fs) {
      await this.loadAllProjectMemories();
    }
  }

  async store(memory: Memory): Promise<void> {
    if (!memory.metadata.projectId) {
      throw new Error('Memory must have projectId for file storage');
    }

    const projectPath = memory.metadata.projectId;
    
    // Update cache
    let memories = this.memoryCache.get(projectPath) || [];
    const index = memories.findIndex(m => m.metadata.id === memory.metadata.id);
    if (index >= 0) {
      memories[index] = memory;
    } else {
      memories.push(memory);
    }
    this.memoryCache.set(projectPath, memories);
    
    // Append to log file for efficiency
    await this.appendToLog(projectPath, memory);
    
    // Periodically compact the log into the main file
    if (memories.length % 100 === 0) {
      await this.compactStorage(projectPath);
    }
  }

  async retrieve(memoryId: string): Promise<Memory | null> {
    for (const [_, memories] of this.memoryCache) {
      const memory = memories.find(m => m.metadata.id === memoryId);
      if (memory) return memory;
    }

    // If not in cache, search file system
    await this.loadAllProjectMemories();
    
    for (const [_, memories] of this.memoryCache) {
      const memory = memories.find(m => m.metadata.id === memoryId);
      if (memory) return memory;
    }

    return null;
  }

  async update(memory: Memory): Promise<void> {
    return this.store(memory);
  }

  async delete(memoryId: string): Promise<void> {
    for (const [projectPath, memories] of this.memoryCache) {
      const index = memories.findIndex(m => m.metadata.id === memoryId);
      if (index >= 0) {
        memories.splice(index, 1);
        await this.saveProjectMemories(projectPath, memories);
        return;
      }
    }
  }

  async bulkDelete(memoryIds: string[]): Promise<void> {
    const memoryIdSet = new Set(memoryIds);
    const projectsToSave = new Map<string, Memory[]>();
    
    // Remove memories from cache and track which projects need saving
    for (const [projectPath, memories] of this.memoryCache) {
      const initialLength = memories.length;
      const filteredMemories = memories.filter(m => !memoryIdSet.has(m.metadata.id));
      
      if (filteredMemories.length < initialLength) {
        // Some memories were deleted from this project
        this.memoryCache.set(projectPath, filteredMemories);
        projectsToSave.set(projectPath, filteredMemories);
      }
    }
    
    // Save all affected projects in parallel
    await Promise.all(
      Array.from(projectsToSave.entries()).map(([projectPath, memories]) =>
        this.saveProjectMemories(projectPath, memories)
      )
    );
  }

  async search(query: MemoryQuery): Promise<MemorySearchResult> {
    const allMemories: Memory[] = [];
    
    if (query.projectId) {
      // Search specific project
      const memories = await this.loadProjectMemories(query.projectId);
      allMemories.push(...memories);
    } else {
      // Search all projects
      for (const [_, memories] of this.memoryCache) {
        allMemories.push(...memories);
      }
    }

    // Apply filters
    let filtered = this.applyFilters(allMemories, query);
    
    // Apply limit
    if (query.limit) {
      filtered = filtered.slice(0, query.limit);
    }

    return {
      memories: filtered,
      totalCount: filtered.length
    };
  }

  async getProjectMemoryPath(projectPath: string): Promise<string> {
    return path.join(projectPath, this.MEMORY_DIR, this.MEMORY_SUBDIR, this.MEMORY_FILE);
  }

  private async loadProjectMemories(projectPath: string): Promise<Memory[]> {
    // Check cache first
    if (this.memoryCache.has(projectPath)) {
      return this.memoryCache.get(projectPath)!;
    }

    const memoryPath = await this.getProjectMemoryPath(projectPath);
    
    try {
      const data = await fs.readFile(memoryPath, 'utf-8');
      const memories = JSON.parse(data) as Memory[];
      
      // Restore Date objects if present
      memories.forEach(memory => {
        if (memory.metadata) {
          if (memory.metadata.createdAt) {
            memory.metadata.createdAt = new Date(memory.metadata.createdAt);
          } else {
            memory.metadata.createdAt = new Date();
          }
          if (memory.metadata.lastAccessed) {
            memory.metadata.lastAccessed = new Date(memory.metadata.lastAccessed);
          } else {
            memory.metadata.lastAccessed = new Date();
          }
        }
      });

      this.memoryCache.set(projectPath, memories);
      return memories;
    } catch (error) {
      // File doesn't exist yet
      if ((error as any).code === 'ENOENT') {
        this.memoryCache.set(projectPath, []);
        return [];
      }
      throw error;
    }
  }

  private async saveProjectMemories(projectPath: string, memories: Memory[]): Promise<void> {
    const memoryPath = await this.getProjectMemoryPath(projectPath);
    const memoryDir = path.dirname(memoryPath);

    // Ensure directory exists
    await fs.mkdir(memoryDir, { recursive: true });

    // Save memories
    await fs.writeFile(memoryPath, JSON.stringify(memories, null, 2));

    // Update cache
    this.memoryCache.set(projectPath, memories);

    // Also create a backup
    await this.createBackup(projectPath, memories);
  }

  private async createBackup(projectPath: string, memories: Memory[]): Promise<void> {
    const backupDir = path.join(
      projectPath,
      this.MEMORY_DIR,
      this.MEMORY_SUBDIR,
      'backups'
    );
    
    await fs.mkdir(backupDir, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `memories-${timestamp}.json`);
    
    await fs.writeFile(backupPath, JSON.stringify(memories, null, 2));

    // Clean old backups (keep last 10)
    await this.cleanOldBackups(backupDir, 10);
  }

  private async cleanOldBackups(backupDir: string, keepCount: number): Promise<void> {
    try {
      const files = await fs.readdir(backupDir);
      const backupFiles = files
        .filter((f: string) => f.startsWith('memories-') && f.endsWith('.json'))
        .sort()
        .reverse();

      if (backupFiles.length > keepCount) {
        const toDelete = backupFiles.slice(keepCount);
        for (const file of toDelete) {
          await fs.unlink(path.join(backupDir, file));
        }
      }
    } catch (error) {
      console.error('Failed to clean old backups:', error);
    }
  }

  private async loadAllProjectMemories(): Promise<void> {
    // Traverse up the directory tree looking for .dante folders
    if (!fs) return; // Skip if not in Node.js environment
    
    try {
      const cwd = typeof process !== 'undefined' ? process.cwd() : '/';
      await this.traverseAndLoadMemories(cwd);
    } catch (error) {
      console.error('Error loading project memories:', error);
    }
  }

  private async traverseAndLoadMemories(startPath: string): Promise<void> {
    const visited = new Set<string>();
    let currentPath = startPath;
    
    // Traverse up the directory tree until we reach the root
    while (currentPath && !visited.has(currentPath)) {
      visited.add(currentPath);
      
      // Check if this directory has a .dante memory store
      const memoryPath = path.join(currentPath, this.MEMORY_DIR, this.MEMORY_SUBDIR, this.MEMORY_FILE);
      
      try {
        await fs.access(memoryPath);
        // Found a .dante directory with memories
        if (!this.memoryCache.has(currentPath)) {
          await this.loadProjectMemories(currentPath);
        }
      } catch {
        // No .dante directory here, continue traversing
      }
      
      // Move up to parent directory
      const parentPath = path.dirname(currentPath);
      
      // Stop if we've reached the root or can't go up further
      if (parentPath === currentPath || parentPath === '/' || parentPath === '.') {
        break;
      }
      
      currentPath = parentPath;
    }
    
    // Also check for workspace roots (git repositories)
    await this.findWorkspaceRoots(startPath);
  }

  private async findWorkspaceRoots(startPath: string): Promise<void> {
    // Look for common workspace indicators
    const workspaceIndicators = ['.git', 'package.json', 'tsconfig.json', 'Cargo.toml', 'go.mod'];
    
    try {
      // Check if current directory is a workspace root
      for (const indicator of workspaceIndicators) {
        const indicatorPath = path.join(startPath, indicator);
        try {
          await fs.access(indicatorPath);
          // Found a workspace root, check for .dante directory
          const memoryPath = path.join(startPath, this.MEMORY_DIR, this.MEMORY_SUBDIR, this.MEMORY_FILE);
          try {
            await fs.access(memoryPath);
            if (!this.memoryCache.has(startPath)) {
              await this.loadProjectMemories(startPath);
            }
          } catch {
            // No .dante directory in this workspace root
          }
          break; // Found a workspace indicator, no need to check others
        } catch {
          // Indicator not found, continue checking
        }
      }
    } catch (error) {
      // Ignore errors when checking for workspace roots
    }
  }

  private applyFilters(memories: Memory[], query: MemoryQuery): Memory[] {
    return memories.filter(memory => {
      const meta = memory.metadata || {} as any;
      if (query.type && meta.type !== query.type) return false;
      
      if (query.tags && query.tags.length > 0) {
        const memTags = Array.isArray(meta.tags) ? meta.tags : [];
        const hasAllTags = query.tags.every(tag => memTags.includes(tag));
        if (!hasAllTags) return false;
      }

      if (query.minConfidence !== undefined) {
        const conf = typeof meta.confidence === 'number' ? meta.confidence : 0;
        if (conf < query.minConfidence) return false;
      }

      if (query.minPriority !== undefined) {
        const pr = typeof meta.priority === 'number' ? meta.priority : 0;
        if (pr < query.minPriority) return false;
      }

      return true;
    });
  }

  async exportProject(projectPath: string): Promise<string> {
    const memories = await this.loadProjectMemories(projectPath);
    return JSON.stringify({
      project: projectPath,
      exportDate: new Date().toISOString(),
      memories
    }, null, 2);
  }

  async importProject(projectPath: string, data: string): Promise<void> {
    const imported = JSON.parse(data);
    const memories = imported.memories as Memory[];
    
    // Restore dates
    memories.forEach(memory => {
      memory.metadata.createdAt = new Date(memory.metadata.createdAt);
      memory.metadata.lastAccessed = new Date(memory.metadata.lastAccessed);
      memory.metadata.projectId = projectPath; // Update project ID
    });

    await this.saveProjectMemories(projectPath, memories);
  }

  async getProjectStats(projectPath: string): Promise<{
    totalMemories: number;
    oldestMemory?: Date;
    newestMemory?: Date;
    totalSize: number;
  }> {
    const memories = await this.loadProjectMemories(projectPath);
    
    if (memories.length === 0) {
      return { totalMemories: 0, totalSize: 0 };
    }

    const dates = memories.map(m => m.metadata.createdAt.getTime());
    const json = JSON.stringify(memories);

    return {
      totalMemories: memories.length,
      oldestMemory: new Date(Math.min(...dates)),
      newestMemory: new Date(Math.max(...dates)),
      totalSize: json.length
    };
  }

  private async appendToLog(projectPath: string, memory: Memory): Promise<void> {
    if (!fs) return;
    
    const logPath = path.join(
      projectPath,
      this.MEMORY_DIR,
      this.MEMORY_SUBDIR,
      this.MEMORY_LOG_FILE
    );
    const logDir = path.dirname(logPath);
    
    await fs.mkdir(logDir, { recursive: true });
    
    // Append as line-delimited JSON
    const line = JSON.stringify({ ...memory, operation: 'upsert' }) + '\n';
    await fs.appendFile(logPath, line);
  }
  
  private async compactStorage(projectPath: string): Promise<void> {
    // Merge log file into main storage file
    const memories = this.memoryCache.get(projectPath) || [];
    await this.saveProjectMemories(projectPath, memories);
    
    // Clear the log file after compaction
    if (fs) {
      const logPath = path.join(
        projectPath,
        this.MEMORY_DIR,
        this.MEMORY_SUBDIR,
        this.MEMORY_LOG_FILE
      );
      try {
        await fs.unlink(logPath);
      } catch {
        // Log file might not exist
      }
    }
  }

  async destroy(): Promise<void> {
    // Compact all storages before destroying
    for (const [projectPath] of this.memoryCache) {
      await this.compactStorage(projectPath);
    }
    this.memoryCache.clear();
  }
}