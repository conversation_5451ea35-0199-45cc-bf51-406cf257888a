import { Memory } from '../types';
import { EmbeddingService } from '../services/EmbeddingService';

interface VectorRecord {
  id: string;
  embedding: number[];
  memory: Memory;
}

interface SimilaritySearchResult {
  memories: Memory[];
  scores: number[];
}

// HNSW (Hierarchical Navigable Small World) index for efficient similarity search
class HNSWIndex {
  private layers: Map<string, Set<string>>[];  // Graph layers
  private entryPoint: string | null = null;
  private M = 16;  // Maximum number of connections
  private efConstruction = 200;  // Size of dynamic candidate list
  private mL = 1.0 / Math.log(2.0);  // Normalization factor for level assignment
  
  constructor() {
    this.layers = [];
  }

  insert(id: string, embedding: number[], allVectors: Map<string, VectorRecord>, embeddingService: EmbeddingService): void {
    const level = this.getRandomLevel();
    
    // Ensure we have enough layers
    while (this.layers.length <= level) {
      this.layers.push(new Map());
    }
    
    // Add node to all layers up to its level
    for (let lc = 0; lc <= level; lc++) {
      if (!this.layers[lc].has(id)) {
        this.layers[lc].set(id, new Set());
      }
    }
    
    if (this.entryPoint === null) {
      this.entryPoint = id;
      return;
    }
    
    // Find nearest neighbors at all layers
    const W = new Set<string>();
    W.add(this.entryPoint);
    
    for (let lc = this.layers.length - 1; lc >= 0; lc--) {
      const candidates = this.searchLayer(embedding, W, 1, lc, allVectors, embeddingService);
      
      const M = lc === 0 ? this.M * 2 : this.M;
      const neighbors = this.getNeighbors(embedding, candidates, M, allVectors, embeddingService);
      
      // Add bidirectional connections
      for (const neighbor of neighbors) {
        this.layers[lc].get(id)?.add(neighbor);
        this.layers[lc].get(neighbor)?.add(id);
        
        // Prune connections of neighbor if needed
        const neighborConnections = this.layers[lc].get(neighbor);
        if (neighborConnections && neighborConnections.size > M) {
          const pruned = this.prune(neighbor, neighborConnections, M, lc, allVectors, embeddingService);
          this.layers[lc].set(neighbor, pruned);
        }
      }
      
      if (lc > level) {
        W.clear();
        for (const neighbor of neighbors) {
          W.add(neighbor);
        }
      }
    }
  }
  
  search(queryEmbedding: number[], k: number, ef: number, allVectors: Map<string, VectorRecord>, embeddingService: EmbeddingService): string[] {
    if (!this.entryPoint) return [];
    
    const W = new Set<string>();
    W.add(this.entryPoint);
    
    // Search from top layer to layer 0
    for (let lc = this.layers.length - 1; lc > 0; lc--) {
      const nearest = this.searchLayer(queryEmbedding, W, 1, lc, allVectors, embeddingService);
      W.clear();
      for (const node of nearest) {
        W.add(node);
      }
    }
    
    // Search at layer 0 with ef parameter
    return this.searchLayer(queryEmbedding, W, Math.max(ef, k), 0, allVectors, embeddingService).slice(0, k);
  }
  
  private searchLayer(
    queryEmbedding: number[], 
    entryPoints: Set<string>, 
    numClosest: number, 
    layer: number,
    allVectors: Map<string, VectorRecord>,
    embeddingService: EmbeddingService
  ): string[] {
    const visited = new Set<string>();
    const candidates = new Map<string, number>();
    const W = new Map<string, number>();
    
    // Initialize with entry points
    for (const point of entryPoints) {
      const dist = this.distance(queryEmbedding, allVectors.get(point)!.embedding, embeddingService);
      candidates.set(point, dist);
      W.set(point, dist);
      visited.add(point);
    }
    
    while (candidates.size > 0) {
      // Get closest candidate
      let closest: string | null = null;
      let closestDist = Infinity;
      for (const [id, dist] of candidates) {
        if (dist < closestDist) {
          closest = id;
          closestDist = dist;
        }
      }
      
      if (!closest) break;
      candidates.delete(closest);
      
      // Check if we should stop
      const furthestW = Math.max(...Array.from(W.values()));
      if (closestDist > furthestW) break;
      
      // Check neighbors
      const neighbors = this.layers[layer]?.get(closest) || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          visited.add(neighbor);
          const dist = this.distance(queryEmbedding, allVectors.get(neighbor)!.embedding, embeddingService);
          
          const furthest = Math.max(...Array.from(W.values()));
          if (dist < furthest || W.size < numClosest) {
            candidates.set(neighbor, dist);
            W.set(neighbor, dist);
            
            // Remove furthest if we have too many
            if (W.size > numClosest) {
              let furthestId = '';
              let furthestDist = -Infinity;
              for (const [id, d] of W) {
                if (d > furthestDist) {
                  furthestId = id;
                  furthestDist = d;
                }
              }
              W.delete(furthestId);
            }
          }
        }
      }
    }
    
    // Sort by distance and return
    return Array.from(W.entries())
      .sort((a, b) => a[1] - b[1])
      .map(([id]) => id);
  }
  
  private getNeighbors(
    embedding: number[], 
    candidates: string[], 
    M: number,
    allVectors: Map<string, VectorRecord>,
    embeddingService: EmbeddingService
  ): Set<string> {
    const distances = candidates.map(id => ({
      id,
      dist: this.distance(embedding, allVectors.get(id)!.embedding, embeddingService)
    }));
    
    distances.sort((a, b) => a.dist - b.dist);
    
    return new Set(distances.slice(0, M).map(d => d.id));
  }
  
  private prune(
    nodeId: string,
    connections: Set<string>,
    M: number,
    layer: number,
    allVectors: Map<string, VectorRecord>,
    embeddingService: EmbeddingService
  ): Set<string> {
    const nodeEmbedding = allVectors.get(nodeId)!.embedding;
    const distances = Array.from(connections).map(id => ({
      id,
      dist: this.distance(nodeEmbedding, allVectors.get(id)!.embedding, embeddingService)
    }));
    
    distances.sort((a, b) => a.dist - b.dist);
    
    return new Set(distances.slice(0, M).map(d => d.id));
  }
  
  private distance(a: number[], b: number[], embeddingService: EmbeddingService): number {
    // Use 1 - cosine similarity as distance
    return 1 - embeddingService.cosineSimilarity(a, b);
  }
  
  private getRandomLevel(): number {
    let level = 0;
    while (Math.random() < 0.5 && level < 16) {
      level++;
    }
    return level;
  }
  
  clear(): void {
    this.layers = [];
    this.entryPoint = null;
  }
}

export class VectorMemoryStore {
  private vectors: Map<string, VectorRecord>;
  private embeddingService: EmbeddingService;
  private indexReady: boolean;
  private hnswIndex: HNSWIndex;
  private indexNeedsRebuild: boolean;

  constructor() {
    this.vectors = new Map();
    this.embeddingService = new EmbeddingService();
    this.indexReady = false;
    this.hnswIndex = new HNSWIndex();
    this.indexNeedsRebuild = false;
  }

  async search(query: any): Promise<any> {
    // Simple search implementation that returns all memories (with optional limit)
    const limit = query.limit || 100;
    const memories = Array.from(this.vectors.values())
      .slice(0, limit)
      .map(record => record.memory);
    
    return {
      memories,
      totalCount: this.vectors.size
    };
  }

  async initialize(): Promise<void> {
    // Load persisted vectors if available
    await this.loadPersistedVectors();
    
    // Build HNSW index
    if (this.vectors.size > 0) {
      await this.rebuildIndex();
    }
    
    this.indexReady = true;
  }

  async store(memory: Memory): Promise<void> {
    if (!memory.metadata.embedding) {
      throw new Error('Memory must have embedding for vector storage');
    }

    const record: VectorRecord = {
      id: memory.metadata.id,
      embedding: memory.metadata.embedding,
      memory: memory
    };

    this.vectors.set(memory.metadata.id, record);
    
    // Add to HNSW index
    if (this.indexReady) {
      this.hnswIndex.insert(memory.metadata.id, memory.metadata.embedding, this.vectors, this.embeddingService);
    } else {
      this.indexNeedsRebuild = true;
    }
    
    await this.persistVectors();
  }

  async retrieve(memoryId: string): Promise<Memory | null> {
    const record = this.vectors.get(memoryId);
    return record ? record.memory : null;
  }

  async update(memory: Memory): Promise<void> {
    return this.store(memory);
  }

  async delete(memoryId: string): Promise<void> {
    this.vectors.delete(memoryId);
    
    // Mark index for rebuild (HNSW doesn't support efficient deletion)
    this.indexNeedsRebuild = true;
    
    await this.persistVectors();
  }

  async bulkDelete(memoryIds: string[]): Promise<void> {
    // Delete all vectors at once
    for (const id of memoryIds) {
      this.vectors.delete(id);
    }
    
    // Mark index for rebuild
    if (memoryIds.length > 0) {
      this.indexNeedsRebuild = true;
    }
    
    // Persist once after all deletions
    await this.persistVectors();
  }

  async similaritySearch(
    queryEmbedding: number[],
    topK: number = 10,
    threshold: number = 0.7
  ): Promise<SimilaritySearchResult> {
    if (!this.indexReady) {
      throw new Error('Vector index not ready');
    }
    
    // Rebuild index if needed
    if (this.indexNeedsRebuild) {
      await this.rebuildIndex();
    }
    
    // For small datasets, fall back to brute force
    if (this.vectors.size < 100) {
      return this.bruteForceSearch(queryEmbedding, topK, threshold);
    }
    
    // Use HNSW for efficient search
    const ef = Math.max(topK * 2, 50);  // ef should be >= k
    const candidateIds = this.hnswIndex.search(queryEmbedding, topK * 2, ef, this.vectors, this.embeddingService);
    
    // Calculate actual similarities and filter by threshold
    const results: Array<{ memory: Memory; score: number }> = [];
    
    for (const id of candidateIds) {
      const record = this.vectors.get(id);
      if (!record) continue;
      
      const similarity = this.embeddingService.cosineSimilarity(queryEmbedding, record.embedding);
      
      if (similarity >= threshold) {
        results.push({ memory: record.memory, score: similarity });
      }
      
      if (results.length >= topK) break;
    }
    
    return {
      memories: results.map(r => r.memory),
      scores: results.map(r => r.score)
    };
  }
  
  private async bruteForceSearch(
    queryEmbedding: number[],
    topK: number = 10,
    threshold: number = 0.7
  ): Promise<SimilaritySearchResult> {
    const embeddings = Array.from(this.vectors.values()).map(r => r.embedding);
    const { indices, scores } = this.embeddingService.findMostSimilar(
      queryEmbedding,
      embeddings,
      topK,
      threshold
    );

    const vectorArray = Array.from(this.vectors.values());
    const memories = indices.map(i => vectorArray[i].memory);

    return { memories, scores };
  }
  
  private async rebuildIndex(): Promise<void> {
    this.hnswIndex.clear();
    
    // Insert all vectors into the index
    for (const [id, record] of this.vectors) {
      this.hnswIndex.insert(id, record.embedding, this.vectors, this.embeddingService);
    }
    
    this.indexNeedsRebuild = false;
  }

  async hybridSearch(
    textQuery: string,
    filters?: {
      type?: string;
      tags?: string[];
      minConfidence?: number;
    },
    topK: number = 10
  ): Promise<SimilaritySearchResult> {
    // Generate embedding for text query
    const queryEmbedding = await this.embeddingService.generateEmbedding(textQuery);
    
    // Get all vectors that match filters
    let filteredVectors = Array.from(this.vectors.values());
    
    if (filters) {
      filteredVectors = filteredVectors.filter(record => {
        const memory = record.memory;
        
        if (filters.type && memory.metadata.type !== filters.type) {
          return false;
        }
        
        if (filters.tags && filters.tags.length > 0) {
          const hasTag = filters.tags.some(tag => 
            memory.metadata.tags.includes(tag)
          );
          if (!hasTag) return false;
        }
        
        if (filters.minConfidence !== undefined) {
          if (memory.metadata.confidence < filters.minConfidence) {
            return false;
          }
        }
        
        return true;
      });
    }
    
    // Calculate similarities
    const similarities: Array<{ memory: Memory; score: number }> = [];
    
    for (const record of filteredVectors) {
      const similarity = this.embeddingService.cosineSimilarity(
        queryEmbedding,
        record.embedding
      );
      similarities.push({ memory: record.memory, score: similarity });
    }
    
    // Sort by similarity
    similarities.sort((a, b) => b.score - a.score);
    
    // Take top K
    const topResults = similarities.slice(0, topK);
    
    return {
      memories: topResults.map(r => r.memory),
      scores: topResults.map(r => r.score)
    };
  }

  async findRelatedMemories(
    memoryId: string,
    topK: number = 5,
    threshold: number = 0.8
  ): Promise<Memory[]> {
    const record = this.vectors.get(memoryId);
    if (!record) return [];

    const result = await this.similaritySearch(
      record.embedding,
      topK + 1, // +1 to exclude self
      threshold
    );

    // Filter out the query memory itself
    return result.memories.filter(m => m.metadata.id !== memoryId);
  }

  async clusterMemories(k: number = 5): Promise<Map<number, Memory[]>> {
    const vectorArray = Array.from(this.vectors.values());
    if (vectorArray.length === 0) return new Map();

    const embeddings = vectorArray.map(r => r.embedding);
    const assignments = await this.embeddingService.clusterEmbeddings(embeddings, k);

    const clusters = new Map<number, Memory[]>();
    
    for (let i = 0; i < assignments.length; i++) {
      const clusterId = assignments[i];
      if (!clusters.has(clusterId)) {
        clusters.set(clusterId, []);
      }
      clusters.get(clusterId)!.push(vectorArray[i].memory);
    }

    return clusters;
  }

  async reindex(): Promise<void> {
    // Re-generate embeddings for all memories if needed
    const updates: Array<{ id: string; embedding: number[] }> = [];
    
    for (const [id, record] of this.vectors) {
      if (!record.embedding || record.embedding.length === 0) {
        const text = this.extractTextFromMemory(record.memory);
        const embedding = await this.embeddingService.generateEmbedding(text);
        updates.push({ id, embedding });
      }
    }

    // Apply updates
    for (const update of updates) {
      const record = this.vectors.get(update.id);
      if (record) {
        record.embedding = update.embedding;
        record.memory.metadata.embedding = update.embedding;
      }
    }

    await this.persistVectors();
  }

  private extractTextFromMemory(memory: Memory): string {
    // Extract searchable text from memory content
    const parts: string[] = [];
    
    // Add tags
    if (memory.metadata.tags.length > 0) {
      parts.push(`Tags: ${memory.metadata.tags.join(', ')}`);
    }
    
    // Add content based on type
    const content = memory.content;
    if (typeof content === 'string') {
      parts.push(content);
    } else if (typeof content === 'object' && content !== null) {
      // Extract text from structured content
      if ('subject' in content && 'knowledge' in content) {
        parts.push(`${content.subject}: ${content.knowledge}`);
      } else if ('task' in content && 'steps' in content) {
        parts.push(`Task: ${content.task}`);
        parts.push(`Steps: ${content.steps.join(', ')}`);
      } else if ('errorPattern' in content && 'solution' in content) {
        parts.push(`Error: ${content.errorPattern}`);
        parts.push(`Solution: ${content.solution}`);
      } else {
        // Generic object handling
        parts.push(JSON.stringify(content));
      }
    }
    
    return parts.join('\n');
  }

  private async loadPersistedVectors(): Promise<void> {
    // Skip if not in browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }
    
    // Load from IndexedDB or localStorage
    try {
      const stored = localStorage.getItem('dante_vector_store');
      if (stored) {
        const data = JSON.parse(stored);
        for (const [id, record] of Object.entries(data)) {
          this.vectors.set(id, record as VectorRecord);
        }
      }
    } catch (error) {
      console.error('Failed to load persisted vectors:', error);
    }
  }

  private async persistVectors(): Promise<void> {
    // Skip if not in browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }
    
    // Persist to localStorage (with size limit check)
    try {
      const data: Record<string, VectorRecord> = {};
      
      // Limit to most recent/important vectors if too large
      const maxVectors = 1000;
      const vectorArray = Array.from(this.vectors.entries());
      
      if (vectorArray.length > maxVectors) {
        // Sort by access count and recency
        vectorArray.sort((a, b) => {
          const memA = a[1].memory;
          const memB = b[1].memory;
          
          // Prioritize by priority level
          if (memA.metadata.priority !== memB.metadata.priority) {
            return memB.metadata.priority - memA.metadata.priority;
          }
          
          // Then by access count
          if (memA.metadata.accessCount !== memB.metadata.accessCount) {
            return memB.metadata.accessCount - memA.metadata.accessCount;
          }
          
          // Then by recency
          return memB.metadata.lastAccessed.getTime() - memA.metadata.lastAccessed.getTime();
        });
        
        // Keep only top vectors
        vectorArray.splice(maxVectors);
      }
      
      for (const [id, record] of vectorArray) {
        data[id] = record;
      }
      
      const json = JSON.stringify(data);
      
      // Check size (localStorage typically has 5-10MB limit)
      if (json.length < 4 * 1024 * 1024) { // 4MB limit
        localStorage.setItem('dante_vector_store', json);
      } else {
        console.warn('Vector store too large for localStorage, keeping most important vectors');
        // Could implement additional compression or use IndexedDB here
      }
    } catch (error) {
      console.error('Failed to persist vectors:', error);
    }
  }

  async getStats(): Promise<{
    totalVectors: number;
    avgSimilarity: number;
    clusters?: number;
  }> {
    const totalVectors = this.vectors.size;
    
    // Calculate average similarity between random pairs
    let totalSimilarity = 0;
    let comparisons = 0;
    const sample = Array.from(this.vectors.values()).slice(0, 100);
    
    for (let i = 0; i < sample.length; i++) {
      for (let j = i + 1; j < sample.length; j++) {
        totalSimilarity += this.embeddingService.cosineSimilarity(
          sample[i].embedding,
          sample[j].embedding
        );
        comparisons++;
      }
    }
    
    const avgSimilarity = comparisons > 0 ? totalSimilarity / comparisons : 0;
    
    return {
      totalVectors,
      avgSimilarity
    };
  }

  async destroy(): Promise<void> {
    await this.persistVectors();
    this.vectors.clear();
    this.hnswIndex.clear();
    this.indexReady = false;
  }

  // Required interface methods
  async bulkStore(memories: Memory[]): Promise<void> {
    for (const memory of memories) {
      await this.store(memory);
    }
  }

  async createSnapshot(): Promise<string> {
    const snapshot = {
      vectors: Array.from(this.vectors.entries()),
      timestamp: new Date().toISOString()
    };
    const snapshotId = `snapshot_${Date.now()}`;
    // In a real implementation, this would be saved to storage
    return snapshotId;
  }

  async restoreSnapshot(snapshotId: string): Promise<void> {
    // In a real implementation, this would load from storage
    // For now, just log the action
    console.log(`Restoring snapshot: ${snapshotId}`);
  }
}