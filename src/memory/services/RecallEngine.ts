import { 
  Memory, 
  MemoryType, 
  MemoryPriority,
  ErrorMemory,
  UserPreferenceMemory,
  ProjectMemory
} from '../types';
import { EmbeddingService } from './EmbeddingService';
import { IMemoryManager } from '../interfaces/IMemoryManager';

interface RecallContext {
  query: string;
  currentProject?: string;
  currentUser?: string;
  recentTopics?: string[];
  taskType?: string;
}

interface RecallStrategy {
  name: string;
  weight: number;
  filter?: (memory: Memory) => boolean;
}

interface CachedRecall {
  memories: Memory[];
  timestamp: number;
}

export class RecallEngine {
  private embeddingService: EmbeddingService;
  private memoryManager: IMemoryManager;
  private recallCache: Map<string, CachedRecall>;
  private readonly CACHE_TTL = 60000; // 1 minute

  constructor(memoryManager: IMemoryManager) {
    this.memoryManager = memoryManager;
    this.embeddingService = new EmbeddingService();
    this.recallCache = new Map();
  }

  /**
   * Enhanced recall that properly extracts details from consolidated memories
   */
  async recallWithDetails(query: string, options: { 
    limit?: number;
    includeConsolidated?: boolean;
    expandConsolidated?: boolean;
  } = {}): Promise<Memory[]> {
    const limit = options.limit || 10;
    const includeConsolidated = options.includeConsolidated ?? true;
    const expandConsolidated = options.expandConsolidated ?? true;
    
    // Perform standard recall
    const results = await this.recall(query, limit * 2);
    
    const finalResults: Memory[] = [];
    const seenIds = new Set<string>();
    
    for (const memory of results) {
      if (seenIds.has(memory.metadata.id)) continue;
      
      // Check if this is a consolidated memory
      const content = memory.content as any;
      if (expandConsolidated && content?.originalMemories) {
        // This is a consolidated memory - expand it if requested
        console.log(`Expanding consolidated memory ${memory.metadata.id} with ${content.originalMemories.length} original memories`);
        
        // Add the consolidated memory itself if requested
        if (includeConsolidated) {
          finalResults.push(memory);
          seenIds.add(memory.metadata.id);
        }
        
        // Extract and add original memories
        for (const original of content.originalMemories) {
          if (!seenIds.has(original.id)) {
            // Reconstruct the memory from the preserved content
            const reconstructed: Memory = {
              metadata: {
                id: original.id,
                type: original.type,
                priority: MemoryPriority.MEDIUM,
                createdAt: new Date(original.createdAt),
                lastAccessed: new Date(),
                accessCount: 1,
                confidence: original.confidence,
                source: original.source,
                tags: [],
                embedding: undefined
              },
              content: original.content,
              related: []
            };
            finalResults.push(reconstructed);
            seenIds.add(original.id);
          }
        }
      } else {
        // Regular memory
        finalResults.push(memory);
        seenIds.add(memory.metadata.id);
      }
      
      if (finalResults.length >= limit) break;
    }
    
    return finalResults.slice(0, limit);
  }

  async recall(context: string | RecallContext, limit: number = 5): Promise<Memory[]> {
    const recallContext = typeof context === 'string' 
      ? { query: context } 
      : context;

    // Check cache
    const cacheKey = this.getCacheKey(recallContext);
    const cached = this.getCached(cacheKey);
    if (cached) return cached;

    // Determine recall strategies based on context
    const strategies = this.determineStrategies(recallContext);
    
    // Execute recall with multiple strategies
    const recalledMemories = await this.executeRecall(recallContext, strategies, limit);
    
    // Cache results
    this.cacheRecall(cacheKey, recalledMemories);
    
    return recalledMemories;
  }

  async recallForError(errorMessage: string, context?: any): Promise<ErrorMemory[]> {
    // Search for similar error patterns
    const results = await this.memoryManager.semanticSearch({
      query: errorMessage,
      tags: ['error', 'solution'],
      type: MemoryType.PROCEDURAL,
      topK: 5,
      threshold: 0.75
    });

    // Filter and rank by success rate
    const errorMemories = results.memories as ErrorMemory[];
    
    return errorMemories
      .filter((m: ErrorMemory) => m.content.successRate > 0.5)
      .sort((a, b) => {
        // Prioritize by success rate and recency
        const scoreA = a.content.successRate * 0.7 + 
                       (1 / (Date.now() - a.metadata.lastAccessed.getTime())) * 0.3;
        const scoreB = b.content.successRate * 0.7 + 
                       (1 / (Date.now() - b.metadata.lastAccessed.getTime())) * 0.3;
        return scoreB - scoreA;
      });
  }

  async recallConversationContext(
    currentMessages: Array<{ role: string; content: string }>,
    limit: number = 3
  ): Promise<Memory[]> {
    // Extract key topics from recent messages
    const recentContent = currentMessages
      .slice(-5)
      .map(m => m.content)
      .join('\n');

    // Search for related episodic and semantic memories
    const results = await this.memoryManager.semanticSearch({
      query: recentContent,
      type: [MemoryType.EPISODIC, MemoryType.SEMANTIC],
      topK: limit,
      threshold: 0.6
    });

    return results.memories;
  }

  async recallProjectContext(projectPath: string): Promise<ProjectMemory | null> {
    return this.memoryManager.recallProjectKnowledge(projectPath);
  }

  async recallRelevantSkills(task: string): Promise<Memory[]> {
    // Search for procedural memories related to the task
    const results = await this.memoryManager.semanticSearch({
      query: task,
      type: MemoryType.PROCEDURAL,
      topK: 5,
      threshold: 0.7
    });

    // Filter for successful procedures
    return results.memories.filter((m: Memory) => {
      const content = m.content as any;
      if (content.successRate !== undefined) {
        return content.successRate > 0.7;
      }
      return true;
    });
  }

  async recallUserContext(userId?: string): Promise<UserPreferenceMemory[]> {
    return this.memoryManager.recallUserPreferences();
  }

  private determineStrategies(context: RecallContext): RecallStrategy[] {
    const strategies: RecallStrategy[] = [];

    // Always include semantic similarity
    strategies.push({
      name: 'semantic',
      weight: 0.4
    });

    // Add recency bias
    strategies.push({
      name: 'recent',
      weight: 0.2,
      filter: (memory) => {
        const daysSinceAccess = (Date.now() - memory.metadata.lastAccessed.getTime()) 
                                / (1000 * 60 * 60 * 24);
        return daysSinceAccess < 7;
      }
    });

    // Add project context if available
    if (context.currentProject) {
      strategies.push({
        name: 'project',
        weight: 0.3,
        filter: (memory) => memory.metadata.projectId === context.currentProject
      });
    }

    // Add task-specific strategy
    if (context.taskType) {
      strategies.push({
        name: 'task',
        weight: 0.2,
        filter: (memory) => {
          if (context.taskType === 'debugging' && memory.metadata.tags.includes('error')) {
            return true;
          }
          if (context.taskType === 'coding' && memory.metadata.type === MemoryType.PROCEDURAL) {
            return true;
          }
          return false;
        }
      });
    }

    // Normalize weights
    const totalWeight = strategies.reduce((sum, s) => sum + s.weight, 0);
    strategies.forEach(s => s.weight /= totalWeight);

    return strategies;
  }

  private async executeRecall(
    context: RecallContext,
    strategies: RecallStrategy[],
    limit: number
  ): Promise<Memory[]> {
    const scoredMemories = new Map<string, { memory: Memory; score: number }>();

    // Execute semantic search
    const semanticResults = await this.memoryManager.semanticSearch({
      query: context.query,
      topK: limit * 3, // Get more candidates for filtering
      threshold: 0.5
    });

    // Score memories based on strategies
    for (let i = 0; i < semanticResults.memories.length; i++) {
      const memory = semanticResults.memories[i];
      const semanticScore = semanticResults.relevanceScores?.[i] || 0;
      
      let totalScore = 0;
      
      for (const strategy of strategies) {
        let strategyScore = 0;
        
        if (strategy.name === 'semantic') {
          strategyScore = semanticScore;
        } else if (strategy.filter) {
          strategyScore = strategy.filter(memory) ? 1 : 0;
        }
        
        totalScore += strategyScore * strategy.weight;
      }

      // Boost score based on priority
      const priorityBoost = memory.metadata.priority / 10;
      totalScore += priorityBoost;

      // Boost score based on confidence
      totalScore *= memory.metadata.confidence;

      scoredMemories.set(memory.metadata.id, { memory, score: totalScore });
    }

    // Sort by score and return top memories
    const sorted = Array.from(scoredMemories.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return sorted.map(item => item.memory);
  }

  async associativeRecall(seedMemory: Memory, limit: number = 5): Promise<Memory[]> {
    if (!seedMemory.metadata.embedding) {
      return [];
    }

    // Find memories similar to the seed
    const vectorStore = this.memoryManager.getVectorStore();
    if (!vectorStore) return [];
    
    const similar = await vectorStore.similaritySearch(
      seedMemory.metadata.embedding,
      limit + 1,
      0.7
    );

    if (!similar) return [];

    // Filter out the seed memory itself
    return similar.memories.filter((m: Memory) => m.metadata.id !== seedMemory.metadata.id);
  }

  async consolidateRelated(memories: Memory[]): Promise<Memory | null> {
    if (memories.length < 2) return null;

    // Find common themes and patterns
    const allTags = new Set<string>();
    const contents: string[] = [];
    const detailedContent: any[] = [];
    
    for (const memory of memories) {
      memory.metadata.tags.forEach((tag: string) => allTags.add(tag));
      contents.push(JSON.stringify(memory.content));
      // Preserve the actual content
      detailedContent.push({
        id: memory.metadata.id,
        type: memory.metadata.type,
        content: memory.content,
        createdAt: memory.metadata.createdAt,
        confidence: memory.metadata.confidence,
        source: memory.metadata.source
      });
    }

    // Generate a consolidated embedding
    const consolidatedEmbedding = await this.embeddingService.generateWeightedEmbedding(
      contents,
      memories.map(m => m.metadata.confidence)
    );

    // Create a new consolidated memory with preserved details
    const consolidated: Memory = {
      metadata: {
        id: `consolidated_${Date.now()}_${Math.random().toString(36).substring(7)}`,
        type: MemoryType.SEMANTIC,
        priority: MemoryPriority.HIGH,
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: memories.reduce((sum, m) => sum + m.metadata.accessCount, 0),
        confidence: memories.reduce((sum, m) => sum + m.metadata.confidence, 0) / memories.length,
        source: 'consolidation',
        tags: Array.from(allTags),
        embedding: consolidatedEmbedding
      },
      content: {
        consolidatedFrom: memories.map(m => m.metadata.id),
        summary: `Consolidated knowledge from ${memories.length} related memories`,
        insights: this.extractComprehensiveInsights(memories),
        // IMPORTANT: Preserve all original content
        originalMemories: detailedContent,
        // Add a merged view for easy access
        mergedContent: this.mergeMemoryContents(memories),
        consolidationTimestamp: new Date().toISOString(),
        consolidationReason: 'high_similarity'
      },
      related: memories.map(m => m.metadata.id)
    };

    return consolidated;
  }

  private extractInsights(memories: Memory[]): string[] {
    const insights: string[] = [];
    
    // Extract common patterns
    const patterns = new Map<string, number>();
    
    for (const memory of memories) {
      if (memory.metadata.type === MemoryType.PROCEDURAL) {
        const content = memory.content as any;
        if (content.steps) {
          content.steps.forEach((step: string) => {
            const count = patterns.get(step) || 0;
            patterns.set(step, count + 1);
          });
        }
      }
    }

    // Find repeated patterns
    for (const [pattern, count] of patterns) {
      if (count > memories.length / 2) {
        insights.push(`Common pattern: ${pattern}`);
      }
    }

    return insights;
  }

  private extractComprehensiveInsights(memories: Memory[]): string[] {
    const insights: string[] = [];
    const contentTypes = new Map<string, number>();
    const keyTopics = new Set<string>();
    const sources = new Set<string>();
    
    for (const memory of memories) {
      // Track content types
      const typeCount = contentTypes.get(memory.metadata.type) || 0;
      contentTypes.set(memory.metadata.type, typeCount + 1);
      
      // Track sources
      sources.add(memory.metadata.source);
      
      // Extract key information based on memory type
      const content = memory.content as any;
      
      if (memory.metadata.type === MemoryType.SEMANTIC) {
        if (typeof content === 'string') {
          insights.push(`Semantic: ${content.substring(0, 200)}...`);
        } else if (content.summary) {
          insights.push(`Semantic: ${content.summary}`);
        }
      } else if (memory.metadata.type === MemoryType.EPISODIC) {
        if (content.event) {
          insights.push(`Event: ${content.event}`);
        }
      } else if (memory.metadata.type === MemoryType.PROCEDURAL) {
        if (content.steps) {
          insights.push(`Procedure with ${content.steps.length} steps`);
        }
      }
      
      // Extract tags as topics
      memory.metadata.tags.forEach(tag => keyTopics.add(tag));
    }
    
    // Add summary insights
    insights.unshift(`Consolidated ${memories.length} memories from sources: ${Array.from(sources).join(', ')}`);
    insights.unshift(`Memory types: ${Array.from(contentTypes.entries()).map(([type, count]) => `${type}(${count})`).join(', ')}`);
    if (keyTopics.size > 0) {
      insights.push(`Key topics: ${Array.from(keyTopics).join(', ')}`);
    }
    
    return insights;
  }

  private mergeMemoryContents(memories: Memory[]): any {
    const merged: any = {
      combinedText: [],
      allTags: new Set<string>(),
      allSources: new Set<string>(),
      contentByType: {}
    };
    
    for (const memory of memories) {
      const content = memory.content as any;
      
      // Organize by type
      if (!merged.contentByType[memory.metadata.type]) {
        merged.contentByType[memory.metadata.type] = [];
      }
      merged.contentByType[memory.metadata.type].push(content);
      
      // Collect text content
      if (typeof content === 'string') {
        merged.combinedText.push(content);
      } else if (content.text) {
        merged.combinedText.push(content.text);
      } else if (content.summary) {
        merged.combinedText.push(content.summary);
      }
      
      // Collect metadata
      memory.metadata.tags.forEach(tag => merged.allTags.add(tag));
      merged.allSources.add(memory.metadata.source);
    }
    
    merged.allTags = Array.from(merged.allTags);
    merged.allSources = Array.from(merged.allSources);
    merged.combinedText = merged.combinedText.join('\n\n');
    
    return merged;
  }

  private getCacheKey(context: RecallContext): string {
    return JSON.stringify({
      query: context.query,
      project: context.currentProject,
      user: context.currentUser,
      taskType: context.taskType
    });
  }

  private getCached(key: string): Memory[] | null {
    const cached = this.recallCache.get(key);
    if (!cached) return null;
    
    // Check if cache has expired
    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_TTL) {
      this.recallCache.delete(key);
      return null;
    }
    
    return cached.memories;
  }

  private cacheRecall(key: string, memories: Memory[]): void {
    this.recallCache.set(key, {
      memories,
      timestamp: Date.now()
    });
    
    // Clean old cache entries
    if (this.recallCache.size > 100) {
      const firstKey = this.recallCache.keys().next().value;
      if (firstKey) {
        this.recallCache.delete(firstKey);
      }
    }
  }

  clearCache(): void {
    this.recallCache.clear();
  }
}