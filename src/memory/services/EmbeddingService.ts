import { OpenAI } from 'openai';
import { config } from '../../utils/config';

export type EmbeddingProvider = 'openai' | 'ollama';
export type EmbeddingProviderPreference = EmbeddingProvider | 'auto';

export interface EmbeddingCacheEntry {
  embedding: number[];
  timestamp: number;
}

export interface EmbeddingServiceOptions {
  provider?: EmbeddingProviderPreference;
  openaiModel?: string;
  ollamaModel?: string;
  ollamaBaseURL?: string;
  cacheTTL?: number;
  maxCacheSize?: number;
  defaultDimensions?: number;
}

export class EmbeddingService {
  private openaiClient?: OpenAI;
  private readonly cache: Map<string, EmbeddingCacheEntry>;
  private readonly cacheTTL: number;
  private readonly maxCacheSize: number;
  private readonly providerPreference: EmbeddingProviderPreference;
  private readonly openaiModel: string;
  private readonly ollamaModel: string;
  private readonly ollamaBaseURL?: string;
  private readonly offlineProviderPreferred: boolean;
  private embeddingDimensions: number;
  private readonly TRUNCATE_CHAR_LIMIT = 8000;

  constructor(options: EmbeddingServiceOptions = {}) {
    this.providerPreference =
      options.provider ??
      (config.embeddings?.provider as EmbeddingProviderPreference) ??
      'auto';

    this.openaiModel =
      options.openaiModel ??
      config.embeddings?.openaiModel ??
      'text-embedding-3-small';

    this.ollamaModel =
      options.ollamaModel ??
      config.embeddings?.ollamaModel ??
      'nomic-embed-text';

    this.ollamaBaseURL = options.ollamaBaseURL ?? config.ollama?.baseURL;

    this.cacheTTL =
      options.cacheTTL ??
      config.embeddings?.cacheTTL ??
      24 * 60 * 60 * 1000;

    this.maxCacheSize =
      options.maxCacheSize ?? config.embeddings?.maxCacheSize ?? 1000;

    this.cache = new Map();

    this.offlineProviderPreferred =
      (config.orchestrator?.offlineProvider || '').toLowerCase() === 'ollama' &&
      !!this.ollamaBaseURL;

    const openaiApiKey = config.openai?.apiKey;
    if (openaiApiKey) {
      const baseURL = config.openai?.endpoint;
      this.openaiClient = new OpenAI({
        apiKey: openaiApiKey,
        baseURL,
      });
    }

    this.embeddingDimensions =
      options.defaultDimensions ?? this.inferDefaultDimensions();
  }

  getEmbeddingDimensions(): number {
    return this.embeddingDimensions;
  }

  async generateEmbedding(text: string): Promise<number[]> {
    const providers = this.resolveProviderOrder();
    if (providers.length === 0) {
      console.error('[EmbeddingService] No embedding providers available');
      return this.zeroVector();
    }

    const truncatedText = this.truncateText(text, this.TRUNCATE_CHAR_LIMIT);

    for (const provider of providers) {
      const cacheKey = this.getCacheKey(provider, text);
      const cached = this.getCached(cacheKey);
      if (cached) {
        return cached;
      }
    }

    let lastProvider: EmbeddingProvider | null = null;
    const errors: Array<{ provider: EmbeddingProvider; error: unknown }> = [];

    for (const provider of providers) {
      lastProvider = provider;
      const cacheKey = this.getCacheKey(provider, text);
      try {
        const embedding = await this.generateEmbeddingWithProvider(provider, truncatedText);
        this.setEmbeddingDimensions(embedding.length);
        this.cacheEmbedding(cacheKey, embedding);
        return embedding;
      } catch (error) {
        errors.push({ provider, error });
        this.logProviderError(provider, error);
      }
    }

    if (lastProvider) {
      this.setEmbeddingDimensions(this.providerDimensionHint(lastProvider));
    }

    if (errors.length > 0) {
      const lastError = errors[errors.length - 1];
      console.error(
        '[EmbeddingService] Failed to generate embedding with all providers:',
        lastError.error
      );
    }

    return this.zeroVector();
  }

  async generateBatchEmbeddings(texts: string[]): Promise<number[][]> {
    if (texts.length === 0) {
      return [];
    }

    const providers = this.resolveProviderOrder();
    const truncatedTexts = texts.map(text =>
      this.truncateText(text, this.TRUNCATE_CHAR_LIMIT)
    );
    const results: Array<number[] | null> = new Array(texts.length).fill(null);
    const pending = new Set<number>(texts.map((_, index) => index));

    // Fill from cache first using provider order so newer cache entries take priority
    for (const provider of providers) {
      for (const index of Array.from(pending)) {
        const cacheKey = this.getCacheKey(provider, texts[index]);
        const cached = this.getCached(cacheKey);
        if (cached) {
          results[index] = cached;
          pending.delete(index);
        }
      }
      if (pending.size === 0) {
        break;
      }
    }

    // Attempt provider-specific batch strategies
    for (const provider of providers) {
      const targetIndices = Array.from(pending);
      if (targetIndices.length === 0) {
        break;
      }

      try {
        let providerResults: Map<number, number[]> | null = null;

        if (provider === 'openai') {
          providerResults = await this.generateOpenAIBatchEmbeddings(
            targetIndices.map(index => ({
              index,
              text: texts[index],
              truncated: truncatedTexts[index],
            }))
          );
        } else if (provider === 'ollama') {
          providerResults = await this.generateOllamaBatchEmbeddings(
            targetIndices.map(index => ({
              index,
              text: texts[index],
              truncated: truncatedTexts[index],
            }))
          );
        }

        if (providerResults && providerResults.size > 0) {
          for (const [index, embedding] of providerResults.entries()) {
            results[index] = embedding;
            pending.delete(index);
            this.cacheEmbedding(this.getCacheKey(provider, texts[index]), embedding);
            this.setEmbeddingDimensions(embedding.length);
          }
        }
      } catch (error) {
        this.logProviderError(provider, error);
        // Leave items pending so the next provider can attempt them
      }
    }

    // Provide deterministic fallback for any remaining entries
    for (const index of pending) {
      results[index] = await this.generateEmbedding(texts[index]);
    }

    return results.map(embedding => embedding ?? this.zeroVector());
  }

  private async generateOpenAIBatchEmbeddings(
    entries: Array<{ index: number; text: string; truncated: string }>
  ): Promise<Map<number, number[]>> {
    if (!this.openaiClient || entries.length === 0) {
      return new Map();
    }

    const MAX_TOKENS_PER_BATCH = 8000;
    const results = new Map<number, number[]>();
    let batch: Array<{ index: number; text: string; truncated: string }> = [];
    let tokenCount = 0;

    const flushBatch = async () => {
      if (!this.openaiClient || batch.length === 0) {
        return;
      }

      const response = await this.openaiClient.embeddings.create({
        model: this.openaiModel,
        input: batch.map(item => item.truncated),
      });

      const vectors = response.data?.map(item => item.embedding) ?? [];
      if (vectors.length !== batch.length) {
        throw new Error('OpenAI batch response size mismatch');
      }

      for (let i = 0; i < batch.length; i++) {
        const { index } = batch[i];
        results.set(index, vectors[i]);
      }

      batch = [];
      tokenCount = 0;
    };

    for (const entry of entries) {
      const estimatedTokens = this.estimateTokenCount(entry.truncated);
      if (tokenCount + estimatedTokens > MAX_TOKENS_PER_BATCH && batch.length > 0) {
        await flushBatch();
      }

      batch.push(entry);
      tokenCount += estimatedTokens;
    }

    await flushBatch();

    return results;
  }

  private async generateOllamaBatchEmbeddings(
    entries: Array<{ index: number; text: string; truncated: string }>
  ): Promise<Map<number, number[]>> {
    if (entries.length === 0) {
      return new Map();
    }

    const results = new Map<number, number[]>();
    for (const entry of entries) {
      try {
        const embedding = await this.generateOllamaEmbedding(entry.truncated);
        results.set(entry.index, embedding);
      } catch (error) {
        this.logProviderError('ollama', error);
      }
    }
    return results;
  }

  // Calculate cosine similarity between two embeddings
  cosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimension');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    norm1 = Math.sqrt(norm1);
    norm2 = Math.sqrt(norm2);

    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }

    return dotProduct / (norm1 * norm2);
  }

  // Find most similar embeddings
  findMostSimilar(
    queryEmbedding: number[],
    embeddings: number[][],
    topK: number = 5,
    threshold: number = 0.7
  ): { indices: number[]; scores: number[] } {
    const similarities: Array<{ index: number; score: number }> = [];

    for (let i = 0; i < embeddings.length; i++) {
      const similarity = this.cosineSimilarity(queryEmbedding, embeddings[i]);
      if (similarity >= threshold) {
        similarities.push({ index: i, score: similarity });
      }
    }

    // Sort by similarity score (descending)
    similarities.sort((a, b) => b.score - a.score);

    // Take top K
    const topResults = similarities.slice(0, topK);

    return {
      indices: topResults.map(r => r.index),
      scores: topResults.map(r => r.score)
    };
  }

  // Cluster embeddings using simple k-means
  async clusterEmbeddings(
    embeddings: number[][],
    k: number = 5
  ): Promise<number[]> {
    if (embeddings.length === 0) return [];
    if (k >= embeddings.length) {
      return embeddings.map((_, i) => i);
    }

    // Simple k-means implementation
    const assignments = new Array(embeddings.length).fill(0);
    const centroids: number[][] = [];

    // Initialize centroids randomly
    const indices = new Set<number>();
    while (indices.size < k) {
      indices.add(Math.floor(Math.random() * embeddings.length));
    }
    Array.from(indices).forEach((idx, i) => {
      centroids[i] = [...embeddings[idx]];
    });

    // Iterate until convergence
    const maxIterations = 50;
    for (let iter = 0; iter < maxIterations; iter++) {
      let changed = false;

      // Assign points to nearest centroid
      for (let i = 0; i < embeddings.length; i++) {
        let minDist = Infinity;
        let bestCluster = 0;

        for (let j = 0; j < k; j++) {
          const dist = this.euclideanDistance(embeddings[i], centroids[j]);
          if (dist < minDist) {
            minDist = dist;
            bestCluster = j;
          }
        }

        if (assignments[i] !== bestCluster) {
          assignments[i] = bestCluster;
          changed = true;
        }
      }

      if (!changed) break;

      // Update centroids
      for (let j = 0; j < k; j++) {
        const clusterPoints = embeddings.filter((_, i) => assignments[i] === j);
        if (clusterPoints.length > 0) {
          centroids[j] = this.calculateCentroid(clusterPoints);
        }
      }
    }

    return assignments;
  }

  private truncateText(text: string, maxChars: number = 8000): string {
    if (text.length <= maxChars) return text;
    return text.substring(0, maxChars) + '...';
  }

  private getCached(key: string): number[] | null {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    const age = Date.now() - cached.timestamp;
    if (age > this.cacheTTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.embedding;
  }

  private cacheEmbedding(key: string, embedding: number[]): void {
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value as string | undefined;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      embedding,
      timestamp: Date.now()
    });
  }

  private euclideanDistance(vec1: number[], vec2: number[]): number {
    let sum = 0;
    for (let i = 0; i < vec1.length; i++) {
      const diff = vec1[i] - vec2[i];
      sum += diff * diff;
    }
    return Math.sqrt(sum);
  }

  private calculateCentroid(points: number[][]): number[] {
    const dimensions = points[0].length;
    const centroid = new Array(dimensions).fill(0);

    for (const point of points) {
      for (let i = 0; i < dimensions; i++) {
        centroid[i] += point[i];
      }
    }

    for (let i = 0; i < dimensions; i++) {
      centroid[i] /= points.length;
    }

    return centroid;
  }

  // Text preprocessing for better embeddings
  preprocessText(text: string): string {
    // Remove excessive whitespace
    text = text.replace(/\s+/g, ' ').trim();

    // Remove very long repeated characters
    text = text.replace(/(.)\1{4,}/g, '$1$1$1');

    // Normalize quotes
    text = text.replace(/[\""]/g, '"').replace(/['']/g, "'");

    return text;
  }

  // Combine multiple texts with weights
  async generateWeightedEmbedding(
    texts: string[],
    weights?: number[]
  ): Promise<number[]> {
    if (texts.length === 0) {
      return this.zeroVector();
    }

    const embeddings = await this.generateBatchEmbeddings(texts);
    if (embeddings.length === 0) {
      return this.zeroVector();
    }

    const vectorLength = embeddings[0].length;
    const consistent = embeddings.every(embedding => embedding.length === vectorLength);
    if (!consistent) {
      throw new Error('Embedding dimensions must match for weighted combination');
    }

    this.setEmbeddingDimensions(vectorLength);

    const finalWeights = weights || new Array(texts.length).fill(1 / texts.length);

    if (embeddings.length !== finalWeights.length) {
      throw new Error('Number of texts and weights must match');
    }

    const sumWeights = finalWeights.reduce((a, b) => a + b, 0);
    const normalizedWeights = finalWeights.map(w => w / sumWeights);

    const result = new Array(vectorLength).fill(0);

    for (let i = 0; i < embeddings.length; i++) {
      const embedding = embeddings[i];
      for (let j = 0; j < embedding.length; j++) {
        result[j] += embedding[j] * normalizedWeights[i];
      }
    }

    return result;
  }

  clearCache(): void {
    this.cache.clear();
  }

  private resolveProviderOrder(): EmbeddingProvider[] {
    const order: EmbeddingProvider[] = [];
    const openaiAvailable = !!this.openaiClient;
    const ollamaAvailable = !!this.ollamaBaseURL;

    const preference = this.providerPreference;

    if (preference === 'openai') {
      if (openaiAvailable) {
        order.push('openai');
      } else if (ollamaAvailable) {
        order.push('ollama');
      }
      return order;
    }

    if (preference === 'ollama') {
      if (ollamaAvailable) {
        order.push('ollama');
      } else if (openaiAvailable) {
        order.push('openai');
      }
      return order;
    }

    if (preference === 'auto') {
      if (this.offlineProviderPreferred && ollamaAvailable) {
        order.push('ollama');
      }
      if (openaiAvailable) {
        order.push('openai');
      }
      if (ollamaAvailable && !order.includes('ollama')) {
        order.push('ollama');
      }
    }

    if (order.length === 0) {
      if (openaiAvailable) {
        order.push('openai');
      }
      if (ollamaAvailable && !order.includes('ollama')) {
        order.push('ollama');
      }
    }

    return order;
  }

  private getCacheKey(provider: EmbeddingProvider, text: string): string {
    const model = provider === 'openai' ? this.openaiModel : this.ollamaModel;
    return `${provider}:${model}:${text}`;
  }

  private async generateEmbeddingWithProvider(
    provider: EmbeddingProvider,
    text: string
  ): Promise<number[]> {
    if (provider === 'openai') {
      return this.generateOpenAIEmbedding(text);
    }

    if (provider === 'ollama') {
      return this.generateOllamaEmbedding(text);
    }

    throw new Error(`Unsupported embedding provider: ${provider}`);
  }

  private async generateOpenAIEmbedding(input: string): Promise<number[]> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client is not configured');
    }

    const response = await this.openaiClient.embeddings.create({
      model: this.openaiModel,
      input,
    });

    const embedding = response.data?.[0]?.embedding;
    if (!embedding) {
      throw new Error('OpenAI embedding response did not include data');
    }

    return embedding;
  }

  private async generateOllamaEmbedding(prompt: string): Promise<number[]> {
    if (!this.ollamaBaseURL) {
      throw new Error('Ollama base URL is not configured');
    }

    const url = this.normalizeOllamaURL('/api/embeddings');
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.ollamaModel,
        prompt,
      }),
    });

    if (!response.ok) {
      const message = await response.text().catch(() => response.statusText);
      throw new Error(
        `Ollama embedding request failed (${response.status} ${response.statusText}): ${message}`
      );
    }

    let responseText = '';
    let payload: any;
    try {
      responseText = await response.text();
      payload = JSON.parse(responseText);
    } catch (error) {
      const originalBody = responseText ? ` | Body: ${responseText.slice(0, 200)}` : '';
      throw new Error(
        `Failed to parse Ollama embedding response: ${error instanceof Error ? error.message : String(error)}${originalBody}`
      );
    }

    const embedding = payload?.embedding;
    if (!Array.isArray(embedding)) {
      throw new Error('Ollama embedding response missing embedding array');
    }

    const vector = embedding.map((value: unknown, index: number) => {
      const numeric = typeof value === 'number' ? value : Number(value);
      if (!Number.isFinite(numeric)) {
          throw new Error(`Ollama embedding contains non-numeric value at index ${index}: ${String(value)}`);
      }
      return numeric;
    });

    return vector;
  }

  private normalizeOllamaURL(path: string): string {
    const base = (this.ollamaBaseURL || '').replace(/\/+$/, '');
    return `${base}${path}`;
  }

  private providerDimensionHint(provider?: EmbeddingProvider | null): number {
    if (!provider) {
      return this.embeddingDimensions || 1536;
    }

    if (provider === 'openai') {
      const hint = Number(config.embeddings?.openaiDimensions);
      if (Number.isFinite(hint) && hint > 0) {
        return hint;
      }
      return 1536;
    }

    const hint = Number(config.embeddings?.ollamaDimensions);
    if (Number.isFinite(hint) && hint > 0) {
      return hint;
    }

    if (this.ollamaModel.toLowerCase().includes('nomic')) {
      return 768;
    }

    if (this.ollamaModel.toLowerCase().includes('all-minilm')) {
      return 384;
    }

    return this.embeddingDimensions || 768;
  }

  private inferDefaultDimensions(): number {
    const providers = this.resolveProviderOrder();
    if (providers.length > 0) {
      const hint = this.providerDimensionHint(providers[0]);
      if (hint > 0) {
        return hint;
      }
    }
    return 1536;
  }

  private setEmbeddingDimensions(length: number): void {
    if (Number.isFinite(length) && length > 0) {
      this.embeddingDimensions = length;
    }
  }

  private zeroVector(length: number = this.embeddingDimensions): number[] {
    const size = Number.isFinite(length) && length > 0 ? Math.floor(length) : 1536;
    return new Array(size).fill(0);
  }

  private logProviderError(provider: EmbeddingProvider, error: unknown): void {
    const description = error instanceof Error ? error.message : String(error);
    console.warn(`[EmbeddingService] ${provider} provider failed: ${description}`);
  }

  private estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }
}
