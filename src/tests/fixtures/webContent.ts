/**
 * Test fixtures for web content chunking tests
 * These fixtures provide consistent data for testing without relying on external websites
 */

export const mockNewsArticleHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>Google Pixel 10 Release Date Finally Revealed</title>
    <meta property="og:title" content="Google Pixel 10 Release Date Revealed">
    <meta name="author" content="<PERSON>">
    <meta property="article:published_time" content="2024-08-17T10:00:00Z">
</head>
<body>
    <nav>Navigation menu here</nav>
    <header>Header content</header>
    
    <main>
        <article class="entry-content">
            <h1 class="entry-title">Google Pixel 10 Release Date Finally Revealed</h1>
            
            <div class="author">By <PERSON></div>
            <time datetime="2024-08-17T10:00:00Z">August 17, 2024</time>
            
            <p>Google has finally announced the official release date for the highly anticipated Pixel 10 smartphone. After months of speculation and leaks, the tech giant confirmed that the device will launch on October 15, 2024, continuing their tradition of fall releases.</p>
            
            <p>The Pixel 10 represents a significant step forward for Google's smartphone lineup. Sources close to the company reveal that this device will feature groundbreaking AI capabilities powered by the latest Tensor G5 chip, which promises to deliver unprecedented performance and efficiency.</p>
            
            <h2>Key Features and Specifications</h2>
            
            <p>According to reliable industry insiders, the Pixel 10 will include several notable upgrades over its predecessor. The device is expected to feature a larger 6.8-inch OLED display with improved brightness and color accuracy. Additionally, the camera system has been completely redesigned with a new 108MP main sensor.</p>
            
            <p>Battery life has been a major focus for Google's engineering team. The Pixel 10 will reportedly include a 5,000mAh battery with support for 65W fast charging, addressing one of the most common complaints about previous Pixel devices.</p>
            
            <h2>Pricing and Availability</h2>
            
            <p>While Google hasn't officially announced pricing details, industry analysts expect the Pixel 10 to start at $899 for the base 128GB model. Higher storage variants will likely be available, with the top-tier 512GB model potentially reaching $1,199.</p>
            
            <p>Pre-orders are expected to begin on September 20, 2024, with general availability following the October 15 launch date. The device will initially be available in Google's traditional markets including the United States, Canada, and select European countries.</p>
            
            <h2>Market Impact and Competition</h2>
            
            <p>The Pixel 10's launch comes at a crucial time for Google as the company seeks to gain market share in the increasingly competitive smartphone space. With Apple's iPhone 16 and Samsung's Galaxy S25 series also launching this fall, Google will need to differentiate its offering through superior AI capabilities and camera performance.</p>
            
            <p>In conclusion, the Pixel 10 represents Google's most ambitious smartphone effort to date. With its advanced AI features, improved camera system, and competitive pricing, the device could potentially reshape the premium smartphone market when it arrives this October.</p>
        </article>
    </main>
    
    <aside class="sidebar">Sidebar content</aside>
    <footer>Footer content</footer>
    
    <script>Some JavaScript</script>
</body>
</html>
`;

export const mockTechNewsHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>Breaking: New AI Breakthrough</title>
</head>
<body>
    <div class="post-content">
        <h1>Revolutionary AI Model Achieves Human-Level Performance</h1>
        
        <p>Scientists at leading research institutions have announced a breakthrough in artificial intelligence that could fundamentally change how we interact with technology. The new model, codenamed "Nexus," demonstrates human-level performance across a wide range of cognitive tasks.</p>
        
        <p>This development represents years of research into neural network architectures and training methodologies. The implications for industries ranging from healthcare to education are profound.</p>
        
        <p>Finally, researchers note that while this achievement is significant, responsible development and deployment of such powerful AI systems remains a critical priority for the scientific community.</p>
    </div>
</body>
</html>
`;

export const mockSearchResults = [
    {
        title: "Google Pixel 10 Release Date Finally Revealed",
        url: "https://example-tech-news.com/pixel-10-release",
        snippet: "Google has finally announced the official release date for the highly anticipated Pixel 10 smartphone...",
        source: "DuckDuckGo",
        publishedDate: "2024-08-17T10:00:00Z"
    },
    {
        title: "Pixel 10 Specs Leaked: Everything We Know",
        url: "https://another-tech-site.com/pixel-10-specs",
        snippet: "New leaks reveal the complete specifications for Google's upcoming Pixel 10, including camera upgrades...",
        source: "DuckDuckGo",
        publishedDate: "2024-08-16T15:30:00Z"
    },
    {
        title: "Google Pixel 10 vs iPhone 16: Which Should You Buy?",
        url: "https://comparison-site.com/pixel-10-vs-iphone-16",
        snippet: "We compare Google's latest Pixel 10 against Apple's iPhone 16 to help you decide...",
        source: "Brave Search",
        publishedDate: "2024-08-15T12:00:00Z"
    }
];

export const expectedChunkStructure = {
    intro: {
        id: 'intro',
        title: 'Introduction',
        type: 'intro',
        priority: 10,
        contentPattern: 'Google has finally announced'
    },
    conclusion: {
        id: 'conclusion',
        title: 'Conclusion',
        type: 'conclusion',
        priority: 9,
        contentPattern: 'In conclusion'
    },
    section: {
        type: 'section',
        priority: 'number',
        content: 'string'
    }
};

export const mockFetchResponse = {
    ok: true,
    status: 200,
    headers: new Map([['content-type', 'text/html']]),
    text: () => Promise.resolve(mockNewsArticleHTML)
};

export const mockErrorResponse = {
    ok: false,
    status: 404,
    statusText: 'Not Found'
};