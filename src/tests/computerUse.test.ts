import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';
import { executeComputerUseTool } from '../tools/computerUse/computerUseToolExecutor';
import { ComputerUseSafety, defaultSafetyConfig } from '../tools/computerUse/safetyFramework';
import { DockerEnvironment } from '../tools/computerUse/environmentManager';

type SafetyCheck = { id: string; code: string; message: string; severity: "low" | "medium" | "high" | "critical" };

// Mock OpenAI
const mockOpenAI = {
  responses: {
    create: mock(() => Promise.resolve({
      id: 'test-response-id',
      output: [{
        type: 'computer_call',
        id: 'test-computer-call',
        call_id: 'test-call-id',
        action: {
          type: 'click',
          x: 100,
          y: 200,
          button: 'left'
        },
        pending_safety_checks: [] as SafetyCheck[],
        status: 'completed'
      }]
    }))
  }
};


// Mock OpenAI module
mock.module('openai', () => ({
  default: mock(() => mockOpenAI)
}));

describe('Computer Use Integration', () => {
  describe('Safety Framework', () => {
    let safety: ComputerUseSafety;

    beforeEach(() => {
      safety = new ComputerUseSafety(defaultSafetyConfig);
    });

    it('should validate URLs correctly', async () => {
      const checks = await safety.validateUrl('https://example.com');
      expect(checks).toHaveLength(0);
    });

    it('should detect blocked domains', async () => {
      const checks = await safety.validateUrl('https://phishing-site.com');
      expect(checks).toHaveLength(1);
      expect(checks[0].severity).toBe('critical');
    });

    it('should detect sensitive domains', async () => {
      const checks = await safety.validateUrl('https://mybank.com');
      expect(checks).toHaveLength(1);
      expect(checks[0].code).toBe('sensitive_domain');
    });

    it('should validate actions', async () => {
      const action = {
        type: 'type' as const,
        text: 'password=secret123'
      };

      const checks = await safety.validateAction(action);
      expect(checks.length).toBeGreaterThan(0);
    });

    it('should detect dangerous key combinations', async () => {
      const action = {
        type: 'keypress' as const,
        keys: ['ctrl', 'alt', 'del']
      };

      const checks = await safety.validateAction(action);
      expect(checks.length).toBeGreaterThan(0);
      expect(checks[0].severity).toBe('high');
    });

    it('should log actions properly', () => {
      const action = {
        type: 'click' as const,
        x: 100,
        y: 200,
        button: 'left' as const
      };

      safety.logAction(action, {
        url: 'https://example.com',
        success: true
      });

      const log = safety.getActionLog();
      expect(log).toHaveLength(1);
      expect(log[0].action).toEqual(expect.objectContaining(action));
    });

    it('should provide session statistics', () => {
      const action = {
        type: 'click' as const,
        x: 100,
        y: 200,
        button: 'left' as const
      };

      safety.logAction(action, {
        url: 'https://example.com',
        success: true
      });

      const stats = safety.getSessionStats();
      expect(stats.actionCount).toBe(1);
      expect(stats.domains).toContain('example.com');
    });
  });

  // BrowserEnvironment removed (Node Playwright eliminated)

  describe('Computer Use Tool', () => {
    it('should handle start_session action', async () => {
      const result = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to example.com'
      });

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('sessionId');
    });

    it('should handle execute_task action', async () => {
      // First create a session
      const sessionResult = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to example.com'
      });

      expect(sessionResult).toHaveProperty('sessionId');

      // Then execute a task
      const result = await executeComputerUseTool({
        action: 'execute_task',
        sessionId: sessionResult.sessionId,
        userPrompt: 'Click the login button'
      });

      expect(result).toHaveProperty('sessionId');
      expect(['success', 'safety_check_required', 'confirmation_required'].includes(result.status)).toBe(true);
    });

    it('should handle close_session action', async () => {
      // First create a session
      const sessionResult = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to example.com'
      });

      // Then close it
      const result = await executeComputerUseTool({
        action: 'close_session',
        sessionId: sessionResult.sessionId
      });

      expect(result).toHaveProperty('success', true);
    });

    it('should validate required parameters', async () => {
      // Missing environmentType
      const result1 = await executeComputerUseTool({
        action: 'start_session'
      });
      expect(result1).toHaveProperty('error');

      // Missing userPrompt
      const result2 = await executeComputerUseTool({
        action: 'execute_task'
      });
      expect(result2).toHaveProperty('error');

      // Missing sessionId for close_session
      const result3 = await executeComputerUseTool({
        action: 'close_session'
      });
      expect(result3).toHaveProperty('error');
    });

    it('should handle unknown action', async () => {
      const result = await executeComputerUseTool({
        action: 'unknown_action'
      });

      expect(result).toHaveProperty('error');
      expect(result.error).toContain('Unknown action');
    });
  });

  describe('Integration Tests', () => {
    it('should create and manage multiple sessions', async () => {
      // Create first session
      const session1 = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to site 1'
      });

      // Create second session
      const session2 = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to site 2'
      });

      expect(session1.sessionId).not.toBe(session2.sessionId);

      // Clean up
      await executeComputerUseTool({
        action: 'close_session',
        sessionId: session1.sessionId
      });

      await executeComputerUseTool({
        action: 'close_session',
        sessionId: session2.sessionId
      });
    });

    it('should handle safety checks workflow', async () => {
      // Mock OpenAI to return safety checks
      mockOpenAI.responses.create.mockResolvedValueOnce({
        id: 'test-response-id',
        output: [{
          type: 'computer_call',
          id: 'test-computer-call',
          call_id: 'test-call-id',
          action: {
            type: 'click',
            x: 100,
            y: 200,
            button: 'left'
          },
          pending_safety_checks: [{
            id: 'safety-check-1',
            code: 'sensitive_domain',
            message: 'This domain appears to be sensitive',
            severity: 'medium'
          }] as SafetyCheck[],
          status: 'completed'
        }]
      });

      const session = await executeComputerUseTool({
        action: 'start_session',
        environmentType: 'browser',
        userPrompt: 'Navigate to banking site'
      });

      const result = await executeComputerUseTool({
        action: 'execute_task',
        sessionId: session.sessionId,
        userPrompt: 'Click login button'
      });

      expect(result.status).toBe('safety_check_required');
      expect(result.safetyChecks).toHaveLength(1);
    });
  });
});

describe('Computer Use Store', () => {
  // Note: These tests would require setting up a test environment for Zustand
  // For now, we're focusing on the core functionality tests

  it('should be tested with proper React Testing Library setup', () => {
    // Placeholder for future store tests
    expect(true).toBe(true);
  });
});
