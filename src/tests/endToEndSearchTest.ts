#!/usr/bin/env bun

import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

async function testWebSearch() {
  console.log('🤖 Testing web search tool...');

  const userMessage = "What is the latest news on AI?";
  const messages = convertUIMessagesToAgentFormat([{
    role: 'user',
    content: userMessage
  }]);

  try {
    const stream = await runDante(messages, {
      stream: true,
      maxTurns: 3
    });

    for await (const event of stream) {
      if (event.type === 'tool_call' || event.type === 'tool_call_start') {
        const toolData = event.data as any;
        if (toolData.toolName === 'web_research') {
          console.log(`\n🔨 Tool called: ${toolData.toolName}`);
          console.log(`📋 Args:`, JSON.stringify(toolData.args, null, 2));
        }
      }

      if (event.type === 'tool_call_result' || event.type === 'tool_result') {
        const toolResult = event.data as any;
        if (toolResult.toolName === 'web_research') {
          console.log(`\n📤 Tool result for ${toolResult.toolName}:`);
          console.log(`📄 Result: ${JSON.stringify(toolResult.result, null, 2)}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Web search test failed:', error);
    return false;
  }
  return true;
}

async function main() {
  console.log('🧪 End-to-End Web Search Test');
  console.log('='.repeat(60));

  const success = await testWebSearch();

  console.log('\n' + '='.repeat(60));
  console.log('🎉 TEST COMPLETED');
  console.log(`Result: ${success ? '✅ PASS' : '❌ FAIL'}`);

  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
