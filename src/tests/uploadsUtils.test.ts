import { expect, test } from 'bun:test';
import path from 'path';
import { resolveUploadPublicUrlToPath } from '../api/utils/uploads';

test('resolveUploadPublicUrlToPath handles relative URL with query string', () => {
  const abs = resolveUploadPublicUrlToPath('/uploads/foo.txt?exp=123');
  expect(abs).toBe(path.resolve(process.cwd(), 'uploads', 'foo.txt'));
});

test('resolveUploadPublicUrlToPath handles absolute URL with fragment', () => {
  const abs = resolveUploadPublicUrlToPath('https://example.com/uploads/bar/baz.md#section');
  expect(abs).toBe(path.resolve(process.cwd(), 'uploads', 'bar', 'baz.md'));
});

test('resolveUploadPublicUrlToPath prevents traversal', () => {
  expect(() => resolveUploadPublicUrlToPath('/uploads/../../etc/passwd')).toThrowError('Path traversal detected');
});

test('resolveUploadPublicUrlToPath rejects non-uploads path', () => {
  expect(() => resolveUploadPublicUrlToPath('/not-uploads/file.txt')).toThrowError('uploads public base');
});
