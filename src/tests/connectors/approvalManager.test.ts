import { describe, it, expect, beforeEach, afterEach, jest } from 'bun:test';
import { ApprovalManager } from '../../services/approvalManager';
import Redis from 'ioredis';
import { WebSocketServer } from 'ws';
import WebSocket from 'ws';

describe('ApprovalManager', () => {
  let approvalManager: ApprovalManager;
  let redis: Redis;

  beforeEach(() => {
    redis = new Redis({
      host: 'localhost',
      port: 6379,
      lazyConnect: true,
      enableOfflineQueue: false
    });

    approvalManager = new ApprovalManager({
      redis,
      defaultTimeout: 5000, // 5 seconds for testing
      enableWebSocket: false
    });
  });

  afterEach(async () => {
    await approvalManager.shutdown();
    await redis.quit();
  });

  describe('Approval Request Creation', () => {
    it('should create approval request when policy requires it', async () => {
      // Set policy requiring approval
      approvalManager.setPolicy('test-connector', [{
        connectorId: 'test-connector',
        operationType: 'delete',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 10000
      }]);

      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'test-connector',
        userId: 'user-123',
        operationType: 'delete',
        resource: '/api/resource/123',
        description: 'Delete resource 123',
        metadata: { resourceType: 'document' }
      });

      expect(requestId).toBeDefined();
      expect(requestId).not.toBe('auto-approved');
    });

    it('should auto-approve when no policy requires approval', async () => {
      // No policy set, should auto-approve
      const callback = jest.fn();

      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'no-policy-connector',
        userId: 'user-123',
        operationType: 'read',
        resource: '/api/resource/123',
        description: 'Read resource 123',
        callback
      });

      expect(requestId).toBe('auto-approved');
      expect(callback).toHaveBeenCalledWith(true, 'Auto-approved: No approval required by policy');
    });

    it('should respect priority levels', async () => {
      approvalManager.setPolicy('priority-connector', [{
        connectorId: 'priority-connector',
        operationType: 'all',
        requiresApproval: true,
        requiredRole: 'user',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      const criticalRequest = await approvalManager.createApprovalRequest({
        connectorId: 'priority-connector',
        userId: 'user-123',
        operationType: 'execute',
        resource: '/api/critical',
        description: 'Critical operation',
        priority: 'critical'
      });

      const lowRequest = await approvalManager.createApprovalRequest({
        connectorId: 'priority-connector',
        userId: 'user-456',
        operationType: 'read',
        resource: '/api/normal',
        description: 'Normal operation',
        priority: 'low'
      });

      const queue = approvalManager.getApprovalQueue();

      // Critical should be first
      expect(queue[0].id).toBe(criticalRequest);
      expect(queue[0].priority).toBe('critical');
    });
  });

  describe('Approval and Rejection', () => {
    it('should approve request with sufficient approvers', async () => {
      approvalManager.setPolicy('approval-test', [{
        connectorId: 'approval-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      const callback = jest.fn();
      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'approval-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'Write operation',
        callback
      });

      await approvalManager.approve(requestId, 'admin-user', 'admin', 'Looks good');

      expect(callback).toHaveBeenCalledWith(true, 'Looks good');
    });

    it('should require multiple approvers when configured', async () => {
      approvalManager.setPolicy('multi-approval', [{
        connectorId: 'multi-approval',
        operationType: 'delete',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 2,
        autoRejectAfter: 60000
      }]);

      const callback = jest.fn();
      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'multi-approval',
        userId: 'user-123',
        operationType: 'delete',
        resource: '/api/resource',
        description: 'Delete operation',
        callback
      });

      // First approval shouldn't complete the request
      await approvalManager.approve(requestId, 'admin-1', 'admin', 'First approval');
      expect(callback).not.toHaveBeenCalled();

      // Second approval should complete it
      await approvalManager.approve(requestId, 'admin-2', 'admin', 'Second approval');
      expect(callback).toHaveBeenCalledWith(true, 'Second approval');
    });

    it('should reject request immediately', async () => {
      approvalManager.setPolicy('reject-test', [{
        connectorId: 'reject-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      const callback = jest.fn();
      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'reject-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'Write operation',
        callback
      });

      await approvalManager.reject(requestId, 'admin-user', 'admin', 'Not authorized');

      expect(callback).toHaveBeenCalledWith(false, 'Not authorized');
    });

    it('should prevent duplicate approvals from same user', async () => {
      approvalManager.setPolicy('duplicate-test', [{
        connectorId: 'duplicate-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 2,
        autoRejectAfter: 60000
      }]);

      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'duplicate-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'Write operation'
      });

      await approvalManager.approve(requestId, 'admin-1', 'admin', 'First approval');

      // Second approval from same user should fail
      await expect(
        approvalManager.approve(requestId, 'admin-1', 'admin', 'Duplicate approval')
      ).rejects.toThrow('User does not have permission to approve this request');
    });
  });

  describe('Auto-expiration and Auto-approval', () => {
    it('should auto-reject after timeout', async () => {
      approvalManager.setPolicy('expire-test', [{
        connectorId: 'expire-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 1000 // 1 second
      }]);

      const callback = jest.fn();
      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'expire-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'Write operation',
        callback
      });

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 1500));

      expect(callback).toHaveBeenCalledWith(false, 'Request expired');
    });

    it('should auto-approve after configured time', async () => {
      approvalManager.setPolicy('auto-approve-test', [{
        connectorId: 'auto-approve-test',
        operationType: 'read',
        requiresApproval: true,
        requiredRole: 'user',
        requiredApprovers: 1,
        autoApproveAfter: 1000, // 1 second
        autoRejectAfter: 10000
      }]);

      const callback = jest.fn();
      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'auto-approve-test',
        userId: 'user-123',
        operationType: 'read',
        resource: '/api/resource',
        description: 'Read operation',
        callback
      });

      // Wait for auto-approval
      await new Promise(resolve => setTimeout(resolve, 1500));

      expect(callback).toHaveBeenCalledWith(true, 'Auto-approved after timeout');
    });
  });

  describe('Policy Conditions', () => {
    it('should evaluate policy conditions correctly', async () => {
      approvalManager.setPolicy('conditional-connector', [{
        connectorId: 'conditional-connector',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000,
        conditions: [{
          field: 'amount',
          operator: 'gt',
          value: 1000
        }]
      }]);

      // Request with amount > 1000 should require approval
      const highAmountRequest = await approvalManager.createApprovalRequest({
        connectorId: 'conditional-connector',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/transaction',
        description: 'High value transaction',
        metadata: { amount: 5000 }
      });

      expect(highAmountRequest).not.toBe('auto-approved');

      // Request with amount <= 1000 should auto-approve
      const lowAmountCallback = jest.fn();
      const lowAmountRequest = await approvalManager.createApprovalRequest({
        connectorId: 'conditional-connector',
        userId: 'user-456',
        operationType: 'write',
        resource: '/api/transaction',
        description: 'Low value transaction',
        metadata: { amount: 500 },
        callback: lowAmountCallback
      });

      expect(lowAmountRequest).toBe('auto-approved');
    });

    it('should support multiple condition operators', async () => {
      approvalManager.setPolicy('complex-conditions', [
        {
          connectorId: 'complex-conditions',
          operationType: 'all',
          requiresApproval: true,
          requiredRole: 'user',
          requiredApprovers: 1,
          autoRejectAfter: 60000,
          conditions: [{
            field: 'department',
            operator: 'equals',
            value: 'finance'
          }]
        },
        {
          connectorId: 'complex-conditions',
          operationType: 'all',
          requiresApproval: true,
          requiredRole: 'admin',
          requiredApprovers: 2,
          autoRejectAfter: 60000,
          conditions: [{
            field: 'risk_level',
            operator: 'regex',
            value: 'high|critical'
          }]
        }
      ]);

      // Finance department request
      const financeRequest = await approvalManager.createApprovalRequest({
        connectorId: 'complex-conditions',
        userId: 'user-123',
        operationType: 'read',
        resource: '/api/financial-data',
        description: 'Access financial data',
        metadata: { department: 'finance' }
      });

      expect(financeRequest).not.toBe('auto-approved');

      // High risk request
      const highRiskRequest = await approvalManager.createApprovalRequest({
        connectorId: 'complex-conditions',
        userId: 'user-456',
        operationType: 'delete',
        resource: '/api/critical-resource',
        description: 'Delete critical resource',
        metadata: { risk_level: 'critical' }
      });

      expect(highRiskRequest).not.toBe('auto-approved');
    });
  });

  describe('Audit Logging', () => {
    it('should maintain audit log of all actions', async () => {
      approvalManager.setPolicy('audit-test', [{
        connectorId: 'audit-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'audit-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'Write operation'
      });

      await approvalManager.approve(requestId, 'admin-user', 'admin', 'Approved for testing');

      const auditLog = approvalManager.getAuditLog(10);

      expect(auditLog.length).toBeGreaterThan(0);
      expect(auditLog.some(entry => entry.action === 'created')).toBe(true);
      expect(auditLog.some(entry => entry.action === 'approved')).toBe(true);
    });
  });

  describe('WebSocket Support', () => {
    it('should broadcast updates via WebSocket', async () => {
      const wsManager = new ApprovalManager({
        redis,
        defaultTimeout: 60000,
        enableWebSocket: true,
        port: 8081
      });

      // Wait for server to start
      await new Promise(resolve => setTimeout(resolve, 100));

      const client = new WebSocket('ws://localhost:8081');
      const messages: any[] = [];

      client.on('message', (data) => {
        messages.push(JSON.parse(data.toString()));
      });

      await new Promise(resolve => {
        client.on('open', resolve);
      });

      // Subscribe to updates
      client.send(JSON.stringify({ type: 'subscribe' }));

      // Create an approval request
      wsManager.setPolicy('ws-test', [{
        connectorId: 'ws-test',
        operationType: 'write',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      await wsManager.createApprovalRequest({
        connectorId: 'ws-test',
        userId: 'user-123',
        operationType: 'write',
        resource: '/api/resource',
        description: 'WebSocket test'
      });

      // Wait for message
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(messages.some(m => m.type === 'subscribed')).toBe(true);
      expect(messages.some(m => m.type === 'approval-update')).toBe(true);

      client.close();
      await wsManager.shutdown();
    });
  });

  describe('Role-based Permissions', () => {
    it('should enforce role requirements', async () => {
      approvalManager.setPolicy('role-test', [{
        connectorId: 'role-test',
        operationType: 'delete',
        requiresApproval: true,
        requiredRole: 'super_admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      const requestId = await approvalManager.createApprovalRequest({
        connectorId: 'role-test',
        userId: 'user-123',
        operationType: 'delete',
        resource: '/api/critical',
        description: 'Delete critical resource'
      });

      // User with insufficient role should fail
      await expect(
        approvalManager.approve(requestId, 'regular-user', 'user', 'Trying to approve')
      ).rejects.toThrow('User does not have permission to approve this request');

      // Admin with insufficient role should fail
      await expect(
        approvalManager.approve(requestId, 'admin-user', 'admin', 'Trying to approve')
      ).rejects.toThrow('User does not have permission to approve this request');

      // Super admin should succeed
      await approvalManager.approve(requestId, 'super-admin', 'super_admin', 'Approved');

      // Verify request was approved
      const queue = approvalManager.getApprovalQueue();
      expect(queue.find(r => r.id === requestId)).toBeUndefined();
    });
  });
});
