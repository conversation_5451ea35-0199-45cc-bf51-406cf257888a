import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { ConnectorService } from '../../services/connectorService';
import { OAuthManager } from '../../services/oauthManager';
import { TokenVault } from '../../services/tokenVault';
import { ApprovalManager } from '../../services/approvalManager';
import { RateLimiter } from '../../services/rateLimiter';
import { ConnectorMonitor } from '../../services/connectorMonitor';
import { HealthChecker } from '../../services/healthChecker';
import Redis from 'ioredis';

describe('ConnectorService', () => {
  let connectorService: ConnectorService;
  let oauthManager: OAuthManager;
  let tokenVault: TokenVault;
  let approvalManager: ApprovalManager;
  let rateLimiter: RateLimiter;
  let monitor: ConnectorMonitor;
  let healthChecker: HealthChecker;
  let redis: Redis;

  beforeEach(() => {
    redis = new Redis({
      host: 'localhost',
      port: 6379,
      lazyConnect: true,
      enableOfflineQueue: false
    });

    tokenVault = new TokenVault({
      encryptionKey: 'test-encryption-key-32-chars-long!!',
      redis
    });

    oauthManager = new OAuthManager({
      tokenVault,
      redis
    });

    approvalManager = new ApprovalManager({
      redis,
      defaultTimeout: 60000
    });

    rateLimiter = new RateLimiter({
      redis,
      enableDistributed: true
    });

    monitor = new ConnectorMonitor({
      redis,
      enablePrometheus: false
    });

    healthChecker = new HealthChecker({
      redis,
      enableAutoHealing: true
    });

    connectorService = new ConnectorService({
      oauthManager,
      approvalManager,
      rateLimiter,
      monitor,
      healthChecker
    });
  });

  afterEach(async () => {
    await redis.quit();
  });

  describe('Connector Registration', () => {
    it('should register a new connector', async () => {
      const connector = {
        id: 'test-connector',
        name: 'Test Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read', 'write']
        }
      };

      await connectorService.registerConnector(connector);
      const registered = connectorService.getConnector('test-connector');
      
      expect(registered).toBeDefined();
      expect(registered?.id).toBe('test-connector');
    });

    it('should reject duplicate connector registration', async () => {
      const connector = {
        id: 'duplicate-connector',
        name: 'Duplicate Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read']
        }
      };

      await connectorService.registerConnector(connector);
      
      await expect(
        connectorService.registerConnector(connector)
      ).rejects.toThrow('Connector duplicate-connector already registered');
    });
  });

  describe('OAuth Flow', () => {
    it('should generate authorization URL', async () => {
      const connector = {
        id: 'oauth-connector',
        name: 'OAuth Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read', 'write']
        }
      };

      await connectorService.registerConnector(connector);
      const authUrl = await connectorService.getAuthorizationUrl(
        'oauth-connector',
        'user-123',
        'http://localhost:3000/callback'
      );

      expect(authUrl).toContain('https://example.com/oauth/authorize');
      expect(authUrl).toContain('client_id=test-client-id');
      expect(authUrl).toContain('redirect_uri=');
    });

    it('should handle OAuth callback', async () => {
      const connector = {
        id: 'callback-connector',
        name: 'Callback Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read']
        }
      };

      await connectorService.registerConnector(connector);
      
      // Mock the OAuth token exchange
      jest.spyOn(oauthManager, 'handleCallback').mockResolvedValue({
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      });

      const tokens = await connectorService.handleOAuthCallback(
        'callback-connector',
        'user-123',
        'mock-auth-code',
        'mock-state'
      );

      expect(tokens).toBeDefined();
      expect(tokens.accessToken).toBe('mock-access-token');
    });
  });

  describe('API Execution', () => {
    it('should execute API request with rate limiting', async () => {
      const connector = {
        id: 'api-connector',
        name: 'API Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      // Configure rate limit
      rateLimiter.setRateLimit('api-connector', {
        windowMs: 60000,
        maxRequests: 10,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Mock the actual API call
      jest.spyOn(global, 'fetch').mockResolvedValue(
        new Response(JSON.stringify({ data: 'test' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      );

      const result = await connectorService.executeRequest(
        'api-connector',
        'user-123',
        {
          method: 'GET',
          endpoint: '/test',
          headers: {},
          params: {}
        }
      );

      expect(result).toBeDefined();
      expect(result.data).toBe('test');
    });

    it('should enforce quota limits', async () => {
      const connector = {
        id: 'quota-connector',
        name: 'Quota Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      // Set strict quota
      rateLimiter.setQuota('user-123:quota-connector', {
        daily: 5,
        requests: 5
      });

      // Mock API calls
      jest.spyOn(global, 'fetch').mockResolvedValue(
        new Response(JSON.stringify({ data: 'test' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      );

      // Make requests up to quota
      for (let i = 0; i < 5; i++) {
        await connectorService.executeRequest(
          'quota-connector',
          'user-123',
          {
            method: 'GET',
            endpoint: '/test',
            headers: {},
            params: {}
          }
        );
      }

      // Next request should fail
      await expect(
        connectorService.executeRequest(
          'quota-connector',
          'user-123',
          {
            method: 'GET',
            endpoint: '/test',
            headers: {},
            params: {}
          }
        )
      ).rejects.toThrow('Quota exceeded');
    });
  });

  describe('Approval Workflow', () => {
    it('should require approval for sensitive operations', async () => {
      const connector = {
        id: 'approval-connector',
        name: 'Approval Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['write', 'delete']
        }
      };

      await connectorService.registerConnector(connector);
      
      // Set approval policy
      approvalManager.setPolicy('approval-connector', [{
        connectorId: 'approval-connector',
        operationType: 'delete',
        requiresApproval: true,
        requiredRole: 'admin',
        requiredApprovers: 1,
        autoRejectAfter: 60000
      }]);

      // Mock approval callback
      const approvalPromise = new Promise((resolve) => {
        jest.spyOn(approvalManager, 'createApprovalRequest').mockImplementation(
          async (params) => {
            setTimeout(() => {
              if (params.callback) {
                params.callback(true, 'Approved by admin');
              }
              resolve(true);
            }, 100);
            return 'approval-123';
          }
        );
      });

      const requestPromise = connectorService.executeRequest(
        'approval-connector',
        'user-123',
        {
          method: 'DELETE',
          endpoint: '/resource/123',
          headers: {},
          params: {},
          requiresApproval: true
        }
      );

      await approvalPromise;
      const result = await requestPromise;
      
      expect(result).toBeDefined();
    });
  });

  describe('Health Monitoring', () => {
    it('should perform health checks', async () => {
      const connector = {
        id: 'health-connector',
        name: 'Health Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        },
        healthCheck: async () => true
      };

      await connectorService.registerConnector(connector);
      
      healthChecker.configureHealthCheck({
        connectorId: 'health-connector',
        checkInterval: 10000,
        timeout: 5000,
        retries: 2,
        checks: ['connectivity', 'authentication'],
        thresholds: {
          latency: 1000,
          successRate: 95,
          errorRate: 5
        }
      });

      const status = await healthChecker.performHealthCheck('health-connector');
      
      expect(status).toBeDefined();
      expect(status.connectorId).toBe('health-connector');
      expect(['healthy', 'degraded', 'unhealthy', 'unknown']).toContain(status.status);
    });

    it('should generate diagnostic reports', async () => {
      const connector = {
        id: 'diagnostic-connector',
        name: 'Diagnostic Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read']
        }
      };

      await connectorService.registerConnector(connector);
      
      // Configure and run health check first
      healthChecker.configureHealthCheck({
        connectorId: 'diagnostic-connector',
        checkInterval: 60000,
        timeout: 10000,
        retries: 2,
        checks: ['connectivity', 'authentication', 'performance']
      });

      await healthChecker.performHealthCheck('diagnostic-connector');
      
      const report = await healthChecker.generateDiagnosticReport('diagnostic-connector');
      
      expect(report).toBeDefined();
      expect(report.connectorId).toBe('diagnostic-connector');
      expect(report.checks).toBeInstanceOf(Array);
      expect(report.recommendations).toBeInstanceOf(Array);
    });
  });

  describe('Monitoring and Metrics', () => {
    it('should record metrics for requests', async () => {
      const connector = {
        id: 'metrics-connector',
        name: 'Metrics Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      // Mock successful request
      jest.spyOn(global, 'fetch').mockResolvedValue(
        new Response(JSON.stringify({ data: 'test' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      );

      await connectorService.executeRequest(
        'metrics-connector',
        'user-123',
        {
          method: 'GET',
          endpoint: '/test',
          headers: {},
          params: {}
        }
      );

      // Check that metrics were recorded
      const report = await rateLimiter.getUsageReport(
        'user-123',
        'metrics-connector',
        new Date(Date.now() - 3600000),
        new Date()
      );

      expect(report.totalRequests).toBeGreaterThan(0);
    });

    it('should track error rates', async () => {
      const connector = {
        id: 'error-connector',
        name: 'Error Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      // Mock failed request
      jest.spyOn(global, 'fetch').mockRejectedValue(
        new Error('Connection failed')
      );

      try {
        await connectorService.executeRequest(
          'error-connector',
          'user-123',
          {
            method: 'GET',
            endpoint: '/test',
            headers: {},
            params: {}
          }
        );
      } catch (error) {
        // Expected error
      }

      const report = await rateLimiter.getUsageReport(
        'user-123',
        'error-connector',
        new Date(Date.now() - 3600000),
        new Date()
      );

      expect(report.errorRate).toBeGreaterThan(0);
    });
  });

  describe('Token Management', () => {
    it('should securely store tokens', async () => {
      const token = {
        accessToken: 'sensitive-access-token',
        refreshToken: 'sensitive-refresh-token',
        expiresAt: new Date(Date.now() + 3600000)
      };

      await tokenVault.storeToken('user-123', 'secure-connector', token);
      const retrieved = await tokenVault.getToken('user-123', 'secure-connector');
      
      expect(retrieved).toBeDefined();
      expect(retrieved?.accessToken).toBe('sensitive-access-token');
    });

    it('should refresh expired tokens', async () => {
      const connector = {
        id: 'refresh-connector',
        name: 'Refresh Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read']
        }
      };

      await connectorService.registerConnector(connector);
      
      // Store expired token
      await tokenVault.storeToken('user-123', 'refresh-connector', {
        accessToken: 'old-access-token',
        refreshToken: 'valid-refresh-token',
        expiresAt: new Date(Date.now() - 1000) // Expired
      });

      // Mock token refresh
      jest.spyOn(oauthManager, 'refreshAccessToken').mockResolvedValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600
      });

      const newToken = await connectorService.ensureValidToken(
        'refresh-connector',
        'user-123'
      );
      
      expect(newToken).toBeDefined();
      expect(newToken?.accessToken).toBe('new-access-token');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const connector = {
        id: 'network-connector',
        name: 'Network Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      // Mock network error
      jest.spyOn(global, 'fetch').mockRejectedValue(
        new Error('Network error: Connection refused')
      );

      await expect(
        connectorService.executeRequest(
          'network-connector',
          'user-123',
          {
            method: 'GET',
            endpoint: '/test',
            headers: {},
            params: {}
          }
        )
      ).rejects.toThrow('Network error');
    });

    it('should handle authentication errors', async () => {
      const connector = {
        id: 'auth-error-connector',
        name: 'Auth Error Connector',
        type: 'oauth' as const,
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          authorizationUrl: 'https://example.com/oauth/authorize',
          tokenUrl: 'https://example.com/oauth/token',
          scope: ['read']
        }
      };

      await connectorService.registerConnector(connector);
      
      // Mock 401 response
      jest.spyOn(global, 'fetch').mockResolvedValue(
        new Response('Unauthorized', {
          status: 401,
          statusText: 'Unauthorized'
        })
      );

      await expect(
        connectorService.executeRequest(
          'auth-error-connector',
          'user-123',
          {
            method: 'GET',
            endpoint: '/test',
            headers: {},
            params: {}
          }
        )
      ).rejects.toThrow('Authentication failed');
    });

    it('should retry failed requests', async () => {
      const connector = {
        id: 'retry-connector',
        name: 'Retry Connector',
        type: 'api_key' as const,
        config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'X-API-Key': 'test-api-key'
          },
          retryConfig: {
            maxRetries: 3,
            retryDelay: 100
          }
        }
      };

      await connectorService.registerConnector(connector);
      
      let attempts = 0;
      jest.spyOn(global, 'fetch').mockImplementation(async () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Temporary failure');
        }
        return new Response(JSON.stringify({ data: 'success' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      });

      const result = await connectorService.executeRequest(
        'retry-connector',
        'user-123',
        {
          method: 'GET',
          endpoint: '/test',
          headers: {},
          params: {}
        }
      );

      expect(attempts).toBe(3);
      expect(result.data).toBe('success');
    });
  });
});