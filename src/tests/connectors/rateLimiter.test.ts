import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { RateLimiter } from '../../services/rateLimiter';
import Redis from 'ioredis';

describe('RateLimiter', () => {
  let rateLimiter: RateLimiter;
  let redis: Redis;

  beforeEach(() => {
    redis = new Redis({
      host: 'localhost',
      port: 6379,
      lazyConnect: true,
      enableOfflineQueue: false
    });

    rateLimiter = new RateLimiter({
      redis,
      enableDistributed: true,
      alertThreshold: 80,
      gracefulDegradation: true,
      costPerToken: 0.002
    });
  });

  afterEach(async () => {
    await rateLimiter.shutdown();
    await redis.quit();
  });

  describe('Sliding Window Rate Limiting', () => {
    it('should allow requests within rate limit', async () => {
      rateLimiter.setRateLimit('test-api', {
        windowMs: 60000, // 1 minute
        maxRequests: 10,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Make 5 requests
      for (let i = 0; i < 5; i++) {
        const result = await rateLimiter.checkRateLimit('test-api');
        expect(result.allowed).toBe(true);
        expect(result.remaining).toBeGreaterThan(0);
      }

      // Should still have 5 remaining
      const status = await rateLimiter.checkRateLimit('test-api', 0);
      expect(status.remaining).toBe(5);
    });

    it('should block requests exceeding rate limit', async () => {
      rateLimiter.setRateLimit('strict-api', {
        windowMs: 60000,
        maxRequests: 3,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Make 3 requests (up to limit)
      for (let i = 0; i < 3; i++) {
        const result = await rateLimiter.checkRateLimit('strict-api');
        expect(result.allowed).toBe(true);
      }

      // 4th request should be blocked
      const blocked = await rateLimiter.checkRateLimit('strict-api');
      expect(blocked.allowed).toBe(false);
      expect(blocked.remaining).toBe(0);
    });

    it('should support weighted requests', async () => {
      rateLimiter.setRateLimit('weighted-api', {
        windowMs: 60000,
        maxRequests: 10,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Make a request with weight 5
      const result1 = await rateLimiter.checkRateLimit('weighted-api', 5);
      expect(result1.allowed).toBe(true);
      expect(result1.remaining).toBe(5);

      // Make another request with weight 5
      const result2 = await rateLimiter.checkRateLimit('weighted-api', 5);
      expect(result2.allowed).toBe(true);
      expect(result2.remaining).toBe(0);

      // Next request should be blocked
      const result3 = await rateLimiter.checkRateLimit('weighted-api', 1);
      expect(result3.allowed).toBe(false);
    });
  });

  describe('Token Bucket Algorithm', () => {
    it('should allow burst traffic within capacity', async () => {
      rateLimiter.setTokenBucket('burst-api', {
        capacity: 10,
        refillRate: 1, // 1 token per second
        initialTokens: 10
      });

      // Consume 10 tokens in burst
      for (let i = 0; i < 10; i++) {
        const result = await rateLimiter.consumeTokens('burst-api', 1);
        expect(result.allowed).toBe(true);
      }

      // 11th request should be blocked
      const blocked = await rateLimiter.consumeTokens('burst-api', 1);
      expect(blocked.allowed).toBe(false);
    });

    it('should refill tokens over time', async () => {
      rateLimiter.setTokenBucket('refill-api', {
        capacity: 5,
        refillRate: 2, // 2 tokens per second
        initialTokens: 0
      });

      // Initially no tokens
      const initial = await rateLimiter.consumeTokens('refill-api', 1);
      expect(initial.allowed).toBe(false);

      // Wait 1 second for refill
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Should have ~2 tokens now
      const afterRefill = await rateLimiter.consumeTokens('refill-api', 2);
      expect(afterRefill.allowed).toBe(true);
    });

    it('should handle multiple token consumption', async () => {
      rateLimiter.setTokenBucket('multi-token-api', {
        capacity: 20,
        refillRate: 5,
        initialTokens: 20
      });

      // Consume 15 tokens at once
      const result1 = await rateLimiter.consumeTokens('multi-token-api', 15);
      expect(result1.allowed).toBe(true);
      expect(result1.remaining).toBe(5);

      // Try to consume 10 more (should fail)
      const result2 = await rateLimiter.consumeTokens('multi-token-api', 10);
      expect(result2.allowed).toBe(false);
      expect(result2.remaining).toBe(5);
    });
  });

  describe('Quota Management', () => {
    it('should enforce daily quotas', async () => {
      rateLimiter.setQuota('user-123:quota-api', {
        daily: 100,
        requests: 100
      });

      // Check initial quota
      const initial = await rateLimiter.checkQuota('user-123', 'quota-api', {
        requests: 0
      });
      expect(initial.allowed).toBe(true);

      // Record usage
      for (let i = 0; i < 50; i++) {
        await rateLimiter.recordUsage('user-123', 'quota-api', {
          requests: 1
        });
      }

      // Check remaining quota
      const midway = await rateLimiter.checkQuota('user-123', 'quota-api', {
        requests: 1
      });
      expect(midway.allowed).toBe(true);
      expect(midway.remaining.daily?.requests).toBe(50);

      // Use up remaining quota
      for (let i = 0; i < 50; i++) {
        await rateLimiter.recordUsage('user-123', 'quota-api', {
          requests: 1
        });
      }

      // Should be at limit
      const exhausted = await rateLimiter.checkQuota('user-123', 'quota-api', {
        requests: 1
      });
      expect(exhausted.allowed).toBe(false);
    });

    it('should enforce token quotas', async () => {
      rateLimiter.setQuota('user-456:token-api', {
        daily: 10000,
        tokens: 10000
      });

      // Use 5000 tokens
      await rateLimiter.recordUsage('user-456', 'token-api', {
        tokens: 5000
      });

      const status = await rateLimiter.checkQuota('user-456', 'token-api', {
        tokens: 1000
      });
      expect(status.allowed).toBe(true);
      expect(status.remaining.daily?.tokens).toBe(5000);

      // Try to use 6000 more tokens (should fail)
      const exceeded = await rateLimiter.checkQuota('user-456', 'token-api', {
        tokens: 6000
      });
      expect(exceeded.allowed).toBe(false);
    });

    it('should enforce cost quotas', async () => {
      rateLimiter.setQuota('user-789:cost-api', {
        daily: 1000, // $10.00 in cents
        cost: 1000
      });

      // Use $5.00
      await rateLimiter.recordUsage('user-789', 'cost-api', {
        tokens: 2500 // 2500 * 0.002 = $5.00
      });

      const status = await rateLimiter.checkQuota('user-789', 'cost-api', {
        cost: 100 // $1.00
      });
      expect(status.allowed).toBe(true);
      expect(status.remaining.daily?.cost).toBe(500);

      // Try to use $6.00 more (should fail)
      const exceeded = await rateLimiter.checkQuota('user-789', 'cost-api', {
        cost: 600
      });
      expect(exceeded.allowed).toBe(false);
    });

    it('should track usage across multiple periods', async () => {
      rateLimiter.setQuota('multi-period:api', {
        daily: 100,
        weekly: 500,
        monthly: 2000,
        requests: 100
      });

      // Use 50 requests
      for (let i = 0; i < 50; i++) {
        await rateLimiter.recordUsage('multi-period', 'api', {
          requests: 1
        });
      }

      const status = await rateLimiter.checkQuota('multi-period', 'api', {
        requests: 1
      });

      expect(status.remaining.daily?.requests).toBe(50);
      expect(status.remaining.weekly?.requests).toBe(450);
      expect(status.remaining.monthly?.requests).toBe(1950);
    });
  });

  describe('Alert Thresholds', () => {
    it('should emit alert when threshold exceeded', async () => {
      const alertHandler = jest.fn();
      rateLimiter.on('quota-alert', alertHandler);

      rateLimiter.setQuota('alert-test:api', {
        daily: 100,
        requests: 100
      });

      // Use 81% of quota (should trigger 80% alert)
      for (let i = 0; i < 81; i++) {
        await rateLimiter.recordUsage('alert-test', 'api', {
          requests: 1
        });
      }

      await rateLimiter.checkQuota('alert-test', 'api', { requests: 1 });

      // Wait for alert processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(alertHandler).toHaveBeenCalled();
      const alert = alertHandler.mock.calls[0][0];
      expect(alert.userId).toBe('alert-test');
      expect(alert.percentUsed).toBeGreaterThanOrEqual(80);
    });

    it('should not send duplicate alerts', async () => {
      const alertHandler = jest.fn();
      rateLimiter.on('quota-alert', alertHandler);

      rateLimiter.setQuota('duplicate-alert:api', {
        daily: 100,
        requests: 100
      });

      // Use 85% of quota
      for (let i = 0; i < 85; i++) {
        await rateLimiter.recordUsage('duplicate-alert', 'api', {
          requests: 1
        });
      }

      // Check quota multiple times
      for (let i = 0; i < 5; i++) {
        await rateLimiter.checkQuota('duplicate-alert', 'api', { requests: 1 });
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Should only have one alert
      expect(alertHandler).toHaveBeenCalledTimes(1);
    });
  });

  describe('Graceful Degradation', () => {
    it('should apply degradation when rate limit exceeded', async () => {
      const degradationHandler = jest.fn();
      rateLimiter.on('degradation-applied', degradationHandler);

      rateLimiter.setRateLimit('degradation-api', {
        windowMs: 60000,
        maxRequests: 3,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Exceed rate limit
      for (let i = 0; i < 4; i++) {
        await rateLimiter.checkRateLimit('degradation-api');
      }

      // Wait for degradation processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(degradationHandler).toHaveBeenCalled();
      const degradation = degradationHandler.mock.calls[0][0];
      expect(degradation.identifier).toBe('degradation-api');
      expect(['reduce-features', 'increase-cache-ttl', 'disable-real-time', 'queue-requests'])
        .toContain(degradation.strategy);
    });
  });

  describe('Usage Reporting', () => {
    it('should generate usage reports', async () => {
      // Record some usage
      await rateLimiter.recordUsage('report-user', 'report-api', {
        requests: 5,
        tokens: 1000,
        latency: 250
      });

      await rateLimiter.recordUsage('report-user', 'report-api', {
        requests: 3,
        tokens: 500,
        latency: 150,
        error: true
      });

      const report = await rateLimiter.getUsageReport(
        'report-user',
        'report-api',
        new Date(Date.now() - 3600000),
        new Date()
      );

      expect(report.totalRequests).toBe(8);
      expect(report.totalTokens).toBe(1500);
      expect(report.totalCost).toBe(3); // 1500 * 0.002
      expect(report.averageLatency).toBe(200); // (250 + 150) / 2
      expect(report.errorRate).toBe(37.5); // 3/8 * 100
    });

    it('should generate cost breakdown', async () => {
      // Record usage for different connectors
      await rateLimiter.recordUsage('cost-user', 'api-1', {
        tokens: 1000
      });

      await rateLimiter.recordUsage('cost-user', 'api-2', {
        tokens: 2000
      });

      const breakdown = await rateLimiter.getCostBreakdown('cost-user', 'daily');
      
      expect(breakdown.get('api-1')).toBe(2); // 1000 * 0.002
      expect(breakdown.get('api-2')).toBe(4); // 2000 * 0.002
    });
  });

  describe('Distributed Rate Limiting', () => {
    it('should share limits across instances', async () => {
      // Create two rate limiter instances sharing same Redis
      const limiter1 = new RateLimiter({
        redis,
        enableDistributed: true
      });

      const limiter2 = new RateLimiter({
        redis,
        enableDistributed: true
      });

      // Set rate limit on first instance
      limiter1.setRateLimit('distributed-api', {
        windowMs: 60000,
        maxRequests: 5,
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      });

      // Use 3 requests on first instance
      for (let i = 0; i < 3; i++) {
        const result = await limiter1.checkRateLimit('distributed-api');
        expect(result.allowed).toBe(true);
      }

      // Check on second instance - should see 2 remaining
      const status = await limiter2.checkRateLimit('distributed-api', 0);
      expect(status.remaining).toBe(2);

      // Use remaining on second instance
      for (let i = 0; i < 2; i++) {
        const result = await limiter2.checkRateLimit('distributed-api');
        expect(result.allowed).toBe(true);
      }

      // Both instances should now block
      const blocked1 = await limiter1.checkRateLimit('distributed-api');
      const blocked2 = await limiter2.checkRateLimit('distributed-api');
      
      expect(blocked1.allowed).toBe(false);
      expect(blocked2.allowed).toBe(false);

      await limiter1.shutdown();
      await limiter2.shutdown();
    });
  });

  describe('Express Middleware', () => {
    it('should create rate limiting middleware', () => {
      const middleware = rateLimiter.createMiddleware('express-api');
      
      expect(middleware).toBeDefined();
      expect(typeof middleware).toBe('function');
    });
  });

  describe('Performance Metrics', () => {
    it('should track latency metrics', async () => {
      // Record various latencies
      const latencies = [100, 200, 150, 300, 250, 180, 220, 400, 350, 120];
      
      for (const latency of latencies) {
        await rateLimiter.recordUsage('perf-user', 'perf-api', {
          requests: 1,
          latency
        });
      }

      const report = await rateLimiter.getUsageReport(
        'perf-user',
        'perf-api'
      );

      expect(report.averageLatency).toBe(227); // Average of all latencies
      expect(report.timeline.length).toBe(10);
    });
  });
});