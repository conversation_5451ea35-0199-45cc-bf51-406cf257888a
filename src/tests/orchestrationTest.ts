#!/usr/bin/env bun

import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

async function testOrchestration() {
  console.log('Testing Dante Orchestration with Proper Message Format\n');
  console.log('=' .repeat(60));
  
  // Test 1: Simple greeting
  console.log('\nTest 1: Simple greeting');
  const test1Messages = [
    { role: 'user', content: 'Hey Dante!' }
  ];
  
  try {
    const converted1 = convertUIMessagesToAgentFormat(test1Messages);
    const result1 = await runDante(converted1, { stream: false, maxTurns: 1 });
    console.log('✓ Success:', result1.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 2: Conversation with history
  console.log('\nTest 2: Conversation with assistant messages');
  const test2Messages = [
    { role: 'user', content: 'Hey <PERSON>!' },
    { role: 'assistant', content: 'Hello! How can I assist you today?' },
    { role: 'user', content: 'What can you help me with?' }
  ];
  
  try {
    const converted2 = convertUIMessagesToAgentFormat(test2Messages);
    console.log('Converted format:', JSON.stringify(converted2, null, 2));
    const result2 = await runDante(converted2, { stream: false, maxTurns: 1 });
    console.log('✓ Success:', result2.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 3: Test agent handoff (weather query)
  console.log('\nTest 3: Agent handoff test (weather)');
  const test3Messages = [
    { role: 'user', content: 'What is the weather in San Francisco?' }
  ];
  
  try {
    const converted3 = convertUIMessagesToAgentFormat(test3Messages);
    const result3 = await runDante(converted3, { stream: false, maxTurns: 3 });
    console.log('✓ Success:', result3.finalOutput);
    console.log('Final agent:', (result3 as any).finalAgent?.name || 'Unknown');
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 4: Code generation request
  console.log('\nTest 4: Code generation request');
  const test4Messages = [
    { role: 'user', content: 'Write a simple Python hello world function' }
  ];
  
  try {
    const converted4 = convertUIMessagesToAgentFormat(test4Messages);
    const result4 = await runDante(converted4, { stream: false, maxTurns: 3 });
    console.log('✓ Success:', result4.finalOutput);
    console.log('Final agent:', (result4 as any).finalAgent?.name || 'Unknown');
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('Orchestration tests complete!');
}

// Run the test
testOrchestration().catch(console.error);