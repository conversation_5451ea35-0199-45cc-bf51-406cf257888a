import { describe, test, expect, beforeEach } from 'bun:test';
import { webSearchTool } from '../tools/webSearch';
import { fetchWebContent } from '../tools/webSearchHelpers';
import { mockNewsArticleHTML, mockSearchResults } from './fixtures/webContent';

// Minimal mock helper for environments where bun:test.mock is unavailable
const mock = <T extends (...args: any[]) => any>(fn: T) => {
  let impl: (...args: any[]) => any = fn;
  const m: any = (...args: any[]) => impl(...args);
  m.mockClear = () => { /* noop for compatibility */ };
  m.mockImplementation = (newImpl: (...args: any[]) => any) => { impl = newImpl; };
  m.mockResolvedValue = (val: any) => { impl = () => Promise.resolve(val); };
  return m as T & { mockClear: () => void; mockImplementation: (newImpl: (...args: any[]) => any) => void; mockResolvedValue: (val: any) => void };
};

// Performance benchmark utilities
interface BenchmarkResult {
  operation: string;
  iterations: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  memoryUsage?: number;
}

async function benchmark(
  operation: string, 
  fn: () => Promise<any>, 
  iterations: number = 10
): Promise<BenchmarkResult> {
  const times: number[] = [];
  let totalTime = 0;

  // Warm up
  await fn();

  // Measure performance
  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    await fn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    times.push(duration);
    totalTime += duration;
  }

  return {
    operation,
    iterations,
    totalTime,
    averageTime: totalTime / iterations,
    minTime: Math.min(...times),
    maxTime: Math.max(...times),
    memoryUsage: process.memoryUsage().heapUsed
  };
}

// Mock setup for consistent benchmarking
const mockFetch = mock(() => Promise.resolve({
  ok: true,
  status: 200,
  headers: new Map([['content-type', 'text/html']]),
  text: () => Promise.resolve(mockNewsArticleHTML)
}));

global.fetch = mockFetch as any;

describe('Chunking Performance Benchmarks', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    console.error = mock(() => {}); // Suppress benchmark logs
  });

  describe('Content Fetching Performance', () => {
    test('should benchmark basic content fetching vs chunking', async () => {
      const basicFetchBenchmark = await benchmark(
        'Basic Content Fetch',
        async () => {
          return await fetchWebContent('https://example.com', true, {
            enableChunking: false,
            contentDepth: 'detailed'
          });
        },
        5
      );

      const chunkingFetchBenchmark = await benchmark(
        'Chunking Content Fetch',
        async () => {
          return await fetchWebContent('https://example.com', true, {
            enableChunking: true,
            contentDepth: 'detailed'
          });
        },
        5
      );

      console.log('Basic Fetch Performance:', basicFetchBenchmark);
      console.log('Chunking Fetch Performance:', chunkingFetchBenchmark);

      // Chunking should not add more than 50% overhead
      const overhead = (chunkingFetchBenchmark.averageTime - basicFetchBenchmark.averageTime) / basicFetchBenchmark.averageTime;
      expect(overhead).toBeLessThan(0.5);

      // Both should complete within reasonable time
      expect(basicFetchBenchmark.averageTime).toBeLessThan(1000); // 1 second
      expect(chunkingFetchBenchmark.averageTime).toBeLessThan(1500); // 1.5 seconds
    });

    test('should benchmark different content depths', async () => {
      const depths = ['summary', 'detailed', 'full'] as const;
      const results: BenchmarkResult[] = [];

      for (const depth of depths) {
        const result = await benchmark(
          `Content Depth: ${depth}`,
          async () => {
            return await fetchWebContent('https://example.com', true, {
              enableChunking: true,
              contentDepth: depth
            });
          },
          3
        );
        results.push(result);
      }

      console.log('Content Depth Performance:', results);

      // Performance should not degrade significantly with depth
      results.forEach(result => {
        expect(result.averageTime).toBeLessThan(2000);
      });

      // Summary should be fastest, full should be slowest
      expect(results[0].averageTime).toBeLessThanOrEqual(results[1].averageTime);
      expect(results[1].averageTime).toBeLessThanOrEqual(results[2].averageTime);
    });
  });

  describe('Search Tool Performance', () => {
    test('should benchmark search without content fetching', async () => {
      const searchBenchmark = await benchmark(
        'Basic Search',
        async () => {
          return await (webSearchTool as any).execute({
            query: 'performance test',
            maxResults: 3,
            fetchContent: false
          });
        },
        5
      );

      console.log('Basic Search Performance:', searchBenchmark);

      expect(searchBenchmark.averageTime).toBeLessThan(2000); // 2 seconds
      expect(searchBenchmark.maxTime).toBeLessThan(5000); // 5 seconds max
    });

    test('should benchmark search with content fetching and chunking', async () => {
      const searchWithContentBenchmark = await benchmark(
        'Search with Content + Chunking',
        async () => {
          return await (webSearchTool as any).execute({
            query: 'performance test with content',
            maxResults: 3,
            fetchContent: true,
            contentDepth: 'detailed',
            enableChunking: true
          });
        },
        3
      );

      console.log('Search with Content + Chunking Performance:', searchWithContentBenchmark);

      expect(searchWithContentBenchmark.averageTime).toBeLessThan(5000); // 5 seconds
      expect(searchWithContentBenchmark.maxTime).toBeLessThan(10000); // 10 seconds max
    });

    test('should benchmark different result counts', async () => {
      const resultCounts = [1, 3, 5];
      const results: BenchmarkResult[] = [];

      for (const count of resultCounts) {
        const result = await benchmark(
          `Results: ${count}`,
          async () => {
            return await (webSearchTool as any).execute({
              query: 'test query',
              maxResults: count,
              fetchContent: true,
              enableChunking: true
            });
          },
          3
        );
        results.push(result);
      }

      console.log('Result Count Performance:', results);

      // Performance should scale roughly linearly
      expect(results[2].averageTime).toBeGreaterThan(results[0].averageTime);
      
      // But should not be more than 3x slower for 5x results
      const scalingFactor = results[2].averageTime / results[0].averageTime;
      expect(scalingFactor).toBeLessThan(3);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    test('should monitor memory usage during chunking operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform multiple chunking operations
      for (let i = 0; i < 5; i++) {
        await fetchWebContent('https://example.com', true, {
          enableChunking: true,
          contentDepth: 'full'
        });
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      console.log('Memory Usage:', {
        initial: initialMemory,
        final: finalMemory,
        increase: memoryIncrease,
        increasePercentage: (memoryIncrease / initialMemory) * 100
      });

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    test('should benchmark memory efficiency of chunking vs full content', async () => {
      const measureMemoryUsage = async (operation: () => Promise<any>) => {
        const before = process.memoryUsage().heapUsed;
        await operation();
        const after = process.memoryUsage().heapUsed;
        return after - before;
      };

      const fullContentMemory = await measureMemoryUsage(async () => {
        for (let i = 0; i < 3; i++) {
          await fetchWebContent('https://example.com', true, {
            enableChunking: false,
            contentDepth: 'full'
          });
        }
      });

      const chunkingMemory = await measureMemoryUsage(async () => {
        for (let i = 0; i < 3; i++) {
          await fetchWebContent('https://example.com', true, {
            enableChunking: true,
            contentDepth: 'summary'
          });
        }
      });

      console.log('Memory Efficiency:', {
        fullContent: fullContentMemory,
        chunking: chunkingMemory,
        efficiency: ((fullContentMemory - chunkingMemory) / fullContentMemory) * 100
      });

      // Chunking with summary should use less memory
      expect(chunkingMemory).toBeLessThanOrEqual(fullContentMemory);
    });
  });

  describe('Concurrent Operation Performance', () => {
    test('should handle concurrent search requests efficiently', async () => {
      const concurrentOperations = 5;
      const startTime = performance.now();

      const promises = Array.from({ length: concurrentOperations }, (_, i) => 
        (webSearchTool as any).execute({
          query: `concurrent test ${i}`,
          maxResults: 2,
          fetchContent: true,
          enableChunking: true
        })
      );

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      console.log('Concurrent Operations Performance:', {
        operations: concurrentOperations,
        totalTime,
        averageTimePerOperation: totalTime / concurrentOperations,
        successfulOperations: results.filter(r => {
          try {
            return JSON.parse(r).success;
          } catch {
            return false;
          }
        }).length
      });

      // All operations should complete
      expect(results.length).toBe(concurrentOperations);
      
      // Concurrent execution should be more efficient than sequential
      expect(totalTime).toBeLessThan(concurrentOperations * 3000); // Less than 3s per operation
    });

    test('should maintain performance under load', async () => {
      const iterations = 10;
      const times: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        await (webSearchTool as any).execute({
          query: `load test ${i}`,
          maxResults: 2,
          fetchContent: true,
          enableChunking: true
        });

        const endTime = performance.now();
        times.push(endTime - startTime);
      }

      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      const standardDeviation = Math.sqrt(
        times.reduce((sq, time) => sq + Math.pow(time - averageTime, 2), 0) / times.length
      );

      console.log('Load Test Performance:', {
        iterations,
        averageTime,
        standardDeviation,
        minTime: Math.min(...times),
        maxTime: Math.max(...times),
        variability: (standardDeviation / averageTime) * 100
      });

      // Performance should be consistent (low variability)
      expect(standardDeviation / averageTime).toBeLessThan(0.5); // Less than 50% variability
      expect(averageTime).toBeLessThan(3000); // Average under 3 seconds
    });
  });

  describe('Content Processing Performance', () => {
    test('should benchmark different HTML sizes', async () => {
      const htmlSizes = [
        mockNewsArticleHTML, // Original size
        mockNewsArticleHTML.repeat(2), // 2x size
        mockNewsArticleHTML.repeat(5)  // 5x size
      ];

      const results: BenchmarkResult[] = [];

      for (let i = 0; i < htmlSizes.length; i++) {
        const html = htmlSizes[i];
        const mockLargeFetch = mock(() => Promise.resolve({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'text/html']]),
          text: () => Promise.resolve(html)
        }));

        global.fetch = mockLargeFetch as any;

        const result = await benchmark(
          `HTML Size: ${i + 1}x`,
          async () => {
            return await fetchWebContent('https://example.com', true, {
              enableChunking: true,
              contentDepth: 'detailed'
            });
          },
          3
        );
        results.push(result);
      }

      console.log('HTML Size Performance:', results);

      // Processing time should scale sub-linearly with content size
      const scalingFactor = results[2].averageTime / results[0].averageTime;
      expect(scalingFactor).toBeLessThan(10); // Should not be 10x slower for 5x content

      // Restore original fetch
      global.fetch = mockFetch as any;
    });
  });

  describe('Performance Regression Detection', () => {
    test('should establish performance baselines', async () => {
      const baselines = {
        basicSearch: await benchmark('Basic Search Baseline', async () => {
          return await (webSearchTool as any).execute({
            query: 'baseline test',
            maxResults: 3,
            fetchContent: false
          });
        }, 5),

        searchWithContent: await benchmark('Search with Content Baseline', async () => {
          return await (webSearchTool as any).execute({
            query: 'baseline test',
            maxResults: 3,
            fetchContent: true,
            enableChunking: false
          });
        }, 3),

        searchWithChunking: await benchmark('Search with Chunking Baseline', async () => {
          return await (webSearchTool as any).execute({
            query: 'baseline test',
            maxResults: 3,
            fetchContent: true,
            enableChunking: true
          });
        }, 3)
      };

      console.log('Performance Baselines:', baselines);

      // Store baselines for future regression testing
      expect(baselines.basicSearch.averageTime).toBeLessThan(2000);
      expect(baselines.searchWithContent.averageTime).toBeLessThan(4000);
      expect(baselines.searchWithChunking.averageTime).toBeLessThan(5000);
    });
  });
});