// import { run } from '@openai/agents'; // Temporarily disabled during migration

// This test file is temporarily disabled during the migration to Vercel AI SDK
console.log('Test temporarily disabled during OpenAI SDK to Vercel AI SDK migration');

/* Original test content temporarily commented out:
import { debugAgent } from '../agents/DebugAgent';
import { formatReasoning, extractReasoning } from '../agents/withReasoning';
import chalk from 'chalk';
import { config } from 'dotenv';

config();

async function testReasoningAgent() {
  console.log(chalk.cyan('Testing Enhanced Debug Agent with Reasoning Output\n'));
  console.log('=' .repeat(60));

  // Test case: A common JavaScript error
  const testCase = {
    errorDescription: 'Getting "Cannot read property \'length\' of undefined" error',
    context: {
      errorMessage: 'TypeError: Cannot read property \'length\' of undefined',
      codeSnippet: `
function processItems(data) {
  const items = data.items;
  for (let i = 0; i < items.length; i++) {
    console.log(items[i]);
  }
}

// Called with:
processItems({});
      `.trim(),
      stackTrace: `
TypeError: Cannot read property 'length' of undefined
  at processItems (index.js:3:29)
  at Object.<anonymous> (index.js:9:1)
      `.trim()
    }
  };

  const input = `
Debug this issue:
${testCase.errorDescription}

Error Message: ${testCase.context.errorMessage}

Code:
${testCase.context.codeSnippet}

Stack Trace:
${testCase.context.stackTrace}
  `.trim();

  try {
    console.log(chalk.yellow('Input to Agent:'));
    console.log(input);
    console.log('\n' + '=' .repeat(60) + '\n');

    console.log(chalk.green('Running agent with reasoning...'));

    // Run the reasoning agent
    const result = await run(debugAgent, [{
      role: 'user',
      content: input
    }]);

    console.log('\n' + '=' .repeat(60) + '\n');

    // Extract and display reasoning
    const reasoningData = extractReasoning(result);

    if (reasoningData) {
      console.log(chalk.cyan('Formatted Reasoning Output:'));
      console.log(formatReasoning(reasoningData));

      console.log('\n' + '=' .repeat(60) + '\n');

      console.log(chalk.green('Solution Data:'));
      console.log(JSON.stringify(reasoningData.data, null, 2));
    } else {
      console.log(chalk.yellow('No structured reasoning found in response'));
      console.log('Raw response:', result);
    }

  } catch (error) {
    console.error(chalk.red('Error:'), error);
  }
}

// Test a simpler agent without structured output to show regular reasoning
async function testSimpleReasoning() {
  console.log('\n\n');
  console.log(chalk.cyan('Testing Simple Agent Response\n'));
  console.log('=' .repeat(60));

  // Create a simple agent that explains its reasoning in text
  // const { Agent } = await import('@openai/agents'); // Disabled during migration

  const simpleReasoningAgent = new Agent({
    name: 'Simple Reasoning Agent',
    model: 'gpt-4o',
    instructions: `You are a helpful assistant that explains your reasoning process.

When answering questions:
1. Start with "REASONING:" and explain your thought process
2. Then provide "ANSWER:" with your final response
3. End with "CONFIDENCE:" and rate your confidence (low/medium/high)

Always think step-by-step and show your work.`
  });

  const question = 'If I have a React component that renders 1000 items in a list and users are complaining about performance, what are the most likely causes and solutions?';

  console.log(chalk.yellow('Question:'), question);
  console.log('\n' + '=' .repeat(60) + '\n');

  const result = await run(simpleReasoningAgent, [{
    role: 'user',
    content: question
  }]);

  console.log(chalk.green('Agent Response:'));
  console.log(result);
}

// Run both tests
async function runTests() {
  try {
    await testReasoningAgent();
    await testSimpleReasoning();
  } catch (error) {
    console.error(chalk.red('Test failed:'), error);
  }
}

runTests().catch(console.error);

*/
