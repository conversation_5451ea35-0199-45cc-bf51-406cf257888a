/**
 * Tests and examples for the ResponsesAPIClient
 * Demonstrates production usage patterns
 */

import { describe, test, expect, beforeEach, afterEach } from 'bun:test';
import { 
  ResponsesAPIClient,
  ProcessedResponse,
  examples
} from '../services/responsesAPIClient';
import { ConnectorId } from '../types/connectors';

describe('ResponsesAPIClient', () => {
  let client: ResponsesAPIClient;
  let sessionId: string;

  beforeEach(() => {
    // Create client with test configuration
    client = new ResponsesAPIClient({
      enableLogging: false,
      defaultModel: 'gpt-5',
      maxRetries: 2,
      timeout: 60000
    });
    
    // Create a test session
    sessionId = client.createSession('test-session');
  });

  afterEach(() => {
    // Clean up sessions
    client.clearAllSessions();
  });

  describe('Session Management', () => {
    test('should create and retrieve sessions', () => {
      const newSessionId = client.createSession();
      expect(newSessionId).toBeTruthy();
      
      const session = client.getSession(newSessionId);
      expect(session).toBeTruthy();
      expect(session?.id).toBe(newSessionId);
    });

    test('should store OAuth tokens in session', () => {
      const token = {
        access_token: 'test-token-123',
        token_type: 'Bearer',
        scope: 'test.scope',
        expires_at: Date.now() + 3600000
      };

      client.storeOAuthToken(sessionId, 'connector_gmail', token);
      
      const session = client.getSession(sessionId);
      expect(session?.oauth_tokens.get('connector_gmail')).toEqual(token);
    });

    test('should clear individual sessions', () => {
      const cleared = client.clearSession(sessionId);
      expect(cleared).toBe(true);
      
      const session = client.getSession(sessionId);
      expect(session).toBeUndefined();
    });
  });

  describe('Connector Validation', () => {
    test('should validate valid connector IDs', () => {
      const result = client.validateConnectorConfig('connector_gmail');
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject invalid connector IDs', () => {
      const result = client.validateConnectorConfig('invalid_connector' as ConnectorId);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid connector ID: invalid_connector');
    });

    test('should validate OAuth token formats', () => {
      // Valid Google OAuth token
      const googleResult = client.validateConnectorConfig('connector_gmail', 'ya29.test-token');
      expect(googleResult.valid).toBe(true);

      // Valid JWT token
      const jwtResult = client.validateConnectorConfig('connector_dropbox', 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9');
      expect(jwtResult.valid).toBe(true);

      // Invalid token format
      const invalidResult = client.validateConnectorConfig('connector_gmail', 'invalid-token');
      expect(invalidResult.errors).toContain('OAuth token format appears invalid');
    });
  });

  describe('Connector Tools', () => {
    test('should return correct tools for Gmail', () => {
      const tools = client.getConnectorTools('connector_gmail');
      expect(tools).toContain('search_emails');
      expect(tools).toContain('read_email');
      expect(tools).toContain('get_profile');
    });

    test('should return correct tools for Google Calendar', () => {
      const tools = client.getConnectorTools('connector_googlecalendar');
      expect(tools).toContain('search_events');
      expect(tools).toContain('read_event');
      expect(tools).toContain('get_profile');
    });

    test('should return correct tools for Dropbox', () => {
      const tools = client.getConnectorTools('connector_dropbox');
      expect(tools).toContain('search');
      expect(tools).toContain('fetch');
      expect(tools).toContain('get_profile');
    });
  });

  describe('Request Processing', () => {
    test('should handle missing OAuth token gracefully', async () => {
      try {
        await client.executeConnectorRequest({
          sessionId,
          connector_id: 'connector_gmail',
          input: 'Test request',
          require_approval: 'never'
        });
        expect(true).toBe(false); // Should not reach here
      } catch (error: any) {
        expect(error.message).toContain('OAuth token required');
      }
    });

    test('should validate MCP server URLs', async () => {
      try {
        await client.executeMCPServerRequest({
          sessionId,
          server_url: 'invalid-url',
          server_label: 'Test Server',
          input: 'Test request'
        });
        expect(true).toBe(false); // Should not reach here
      } catch (error: any) {
        expect(error.message).toContain('Invalid server URL');
      }
    });
  });

  describe('Response Processing', () => {
    test('should safely parse JSON in responses', () => {
      // This is tested internally by the client
      // We're testing the behavior through the public API
      const testSession = client.createSession();
      expect(testSession).toBeTruthy();
    });
  });
});

describe('ResponsesAPIClient Examples', () => {
  test.skip('Gmail connector example (requires OAuth token)', async () => {
    // This test is skipped in CI but can be run locally with a valid token
    const oauthToken = process.env.TEST_GMAIL_OAUTH_TOKEN;
    if (!oauthToken) {
      console.log('Skipping Gmail test - no OAuth token provided');
      return;
    }

    const response = await examples.gmailExample(oauthToken);
    expect(response).toBeTruthy();
    expect(response.output_text).toBeTruthy();
  });

  test.skip('MCP server with approval example', async () => {
    // This test demonstrates the approval workflow
    const response = await examples.mcpServerWithApproval();
    expect(response).toBeTruthy();
    
    if (response.approval_requests.length > 0) {
      console.log('Approval requests:', response.approval_requests);
    }
  });

  test.skip('Batch connector requests example', async () => {
    // This test is skipped in CI but can be run locally with valid tokens
    const gmailToken = process.env.TEST_GMAIL_OAUTH_TOKEN;
    const calendarToken = process.env.TEST_CALENDAR_OAUTH_TOKEN;
    
    if (!gmailToken || !calendarToken) {
      console.log('Skipping batch test - OAuth tokens not provided');
      return;
    }

    const results = await examples.batchConnectorRequests(gmailToken, calendarToken);
    expect(results).toHaveLength(2);
    expect(results[0].output_text).toBeTruthy();
    expect(results[1].output_text).toBeTruthy();
  });
});

describe('ResponsesAPIClient Integration', () => {
  test('should create a complete workflow session', () => {
    const client = new ResponsesAPIClient({ enableLogging: false });
    
    // Create session
    const sessionId = client.createSession('workflow-test');
    
    // Store multiple OAuth tokens
    client.storeOAuthToken(sessionId, 'connector_gmail', {
      access_token: 'gmail-token',
      token_type: 'Bearer',
      scope: 'gmail.modify'
    });
    
    client.storeOAuthToken(sessionId, 'connector_googlecalendar', {
      access_token: 'calendar-token',
      token_type: 'Bearer',
      scope: 'calendar.events'
    });
    
    // Verify session state
    const session = client.getSession(sessionId);
    expect(session?.oauth_tokens.size).toBe(2);
    expect(session?.oauth_tokens.get('connector_gmail')).toBeTruthy();
    expect(session?.oauth_tokens.get('connector_googlecalendar')).toBeTruthy();
    
    // Clear session
    client.clearSession(sessionId);
    expect(client.getSession(sessionId)).toBeUndefined();
  });

  test('should handle expired tokens correctly', async () => {
    const client = new ResponsesAPIClient({ enableLogging: false });
    const sessionId = client.createSession();
    
    // Store expired token
    client.storeOAuthToken(sessionId, 'connector_gmail', {
      access_token: 'expired-token',
      token_type: 'Bearer',
      scope: 'gmail.modify',
      expires_at: Date.now() - 1000 // Already expired
    });
    
    // Try to use expired token
    await expect(
      client.executeConnectorRequest({
        sessionId,
        connector_id: 'connector_gmail',
        input: 'Test with expired token'
      })
    ).rejects.toThrow('expired');
  });
});

// Performance tests
describe('ResponsesAPIClient Performance', () => {
  test('should handle multiple sessions efficiently', () => {
    const client = new ResponsesAPIClient({ enableLogging: false });
    const sessionIds: string[] = [];
    
    // Create 100 sessions
    const startTime = Date.now();
    for (let i = 0; i < 100; i++) {
      const id = client.createSession(`perf-test-${i}`);
      sessionIds.push(id);
    }
    const createTime = Date.now() - startTime;
    
    // Should create 100 sessions in under 100ms
    expect(createTime).toBeLessThan(100);
    
    // Verify all sessions exist
    for (const id of sessionIds) {
      expect(client.getSession(id)).toBeTruthy();
    }
    
    // Clean up
    const cleanupStart = Date.now();
    client.clearAllSessions();
    const cleanupTime = Date.now() - cleanupStart;
    
    // Should clear all sessions in under 10ms
    expect(cleanupTime).toBeLessThan(10);
  });

  test('should validate connectors quickly', () => {
    const client = new ResponsesAPIClient({ enableLogging: false });
    const connectors: ConnectorId[] = [
      'connector_gmail',
      'connector_googlecalendar',
      'connector_googledrive',
      'connector_dropbox',
      'connector_microsoftteams',
      'connector_outlookcalendar',
      'connector_outlookemail',
      'connector_sharepoint'
    ];
    
    const startTime = Date.now();
    for (const connector of connectors) {
      const result = client.validateConnectorConfig(connector);
      expect(result.valid).toBe(true);
    }
    const validationTime = Date.now() - startTime;
    
    // Should validate all connectors in under 10ms
    expect(validationTime).toBeLessThan(10);
  });
});
