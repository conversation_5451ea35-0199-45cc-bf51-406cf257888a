import { describe, it, expect } from 'bun:test';
import { ThoughtStream, ThoughtMessage } from '../utils/ThoughtStream';

function collect(iter: AsyncIterable<ThoughtMessage>, limit = 50): Promise<ThoughtMessage[]> {
  return new Promise(async (resolve) => {
    const out: ThoughtMessage[] = [];
    try {
      for await (const m of iter) {
        out.push(m);
        if (out.length >= limit) break;
      }
    } catch {
      // ignore
    }
    resolve(out);
  });
}

describe('ThoughtStream', () => {
  it('emits ordered start/delta/complete with seq per key', async () => {
    const ts = new ThoughtStream({ dedupeWindowMs: 100 });
    const ac = new AbortController();
    const sub = ts.subscribe({ signal: ac.signal });

    ts.emitThought({ promptId: 'p1', agentId: 'A', stepId: 's1', stage: 'start', content: 'begin' });
    ts.emitThought({ promptId: 'p1', agentId: 'A', stepId: 's1', stage: 'delta', content: 'hello' });
    ts.emitThought({ promptId: 'p1', agentId: 'A', stepId: 's1', stage: 'complete', content: 'done' });

    const events = await collect(sub, 3);
    ac.abort();

    expect(events.length).toBe(3);
    expect(events[0].stage).toBe('start');
    expect(events[1].stage).toBe('delta');
    expect(events[2].stage).toBe('complete');
    // seq should increase monotonically for same key
    expect(events[0].seq).toBeLessThan(events[2].seq);
  });

  it('dedupes repeated delta content within window', async () => {
    const ts = new ThoughtStream({ dedupeWindowMs: 500 });
    const ac = new AbortController();
    const sub = ts.subscribe({ signal: ac.signal });

    ts.emitThought({ promptId: 'p2', agentId: 'B', stepId: 's2', stage: 'delta', content: 'same' });
    ts.emitThought({ promptId: 'p2', agentId: 'B', stepId: 's2', stage: 'delta', content: 'same' });
    ts.emitThought({ promptId: 'p2', agentId: 'B', stepId: 's2', stage: 'delta', content: 'same' });

    const events = await collect(sub, 1);
    ac.abort();

    expect(events.length).toBe(1);
    expect(events[0].content).toBe('same');
  });

  it('supports nesting via pipeFrom', async () => {
    const parent = new ThoughtStream();
    const child = new ThoughtStream();
    const ac = new AbortController();
    const sub = parent.subscribe({ signal: ac.signal });

    // Pipe and map to attach parentId
    parent.pipeFrom(child, (m) => ({ ...m, parentId: m.stepId }));

    child.emitThought({ promptId: 'p3', agentId: 'C', stepId: 's3', stage: 'start' });
    child.emitThought({ promptId: 'p3', agentId: 'C', stepId: 's3', stage: 'delta', content: 'child delta' });
    child.emitThought({ promptId: 'p3', agentId: 'C', stepId: 's3', stage: 'complete' });

    const events = await collect(sub, 3);
    ac.abort();

    expect(events.length).toBe(3);
    expect(events[0].parentId).toBe('s3');
    expect(events[1].content).toContain('child delta');
  });

  it('cancels subscription via AbortController', async () => {
    const ts = new ThoughtStream();
    const ac = new AbortController();
    const sub = ts.subscribe({ signal: ac.signal });

    // queue one event then cancel
    ts.emitThought({ promptId: 'p4', agentId: 'D', stepId: 's4', stage: 'start' });
    const eventsPromise = collect(sub, 5);
    ac.abort();
    const events = await eventsPromise;

    expect(events.length).toBeGreaterThanOrEqual(1);
  });
});

