import { computerUseDispatcher } from '../tools/computerUseDispatcher';
import { ComputerUseTaskManager } from '../agents/vercel/************************';

describe('Isolated Computer Use System', () => {
  test('Dispatch and monitor YouTube playlist task', async () => {
    console.log('🎵 Testing YouTube playlist automation with isolation...');

    // Step 1: Dispatch task
    const dispatchResult = await computerUseDispatcher.execute({
      action: 'dispatch_task',
      taskType: 'playlist_play',
      target: 'https://music.youtube.com',
      objective: 'Navigate to music.youtube.com and play the first available playlist',
      customInstructions: [
        'Look for playlists, mixes, or auto-play content on the homepage',
        'Click on the first available playlist or mix',
        'Click the play button to start playback',
        'Verify that music is playing in the browser',
        'Report the playlist name and current playback status'
      ]
    });

    console.log('📤 Dispatch result:', dispatchResult);
    const dispatchData = JSON.parse(dispatchResult);

    expect(dispatchData.success).toBe(true);
    expect(dispatchData.taskId).toBeDefined();

    const taskId = dispatchData.taskId;
    console.log('🆔 Task ID:', taskId);

    // Step 2: Wait a moment then check status
    await new Promise(resolve => setTimeout(resolve, 2000));

    const statusResult = await computerUseDispatcher.execute({
      action: 'check_status',
      taskId: taskId
    });

    console.log('📊 Status result:', statusResult);
    const statusData = JSON.parse(statusResult);

    expect(statusData.success).toBe(true);
    expect(['pending', 'running', 'completed', 'failed', 'ongoing']).toContain(statusData.status);

    // Step 3: List active tasks
    const activeTasksResult = await computerUseDispatcher.execute({
      action: 'list_active_tasks'
    });

    console.log('📋 Active tasks result:', activeTasksResult);
    const activeTasksData = JSON.parse(activeTasksResult);

    expect(activeTasksData.success).toBe(true);
    expect(Array.isArray(activeTasksData.activeTasks)).toBe(true);

    // Step 4: Get final result (may not be ready yet)
    const resultResult = await computerUseDispatcher.execute({
      action: 'get_result',
      taskId: taskId
    });

    console.log('📄 Final result:', resultResult);
    const resultData = JSON.parse(resultResult);

    expect(resultData.success).toBe(true);
    expect(resultData.taskId).toBe(taskId);

    console.log('✅ Isolated computer use system test completed');
  }, 30000);

  test('Facebook login check with isolation', async () => {
    console.log('👤 Testing Facebook login check with isolation...');

    // Dispatch Facebook login check task
    const dispatchResult = await computerUseDispatcher.execute({
      action: 'dispatch_task',
      taskType: 'website_check',
      target: 'https://www.facebook.com',
      objective: 'Check if user is currently logged in to Facebook',
      customInstructions: [
        'Navigate to facebook.com',
        'Wait for page to fully load',
        'Analyze page content for login indicators',
        'Look for profile photo, name, or account menu (logged in)',
        'Look for login form or "Log In" button (logged out)',
        'Take screenshot of current state',
        'Provide definitive YES or NO answer about login status'
      ]
    });

    console.log('📤 Facebook dispatch result:', dispatchResult);
    const dispatchData = JSON.parse(dispatchResult);

    expect(dispatchData.success).toBe(true);
    expect(dispatchData.taskId).toBeDefined();

    const taskId = dispatchData.taskId;

    // Check status immediately
    const statusResult = await computerUseDispatcher.execute({
      action: 'check_status',
      taskId: taskId
    });

    console.log('📊 Facebook status result:', statusResult);
    const statusData = JSON.parse(statusResult);

    expect(statusData.success).toBe(true);
    expect(statusData.taskId).toBe(taskId);

    console.log('✅ Facebook login check isolation test completed');
  }, 30000);

  test('Task cleanup and management', async () => {
    console.log('🧹 Testing task cleanup and management...');

    // Create a simple task
    const task = ComputerUseTaskManager.createTask({
      type: 'website_check',
      target: 'https://example.com',
      objective: 'Test task for cleanup'
    });

    expect(task.id).toBeDefined();
    expect(task.status).toBe('pending');

    // Update task status
    ComputerUseTaskManager.updateTaskStatus(task.id, 'completed', 'Task completed successfully');

    const updatedTask = ComputerUseTaskManager.getTask(task.id);
    expect(updatedTask?.status).toBe('completed');
    expect(updatedTask?.result).toBe('Task completed successfully');

    // Test active tasks retrieval
    const activeTasks = ComputerUseTaskManager.getActiveTasks();
    console.log('📋 Active tasks count:', activeTasks.length);

    // Test cleanup (this won't remove the task since it's recent)
    ComputerUseTaskManager.cleanupOldTasks(0.001); // Very short time for testing

    console.log('✅ Task management test completed');
  });
});
