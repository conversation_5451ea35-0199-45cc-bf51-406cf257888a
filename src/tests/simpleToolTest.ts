#!/usr/bin/env bun

/**
 * Simple tool test to understand tool structure and outputs
 */

import { searchCodeTool } from '../tools/fileOperations';
import { sanitizeToolOutput } from '../mcp/utils';

async function testSearchTool() {
  console.log('🔍 Testing search_code tool directly...');
  console.log('Tool type:', typeof searchCodeTool);
  console.log('Tool structure:', Object.keys(searchCodeTool));
  
  if (searchCodeTool && typeof searchCodeTool === 'object' && 'invoke' in searchCodeTool) {
    console.log('✅ Tool has invoke method');
    
    try {
      const result = await (searchCodeTool as any).invoke({
        pattern: 'gpt-4o',
        path: '/Users/<USER>/dante-gpt',
        fileTypes: ['ts', 'js'],
        caseSensitive: false
      });
      
      console.log('\n📊 Raw tool result:');
      console.log('Success:', result.success);
      console.log('Matches:', result.matches);
      console.log('Truncated:', result.truncated);
      console.log('Results count:', result.results?.length);
      
      if (result.results && result.results.length > 0) {
        console.log('\n📋 First few matches:');
        result.results.slice(0, 3).forEach((match: any, i: number) => {
          console.log(`${i + 1}. ${match.file}:${match.line} - ${match.match.substring(0, 80)}`);
        });
      }
      
      // Test sanitization
      console.log('\n🧹 Testing sanitizeToolOutput:');
      const original = JSON.stringify(result, null, 2);
      console.log('Original length:', original.length, 'characters');
      
      const sanitized = sanitizeToolOutput(result);
      console.log('Sanitized length:', sanitized.length, 'characters');
      console.log('Truncation occurred:', sanitized.includes('truncated'));
      
      // Try to parse sanitized result
      try {
        const parsed = JSON.parse(sanitized);
        console.log('✅ Sanitized result is valid JSON');
        console.log('Data integrity - success:', parsed.success === result.success);
        console.log('Data integrity - matches:', parsed.matches === result.matches);
      } catch (e) {
        console.log('❌ Sanitized result is not valid JSON');
        console.log('Error:', e);
      }
      
    } catch (error) {
      console.error('❌ Tool execution failed:', error);
    }
    
  } else {
    console.log('❌ Tool does not have invoke method');
    console.log('Available methods:', Object.keys(searchCodeTool));
  }
}

async function main() {
  console.log('🧪 Simple Tool Structure Test');
  console.log('=' .repeat(50));
  
  await testSearchTool();
  
  console.log('\n✅ Test completed');
}

main().catch(console.error);