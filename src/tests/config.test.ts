import { describe, test, expect } from 'bun:test';

function runValidator(env: Record<string, string | undefined>) {
  // Use the provided env exactly, but keep PATH so <PERSON><PERSON> can run.
  const baseEnv: Record<string, string | undefined> = {
    ...(env || {}),
    PATH: process.env.PATH,
  };

  // Bun.spawnSync expects string values; normalize undefined to empty string.
  const normalizedEnv: Record<string, string> = {};
  for (const [k, v] of Object.entries(baseEnv)) {
    normalizedEnv[k] = v === undefined ? '' : String(v);
  }

  const res = Bun.spawnSync({
    cmd: ["bun", "src/tests/validateConfigRunner.ts"],
    env: normalizedEnv,
    stdout: "pipe",
    stderr: "pipe",
  });
  const dec = new TextDecoder();
  return {
    exitCode: res.exitCode,
    stdout: dec.decode(res.stdout as Uint8Array),
    stderr: dec.decode(res.stderr as Uint8Array),
  };
}

describe('validateConfig (subprocess)', () => {
  test('throws when OPENAI_API_KEY is not set', () => {
    const env = { ...process.env } as Record<string, string | undefined>;
    delete env.OPENAI_API_KEY;

    const result = runValidator(env);
    // Expect non-zero exit code and a JSON error message on stderr
    // When OPENAI_API_KEY is unset, our runner returns exitCode 1 to indicate failure
    expect(result.exitCode).toBe(1);
    expect(result.stderr).toContain('OPENAI_API_KEY is required');
  });

  test('does not throw when OPENAI_API_KEY is set', () => {
    const env = { ...process.env, OPENAI_API_KEY: 'test-key' } as Record<string, string | undefined>;
    const result = runValidator(env);
    expect(result.exitCode).toBe(0);
    // Bun's JSON output doesn't include spaces; check for the compact form
    expect(result.stdout).toContain('{"ok":true');
    expect(result.stdout).toContain('test-key');
  });
});
