import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { TwoPhaseCommitCoordinator } from '../../agents/orchestration/OrchestrationEngine';
import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import { LockManager } from '../../agents/orchestration/locks/LockManager';
import {
  CoordinatorTransactionContext,
  CoordinatorState,
  TwoPhaseParticipant,
} from '../../agents/orchestration/types';

async function withTempDir(fn: (dir: string) => Promise<void>) {
  const dir = await fs.mkdtemp(path.join(tmpdir(), 'coord-'));
  try {
    await fn(dir);
  } finally {
    await fs.rm(dir, { recursive: true, force: true });
  }
}

describe('TwoPhaseCommitCoordinator recovery', () => {
  it('replays commit notifications after crash post-decision', async () => {
    await withTempDir(async (dir) => {
      const journal = new RunJournal(path.join(dir, 'journal'));
      const lockManager = new LockManager({ persistDir: path.join(dir, 'locks') });
      await lockManager.init();

      const coordinator = new TwoPhaseCommitCoordinator({
        runId: 'run-recover',
        journal,
        lockManager,
        config: {
          prepareTimeoutMs: 2000,
          commitAckTimeoutMs: 2000,
          lockLeaseMs: 4000,
          lockRenewIntervalMs: 500,
        },
      });

      const runId = 'run-recover';
      const taskId = 'task-1';
      const txnId = 'txn-3';
      const resourceKeys = ['repo:demo|branch:main|path:src/component.ts'];

      // Simulate prior execution up to decision persisted (commit) with no commit ack
      await journal.recordPrepareSent({
        runId,
        taskId,
        txnId,
        seqNo: 1,
        participantId: 'codegen',
        resourceKeys,
      });
      await journal.recordPrepareAck({
        runId,
        taskId,
        txnId,
        seqNo: 2,
        participantId: 'codegen',
        vote: 'yes',
        respondedAt: new Date().toISOString(),
      });
      await journal.recordDecisionPersisted({
        runId,
        taskId,
        txnId,
        seqNo: 3,
        decision: 'commit',
        resourceKeys,
        participants: ['codegen'],
      });

      const markers: string[] = [];

      const participant: TwoPhaseParticipant = {
        id: 'codegen',
        agentId: 'vercel-codegen',
        prepare: async () => ({ vote: 'yes', respondedAt: new Date().toISOString() }),
        commit: async () => {
          markers.push('commit-called');
          return { ok: true, commitSha: 'cafebabe', respondedAt: new Date().toISOString() };
        },
        abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
      };

      const ctx: CoordinatorTransactionContext = {
        runId,
        taskId,
        txnId,
        stepId: 'step-recover',
        participants: [participant],
        resourceKeys,
      };

      const result = await coordinator.recover(ctx);

      expect(result.state).toBe(CoordinatorState.Committed);
      expect(result.commitAcks.codegen?.ok).toBe(true);
      expect(markers).toEqual(['commit-called']);

      const events = await journal.listProtocolEvents(runId, { taskId, txnId });
      const types = events.map((evt) => evt.type);
      expect(types.filter((t) => t === 'commit_sent').length).toBeGreaterThan(0);
      expect(types.filter((t) => t === 'commit_ack').length).toBeGreaterThan(0);
      expect(result.locksReleased).toEqual(resourceKeys);
    });
  });
});
