import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { LockManager } from '../../agents/orchestration/locks/LockManager';

async function withTempDir(fn: (dir: string) => Promise<void>) {
  const dir = await fs.mkdtemp(path.join(tmpdir(), 'lockmgr-'));
  try {
    await fn(dir);
  } finally {
    await fs.rm(dir, { recursive: true, force: true });
  }
}

describe('LockManager', () => {
  it('acquires and releases locks honoring queueing', async () => {
    await withTempDir(async (dir) => {
      const lm = new LockManager({ persistDir: dir });
      await lm.init();

      const key = 'repo:demo|branch:main|path:src/index.ts';

      const res1 = await lm.acquire('txnA', [key], 5000, 1_000);
      expect(res1.ok).toBe(true);
      expect(res1.granted).toEqual([key]);

      const res2 = await lm.acquire('txnB', [key], 5000, 2_000);
      expect(res2.ok).toBe(false);
      expect(res2.pending).toEqual([key]);

      await lm.release('txnA');

      const res3 = await lm.acquire('txnB', [key], 5000, 2_000);
      expect(res3.ok).toBe(true);
      expect(res3.granted).toEqual([key]);
    });
  });

  it('expires leases and allows new transactions after TTL', async () => {
    await withTempDir(async (dir) => {
      let now = 0;
      const clock = () => now;
      const lm = new LockManager({ persistDir: dir, clock });
      await lm.init();

      const key = 'repo:demo|branch:main|path:src/ttl.ts';
      await lm.acquire('txnA', [key], 100, 1_000);

      now = 150; // advance past expiry
      const res = await lm.acquire('txnB', [key], 100, 2_000);
      expect(res.ok).toBe(true);
      expect(res.granted).toEqual([key]);
    });
  });

  it('signals wound-wait when older txn requests held key', async () => {
    await withTempDir(async (dir) => {
      const lm = new LockManager({ persistDir: dir });
      await lm.init();

      const key = 'repo:demo|branch:main|path:src/wound.ts';
      await lm.acquire('youngTxn', [key], 5000, 5_000);

      const res = await lm.acquire('oldTxn', [key], 5000, 1_000);
      expect(res.ok).toBe(false);
      expect(res.waitReason).toBe('wound-wait');
      expect(res.wounded).toEqual(['youngTxn']);
      expect(res.pending).toEqual([key]);
    });
  });

  it('promotes queued requests fairly with aging', async () => {
    await withTempDir(async (dir) => {
      let now = 0;
      const clock = () => now;
      const lm = new LockManager({ persistDir: dir, clock, agingHalfLifeMs: 50 });
      await lm.init();

      const key = 'repo:demo|branch:main|path:src/fair.ts';
      await lm.acquire('holder', [key], 1_000, 1_000);

      await lm.acquire('first', [key], 1_000, 9_000);
      now += 500; // allow first to age significantly
      await lm.acquire('second', [key], 1_000, 2_000);

      // Release holder; first has worse priority but has been waiting longer with aging factor
      await lm.release('holder');

      const resFirst = await lm.acquire('first', [key], 1_000, 9_000);
      expect(resFirst.ok).toBe(true);
      expect(resFirst.granted).toEqual([key]);

      await lm.release('first');
      const resSecond = await lm.acquire('second', [key], 1_000, 2_000);
      expect(resSecond.ok).toBe(true);
      expect(resSecond.granted).toEqual([key]);
    });
  });

  it('restores state from persisted snapshot', async () => {
    await withTempDir(async (dir) => {
      const key = 'repo:demo|branch:main|path:src/snap.ts';

      const lm1 = new LockManager({ persistDir: dir });
      await lm1.init();
      await lm1.acquire('txnPersist', [key], 5_000, 1_000);

      const snapshot1 = await lm1.snapshot();
      expect(snapshot1.held[key]?.txnId).toBe('txnPersist');

      const lm2 = new LockManager({ persistDir: dir });
      await lm2.init();
      const snapshot2 = await lm2.snapshot();
      expect(snapshot2.held[key]?.txnId).toBe('txnPersist');
    });
  });
});
