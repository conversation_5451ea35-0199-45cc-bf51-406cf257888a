import { describe, it, expect } from 'bun:test';
import { validateCheckpointRequired, CheckpointIncompleteError, CheckpointState, NextAction } from '../../agents/orchestration/journal/types';

describe('Checkpoint validation', () => {
  it('throws CheckpointIncompleteError when required fields are missing', () => {
    const cp: any = {
      runId: 'r1',
      taskId: 't1',
      stepId: 's1',
      agent: 'agentA',
      timestamps: { createdAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      // scope missing fileScopes
      scope: {},
    };
    expect(() => validateCheckpointRequired(cp)).toThrow(CheckpointIncompleteError);
    try {
      validateCheckpointRequired(cp);
    } catch (e: any) {
      expect(e.code).toBe('CheckpointIncomplete');
      expect(e.details?.missing).toContain('scope.fileScopes');
    }
  });

  it('passes when required fields are present', () => {
    const cp: any = {
      runId: 'r1',
      taskId: 't1',
      stepId: 's1',
      agent: 'agentA',
      timestamps: { createdAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/index.ts'] },
    };
    expect(() => validateCheckpointRequired(cp)).not.toThrow();
  });
});
