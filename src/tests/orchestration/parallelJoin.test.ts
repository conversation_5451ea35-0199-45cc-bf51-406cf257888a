import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { AgentRegistry } from '../../agents/orchestration/agents/AgentRegistry';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  AssignmentExecutor,
  WorkScheduler,
  TwoPhasePartitionContext,
} from '../../agents/orchestration/scheduler/WorkScheduler';
import {
  PartitionPlan,
  WorkUnit,
  WorkCategory,
  Intent,
  OrchestrationRequest,
  ProgressEventType,
  CoordinatorState,
  TwoPhaseParticipant,
} from '../../agents/orchestration/types';
import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import type { ConcurrencyPartitionDescriptor, ConcurrencyPartitionResult } from '../../agents/orchestration/concurrency/ConcurrencyManager';
import type { ConcurrencyParticipantSpec } from '../../agents/orchestration/concurrency/ConcurrencyManager';
import DiffJoiner from '../../agents/orchestration/concurrency/DiffJoiner';
import { ProtocolEventType, JoinMergedEvent } from '../../agents/orchestration/journal/types';

function makeCodegenParticipant(id: string): TwoPhaseParticipant {
  return {
    id,
    agentId: 'vercel-codegen',
    prepare: async () => ({ vote: 'yes', respondedAt: new Date().toISOString() }),
    commit: async () => ({ ok: true, commitSha: null, respondedAt: new Date().toISOString() }),
    abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
  };
}

describe('ConcurrencyManager join integration', () => {
  it('merges disjoint partitions and records join events deterministically', async () => {
    const tmp = await fs.mkdtemp(path.join(tmpdir(), 'parallel-join-'));
    const journal = new RunJournal(tmp);

    const registry = new AgentRegistry({
      defaultAgents: [
        {
          id: 'vercel-codegen',
          name: 'Code Generation',
          capabilities: ['typescript'],
          maxTokenContext: 4096,
          concurrencyLimit: 2,
          categoryAffinity: [WorkCategory.Coding],
        },
      ],
    });
    const packager = new ContextPackager({ tokenCap: 512 });
    const executor: AssignmentExecutor = async function* () {
      // Two-phase executor path short-circuits before reaching the provided executor.
    };

    const diffA = `--- a/src/a.ts\n+++ b/src/a.ts\n@@ -0,0 +1,1 @@\n+console.log('a');\n`;
    const diffB = `--- a/src/b.ts\n+++ b/src/b.ts\n@@ -0,0 +1,1 @@\n+console.log('b');\n`;

    const participantSpecs: ConcurrencyParticipantSpec[] = [
      {
        id: 'vercel-codegen#a',
        participant: makeCodegenParticipant('vercel-codegen#a'),
        resourceKeys: ['repo:workspace|branch:main|path:src/a.ts'],
        priorityTs: Date.now(),
        metadata: { previewDiff: diffA, patchPlanHash: 'hash-a' },
      },
      {
        id: 'vercel-codegen#b',
        participant: makeCodegenParticipant('vercel-codegen#b'),
        resourceKeys: ['repo:workspace|branch:main|path:src/b.ts'],
        priorityTs: Date.now(),
        metadata: { previewDiff: diffB, patchPlanHash: 'hash-b' },
      },
    ];

    const twoPhaseOptions = {
      enabled: true,
      parallelEnabled: true,
      maxParallel: 2,
      conflictPolicy: 'resolve-prefer-older' as const,
      journal,
      deriveParticipants: async () =>
        participantSpecs.map((spec) => ({
          id: spec.id,
          participant: spec.participant,
          resourceKeys: [...spec.resourceKeys],
          priorityTs: spec.priorityTs,
          metadata: spec.metadata,
        })),
      executePartition: async (
        partition: ConcurrencyPartitionDescriptor,
        ctx: TwoPhasePartitionContext,
      ): Promise<ConcurrencyPartitionResult> => {
        const meta = (partition.participants[0]?.metadata ?? {}) as { previewDiff?: string; patchPlanHash?: string };
        const participantId = partition.participants[0]?.id ?? 'vercel-codegen';
        return {
          partitionId: partition.id,
          decision: 'commit',
          coordinator: {
            state: CoordinatorState.Committed,
            decision: 'commit',
            prepareAcks: {
              [participantId]: {
                vote: 'yes',
                respondedAt: new Date().toISOString(),
                payload: { previewDiff: meta.previewDiff, patchPlanHash: meta.patchPlanHash },
              },
            },
            commitAcks: {
              [participantId]: { ok: true, commitSha: null, respondedAt: new Date().toISOString() },
            },
            abortAcks: {},
            locksReleased: partition.resourceKeys,
          },
          previewDiff: meta.previewDiff ?? null,
          patchPlanHash: meta.patchPlanHash ?? null,
          participantIds: partition.participants.map((p) => p.id),
        };
      },
    };

    const scheduler = new WorkScheduler({
      agentRegistry: registry,
      contextPackager: packager,
      executor,
      twoPhase: twoPhaseOptions,
    });

    const runId = 'parallel-run';
    const workUnit: WorkUnit = {
      id: 'wu-parallel',
      category: WorkCategory.Coding,
      scope: { files: ['src/a.ts', 'src/b.ts'] },
      intent: Intent.Implement,
      acceptanceCriteria: [''],
      dependencies: [],
      priority: 1,
      status: 'pending' as any,
      createdAt: Date.now(),
    };

    const plan: PartitionPlan = {
      id: 'plan-parallel',
      requestId: runId,
      units: [workUnit],
      adjacency: {},
      createdAt: Date.now(),
    };

    const request: OrchestrationRequest = {
      id: runId,
      objective: 'Test parallel join',
      categoryMix: { coding: true },
      flags: { fineGrainedOrchestration: true },
    };

    const events: ProgressEventType[] = [];
    for await (const evt of scheduler.run(plan, request)) {
      events.push(evt.type);
    }

    expect(events).toContain(ProgressEventType.Done);

    const protocolEvents = await journal.listProtocolEvents(runId, { taskId: workUnit.id });
    const joinEvents = protocolEvents.filter((evt) =>
      evt.type === ProtocolEventType.JoinStarted ||
      evt.type === ProtocolEventType.JoinMerged,
    );
    expect(joinEvents.map((e) => e.type)).toEqual([
      ProtocolEventType.JoinStarted,
      ProtocolEventType.JoinMerged,
    ]);

    const mergedEvent = joinEvents.find((e) => e.type === ProtocolEventType.JoinMerged) as JoinMergedEvent;
    expect(mergedEvent.mergedPatchPlanHash).toBeTruthy();
    expect(mergedEvent.conflictCount ?? 0).toBe(0);

    await fs.rm(tmp, { recursive: true, force: true });
  });
});
