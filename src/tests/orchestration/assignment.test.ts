import { describe, it, expect } from 'bun:test';
import { AssignmentManager } from '../../agents/orchestration/assign/AssignmentManager';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  WorkUnit,
  WorkCategory,
  Intent,
  AgentProfile,
  AssignmentStatus,
} from '../../agents/orchestration/types';

describe('AssignmentManager - instruction and assignment creation', () => {
  it('builds instruction with required sections and creates queued assignment', async () => {
    const workUnit: WorkUnit = {
      id: 'wu_assign_1',
      category: WorkCategory.Coding,
      scope: { files: ['src/agents/TaskOrchestrator.ts'] },
      intent: Intent.Implement,
      acceptanceCriteria: ['Only files in scope are modified or newly added.'],
      dependencies: [],
      priority: 100,
      status: 'pending' as any,
      createdAt: Date.now(),
    };

    const agent: AgentProfile = {
      id: 'agent_test',
      name: 'Test Agent',
      capabilities: ['typescript'],
      maxTokenContext: 4096,
      concurrencyLimit: 1,
      categoryAffinity: [WorkCategory.Coding],
    };

    const packager = new ContextPackager({ tokenCap: 512 });
    const context = await packager.package(workUnit);

    const manager = new AssignmentManager();
    const instruction = manager.buildInstruction(workUnit, agent, context);

    expect(instruction).toContain('Objective:');
    expect(instruction).toContain('Scope:');
    expect(instruction).toContain('Deliverables:');
    expect(instruction).toContain('Do not modify files outside scope.');

    const assignment = manager.createAssignment(workUnit, agent, context);
    expect(assignment.status).toBe(AssignmentStatus.Queued);
    expect(assignment.id.startsWith('as_')).toBe(true);
  });
});
