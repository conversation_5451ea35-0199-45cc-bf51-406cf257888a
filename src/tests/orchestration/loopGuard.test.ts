import { describe, it, expect } from 'bun:test';

// Enable loop guard via config before importing/constructing scheduler
process.env.ORCH_ENABLE_LOOP_GUARD = 'true';

import {
  WorkScheduler,
  AssignmentExecutor,
} from '../../agents/orchestration/scheduler/WorkScheduler';
import { AgentRegistry } from '../../agents/orchestration/agents/AgentRegistry';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  WorkUnit,
  PartitionPlan,
  WorkCategory,
  Intent,
  OrchestrationRequest,
  ProgressEvent,
  ProgressEventType,
  AgentProfile,
} from '../../agents/orchestration/types';
import { deriveContentFingerprint } from '../../agents/orchestration/journal/canonicalize';

describe('WorkScheduler - loop guard A→B→A detection', () => {
  it('emits Failed(loop_detected) when history indicates A→B→A on same fingerprint', async () => {
    const runId = `loop-run-${Date.now()}`;

    const unit: WorkUnit = {
      id: 'wu_loop',
      category: WorkCategory.Coding,
      scope: { files: ['src/foo.ts'] },
      intent: Intent.Implement as any,
      acceptanceCriteria: ['ok'],
      dependencies: [],
      priority: 1,
      status: 'pending' as any,
      createdAt: Date.now(),
    };

    const plan: PartitionPlan = {
      id: `plan_${runId}`,
      requestId: runId,
      units: [unit],
      adjacency: {},
      createdAt: Date.now(),
    };

    const request: OrchestrationRequest = {
      id: runId,
      objective: 'Trigger loop guard test',
      flags: { fineGrainedOrchestration: true },
      categoryMix: { coding: true },
      constraints: { concurrency: 1 },
    };

    // Registry with one agent 'a1' so selection = 'a1'
    const agents: AgentProfile[] = [
      { id: 'a1', name: 'Agent One', capabilities: ['typescript'], maxTokenContext: 4096, concurrencyLimit: 1, categoryAffinity: [WorkCategory.Coding] },
    ];
    const registry = new AgentRegistry({ defaultAgents: agents });

    const packager = new ContextPackager({ tokenCap: 128 });

    // Executor will not be invoked because loop guard triggers before packaging/scheduling continues
    const executor: AssignmentExecutor = async function* (): AsyncGenerator<ProgressEvent> {
      // no-op
    };

    const events: ProgressEvent[] = [];
    const scheduler = new WorkScheduler({
      agentRegistry: registry,
      contextPackager: packager,
      executor,
      heartbeatIntervalMs: 5_000,
      timeoutMsPerUnit: 5_000,
      onProgress: (e) => {
        events.push(e);
      },
    }) as any;

    // Pre-populate fingerprint history to simulate prior A->B; next selection 'a1' => A, so A->B->A detected
    const fp = deriveContentFingerprint({
      goal: request.objective,
      scope: { fileScopes: unit.scope.files },
    } as any);
    scheduler.fingerprintAgentHistory = scheduler.fingerprintAgentHistory ?? new Map<string, string[]>();
    scheduler.fingerprintAgentHistory.set(fp, ['a1', 'a2']);

    // Run
    for await (const _ of (scheduler as WorkScheduler).run(plan, request)) {
      // drain
    }

    // Expect a Failed event with loop_detected code
    const failed = events.find((e) => e.type === ProgressEventType.Failed);
    expect(failed).toBeTruthy();
    expect(failed?.error?.code).toBe('loop_detected');
  });
});
