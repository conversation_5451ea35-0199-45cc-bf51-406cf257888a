import { describe, it, expect } from 'bun:test';
import { WorkScheduler, AssignmentExecutor } from '../../agents/orchestration/scheduler/WorkScheduler';
import { AgentRegistry } from '../../agents/orchestration/agents/AgentRegistry';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  WorkUnit,
  PartitionPlan,
  WorkCategory,
  Intent,
  OrchestrationRequest,
  ProgressEventType,
  Assignment,
  ProgressEvent,
} from '../../agents/orchestration/types';

function sleep(ms: number) {
  return new Promise((r) => setTimeout(r, ms));
}

describe('WorkScheduler - concurrency and event flow', () => {
  it('runs 3 coding units concurrently (started -> done) within < 200ms wall time', async () => {
    const runId = `test-run-${Date.now()}`;

    // Minimal 3 coding units, no deps, empty file scopes to avoid I/O in ContextPackager
    const units: WorkUnit[] = [0, 1, 2].map((i) => ({
      id: `wu_${i}`,
      category: WorkCategory.Coding,
      scope: { files: [] },
      intent: Intent.Implement,
      acceptanceCriteria: ['Only files in scope are modified or newly added.'],
      dependencies: [],
      priority: 100,
      status: 'pending' as any,
      createdAt: Date.now(),
    }));

    const plan: PartitionPlan = {
      id: `plan_${runId}`,
      requestId: runId,
      units,
      adjacency: {}, // no deps
      createdAt: Date.now(),
    };

    const request: OrchestrationRequest = {
      id: runId,
      objective: 'Test scheduler concurrency',
      flags: { fineGrainedOrchestration: true },
      categoryMix: { coding: true },
      constraints: { concurrency: 3 },
    };

    // Register 3 agents with concurrencyLimit=1
    const registry = new AgentRegistry({
      defaultAgents: [
        { id: 'a1', name: 'A1', capabilities: ['typescript'], maxTokenContext: 4096, concurrencyLimit: 1, categoryAffinity: [WorkCategory.Coding] },
        { id: 'a2', name: 'A2', capabilities: ['typescript'], maxTokenContext: 4096, concurrencyLimit: 1, categoryAffinity: [WorkCategory.Coding] },
        { id: 'a3', name: 'A3', capabilities: ['typescript'], maxTokenContext: 4096, concurrencyLimit: 1, categoryAffinity: [WorkCategory.Coding] },
      ],
    });

    const packager = new ContextPackager({ tokenCap: 256 }); // keep small/fast

    // Executor: small delay (~35ms), no extra events (scheduler emits started and done)
    const executor: AssignmentExecutor = async function* (_assignment: Assignment): AsyncGenerator<ProgressEvent> {
      await sleep(35);
      // yield nothing; scheduler will emit review-request (for coding) and done
    };

    const scheduler = new WorkScheduler({
      agentRegistry: registry,
      contextPackager: packager,
      executor,
      perCategoryConcurrency: { [WorkCategory.Coding]: 3 },
      heartbeatIntervalMs: 5_000, // keep off
      timeoutMsPerUnit: 5_000,
    });

    const startedBy: Record<string, number> = {};
    const doneBy: Record<string, number> = {};

    const t0 = Date.now();
    for await (const evt of scheduler.run(plan, request)) {
      if (evt.type === ProgressEventType.Started) {
        startedBy[evt.workUnitId] = (startedBy[evt.workUnitId] ?? 0) + 1;
      }
      if (evt.type === ProgressEventType.Done) {
        doneBy[evt.workUnitId] = (doneBy[evt.workUnitId] ?? 0) + 1;
      }
    }
    const wall = Date.now() - t0;

    // Assert each unit started and done
    for (const u of units) {
      expect(startedBy[u.id]).toBeGreaterThanOrEqual(1);
      expect(doneBy[u.id]).toBeGreaterThanOrEqual(1);
    }

    // Coarse concurrency check: all three should complete in < 200ms
    expect(wall).toBeLessThan(200);
  });
});
