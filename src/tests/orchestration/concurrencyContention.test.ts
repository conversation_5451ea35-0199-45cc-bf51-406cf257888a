import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { TwoPhaseCommitCoordinator } from '../../agents/orchestration/OrchestrationEngine';
import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import { LockManager } from '../../agents/orchestration/locks/LockManager';
import {
  CoordinatorTransactionContext,
  CoordinatorState,
  TwoPhaseParticipant,
} from '../../agents/orchestration/types';

async function withTempDir(fn: (dir: string) => Promise<void>) {
  const dir = await fs.mkdtemp(path.join(tmpdir(), 'coord-'));
  try {
    await fn(dir);
  } finally {
    await fs.rm(dir, { recursive: true, force: true });
  }
}

describe('TwoPhaseCommitCoordinator concurrency contention', () => {
  it('serializes conflicting transactions and preserves fairness', async () => {
    await withTempDir(async (dir) => {
      const journal = new RunJournal(path.join(dir, 'journal'));
      const lockManager = new LockManager({ persistDir: path.join(dir, 'locks') });
      await lockManager.init();

      const config = {
        prepareTimeoutMs: 3000,
        commitAckTimeoutMs: 3000,
        lockLeaseMs: 6000,
        lockRenewIntervalMs: 500,
      };

      const coordinatorA = new TwoPhaseCommitCoordinator({ runId: 'run-shared', journal, lockManager, config });
      const coordinatorB = new TwoPhaseCommitCoordinator({ runId: 'run-shared', journal, lockManager, config });

      const timeline: string[] = [];

      function makeParticipant(id: string): TwoPhaseParticipant {
        return {
          id,
          agentId: 'vercel-codegen',
          prepare: async () => {
            timeline.push(`${id}-prepare`);
            return { vote: 'yes', respondedAt: new Date().toISOString() };
          },
          commit: async () => {
            timeline.push(`${id}-commit`);
            return { ok: true, commitSha: `${id}-sha`, respondedAt: new Date().toISOString() };
          },
          abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
        };
      }

      const ctxA: CoordinatorTransactionContext = {
        runId: 'run-shared',
        taskId: 'task-a',
        txnId: 'txn-a',
        stepId: 'step-a',
        participants: [makeParticipant('a')],
        resourceKeys: ['repo:demo|branch:main|path:src/shared.ts'],
      };

      const ctxB: CoordinatorTransactionContext = {
        runId: 'run-shared',
        taskId: 'task-b',
        txnId: 'txn-b',
        stepId: 'step-b',
        participants: [makeParticipant('b')],
        resourceKeys: ['repo:demo|branch:main|path:src/shared.ts'],
      };

      const [resA, resB] = await Promise.all([
        coordinatorA.runTransaction(ctxA),
        coordinatorB.runTransaction(ctxB),
      ]);

      expect(resA.state).toBe(CoordinatorState.Committed);
      expect(resB.state).toBe(CoordinatorState.Committed);

      expect(timeline).toEqual(['a-prepare', 'a-commit', 'b-prepare', 'b-commit']);
    });
  });
});
