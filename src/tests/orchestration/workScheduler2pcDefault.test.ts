import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { AgentRegistry } from '../../agents/orchestration/agents/AgentRegistry';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  WorkScheduler,
  TwoPhasePartitionContext,
} from '../../agents/orchestration/scheduler/WorkScheduler';
import {
  AssignmentExecutor,
  PartitionPlan,
  WorkUnit,
  WorkCategory,
  Intent,
  OrchestrationRequest,
  ProgressEventType,
  CoordinatorState,
  TwoPhaseParticipant,
} from '../../agents/orchestration/types';
import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import type { ConcurrencyParticipantSpec, ConcurrencyPartitionDescriptor, ConcurrencyPartitionResult } from '../../agents/orchestration/concurrency/ConcurrencyManager';
import { ProtocolEventType } from '../../agents/orchestration/journal/types';

function participantWithId(id: string): TwoPhaseParticipant {
  return {
    id,
    agentId: 'vercel-codegen',
    prepare: async () => ({ vote: 'yes', respondedAt: new Date().toISOString() }),
    commit: async () => ({ ok: true, commitSha: null, respondedAt: new Date().toISOString() }),
    abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
  };
}

describe('WorkScheduler two-phase default path', () => {
  it('routes code-changing assignments through 2PC and preserves persist-before-notify ordering', async () => {
    const tmp = await fs.mkdtemp(path.join(tmpdir(), 'scheduler-2pc-'));
    const journal = new RunJournal(tmp);

    const registry = new AgentRegistry({
      defaultAgents: [
        {
          id: 'vercel-codegen',
          name: 'Code Generation',
          capabilities: ['typescript'],
          maxTokenContext: 2048,
          concurrencyLimit: 1,
          categoryAffinity: [WorkCategory.Coding],
        },
      ],
    });
    const packager = new ContextPackager({ tokenCap: 256 });
    const executor: AssignmentExecutor = async function* () {
      throw new Error('executor should not be used when two-phase commit is active');
    };

    const participants: ConcurrencyParticipantSpec[] = [
      {
        id: 'vercel-codegen#1',
        participant: participantWithId('vercel-codegen#1'),
        resourceKeys: ['repo:workspace|branch:main|path:src/core.ts'],
        priorityTs: Date.now(),
        metadata: { previewDiff: `--- a/src/core.ts\n+++ b/src/core.ts\n@@ -0,0 +1,1 @@\n+console.log('core');\n`, patchPlanHash: 'core-hash' },
      },
    ];

    let executeCalls = 0;
    let seq = 1;

    const scheduler = new WorkScheduler({
      agentRegistry: registry,
      contextPackager: packager,
      executor,
      twoPhase: {
        enabled: true,
        parallelEnabled: false,
        maxParallel: 1,
        conflictPolicy: 'resolve-prefer-older',
        journal,
        deriveParticipants: async () => participants.map((spec) => ({
          id: spec.id,
          participant: spec.participant,
          resourceKeys: [...spec.resourceKeys],
          priorityTs: spec.priorityTs,
          metadata: spec.metadata,
        })),
        executePartition: async (
          partition: ConcurrencyPartitionDescriptor,
          ctx: TwoPhasePartitionContext,
        ): Promise<ConcurrencyPartitionResult> => {
          executeCalls += 1;
          const participantId = partition.participants[0]?.id ?? 'vercel-codegen#1';
          const meta = (partition.participants[0]?.metadata ?? {}) as { previewDiff?: string; patchPlanHash?: string };
          await journal.recordPrepareSent({
            runId: ctx.runId,
            taskId: ctx.taskId,
            txnId: ctx.parentTxnId,
            seqNo: seq++,
            participantId,
            resourceKeys: partition.resourceKeys,
          });
          await journal.recordPrepareAck({
            runId: ctx.runId,
            taskId: ctx.taskId,
            txnId: ctx.parentTxnId,
            seqNo: seq++,
            participantId,
            vote: 'yes',
            respondedAt: new Date().toISOString(),
            payload: { previewDiff: meta.previewDiff, patchPlanHash: meta.patchPlanHash },
          });
          await journal.recordDecisionPersisted({
            runId: ctx.runId,
            taskId: ctx.taskId,
            txnId: ctx.parentTxnId,
            seqNo: seq++,
            decision: 'commit',
            resourceKeys: partition.resourceKeys,
            participants: partition.participants.map((p) => p.id),
          });
          await journal.recordCommitSent({
            runId: ctx.runId,
            taskId: ctx.taskId,
            txnId: ctx.parentTxnId,
            seqNo: seq++,
            participantId,
          });
          await journal.recordCommitAck({
            runId: ctx.runId,
            taskId: ctx.taskId,
            txnId: ctx.parentTxnId,
            seqNo: seq++,
            participantId,
            ok: true,
            respondedAt: new Date().toISOString(),
          });
          return {
            partitionId: partition.id,
            decision: 'commit',
            coordinator: {
              state: CoordinatorState.Committed,
              decision: 'commit',
              prepareAcks: {
                [participantId]: { vote: 'yes', respondedAt: new Date().toISOString(), payload: { previewDiff: meta.previewDiff, patchPlanHash: meta.patchPlanHash } },
              },
              commitAcks: {
                [participantId]: { ok: true, commitSha: null, respondedAt: new Date().toISOString() },
              },
              abortAcks: {},
              locksReleased: partition.resourceKeys,
            },
            previewDiff: meta.previewDiff ?? null,
            patchPlanHash: meta.patchPlanHash ?? null,
            participantIds: partition.participants.map((p) => p.id),
          };
        },
      },
    });

    const runId = 'scheduler-two-phase';
    const workUnit: WorkUnit = {
      id: 'wu-2pc',
      category: WorkCategory.Coding,
      scope: { files: ['src/core.ts'] },
      intent: Intent.Fix,
      acceptanceCriteria: [''],
      dependencies: [],
      priority: 1,
      status: 'pending' as any,
      createdAt: Date.now(),
    };

    const plan: PartitionPlan = {
      id: 'plan-2pc',
      requestId: runId,
      units: [workUnit],
      adjacency: {},
      createdAt: Date.now(),
    };

    const request: OrchestrationRequest = {
      id: runId,
      objective: 'Ensure 2PC path',
    };

    const observed: ProgressEventType[] = [];
    for await (const evt of scheduler.run(plan, request)) {
      observed.push(evt.type);
    }

    expect(executeCalls).toBe(1);
    expect(observed).toContain(ProgressEventType.Done);

    const events = await journal.listProtocolEvents(runId, { taskId: workUnit.id });
    const decisionIdx = events.findIndex((e) => e.type === ProtocolEventType.DecisionPersisted);
    const commitSentIdx = events.findIndex((e) => e.type === ProtocolEventType.CommitSent);
    expect(decisionIdx).toBeGreaterThanOrEqual(0);
    expect(commitSentIdx).toBeGreaterThan(decisionIdx);

    await fs.rm(tmp, { recursive: true, force: true });
  });
});
