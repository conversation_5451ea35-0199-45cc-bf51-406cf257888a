import { describe, it, expect } from 'bun:test';
import { JsonLedger } from '../../agents/orchestration/ledger/JsonLedger';
import {
  Assignment,
  AssignmentStatus,
  PartitionPlan,
  ProgressEvent,
  ProgressEventType,
  WorkCategory,
  WorkUnit,
  WorkUnitStatus,
} from '../../agents/orchestration/types';
import { promises as fs } from 'fs';
import path from 'path';

describe('JsonLedger - dedup and load', () => {
  it('dedupes events and loads single instance', async () => {
    const runId = `test-run-${Date.now()}`;
    const ledger = await JsonLedger.open(runId);

    const wu: WorkUnit = {
      id: 'wu_x',
      category: WorkCategory.Coding,
      scope: { files: [] },
      intent: 'implement' as any,
      acceptanceCriteria: [],
      dependencies: [],
      priority: 1,
      status: WorkUnitStatus.Pending,
      createdAt: Date.now(),
    };

    const plan: PartitionPlan = {
      id: `plan_${runId}`,
      requestId: runId,
      units: [wu],
      adjacency: { [wu.id]: [] },
      createdAt: Date.now(),
    };

    await ledger.recordPlan(plan);

    const assignment: Assignment = {
      id: `as_${runId}`,
      workUnitId: wu.id,
      agentId: 'agent_1',
      contextPackage: {
        id: `ctx_${runId}`,
        artifacts: [],
        constraints: { tokenCap: 256 },
      },
      instructions: 'test instructions',
      status: AssignmentStatus.Queued,
      attempts: 0,
    };
    await ledger.recordAssignment(assignment);

    const evt: ProgressEvent = {
      eventId: 'evt1',
      type: ProgressEventType.Started,
      workUnitId: wu.id,
      percentComplete: 0,
      timestamp: Date.now(),
    };

    const first = await ledger.recordEvent(evt);
    const second = await ledger.recordEvent(evt);
    expect(first.deduped).toBe(false);
    expect(second.deduped).toBe(true);

    const loaded = await ledger.load();
    const eventsForKey = loaded.events.filter((e) => e.eventId === 'evt1' && e.workUnitId === wu.id);
    expect(eventsForKey.length).toBe(1);

    // Cleanup JSONL file
    const filePath = path.join(process.cwd(), 'data', 'ledger', `${runId}.jsonl`);
    await fs.rm(filePath, { force: true });
  });
});
