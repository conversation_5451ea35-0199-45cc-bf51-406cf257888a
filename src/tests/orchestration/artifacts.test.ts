import { describe, it, expect } from 'bun:test';
import { ArtifactStore } from '../../agents/orchestration/artifacts/ArtifactStore';
import { ArtifactType, ArtifactRef } from '../../agents/orchestration/types';
import { sha1 as sha1str } from '../../agents/orchestration/context/ContextPackager';
import { promises as fs } from 'fs';
import path from 'path';

describe('ArtifactStore - put/list/get', () => {
  it('stores a text artifact and returns matching checksum and size', async () => {
    const runId = `test-run-${Date.now()}`;
    const store = new ArtifactStore();

    const content = 'hello-artifact';
    const ref = await store.put({
      runId,
      type: ArtifactType.Doc,
      filename: 'note.txt',
      content,
      metadata: { test: true },
    });

    // Basic assertions on returned ref
    expect(ref.id).toBeTruthy();
    expect(ref.type).toBe(ArtifactType.Doc);
    expect(ref.version).toBe('v1');
    expect(ref.checksum).toBe(sha1str(content));
    expect(ref.size).toBe(Buffer.byteLength(content));

    // List should include this artifact
    const list = await store.list(runId);
    const found = list.find((r: ArtifactRef) => r.id === ref.id);
    expect(found).toBeTruthy();
    expect((found && found.checksum)!).toBe(ref.checksum!);
    expect((found && found.size)!).toBe(ref.size!);

    // Get should reconstruct matching ref and return a valid path
    const got = await store.get(runId, ref.id);
    expect(got.ref.id).toBe(ref.id);
    expect(got.ref.version).toBe('v1');
    expect(got.ref.checksum!).toBe(ref.checksum!);
    expect(got.ref.size!).toBe(ref.size!);

    // Cleanup stored directory
    const dir = path.join(process.cwd(), 'data', 'artifacts', runId);
    await fs.rm(dir, { recursive: true, force: true });
  });
});
