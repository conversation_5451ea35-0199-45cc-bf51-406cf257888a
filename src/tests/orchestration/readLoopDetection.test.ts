import { describe, it, expect } from 'bun:test';

import { DanteAIOrchestrator } from '../../agents/DanteOrchestrator';

describe('DanteAIOrchestrator read-loop heuristics', () => {
  const orchestrator = new DanteAIOrchestrator();
  const guard = (orchestrator as any)._shouldSkipWriteGuardForAnalysis.bind(orchestrator) as (args: any) => boolean;

  const baseArgs = {
    input: 'Please analyze the current state of the project before making changes.',
    routeText: 'Analyze project state',
    summary: '',
    finalText: '',
    declaredPaths: [] as string[],
    hadFileEditAttempt: false,
    readFileCount: 0,
    uniqueReadPaths: 0,
    readFileMaxRepeat: 0,
    listDirCount: 0,
    totalToolCalls: 0,
  } as const;

  it('does not skip guard when heavy read loop detected', () => {
    const result = guard({
      ...baseArgs,
      summary: 'No code changes were made.',
      finalText: 'Analysis complete; no edits applied.',
      readFileCount: 18,
      uniqueReadPaths: 3,
      readFileMaxRepeat: 6,
      listDirCount: 2,
      totalToolCalls: 20,
    });
    expect(result).toBe(false);
  });

  it('skips guard for light read-only analysis output', () => {
    const result = guard({
      ...baseArgs,
      summary: 'Analysis only: no file changes performed.',
      finalText: 'Next steps: plan implementation later.',
      readFileCount: 3,
      uniqueReadPaths: 3,
      readFileMaxRepeat: 1,
      listDirCount: 1,
      totalToolCalls: 4,
    });
    expect(result).toBe(true);
  });

  it('requires guard when assistant declares modified files', () => {
    const result = guard({
      ...baseArgs,
      summary: 'Updated foo.ts accordingly.',
      finalText: 'Changes have been applied.',
      declaredPaths: ['src/foo.ts'],
      readFileCount: 2,
      uniqueReadPaths: 2,
      readFileMaxRepeat: 1,
      listDirCount: 0,
      totalToolCalls: 3,
    });
    expect(result).toBe(false);
  });
});
