import { describe, it, expect, beforeEach } from 'bun:test';
import { ResponsesAPIClient } from '../../services/responsesAPIClient';
import RunJournal from '../../agents/orchestration/journal/RunJournal';
import { Checkpoint, CheckpointState, NextAction, isoNow } from '../../agents/orchestration/journal/types';
import { runWithContext } from '../../utils/requestContext';

function uniqueId(prefix: string) {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;
}

function makeStream(chunks: any[], throwAtIndex: number | null, errorToThrow: any) {
  return {
    async *[Symbol.asyncIterator]() {
      for (let i = 0; i < chunks.length; i++) {
        if (throwAtIndex !== null && i === throwAtIndex) {
          throw errorToThrow;
        }
        yield chunks[i];
      }
    }
  };
}

describe('Responses rs_* retry and RunJournal integration', () => {
  beforeEach(() => {
    // Deterministic backoff
    process.env.ORCH_RETRY_JITTER = '0';
    process.env.ORCH_RETRY_BASE_MS = '500';
    process.env.ORCH_RETRY_MAX_MS = '8000';
    process.env.ORCH_RETRY_MAX_ATTEMPTS = '5';
  });

  it('retries on streaming rs_* 404 "Item with id ... not found" up to 5 attempts and succeeds', async () => {
    const runId = uniqueId('run');
    const taskId = 'taskA';
    const journal = new RunJournal();

    // Seed a minimal valid checkpoint so retryState updates have a target
    const baseCp: Checkpoint = {
      runId,
      taskId,
      stepId: 'seed',
      agent: 'vercel-research',
      timestamps: { createdAt: isoNow() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/index.ts'], fileLocks: [], concurrencyGroupId: undefined },
    } as any;
    await journal.upsertCheckpoint(baseCp);

    const client = new ResponsesAPIClient({ apiKey: 'test', enableLogging: false }) as any;

    // Capture sleeps to verify exponential backoff progression
    const sleeps: number[] = [];
    client.sleep = async (ms: number) => { sleeps.push(ms); };

    let attempt = 0;
    const rsError = { status: 404, message: "Item with id 'rs_abc' not found" };
    client.openai = {
      responses: {
        create: async (_body: any) => {
          attempt++;
          if (attempt < 5) {
            // Stream that throws mid-iteration to simulate continuation failure
            return makeStream(
              [
                { type: 'response.id', id: `rs_attempt_${attempt}` },
                { type: 'response.output_text.delta', delta: { text: 'partial' } }
              ],
              1, // throw error after first data chunk
              rsError
            );
          }
          // Success on 5th attempt
          return makeStream(
            [
              { type: 'response.id', id: 'rs_success' },
              { type: 'response.output_text.delta', delta: { text: 'ok' } }
            ],
            null,
            null
          );
        }
      }
    };

    const sessionId = client.createSession();
    const run = async () => client.executeMCPServerRequest({
      sessionId,
      server_url: 'https://example.com/mcp',
      server_label: 'Example',
      input: 'do something',
      require_approval: 'never',
      stream: true
    });

    const result = await runWithContext({ runId, taskId }, run) as any;

    expect(attempt).toBe(5);
    // With jitter=0, backoff should be: 500, 1000, 2000, 4000
    expect(sleeps).toEqual([500, 1000, 2000, 4000]);
    expect(typeof result).toBe('object');
    const current = await journal.getCurrent(runId, taskId);
    expect(current).not.toBeNull();
    // On last failing attempt before success, rsRetryCount should be 4
    expect(current!.retryState?.rsRetryCount).toBe(4);
    expect(typeof current!.retryState?.operationKey).toBe('string');
  });

  it('honors Retry-After header on 429 in non-streaming mode', async () => {
    const runId = uniqueId('run');
    const taskId = 'taskB';
    const journal = new RunJournal();
    const baseCp: Checkpoint = {
      runId,
      taskId,
      stepId: 'seed',
      agent: 'vercel-debug',
      timestamps: { createdAt: isoNow() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/app.ts'], fileLocks: [], concurrencyGroupId: undefined },
    } as any;
    await journal.upsertCheckpoint(baseCp);

    const client = new ResponsesAPIClient({ apiKey: 'test', enableLogging: false }) as any;

    const sleeps: number[] = [];
    client.sleep = async (ms: number) => { sleeps.push(ms); };

    let attempt = 0;
    const rateLimitErr = { status: 429, message: 'rate limit', headers: { 'Retry-After': '2' } };
    client.openai = {
      responses: {
        create: async (_body: any) => {
          attempt++;
          if (attempt === 1) throw rateLimitErr;
          return { id: 'ok', output: [] }; // minimal non-stream success
        }
      }
    };

    const sessionId = client.createSession();
    const run = async () => client.executeMCPServerRequest({
      sessionId,
      server_url: 'https://example.com/mcp',
      server_label: 'Example',
      input: 'non-streaming op',
      require_approval: 'never',
      stream: false
    });

    await runWithContext({ runId, taskId }, run);
    expect(attempt).toBe(2);
    // With Retry-After: 2 seconds, delay should be max(2000, backoff(1)=500) = 2000
    expect(sleeps).toEqual([2000]);

    const current = await journal.getCurrent(runId, taskId);
    expect(current).not.toBeNull();
    expect(current!.retryState?.lastStatus).toBe(429);
  });

  it('does not retry on permanent errors (401/403)', async () => {
    const runId = uniqueId('run');
    const taskId = 'taskC';
    const journal = new RunJournal();
    const baseCp: Checkpoint = {
      runId,
      taskId,
      stepId: 'seed',
      agent: 'vercel-googleworkspace',
      timestamps: { createdAt: isoNow() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/main.ts'], fileLocks: [], concurrencyGroupId: undefined },
    } as any;
    await journal.upsertCheckpoint(baseCp);

    const client = new ResponsesAPIClient({ apiKey: 'test', enableLogging: false }) as any;
    let attempt = 0;
    client.openai = {
      responses: {
        create: async (_body: any) => {
          attempt++;
          throw { status: 401, message: 'unauthorized' };
        }
      }
    };

    const sessionId = client.createSession();
    const run = async () => client.executeMCPServerRequest({
      sessionId,
      server_url: 'https://example.com/mcp',
      server_label: 'Example',
      input: 'should fail fast',
      require_approval: 'never',
      stream: false
    });

    await expect(runWithContext({ runId, taskId }, run)).rejects.toBeDefined();
    expect(attempt).toBe(1);

    const current = await journal.getCurrent(runId, taskId);
    expect(current).not.toBeNull();
    expect(current!.retryState?.lastStatus).toBe(401);
  });
});
