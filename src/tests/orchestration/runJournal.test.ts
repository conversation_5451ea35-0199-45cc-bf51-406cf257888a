import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import os from 'node:os';
import path from 'node:path';

import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import { canonicalizePlan, hashPlan, deriveContentFingerprint } from '../../agents/orchestration/journal/canonicalize';
import { Checkpoint, CheckpointState, NextAction } from '../../agents/orchestration/journal/types';

function journalFile(runId: string) {
  return path.resolve(process.cwd(), 'data', 'run-journal', `${runId}.jsonl`);
}

function journalSnapshot(runId: string) {
  return path.resolve(process.cwd(), 'data', 'run-journal', 'snapshots', `${runId}.snapshot.json`);
}

describe('RunJournal - canonicalization and persistence', () => {
  it('canonicalizePlan produces deterministic output and hash regardless of key order', () => {
    const planA = {
      files: ['src/b.ts', 'src/a.ts'],
      ops: [{ path: 'src/a.ts', change: 'edit', data: { x: 1 } }],
      meta: { z: 3, a: 1 },
    };
    const planB = {
      meta: { a: 1, z: 3 },
      ops: [{ data: { x: 1 }, change: 'edit', path: 'src/a.ts' }],
      files: ['src\\a.ts', 'src/b.ts'], // include backslash to test normalization
    };

    const ca = canonicalizePlan(planA);
    const cb = canonicalizePlan(planB);
    expect(ca).toBe(cb);

    const ha = hashPlan(planA);
    const hb = hashPlan(planB);
    expect(ha).toBe(hb);
  });

  it('deriveContentFingerprint is stable across path order and separators', () => {
    const cp1: Partial<Checkpoint> = {
      goal: 'Refactor module loader',
      scope: { fileScopes: ['src\\loader.ts', 'src/utils/path.ts'] },
      plan: { patchPlanHash: 'abc123' },
      runId: 'r1',
      taskId: 't1',
      stepId: 's1',
      agent: 'agentA',
      timestamps: { createdAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
    };
    const cp2: Partial<Checkpoint> = {
      goal: 'refactor module loader', // case-insensitive
      scope: { fileScopes: ['src/utils/path.ts', 'src/loader.ts'] }, // order swapped and normalized
      plan: { patchPlanHash: 'abc123' },
      runId: 'r1',
      taskId: 't1',
      stepId: 's1',
      agent: 'agentA',
      timestamps: { createdAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
    };

    const f1 = deriveContentFingerprint(cp1);
    const f2 = deriveContentFingerprint(cp2);
    expect(f1).toBe(f2);
    expect(f1).toHaveLength(64); // sha-256 hex
  });

  it('append checkpoints and reconstruct current view after restart', async () => {
    const runId = `rj_${Date.now()}`;
    const journal = new RunJournal();

    const baseCp: Checkpoint = {
      runId,
      taskId: 'taskA',
      stepId: 'step1',
      agent: 'agentA',
      timestamps: { createdAt: new Date().toISOString(), startedAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/a.ts'] },
    };

    await journal.upsertCheckpoint(baseCp);

    const cp2: Checkpoint = {
      ...baseCp,
      stepId: 'step2',
      state: CheckpointState.Review,
      nextAction: NextAction.Review,
    };
    await journal.upsertCheckpoint(cp2);

    // Ensure current view
    const current = await journal.getCurrent(runId, 'taskA');
    expect(current).not.toBeNull();
    expect(current!.stepId).toBe('step2');
    expect(current!.state).toBe(CheckpointState.Review);

    // Restart-safe load: new instance should read latest
    const journal2 = new RunJournal();
    const current2 = await journal2.getCurrent(runId, 'taskA');
    expect(current2).not.toBeNull();
    expect(current2!.stepId).toBe('step2');

    // Cleanup
    await Promise.all([
      fs.rm(journalFile(runId), { force: true }),
      fs.rm(journalSnapshot(runId), { force: true }),
    ]);
  });

  it('hydrates from snapshot and replays tail entries when snapshot is stale', async () => {
    const tmpRoot = await fs.mkdtemp(path.join(os.tmpdir(), 'runjournal-'));
    const runId = `snap_${Date.now()}`;
    const journal = new RunJournal(tmpRoot);

    const baseCp: Checkpoint = {
      runId,
      taskId: 'taskA',
      stepId: 's1',
      agent: 'agent',
      timestamps: { createdAt: new Date().toISOString() },
      state: CheckpointState.Running,
      nextAction: NextAction.Plan,
      scope: { fileScopes: ['src/file.ts'] },
    };

    await journal.upsertCheckpoint(baseCp);

    const snapshotPath = path.join(tmpRoot, 'snapshots', `${runId}.snapshot.json`);
    const initialSnapshot = await fs.readFile(snapshotPath, 'utf8');

    const cp2: Checkpoint = {
      ...baseCp,
      stepId: 's2',
      state: CheckpointState.Review,
      nextAction: NextAction.Review,
    };
    await journal.upsertCheckpoint(cp2);

    // Simulate a crash that left snapshot stale by rewriting old snapshot content
    await fs.writeFile(snapshotPath, initialSnapshot);

    const journalReloaded = new RunJournal(tmpRoot);
    const current = await journalReloaded.getCurrent(runId, 'taskA');
    expect(current).not.toBeNull();
    expect(current!.stepId).toBe('s2');
    expect(current!.state).toBe(CheckpointState.Review);

    await fs.rm(tmpRoot, { recursive: true, force: true });
  });
});
