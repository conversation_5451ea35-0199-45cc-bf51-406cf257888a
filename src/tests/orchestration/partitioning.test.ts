import { describe, it, expect } from 'bun:test';
import { HeuristicPartitioner } from '../../agents/orchestration/partitioning/HeuristicPartitioner';
import { OrchestrationRequest, WorkCategory, WorkUnit } from '../../agents/orchestration/types';

function isAcyclic(nodeIds: string[], adjacency?: Record<string, string[]>): boolean {
  const adj: Record<string, string[]> = {};
  const nodes = new Set(nodeIds);
  // Ensure all nodes present
  for (const id of nodeIds) adj[id] = [];
  if (adjacency) {
    for (const [from, tos] of Object.entries(adjacency)) {
      if (!nodes.has(from)) continue;
      adj[from] = (tos ?? []).filter((t) => nodes.has(t));
    }
  }
  // Compute indegree
  const indeg = new Map<string, number>();
  for (const id of nodes) indeg.set(id, 0);
  for (const [from, tos] of Object.entries(adj)) {
    if (!nodes.has(from)) continue;
    for (const to of tos) {
      indeg.set(to, (indeg.get(to) ?? 0) + 1);
    }
  }
  // Kahn
  const q: string[] = [];
  for (const [id, d] of indeg.entries()) if (d === 0) q.push(id);
  let visited = 0;
  while (q.length) {
    const u = q.shift()!;
    visited++;
    for (const v of adj[u] ?? []) {
      const d = (indeg.get(v) ?? 0) - 1;
      indeg.set(v, d);
      if (d === 0) q.push(v);
    }
  }
  return visited === nodes.size;
}

describe('HeuristicPartitioner - builds acyclic DAG within size caps', () => {
  it('builds acyclic DAG within size caps', async () => {
    const runId = `test-run-${Date.now()}`;
    const request: OrchestrationRequest = {
      id: runId,
      objective: 'Partition selected orchestrator files',
      scopeHints: {
        files: [
          'src/agents/TaskOrchestrator.ts',
          'src/agents/DanteOrchestrator.ts',
        ],
      },
      constraints: {
        maxFilesPerUnit: 2,
        maxLOCPerUnit: 1000,
      },
      categoryMix: {
        coding: true,
        review: true,
      },
      flags: { fineGrainedOrchestration: true },
    };

    const partitioner = new HeuristicPartitioner();
    const plan = await partitioner.build(request);

    expect(plan.units.length).toBeGreaterThan(0);

    const nodeIds = plan.units.map((u: WorkUnit) => u.id);
    expect(isAcyclic(nodeIds, plan.adjacency)).toBe(true);

    // Ensure coding units respect max files per unit = 2
    for (const u of plan.units) {
      if (u.category === WorkCategory.Coding) {
        const count = (u.scope.files ?? []).length;
        expect(count).toBeLessThanOrEqual(2);
      }
    }
  });
});
