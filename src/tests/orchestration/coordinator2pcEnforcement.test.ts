import { describe, it, expect } from 'bun:test';
import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { tmpdir } from 'node:os';

import { TwoPhaseCommitCoordinator } from '../../agents/orchestration/OrchestrationEngine';
import { RunJournal } from '../../agents/orchestration/journal/RunJournal';
import { LockManager } from '../../agents/orchestration/locks/LockManager';
import {
  CoordinatorState,
  TwoPhaseParticipant,
  CoordinatorTransactionContext,
} from '../../agents/orchestration/types';

async function withTempDir(fn: (dir: string) => Promise<void>) {
  const dir = await fs.mkdtemp(path.join(tmpdir(), 'coord-'));
  try {
    await fn(dir);
  } finally {
    await fs.rm(dir, { recursive: true, force: true });
  }
}

describe('TwoPhaseCommitCoordinator enforcement', () => {
  it('persists decision before notifying participants and reaches committed state', async () => {
    await withTempDir(async (dir) => {
      const journal = new RunJournal(path.join(dir, 'journal'));
      const lockManager = new LockManager({ persistDir: path.join(dir, 'locks') });
      await lockManager.init();

      const coordinator = new TwoPhaseCommitCoordinator({
        runId: 'run-commit',
        journal,
        lockManager,
        config: {
          prepareTimeoutMs: 2000,
          commitAckTimeoutMs: 2000,
          lockLeaseMs: 4000,
          lockRenewIntervalMs: 500,
        },
      });

      const timeline: string[] = [];

      const participant: TwoPhaseParticipant = {
        id: 'codegen',
        agentId: 'vercel-codegen',
        prepare: async () => {
          timeline.push('prepare');
          return { vote: 'yes', respondedAt: new Date().toISOString() };
        },
        commit: async () => {
          timeline.push('commit');
          return { ok: true, commitSha: 'deadbeef', respondedAt: new Date().toISOString() };
        },
        abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
      };

      const ctx: CoordinatorTransactionContext = {
        runId: 'run-commit',
        taskId: 'task-1',
        txnId: 'txn-1',
        stepId: 'step-1',
        participants: [participant],
        resourceKeys: ['repo:demo|branch:main|path:src/app.ts'],
        patchPlanHash: 'hash123',
      };

      const result = await coordinator.runTransaction(ctx);

      expect(result.state).toBe(CoordinatorState.Committed);
      expect(result.decision).toBe('commit');
      expect(Object.keys(result.prepareAcks)).toEqual(['codegen']);
      expect(result.commitAcks.codegen?.ok).toBe(true);

      const events = await journal.listProtocolEvents('run-commit', { taskId: 'task-1', txnId: 'txn-1' });
      const seqByType = new Map<string, number>();
      events.forEach((evt, idx) => seqByType.set(`${evt.type}_${(evt as any).participantId ?? 'coordinator'}`, idx));

      const decisionIdx = events.findIndex((evt) => evt.type === 'decision_persisted');
      const commitSentIdx = events.findIndex((evt) => evt.type === 'commit_sent');
      expect(decisionIdx).toBeGreaterThanOrEqual(0);
      expect(commitSentIdx).toBeGreaterThan(decisionIdx);

      expect(timeline).toEqual(['prepare', 'commit']);
      expect(result.locksReleased).toEqual(['repo:demo|branch:main|path:src/app.ts']);
    });
  });

  it('aborts when any participant votes no', async () => {
    await withTempDir(async (dir) => {
      const journal = new RunJournal(path.join(dir, 'journal'));
      const lockManager = new LockManager({ persistDir: path.join(dir, 'locks') });
      await lockManager.init();

      const coordinator = new TwoPhaseCommitCoordinator({
        runId: 'run-abort',
        journal,
        lockManager,
        config: {
          prepareTimeoutMs: 2000,
          commitAckTimeoutMs: 2000,
          lockLeaseMs: 4000,
          lockRenewIntervalMs: 500,
        },
      });

      const participant: TwoPhaseParticipant = {
        id: 'codegen',
        agentId: 'vercel-codegen',
        prepare: async () => ({ vote: 'no', reason: 'lint_failed', respondedAt: new Date().toISOString() }),
        commit: async () => ({ ok: true, commitSha: null, respondedAt: new Date().toISOString() }),
        abort: async () => ({ ok: true, respondedAt: new Date().toISOString() }),
      };

      const ctx: CoordinatorTransactionContext = {
        runId: 'run-abort',
        taskId: 'task-1',
        txnId: 'txn-2',
        stepId: 'step-1',
        participants: [participant],
        resourceKeys: ['repo:demo|branch:main|path:src/utils.ts'],
      };

      const result = await coordinator.runTransaction(ctx);

      expect(result.state).toBe(CoordinatorState.Aborted);
      expect(result.decision).toBe('abort');
      expect(result.abortAcks.codegen?.ok).toBe(true);

      const events = await journal.listProtocolEvents('run-abort', { taskId: 'task-1', txnId: 'txn-2' });
      const types = events.map((evt) => evt.type);
      expect(types).toContain('prepare_sent');
      expect(types).toContain('prepare_ack');
      expect(types).toContain('decision_persisted');
      expect(types).toContain('abort_sent');
      expect(types).toContain('abort_ack');
    });
  });
});
