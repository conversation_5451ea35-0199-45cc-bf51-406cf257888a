import { describe, it, expect } from 'bun:test';
import { JsonLedger } from '../../agents/orchestration/ledger/JsonLedger';

describe('JsonLedger - tracker dedup cooling window', () => {
  it('creates canonical then supersedes within cooling window, then dedup without reject after window', async () => {
    const runId = `trk_${Date.now()}`;
    const ledger = await JsonLedger.open(runId);

    const taskId = 'task-1';
    const fingerprint = 'fp-abc';

    // First call: create canonical
    const first = await ledger.recordOrDedupTracker(taskId, fingerprint, { coolingWindowMs: 50 });
    expect(first.deduped).toBe(false);
    expect(first.canonical).toBeTruthy();
    const canonicalId = first.canonical.id;
    expect(first.canonical.state).toBe('canonical');

    // Immediate second call: should be coolingRejected with superseded entry
    const second = await ledger.recordOrDedupTracker(taskId, fingerprint, { coolingWindowMs: 50 });
    expect(second.deduped).toBe(true);
    expect(second.coolingRejected).toBe(true);
    expect(second.canonical.id).toBe(canonicalId);
    expect(second.superseded?.state).toBe('superseded');

    // After window, update canonical lastSeenAt (no reject)
    await new Promise((r) => setTimeout(r, 60));
    const third = await ledger.recordOrDedupTracker(taskId, fingerprint, { coolingWindowMs: 50 });
    expect(third.deduped).toBe(true);
    expect(third.coolingRejected).toBeUndefined();
    expect(third.canonical.id).toBe(canonicalId);
  });
});
