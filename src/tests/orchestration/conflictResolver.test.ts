import { describe, it, expect } from 'bun:test';

import ConflictResolver, { ConflictResolutionContext } from '../../agents/orchestration/concurrency/ConflictResolver';
import type { ConflictEntry } from '../../agents/orchestration/concurrency/DiffJoiner';
import { serializeUnifiedDiffFromMap, UnifiedHunk } from '../../utils/unifiedDiff';
import { sha256Hex } from '../../agents/orchestration/journal/types';

function buildConflictEntry(hunksA: UnifiedHunk[], hunksB: UnifiedHunk[]): ConflictEntry {
  const diffA = serializeUnifiedDiffFromMap(new Map([['src/file.ts', { hunks: hunksA }]]));
  const diffB = serializeUnifiedDiffFromMap(new Map([['src/file.ts', { hunks: hunksB }]]));
  return {
    filePath: 'src/file.ts',
    participants: ['agent/a', 'agent/b'],
    partitionIds: ['partition-a', 'partition-b'],
    diffs: [
      { participantId: 'agent/a', partitionId: 'partition-a', hunks: hunksA, diffText: diffA },
      { participantId: 'agent/b', partitionId: 'partition-b', hunks: hunksB, diffText: diffB },
    ],
    hash: sha256Hex(JSON.stringify({ diffA, diffB })),
  };
}

describe('ConflictResolver', () => {
  it('prefers older partition when edits overlap', async () => {
    const overlappingHunkA: UnifiedHunk = {
      oldStart: 1,
      oldLines: 1,
      newStart: 1,
      newLines: 1,
      lines: ['-console.log("a")', '+console.log("a1")'],
    };
    const overlappingHunkB: UnifiedHunk = {
      oldStart: 1,
      oldLines: 1,
      newStart: 1,
      newLines: 1,
      lines: ['-console.log("a")', '+console.log("a2")'],
    };

    const conflict = buildConflictEntry([overlappingHunkA], [overlappingHunkB]);
    const resolver = new ConflictResolver({ policy: 'resolve-prefer-older' });
    const ctx: ConflictResolutionContext = { partitionOrder: ['partition-a', 'partition-b'] };

    const outcome = await resolver.resolveConflicts([conflict], ctx);
    expect(outcome.unresolvedConflicts).toHaveLength(0);
    expect(outcome.resolvedConflicts).toHaveLength(1);
    expect(outcome.resolvedConflicts[0]?.diff).toContain('a1');
  });

  it('delegates to agent resolver for non-overlapping edits and caches result', async () => {
    const hunkA: UnifiedHunk = {
      oldStart: 1,
      oldLines: 0,
      newStart: 1,
      newLines: 1,
      lines: ['+const foo = 1;'],
    };
    const hunkB: UnifiedHunk = {
      oldStart: 5,
      oldLines: 0,
      newStart: 5,
      newLines: 1,
      lines: ['+const bar = 2;'],
    };
    const conflict = buildConflictEntry([hunkA], [hunkB]);
    let agentCalls = 0;
    const resolver = new ConflictResolver({
      policy: 'resolve-prefer-older',
      resolveWithAgent: async () => {
        agentCalls += 1;
        const merged = serializeUnifiedDiffFromMap(
          new Map([
            ['src/file.ts', { hunks: [hunkA, hunkB] }],
          ]),
        );
        return { filePath: 'src/file.ts', diff: merged, notes: 'agent-merge' };
      },
    });
    const ctx: ConflictResolutionContext = { partitionOrder: ['partition-a', 'partition-b'] };

    const first = await resolver.resolveConflicts([conflict], ctx);
    expect(first.unresolvedConflicts).toHaveLength(0);
    expect(first.resolvedConflicts[0]?.notes).toBe('agent-merge');
    expect(agentCalls).toBe(1);

    const second = await resolver.resolveConflicts([conflict], ctx);
    expect(second.unresolvedConflicts).toHaveLength(0);
    expect(agentCalls).toBe(1); // cached
  });
});
