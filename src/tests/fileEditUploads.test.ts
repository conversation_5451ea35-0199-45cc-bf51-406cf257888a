import { afterAll, expect, test } from 'bun:test';
import fs from 'fs/promises';
import path from 'path';
import { fileEditTool } from '../tools/fileOperations';
import { runWithContext } from '../utils/requestContext';

const uploadsRoot = path.resolve(process.cwd(), 'uploads');

afterAll(async () => {
  for (const dir of ['tests', 'ctx']) {
    try {
      await fs.rm(path.join(uploadsRoot, dir), { recursive: true, force: true });
    } catch {}
  }
  try {
    await fs.rm(path.join(process.cwd(), 'tmp-file-edit'), { recursive: true, force: true });
  } catch {}
});

test('file_edit writes to uploads directory when given /uploads path', async () => {
  const target = '/uploads/tests/integration.txt';
  const result = await (fileEditTool as any).execute({
    operation: 'write',
    filePath: target,
    createDirs: true,
    content: 'hello uploads',
    diagnostics: false,
  });

  expect(result.success).toBe(true);
  const resolved = path.join(uploadsRoot, 'tests', 'integration.txt');
  const contents = await fs.readFile(resolved, 'utf-8');
  expect(contents).toBe('hello uploads');
});

test('file_edit respects request context working directory for relative paths', async () => {
  const scopedDir = path.join(uploadsRoot, 'ctx');
  await fs.mkdir(scopedDir, { recursive: true });

  await runWithContext({ cwd: scopedDir }, async () => {
    const result = await (fileEditTool as any).execute({
      operation: 'write',
      filePath: 'context-relative.txt',
      createDirs: true,
      content: 'scoped write',
      diagnostics: false,
    });
    expect(result.success).toBe(true);
  });

  const resolved = path.join(scopedDir, 'context-relative.txt');
  const contents = await fs.readFile(resolved, 'utf-8');
  expect(contents).toBe('scoped write');
  await fs.unlink(resolved);
});

test('file_edit maps /uploads/<projectName> paths to workspace directory', async () => {
  const projectName = path.basename(process.cwd());
  const target = `/uploads/${projectName}/tmp-file-edit/workspace.txt`;
  const result = await (fileEditTool as any).execute({
    operation: 'write',
    filePath: target,
    createDirs: true,
    content: 'workspace write',
    diagnostics: false,
  });

  expect(result.success).toBe(true);
  const resolved = path.join(process.cwd(), 'tmp-file-edit', 'workspace.txt');
  const contents = await fs.readFile(resolved, 'utf-8');
  expect(contents).toBe('workspace write');
});
