import { describe, test, expect } from 'bun:test';
import {
  extractTextContent,
  aggregateResults,
  chunkContent,
  generateSummary,
  type SearchResult,
  type ContentChunk
} from '../tools/webSearchHelpers';
import {
  mockNewsArticleHTML,
  mockTechNewsHTML,
  mockSearchResults
} from './fixtures/webContent';

describe('Content Chunking Functions - Fixed', () => {
  describe('extractTextContent', () => {
    test('should extract title from various sources', () => {
      const result = extractTextContent(mockNewsArticleHTML, 'https://example.com');

      expect(result.title).toBe('Google Pixel 10 Release Date Finally Revealed');
      expect(result.content).toContain('Google has finally announced');
      expect(result.markdown).toContain('# Google Pixel 10');
    });

    test('should remove unwanted elements', () => {
      const result = extractTextContent(mockNewsArticleHTML, 'https://example.com');

      // Should not contain navigation, header, footer, or script content
      expect(result.content).not.toContain('Navigation menu');
      expect(result.content).not.toContain('Header content');
      expect(result.content).not.toContain('Footer content');
      expect(result.content).not.toContain('Some JavaScript');
      expect(result.content).not.toContain('Sidebar content');
    });

    test('should preserve article structure', () => {
      const result = extractTextContent(mockNewsArticleHTML, 'https://example.com');

      expect(result.content).toContain('Key Features and Specifications');
      expect(result.content).toContain('Pricing and Availability');
      expect(result.content).toContain('Market Impact and Competition');
    });

    test('should handle articles without clear structure', () => {
      const simpleHTML = '<html><body><p>Simple content</p></body></html>';
      const result = extractTextContent(simpleHTML, 'https://example.com');

      expect(result.content).toContain('Simple content');
      expect(result.title).toBe('Untitled Page');
    });
  });

  describe('chunkContent function', () => {
    test('should create chunks from content', () => {
      const content = `Google has finally announced the official release date for the highly anticipated Pixel 10 smartphone.

This is a middle section with more details about the phone features.

In conclusion, the Pixel 10 represents Google's most ambitious smartphone effort to date.`;

      const chunks = chunkContent(content, 'Test Article');

      expect(Array.isArray(chunks)).toBe(true);
      expect(chunks.length).toBeGreaterThan(0);

      const introChunk = chunks.find(c => c.type === 'intro');
      expect(introChunk).toBeDefined();
      expect(introChunk!.priority).toBe(10);

      const conclusionChunk = chunks.find(c => c.type === 'conclusion');
      expect(conclusionChunk).toBeDefined();
      expect(conclusionChunk!.priority).toBe(9);
    });

    test('should generate summaries from chunks', () => {
      const chunks = [
        { id: 'intro', title: 'Introduction', content: 'This is the introduction with important information.', type: 'intro' as const, priority: 10, wordCount: 8 },
        { id: 'conclusion', title: 'Conclusion', content: 'In conclusion, this is the summary.', type: 'conclusion' as const, priority: 9, wordCount: 6 }
      ];

      const summary = generateSummary(chunks, 200);

      expect(summary).toBeDefined();
      expect(summary.length).toBeGreaterThan(10);
      expect(summary.length).toBeLessThanOrEqual(200);
    });

    test('should create chunks with proper structure from real content', () => {
      const extracted = extractTextContent(mockNewsArticleHTML, 'https://example.com');
      const chunks = chunkContent(extracted.content, extracted.title);

      expect(chunks.length).toBeGreaterThan(0);
      
      // Should have intro chunk
      const introChunk = chunks.find(c => c.type === 'intro');
      expect(introChunk).toBeDefined();
      expect(introChunk!.priority).toBe(10);
      expect(introChunk!.content).toContain('Google has finally announced');

      // All chunks should have valid properties
      for (const chunk of chunks) {
        expect(chunk.id).toBeDefined();
        expect(chunk.content.length).toBeGreaterThan(0);
        expect(chunk.wordCount).toBeGreaterThan(0);
        expect(chunk.priority).toBeGreaterThan(0);
        expect(chunk.priority).toBeLessThanOrEqual(10);
        expect(['intro', 'section', 'conclusion', 'misc']).toContain(chunk.type);
      }

      // Chunks should be sorted by priority (highest first)
      for (let i = 0; i < chunks.length - 1; i++) {
        expect(chunks[i].priority).toBeGreaterThanOrEqual(chunks[i + 1].priority);
      }
    });

    test('should generate meaningful summaries from real content', () => {
      const extracted = extractTextContent(mockNewsArticleHTML, 'https://example.com');
      const chunks = chunkContent(extracted.content, extracted.title);
      const summary = generateSummary(chunks, 500);

      expect(summary).toBeDefined();
      expect(summary.length).toBeGreaterThan(50);
      expect(summary.length).toBeLessThanOrEqual(500);
      expect(summary).toContain('Pixel');
      expect(summary).toContain('Google');
    });
  });

  describe('aggregateResults', () => {
    test('should deduplicate results by URL', () => {
      const duplicateResults = [
        mockSearchResults,
        [
          {
            title: "Duplicate Article",
            url: "https://example-tech-news.com/pixel-10-release", // Same URL as first result
            snippet: "Different snippet",
            source: "Different Source"
          }
        ]
      ];

      const aggregated = aggregateResults(duplicateResults);

      expect(aggregated.length).toBe(3); // Should only have 3 unique URLs
      expect(aggregated[0].title).toBe("Google Pixel 10 Release Date Finally Revealed");
    });

    test('should preserve all unique results', () => {
      const uniqueResults = [mockSearchResults];
      const aggregated = aggregateResults(uniqueResults);

      expect(aggregated.length).toBe(3);
      expect(aggregated).toEqual(mockSearchResults);
    });

    test('should handle empty result arrays', () => {
      const emptyResults = [[], []];
      const aggregated = aggregateResults(emptyResults);

      expect(aggregated.length).toBe(0);
      expect(Array.isArray(aggregated)).toBe(true);
    });
  });

  describe('Content Processing Edge Cases', () => {
    test('should handle very short content', () => {
      const shortHTML = '<html><body><p>Short.</p></body></html>';
      const result = extractTextContent(shortHTML, 'https://example.com');

      expect(result.content.length).toBeGreaterThan(0);
      expect(result.title).toBeDefined();
    });

    test('should handle content with no paragraphs', () => {
      const noParagraphHTML = '<html><body><div>No paragraphs here</div></body></html>';
      const result = extractTextContent(noParagraphHTML, 'https://example.com');

      expect(result.content).toContain('No paragraphs here');
    });

    test('should handle malformed HTML', () => {
      const malformedHTML = '<html><body><p>Unclosed paragraph<div>Mixed tags</p></div></body>';

      expect(() => {
        extractTextContent(malformedHTML, 'https://example.com');
      }).not.toThrow();
    });

    test('should respect content length limits', () => {
      const result = extractTextContent(mockNewsArticleHTML, 'https://example.com');

      expect(result.content.length).toBeLessThanOrEqual(10000);
      expect(result.markdown.length).toBeLessThanOrEqual(15000);
    });
  });

  describe('Chunk Quality Validation', () => {
    test('should create chunks with meaningful content', () => {
      const extracted = extractTextContent(mockNewsArticleHTML, 'https://example.com');
      const chunks = chunkContent(extracted.content, extracted.title);

      for (const chunk of chunks) {
        expect(chunk.id).toBeDefined();
        expect(chunk.content.length).toBeGreaterThan(20);
        expect(chunk.wordCount).toBeGreaterThan(0);
        expect(chunk.priority).toBeGreaterThan(0);
        expect(chunk.priority).toBeLessThanOrEqual(10);
        expect(['intro', 'section', 'conclusion', 'misc']).toContain(chunk.type);
      }
    });

    test('should have consistent word count calculation', () => {
      const extracted = extractTextContent(mockNewsArticleHTML, 'https://example.com');
      const chunks = chunkContent(extracted.content, extracted.title);

      for (const chunk of chunks) {
        const actualWordCount = chunk.content.split(' ').length;
        // Allow for some variance in word counting methods
        expect(Math.abs(chunk.wordCount - actualWordCount)).toBeLessThanOrEqual(5);
      }
    });
  });
});