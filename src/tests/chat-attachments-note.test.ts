import { expect, test } from 'bun:test';
import request from 'supertest';
import app from '../api/server';

test('attachment system note prepended when attachments present', async () => {
  const messages = [
    {
      role: 'user',
      content: 'Please read these files',
      attachments: [
        { filename: 'a.txt', url: '/uploads/a.txt' },
        { filename: 'b.pdf', url: '/uploads/b.pdf' },
      ],
    },
  ];

  // We cannot call the real /api/chat here without a running agent; instead, we assert 400 schema errors are not due to attachments
  // and rely on server behavior to prepend the system note. As a proxy, we call /api/chat and expect a 200 SSE response.
  const res = await request(app)
    .post('/api/chat?events=false')
    .send({ messages })
    .set('Content-Type', 'application/json');

  // We should get a 200 OK (stream or JSON). If the body is streamed, supertest may not capture content; so we just assert status.
  expect(res.status).toBe(200);
});
