import { memoryManager } from '../memory/MemoryManager';
import { EmbeddingService } from '../memory/services/EmbeddingService';
import { QdrantClient } from '@qdrant/js-client-rest';
import { config } from 'dotenv';

config();

async function testSemanticDebug() {
  console.log('Debugging semantic search...\n');

  // Initialize
  await memoryManager.initialize();

  // Test embedding generation
  const embeddingService = new EmbeddingService();
  const testQuery = 'Bob Python backend preference';

  console.log('Generating embedding for query:', testQuery);
  const queryEmbedding = await embeddingService.generateEmbedding(testQuery);
  console.log('Embedding generated, length:', queryEmbedding.length);
  console.log('First 5 values:', queryEmbedding.slice(0, 5));

  // Test Qdrant search directly
  const client = new QdrantClient({
    url: process.env.QDRANT_URL || 'http://localhost:6333',
    apiKey: process.env.QDRANT_API_KEY,
  });

  console.log('\nSearching Qdrant directly with embedding...');
  try {
    const response = await client.search('dante_memories', {
      vector: queryEmbedding,
      limit: 5,
      with_payload: true,
    });

    console.log(`Found ${response.length} results from Qdrant`);
    if (response.length > 0) {
      console.log('\nTop result:');
      console.log('  Score:', response[0].score);
      console.log('  Content:', typeof response[0].payload?.content === 'string' ? response[0].payload.content.substring(0, 100) : response[0].payload?.content);
    }
  } catch (error) {
    console.error('Qdrant search error:', error);
  }

  // Test semantic search through MemoryManager
  console.log('\n\nTesting semanticSearch through MemoryManager...');
  const result = await memoryManager.semanticSearch({
    query: testQuery,
    topK: 5
  });

  console.log(`Found ${result.memories.length} memories`);
  if (result.memories.length > 0) {
    console.log('Top result:', result.memories[0].content);
  }
}

testSemanticDebug().catch(console.error);
