#!/usr/bin/env bun

import { initializeMemory, getUserPreferences } from '../tools/memoryTools';

console.log('🧪 Testing Optimized Memory System\n');

// Test 1: Memory initialization (should not load memories)
console.log('1. Testing memory initialization...');
const initResult = await initializeMemory();
console.log('Init result:', initResult);

// Test 2: User preferences (should be minimal)
console.log('\n2. Testing user preferences loading...');
const userPrefs = await getUserPreferences();
console.log('User preferences length:', userPrefs.length);
console.log('User preferences:', userPrefs.substring(0, 200) + (userPrefs.length > 200 ? '...' : ''));

console.log('\n✅ Memory optimization tests completed');
console.log('📋 Memory system now uses on-demand retrieval instead of auto-loading');
console.log('🔍 Use contextual_memory_search and recall tools for targeted memory access');
