#!/usr/bin/env bun

/**
 * Test script to demonstrate MCP integration setup
 *
 * This script shows how to:
 * 1. Configure a simple filesystem MCP server
 * 2. Test the connection
 * 3. Discover available tools
 * 4. Execute a simple tool
 */

import { mcpServerManager } from '../mcp/MCPServerManager.js';
import { mcpToolFactory } from '../mcp/MCPToolFactory.js';
import { MCPServerTemplates } from '../tools/mcpTools/index.js';

console.log('🚀 Testing MCP Integration...\n');

async function testMCPSetup() {
  try {
    // 1. Create a simple filesystem server configuration
    console.log('📁 Creating filesystem MCP server...');
    const fsServerConfig = MCPServerTemplates.localFileSystem(process.cwd(), {
      allowWrite: false,
      priority: 100,
      enabled: true
    });

    console.log(`   Server ID: ${fsServerConfig.id}`);
    console.log(`   Command: ${fsServerConfig.config.fullCommand}`);
    console.log(`   Tags: ${fsServerConfig.tags.join(', ')}\n`);

    // 2. Register the server
    console.log('📝 Registering server...');
    mcpServerManager.registerServer(fsServerConfig);
    console.log('✅ Server registered successfully\n');

    // 3. Try to connect (Note: This might fail if the MCP package isn't installed)
    console.log('🔌 Attempting to connect...');
    try {
      await mcpServerManager.connectServer(fsServerConfig.id);
      console.log('✅ Server connected successfully\n');

      // 4. Discover tools
      console.log('🔍 Discovering available tools...');
      const { tools, metadata } = await mcpToolFactory.getAllTools();
      console.log(`📊 Found ${tools.length} tools:\n`);

      metadata.forEach((meta, index) => {
        console.log(`   ${index + 1}. ${meta.toolName}`);
        console.log(`      Description: ${meta.description}`);
        console.log(`      Server: ${meta.serverName}`);
        console.log(`      Tags: ${meta.tags.join(', ')}\n`);
      });

    } catch (connectError) {
      console.log('❌ Connection failed (this is expected if MCP packages aren\'t installed)');
      console.log(`   Error: ${connectError.message}\n`);

      console.log('📋 To fix this, install the MCP filesystem server:');
      console.log('   npm install -g @modelcontextprotocol/server-filesystem\n');
    }

    // 5. Show server status
    console.log('📊 Server Status:');
    const status = mcpServerManager.getServerStatus(fsServerConfig.id);
    console.log(`   Status: ${status.status}`);
    console.log(`   Last Error: ${status.lastError || 'None'}`);
    console.log(`   Tool Count: ${status.toolCount || 'Unknown'}\n`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    console.log('🧹 Cleaning up...');
    await mcpServerManager.cleanup();
    console.log('✅ Cleanup complete');
  }
}

// Run the test
testMCPSetup().catch(console.error);
