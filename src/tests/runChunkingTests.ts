#!/usr/bin/env node

/**
 * Comprehensive test runner for web search chunking features
 * This script runs all chunking-related tests in the correct order
 *
 * Notes:
 * - Converted to Node-compatible execution so the test runner can be used
 *   in typical CI environments where Bun globals (Bun.write, Bun.spawn) are
 *   not available.
 */

import { spawnSync } from 'child_process';
import fs from 'fs';
import chalk from 'chalk';

interface TestSuite {
  name: string;
  file: string;
  description: string;
  timeout?: number;
}

// Lightweight progress event emission to support machine parsing in CI
// These events are printed as single-line JSON objects
// Events: started, heartbeat, artifact, done (and blocker/needs-info if ever needed)
function emitProgressEvent(event: string, data: Record<string, unknown> = {}): void {
  const payload = {
    kind: 'ProgressEvent',
    event,
    timestamp: new Date().toISOString(),
    data,
  };
  // Print as raw JSON without ANSI coloring to keep it parseable
  try {
    console.log(JSON.stringify(payload));
  } catch {
    // Fallback in case of circular structure in data
    console.log(
      JSON.stringify({ kind: 'ProgressEvent', event, timestamp: new Date().toISOString() })
    );
  }
}

const testSuites: TestSuite[] = [
  {
    name: 'Unit Tests',
    file: 'src/tests/webContentChunking.test.ts',
    description: 'Content chunking and summarization functions',
    timeout: 30000,
  },
  {
    name: 'Integration Tests',
    file: 'src/tests/webSearchTool.integration.test.ts',
    description: 'WebSearchTool with chunking parameters',
    timeout: 60000,
  },
  {
    name: 'End-to-End Tests',
    file: 'src/tests/webContentChunkTool.e2e.test.ts',
    description: 'WebContentChunkTool complete workflows',
    timeout: 60000,
  },
  {
    name: 'Agent Integration Tests',
    file: 'src/tests/agentChunkingIntegration.test.ts',
    description: 'Agent integration with chunking capabilities',
    timeout: 45000,
  },
  {
    name: 'Error Handling Tests',
    file: 'src/tests/chunkingErrorHandling.test.ts',
    description: 'Error scenarios and edge cases',
    timeout: 45000,
  },
  {
    name: 'Performance Benchmarks',
    file: 'src/tests/chunkingPerformance.benchmark.test.ts',
    description: 'Performance benchmarks and regression detection',
    timeout: 120000,
  },
];

interface TestResult {
  suite: string;
  passed: boolean;
  duration: number;
  output: string;
  error?: string;
}

async function runTestSuite(suite: TestSuite): Promise<TestResult> {
  console.log(chalk.blue(`\n🧪 Running ${suite.name}...`));
  console.log(chalk.gray(`   ${suite.description}`));
  emitProgressEvent('heartbeat', { phase: 'suite-start', suite: suite.name, file: suite.file });

  const startTime = Date.now();

  try {
    // Use spawnSync for predictable synchronous test execution in Node CI
    const cmd = process.platform === 'win32' ? 'bun.cmd' : 'bun';
    const result = spawnSync(cmd, ['test', suite.file], {
      encoding: 'utf8',
      timeout: suite.timeout,
    });

    const duration = Date.now() - startTime;
    const output = String(result.stdout || '');
    const stderr = String(result.stderr || '');

    const passed = result.status === 0;

    if (passed) {
      console.log(chalk.green(`   ✅ ${suite.name} passed (${duration}ms)`));
    } else {
      console.log(chalk.red(`   ❌ ${suite.name} failed (${duration}ms)`));
      if (stderr) {
        console.log(chalk.red(`   Error: ${stderr.slice(0, 200)}...`));
      }
    }

    const resultObj: TestResult = {
      suite: suite.name,
      passed,
      duration,
      output,
      error: stderr || undefined,
    };

    emitProgressEvent('heartbeat', {
      phase: 'suite-finish',
      suite: suite.name,
      passed,
      duration,
    });

    return resultObj;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(chalk.red(`   💥 ${suite.name} crashed (${duration}ms)`));
    console.log(chalk.red(`   Error: ${error}`));

    emitProgressEvent('heartbeat', {
      phase: 'suite-crash',
      suite: suite.name,
      passed: false,
      duration,
      error: error instanceof Error ? error.message : String(error),
    });

    return {
      suite: suite.name,
      passed: false,
      duration,
      output: '',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

async function generateTestReport(results: TestResult[]): Promise<void> {
  const totalTests = results.length;
  const passedTests = results.filter((r) => r.passed).length;
  const failedTests = totalTests - passedTests;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  console.log(chalk.bold('\n📊 Test Results Summary'));
  console.log('='.repeat(50));

  console.log(`Total Test Suites: ${totalTests}`);
  console.log(chalk.green(`Passed: ${passedTests}`));
  console.log(chalk.red(`Failed: ${failedTests}`));
  console.log(`Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  console.log('\n📋 Detailed Results:');
  results.forEach((result) => {
    const status = result.passed ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
    const duration = chalk.gray(`(${result.duration}ms)`);
    console.log(`  ${status} ${result.suite} ${duration}`);

    if (!result.passed && result.error) {
      console.log(chalk.red(`    Error: ${result.error.slice(0, 100)}...`));
    }
  });

  if (failedTests > 0) {
    console.log(chalk.yellow('\n⚠️  Some tests failed. Check the detailed output above.'));
    console.log(chalk.yellow('   Consider running individual test suites for more details:'));

    results
      .filter((r) => !r.passed)
      .forEach((result) => {
        const testFile = testSuites.find((s) => s.name === result.suite)?.file;
        console.log(chalk.gray(`   bun test ${testFile}`));
      });
  } else {
    console.log(chalk.green('\n🎉 All chunking tests passed successfully!'));
    console.log(chalk.green('   Your web search chunking implementation is ready for production.'));
  }

  // Write detailed report to file using Node fs
  const reportContent = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests,
      totalDuration,
      successRate: (passedTests / totalTests) * 100,
    },
    results,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    },
  };

  fs.writeFileSync(
    'src/tests/chunking-test-report.json',
    JSON.stringify(reportContent, null, 2),
    'utf8'
  );
  console.log(chalk.gray('\n📄 Detailed report saved to: src/tests/chunking-test-report.json'));
  emitProgressEvent('artifact', {
    type: 'test-report',
    path: 'src/tests/chunking-test-report.json',
    summary: reportContent.summary,
  });
}

async function main() {
  console.log(chalk.bold.blue('🚀 Web Search Chunking Test Suite'));
  console.log(chalk.gray('Testing comprehensive chunking functionality...'));

  emitProgressEvent('started', {
    task: 'Web Search Chunking Test Suite',
    totalSuites: testSuites.length,
    suites: testSuites.map((s) => ({ name: s.name, file: s.file })),
  });

  const results: TestResult[] = [];

  // Run all test suites
  for (const suite of testSuites) {
    const result = await runTestSuite(suite);
    results.push(result);

    // Small delay between test suites
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Generate comprehensive report
  await generateTestReport(results);

  // Exit with appropriate code
  const allPassed = results.every((r) => r.passed);

  emitProgressEvent('done', {
    success: allPassed,
    totals: {
      total: results.length,
      passed: results.filter((r) => r.passed).length,
      failed: results.filter((r) => !r.passed).length,
    },
  });

  process.exit(allPassed ? 0 : 1);
}

// Handle interruption gracefully
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  Test run interrupted'));
  emitProgressEvent('done', { success: false, reason: 'SIGINT' });
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n⚠️  Test run terminated'));
  emitProgressEvent('done', { success: false, reason: 'SIGTERM' });
  process.exit(1);
});

// Run the test suite
main().catch((error) => {
  console.error(chalk.red('💥 Test runner crashed:'), error);
  emitProgressEvent('done', {
    success: false,
    reason: 'exception',
    error: error instanceof Error ? error.message : String(error),
  });
  process.exit(1);
});