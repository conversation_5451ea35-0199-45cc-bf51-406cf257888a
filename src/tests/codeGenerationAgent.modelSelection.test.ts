import { describe, it, expect } from "bun:test";
import { executeCodeGeneration } from "../agents/vercel/codeGeneration";

describe("CodeGenerationAgent model selection (orchestrator-driven)", () => {
  it("throws a clear error when no model is provided", async () => {
    await expect(executeCodeGeneration("test without model", {} as any)).rejects.toThrow(
      "requires a model supplied by the orchestrator"
    );
  });

  it("accepts a provided model (does not override or read env internally)", async () => {
    const dummyModel: any = { __testModel: true }; // sentinel; not a real provider model
    try {
      await executeCodeGeneration("test with provided model", { model: dummyModel } as any);
      // If it somehow resolves (unlikely with dummy model), the important bit is no internal override occurred
      expect(true).toBe(true);
    } catch (err: any) {
      // It is expected to fail deeper (generateText) with an invalid model error.
      // The key assertion: it must NOT be the "requires a model supplied by the orchestrator" error,
      // which would indicate the agent ignored our provided model.
      expect(String(err?.message || err)).not.toContain("requires a model supplied by the orchestrator");
    }
  });
});
