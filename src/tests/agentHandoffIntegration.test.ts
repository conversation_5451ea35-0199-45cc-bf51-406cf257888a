import { describe, it, expect, beforeAll } from 'bun:test';
import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

describe('Agent Handoff Integration Tests', () => {
  
  beforeAll(() => {
    console.log('🤝 Starting agent handoff integration tests...');
  });

  describe('Simple Task Handling (No Handoffs Expected)', () => {
    it('should handle simple search directly without creating subagents', async () => {
      console.log('\n🔍 Testing simple search task handling...');
      
      const userMessage = "Find all occurrences of 'gpt-4o' in the TypeScript files";
      const messages = convertUIMessagesToAgentFormat([{
        role: 'user',
        content: userMessage
      }]);

      let toolCallsLog: Array<{tool: string, args: any, result?: any}> = [];
      let agentSwitches: string[] = [];
      let finalResponse = '';
      
      try {
        const stream = await runDante(messages, { 
          stream: true, 
          maxTurns: 2,
          onToolCall: (toolName, args) => {
            console.log(`🔨 Tool: ${toolName}`);
            toolCallsLog.push({ tool: toolName, args });
          },
          onAgentSwitch: (agentName) => {
            console.log(`🔄 Agent switch: ${agentName}`);
            agentSwitches.push(agentName);
          },
          onText: (text) => {
            finalResponse += text;
          }
        });

        await stream.finalValue();

        console.log('\n📊 Simple task analysis:');
        console.log('Tools called:', toolCallsLog.map(t => t.tool).join(', '));
        console.log('Agent switches:', agentSwitches.length);
        console.log('Response contains results:', finalResponse.includes('gpt-4o') || finalResponse.includes('found'));

        // For a simple search, we expect:
        // 1. Direct tool usage (search_code)
        // 2. No agent switches/handoffs  
        // 3. No task management tools
        const searchTools = toolCallsLog.filter(t => t.tool === 'search_code');
        const taskTools = toolCallsLog.filter(t => 
          t.tool.includes('create_task') || 
          t.tool.includes('create_') && t.tool.includes('_subagent')
        );

        expect(searchTools.length).toBeGreaterThan(0);
        expect(taskTools.length).toBe(0); // No task management for simple search
        expect(agentSwitches.length).toBe(0); // No handoffs needed

      } catch (error) {
        console.error('❌ Simple task test failed:', error);
        throw error;
      }
    });
  });

  describe('Complex Task Handling (Handoffs Expected)', () => {
    it('should use subagent tools for complex debugging tasks', async () => {
      console.log('\n🐛 Testing complex debugging task requiring handoffs...');
      
      const userMessage = "Debug why the authentication system is failing in production and provide a comprehensive analysis with fixes";
      const messages = convertUIMessagesToAgentFormat([{
        role: 'user',
        content: userMessage
      }]);

      let toolCallsLog: Array<{tool: string, args: any}> = [];
      let agentSwitches: string[] = [];
      let finalResponse = '';
      
      try {
        const stream = await runDante(messages, { 
          stream: true, 
          maxTurns: 3,
          onToolCall: (toolName, args) => {
            console.log(`🔨 Tool: ${toolName}`);
            toolCallsLog.push({ tool: toolName, args });
          },
          onAgentSwitch: (agentName) => {
            console.log(`🔄 Agent switch: ${agentName}`);
            agentSwitches.push(agentName);
          },
          onText: (text) => {
            finalResponse += text;
          }
        });

        await stream.finalValue();

        console.log('\n📊 Complex task analysis:');
        console.log('Tools called:', toolCallsLog.map(t => t.tool).join(', '));
        console.log('Agent switches:', agentSwitches);
        console.log('Used subagent tools:', toolCallsLog.filter(t => t.tool.includes('create_') && t.tool.includes('_subagent')).length);

        // For complex debugging, we expect:
        // 1. Either direct complex handling OR subagent delegation
        // 2. Possibly create_debug_subagent or create_planning_subagent
        const subagentTools = toolCallsLog.filter(t => 
          t.tool.includes('create_') && t.tool.includes('_subagent')
        );
        
        const taskTrackers = toolCallsLog.filter(t => 
          t.tool.includes('create_task_tracker')
        );

        console.log('Subagent tools used:', subagentTools.map(t => t.tool));
        console.log('Task trackers used:', taskTrackers.map(t => t.tool));

        // This is a complex task, so we expect either:
        // - Subagent delegation, OR
        // - Comprehensive direct handling
        const hasSubagentDelegation = subagentTools.length > 0;
        const hasComplexAnalysis = finalResponse.length > 500; // Substantial response

        expect(hasSubagentDelegation || hasComplexAnalysis).toBe(true);

      } catch (error) {
        console.error('❌ Complex task test failed:', error);
        throw error;
      }
    });
  });

  describe('Task Classification Accuracy', () => {
    const testCases = [
      {
        message: "What is the weather like today?",
        expectedBehavior: "should use create_weather_subagent or handle directly",
        expectHandoff: true,
        expectedSubagent: "weather"
      },
      {
        message: "List all files in the src directory",
        expectedBehavior: "should handle directly with list_directory tool",
        expectHandoff: false,
        expectedSubagent: null
      },
      {
        message: "Monitor news about the Tesla earnings report and give me updates",
        expectedBehavior: "should use create_reminder_subagent for automated monitoring",
        expectHandoff: true,
        expectedSubagent: "reminder"
      },
      {
        message: "Refactor the entire codebase to use modern TypeScript patterns",
        expectedBehavior: "should use create_code_refactor_subagent or create_orchestrator_subagent",
        expectHandoff: true,
        expectedSubagent: "code_refactor"
      }
    ];

    testCases.forEach((testCase, index) => {
      it(`should correctly classify task ${index + 1}: "${testCase.message.substring(0, 50)}..."`, async () => {
        console.log(`\n🎯 Testing task classification: ${testCase.message}`);
        
        const messages = convertUIMessagesToAgentFormat([{
          role: 'user', 
          content: testCase.message
        }]);

        let toolCallsLog: Array<{tool: string, args: any}> = [];
        
        try {
          const stream = await runDante(messages, { 
            stream: true, 
            maxTurns: 2,
            onToolCall: (toolName, args) => {
              toolCallsLog.push({ tool: toolName, args });
            }
          });

          await stream.finalValue();

          const subagentTools = toolCallsLog.filter(t => 
            t.tool.includes('create_') && t.tool.includes('_subagent')
          );
          
          const directTools = toolCallsLog.filter(t => 
            !t.tool.includes('create_') && !t.tool.includes('task_tracker')
          );

          console.log(`Expected handoff: ${testCase.expectHandoff}`);
          console.log(`Actual subagent tools: ${subagentTools.map(t => t.tool)}`);
          console.log(`Direct tools used: ${directTools.map(t => t.tool)}`);

          if (testCase.expectHandoff) {
            // Should use subagent tools
            expect(subagentTools.length).toBeGreaterThan(0);
            
            if (testCase.expectedSubagent) {
              const hasExpectedSubagent = subagentTools.some(t => 
                t.tool.includes(testCase.expectedSubagent)
              );
              expect(hasExpectedSubagent).toBe(true);
            }
          } else {
            // Should handle directly
            expect(directTools.length).toBeGreaterThan(0);
            // Should not create unnecessary subagents for simple tasks
            expect(subagentTools.length).toBe(0);
          }

        } catch (error) {
          console.error(`❌ Task classification test failed for: ${testCase.message}`, error);
          throw error;
        }
      });
    });
  });

  describe('Tool Output Quality Verification', () => {
    it('should verify search results are complete and accurate', async () => {
      console.log('\n🔍 Verifying search result quality...');
      
      const userMessage = "Find the exact line where 'gpt-4o' is used as the default model";
      const messages = convertUIMessagesToAgentFormat([{
        role: 'user',
        content: userMessage
      }]);

      let searchResults: any[] = [];
      let finalResponse = '';
      
      const stream = await runDante(messages, { 
        stream: true, 
        maxTurns: 2,
        onToolCall: (toolName, args, result) => {
          if (toolName === 'search_code') {
            searchResults.push({ args, result });
          }
        },
        onText: (text) => {
          finalResponse += text;
        }
      });

      await stream.finalValue();

      console.log('Search calls made:', searchResults.length);
      
      if (searchResults.length > 0) {
        const lastSearch = searchResults[searchResults.length - 1];
        console.log('Last search args:', JSON.stringify(lastSearch.args, null, 2));
        
        // Verify search found the known reference in config.ts
        const shouldFindConfigReference = finalResponse.includes('config.ts') || 
                                        finalResponse.includes('defaultModel') ||
                                        finalResponse.includes('DEFAULT_MODEL');
        
        console.log('Found config reference:', shouldFindConfigReference);
        console.log('Response length:', finalResponse.length);
        console.log('Contains line numbers:', finalResponse.includes(':'));
        
        expect(shouldFindConfigReference).toBe(true);
      }
    });
  });
});