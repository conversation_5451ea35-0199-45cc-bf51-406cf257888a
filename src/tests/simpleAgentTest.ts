import { test, expect } from 'bun:test';
import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

async function simpleTest() {
  console.log('🤖 Simple Agent Test with Extended Turns');
  
  const messages = convertUIMessagesToAgentFormat([{
    role: 'user',
    content: "Search for any file in the current directory that contains the word 'export'"
  }]);

  try {
    const stream = await runDante(messages, { 
      stream: true, 
      maxTurns: 5
    });

    let textOutput = '';
    let eventCount = 0;
    let toolCalls = 0;
    
    for await (const event of stream) {
      eventCount++;
      
      if (event.type === 'raw_model_stream_event') {
        if ((event.data as any).type === 'output_text_delta') {
          const text = (event.data as any).delta;
          textOutput += text;
          process.stdout.write(text);
        }
      }
      
      if (event.type.includes('tool')) {
        toolCalls++;
        console.log(`\n🔧 Tool event: ${event.type}`);
      }
      
      if (eventCount % 50 === 0) {
        console.log(`\n[${eventCount} events processed...]`);
      }
      
      if (event.type === 'final_result') {
        console.log('\n✅ Got final result!');
        break;
      }
    }
    
    console.log(`\n\n📊 Summary:`);
    console.log(`Events: ${eventCount}`);
    console.log(`Tool calls: ${toolCalls}`);
    console.log(`Text output length: ${textOutput.length}`);
    console.log(`Contains 'found': ${textOutput.toLowerCase().includes('found')}`);    
    console.log(`Contains 'export': ${textOutput.includes('export')}`);
    
    return textOutput.toLowerCase().includes('found') || textOutput.includes('export');
    
  } catch (error) {
    console.error('❌ Simple test failed:', error);
    return false;
  }
}

// Expose as a bun:test so running the test suite doesn't exit the process
test('Simple Agent Test - simpleAgentTest', async () => {
  const success = await simpleTest();
  expect(success).toBe(true);
}, 60000);
