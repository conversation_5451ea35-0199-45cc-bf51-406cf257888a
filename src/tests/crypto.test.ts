/**
 * Unit tests for cryptographic utilities
 */

import { describe, test, expect, beforeEach } from 'bun:test';
import {
  encrypt,
  decrypt,
  deriveKey,
  generateSecureRandom,
  generateNonce,
  hash,
  secureCompare,
  rotateKey,
  isValidEncryptedData,
  createKeyId,
  type EncryptedData
} from '../utils/crypto';

describe('Crypto Utilities', () => {
  const testPassword = 'TestPassword123!@#$';
  const testPlaintext = 'This is sensitive data that needs encryption';

  describe('deriveKey', () => {
    test('should derive consistent key from same password and salt', () => {
      const salt = Buffer.from('test-salt-12345678901234567890123456');
      const { key: key1 } = deriveKey({ password: testPassword, salt });
      const { key: key2 } = deriveKey({ password: testPassword, salt });
      
      expect(key1.equals(key2)).toBe(true);
    });

    test('should derive different keys for different passwords', () => {
      const salt = Buffer.from('test-salt-12345678901234567890123456');
      const { key: key1 } = deriveKey({ password: testPassword, salt });
      const { key: key2 } = deriveKey({ password: 'DifferentPassword456', salt });
      
      expect(key1.equals(key2)).toBe(false);
    });

    test('should derive different keys for different salts', () => {
      const { key: key1, salt: salt1 } = deriveKey({ password: testPassword });
      const { key: key2, salt: salt2 } = deriveKey({ password: testPassword });
      
      expect(key1.equals(key2)).toBe(false);
      expect(salt1.equals(salt2)).toBe(false);
    });

    test('should throw error for short password', () => {
      expect(() => deriveKey({ password: 'short' })).toThrow('Password must be at least 12 characters long');
    });
  });

  describe('encrypt/decrypt', () => {
    test('should encrypt and decrypt data successfully', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      const decrypted = decrypt(encrypted, testPassword);
      
      expect(decrypted).toBe(testPlaintext);
    });

    test('should produce different ciphertext for same plaintext', () => {
      const encrypted1 = encrypt(testPlaintext, testPassword);
      const encrypted2 = encrypt(testPlaintext, testPassword);
      
      expect(encrypted1.ciphertext).not.toBe(encrypted2.ciphertext);
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      expect(encrypted1.salt).not.toBe(encrypted2.salt);
    });

    test('should fail decryption with wrong password', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      
      expect(() => decrypt(encrypted, 'WrongPassword123!@#')).toThrow('Invalid password or corrupted data');
    });

    test('should fail decryption with tampered ciphertext', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      encrypted.ciphertext = Buffer.from('tampered').toString('base64');
      
      expect(() => decrypt(encrypted, testPassword)).toThrow();
    });

    test('should fail decryption with tampered authentication tag', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      encrypted.tag = Buffer.from('tampered').toString('base64');
      
      expect(() => decrypt(encrypted, testPassword)).toThrow('Invalid password or corrupted data');
    });

    test('should handle empty plaintext error', () => {
      expect(() => encrypt('', testPassword)).toThrow('Plaintext cannot be empty');
    });

    test('should handle invalid encrypted data', () => {
      expect(() => decrypt(null as any, testPassword)).toThrow('Invalid encrypted data');
      expect(() => decrypt({} as any, testPassword)).toThrow('Invalid encrypted data');
    });

    test('should include version and timestamp in encrypted data', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      
      expect(encrypted.version).toBe(1);
      expect(encrypted.timestamp).toBeGreaterThan(0);
      expect(encrypted.timestamp).toBeLessThanOrEqual(Date.now());
    });
  });

  describe('generateSecureRandom', () => {
    test('should generate random string of specified length', () => {
      const random = generateSecureRandom(32);
      const buffer = Buffer.from(random, 'base64url');
      
      expect(buffer.length).toBe(32);
    });

    test('should generate unique values', () => {
      const values = new Set();
      for (let i = 0; i < 100; i++) {
        values.add(generateSecureRandom(16));
      }
      
      expect(values.size).toBe(100);
    });

    test('should use default length when not specified', () => {
      const random = generateSecureRandom();
      const buffer = Buffer.from(random, 'base64url');
      
      expect(buffer.length).toBe(32);
    });
  });

  describe('generateNonce', () => {
    test('should generate 32-byte nonce', () => {
      const nonce = generateNonce();
      const buffer = Buffer.from(nonce, 'base64url');
      
      expect(buffer.length).toBe(32);
    });

    test('should generate unique nonces', () => {
      const nonces = new Set();
      for (let i = 0; i < 100; i++) {
        nonces.add(generateNonce());
      }
      
      expect(nonces.size).toBe(100);
    });
  });

  describe('hash', () => {
    test('should generate consistent SHA-256 hash', () => {
      const hash1 = hash('test data');
      const hash2 = hash('test data');
      
      expect(hash1).toBe(hash2);
      expect(hash1.length).toBe(64); // SHA-256 produces 64 hex characters
    });

    test('should generate different hashes for different data', () => {
      const hash1 = hash('test data 1');
      const hash2 = hash('test data 2');
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('secureCompare', () => {
    test('should return true for identical strings', () => {
      const str = 'test-string-12345';
      expect(secureCompare(str, str)).toBe(true);
    });

    test('should return false for different strings', () => {
      expect(secureCompare('string1', 'string2')).toBe(false);
    });

    test('should return false for different length strings', () => {
      expect(secureCompare('short', 'longer string')).toBe(false);
    });

    test('should be timing-safe', () => {
      // This test is conceptual - actual timing safety is hard to test
      const secret = 'secret-value-12345';
      const attempts = [
        'wrong-value-123456',
        'secret-value-12346',
        'secret-value-12345'
      ];
      
      const results = attempts.map(attempt => secureCompare(secret, attempt));
      expect(results).toEqual([false, false, true]);
    });
  });

  describe('rotateKey', () => {
    test('should successfully rotate encryption key', () => {
      const oldPassword = 'OldPassword123!@#';
      const newPassword = 'NewPassword456$%^';
      
      const encrypted = encrypt(testPlaintext, oldPassword);
      const rotated = rotateKey(encrypted, oldPassword, newPassword);
      const decrypted = decrypt(rotated, newPassword);
      
      expect(decrypted).toBe(testPlaintext);
    });

    test('should fail decryption with old password after rotation', () => {
      const oldPassword = 'OldPassword123!@#';
      const newPassword = 'NewPassword456$%^';
      
      const encrypted = encrypt(testPlaintext, oldPassword);
      const rotated = rotateKey(encrypted, oldPassword, newPassword);
      
      expect(() => decrypt(rotated, oldPassword)).toThrow('Invalid password or corrupted data');
    });

    test('should fail rotation with wrong old password', () => {
      const oldPassword = 'OldPassword123!@#';
      const newPassword = 'NewPassword456$%^';
      
      const encrypted = encrypt(testPlaintext, oldPassword);
      
      expect(() => rotateKey(encrypted, 'WrongOldPassword', newPassword)).toThrow('Invalid password or corrupted data');
    });
  });

  describe('isValidEncryptedData', () => {
    test('should validate correct encrypted data structure', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      expect(isValidEncryptedData(encrypted)).toBe(true);
    });

    test('should reject invalid data structures', () => {
      // Test null/undefined
      const nullResult = isValidEncryptedData(null);
      expect(nullResult).toBe(false);
      
      const undefinedResult = isValidEncryptedData(undefined);
      expect(undefinedResult).toBe(false);
      
      // Test empty object
      expect(isValidEncryptedData({})).toBe(false);
      
      // Test partial objects
      expect(isValidEncryptedData({ version: 1 })).toBe(false);
      expect(isValidEncryptedData({
        version: 1,
        ciphertext: 'test',
        iv: 'test',
        tag: 'test',
        salt: 'test',
        // Missing timestamp
      })).toBe(false);
    });

    test('should reject data with wrong types', () => {
      expect(isValidEncryptedData({
        version: '1', // Should be number
        ciphertext: 'test',
        iv: 'test',
        tag: 'test',
        salt: 'test',
        timestamp: 12345
      })).toBe(false);
    });
  });

  describe('createKeyId', () => {
    test('should create consistent key ID for same key', () => {
      const key = Buffer.from('test-key-12345678901234567890123');
      const id1 = createKeyId(key);
      const id2 = createKeyId(key);
      
      expect(id1).toBe(id2);
      expect(id1.length).toBe(16); // First 16 chars of SHA-256 hash
    });

    test('should create different IDs for different keys', () => {
      const key1 = Buffer.from('test-key-1234567890123456789012');
      const key2 = Buffer.from('different-key-12345678901234567');
      
      const id1 = createKeyId(key1);
      const id2 = createKeyId(key2);
      
      expect(id1).not.toBe(id2);
    });
  });

  describe('Version compatibility', () => {
    test('should handle future version error', () => {
      const encrypted = encrypt(testPlaintext, testPassword);
      encrypted.version = 999; // Future version
      
      expect(() => decrypt(encrypted, testPassword)).toThrow('Unsupported encryption version: 999');
    });
  });

  describe('Performance', () => {
    test('should handle large data efficiently', () => {
      const largeData = 'x'.repeat(1024 * 1024); // 1MB of data
      const startTime = Date.now();
      
      const encrypted = encrypt(largeData, testPassword);
      const decrypted = decrypt(encrypted, testPassword);
      
      const duration = Date.now() - startTime;
      
      expect(decrypted).toBe(largeData);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Unicode handling', () => {
    test('should handle Unicode characters correctly', () => {
      const unicodeText = '测试数据 🔐 Тестовые данные 🔑 テストデータ';
      
      const encrypted = encrypt(unicodeText, testPassword);
      const decrypted = decrypt(encrypted, testPassword);
      
      expect(decrypted).toBe(unicodeText);
    });
  });
});