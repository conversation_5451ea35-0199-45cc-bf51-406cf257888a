/**
 * Test script for OpenAI Connector integrations
 */

import { connectorService } from '../services/connectorService';
import { searchGmail, getTodayEvents, searchDrive } from '../tools/connectors';

function getAuthorizationUrl(service: 'gmail' | 'calendar' | 'drive'): string {
  return `/api/auth/google?connector=${service}`;
}

// Test configuration
const TEST_USER_ID = 'test-user';
const TEST_OAUTH_TOKEN = {
  access_token: 'test-token-123',
  refresh_token: 'refresh-token-123',
  expires_at: Date.now() + 3600000, // 1 hour from now
  token_type: 'Bearer',
  scope: 'https://www.googleapis.com/auth/gmail.modify'
};

async function testConnectorService() {
  console.log('🧪 Testing Connector Service...\n');

  // Test 1: Store OAuth token
  console.log('1️⃣ Testing OAuth token storage...');
  await connectorService.storeOAuthToken(TEST_USER_ID, 'connector_gmail', TEST_OAUTH_TOKEN);
  const retrievedToken = connectorService.getOAuthToken({ id: TEST_USER_ID, email: TEST_USER_ID }, 'connector_gmail');
  console.log('✅ Token stored and retrieved:', await retrievedToken ? 'SUCCESS' : 'FAILED');

  // Test 2: Get connector tools
  console.log('\n2️⃣ Testing connector tool retrieval...');
  const gmailTools = connectorService.getConnectorTools('connector_gmail');
  console.log('Gmail tools available:', gmailTools);

  const calendarTools = connectorService.getConnectorTools('connector_googlecalendar');
  console.log('Calendar tools available:', calendarTools);

  const driveTools = connectorService.getConnectorTools('connector_googledrive');
  console.log('Drive tools available:', driveTools);

  // Test 3: Generate OAuth URLs
  console.log('\n3️⃣ Testing OAuth URL generation...');
  try {
    const gmailAuthUrl = getAuthorizationUrl('gmail');
    console.log('Gmail auth URL:', gmailAuthUrl ? '✅ Generated' : '❌ Failed');

    const calendarAuthUrl = getAuthorizationUrl('calendar');
    console.log('Calendar auth URL:', calendarAuthUrl ? '✅ Generated' : '❌ Failed');

    const driveAuthUrl = getAuthorizationUrl('drive');
    console.log('Drive auth URL:', driveAuthUrl ? '✅ Generated' : '❌ Failed');
  } catch (error) {
    console.log('⚠️ OAuth URL generation failed - Google OAuth credentials not configured');
    console.log('   Set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env file');
  }

  // Test 4: Test tool execution (without actual API calls)
  console.log('\n4️⃣ Testing connector tool initialization...');

  // Test Gmail tool
  try {
    const gmailTool = searchGmail as any;
    const gmailSearchResult = await gmailTool._execute({
      query: 'test query',
      maxResults: 5,
      userId: TEST_USER_ID
    });
    console.log('Gmail search tool:', gmailSearchResult.success === false ? '✅ Correctly requires auth' : '❌ Unexpected result');
  } catch (error: any) {
    console.log('Gmail search tool:', error.message.includes('not authenticated') ? '✅ Correctly requires auth' : '❌ Unexpected error');
  }

  // Test Calendar tool
  try {
    const calendarTool = getTodayEvents as any;
    const calendarResult = await calendarTool._execute({
      userId: 'no-auth-user'
    });
    console.log('Calendar tool:', calendarResult.success === false ? '✅ Correctly requires auth' : '❌ Unexpected result');
  } catch (error: any) {
    console.log('Calendar tool:', error.message.includes('not authenticated') ? '✅ Correctly requires auth' : '❌ Unexpected error');
  }

  // Test Drive tool
  try {
    const driveTool = searchDrive as any;
    const driveResult = await driveTool._execute({
      query: 'test document',
      userId: 'no-auth-user'
    });
    console.log('Drive tool:', driveResult.success === false ? '✅ Correctly requires auth' : '❌ Unexpected result');
  } catch (error: any) {
    console.log('Drive tool:', error.message.includes('not authenticated') ? '✅ Correctly requires auth' : '❌ Unexpected error');
  }

  // Cleanup
  console.log('\n5️⃣ Cleaning up...');
  connectorService.clearSessions();
  console.log('✅ Sessions cleared');

  console.log('\n✨ Connector integration tests completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Configure Google OAuth credentials in .env file');
  console.log('2. Set up OAuth consent screen in Google Cloud Console');
  console.log('3. Add authorized redirect URI: http://localhost:3001/api/auth/google/callback');
  console.log('4. Test the OAuth flow by visiting: http://localhost:3001/api/auth/google/gmail');
}

// Run tests if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testConnectorService().catch(console.error);
}

export { testConnectorService };
