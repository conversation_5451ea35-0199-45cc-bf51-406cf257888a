#!/usr/bin/env bun

import { mcpServerManager } from '../mcp/MCPServerManager';
import { mcpToolFactory } from '../mcp/MCPToolFactory';

async function listMCPTools() {
  console.log('🔧 MCP Tools Analysis');
  console.log('=' .repeat(50));
  
  try {
    // Get connected servers
    const connectedServers = mcpServerManager.getConnectedServers();
    console.log(`Connected MCP servers: ${connectedServers.length}`);
    
    for (const { id, name, instance } of connectedServers) {
      console.log(`\n📡 Server: ${name} (${id})`);
      
      try {
        // Try to get tools from this server
        const tools = await mcpToolFactory.getToolsFromServer(id);
        console.log(`   Tools available: ${tools.length}`);
        
        tools.forEach(tool => {
          console.log(`     • ${tool.name}: ${tool.description?.substring(0, 60) || 'No description'}...`);
        });
        
        // Check for problematic tools
        const problemTools = tools.filter(tool => 
          tool.name === 'directory_tree' ||
          tool.name === 'search_files' ||
          tool.name === 'analyze_project' ||
          tool.name.includes('recursive')
        );
        
        if (problemTools.length > 0) {
          console.log(`   🚨 Problematic tools found: ${problemTools.map(t => t.name).join(', ')}`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error getting tools: ${error}`);
      }
    }
    
    // Check tool filtering
    console.log('\n🔍 Tool Filtering Status:');
    const cacheStats = mcpToolFactory.getCacheStats();
    console.log(`Cache entries: ${cacheStats.entries}`);
    console.log(`Cache hits: ${cacheStats.hits}`);
    console.log(`Cache misses: ${cacheStats.misses}`);
    
  } catch (error) {
    console.error('❌ MCP tools analysis failed:', error);
  }
}

async function main() {
  await listMCPTools();
}

main().catch(console.error);