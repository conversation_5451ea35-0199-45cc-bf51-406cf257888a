#!/usr/bin/env bun

import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

async function testFileOperations() {
  console.log('Testing Dante File Operations\n');
  console.log('=' .repeat(60));
  
  // Test 1: Create and read a file
  console.log('\nTest 1: Create and read a file');
  const test1Messages = [
    { role: 'user', content: 'Create a file called test.txt with the content "Hello from Dante!" then read it back to confirm' }
  ];
  
  try {
    const converted1 = convertUIMessagesToAgentFormat(test1Messages);
    const result1 = await runDante(converted1, { stream: false, maxTurns: 3 });
    console.log('✓ Result:', result1.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 2: List directory contents
  console.log('\nTest 2: List directory contents');
  const test2Messages = [
    { role: 'user', content: 'List all files in the src directory' }
  ];
  
  try {
    const converted2 = convertUIMessagesToAgentFormat(test2Messages);
    const result2 = await runDante(converted2, { stream: false, maxTurns: 2 });
    console.log('✓ Result:', result2.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 3: Execute JavaScript code
  console.log('\nTest 3: Execute JavaScript code');
  const test3Messages = [
    { role: 'user', content: 'Execute this JavaScript code: console.log("2 + 2 =", 2 + 2)' }
  ];
  
  try {
    const converted3 = convertUIMessagesToAgentFormat(test3Messages);
    const result3 = await runDante(converted3, { stream: false, maxTurns: 2 });
    console.log('✓ Result:', result3.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 4: Analyze project structure
  console.log('\nTest 4: Analyze project structure');
  const test4Messages = [
    { role: 'user', content: 'Analyze the structure of this project' }
  ];
  
  try {
    const converted4 = convertUIMessagesToAgentFormat(test4Messages);
    const result4 = await runDante(converted4, { stream: false, maxTurns: 2 });
    console.log('✓ Result:', result4.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  // Test 5: Search for code patterns
  console.log('\nTest 5: Search for code patterns');
  const test5Messages = [
    { role: 'user', content: 'Search for all functions that contain the word "execute" in TypeScript files' }
  ];
  
  try {
    const converted5 = convertUIMessagesToAgentFormat(test5Messages);
    const result5 = await runDante(converted5, { stream: false, maxTurns: 2 });
    console.log('✓ Result:', result5.finalOutput);
  } catch (error) {
    console.error('✗ Failed:', (error as Error).message);
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('File operations tests complete!');
}

// Run the test
testFileOperations().catch(console.error);