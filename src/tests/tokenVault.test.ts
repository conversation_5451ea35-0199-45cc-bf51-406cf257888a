/**
 * Unit tests for TokenVault secure storage system
 */

import { describe, test, expect, beforeEach, afterEach } from 'bun:test';
import { TokenVault, type OAuthToken } from '../services/tokenVault';
import { hash } from '../utils/crypto';
import { rmSync, existsSync } from 'fs';
import { join } from 'path';

describe('TokenVault', () => {
  let vault: TokenVault;
  let testDbPath: string;
  const USE_MEMORY_DB = process.env.USE_MEMORY_DB === '1';

  const makeUniqueDbPath = () => {
    const uid = `${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
    return join(process.cwd(), `test-tokens-${uid}.db`);
  };

  const deleteDbFiles = (base: string) => {
    if (!base || base === ':memory:') return;
    const candidates = [base, `${base}-wal`, `${base}-shm`, `${base}-journal`];
    for (const p of candidates) {
      try {
        if (existsSync(p)) rmSync(p, { force: true });
      } catch {}
    }
  };

  beforeEach(() => {
    // Create a unique database per test (ensures isolation for parallel runs)
    testDbPath = USE_MEMORY_DB ? ':memory:' : makeUniqueDbPath();

    // Pre-clean any remnants for this specific path (shouldn't exist, but safe)
    deleteDbFiles(testDbPath);

    // Initialize vault with test configuration
    vault = new TokenVault({
      database: {
        type: 'sqlite',
        filename: testDbPath
      },
      autoRefreshThreshold: 5,
      maxRefreshAttempts: 3,
      auditRetentionDays: 30,
      enableAutoCleanup: false // Disable auto cleanup for tests
    });
  });

  afterEach(async () => {
    // Close vault connection
    await vault.close();

    // Clean up database files (main .db and any WAL/SHM/journal files)
    deleteDbFiles(testDbPath);
  });

  describe('User Management', () => {
    test('should create new user', async () => {
      const user = await vault.upsertUser(
        'external-123',
        '<EMAIL>',
        'Test User',
        { role: 'admin' }
      );

      expect(user.externalId).toBe('external-123');
      expect(user.email).toBe('<EMAIL>');
      expect(user.name).toBe('Test User');
      expect(user.metadata?.role).toBe('admin');
    });

    test('should update existing user', async () => {
      await vault.upsertUser('external-123', '<EMAIL>', 'Test User');

      const updated = await vault.upsertUser(
        'external-123',
        '<EMAIL>',
        'Updated Name'
      );

      expect(updated.email).toBe('<EMAIL>');
      expect(updated.name).toBe('Updated Name');
    });

    test('should get user by email', async () => {
      await vault.upsertUser('external-123', '<EMAIL>', 'Test User');

      const user = await vault.getUserByEmail('<EMAIL>');

      expect(user).not.toBeNull();
      expect(user?.email).toBe('<EMAIL>');
    });

    test('should return null for non-existent user', async () => {
      const user = await vault.getUserByEmail('<EMAIL>');
      expect(user).toBeNull();
    });
  });

  describe('Token Storage', () => {
    let userId: number;

    beforeEach(async () => {
      const user = await vault.upsertUser('user-123', '<EMAIL>');
      userId = user.id;
    });

    test('should store OAuth tokens securely', async () => {
      const tokens: OAuthToken = {
        accessToken: 'access-token-12345',
        refreshToken: 'refresh-token-67890',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000),
        refreshExpiresAt: new Date(Date.now() + 86400000),
        scope: 'read write',
        idToken: 'id-token-abcdef'
      };

      const session = await vault.storeTokens(
        userId,
        'google',
        tokens,
        hash('state-123'),
        hash('verifier-456'),
        '127.0.0.1',
        'Mozilla/5.0',
        { customField: 'value' }
      );

      expect(session.userId).toBe(userId);
      expect(session.provider).toBe('google');
      expect(session.tokens.accessToken).toBe('access-token-12345');
      expect(session.tokens.refreshToken).toBe('refresh-token-67890');
      expect(session.metadata?.customField).toBe('value');
    });

    test('should revoke existing session when storing new one', async () => {
      const tokens1: OAuthToken = {
        accessToken: 'first-token',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000)
      };

      const tokens2: OAuthToken = {
        accessToken: 'second-token',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000)
      };

      await vault.storeTokens(userId, 'google', tokens1, hash('state1'));
      await vault.storeTokens(userId, 'google', tokens2, hash('state2'));

      const session = await vault.getTokens(userId, 'google');

      expect(session?.tokens.accessToken).toBe('second-token');
    });

    test('should retrieve and decrypt tokens', async () => {
      const tokens: OAuthToken = {
        accessToken: 'encrypted-access-token',
        refreshToken: 'encrypted-refresh-token',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000)
      };

      await vault.storeTokens(userId, 'google', tokens, hash('state'));

      const retrieved = await vault.getTokens(userId, 'google');

      expect(retrieved).not.toBeNull();
      expect(retrieved?.tokens.accessToken).toBe('encrypted-access-token');
      expect(retrieved?.tokens.refreshToken).toBe('encrypted-refresh-token');
    });

    test('should return null for non-existent session', async () => {
      const session = await vault.getTokens(userId, 'nonexistent');
      expect(session).toBeNull();
    });

    test('should update tokens after refresh', async () => {
      const initialTokens: OAuthToken = {
        accessToken: 'initial-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000)
      };

      const session = await vault.storeTokens(userId, 'google', initialTokens, hash('state'));

      const newTokens: Partial<OAuthToken> = {
        accessToken: 'new-access-token',
        expiresAt: new Date(Date.now() + 7200000)
      };

      await vault.updateTokens(session.sessionId, newTokens);

      const updated = await vault.getTokens(userId, 'google');

      expect(updated?.tokens.accessToken).toBe('new-access-token');
      expect(updated?.tokens.refreshToken).toBe('refresh-token'); // Should keep old refresh token
      expect(updated?.refreshCount).toBe(1);
    });

    test('should revoke tokens', async () => {
      const tokens: OAuthToken = {
        accessToken: 'token-to-revoke',
        tokenType: 'Bearer',
        expiresAt: new Date(Date.now() + 3600000)
      };

      const session = await vault.storeTokens(userId, 'google', tokens, hash('state'));

      await vault.revokeTokens(session.sessionId, 'user_requested');

      const revoked = await vault.getTokens(userId, 'google');
      expect(revoked).toBeNull();
    });
  });

  describe('Audit Logging', () => {
    test('should log audit events', async () => {
      await vault.logAudit({
        eventType: 'test_event',
        userId: 1,
        sessionId: 'session-123',
        provider: 'google',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Agent',
        success: true,
        metadata: { action: 'test' }
      });

      // Event should be logged without errors
      expect(true).toBe(true);
    });

    test('should log failed events', async () => {
      await vault.logAudit({
        eventType: 'failed_auth',
        success: false,
        errorMessage: 'Invalid credentials'
      });

      // Event should be logged without errors
      expect(true).toBe(true);
    });
  });

  describe('Rate Limiting', () => {
    test('should check rate limits', async () => {
      const identifier = '<EMAIL>';
      const attemptType = 'login';

      // Should allow initial attempts
      const allowed = await vault.checkRateLimit(identifier, attemptType);
      expect(allowed).toBe(true);
    });

    test('should record failed attempts', async () => {
      await vault.recordFailedAttempt(
        '<EMAIL>',
        'login',
        'invalid_password',
        '127.0.0.1',
        'Test Agent'
      );

      // Should still be under rate limit after one attempt
      const allowed = await vault.checkRateLimit('<EMAIL>', 'login');
      expect(allowed).toBe(true);
    });

    test('should enforce rate limits after multiple failures', async () => {
      const identifier = '<EMAIL>';

      // Record 5 failed attempts (the limit)
      for (let i = 0; i < 5; i++) {
        await vault.recordFailedAttempt(identifier, 'login', 'invalid_password');
      }

      // Should be rate limited now
      const allowed = await vault.checkRateLimit(identifier, 'login');
      expect(allowed).toBe(false);
    });
  });

  describe('Statistics', () => {
    test('should provide vault statistics', async () => {
      // Create some test data
      const user1 = await vault.upsertUser('user1', '<EMAIL>');
      const user2 = await vault.upsertUser('user2', '<EMAIL>');

      await vault.storeTokens(
        user1.id,
        'google',
        {
          accessToken: 'token1',
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() + 3600000)
        },
        hash('state1')
      );

      await vault.storeTokens(
        user2.id,
        'microsoft',
        {
          accessToken: 'token2',
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() + 3600000)
        },
        hash('state2')
      );

      const stats = await vault.getStatistics();

      expect(stats.totalUsers).toBe(2);
      expect(stats.activeSessions).toBe(2);
      expect(stats.sessionsByProvider.google).toBe(1);
      expect(stats.sessionsByProvider.microsoft).toBe(1);
    });
  });

  describe('Cleanup', () => {
    test('should clean up expired sessions', async () => {
      const user = await vault.upsertUser('cleanup-user', '<EMAIL>');

      // Store expired session
      await vault.storeTokens(
        user.id,
        'google',
        {
          accessToken: 'expired-token',
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() - 3600000), // Expired 1 hour ago
        },
        hash('state')
      );

      await vault.cleanup();

      const session = await vault.getTokens(user.id, 'google');
      expect(session).toBeNull();
    });
  });

  describe('Event Emitter', () => {
    test('should emit events on token operations', async () => {
      const user = await vault.upsertUser('event-user', '<EMAIL>');
      let eventFired = false;

      vault.on('audit:logged', (event) => {
        if (event.eventType === 'token_stored') {
          eventFired = true;
        }
      });

      await vault.storeTokens(
        user.id,
        'google',
        {
          accessToken: 'test-token',
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() + 3600000)
        },
        hash('state')
      );

      expect(eventFired).toBe(true);
    });

    test('should emit cleanup completion event', async () => {
      let cleanupCompleted = false;

      vault.on('cleanup:completed', () => {
        cleanupCompleted = true;
      });

      await vault.cleanup();

      expect(cleanupCompleted).toBe(true);
    });
  });

  describe('Security Features', () => {
    test('should encrypt tokens at rest', async () => {
      const user = await vault.upsertUser('security-user', '<EMAIL>');

      const sensitiveToken = 'super-secret-token-12345';

      await vault.storeTokens(
        user.id,
        'google',
        {
          accessToken: sensitiveToken,
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() + 3600000)
        },
        hash('state')
      );

      // The token should be encrypted in storage and decrypted on retrieval
      const retrieved = await vault.getTokens(user.id, 'google');
      expect(retrieved?.tokens.accessToken).toBe(sensitiveToken);
    });

    test('should handle token rotation tracking', async () => {
      const user = await vault.upsertUser('rotation-user', '<EMAIL>');

      const session = await vault.storeTokens(
        user.id,
        'google',
        {
          accessToken: 'initial-token',
          refreshToken: 'refresh-token',
          tokenType: 'Bearer',
          expiresAt: new Date(Date.now() + 3600000)
        },
        hash('state')
      );

      // Perform multiple refreshes
      for (let i = 1; i <= 3; i++) {
        await vault.updateTokens(session.sessionId, {
          accessToken: `refreshed-token-${i}`,
          expiresAt: new Date(Date.now() + 3600000 * (i + 1))
        });
      }

      const final = await vault.getTokens(user.id, 'google');
      expect(final?.refreshCount).toBe(3);
      expect(final?.tokens.accessToken).toBe('refreshed-token-3');
    });
  });
});
