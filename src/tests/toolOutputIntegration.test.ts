import { describe, it, expect, beforeAll } from 'bun:test';
import { searchCodeTool, listDirectoryTool } from '../tools/fileOperations';
import { runDante } from '../index';
import * as path from 'path';

describe('Tool Output Integration Tests', () => {
  const testProjectPath = '/Users/<USER>/dante-gpt';

  beforeAll(async () => {
    // Ensure test environment is ready
    console.log('🧪 Starting tool output integration tests...');
  });

  describe('Search Tool Output Structure Analysis', () => {
    it('should analyze search_code tool output format and size', async () => {
      console.log('\n🔍 Testing search_code tool with GPT-4o pattern...');

      const result = await (searchCodeTool as any).execute(
        {
          pattern: 'gpt-4o',
          path: testProjectPath,
          fileTypes: ['ts', 'js'],
          contextLines: 2
        },
        {}
      );

      const resultObj = result; // tool.execute returns an object (not JSON string)

      console.log('Success:', resultObj.success);
      console.log('Matches found:', resultObj.matches);
      console.log('Results truncated:', resultObj.truncated);
      console.log('Results array length:', resultObj.results?.length);

      // Analyze the full JSON structure
      const fullJsonString = JSON.stringify(resultObj, null, 2);
      console.log('Full JSON length:', fullJsonString.length, 'characters');
      console.log('Estimated tokens:', Math.floor(fullJsonString.length / 4));

      // Verify we actually found the known references
      expect(resultObj.success).toBe(true);
      expect(resultObj.matches).toBeGreaterThan(20); // We know there are 26+ references
    });

    it('should handle various search patterns gracefully', async () => {
      const testCases = [
        { pattern: 'export', description: 'Common pattern (many matches)' },
        { pattern: 'DanteOrchestrator', description: 'Specific class (few matches)' },
        { pattern: 'nonexistentpattern123', description: 'No matches' },
      ];

      for (const testCase of testCases) {
        console.log(`\n🔍 Testing pattern: ${testCase.pattern} (${testCase.description})`);

        const result = await searchCodeTool.invoke(
          {
            pattern: testCase.pattern,
            path: testProjectPath,
            fileTypes: ['ts', 'js'],
            contextLines: 1
          },
          {}
        );

        const resultObj = JSON.parse(result);

        expect(resultObj.success).toBe(true);
        console.log(`Matches for "${testCase.pattern}": ${resultObj.matches}`);
      }
    });
  });

  describe('Directory Listing Tool Analysis', () => {
    it('should analyze list_directory tool output size', async () => {
      console.log('\n📁 Testing list_directory tool output...');

      const result = await listDirectoryTool.invoke(
        {
          dirPath: testProjectPath,
          recursive: true,
          includeHidden: false,
          maxFiles: 1000
        },
        {}
      );

      const resultObj = JSON.parse(result);

      const fullJsonString = JSON.stringify(resultObj, null, 2);
      console.log('Directory listing JSON length:', fullJsonString.length);
      console.log('Files found:', resultObj.files?.length);

      expect(resultObj.success).toBe(true);
      expect(resultObj.files.length).toBeGreaterThan(100);
    });
  });
});
