{"timestamp": "2025-08-17T15:39:38.499Z", "summary": {"totalTests": 6, "passedTests": 0, "failedTests": 6, "totalDuration": 11, "successRate": 0}, "results": [{"suite": "Unit Tests", "passed": false, "duration": 1, "output": "", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/webContentChunking.test.ts:\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > extractTextContent > should extract title from various sources [32.97ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > extractTextContent > should remove unwanted elements [7.97ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > extractTextContent > should preserve article structure [6.52ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > extractTextContent > should handle articles without clear structure [3.55ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > chunkContent function > should create chunks from content [0.28ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > chunkContent function > should generate summaries from chunks [0.21ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > fetchWebContent with chunking > should enable chunking when requested [398.89ms]\n123 |       \n124 |       // Should have intro chunk with highest priority\n125 |       const introChunk = chunks.find(c => c.type === 'intro');\n126 |       expect(introChunk).toBeDefined();\n127 |       expect(introChunk!.priority).toBe(10);\n128 |       expect(introChunk!.content).toContain('Google has finally announced');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"Google has finally announced\"\nReceived: \"Example Domain This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission. More information...\"\n\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunking.test.ts:128:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Content Chunking Functions > fetchWebContent with chunking > should create structured chunks with proper priorities [104.74ms]\n150 |       });\n151 | \n152 |       expect(result.summary).toBeDefined();\n153 |       expect(result.summary!.length).toBeGreaterThan(50);\n154 |       expect(result.summary!.length).toBeLessThanOrEqual(500);\n155 |       expect(result.summary).toContain('Google');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"Google\"\nReceived: \"Example Domain This domain is for use in illustrative examples in documents.  You may use this domain in literature without prior coordination or asking for permission.\"\n\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunking.test.ts:155:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Content Chunking Functions > fetchWebContent with chunking > should generate meaningful summaries [116.00ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > fetchWebContent with chunking > should not chunk when disabled [133.97ms]\n174 | \n175 |       const result = await fetchWebContent('https://example.com', true, {\n176 |         enableChunking: true\n177 |       });\n178 | \n179 |       expect(result.success).toBe(false);\n          ^\nerror: expect(received).toBe(expected)\n\nExpected: false\nReceived: true\n\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunking.test.ts:179:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Content Chunking Functions > fetchWebContent with chunking > should handle fetch errors gracefully [140.58ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > aggregateResults > should deduplicate results by URL [0.13ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > aggregateResults > should preserve all unique results [0.03ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > aggregateResults > should handle empty result arrays [0.02ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Content Processing Edge Cases > should handle very short content [2.75ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Content Processing Edge Cases > should handle content with no paragraphs [2.83ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Content Processing Edge Cases > should handle malformed HTML [2.95ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Content Processing Edge Cases > should respect content length limits [4.41ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Chunk Quality Validation > should create chunks with meaningful content [99.36ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Content Chunking Functions > Chunk Quality Validation > should have consistent word count calculation [105.56ms]\n\n 17 pass\n 3 fail\n 56 expect() calls\nRan 20 tests across 1 files. [1487.00ms]\n"}, {"suite": "Integration Tests", "passed": false, "duration": 4, "output": "", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/webSearchTool.integration.test.ts:\n34 |     console.error = originalConsoleError;\n35 |   });\n36 | \n37 |   describe('Basic Search Functionality', () => {\n38 |     test('should return search results without content fetching', async () => {\n39 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"Google Pixel 10 release\",\n        maxResults: 3,\n        fetchContent: !1\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:39:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:38:66\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Basic Search Functionality > should return search results without content fetching [1.77ms]\n52 |       expect(data.sourcesUsed).toBeDefined();\n53 |       expect(Array.isArray(data.sourcesUsed)).toBe(true);\n54 |     });\n55 | \n56 |     test('should limit results to maxResults parameter', async () => {\n57 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"test query\",\n        maxResults: 2,\n        fetchContent: !1\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:57:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:56:57\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Basic Search Functionality > should limit results to maxResults parameter [0.12ms]\n68 |       // Mock all search functions to throw errors\n69 |       const failingSearch = mock(() => Promise.reject(new Error('Search service unavailable')));\n70 |       \n71 |       // This would require proper dependency injection to test effectively\n72 |       // For now, we test the error handling structure\n73 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"test query\",\n        maxResults: 5,\n        fetchContent: !1\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:73:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:67:51\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Basic Search Functionality > should handle search errors gracefully [0.09ms]\n84 |     });\n85 |   });\n86 | \n87 |   describe('Content Fetching with Chunking', () => {\n88 |     test('should fetch content when fetchContent is true', async () => {\n89 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:89:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:88:59\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Content Fetching with Chunking > should fetch content when fetchContent is true [0.09ms]\n110 |         { depth: 'detailed', expectedMaxLength: 2000 },\n111 |         { depth: 'full', expectedMaxLength: 10000 }\n112 |       ];\n113 | \n114 |       for (const testCase of tests) {\n115 |         const result = await webSearchTool.execute({\n                                 ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:115:29\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:107:50\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Content Fetching with Chunking > should respect contentDepth parameter [0.17ms]\n128 |         }\n129 |       }\n130 |     });\n131 | \n132 |     test('should enable chunking when requested', async () => {\n133 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:133:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:132:50\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Content Fetching with Chunking > should enable chunking when requested [0.10ms]\n149 |       expect(firstChunkedResult.chunkCount).toBeGreaterThan(0);\n150 |       expect(Array.isArray(firstChunkedResult.chunks)).toBe(true);\n151 |     });\n152 | \n153 |     test('should process multiple results with chunking', async () => {\n154 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:154:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:153:58\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Content Fetching with Chunking > should process multiple results with chunking [0.09ms]\n170 |     });\n171 |   });\n172 | \n173 |   describe('Parameter Validation and Defaults', () => {\n174 |     test('should use default values for optional parameters', async () => {\n175 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"minimal parameters test\"\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:175:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:174:62\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Parameter Validation and Defaults > should use default values for optional parameters [0.09ms]\n182 |       expect(data.results).toBeDefined();\n183 |       expect(data.results.length).toBeLessThanOrEqual(5); // Default maxResults\n184 |     });\n185 | \n186 |     test('should handle null and undefined parameters gracefully', async () => {\n187 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:187:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:186:67\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Parameter Validation and Defaults > should handle null and undefined parameters gracefully [0.08ms]\n199 | \n200 |     test('should validate contentDepth enum values', async () => {\n201 |       const validDepths = ['summary', 'detailed', 'full'];\n202 |       \n203 |       for (const depth of validDepths) {\n204 |         const result = await webSearchTool.execute({\n                                 ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n          query: \"test query\",\n          fetchContent: !0,\n          contentDepth: depth\n        })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:204:29\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:200:53\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Parameter Validation and Defaults > should validate contentDepth enum values [0.14ms]\n213 |     });\n214 |   });\n215 | \n216 |   describe('Response Format Validation', () => {\n217 |     test('should return valid JSON structure', async () => {\n218 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"json structure test\",\n        maxResults: 3,\n        fetchContent: !0\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:218:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:217:47\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Response Format Validation > should return valid JSON structure [0.12ms]\n240 |       expect(typeof data.timestamp).toBe('string');\n241 |       expect(Array.isArray(data.sourcesUsed)).toBe(true);\n242 |     });\n243 | \n244 |     test('should include proper result structure for each search result', async () => {\n245 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"result structure test\",\n        maxResults: 2,\n        fetchContent: !1\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:245:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:244:74\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Response Format Validation > should include proper result structure for each search result [0.11ms]\n266 |         expect(typeof firstResult.source).toBe('string');\n267 |       }\n268 |     });\n269 | \n270 |     test('should include chunking metadata when enabled', async () => {\n271 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:271:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:270:58\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Response Format Validation > should include chunking metadata when enabled [0.11ms]\n305 |       }));\n306 |       \n307 |       const originalFetch = global.fetch;\n308 |       global.fetch = timeoutFetch as any;\n309 | \n310 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"timeout test\",\n        fetchContent: !0\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:310:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:301:54\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Error Handling > should handle network timeouts gracefully [0.11ms]\n322 |       global.fetch = originalFetch;\n323 |     });\n324 | \n325 |     test('should provide meaningful error messages', async () => {\n326 |       // Test with invalid query to trigger error path\n327 |       const result = await webSearchTool.execute({\n                               ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: \"\",\n        maxResults: 1\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:327:27\n      at /Users/<USER>/dante-gpt/src/tests/webSearchTool.integration.test.ts:325:53\n\u001b[0m\u001b[31m(fail)\u001b[0m WebSearchTool Integration Tests > Error Handling > should provide meaningful error messages [0.09ms]\n\n 0 pass\n 15 fail\nRan 15 tests across 1 files. [361.00ms]\n"}, {"suite": "End-to-End Tests", "passed": false, "duration": 0, "output": "", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/webContentChunkTool.e2e.test.ts:\n20 |     console.error = console.error; // Restore console.error\n21 |   });\n22 | \n23 |   describe('Basic Content Fetching', () => {\n24 |     test('should fetch and chunk content from URL', async () => {\n25 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webContentChunkTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:25:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:24:52\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Basic Content Fetching > should fetch and chunk content from URL [1.82ms]\n45 |         { depth: 'detailed', maxLength: 2000 },\n46 |         { depth: 'full', maxLength: 15000 }\n47 |       ];\n48 | \n49 |       for (const { depth, maxLength } of depths) {\n50 |         const result = await webContentChunkTool.execute({\n                                 ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n          url: \"https://example.com/article\",\n          contentDepth: depth,\n          includeChunks: !1\n        })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:50:29\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:42:61\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Basic Content Fetching > should return appropriate content based on depth [0.25ms]\n59 |         expect(data.content.length).toBeLessThanOrEqual(maxLength + 10); // Account for '...'\n60 |       }\n61 |     });\n62 | \n63 |     test('should provide chunk preview information', async () => {\n64 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/article\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:64:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:63:53\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Basic Content Fetching > should provide chunk preview information [0.12ms]\n85 |     });\n86 |   });\n87 | \n88 |   describe('Specific Chunk Fetching', () => {\n89 |     test('should fetch specific chunks by ID', async () => {\n90 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/article\",\n        chunkIds: [\"intro\", \"conclusion\"],\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:90:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:89:47\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Specific Chunk Fetching > should fetch specific chunks by ID [0.11ms]\n105 |       expect(chunkIds).toContain('conclusion');\n106 |       expect(chunkIds.length).toBeLessThanOrEqual(2);\n107 |     });\n108 | \n109 |     test('should format content properly for specific chunks', async () => {\n110 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/article\",\n        chunkIds: [\"intro\"],\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:110:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:109:63\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Specific Chunk Fetching > should format content properly for specific chunks [0.09ms]\n122 |       expect(data.content).toContain('## ');\n123 |       expect(data.content).toContain('Introduction');\n124 |     });\n125 | \n126 |     test('should handle non-existent chunk IDs gracefully', async () => {\n127 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webContentChunkTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:127:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:126:60\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Specific Chunk Fetching > should handle non-existent chunk IDs gracefully [0.09ms]\n138 |       // Should return empty array for non-existent chunks\n139 |       expect(data.chunks.length).toBe(0);\n140 |     });\n141 | \n142 |     test('should combine multiple specific chunks', async () => {\n143 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: undefined is not a function (near '...webContentChunkTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:143:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:142:52\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Specific Chunk Fetching > should combine multiple specific chunks [0.09ms]\n156 |     });\n157 |   });\n158 | \n159 |   describe('Content Processing Options', () => {\n160 |     test('should disable chunking when includeChunks is false', async () => {\n161 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/article\",\n        includeChunks: !1,\n        contentDepth: \"detailed\"\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:161:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:160:64\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Content Processing Options > should disable chunking when includeChunks is false [0.08ms]\n171 |       expect(data.availableChunks).toBeUndefined();\n172 |       expect(data.chunks).toBeUndefined();\n173 |     });\n174 | \n175 |     test('should include summary when available', async () => {\n176 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/article\",\n        includeChunks: !0,\n        contentDepth: \"summary\"\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:176:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:175:50\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Content Processing Options > should include summary when available [0.08ms]\n197 |         json: () => Promise.resolve({ message: 'API response' })\n198 |       }));\n199 | \n200 |       global.fetch = jsonFetch as any;\n201 | \n202 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://api.example.com/data\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:202:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:191:50\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Content Processing Options > should handle different content types [0.10ms]\n220 |         statusText: 'Not Found'\n221 |       }));\n222 | \n223 |       global.fetch = errorFetch as any;\n224 | \n225 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/not-found\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:225:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:216:49\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Error Handling and Edge Cases > should handle HTTP errors gracefully [0.11ms]\n239 | \n240 |     test('should handle network failures', async () => {\n241 |       const networkError = mock(() => Promise.reject(new Error('Network unreachable')));\n242 |       global.fetch = networkError as any;\n243 | \n244 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/unreachable\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:244:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:240:43\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Error Handling and Edge Cases > should handle network failures [0.09ms]\n255 |       // Restore fetch\n256 |       global.fetch = mockFetch as any;\n257 |     });\n258 | \n259 |     test('should validate URL parameter', async () => {\n260 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"invalid-url\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:260:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:259:42\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Error Handling and Edge Cases > should validate URL parameter [0.08ms]\n275 |         text: () => Promise.resolve('')\n276 |       }));\n277 | \n278 |       global.fetch = emptyFetch as any;\n279 | \n280 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/empty\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:280:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:270:50\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Error Handling and Edge Cases > should handle empty content responses [0.10ms]\n298 |         text: () => Promise.resolve(malformedHTML)\n299 |       }));\n300 | \n301 |       global.fetch = malformedFetch as any;\n302 | \n303 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/malformed\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:303:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:292:52\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Error Handling and Edge Cases > should handle malformed HTML gracefully [0.09ms]\n313 |     });\n314 |   });\n315 | \n316 |   describe('Response Format Validation', () => {\n317 |     test('should return consistent response structure', async () => {\n318 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/test\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:318:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:317:56\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Response Format Validation > should return consistent response structure [0.10ms]\n335 |         expect(data).toHaveProperty('error');\n336 |       }\n337 |     });\n338 | \n339 |     test('should provide valid timestamps', async () => {\n340 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/test\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:340:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:339:44\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Response Format Validation > should provide valid timestamps [0.11ms]\n361 |         { includeChunks: false, contentDepth: 'summary' },\n362 |         { includeChunks: true, contentDepth: 'full' }\n363 |       ];\n364 | \n365 |       for (const testCase of testCases) {\n366 |         const result = await webContentChunkTool.execute({\n                                 ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n          url: \"https://example.com/test\",\n          ...testCase\n        })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:366:29\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:358:54\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Response Format Validation > should return valid JSON in all scenarios [0.17ms]\n377 |     });\n378 |   });\n379 | \n380 |   describe('Performance and Content Limits', () => {\n381 |     test('should respect content length limits', async () => {\n382 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/long-article\",\n        contentDepth: \"full\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:382:27\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:381:49\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Performance and Content Limits > should respect content length limits [0.10ms]\n394 |     });\n395 | \n396 |     test('should complete within reasonable time', async () => {\n397 |       const startTime = Date.now();\n398 |       \n399 |       await webContentChunkTool.execute({\n                ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/test\",\n        includeChunks: !0,\n        contentDepth: \"full\"\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:399:12\n      at /Users/<USER>/dante-gpt/src/tests/webContentChunkTool.e2e.test.ts:396:51\n\u001b[0m\u001b[31m(fail)\u001b[0m WebContentChunkTool End-to-End Tests > Performance and Content Limits > should complete within reasonable time [0.09ms]\n\n 0 pass\n 20 fail\nRan 20 tests across 1 files. [348.00ms]\n"}, {"suite": "Agent Integration Tests", "passed": false, "duration": 3, "output": "[dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  enable debug logging with { debug: true }\nUsing QdrantMemoryStore as primary storage\n", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/agentChunkingIntegration.test.ts:\n122 |       toolDescriptionOverride: 'Transfer to Planning Agent for complex/multi-step tasks that need structured planning and todo creation',\n123 |     }),\n124 |     handoff(taskOrchestrator, {\n125 |       toolDescriptionOverride: 'Transfer to Task Orchestrator for large-scale analysis requiring distributed processing (>5 files or >20k tokens)',\n126 |     }),\n127 |     handoff(researchAgent, {\n               ^\nReferenceError: Cannot access uninitialized variable.\n      at /Users/<USER>/dante-gpt/src/agents/DanteCore.ts:127:12\n      at processTicksAndRejections (:55:76)\n\n 0 pass\n 1 fail\nRan 1 tests across 1 files. [559.00ms]\n"}, {"suite": "Error <PERSON>", "passed": false, "duration": 3, "output": "", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/chunkingErrorHandling.test.ts:\n27 |       const result = await fetchWebContent('https://non-existent-domain.invalid', true, {\n28 |         enableChunking: true\n29 |       });\n30 | \n31 |       expect(result.success).toBe(false);\n32 |       expect(result.error).toContain('ENOTFOUND');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"ENOTFOUND\"\nReceived: \"Was there a typo in the url or port?\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:32:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Network Error Handling > should handle DNS resolution failures [14.35ms]\n42 |       const result = await fetchWebContent('https://slow-site.com', true, {\n43 |         enableChunking: true\n44 |       });\n45 | \n46 |       expect(result.success).toBe(false);\n47 |       expect(result.error).toContain('timeout');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"timeout\"\nReceived: \"Was there a typo in the url or port?\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:47:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Network Error Handling > should handle connection timeouts [1.99ms]\n54 |       const result = await fetchWebContent('https://localhost:99999', true, {\n55 |         enableChunking: true\n56 |       });\n57 | \n58 |       expect(result.success).toBe(false);\n59 |       expect(result.error).toContain('ECONNREFUSED');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"ECONNREFUSED\"\nReceived: \"fetch() URL is invalid\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:59:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Network Error Handling > should handle connection refused errors [0.14ms]\n66 |       const result = await fetchWebContent('https://invalid-ssl.com', true, {\n67 |         enableChunking: true\n68 |       });\n69 | \n70 |       expect(result.success).toBe(false);\n71 |       expect(result.error).toContain('SSL certificate error');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"SSL certificate error\"\nReceived: \"Was there a typo in the url or port?\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:71:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Network Error Handling > should handle SSL/TLS errors [1.63ms]\n75 |   describe('HTTP Error Status Handling', () => {\n76 |     test('should handle 404 Not Found', async () => {\n77 |       const notFoundFetch = mock(() => Promise.resolve(createMockResponse(404, 'Not Found')));\n78 |       global.fetch = notFoundFetch as any;\n79 | \n80 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/not-found\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:80:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:76:40\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > HTTP Error Status Handling > should handle 404 Not Found [0.12ms]\n89 | \n90 |     test('should handle 403 Forbidden', async () => {\n91 |       const forbiddenFetch = mock(() => Promise.resolve(createMockResponse(403, 'Forbidden')));\n92 |       global.fetch = forbiddenFetch as any;\n93 | \n94 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/forbidden\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:94:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:90:40\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > HTTP Error Status Handling > should handle 403 Forbidden [0.09ms]\n103 | \n104 |     test('should handle 500 Internal Server Error', async () => {\n105 |       const serverErrorFetch = mock(() => Promise.resolve(createMockResponse(500, 'Internal Server Error')));\n106 |       global.fetch = serverErrorFetch as any;\n107 | \n108 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/server-error\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:108:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:104:52\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > HTTP Error Status Handling > should handle 500 Internal Server Error [0.09ms]\n117 | \n118 |     test('should handle 429 Rate Limiting', async () => {\n119 |       const rateLimitFetch = mock(() => Promise.resolve(createMockResponse(429, 'Too Many Requests')));\n120 |       global.fetch = rateLimitFetch as any;\n121 | \n122 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/rate-limited\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:122:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:118:44\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > HTTP Error Status Handling > should handle 429 Rate Limiting [0.09ms]\n131 | \n132 |     test('should handle redirects that fail', async () => {\n133 |       const redirectFetch = mock(() => Promise.resolve(createMockResponse(301, 'Moved Permanently')));\n134 |       global.fetch = redirectFetch as any;\n135 | \n136 |       const result = await webContentChunkTool.execute({\n                               ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n        url: \"https://example.com/redirect\",\n        includeChunks: !0\n      })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:136:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:132:46\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > HTTP Error Status Handling > should handle redirects that fail [0.08ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Content Processing Error Handling > should handle empty HTML responses [335.26ms]\n167 |       const result = await fetchWebContent('https://example.com', true, {\n168 |         enableChunking: true\n169 |       });\n170 | \n171 |       expect(result.success).toBe(true);\n172 |       expect(result.content).toContain('Unclosed paragraph');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"Unclosed paragraph\"\nReceived: \"Example Domain This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission. More information...\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:172:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Content Processing Error Handling > should handle malformed HTML [140.29ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Content Processing Error Handling > should handle HTML with no text content [119.56ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Content Processing Error Handling > should handle extremely large HTML responses [112.15ms]\n210 |       const result = await fetchWebContent('https://example.com', true, {\n211 |         enableChunking: true\n212 |       });\n213 | \n214 |       expect(result.success).toBe(true);\n215 |       expect(result.content).toContain('Special chars');\n          ^\nerror: expect(received).toContain(expected)\n\nExpected to contain: \"Special chars\"\nReceived: \"Example Domain This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission. More information...\"\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:215:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Content Processing Error Handling > should handle HTML with special characters and encoding [193.66ms]\n229 |         null,\n230 |         undefined\n231 |       ];\n232 | \n233 |       for (const url of invalidUrls) {\n234 |         const result = await webContentChunkTool.execute({\n                                 ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n          url,\n          includeChunks: !0\n        })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:234:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:222:50\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Parameter Validation and Edge Cases > should handle invalid URLs gracefully [0.27ms]\n248 |       global.fetch = validFetch as any;\n249 | \n250 |       const invalidDepths = ['invalid', 'super-detailed', '', null, undefined, 123];\n251 | \n252 |       for (const depth of invalidDepths) {\n253 |         const result = await webSearchTool.execute({\n                                 ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:253:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:246:54\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Parameter Validation and Edge Cases > should handle invalid contentDepth values [0.19ms]\n274 |         [null, undefined, ''],\n275 |         [123, true, {}]\n276 |       ];\n277 | \n278 |       for (const chunkIds of invalidChunkIds) {\n279 |         const result = await webContentChunkTool.execute({\n                                 ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n          url: \"https://example.com\",\n          chunkIds,\n          includeChunks: !0\n        })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:279:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:266:53\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Parameter Validation and Edge Cases > should handle invalid chunkIds parameter [0.16ms]\n301 |         { fetchContent: 'not-boolean' as any },\n302 |         { enableChunking: 'invalid' as any }\n303 |       ];\n304 | \n305 |       for (const testCase of extremeCases) {\n306 |         const result = await webSearchTool.execute({\n                                 ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n          query: \"test\",\n          maxResults: 3,\n          fetchContent: !0,\n          ...testCase\n        })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:306:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:291:51\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Parameter Validation and Edge Cases > should handle extreme parameter values [0.18ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Chunk Processing Edge Cases > should handle content with no clear paragraph structure [3.46ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Chunk Processing Edge Cases > should handle content with only headings and no paragraphs [3.21ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Chunk Processing Edge Cases > should handle content with nested elements [4.34ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Chunk Processing Edge Cases > should handle content with tables and lists [6.58ms]\n388 |     test('should handle multiple concurrent failures gracefully', async () => {\n389 |       const failingFetch = mock(() => Promise.reject(new Error('Concurrent failure')));\n390 |       global.fetch = failingFetch as any;\n391 | \n392 |       const promises = Array.from({ length: 5 }, (_, i) => \n393 |         webSearchTool.execute({\n                                 ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:393:30\n      at from (:1:20)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:392:23\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:388:66\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Concurrent Operation Error Handling > should handle multiple concurrent failures gracefully [0.25ms]\n431 |       expect(results.length).toBe(6);\n432 |       \n433 |       const successes = results.filter(r => r.success).length;\n434 |       const failures = results.filter(r => !r.success).length;\n435 |       \n436 |       expect(successes).toBeGreaterThan(0);\n          ^\nerror: expect(received).toBeGreaterThan(expected)\n\nExpected: > 0\nReceived: 0\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:436:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Concurrent Operation Error Handling > should handle mixed success/failure scenarios [1036.69ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Error Handling and Edge Cases > Resource Management and Cleanup > should handle memory pressure gracefully [839.53ms]\n480 | \n481 |       const result = await fetchWebContent('https://example.com', true, {\n482 |         enableChunking: true\n483 |       });\n484 | \n485 |       expect(result.success).toBe(false);\n          ^\nerror: expect(received).toBe(expected)\n\nExpected: false\nReceived: true\n\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:485:6\n      at processTicksAndRejections (:55:76)\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Resource Management and Cleanup > should handle abort scenarios [109.96ms]\n497 |       ];\n498 | \n499 |       for (const errorFetch of errorScenarios) {\n500 |         global.fetch = mock(errorFetch) as any;\n501 | \n502 |         const result = await webContentChunkTool.execute({\n                                 ^\nTypeError: webContentChunkTool.execute is not a function. (In 'webContentChunkTool.execute({\n          url: \"https://example.com/error-test\",\n          includeChunks: !0\n        })', 'webContentChunkTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:502:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingErrorHandling.test.ts:491:63\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Error Handling and Edge Cases > Response Format Integrity > should maintain response format even during errors [0.35ms]\n\n 8 pass\n 19 fail\n 42 expect() calls\nRan 27 tests across 1 files. [3.31s]\n"}, {"suite": "Performance Benchmarks", "passed": false, "duration": 0, "output": "Memory Usage: {\n  initial: 27314161,\n  final: 33613597,\n  increase: 6299436,\n  increasePercentage: 23.06289400578696\n}\nMemory Efficiency: {\n  fullContent: 953583,\n  chunking: 110544,\n  efficiency: 88.40751145941151\n}\nHTML Size Performance: [\n  {\n    operation: \"HTML Size: 1x\",\n    iterations: 3,\n    totalTime: 317.5789590000022,\n    averageTime: 105.85965300000073,\n    minTime: 101.72837499999878,\n    maxTime: 111.50866700000188,\n    memoryUsage: 36913288\n  }, {\n    operation: \"HTML Size: 2x\",\n    iterations: 3,\n    totalTime: 301.37016599999697,\n    averageTime: 100.45672199999899,\n    minTime: 96.03299999999945,\n    maxTime: 107.52924999999959,\n    memoryUsage: 37757187\n  }, {\n    operation: \"HTML Size: 3x\",\n    iterations: 3,\n    totalTime: 319.39154100000087,\n    averageTime: 106.46384700000029,\n    minTime: 101.5499579999996,\n    maxTime: 109.51379200000156,\n    memoryUsage: 40667949\n  }\n]\n", "error": "bun test v1.0.3 (25e69c71)\n\nsrc/tests/chunkingPerformance.benchmark.test.ts:\nTimeout: test \"should benchmark basic content fetching vs chunking\" timed out after 5000ms\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Content Fetching Performance > should benchmark basic content fetching vs chunking [5004.40ms]\nTimeout: test \"should benchmark different content depths\" timed out after 5000ms\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Content Fetching Performance > should benchmark different content depths [9997.56ms]\n132 |   describe('Search Tool Performance', () => {\n133 |     test('should benchmark search without content fetching', async () => {\n134 |       const searchBenchmark = await benchmark(\n135 |         'Basic Search',\n136 |         async () => {\n137 |           return await webSearchTool.execute({\n                           ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n          query: \"performance test\",\n          maxResults: 3,\n          fetchContent: !1\n        })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:137:23\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:136:20\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:26:8\n      at benchmark (/Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:18:2)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:134:36\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:133:61\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Search Tool Performance > should benchmark search without content fetching [1.59ms]\n151 | \n152 |     test('should benchmark search with content fetching and chunking', async () => {\n153 |       const searchWithContentBenchmark = await benchmark(\n154 |         'Search with Content + Chunking',\n155 |         async () => {\n156 |           return await webSearchTool.execute({\n                           ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:156:23\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:155:20\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:26:8\n      at benchmark (/Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:18:2)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:153:47\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:152:71\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Search Tool Performance > should benchmark search with content fetching and chunking [0.46ms]\n176 | \n177 |       for (const count of resultCounts) {\n178 |         const result = await benchmark(\n179 |           `Results: ${count}`,\n180 |           async () => {\n181 |             return await webSearchTool.execute({\n                             ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:181:25\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:180:22\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:26:8\n      at benchmark (/Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:18:2)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:178:29\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:173:53\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Search Tool Performance > should benchmark different result counts [0.68ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Performance Benchmarks > Memory Usage Benchmarks > should monitor memory usage during chunking operations [802.50ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Performance Benchmarks > Memory Usage Benchmarks > should benchmark memory efficiency of chunking vs full content [626.43ms]\n268 |     test('should handle concurrent search requests efficiently', async () => {\n269 |       const concurrentOperations = 5;\n270 |       const startTime = performance.now();\n271 | \n272 |       const promises = Array.from({ length: concurrentOperations }, (_, i) => \n273 |         webSearchTool.execute({\n                                 ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n        query: `concurrent test ${i}`,\n        maxResults: 2,\n        fetchContent: !0,\n        enableChunking: !0\n      })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:273:30\n      at from (:1:20)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:272:23\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:268:65\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Concurrent Operation Performance > should handle concurrent search requests efficiently [0.31ms]\n307 |       const times: number[] = [];\n308 | \n309 |       for (let i = 0; i < iterations; i++) {\n310 |         const startTime = performance.now();\n311 |         \n312 |         await webSearchTool.execute({\n                  ^\nTypeError: undefined is not a function (near '...webSearchTool.execute...')\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:312:14\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:305:51\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Concurrent Operation Performance > should maintain performance under load [0.21ms]\n\u001b[0m\u001b[32m(pass)\u001b[0m Chunking Performance Benchmarks > Content Processing Performance > should benchmark different HTML sizes [1242.10ms]\n387 | \n388 |   describe('Performance Regression Detection', () => {\n389 |     test('should establish performance baselines', async () => {\n390 |       const baselines = {\n391 |         basicSearch: await benchmark('Basic Search Baseline', async () => {\n392 |           return await webSearchTool.execute({\n                           ^\nTypeError: webSearchTool.execute is not a function. (In 'webSearchTool.execute({\n            query: \"baseline test\",\n            maxResults: 3,\n            fetchContent: !1\n          })', 'webSearchTool.execute' is undefined)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:392:23\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:391:74\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:26:8\n      at benchmark (/Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:18:2)\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:391:27\n      at /Users/<USER>/dante-gpt/src/tests/chunkingPerformance.benchmark.test.ts:389:51\n\u001b[0m\u001b[31m(fail)\u001b[0m Chunking Performance Benchmarks > Performance Regression Detection > should establish performance baselines [0.39ms]\n\n 3 pass\n 8 fail\n 3 expect() calls\nRan 11 tests across 1 files. [18.04s]\n"}], "environment": {"nodeVersion": "v18.15.0", "platform": "darwin", "arch": "arm64"}}