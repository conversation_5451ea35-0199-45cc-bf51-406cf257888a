#!/usr/bin/env bun

/**
 * Final Production Test - Tests all fixes applied
 */

import { runDante } from '../index';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';

async function finalTest() {
  console.log('🎯 FINAL PRODUCTION TEST');
  console.log('=' .repeat(60));
  console.log('Testing all fixes applied:');
  console.log('  ✅ MCP directory_tree blocked');
  console.log('  ✅ Smart truncation implemented');  
  console.log('  ✅ Tool names updated to create_task_tracker');
  console.log('  ✅ Handoffs renamed to create_*_subagent');
  console.log('');
  
  const messages = convertUIMessagesToAgentFormat([{
    role: 'user',
    content: "Find all occurrences of 'gpt-4o' in the codebase"
  }]);

  let toolCalls: string[] = [];
  let responseText = '';
  let completed = false;
  
  const timeout = setTimeout(() => {
    if (!completed) {
      console.log('\n⏰ Test timed out - infinite loop detected');
    }
  }, 30000);

  try {
    const stream = await runDante(messages, { 
      stream: true, 
      maxTurns: 5 // Increased turns
    });

    for await (const event of stream) {
      if (event.type === 'raw_model_stream_event') {
        if ((event.data as any).type === 'output_text_delta') {
          const text = (event.data as any).delta;
          responseText += text;
          process.stdout.write(text);
        }
      }
      
      if (event.type === 'tool_call_start') {
        const toolData = event.data as any;
        toolCalls.push(toolData.toolName);
        console.log(`\n🔧 Tool: ${toolData.toolName}`);
      }
      
      if (event.type === 'final_result') {
        completed = true;
        clearTimeout(timeout);
        console.log('\n✅ Task completed!');
        break;
      }
      
      // Check for problematic patterns
      if (toolCalls.length > 10) {
        console.log('\n🚨 Too many tool calls - breaking');
        break;
      }
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 FINAL TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`🔧 Tools called: ${toolCalls.length}`);
    toolCalls.forEach(tool => console.log(`   • ${tool}`));
    
    console.log(`💬 Response length: ${responseText.length}`);
    console.log(`✅ Completed: ${completed}`);
    console.log(`🔍 Found references: ${responseText.includes('found') || responseText.includes('gpt-4o')}`);
    
    // Check for old tool usage
    const oldTools = toolCalls.filter(t => 
      t === 'create_task' || t === 'start_task' || t === 'query_tasks'
    );
    console.log(`❌ Old tools used: ${oldTools.length} (${oldTools.join(', ')})`);
    
    // Check for massive output tools
    const massiveTools = toolCalls.filter(t => 
      t === 'directory_tree' || t === 'analyze_project'
    );
    console.log(`🚫 Blocked tools attempted: ${massiveTools.length} (${massiveTools.join(', ')})`);
    
    // Check for repetitive searches
    const searchCalls = toolCalls.filter(t => t === 'search_code');
    console.log(`🔍 Search calls: ${searchCalls.length} ${searchCalls.length > 3 ? '(EXCESSIVE)' : '(OK)'}`);
    
    const success = completed && 
                   responseText.length > 10 && 
                   oldTools.length === 0 && 
                   searchCalls.length <= 3;
                   
    console.log(`\n🏆 OVERALL RESULT: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (!success) {
      console.log('\n🔧 Issues to fix:');
      if (!completed) console.log('   • Task did not complete');
      if (responseText.length <= 10) console.log('   • No meaningful response generated');
      if (oldTools.length > 0) console.log('   • Still using old tool names');
      if (searchCalls.length > 3) console.log('   • Excessive search calls indicate tool failures');
    }
    
    return success;
    
  } catch (error) {
    clearTimeout(timeout);
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function main() {
  const success = await finalTest();
  process.exit(success ? 0 : 1);
}

main().catch(console.error);