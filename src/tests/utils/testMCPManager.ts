import { mcpServerManager } from '../../mcp/MCPServerManager';
import { MCPServerConfig } from '../../mcp/MCPServerManager';

export async function setupTestMCPManager(): Promise<void> {
  const browserUseConfig: MCPServerConfig = {
    id: 'browser-use',
    name: 'Browser-Use Advanced Automation',
    type: 'stdio',
    config: {
      fullCommand: `${process.env.HOME}/.dante-gpt/run-browser-use-mcp.sh`,
      cacheToolsList: true,
      connectionTimeout: 45000,
      reconnectionOptions: {
        maxRetries: 2,
        retryDelay: 10000,
      },
    },
    enabled: true,
    priority: 96,
    tags: ['browser', 'automation', 'web', 'python', 'browser-use', 'advanced'],
  };

  if (process.env.OPENAI_API_KEY) {
    browserUseConfig.config.env = {
      OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    };
  }

  // Register the server with the global manager
  mcpServerManager.registerServer(browserUseConfig);

  // Connect to all registered servers
  await mcpServerManager.connectAllServers();
}

export async function stopTestMCPManager(): Promise<void> {
  try {
    await mcpServerManager.unregisterServer('browser-use');
  } catch (error) {
    console.warn('Could not unregister browser-use server, it might not have been registered.');
  }
  await mcpServerManager.disconnectAllServers();
}
