/**
 * Error Recovery Integration Tests
 * Validates the restored error recovery system functionality
 */

import { expect, test, describe, beforeAll } from "bun:test";
import { errorRecoveryMiddleware, RecoveryContext } from '../utils/errorRecoveryMiddleware';
import { hybridHandoffSystem } from '../utils/hybridHandoff';
import { diagnosticAgent } from '../agents/DiagnosticAgent';

describe('Error Recovery System Integration', () => {
  beforeAll(() => {
    // Reset stats before tests
    errorRecoveryMiddleware.resetStats();
  });

  test('Error recovery middleware should be instantiated', () => {
    expect(errorRecoveryMiddleware).toBeDefined();
    expect(typeof errorRecoveryMiddleware.recover).toBe('function');
    expect(typeof errorRecoveryMiddleware.isRecoverable).toBe('function');
    expect(typeof errorRecoveryMiddleware.getRecoveryStats).toBe('function');
  });

  test('Should identify recoverable errors', () => {
    const systemError = new Error('MCP server connection refused');
    const context: RecoveryContext = {
      sessionId: 'test-session',
      agentName: 'TestAgent',
      originalInput: 'test input',
      errorHistory: []
    };

    const isRecoverable = errorRecoveryMiddleware.isRecoverable(systemError, context);
    expect(isRecoverable).toBe(true);
  });

  test('Should identify non-recoverable errors', () => {
    const authError = new Error('Authentication failed - invalid API key');
    const context: RecoveryContext = {
      sessionId: 'test-session',
      agentName: 'TestAgent',
      originalInput: 'test input',
      errorHistory: []
    };

    const isRecoverable = errorRecoveryMiddleware.isRecoverable(authError, context);
    expect(isRecoverable).toBe(false);
  });

  test('Should track recovery statistics', () => {
    const stats = errorRecoveryMiddleware.getRecoveryStats();
    
    expect(stats).toHaveProperty('totalRecoveries');
    expect(stats).toHaveProperty('successfulRecoveries');
    expect(stats).toHaveProperty('failedRecoveries');
    expect(stats).toHaveProperty('recentRecoveries');
    expect(stats).toHaveProperty('totalAttempts');
    expect(stats).toHaveProperty('diagnosticAgentInvocations');
    expect(stats).toHaveProperty('averageRecoveryTime');
    expect(stats).toHaveProperty('recentFailures');
    expect(stats).toHaveProperty('strategySuccessRates');
  });

  test('Hybrid handoff system should be instantiated', () => {
    expect(hybridHandoffSystem).toBeDefined();
    expect(typeof hybridHandoffSystem.handoff).toBe('function');
    expect(typeof hybridHandoffSystem.createWorkflow).toBe('function');
    expect(typeof hybridHandoffSystem.registerAgent).toBe('function');
    expect(typeof hybridHandoffSystem.registerStrategy).toBe('function');
  });

  test('Should provide handoff statistics', () => {
    const stats = hybridHandoffSystem.getHandoffStats();
    
    expect(stats).toHaveProperty('totalHandoffs');
    expect(stats).toHaveProperty('successfulHandoffs');
    expect(stats).toHaveProperty('averageConfidence');
    expect(stats).toHaveProperty('mostUsedAgent');
    expect(stats).toHaveProperty('recentHandoffs');
    expect(Array.isArray(stats.recentHandoffs)).toBe(true);
  });

  test('Diagnostic agent should be functional', () => {
    expect(diagnosticAgent).toBeDefined();
    expect(typeof diagnosticAgent.execute).toBe('function');
    expect(typeof diagnosticAgent.getName).toBe('function');
    expect(typeof diagnosticAgent.getDescription).toBe('function');
    expect(typeof diagnosticAgent.getCapabilities).toBe('function');
    expect(typeof diagnosticAgent.setModel).toBe('function');
  });

  test('Diagnostic agent should provide correct metadata', () => {
    expect(diagnosticAgent.getName()).toBe('Diagnostic Agent');
    expect(diagnosticAgent.getDescription()).toContain('diagnostic');
    
    const capabilities = diagnosticAgent.getCapabilities();
    expect(Array.isArray(capabilities)).toBe(true);
    expect(capabilities.length).toBeGreaterThan(0);
    expect(capabilities.some(cap => cap.includes('diagnostic'))).toBe(true);
  });

  test('Should be able to register agents with handoff system', () => {
    hybridHandoffSystem.registerAgent({
      name: 'TestAgent',
      description: 'Test agent for validation',
      strengths: ['testing', 'validation'],
      tools: ['test_tool'],
      specializations: ['unit testing'],
      maxSteps: 5
    });

    // No error should be thrown
    expect(true).toBe(true);
  });

  test('Should be able to create workflows', async () => {
    const workflow = await hybridHandoffSystem.createWorkflow(
      'Test Workflow',
      [
        {
          agent: 'TestAgent',
          input: 'Test step 1',
          metadata: { priority: 'high' }
        },
        {
          agent: 'TestAgent',
          input: 'Test step 2',
          metadata: { priority: 'medium' }
        }
      ],
      {
        executeImmediately: false
      }
    );

    expect(workflow).toBeDefined();
    expect(workflow.name).toBe('Test Workflow');
    expect(workflow.steps.length).toBe(2);
    expect(workflow.status).toBe('pending');
    expect(workflow.steps[0].status).toBe('pending');
  });

  test('Error recovery should handle connection errors', async () => {
    const connectionError = new Error('ECONNREFUSED: Connection refused to localhost:3000');
    const context: RecoveryContext = {
      sessionId: 'test-connection',
      agentName: 'TestAgent',
      originalInput: 'test connection',
      errorHistory: [{
        error: connectionError,
        timestamp: Date.now(),
        recoveryAttempted: false
      }]
    };

    // This should not throw an error (basic validation)
    const isRecoverable = errorRecoveryMiddleware.isRecoverable(connectionError, context);
    expect(isRecoverable).toBe(true);
  });

  test('Should maintain recovery statistics after operations', () => {
    const initialStats = errorRecoveryMiddleware.getRecoveryStats();
    const statsAfterTests = errorRecoveryMiddleware.getRecoveryStats();
    
    // Basic validation that stats structure is maintained
    expect(typeof statsAfterTests.totalRecoveries).toBe('number');
    expect(typeof statsAfterTests.successfulRecoveries).toBe('number');
    expect(typeof statsAfterTests.failedRecoveries).toBe('number');
    expect(Array.isArray(statsAfterTests.recentRecoveries)).toBe(true);
    expect(typeof statsAfterTests.averageRecoveryTime).toBe('number');
  });

  test('Should reset statistics correctly', () => {
    errorRecoveryMiddleware.resetStats();
    
    const stats = errorRecoveryMiddleware.getRecoveryStats();
    expect(stats.totalRecoveries).toBe(0);
    expect(stats.successfulRecoveries).toBe(0);
    expect(stats.failedRecoveries).toBe(0);
    expect(stats.recentRecoveries.length).toBe(0);
    expect(stats.totalAttempts).toBe(0);
    expect(stats.diagnosticAgentInvocations).toBe(0);
    expect(stats.averageRecoveryTime).toBe(0);
    expect(stats.recentFailures.length).toBe(0);
    expect(Object.keys(stats.strategySuccessRates).length).toBe(0);
  });
});

describe('Error Recovery System Validation', () => {
  test('All required exports should be available', () => {
    // Validate errorRecoveryMiddleware exports
    expect(errorRecoveryMiddleware).toBeDefined();
    expect(errorRecoveryMiddleware.recover).toBeDefined();
    expect(errorRecoveryMiddleware.isRecoverable).toBeDefined();
    expect(errorRecoveryMiddleware.getRecoveryStats).toBeDefined();
    expect(errorRecoveryMiddleware.resetStats).toBeDefined();

    // Validate hybridHandoffSystem exports
    expect(hybridHandoffSystem).toBeDefined();
    expect(hybridHandoffSystem.handoff).toBeDefined();
    expect(hybridHandoffSystem.createWorkflow).toBeDefined();
    expect(hybridHandoffSystem.executeWorkflow).toBeDefined();
    expect(hybridHandoffSystem.registerAgent).toBeDefined();
    expect(hybridHandoffSystem.registerStrategy).toBeDefined();
    expect(hybridHandoffSystem.getHandoffStats).toBeDefined();

    // Validate diagnosticAgent exports
    expect(diagnosticAgent).toBeDefined();
    expect(diagnosticAgent.execute).toBeDefined();
    expect(diagnosticAgent.getName).toBeDefined();
    expect(diagnosticAgent.getDescription).toBeDefined();
    expect(diagnosticAgent.getCapabilities).toBeDefined();
    expect(diagnosticAgent.setModel).toBeDefined();
  });

  test('Error recovery system should handle edge cases', () => {
    // Test with null/undefined inputs
    expect(() => {
      const context: RecoveryContext = {
        sessionId: '',
        agentName: '',
        originalInput: '',
        errorHistory: []
      };
      errorRecoveryMiddleware.isRecoverable(new Error(''), context);
    }).not.toThrow();

    // Test with empty error message
    const emptyError = new Error('');
    const context: RecoveryContext = {
      sessionId: 'test',
      agentName: 'test',
      originalInput: 'test',
      errorHistory: []
    };
    
    const isRecoverable = errorRecoveryMiddleware.isRecoverable(emptyError, context);
    expect(typeof isRecoverable).toBe('boolean');
  });

  test('Diagnostic agent should handle model switching', () => {
    const originalModel = 'gpt-4o';
    const newModel = 'gpt-4o-mini';
    
    diagnosticAgent.setModel(newModel);
    // Should not throw error
    expect(true).toBe(true);
    
    // Reset to original
    diagnosticAgent.setModel(originalModel);
  });
});