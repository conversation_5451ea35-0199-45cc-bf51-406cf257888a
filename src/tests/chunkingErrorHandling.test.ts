import { describe, test, expect, beforeEach } from 'bun:test';
import { webSearchTool } from '../tools/webSearch';
import { webContentChunkTool } from '../tools/webContentChunk';
import { fetchWebContent, extractTextContent } from '../tools/webSearchHelpers';

// Error simulation utilities
const createMockResponse = (status: number, statusText: string, content?: string) => ({
  ok: status >= 200 && status < 300,
  status,
  statusText,
  headers: new Map([['content-type', 'text/html']]),
  text: () => content ? Promise.resolve(content) : Promise.reject(new Error(statusText))
});

const createNetworkError = (message: string) => new Error(message);

describe('Chunking Error Handling and Edge Cases', () => {
  beforeEach(() => {
    console.error = () => {}; // Suppress error logs in tests
  });

  describe('Network Error Handling', () => {
    test('should handle DNS resolution failures', async () => {
      const dnsError = () => Promise.reject(createNetworkError('ENOTFOUND'));
      global.fetch = dnsError as any;

      const result = await fetchWebContent('https://non-existent-domain.invalid', true, {
        enableChunking: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('ENOTFOUND');
      expect(result.chunks).toBeUndefined();
    });

    test('should handle connection timeouts', async () => {
      const timeoutError = () => new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 50);
      });
      global.fetch = timeoutError as any;

      const result = await fetchWebContent('https://slow-site.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });

    test('should handle connection refused errors', async () => {
      const connectionError = () => Promise.reject(createNetworkError('ECONNREFUSED'));
      global.fetch = connectionError as any;

      const result = await fetchWebContent('https://localhost:99999', true, {
        enableChunking: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('ECONNREFUSED');
    });

    test('should handle SSL/TLS errors', async () => {
      const sslError = () => Promise.reject(createNetworkError('SSL certificate error'));
      global.fetch = sslError as any;

      const result = await fetchWebContent('https://invalid-ssl.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('SSL certificate error');
    });
  });

  describe('HTTP Error Status Handling', () => {
    test('should handle 404 Not Found', async () => {
      const notFoundFetch = () => Promise.resolve(createMockResponse(404, 'Not Found'));
      global.fetch = notFoundFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/not-found',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(false);
      expect(data.error).toContain('404');
    });

    test('should handle 403 Forbidden', async () => {
      const forbiddenFetch = () => Promise.resolve(createMockResponse(403, 'Forbidden'));
      global.fetch = forbiddenFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/forbidden',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(false);
      expect(data.error).toContain('403');
    });

    test('should handle 500 Internal Server Error', async () => {
      const serverErrorFetch = () => Promise.resolve(createMockResponse(500, 'Internal Server Error'));
      global.fetch = serverErrorFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/server-error',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(false);
      expect(data.error).toContain('500');
    });

    test('should handle 429 Rate Limiting', async () => {
      const rateLimitFetch = () => Promise.resolve(createMockResponse(429, 'Too Many Requests'));
      global.fetch = rateLimitFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/rate-limited',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(false);
      expect(data.error).toContain('429');
    });

    test('should handle redirects that fail', async () => {
      const redirectFetch = () => Promise.resolve(createMockResponse(301, 'Moved Permanently'));
      global.fetch = redirectFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/redirect',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(false);
      expect(data.error).toContain('301');
    });
  });

  describe('Content Processing Error Handling', () => {
    test('should handle empty HTML responses', async () => {
      const emptyFetch = () => Promise.resolve(createMockResponse(200, 'OK', ''));
      global.fetch = emptyFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
      expect(result.chunks).toBeDefined();
      // Should handle empty content gracefully
    });

    test('should handle malformed HTML', async () => {
      const malformedHTML = '<html><body><p>Unclosed paragraph<div>Mixed<span>tags</div></p></body>';
      const malformedFetch = () => Promise.resolve(createMockResponse(200, 'OK', malformedHTML));
      global.fetch = malformedFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(true);
      expect(result.content).toContain('Unclosed paragraph');
      // Should not crash on malformed HTML
    });

    test('should handle HTML with no text content', async () => {
      const noTextHTML = '<html><body><script>console.log("only scripts");</script><style>body{color:red;}</style></body></html>';
      const noTextFetch = () => Promise.resolve(createMockResponse(200, 'OK', noTextHTML));
      global.fetch = noTextFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(true);
      expect(result.chunks).toBeDefined();
      // Should handle content with no extractable text
    });

    test('should handle extremely large HTML responses', async () => {
      const largeHTML = '<html><body>' + '<p>Large content paragraph. '.repeat(10000) + '</p></body></html>';
      const largeFetch = () => Promise.resolve(createMockResponse(200, 'OK', largeHTML));
      global.fetch = largeFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
      // Should handle large content without crashing
      expect(result.content!.length).toBeLessThanOrEqual(10000); // Should be truncated
    });

    test('should handle HTML with special characters and encoding', async () => {
      const specialCharsHTML = '<html><body><p>Special chars: áéíóú ñ 中文 🚀 ⚡ </p></body></html>';
      const specialCharsFetch = () => Promise.resolve(createMockResponse(200, 'OK', specialCharsHTML));
      global.fetch = specialCharsFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(true);
      expect(result.content).toContain('Special chars');
      expect(result.content).toContain('中文');
      expect(result.content).toContain('🚀');
    });
  });

  describe('Parameter Validation and Edge Cases', () => {
    test('should handle invalid URLs gracefully', async () => {
      const invalidUrls = [
        'not-a-url',
        'http://',
        'https://',
        'ftp://example.com',
        '',
        null,
        undefined
      ];

      for (const url of invalidUrls) {
        const result = await (webContentChunkTool as any).execute({
          url: url as any,
          includeChunks: true
        });

        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        expect(data).toHaveProperty('url');
        // Should handle invalid URLs without crashing
      }
    });

    test('should handle invalid contentDepth values', async () => {
      const validFetch = () => Promise.resolve(createMockResponse(200, 'OK', '<html><body><p>Test</p></body></html>'));
      global.fetch = validFetch as any;

      const invalidDepths = ['invalid', 'super-detailed', '', null, undefined, 123];

      for (const depth of invalidDepths) {
        const result = await (webSearchTool as any).execute({
          query: 'test query',
          fetchContent: true,
          contentDepth: depth as any,
          enableChunking: true
        });

        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        // Should handle invalid depth gracefully (use default)
      }
    });

    test('should handle invalid chunkIds parameter', async () => {
      const validFetch = () => Promise.resolve(createMockResponse(200, 'OK', '<html><body><p>Test content</p></body></html>'));
      global.fetch = validFetch as any;

      const invalidChunkIds = [
        'not-an-array',
        123,
        null,
        [null, undefined, ''],
        [123, true, {}]
      ];

      for (const chunkIds of invalidChunkIds) {
        const result = await (webContentChunkTool as any).execute({
          url: 'https://example.com',
          chunkIds: chunkIds as any,
          includeChunks: true
        });

        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        // Should handle invalid chunkIds without crashing
      }
    });

    test('should handle extreme parameter values', async () => {
      const validFetch = () => Promise.resolve(createMockResponse(200, 'OK', '<html><body><p>Test</p></body></html>'));
      global.fetch = validFetch as any;

      const extremeCases = [
        { maxResults: -1 },
        { maxResults: 0 },
        { maxResults: 1000000 },
        { query: '' },
        { query: 'a'.repeat(10000) },
        { fetchContent: 'not-boolean' as any },
        { enableChunking: 'invalid' as any }
      ];

      for (const testCase of extremeCases) {
        const result = await (webSearchTool as any).execute({
          query: 'test',
          maxResults: 3,
          fetchContent: true,
          ...testCase
        });

        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        expect(data).toHaveProperty('query');
        // Should handle extreme values gracefully
      }
    });
  });

  describe('Chunk Processing Edge Cases', () => {
    test('should handle content with no clear paragraph structure', async () => {
      const noStructureHTML = '<html><body>Just one long continuous text without any paragraph breaks or clear structure whatsoever this is a very long sentence that goes on and on</body></html>';

      const result = extractTextContent(noStructureHTML, 'https://example.com');

      expect(result.content).toBeDefined();
      expect(result.title).toBeDefined();
      // Should not crash when chunking unstructured content
    });

    test('should handle content with only headings and no paragraphs', async () => {
      const headingsOnlyHTML = '<html><body><h1>Title</h1><h2>Subtitle</h2><h3>Sub-subtitle</h3></body></html>';

      const result = extractTextContent(headingsOnlyHTML, 'https://example.com');

      expect(result.content).toContain('Title');
      expect(result.content).toContain('Subtitle');
    });

    test('should handle content with nested elements', async () => {
      const nestedHTML = `
        <html><body>
          <div>
            <article>
              <section>
                <div>
                  <p>Deeply nested <span>content <strong>here</strong></span></p>
                </div>
              </section>
            </article>
          </div>
        </body></html>
      `;

      const result = extractTextContent(nestedHTML, 'https://example.com');

      expect(result.content).toContain('Deeply nested content here');
    });

    test('should handle content with tables and lists', async () => {
      const tableListHTML = `
        <html><body>
          <table>
            <tr><td>Cell 1</td><td>Cell 2</td></tr>
            <tr><td>Cell 3</td><td>Cell 4</td></tr>
          </table>
          <ul>
            <li>List item 1</li>
            <li>List item 2</li>
          </ul>
          <ol>
            <li>Ordered item 1</li>
            <li>Ordered item 2</li>
          </ol>
        </body></html>
      `;

      const result = extractTextContent(tableListHTML, 'https://example.com');

      expect(result.content).toContain('Cell 1');
      expect(result.content).toContain('List item 1');
      expect(result.content).toContain('Ordered item 1');
    });
  });

  describe('Concurrent Operation Error Handling', () => {
    test('should handle multiple concurrent failures gracefully', async () => {
      const failingFetch = () => Promise.reject(new Error('Concurrent failure'));
      global.fetch = failingFetch as any;

      const promises = Array.from({ length: 5 }, (_, i) =>
        (webSearchTool as any).execute({
          query: `concurrent failure test ${i}`,
          maxResults: 2,
          fetchContent: true,
          enableChunking: true
        })
      );

      const results = await Promise.all(promises);

      expect(results.length).toBe(5);
      results.forEach(result => {
        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        expect(data).toHaveProperty('query');
        // All should handle errors gracefully
      });
    });

    test('should handle mixed success/failure scenarios', async () => {
      let callCount = 0;
      const mixedFetch = () => {
        callCount++;
        if (callCount % 2 === 0) {
          return Promise.reject(new Error('Intermittent failure'));
        }
        return Promise.resolve(createMockResponse(200, 'OK', '<html><body><p>Success</p></body></html>'));
      };
      global.fetch = mixedFetch as any;

      const promises = Array.from({ length: 6 }, (_, i) =>
        fetchWebContent(`https://example.com/test-${i}`, true, {
          enableChunking: true
        })
      );

      const results = await Promise.all(promises);

      expect(results.length).toBe(6);

      const successes = results.filter(r => r.success).length;
      const failures = results.filter(r => !r.success).length;

      expect(successes).toBeGreaterThan(0);
      expect(failures).toBeGreaterThan(0);
      expect(successes + failures).toBe(6);
    });
  });

  describe('Resource Management and Cleanup', () => {
    test('should handle memory pressure gracefully', async () => {
      const largeFetch = () => Promise.resolve(createMockResponse(
        200,
        'OK',
        '<html><body>' + '<p>Large paragraph. '.repeat(50000) + '</p></body></html>'
      ));
      global.fetch = largeFetch as any;

      // Process multiple large documents
      const promises = Array.from({ length: 10 }, (_, i) =>
        fetchWebContent(`https://example.com/large-${i}`, true, {
          enableChunking: true,
          contentDepth: 'full'
        })
      );

      const results = await Promise.all(promises);

      // Should handle large content without running out of memory
      expect(results.length).toBe(10);
      results.forEach(result => {
        expect(result).toHaveProperty('success');
        if (result.success) {
          // Content should be properly truncated
          expect(result.content!.length).toBeLessThanOrEqual(10000);
        }
      });
    });

    test('should handle abort scenarios', async () => {
      // Test with AbortController-like scenarios
      const abortFetch = () => {
        const error = new Error('The operation was aborted');
        error.name = 'AbortError';
        return Promise.reject(error);
      };
      global.fetch = abortFetch as any;

      const result = await fetchWebContent('https://example.com', true, {
        enableChunking: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('aborted');
    });
  });

  describe('Response Format Integrity', () => {
    test('should maintain response format even during errors', async () => {
      const errorScenarios = [
        () => Promise.reject(new Error('Network error')),
        () => Promise.resolve(createMockResponse(404, 'Not Found')),
        () => Promise.resolve(createMockResponse(500, 'Server Error')),
        () => Promise.reject(new Error('DNS error'))
      ];

      for (const errorFetch of errorScenarios) {
        global.fetch = errorFetch as any;

        const result = await (webContentChunkTool as any).execute({
          url: 'https://example.com/error-test',
          includeChunks: true
        });

        const data = JSON.parse(result);

        // Should always have these fields
        expect(data).toHaveProperty('success');
        expect(data).toHaveProperty('url');
        expect(data).toHaveProperty('timestamp');

        // Error cases should have error field
        if (!data.success) {
          expect(data).toHaveProperty('error');
          expect(typeof data.error).toBe('string');
          expect(data.error.length).toBeGreaterThan(0);
        }
      }
    });
  });
});
