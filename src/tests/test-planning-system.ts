#!/usr/bin/env bun

/**
 * Simple test script to verify the multi-step planning system works
 */

import { needsPlanning, estimateComplexity } from '../agents/PlanningAgent';
import { agentTaskManager } from '../tools/taskManagement';

console.log('🧪 Testing Multi-Step Planning System\n');

// Test 1: Planning detection
console.log('1. Testing planning detection:');
const testRequests = [
  'Create a user registration feature with email verification and password reset',
  'Find the source of this bug: API returns 500 error',
  'How is the weather today?',
  'Optimize performance across the entire application',
  'Debug why the authentication system is failing',
];

testRequests.forEach(request => {
  const needs = needsPlanning(request);
  const complexity = estimateComplexity(request);
  console.log(`  "${request}"`);
  console.log(`    Needs planning: ${needs ? '✅' : '❌'}`);
  console.log(`    Complexity: ${complexity}\n`);
});

// Test 2: Task management
console.log('2. Testing task management:');

// Create a main task
const mainTaskId = agentTaskManager.createTask({
  description: 'Implement user authentication system',
  priority: 'high',
  status: 'pending',
  estimatedTokens: 15000,
});

console.log(`  ✅ Created main task: ${mainTaskId}`);

// Create subtasks
const subtask1 = agentTaskManager.createSubtask(mainTaskId, {
  description: 'Design database schema for users',
  priority: 'high',
  status: 'pending',
  estimatedTokens: 3000,
  assignedAgent: 'Code Generation Agent',
});

const subtask2 = agentTaskManager.createSubtask(mainTaskId, {
  description: 'Implement registration endpoint',
  priority: 'high',
  status: 'pending',
  estimatedTokens: 5000,
  assignedAgent: 'Code Generation Agent',
});

const subtask3 = agentTaskManager.createSubtask(mainTaskId, {
  description: 'Add authentication tests',
  priority: 'medium',
  status: 'pending',
  estimatedTokens: 4000,
  assignedAgent: 'Code Generation Agent',
});

console.log(`  ✅ Created subtask 1: ${subtask1}`);
console.log(`  ✅ Created subtask 2: ${subtask2}`);
console.log(`  ✅ Created subtask 3: ${subtask3}`);

// Test task status updates
agentTaskManager.startTask(subtask1, 'Code Generation Agent');
console.log(`  ✅ Started subtask 1`);

agentTaskManager.updateProgress(subtask1, 50);
console.log(`  ✅ Updated progress for subtask 1 to 50%`);

agentTaskManager.completeTask(subtask1, 2800);
console.log(`  ✅ Completed subtask 1`);

// Test task queries
const pendingTasks = agentTaskManager.getTasksByStatus('pending');
const completedTasks = agentTaskManager.getTasksByStatus('completed');
const agentTasks = agentTaskManager.getTasksByAgent('Code Generation Agent');

console.log(`\n3. Testing task queries:`);
console.log(`  Pending tasks: ${pendingTasks.length}`);
console.log(`  Completed tasks: ${completedTasks.length}`);
console.log(`  Tasks for Code Generation Agent: ${agentTasks.length}`);

// Display all tasks
const allTasks = agentTaskManager.getAllTasks();
console.log(`\n4. All tasks in system:`);
allTasks.forEach(task => {
  const indent = task.parentTaskId ? '    ' : '  ';
  console.log(`${indent}[${task.status}] ${task.description} (${task.priority})`);
  if (task.progress > 0) {
    console.log(`${indent}Progress: ${task.progress}%`);
  }
  if (task.assignedAgent) {
    console.log(`${indent}Assigned to: ${task.assignedAgent}`);
  }
});

console.log('\n🎉 Multi-step planning system test completed successfully!');
console.log('\nKey features verified:');
console.log('  ✅ Automatic complex task detection');
console.log('  ✅ Task creation and hierarchy');
console.log('  ✅ Progress tracking');
console.log('  ✅ Agent assignment');
console.log('  ✅ Task status management');
console.log('  ✅ Task querying and filtering');
