import { describe, test, expect } from 'bun:test';
import { 
  extractTextContent, 
  chunkContent,
  generateSummary,
  aggregateResults
} from '../tools/webSearchHelpers';
import { mockNewsArticleHTML } from './fixtures/webContent';

describe('Simple Chunking Tests', () => {
  test('should extract text content from HTML', () => {
    const result = extractTextContent(mockNewsArticleHTML, 'https://example.com');
    
    expect(result.title).toBe('Google Pixel 10 Release Date Finally Revealed');
    expect(result.content).toContain('Google has finally announced');
    expect(result.content).not.toContain('Navigation menu');
  });

  test('should create chunks from content', () => {
    const content = `<PERSON> has finally announced the official release date for the highly anticipated Pixel 10 smartphone.

This is a middle section with more details about the phone features.

In conclusion, the Pixel 10 represents Google's most ambitious smartphone effort to date.`;
    
    const chunks = chunkContent(content, 'Test Article');
    
    expect(Array.isArray(chunks)).toBe(true);
    expect(chunks.length).toBeGreaterThan(0);
    
    const introChunk = chunks.find(c => c.type === 'intro');
    expect(introChunk).toBeDefined();
    expect(introChunk!.priority).toBe(10);
    
    const conclusionChunk = chunks.find(c => c.type === 'conclusion');
    expect(conclusionChunk).toBeDefined();
    expect(conclusionChunk!.priority).toBe(9);
  });
  
  test('should generate summaries from chunks', () => {
    const chunks = [
      { 
        id: 'intro', 
        title: 'Introduction', 
        content: 'This is the introduction with important information about the topic.', 
        type: 'intro' as const, 
        priority: 10, 
        wordCount: 10 
      },
      { 
        id: 'conclusion', 
        title: 'Conclusion', 
        content: 'In conclusion, this is the final summary of everything discussed.', 
        type: 'conclusion' as const, 
        priority: 9, 
        wordCount: 11 
      }
    ];
    
    const summary = generateSummary(chunks, 200);
    
    expect(summary).toBeDefined();
    expect(summary.length).toBeGreaterThan(10);
    expect(summary.length).toBeLessThanOrEqual(200);
    expect(summary).toContain('introduction');
  });

  test('should aggregate and deduplicate search results', () => {
    const results1 = [
      { title: 'Test 1', url: 'https://example.com/1', snippet: 'Test', source: 'A' },
      { title: 'Test 2', url: 'https://example.com/2', snippet: 'Test', source: 'A' }
    ];
    
    const results2 = [
      { title: 'Test 1 Duplicate', url: 'https://example.com/1', snippet: 'Different', source: 'B' },
      { title: 'Test 3', url: 'https://example.com/3', snippet: 'Test', source: 'B' }
    ];
    
    const aggregated = aggregateResults([results1, results2]);
    
    expect(aggregated.length).toBe(3); // Should deduplicate the first URL
    expect(aggregated.map(r => r.url)).toEqual([
      'https://example.com/1',
      'https://example.com/2', 
      'https://example.com/3'
    ]);
  });
});