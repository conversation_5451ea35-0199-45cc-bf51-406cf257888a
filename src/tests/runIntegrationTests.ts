#!/usr/bin/env bun

/**
 * Integration Test Runner for Tool Output and Agent Handoff Analysis
 * 
 * This script runs comprehensive tests to analyze:
 * 1. Tool output formats and truncation behavior
 * 2. Agent handoff accuracy and timing
 * 3. End-to-end search functionality
 * 4. Task classification correctness
 */

import { spawn } from 'bun';
import * as fs from 'fs/promises';
import * as path from 'path';

interface TestResults {
  timestamp: string;
  toolOutputTests: any;
  handoffTests: any;
  summary: {
    totalTests: number;
    passed: number;
    failed: number;
    issues: string[];
    recommendations: string[];
  };
}

async function runTestSuite(testFile: string, description: string): Promise<any> {
  console.log(`\n🧪 Running ${description}...`);
  console.log(`📁 Test file: ${testFile}`);
  
  try {
    const repoRoot = path.resolve(process.cwd());
    const proc = spawn(['bun', 'test', testFile, '--verbose'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: repoRoot
    });

    let stdout = '';
    let stderr = '';

    proc.stdout?.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      process.stdout.write(output);
    });

    proc.stderr?.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      process.stderr.write(output);
    });

    const exitCode = await new Promise<number>((resolve) => {
      proc.on('exit', (code) => resolve(code || 0));
    });

    return {
      exitCode,
      stdout,
      stderr,
      success: exitCode === 0
    };

  } catch (error) {
    console.error(`❌ Failed to run ${description}:`, error);
    return {
      exitCode: 1,
      stdout: '',
      stderr: error instanceof Error ? error.message : 'Unknown error',
      success: false
    };
  }
}

function analyzeTestOutput(output: string): any {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Analyze for common issues
  if (output.includes('Multiple search calls detected')) {
    issues.push('Agent is making redundant search calls');
    recommendations.push('Review search result caching and tool output truncation');
  }
  
  if (output.includes('Task management tools used for simple search')) {
    issues.push('Agent incorrectly using task management for simple tasks');
    recommendations.push('Review agent instructions for task classification');
  }
  
  if (output.includes('JSON output truncated')) {
    issues.push('Tool outputs being truncated, potentially losing data');
    recommendations.push('Adjust sanitizeToolOutput limits based on tool type');
  }
  
  if (output.includes('Data corrupted')) {
    issues.push('Data corruption detected in truncated outputs');
    recommendations.push('Implement smarter truncation that preserves JSON structure');
  }

  // Extract metrics
  const matches = {
    testsPassed: (output.match(/✓/g) || []).length,
    testsFailed: (output.match(/✗|❌/g) || []).length,
    searchCalls: (output.match(/Tool: search_code/g) || []).length,
    agentSwitches: (output.match(/Agent switch:/g) || []).length,
    subagentTools: (output.match(/create_\w+_subagent/g) || []).length
  };

  return {
    issues,
    recommendations,
    metrics: matches
  };
}

async function generateReport(results: TestResults): Promise<void> {
  const repoRoot = path.resolve(process.cwd());
  const reportPath = path.join(repoRoot, 'integration-test-report.md');
  
  const report = `# Integration Test Report

Generated: ${results.timestamp}

## Summary
- **Total Tests:** ${results.summary.totalTests}
- **Passed:** ${results.summary.passed}
- **Failed:** ${results.summary.failed}
- **Success Rate:** ${((results.summary.passed / results.summary.totalTests) * 100).toFixed(1)}%

## Issues Detected
${results.summary.issues.length > 0 
  ? results.summary.issues.map(issue => `- ❌ ${issue}`).join('\n')
  : '✅ No issues detected'
}

## Recommendations
${results.summary.recommendations.length > 0 
  ? results.summary.recommendations.map(rec => `- 💡 ${rec}`).join('\n')
  : '✅ System appears to be functioning correctly'
}

## Tool Output Test Results
\`\`\`json
${JSON.stringify(results.toolOutputTests, null, 2)}
\`\`\`

## Agent Handoff Test Results  
\`\`\`json
${JSON.stringify(results.handoffTests, null, 2)}
\`\`\`

---
*Report generated by integration test runner*
`;

  await fs.writeFile(reportPath, report);
  console.log(`\n📊 Detailed report saved to: ${reportPath}`);
}

async function main() {
  console.log('🚀 Starting Comprehensive Integration Test Suite');
  console.log('=' .repeat(60));
  
  const results: TestResults = {
    timestamp: new Date().toISOString(),
    toolOutputTests: {},
    handoffTests: {},
    summary: {
      totalTests: 0,
      passed: 0, 
      failed: 0,
      issues: [],
      recommendations: []
    }
  };

  // Run tool output tests
  const toolOutputResult = await runTestSuite(
    'src/tests/toolOutputIntegration.test.ts',
    'Tool Output Integration Tests'
  );
  
  results.toolOutputTests = {
    ...toolOutputResult,
    analysis: analyzeTestOutput(toolOutputResult.stdout)
  };

  // Run agent handoff tests
  const handoffResult = await runTestSuite(
    'src/tests/agentHandoffIntegration.test.ts', 
    'Agent Handoff Integration Tests'
  );
  
  results.handoffTests = {
    ...handoffResult,
    analysis: analyzeTestOutput(handoffResult.stdout)
  };

  // Compile summary
  results.summary.totalTests = 
    (results.toolOutputTests.analysis?.metrics?.testsPassed || 0) +
    (results.toolOutputTests.analysis?.metrics?.testsFailed || 0) +
    (results.handoffTests.analysis?.metrics?.testsPassed || 0) +
    (results.handoffTests.analysis?.metrics?.testsFailed || 0);
    
  results.summary.passed = 
    (results.toolOutputTests.analysis?.metrics?.testsPassed || 0) +
    (results.handoffTests.analysis?.metrics?.testsPassed || 0);
    
  results.summary.failed = 
    (results.toolOutputTests.analysis?.metrics?.testsFailed || 0) +
    (results.handoffTests.analysis?.metrics?.testsFailed || 0);

  results.summary.issues = [
    ...(results.toolOutputTests.analysis?.issues || []),
    ...(results.handoffTests.analysis?.issues || [])
  ];
  
  results.summary.recommendations = [
    ...(results.toolOutputTests.analysis?.recommendations || []),
    ...(results.handoffTests.analysis?.recommendations || [])
  ];

  // Generate comprehensive report
  await generateReport(results);

  console.log('\n' + '='.repeat(60));
  console.log('🎯 INTEGRATION TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${results.summary.passed}`);
  console.log(`❌ Tests Failed: ${results.summary.failed}`);
  console.log(`🔍 Issues Found: ${results.summary.issues.length}`);
  console.log(`💡 Recommendations: ${results.summary.recommendations.length}`);
  
  if (results.summary.issues.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES:');
    results.summary.issues.forEach(issue => console.log(`  • ${issue}`));
  }
  
  if (results.summary.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    results.summary.recommendations.forEach(rec => console.log(`  • ${rec}`));
  }

  const overallSuccess = results.summary.failed === 0 && results.summary.issues.length === 0;
  console.log(`\n🎉 Overall Status: ${overallSuccess ? '✅ PASS' : '⚠️ NEEDS ATTENTION'}`);
  
  process.exit(overallSuccess ? 0 : 1);
}

// Run the test suite
main().catch((error) => {
  console.error('💥 Integration test runner failed:', error);
  process.exit(1);
});