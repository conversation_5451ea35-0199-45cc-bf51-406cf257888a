#!/usr/bin/env bun

async function testAPISearch() {
  console.log('Testing web search through API...\n');
  
  try {
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'What is the latest news about artificial intelligence today? Please search the web for current information.'
          }
        ],
        model: 'gpt-5-mini'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let result = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      result += chunk;
      process.stdout.write(chunk); // Stream output
    }
    
    console.log('\n\nTest completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAPISearch();