import { describe, test, expect, mock, beforeEach } from 'bun:test';
import { researchAgent } from '../agents/ResearchAgent';
import DanteOrchestrator from '../agents/DanteOrchestrator';
import { mockNewsArticleHTML, mockSearchResults } from './fixtures/webContent';

// Mock external dependencies for agent testing
const mockFetch = mock(() => Promise.resolve({
  ok: true,
  status: 200,
  headers: new Map([['content-type', 'text/html']]),
  text: () => Promise.resolve(mockNewsArticleHTML)
}));

global.fetch = mockFetch as any;

describe('Agent Integration with Chunking Features', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    console.error = mock(() => {}); // Suppress test logs
  });

  describe('ResearchAgent with Chunking', () => {
    test('should have access to all web search tools including chunking', () => {
      const toolNames = researchAgent.tools.map((tool: any) => tool.name);

      expect(toolNames).toContain('web_search');
      expect(toolNames).toContain('web_content_chunk');
      expect(toolNames).toContain('web_fetch');
      expect(toolNames).toContain('enhanced_web_search');
      expect(toolNames).toContain('web_research');
    });

    test('should be able to execute web search with chunking parameters', async () => {
      // Find the web_search tool in the agent
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      expect(webSearchTool).toBeDefined();

      if (webSearchTool) {
        const result = await (webSearchTool as any).execute({
          query: 'AI research breakthrough',
          maxResults: 3,
          fetchContent: true,
          contentDepth: 'detailed',
          enableChunking: true
        });

        const data = JSON.parse(result);
        expect(data.success).toBeDefined();
        expect(data.results).toBeDefined();
      }
    });

    test('should be able to execute chunk-specific content fetching', async () => {
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      expect(chunkTool).toBeDefined();

      if (chunkTool) {
        const result = await (chunkTool as any).execute({
          url: 'https://example.com/research-article',
          chunkIds: ['intro', 'conclusion'],
          includeChunks: true
        });

        const data = JSON.parse(result);
        expect(data.success).toBeDefined();
      }
    });

    test('should handle tool execution errors gracefully', async () => {
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (chunkTool) {
        // Test with invalid URL
        const result = await (chunkTool as any).execute({
          url: 'invalid-url-format',
          includeChunks: true
        });

        const data = JSON.parse(result);
        expect(data).toHaveProperty('success');
        // Should not throw errors even with invalid input
      }
    });
  });

  describe('DanteCore with Chunking', () => {
    test('should have access to chunking tools', () => {
      const toolNames = DanteOrchestrator.tools.map((tool: any) => tool.name);

      expect(toolNames).toContain('web_search');
      expect(toolNames).toContain('web_content_chunk');
    });

    test('should be able to use enhanced search parameters', async () => {
      const webSearchTool = DanteOrchestrator.tools.find((tool: any) => tool.name === 'web_search');

      expect(webSearchTool).toBeDefined();

      if (webSearchTool) {
        const result = await (webSearchTool as any).execute({
          query: 'latest technology trends',
          maxResults: 5,
          fetchContent: true,
          contentDepth: 'full',
          enableChunking: true
        });

        expect(() => JSON.parse(result)).not.toThrow();
      }
    });

    test('should handle both basic and advanced search scenarios', async () => {
      const webSearchTool = DanteOrchestrator.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        // Basic search
        const basicResult = await (webSearchTool as any).execute({
          query: 'simple query'
        });

        // Advanced search with chunking
        const advancedResult = await (webSearchTool as any).execute({
          query: 'complex research topic',
          maxResults: 3,
          fetchContent: true,
          contentDepth: 'detailed',
          enableChunking: true
        });

        expect(() => JSON.parse(basicResult)).not.toThrow();
        expect(() => JSON.parse(advancedResult)).not.toThrow();
      }
    });
  });

  describe('Tool Parameter Validation in Agents', () => {
    test('should validate contentDepth parameter values', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        const validDepths = ['summary', 'detailed', 'full'];

        for (const depth of validDepths) {
          const result = await (webSearchTool as any).execute({
            query: 'test query',
            fetchContent: true,
            contentDepth: depth
          });

          expect(() => JSON.parse(result)).not.toThrow();
        }
      }
    });

    test('should handle boolean parameters correctly', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        const booleanTests = [
          { fetchContent: true, enableChunking: true },
          { fetchContent: true, enableChunking: false },
          { fetchContent: false, enableChunking: false },
          { fetchContent: false, enableChunking: true } // Should be ignored when fetchContent is false
        ];

        for (const testCase of booleanTests) {
          const result = await (webSearchTool as any).execute({
            query: 'boolean test',
            ...testCase
          });

          expect(() => JSON.parse(result)).not.toThrow();
        }
      }
    });

    test('should handle array parameters in chunk tool', async () => {
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (chunkTool) {
        const arrayTests = [
          { chunkIds: ['intro'] },
          { chunkIds: ['intro', 'conclusion'] },
          { chunkIds: ['section-1', 'section-2', 'section-3'] },
          { chunkIds: [] },
          // Test without chunkIds (should return all chunks)
          {}
        ];

        for (const testCase of arrayTests) {
          const result = await (chunkTool as any).execute({
            url: 'https://example.com/test',
            includeChunks: true,
            ...testCase
          });

          expect(() => JSON.parse(result)).not.toThrow();
        }
      }
    });
  });

  describe('Agent Tool Composition', () => {
    test('should be able to chain web search and chunk tools', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (webSearchTool && chunkTool) {
        // First, do a search
        const searchResult = await (webSearchTool as any).execute({
          query: 'AI research',
          maxResults: 1,
          fetchContent: false
        });

        const searchData = JSON.parse(searchResult);

        if (searchData.success && searchData.results.length > 0) {
          // Then, fetch specific chunks from the first result
          const chunkResult = await (chunkTool as any).execute({
            url: searchData.results[0].url,
            chunkIds: ['intro'],
            includeChunks: true
          });

          expect(() => JSON.parse(chunkResult)).not.toThrow();
        }
      }
    });

    test('should handle progressive content fetching workflow', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (webSearchTool && chunkTool) {
        // Step 1: Search with summary-level content
        const summarySearch = await (webSearchTool as any).execute({
          query: 'technology trends 2024',
          maxResults: 3,
          fetchContent: true,
          contentDepth: 'summary',
          enableChunking: false
        });

        const summaryData = JSON.parse(summarySearch);
        expect(summaryData.success).toBeDefined();

        // Step 2: Get detailed chunks from promising result
        if (summaryData.success && summaryData.results.length > 0) {
          const detailedChunks = await (chunkTool as any).execute({
            url: summaryData.results[0].url,
            contentDepth: 'detailed',
            includeChunks: true
          });

          const detailedData = JSON.parse(detailedChunks);
          expect(detailedData.success).toBeDefined();

          // Step 3: Get specific sections if needed
          if (detailedData.success && detailedData.availableChunks) {
            const specificChunks = await (chunkTool as any).execute({
              url: summaryData.results[0].url,
              chunkIds: ['intro', 'conclusion'],
              includeChunks: true
            });

            expect(() => JSON.parse(specificChunks)).not.toThrow();
          }
        }
      }
    });
  });

  describe('Error Handling in Agent Context', () => {
    test('should handle tool execution failures gracefully', async () => {
      // Mock a failing fetch
      const failingFetch = mock(() => Promise.reject(new Error('Network failure')));
      const originalFetch = global.fetch;
      global.fetch = failingFetch as any;

      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        const result = await (webSearchTool as any).execute({
          query: 'test query',
          fetchContent: true,
          enableChunking: true
        });

        const data = JSON.parse(result);
        // Should handle errors without crashing
        expect(data).toHaveProperty('success');
      }

      // Restore fetch
      global.fetch = originalFetch;
    });

    test('should provide meaningful error context', async () => {
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (chunkTool) {
        // Test with completely invalid URL
        const result = await (chunkTool as any).execute({
          url: 'not-a-url-at-all',
          includeChunks: true
        });

        const data = JSON.parse(result);

        if (!data.success) {
          expect(data.error).toBeDefined();
          expect(typeof data.error).toBe('string');
          expect(data.error.length).toBeGreaterThan(0);
        }
      }
    });

    test('should maintain consistent response format during errors', async () => {
      const chunkTool = researchAgent.tools.find((tool: any) => tool.name === 'web_content_chunk');

      if (chunkTool) {
        const result = await (chunkTool as any).execute({
          url: 'https://non-existent-domain-12345.com',
          includeChunks: true
        });

        const data = JSON.parse(result);

        // Should always have these fields
        expect(data).toHaveProperty('success');
        expect(data).toHaveProperty('url');
        expect(data).toHaveProperty('timestamp');

        // Error case should have error field
        if (!data.success) {
          expect(data).toHaveProperty('error');
        }
      }
    });
  });

  describe('Performance in Agent Context', () => {
    test('should complete tool executions within reasonable time', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        const startTime = Date.now();

        await (webSearchTool as any).execute({
          query: 'performance test',
          maxResults: 2,
          fetchContent: true,
          contentDepth: 'detailed',
          enableChunking: true
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Should complete within reasonable time (considering network mocking)
        expect(duration).toBeLessThan(3000); // 3 seconds
      }
    });

    test('should handle concurrent tool executions', async () => {
      const webSearchTool = researchAgent.tools.find((tool: any) => tool.name === 'web_search');

      if (webSearchTool) {
        const promises = [
          (webSearchTool as any).execute({ query: 'test query 1', maxResults: 1 }),
          (webSearchTool as any).execute({ query: 'test query 2', maxResults: 1 }),
          (webSearchTool as any).execute({ query: 'test query 3', maxResults: 1 })
        ];

        const results = await Promise.all(promises);

        expect(results.length).toBe(3);
        results.forEach(result => {
          expect(() => JSON.parse(result)).not.toThrow();
        });
      }
    });
  });
});
