import { describe, test, expect, beforeAll } from 'bun:test';
import { config, validateGeminiConfig } from '../utils/config';
import { getModelById, getDefaultModel } from '../types/models';
import { runDante } from '../index';
import DanteOrchestrator from '../agents/DanteOrchestrator';

describe('Gemini Integration Tests', () => {
  beforeAll(() => {
    // Ensure Gemini API key is configured for tests
    if (!config.gemini.apiKey) {
      throw new Error('Gemini API key not configured. Please set it in your .env file.');
    }
  });

  describe('Configuration Tests', () => {
    test('should have Gemini API key configured', () => {
      expect(config.gemini.apiKey).toBeDefined();
      expect(config.gemini.apiKey).not.toBe('');
    });

    test('should validate Gemini configuration', () => {
      expect(validateGeminiConfig()).toBe(true);
    });

    test('should have correct Gemini endpoint', () => {
      expect(config.gemini.endpoint).toBe('https://generativelanguage.googleapis.com/v1beta');
    });

    test('should have default Gemini model', () => {
      expect(config.gemini.defaultModel).toBe('gemini-2.5-flash');
    });
  });

  describe('Model Detection Tests', () => {
    test('should have Gemini models in available models list', () => {
      const geminiPro = getModelById('gemini-2.5-pro');
      const geminiFlash = getModelById('gemini-2.5-flash');
      const geminiLite = getModelById('gemini-2.5-flash-lite');

      expect(geminiPro).toBeDefined();
      expect(geminiFlash).toBeDefined();
      expect(geminiLite).toBeDefined();

      expect(geminiPro?.contextWindow).toBe('1M context');
      expect(geminiFlash?.contextWindow).toBe('1M context');
      expect(geminiLite?.contextWindow).toBe('1M context');
    });

    test('should use Gemini as default when available', () => {
      const defaultModel = getDefaultModel();
      expect(defaultModel.id).toBe('gemini-2.5-flash');
    });
  });

  describe('Integration Tests', () => {
    test('should run simple task with Gemini', async () => {
      console.log('🎯 Testing simple task with Gemini...');

      const input = 'Hello! Can you tell me what AI model you are and what your capabilities are?';

      try {
        const result = await runDante(input, {
          model: 'gemini-2.5-flash',
          stream: false,
          taskType: 'simple',
          useMemory: false,
          useMCP: false
        });

        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        expect(result.toLowerCase()).toContain('gemini');
        console.log('✅ Simple task test completed');
      } catch (error) {
        console.error('❌ Simple task test failed:', error);
        throw error;
      }
    }, 30000); // 30 second timeout

    test('should handle orchestration task', async () => {
      console.log('🎭 Testing orchestration task with Gemini...');

      const input = 'I need you to analyze a complex development task. Please think through how you would approach building a simple todo application with multiple components, considering architecture, testing, and deployment.';

      try {
        const result = await runDante(input, {
          model: 'gemini-2.5-flash',
          stream: false,
          taskType: 'orchestration',
          useMemory: false,
          useMCP: false
        });

        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        expect(result.toLowerCase()).toContain('todo');
        console.log('✅ Orchestration task test completed');
      } catch (error) {
        console.error('❌ Orchestration task test failed:', error);
        throw error;
      }
    }, 60000); // 60 second timeout

    test('should handle analysis task with large context', async () => {
      console.log('🔍 Testing analysis task with Gemini...');

      // Create a larger context to test Gemini's 1M+ token capability
      const largeContext = Array(100).fill('This is a sample line of code or documentation that represents a large codebase.').join('\n');
      const input = `Please analyze this large codebase:\n\n${largeContext}\n\nWhat patterns do you see and what improvements would you recommend?`;

      try {
        const result = await runDante(input, {
          model: 'gemini-2.5-flash',
          stream: false,
          taskType: 'analysis',
          useMemory: false,
          useMCP: false
        });

        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        expect(result.toLowerCase()).toContain('patterns');
        console.log('✅ Analysis task test completed');
      } catch (error) {
        console.error('❌ Analysis task test failed:', error);
        throw error;
      }
    }, 90000); // 90 second timeout

    test('should fallback to OpenAI if Gemini fails', async () => {
      console.log('🔄 Testing fallback mechanism...');

      const input = 'Test fallback behavior';

      try {
        const result = await runDante(input, {
          model: 'gemini-invalid-model', // This should trigger fallback
          stream: false,
          taskType: 'simple',
          useMemory: false,
          useMCP: false
        });

        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
        console.log('✅ Fallback test completed');
      } catch (error) {
        console.error('❌ Fallback test failed:', error);
        throw error;
      }
    }, 30000);
  });

  describe('Streaming Tests', () => {
    test('should handle streaming with Gemini', async () => {
      console.log('🌊 Testing streaming with Gemini...');

      const input = 'Please explain how AI orchestration works in a conversational way.';

      try {
        const stream = await runDante(input, {
          model: 'gemini-2.5-flash',
          stream: true,
          taskType: 'simple',
          useMemory: false,
          useMCP: false
        });

        expect(stream).toBeDefined();

        // Test that stream is iterable
        let chunks = 0;
        for await (const chunk of stream) {
          chunks++;
          if (chunks > 5) break; // Don't wait for the entire response
        }

        expect(chunks).toBeGreaterThan(0);
        console.log(`✅ Streaming test completed (${chunks} chunks received)`);
      } catch (error) {
        console.error('❌ Streaming test failed:', error);
        throw error;
      }
    }, 30000);
  });
});
