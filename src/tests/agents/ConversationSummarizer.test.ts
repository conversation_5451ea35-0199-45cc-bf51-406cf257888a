
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { ConversationSummarizer } from '../../agents/ConversationSummarizer';
import { GENERIC_AGENT_ERROR } from '../../utils/constants';

describe('ConversationSummarizer', () => {
  const originalEnv = process.env;

  beforeAll(() => {
    process.env = {
      ...originalEnv,
      AI_PROVIDER: 'ollama',
      OLLAMA_BASE_URL: 'http://localhost:11434',
    };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  it(
    'should summarize a conversation into a single sentence',
    async () => {
      const summarizer = new ConversationSummarizer();
      const conversationHistory = [
        'User: I need to find a good recipe for chicken curry.',
        'Agent: Sure, I can help with that. Do you prefer a spicy or mild curry?',
        'User: I like it spicy, with coconut milk.',
        'Agent: Great! I found a recipe for Thai Green Curry with chicken and coconut milk. It uses green chilies for spice.',
        'User: Sounds perfect! What are the ingredients?',
      ];
      const modelName = 'gpt-oss:20b'; // Use a model you have installed

      const summary = await summarizer.summarize(conversationHistory, modelName);

      console.log('Conversation Summary:', summary);

      expect(summary).toBeDefined();
      expect(typeof summary).toBe('string');
      // More specific assertion:
      expect(summary.toLowerCase()).toContain('chicken curry');
      expect(summary.toLowerCase()).not.toContain('error');
    },
    { timeout: 30000 },
  );

  it(
    'should handle an empty conversation history gracefully',
    async () => {
      const summarizer = new ConversationSummarizer();
      const conversationHistory: string[] = [];
      const modelName = 'gemma3:12b';

      const summary = await summarizer.summarize(conversationHistory, modelName);

      expect(summary).toBeDefined();
      expect(typeof summary).toBe('string');
      expect(summary).toBe(''); // Expect an empty string for empty input
    },
    { timeout: 10000 },
  );

  it(
    'should return an error message if the Ollama model is not found',
    async () => {
      const summarizer = new ConversationSummarizer();
      const conversationHistory = [
        'User: Hello',
        'Agent: Hi',
      ];
      const modelName = 'non-existent-model';

      const summary = await summarizer.summarize(conversationHistory, modelName);

      expect(summary).toBeDefined();
      expect(typeof summary).toBe('string');
      expect(summary.toLowerCase()).toContain(GENERIC_AGENT_ERROR.toLowerCase());
    },
    { timeout: 10000 },
  );
});
