#!/usr/bin/env bun

/**
 * Simple test to verify MCP integration is working
 * This tests the core MCP functionality without external dependencies
 */

import { mcpServerManager } from '../mcp/MCPServerManager.js';

console.log('🧪 Testing MCP Integration Core Functionality\n');

async function testMCPCore() {
  console.log('📋 Test 1: Server Registration');

  // Test server registration
  const testConfig = {
    id: 'test-echo-server',
    name: 'Test Echo Server',
    type: 'stdio',
    config: {
      fullCommand: 'echo "Hello from MCP test server"'
    },
    enabled: true,
    priority: 100,
    tags: ['test', 'echo']
  };

  try {
    mcpServerManager.registerServer(testConfig);
    console.log('✅ Server registration: SUCCESS');

    const status = mcpServerManager.getServerStatus('test-echo-server');
    console.log(`   Status: ${status.status}`);
    console.log(`   ID: ${status.id}`);
    console.log(`   Name: ${status.name}\n`);
  } catch (error) {
    console.log('❌ Server registration: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  console.log('📋 Test 2: Server Filtering');

  try {
    const testServers = mcpServerManager.getServersByTags(['test']);
    console.log('✅ Server filtering: SUCCESS');
    console.log(`   Found ${testServers.length} servers with 'test' tag`);
    console.log(`   Server names: ${testServers.map(s => s.config.name).join(', ')}\n`);
  } catch (error) {
    console.log('❌ Server filtering: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  console.log('📋 Test 3: Server Status Management');

  try {
    const allStatuses = mcpServerManager.getAllServerStatuses();
    console.log('✅ Status management: SUCCESS');
    console.log(`   Total servers: ${allStatuses.length}`);

    allStatuses.forEach(status => {
      console.log(`   - ${status.name}: ${status.status}`);
    });
    console.log('');
  } catch (error) {
    console.log('❌ Status management: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  console.log('📋 Test 4: Server Cleanup');

  try {
    await mcpServerManager.cleanup();
    console.log('✅ Cleanup: SUCCESS');
    console.log('   All servers and resources cleaned up\n');
  } catch (error) {
    console.log('❌ Cleanup: FAILED');
    console.log(`   Error: ${error.message}\n`);
  }

  console.log('🎯 MCP Core Integration Test Complete!');
  console.log('');
  console.log('Next steps to test with real MCP servers:');
  console.log('1. Ensure @modelcontextprotocol/server-filesystem is installed globally');
  console.log('2. Start Dante: bun run dev:api');
  console.log('3. Check status: curl http://localhost:3001/api/mcp/status');
  console.log('4. The "connecting" status is normal - it means the integration is working!');
}

testMCPCore().catch(console.error);
