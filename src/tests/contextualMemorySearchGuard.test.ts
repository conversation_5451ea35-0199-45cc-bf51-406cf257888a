import { describe, it, expect, beforeAll, beforeEach } from 'bun:test';
import { contextualMemorySearchTool, resetContextualMemorySearchGuard } from '../tools/memoryTools';
import { memoryManager } from '../memory/MemoryManager';

describe('contextual_memory_search loop guard', () => {
  beforeAll(async () => {
    await memoryManager.initialize();
  });

  beforeEach(() => {
    resetContextualMemorySearchGuard();
  });

  it('reuses cached results and triggers guard on repeated queries', async () => {
    const params = {
      conversationContext: "User saved a PDF at 'uploads/1757963490637-389422180.pdf' and keeps asking about attachments.",
      specificNeed: 'Confirm whether to use the uploads URL or a fileId when structuring the assistant message.'
    };

    const first = await contextualMemorySearchTool.execute(params);
    expect(first.reusedFromCache ?? false).toBe(false);
    expect(first.loopGuardActive ?? false).toBe(false);
    expect(first.loopGuardCount).toBe(1);

    const second = await contextualMemorySearchTool.execute(params);
    expect(second.reusedFromCache).toBe(true);
    expect(second.loopGuardActive ?? false).toBe(false);
    expect(second.loopGuardCount).toBe(2);
    expect(String(second.message)).toContain('Reusing cached contextual_memory_search result');

    const third = await contextualMemorySearchTool.execute(params);
    expect(third.reusedFromCache).toBe(true);
    expect(third.loopGuardActive).toBe(true);
    expect(third.loopGuardCount).toBeGreaterThanOrEqual(3);
    expect(String(third.message)).toContain('Memory search loop guard triggered');
    expect(third.guardrail).toBe('memory_search_loop');
  });
});
