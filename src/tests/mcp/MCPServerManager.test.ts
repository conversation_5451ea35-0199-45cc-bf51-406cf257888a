import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { MCPServerManager } from '../../mcp/MCPServerManager';
import type { MCPServerConfig } from '../../mcp/MCPServerManager';

describe('MCPServerManager', () => {
  let manager: MCPServerManager;

  beforeEach(() => {
    manager = new MCPServerManager();
  });

  afterEach(async () => {
    await manager.cleanup();
  });

  describe('Server Registration', () => {
    it('should register a new MCP server', () => {
      const config: MCPServerConfig = {
        id: 'test-server',
        name: 'Test Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      expect(() => manager.registerServer(config)).not.toThrow();
      
      const status = manager.getServerStatus('test-server');
      expect(status).toBeDefined();
      expect(status?.id).toBe('test-server');
      expect(status?.name).toBe('Test Server');
      expect(status?.status).toBe('disconnected');
    });

    it('should not allow duplicate server IDs', () => {
      const config: MCPServerConfig = {
        id: 'duplicate-server',
        name: 'Duplicate Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      manager.registerServer(config);
      expect(() => manager.registerServer(config)).toThrow('already registered');
    });

    it('should unregister a server', async () => {
      const config: MCPServerConfig = {
        id: 'temp-server',
        name: 'Temporary Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      manager.registerServer(config);
      expect(manager.getServerStatus('temp-server')).toBeDefined();

      await manager.unregisterServer('temp-server');
      expect(manager.getServerStatus('temp-server')).toBeUndefined();
    });
  });

  describe('Server Status Management', () => {
    beforeEach(() => {
      const config: MCPServerConfig = {
        id: 'status-test-server',
        name: 'Status Test Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };
      manager.registerServer(config);
    });

    it('should get all server statuses', () => {
      const statuses = manager.getAllServerStatuses();
      expect(statuses).toHaveLength(1);
      expect(statuses[0].id).toBe('status-test-server');
    });

    it('should enable and disable servers', () => {
      manager.setServerEnabled('status-test-server', false);
      const status = manager.getServerStatus('status-test-server');
      expect(status?.status).toBe('disconnected');

      manager.setServerEnabled('status-test-server', true);
      // Server should still be disconnected but enabled
      const statusAfter = manager.getServerStatus('status-test-server');
      expect(statusAfter?.status).toBe('disconnected');
    });
  });

  describe('Server Filtering', () => {
    beforeEach(() => {
      const configs: MCPServerConfig[] = [
        {
          id: 'fs-server',
          name: 'Filesystem Server',
          type: 'stdio',
          config: { fullCommand: 'echo fs' },
          enabled: true,
          priority: 100,
          tags: ['filesystem', 'local']
        },
        {
          id: 'git-server',
          name: 'Git Server',
          type: 'stdio',
          config: { fullCommand: 'echo git' },
          enabled: true,
          priority: 90,
          tags: ['git', 'version-control']
        },
        {
          id: 'web-server',
          name: 'Web Server',
          type: 'streamable_http',
          config: { url: 'http://example.com' },
          enabled: true,
          priority: 80,
          tags: ['web', 'search']
        }
      ];

      configs.forEach(config => manager.registerServer(config));
    });

    it('should filter servers by tags', () => {
      const filesystemServers = manager.getServersByTags(['filesystem']);
      expect(filesystemServers).toHaveLength(1);
      expect(filesystemServers[0].id).toBe('fs-server');

      const localServers = manager.getServersByTags(['local']);
      expect(localServers).toHaveLength(1);
      expect(localServers[0].id).toBe('fs-server');

      const versionControlServers = manager.getServersByTags(['version-control']);
      expect(versionControlServers).toHaveLength(1);
      expect(versionControlServers[0].id).toBe('git-server');
    });

    it('should sort servers by priority', () => {
      const allServers = manager.getServersByTags(['filesystem', 'git', 'web']);
      expect(allServers).toHaveLength(3);
      
      // Should be sorted by priority (higher first)
      expect(allServers[0].config.priority).toBe(100);
      expect(allServers[1].config.priority).toBe(90);
      expect(allServers[2].config.priority).toBe(80);
    });
  });

  describe('Configuration Validation', () => {
    it('should handle stdio server configuration', () => {
      const config: MCPServerConfig = {
        id: 'stdio-test',
        name: 'Stdio Test',
        type: 'stdio',
        config: {
          fullCommand: 'npx -y @modelcontextprotocol/server-filesystem /tmp'
        },
        enabled: true,
        priority: 100,
        tags: ['filesystem']
      };

      expect(() => manager.registerServer(config)).not.toThrow();
    });

    it('should handle streamable HTTP server configuration', () => {
      const config: MCPServerConfig = {
        id: 'http-test',
        name: 'HTTP Test',
        type: 'streamable_http',
        config: {
          url: 'https://api.example.com/mcp',
          authProvider: {
            type: 'bearer',
            token: 'test-token'
          }
        },
        enabled: true,
        priority: 100,
        tags: ['web']
      };

      expect(() => manager.registerServer(config)).not.toThrow();
    });
  });

  describe('Health Monitoring', () => {
    beforeEach(() => {
      const config: MCPServerConfig = {
        id: 'health-test-server',
        name: 'Health Test Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };
      manager.registerServer(config);
    });

    it('should perform health check on all servers', async () => {
      const healthResults = await manager.healthCheck();
      expect(healthResults).toBeDefined();
      expect(typeof healthResults['health-test-server']).toBe('boolean');
    });
  });

  describe('Event Handling', () => {
    it('should emit events for server registration', (done) => {
      manager.on('serverRegistered', (config) => {
        expect(config.id).toBe('event-test-server');
        done();
      });

      const config: MCPServerConfig = {
        id: 'event-test-server',
        name: 'Event Test Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      manager.registerServer(config);
    });

    it('should emit events for server status changes', (done) => {
      let eventCount = 0;
      
      manager.on('serverStatusChanged', (status) => {
        eventCount++;
        if (eventCount === 1) {
          // First event should be registration
          expect(status.status).toBe('disconnected');
          done();
        }
      });

      const config: MCPServerConfig = {
        id: 'status-event-server',
        name: 'Status Event Server',
        type: 'stdio',
        config: {
          fullCommand: 'echo test'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      manager.registerServer(config);
    });
  });
});