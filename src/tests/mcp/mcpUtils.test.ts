import { describe, it, expect } from 'bun:test';
import {
  validateMCPServerConfig,
  createFilesystemMCPConfig,
  createGitMCPConfig,
  createDatabaseMCPConfig,
  createWebSearchMCPConfig,
  sanitizeToolInput,
  sanitizeToolOutput,
  isToolCallSafe,
  createDefaultMCPToolFactoryOptions,
  getMCPServerRecommendations
} from '../../mcp/utils';
import type { MCPServerConfig } from '../../mcp/MCPServerManager';

describe('MCP Utils', () => {
  describe('validateMCPServerConfig', () => {
    it('should validate a correct stdio server config', () => {
      const config: MCPServerConfig = {
        id: 'test-server',
        name: 'Test Server',
        type: 'stdio',
        config: {
          fullCommand: 'npx test-server'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a correct streamable_http server config', () => {
      const config: MCPServerConfig = {
        id: 'http-server',
        name: 'HTTP Server',
        type: 'streamable_http',
        config: {
          url: 'https://api.example.com/mcp'
        },
        enabled: true,
        priority: 90,
        tags: ['web']
      };

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject config with missing required fields', () => {
      const config = {
        id: '',
        name: '',
        type: 'invalid_type',
        config: {},
        enabled: 'not_boolean',
        priority: -1,
        tags: 'not_array'
      } as any;

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('Server ID is required and must be a string');
      expect(result.errors).toContain('Server name is required and must be a string');
      expect(result.errors).toContain('Server type must be one of: stdio, streamable_http, hosted');
    });

    it('should reject stdio config without fullCommand', () => {
      const config: MCPServerConfig = {
        id: 'test-server',
        name: 'Test Server',
        type: 'stdio',
        config: {},
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('fullCommand is required for stdio servers');
    });

    it('should reject streamable_http config without url', () => {
      const config: MCPServerConfig = {
        id: 'http-server',
        name: 'HTTP Server',
        type: 'streamable_http',
        config: {},
        enabled: true,
        priority: 90,
        tags: ['web']
      };

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('url is required for streamable HTTP servers');
    });

    it('should reject invalid URL format', () => {
      const config: MCPServerConfig = {
        id: 'http-server',
        name: 'HTTP Server',
        type: 'streamable_http',
        config: {
          url: 'not-a-valid-url'
        },
        enabled: true,
        priority: 90,
        tags: ['web']
      };

      const result = validateMCPServerConfig(config);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('url must be a valid URL for streamable HTTP servers');
    });
  });

  describe('MCP Server Config Creators', () => {
    it('should create filesystem MCP config', () => {
      const config = createFilesystemMCPConfig(
        'fs-test',
        'Filesystem Test',
        '/tmp',
        { allowWrite: true, priority: 95 }
      );

      expect(config.id).toBe('fs-test');
      expect(config.name).toBe('Filesystem Test');
      expect(config.type).toBe('stdio');
      expect(config.config.fullCommand).toContain('/tmp');
      expect(config.priority).toBe(95);
      expect(config.tags).toContain('filesystem');
    });

    it('should create git MCP config', () => {
      const config = createGitMCPConfig(
        'git-test',
        'Git Test',
        '/repo',
        { remote: 'origin', priority: 85 }
      );

      expect(config.id).toBe('git-test');
      expect(config.name).toBe('Git Test');
      expect(config.type).toBe('stdio');
      expect(config.config.fullCommand).toContain('/repo');
      expect(config.config.fullCommand).toContain('--remote origin');
      expect(config.priority).toBe(85);
      expect(config.tags).toContain('git');
    });

    it('should create database MCP config', () => {
      const config = createDatabaseMCPConfig(
        'db-test',
        'Database Test',
        'postgresql://localhost:5432/test',
        'http://localhost:3000/mcp',
        { priority: 75 }
      );

      expect(config.id).toBe('db-test');
      expect(config.name).toBe('Database Test');
      expect(config.type).toBe('streamable_http');
      expect(config.config.url).toBe('http://localhost:3000/mcp');
      expect(config.priority).toBe(75);
      expect(config.tags).toContain('database');
    });

    it('should create web search MCP config', () => {
      const config = createWebSearchMCPConfig(
        'search-test',
        'Search Test',
        'https://api.search.com/mcp',
        { apiKey: 'test-key', priority: 65 }
      );

      expect(config.id).toBe('search-test');
      expect(config.name).toBe('Search Test');
      expect(config.type).toBe('streamable_http');
      expect(config.config.url).toBe('https://api.search.com/mcp');
      expect(config.config.authProvider?.token).toBe('test-key');
      expect(config.priority).toBe(65);
      expect(config.tags).toContain('web');
    });
  });

  describe('Input/Output Sanitization', () => {
    it('should sanitize string input', () => {
      const input = '  test string with lots of spaces  ';
      const sanitized = sanitizeToolInput(input);
      expect(sanitized).toBe('test string with lots of spaces');
    });

    it('should sanitize long string input', () => {
      const longString = 'a'.repeat(20000);
      const sanitized = sanitizeToolInput(longString);
      expect(sanitized.length).toBeLessThanOrEqual(10000);
    });

    it('should sanitize object input', () => {
      const input = {
        key1: 'value1',
        key2: 'value2',
        nested: {
          key3: 'value3'
        }
      };
      
      const sanitized = sanitizeToolInput(input);
      expect(typeof sanitized).toBe('object');
      expect(sanitized.key1).toBe('value1');
      expect(sanitized.nested.key3).toBe('value3');
    });

    it('should sanitize array input', () => {
      const input = ['item1', 'item2', 'item3'];
      const sanitized = sanitizeToolInput(input);
      expect(Array.isArray(sanitized)).toBe(true);
      expect(sanitized).toHaveLength(3);
    });

    it('should limit object size', () => {
      const largeObject: any = {};
      for (let i = 0; i < 100; i++) {
        largeObject[`key${i}`] = `value${i}`;
      }
      
      const sanitized = sanitizeToolInput(largeObject);
      expect(Object.keys(sanitized).length).toBeLessThanOrEqual(50);
    });

    it('should sanitize output strings', () => {
      const output = 'test output';
      const sanitized = sanitizeToolOutput(output);
      expect(sanitized).toBe('test output');
    });

    it('should sanitize long output', () => {
      const longOutput = 'a'.repeat(100000);
      const sanitized = sanitizeToolOutput(longOutput);
      expect(sanitized.length).toBeLessThanOrEqual(50000);
    });

    it('should sanitize object output to JSON', () => {
      const output = { key: 'value', number: 42 };
      const sanitized = sanitizeToolOutput(output);
      expect(sanitized).toContain('"key": "value"');
      expect(sanitized).toContain('"number": 42');
    });
  });

  describe('Tool Safety Validation', () => {
    it('should allow safe tool calls', () => {
      const result = isToolCallSafe('read_file', { path: '/safe/path.txt' });
      expect(result.safe).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should block dangerous tool names', () => {
      const result = isToolCallSafe('system_shutdown', {});
      expect(result.safe).toBe(false);
      expect(result.reason).toContain('potentially dangerous');
    });

    it('should block dangerous command patterns', () => {
      const result = isToolCallSafe('execute_command', { 
        command: 'rm -rf /' 
      });
      expect(result.safe).toBe(false);
      expect(result.reason).toContain('dangerous pattern');
    });

    it('should block sudo commands', () => {
      const result = isToolCallSafe('run_shell', { 
        command: 'sudo rm file.txt' 
      });
      expect(result.safe).toBe(false);
      expect(result.reason).toContain('dangerous pattern');
    });

    it('should allow legitimate file operations', () => {
      const result = isToolCallSafe('file_edit', { 
        operation: 'write',
        path: './output.txt',
        content: 'Hello, world!'
      });
      expect(result.safe).toBe(true);
    });
  });

  describe('Default Factory Options', () => {
    it('should create default MCP tool factory options', () => {
      const options = createDefaultMCPToolFactoryOptions();
      
      expect(options.enableCaching).toBe(true);
      expect(options.cacheExpiry).toBeGreaterThan(0);
      expect(options.maxCacheSize).toBeGreaterThan(0);
      expect(Array.isArray(options.toolFilters.blockedTools)).toBe(true);
      expect(options.toolFilters.blockedTools?.length).toBeGreaterThan(0);
    });
  });

  describe('Server Recommendations', () => {
    it('should recommend servers for file operations', () => {
      const recommendations = getMCPServerRecommendations('file-operations');
      
      expect(recommendations.recommended).toContain('filesystem');
      expect(recommendations.optional).toContain('git');
      expect(recommendations.description).toContain('File system operations');
    });

    it('should recommend servers for code analysis', () => {
      const recommendations = getMCPServerRecommendations('code-analysis');
      
      expect(recommendations.recommended).toContain('filesystem');
      expect(recommendations.recommended).toContain('git');
      expect(recommendations.description).toContain('Code analysis');
    });

    it('should recommend servers for web research', () => {
      const recommendations = getMCPServerRecommendations('web-research');
      
      expect(recommendations.recommended).toContain('web-search');
      expect(recommendations.description).toContain('Web research');
    });

    it('should recommend servers for data analysis', () => {
      const recommendations = getMCPServerRecommendations('data-analysis');
      
      expect(recommendations.recommended).toContain('database');
      expect(recommendations.recommended).toContain('filesystem');
      expect(recommendations.description).toContain('Data analysis');
    });

    it('should recommend servers for development', () => {
      const recommendations = getMCPServerRecommendations('development');
      
      expect(recommendations.recommended).toContain('filesystem');
      expect(recommendations.recommended).toContain('git');
      expect(recommendations.description).toContain('Development tasks');
    });

    it('should provide default recommendations for unknown task types', () => {
      const recommendations = getMCPServerRecommendations('unknown-task');
      
      expect(recommendations.recommended).toContain('filesystem');
      expect(recommendations.optional).toContain('git');
      expect(recommendations.description).toContain('General tasks');
    });
  });
});
