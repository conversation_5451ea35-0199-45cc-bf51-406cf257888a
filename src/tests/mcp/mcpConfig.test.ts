import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import {
  loadMCPConfiguration,
  getMCPConfigForEnvironment,
  createMCPConfigForUseCase,
  mergeMCPConfigurations
} from '../../config/mcpConfig';

describe('MCP Configuration', () => {
  const originalEnv = { ...process.env };

  beforeEach(() => {
    // Reset environment to clean state
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    // Restore original environment
    process.env = { ...originalEnv };
  });

  describe('loadMCPConfiguration', () => {
    it('should load default configuration when no env vars are set', () => {
      // Clear MCP-related env vars
      delete process.env.MCP_ENABLED;
      delete process.env.MCP_AUTO_CONNECT;
      delete process.env.MCP_FILESYSTEM_ENABLED;
      delete process.env.MCP_GIT_ENABLED;

      const config = loadMCPConfiguration();
      
      expect(config.enabled).toBe(true);
      expect(config.autoConnect).toBe(true);
      expect(config.healthCheckInterval).toBe(60000);
      expect(config.maxConcurrentConnections).toBe(10);
      expect(config.connectionTimeout).toBe(30000);
      expect(Array.isArray(config.servers)).toBe(true);
    });

    it('should respect MCP_ENABLED environment variable', () => {
      process.env.MCP_ENABLED = 'false';
      
      const config = loadMCPConfiguration();
      expect(config.enabled).toBe(false);
    });

    it('should respect MCP_AUTO_CONNECT environment variable', () => {
      process.env.MCP_AUTO_CONNECT = 'false';
      
      const config = loadMCPConfiguration();
      expect(config.autoConnect).toBe(false);
    });

    it('should parse numeric environment variables', () => {
      process.env.MCP_HEALTH_CHECK_INTERVAL = '30000';
      process.env.MCP_MAX_CONNECTIONS = '5';
      process.env.MCP_CONNECTION_TIMEOUT = '15000';
      
      const config = loadMCPConfiguration();
      expect(config.healthCheckInterval).toBe(30000);
      expect(config.maxConcurrentConnections).toBe(5);
      expect(config.connectionTimeout).toBe(15000);
    });

    it('should include filesystem server when enabled', () => {
      process.env.MCP_FILESYSTEM_ENABLED = 'true';
      
      const config = loadMCPConfiguration();
      const fsServer = config.servers.find(s => s.id === 'filesystem-local');
      expect(fsServer).toBeDefined();
      expect(fsServer?.type).toBe('stdio');
      expect(fsServer?.tags).toContain('filesystem');
    });

    it('should include git server when enabled', () => {
      process.env.MCP_GIT_ENABLED = 'true';
      
      const config = loadMCPConfiguration();
      const gitServer = config.servers.find(s => s.id === 'git-local');
      expect(gitServer).toBeDefined();
      expect(gitServer?.type).toBe('stdio');
      expect(gitServer?.tags).toContain('git');
    });

    it('should include web search server when configured', () => {
      process.env.MCP_WEB_SEARCH_URL = 'https://api.search.com/mcp';
      process.env.MCP_WEB_SEARCH_API_KEY = 'test-key';
      
      const config = loadMCPConfiguration();
      const searchServer = config.servers.find(s => s.id === 'web-search');
      expect(searchServer).toBeDefined();
      expect(searchServer?.type).toBe('streamable_http');
      expect(searchServer?.config.url).toBe('https://api.search.com/mcp');
    });

    it('should include database server when configured', () => {
      process.env.MCP_DATABASE_URL = 'postgresql://localhost:5432/test';
      process.env.MCP_DATABASE_SERVER_URL = 'http://localhost:3000/mcp';
      
      const config = loadMCPConfiguration();
      const dbServer = config.servers.find(s => s.id === 'database-primary');
      expect(dbServer).toBeDefined();
      expect(dbServer?.type).toBe('streamable_http');
      expect(dbServer?.tags).toContain('database');
    });
  });

  describe('Custom Server Loading', () => {
    it('should load custom servers from JSON environment variable', () => {
      const customServers = [
        {
          id: 'custom-server-1',
          name: 'Custom Server 1',
          type: 'stdio',
          config: {
            fullCommand: 'echo test1'
          },
          enabled: true,
          priority: 50,
          tags: ['custom']
        }
      ];
      
      process.env.MCP_CUSTOM_SERVERS = JSON.stringify(customServers);
      
      const config = loadMCPConfiguration();
      const customServer = config.servers.find(s => s.id === 'custom-server-1');
      expect(customServer).toBeDefined();
      expect(customServer?.name).toBe('Custom Server 1');
    });

    it('should load numbered custom servers from environment variables', () => {
      process.env.MCP_SERVER_1_ID = 'numbered-server';
      process.env.MCP_SERVER_1_NAME = 'Numbered Server';
      process.env.MCP_SERVER_1_TYPE = 'stdio';
      process.env.MCP_SERVER_1_COMMAND = 'echo numbered';
      process.env.MCP_SERVER_1_PRIORITY = '60';
      process.env.MCP_SERVER_1_TAGS = 'test,numbered';
      
      const config = loadMCPConfiguration();
      const numberedServer = config.servers.find(s => s.id === 'numbered-server');
      expect(numberedServer).toBeDefined();
      expect(numberedServer?.name).toBe('Numbered Server');
      expect(numberedServer?.priority).toBe(60);
      expect(numberedServer?.tags).toContain('test');
      expect(numberedServer?.tags).toContain('numbered');
    });

    it('should handle HTTP server environment variables', () => {
      process.env.MCP_SERVER_1_ID = 'http-server';
      process.env.MCP_SERVER_1_NAME = 'HTTP Server';
      process.env.MCP_SERVER_1_TYPE = 'streamable_http';
      process.env.MCP_SERVER_1_URL = 'https://api.example.com/mcp';
      process.env.MCP_SERVER_1_API_KEY = 'secret-key';
      
      const config = loadMCPConfiguration();
      const httpServer = config.servers.find(s => s.id === 'http-server');
      expect(httpServer).toBeDefined();
      expect(httpServer?.type).toBe('streamable_http');
      expect(httpServer?.config.url).toBe('https://api.example.com/mcp');
      expect(httpServer?.config.authProvider?.token).toBe('secret-key');
    });

    it('should handle invalid JSON in MCP_CUSTOM_SERVERS gracefully', () => {
      process.env.MCP_CUSTOM_SERVERS = 'invalid json';
      
      expect(() => loadMCPConfiguration()).not.toThrow();
      const config = loadMCPConfiguration();
      expect(Array.isArray(config.servers)).toBe(true);
    });
  });

  describe('Environment-Specific Configurations', () => {
    it('should provide development configuration', () => {
      const devConfig = getMCPConfigForEnvironment('development');
      
      expect(devConfig.enabled).toBe(true);
      expect(devConfig.autoConnect).toBe(true);
      expect(devConfig.healthCheckInterval).toBe(30000);
      expect(devConfig.maxConcurrentConnections).toBe(5);
      expect(devConfig.connectionTimeout).toBe(10000);
      expect(devConfig.toolFactory?.enableCaching).toBe(true);
      expect(devConfig.toolFactory?.cacheExpiry).toBe(2 * 60 * 1000);
    });

    it('should provide production configuration', () => {
      const prodConfig = getMCPConfigForEnvironment('production');
      
      expect(prodConfig.enabled).toBe(true);
      expect(prodConfig.autoConnect).toBe(true);
      expect(prodConfig.healthCheckInterval).toBe(60000);
      expect(prodConfig.maxConcurrentConnections).toBe(10);
      expect(prodConfig.connectionTimeout).toBe(30000);
      expect(prodConfig.toolFactory?.enableCaching).toBe(true);
      expect(prodConfig.toolFactory?.cacheExpiry).toBe(10 * 60 * 1000);
      expect(prodConfig.toolFactory?.toolFilters?.blockedTools).toContain('admin_access');
    });

    it('should provide test configuration', () => {
      const testConfig = getMCPConfigForEnvironment('test');
      
      expect(testConfig.enabled).toBe(false);
      expect(testConfig.autoConnect).toBe(false);
      expect(testConfig.healthCheckInterval).toBe(5000);
      expect(testConfig.maxConcurrentConnections).toBe(2);
      expect(testConfig.connectionTimeout).toBe(5000);
      expect(testConfig.toolFactory?.enableCaching).toBe(false);
    });
  });

  describe('Use Case Configurations', () => {
    it('should provide code analysis configuration', () => {
      const config = createMCPConfigForUseCase('code-analysis');
      
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('filesystem');
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('git');
    });

    it('should provide web research configuration', () => {
      const config = createMCPConfigForUseCase('web-research');
      
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('web');
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('search');
    });

    it('should provide data processing configuration', () => {
      const config = createMCPConfigForUseCase('data-processing');
      
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('database');
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('filesystem');
    });

    it('should provide development configuration', () => {
      const config = createMCPConfigForUseCase('development');
      
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('filesystem');
      expect(config.toolFactory?.toolFilters?.requiredTags).toContain('git');
    });

    it('should handle unknown use case', () => {
      const config = createMCPConfigForUseCase('unknown-use-case');
      expect(config).toEqual({});
    });
  });

  describe('Configuration Merging', () => {
    it('should merge multiple configurations correctly', () => {
      const config1 = {
        enabled: false,
        servers: [
          {
            id: 'server1',
            name: 'Server 1',
            type: 'stdio' as const,
            config: { fullCommand: 'echo 1' },
            enabled: true,
            priority: 100,
            tags: ['test1']
          }
        ],
        toolFactory: {
          enableCaching: false,
          cacheExpiry: 1000,
          maxCacheSize: 5,
          toolFilters: {
            allowedTools: ['tool1']
          }
        }
      };

      const config2 = {
        enabled: true,
        servers: [
          {
            id: 'server2',
            name: 'Server 2',
            type: 'stdio' as const,
            config: { fullCommand: 'echo 2' },
            enabled: true,
            priority: 90,
            tags: ['test2']
          }
        ],
        toolFactory: {
          enableCaching: true,
          cacheExpiry: 5 * 60 * 1000,
          maxCacheSize: 1000,
          toolFilters: {
            blockedTools: ['dangerous_tool']
          }
        }
      };

      const merged = mergeMCPConfigurations(config1, config2);
      
      expect(merged.enabled).toBe(true); // Last value wins
      expect(merged.servers).toHaveLength(4); // Default + both configs
      expect(merged.toolFactory.enableCaching).toBe(true);
      expect(merged.toolFactory.toolFilters.allowedTools).toContain('tool1');
      expect(merged.toolFactory.toolFilters.blockedTools).toContain('dangerous_tool');
    });

    it('should handle empty configuration merging', () => {
      const base = loadMCPConfiguration();
      const merged = mergeMCPConfigurations({}, {});
      
      expect(merged.enabled).toBe(base.enabled);
      expect(merged.servers.length).toBe(base.servers.length);
    });
  });
});