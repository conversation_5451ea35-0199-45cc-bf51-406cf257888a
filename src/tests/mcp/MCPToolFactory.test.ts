import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { MCPToolFactory } from '../../mcp/MCPToolFactory';
import { MCPServerManager } from '../../mcp/MCPServerManager';
import type { MCPServerConfig } from '../../mcp/MCPServerManager';

describe('MCPToolFactory', () => {
  let toolFactory: MCPToolFactory;
  let serverManager: MCPServerManager;

  beforeEach(() => {
    toolFactory = new MCPToolFactory({
      enableCaching: true,
      cacheExpiry: 1000, // 1 second for testing
      maxCacheSize: 10,
      toolFilters: {}
    });
    serverManager = new MCPServerManager();
  });

  afterEach(async () => {
    await serverManager.cleanup();
    toolFactory.invalidateCache();
  });

  describe('Tool Factory Configuration', () => {
    it('should initialize with default options', () => {
      const factory = new MCPToolFactory();
      const stats = factory.getCacheStats();
      expect(stats.totalEntries).toBe(0);
      expect(stats.totalTools).toBe(0);
    });

    it('should initialize with custom options', () => {
      const customFactory = new MCPToolFactory({
        enableCaching: false,
        maxCacheSize: 5,
        toolFilters: {
          allowedTools: ['safe_tool'],
          blockedTools: ['dangerous_tool']
        }
      });

      expect(customFactory).toBeDefined();
    });
  });

  describe('Tool Filtering', () => {
    beforeEach(() => {
      const configs: MCPServerConfig[] = [
        {
          id: 'test-server-1',
          name: 'Test Server 1',
          type: 'stdio',
          config: { fullCommand: 'echo test1' },
          enabled: true,
          priority: 100,
          tags: ['filesystem', 'safe']
        },
        {
          id: 'test-server-2',
          name: 'Test Server 2',
          type: 'stdio',
          config: { fullCommand: 'echo test2' },
          enabled: true,
          priority: 90,
          tags: ['dangerous', 'admin']
        }
      ];

      configs.forEach(config => serverManager.registerServer(config));
    });

    it('should filter servers by allowed list', () => {
      toolFactory.updateFilters({
        allowedServers: ['test-server-1']
      });

      // Test would need actual server connections to fully validate
      // This tests the filter configuration
      expect(toolFactory).toBeDefined();
    });

    it('should filter servers by blocked list', () => {
      toolFactory.updateFilters({
        blockedServers: ['test-server-2']
      });

      expect(toolFactory).toBeDefined();
    });

    it('should filter tools by required tags', () => {
      toolFactory.updateFilters({
        requiredTags: ['safe']
      });

      expect(toolFactory).toBeDefined();
    });
  });

  describe('Cache Management', () => {
    it('should provide cache statistics', () => {
      const stats = toolFactory.getCacheStats();
      expect(stats).toHaveProperty('totalEntries');
      expect(stats).toHaveProperty('totalTools');
      expect(stats).toHaveProperty('cacheHitRate');
      expect(typeof stats.totalEntries).toBe('number');
      expect(typeof stats.totalTools).toBe('number');
    });

    it('should invalidate entire cache', () => {
      toolFactory.invalidateCache();
      const stats = toolFactory.getCacheStats();
      expect(stats.totalEntries).toBe(0);
      expect(stats.totalTools).toBe(0);
    });

    it('should invalidate cache for specific server', () => {
      toolFactory.invalidateCache('test-server');
      const stats = toolFactory.getCacheStats();
      expect(stats.totalEntries).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Tool Discovery', () => {
    it('should handle empty server list', async () => {
      const result = await toolFactory.getAllTools();
      expect(result.tools).toHaveLength(0);
      expect(result.metadata).toHaveLength(0);
    });

    it('should handle tool discovery with no connected servers', async () => {
      // Register but don't connect servers
      const config: MCPServerConfig = {
        id: 'unconnected-server',
        name: 'Unconnected Server',
        type: 'stdio',
        config: { fullCommand: 'echo test' },
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      serverManager.registerServer(config);

      const result = await toolFactory.getAllTools();
      expect(result.tools).toHaveLength(0);
      expect(result.metadata).toHaveLength(0);
    });
  });

  describe('Tool Search', () => {
    it('should search for tools by name when no servers are connected', async () => {
      const result = await toolFactory.getToolByName('nonexistent_tool');
      expect(result).toBeNull();
    });

    it('should handle tag-based tool filtering with no servers', async () => {
      const result = await toolFactory.getToolsByTags(['filesystem']);
      expect(result.tools).toHaveLength(0);
      expect(result.metadata).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle tool factory errors gracefully', async () => {
      // Test with invalid server configuration
      const config: MCPServerConfig = {
        id: 'invalid-server',
        name: 'Invalid Server',
        type: 'stdio',
        config: { fullCommand: '' }, // Invalid empty command
        enabled: true,
        priority: 100,
        tags: ['test']
      };

      serverManager.registerServer(config);

      // Should not throw, but return empty results
      const result = await toolFactory.getAllTools();
      expect(result.tools).toHaveLength(0);
    });
  });

  describe('Tool Metadata', () => {
    it('should track tool metadata correctly', async () => {
      const result = await toolFactory.getAllTools();
      
      for (const metadata of result.metadata) {
        expect(metadata).toHaveProperty('serverId');
        expect(metadata).toHaveProperty('serverName');
        expect(metadata).toHaveProperty('toolName');
        expect(metadata).toHaveProperty('description');
        expect(metadata).toHaveProperty('tags');
        expect(metadata).toHaveProperty('cached');
        expect(metadata).toHaveProperty('lastUpdated');
        expect(metadata.lastUpdated).toBeInstanceOf(Date);
      }
    });
  });

  describe('Performance', () => {
    it('should handle multiple concurrent tool requests', async () => {
      const promises = [
        toolFactory.getAllTools(),
        toolFactory.getToolsByTags(['test']),
        toolFactory.getToolByName('test_tool')
      ];

      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      
      // All should return valid results (even if empty)
      results.forEach(result => {
        if (result && 'tools' in result) {
          expect(Array.isArray(result.tools)).toBe(true);
          expect(Array.isArray(result.metadata)).toBe(true);
        }
      });
    });

    it('should handle cache expiry correctly', async () => {
      // This test would require waiting for cache expiry
      // For now, just test that cache expiry configuration works
      const factoryWithShortExpiry = new MCPToolFactory({
        enableCaching: true,
        cacheExpiry: 1, // 1ms
        maxCacheSize: 10
      });

      expect(factoryWithShortExpiry).toBeDefined();
    });
  });
});