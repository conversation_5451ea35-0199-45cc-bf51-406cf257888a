import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { 
  initializeMCPServers, 
  createDanteWithMCP, 
  getMCPStatus, 
  cleanupMCP 
} from '../../mcp';
import { mcpServerManager } from '../../mcp/MCPServerManager';
import { MCPServerTemplates, MCPServerCollections } from '../../tools/mcpTools';

describe('MCP Integration', () => {
  beforeEach(async () => {
    // Clean up any existing state
    await cleanupMCP();
  });

  afterEach(async () => {
    await cleanupMCP();
  });

  describe('MCP Initialization', () => {
    it('should initialize MCP servers without errors', async () => {
      // Mock environment to enable MCP
      const originalEnv = process.env.MCP_ENABLED;
      process.env.MCP_ENABLED = 'true';
      
      try {
        await expect(initializeMCPServers()).resolves.not.toThrow();
      } finally {
        process.env.MCP_ENABLED = originalEnv;
      }
    });

    it('should skip initialization when MCP is disabled', async () => {
      const originalEnv = process.env.MCP_ENABLED;
      process.env.MCP_ENABLED = 'false';
      
      try {
        await initializeMCPServers();
        const status = getMCPStatus();
        expect(status.enabled).toBe(false);
      } finally {
        process.env.MCP_ENABLED = originalEnv;
      }
    });
  });

  describe('Dante Agent with MCP', () => {
    it('should create Dante agent with MCP integration', async () => {
      const agent = await createDanteWithMCP();
      expect(agent).toBeDefined();
      expect(agent.name).toBe('Dante');
    });

    it('should create Dante agent with custom model', async () => {
      const agent = await createDanteWithMCP('gpt-4');
      expect(agent).toBeDefined();
      expect(agent.name).toBe('Dante');
    });

    it('should handle MCP creation with no connected servers', async () => {
      // Ensure no servers are connected
      await mcpServerManager.disconnectAllServers();
      
      const agent = await createDanteWithMCP();
      expect(agent).toBeDefined();
    });
  });

  describe('MCP Status Monitoring', () => {
    it('should provide MCP status information', () => {
      const status = getMCPStatus();
      
      expect(status).toHaveProperty('enabled');
      expect(status).toHaveProperty('serverCount');
      expect(status).toHaveProperty('connectedCount');
      expect(status).toHaveProperty('servers');
      expect(status).toHaveProperty('toolFactory');
      
      expect(typeof status.enabled).toBe('boolean');
      expect(typeof status.serverCount).toBe('number');
      expect(typeof status.connectedCount).toBe('number');
      expect(Array.isArray(status.servers)).toBe(true);
      expect(typeof status.toolFactory).toBe('object');
    });

    it('should show zero servers initially', () => {
      const status = getMCPStatus();
      expect(status.serverCount).toBeGreaterThanOrEqual(0);
      expect(status.connectedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('MCP Server Templates', () => {
    it('should create local filesystem template', () => {
      const config = MCPServerTemplates.localFileSystem('/tmp', {
        allowWrite: true,
        priority: 95
      });
      
      expect(config.id).toBe('filesystem-local');
      expect(config.type).toBe('stdio');
      expect(config.priority).toBe(95);
      expect(config.tags).toContain('filesystem');
      expect(config.config.fullCommand).toContain('/tmp');
    });

    it('should create local git repository template', () => {
      const config = MCPServerTemplates.localGitRepo('/repo', {
        remote: 'origin',
        priority: 85
      });
      
      expect(config.id).toBe('git-local');
      expect(config.type).toBe('stdio');
      expect(config.priority).toBe(85);
      expect(config.tags).toContain('git');
      expect(config.config.fullCommand).toContain('/repo');
    });

    it('should create database template', () => {
      const config = MCPServerTemplates.database(
        'postgresql://localhost:5432/test',
        'http://localhost:3000/mcp',
        { name: 'Test DB', priority: 75 }
      );
      
      expect(config.id).toBe('database-test-db');
      expect(config.name).toBe('Test DB');
      expect(config.type).toBe('streamable_http');
      expect(config.priority).toBe(75);
      expect(config.tags).toContain('database');
    });

    it('should create web search template', () => {
      const config = MCPServerTemplates.webSearch(
        'https://api.search.com/mcp',
        { apiKey: 'test-key', priority: 65 }
      );
      
      expect(config.id).toBe('web-search');
      expect(config.type).toBe('streamable_http');
      expect(config.priority).toBe(65);
      expect(config.tags).toContain('web');
      expect(config.config.authProvider?.token).toBe('test-key');
    });

    it('should create custom stdio template', () => {
      const config = MCPServerTemplates.customStdio(
        'custom-id',
        'Custom Server',
        'echo custom',
        { priority: 55, tags: ['custom', 'test'] }
      );
      
      expect(config.id).toBe('custom-id');
      expect(config.name).toBe('Custom Server');
      expect(config.type).toBe('stdio');
      expect(config.priority).toBe(55);
      expect(config.tags).toContain('custom');
      expect(config.config.fullCommand).toBe('echo custom');
    });

    it('should create custom HTTP template', () => {
      const config = MCPServerTemplates.customHttp(
        'http-id',
        'HTTP Server',
        'https://api.example.com/mcp',
        { 
          priority: 45, 
          tags: ['http', 'test'],
          authProvider: { type: 'bearer', token: 'auth-token' }
        }
      );
      
      expect(config.id).toBe('http-id');
      expect(config.name).toBe('HTTP Server');
      expect(config.type).toBe('streamable_http');
      expect(config.priority).toBe(45);
      expect(config.tags).toContain('http');
      expect(config.config.url).toBe('https://api.example.com/mcp');
      expect(config.config.authProvider?.token).toBe('auth-token');
    });
  });

  describe('MCP Server Collections', () => {
    it('should create development collection', () => {
      const servers = MCPServerCollections.development('/project');
      
      expect(servers.length).toBeGreaterThanOrEqual(2);
      
      const fsServer = servers.find(s => s.tags.includes('filesystem'));
      const gitServer = servers.find(s => s.tags.includes('git'));
      
      expect(fsServer).toBeDefined();
      expect(gitServer).toBeDefined();
      expect(fsServer?.config.fullCommand).toContain('/project');
      expect(gitServer?.config.fullCommand).toContain('/project');
    });

    it('should create research collection', () => {
      const servers = MCPServerCollections.research();
      
      expect(servers.length).toBeGreaterThanOrEqual(1);
      
      const fsServer = servers.find(s => s.tags.includes('filesystem'));
      expect(fsServer).toBeDefined();
    });

    it('should create data processing collection', () => {
      const servers = MCPServerCollections.dataProcessing();
      
      expect(servers.length).toBeGreaterThanOrEqual(1);
      
      const fsServer = servers.find(s => s.tags.includes('filesystem'));
      expect(fsServer).toBeDefined();
    });

    it('should create comprehensive collection', () => {
      const servers = MCPServerCollections.comprehensive('/workspace');
      
      expect(servers.length).toBeGreaterThanOrEqual(2);
      
      const fsServer = servers.find(s => s.tags.includes('filesystem'));
      const gitServer = servers.find(s => s.tags.includes('git'));
      
      expect(fsServer).toBeDefined();
      expect(gitServer).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle cleanup gracefully when no servers are registered', async () => {
      await expect(cleanupMCP()).resolves.not.toThrow();
    });

    it('should handle status check when MCP is not initialized', () => {
      const status = getMCPStatus();
      expect(status).toBeDefined();
      expect(typeof status.enabled).toBe('boolean');
    });

    it('should handle agent creation when MCP servers fail to connect', async () => {
      // Register a server that will fail to connect
      mcpServerManager.registerServer({
        id: 'failing-server',
        name: 'Failing Server',
        type: 'stdio',
        config: {
          fullCommand: 'non-existent-command'
        },
        enabled: true,
        priority: 100,
        tags: ['test']
      });

      // Should not throw even if server fails
      const agent = await createDanteWithMCP();
      expect(agent).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should handle multiple agent creations efficiently', async () => {
      const startTime = Date.now();
      
      const agents = await Promise.all([
        createDanteWithMCP(),
        createDanteWithMCP(),
        createDanteWithMCP()
      ]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(agents).toHaveLength(3);
      agents.forEach(agent => expect(agent).toBeDefined());
      
      // Should complete in reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000);
    });

    it('should handle status checks efficiently', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 10; i++) {
        const status = getMCPStatus();
        expect(status).toBeDefined();
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Multiple status checks should be very fast
      expect(duration).toBeLessThan(1000);
    });
  });
});