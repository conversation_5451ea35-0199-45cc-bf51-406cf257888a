#!/usr/bin/env bun

import { runDante } from '../index';
import { handleStream } from '../utils/streamHandler';
import { extractReasoningFromResponse, formatReasoningForDisplay } from '../utils/extractReasoning';
import chalk from 'chalk';

async function testProductionReasoning() {
  console.log(chalk.cyan('Testing Reasoning in Production Code\n'));
  console.log('=' .repeat(60));

  const testQuery = 'What are the most common causes of memory leaks in JavaScript applications?';

  console.log(chalk.yellow('Query:'), testQuery);
  console.log('\n' + '=' .repeat(60) + '\n');

  try {
    // Test with streaming
    console.log(chalk.green('Testing with streaming...'));
    const stream = await runDante([{
      role: 'user',
      content: testQuery
    }], {
      stream: true
    });

    let fullResponse = '';
    const result = await handleStream(stream as any, {
      onText: (text) => {
        fullResponse += text;
        process.stdout.write(text);
      },
      onComplete: (result) => {
        console.log('\n\n' + chalk.gray('─'.repeat(60)));

        // Check for reasoning
        const reasoning = extractReasoningFromResponse(result.parsedOutput || result);
        if (reasoning) {
          console.log(chalk.green('\n✅ Reasoning captured successfully!\n'));
          console.log(formatReasoningForDisplay(reasoning));
        } else {
          console.log(chalk.yellow('\n⚠️  No structured reasoning found in response'));
          console.log(chalk.gray('(This is normal for agents without structured output)'));
        }
      },
      showProgress: false,
      captureReasoning: true,
    });

    console.log('\n' + '=' .repeat(60));
    console.log(chalk.green('\n✅ Production reasoning test completed!'));

  } catch (error) {
    console.error(chalk.red('\n❌ Error:'), error);
  }
}

testProductionReasoning().catch(console.error);
