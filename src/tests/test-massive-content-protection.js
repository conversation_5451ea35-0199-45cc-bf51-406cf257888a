/**
 * Test script to verify massive content protection
 */

// Create a massive content string similar to your Flutter build output
const createMassiveContent = () => {
  const buildPaths = [];
  
  // Simulate thousands of build files like your Flutter example
  for (let i = 0; i < 5000; i++) {
    buildPaths.push(`"/Users/<USER>/project/build/intermediates/compile_library_classes_jar/debug/file${i}.class"`);
    buildPaths.push(`"/Users/<USER>/project/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/file${i}.class"`);
    buildPaths.push(`"/Users/<USER>/project/build/intermediates/runtime_library_classes_dir/debug/file${i}.class"`);
  }
  
  return buildPaths.join(',\n        ');
};

async function testMassiveContentProtection() {
  console.log('🧪 Testing Massive Content Protection...\n');
  
  const massiveContent = createMassiveContent();
  console.log(`📊 Generated test content: ${massiveContent.length} characters (${Math.round(massiveContent.length/1000)}KB)`);
  
  const testMessage = {
    messages: [{
      role: 'user',
      content: `Here's my project structure:\n${massiveContent}\n\nCan you help me fix a bug?`
    }]
  };
  
  try {
    console.log('\n🚀 Sending request to API server...');
    
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testMessage)
    });
    
    const result = await response.json();
    
    console.log('\n✅ Response received:');
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    console.log('Result:', result);
    
    if (result.emergencyReduction) {
      console.log('\n🛡️ SUCCESS: Emergency reduction applied!');
      console.log(`Original: ${result.originalSize} chars`);
      console.log(`Reduced: ${result.reducedSize} chars`);
      console.log(`Reduction: ${Math.round((1 - result.reducedSize/result.originalSize) * 100)}%`);
    } else {
      console.log('\n❌ FAIL: Expected emergency reduction but got normal response');
    }
    
  } catch (error) {
    console.error('\n❌ Request failed:', error.message);
  }
}

// Run the test if server is available
async function checkServerAndTest() {
  try {
    const healthCheck = await fetch('http://localhost:3001/api/health');
    if (healthCheck.ok) {
      await testMassiveContentProtection();
    } else {
      console.log('❌ Server not responding. Start server with: bun run dev:api');
    }
  } catch (error) {
    console.log('❌ Server not available. Start server with: bun run dev:api');
  }
}

checkServerAndTest();