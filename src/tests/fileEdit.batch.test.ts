import { describe, test, expect } from 'bun:test';
import { promises as fs } from 'fs';
import os from 'os';
import path from 'path';
import { fileEditTool } from '../tools/fileOperations';

function tmpFile(name: string) {
  const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
  return path.join(os.tmpdir(), `file-edit-batch-${id}-${name}`);
}

describe('file_edit batch transactions', () => {
  test('best_effort batch applies multiple ops', async () => {
    const f1 = tmpFile('be1.txt');
    const f2 = tmpFile('be2.txt');
    await fs.writeFile(f1, 'a', 'utf-8');
    await fs.writeFile(f2, 'x', 'utf-8');
    const res = await (fileEditTool as any).execute({
      ops: [
        { operation: 'replace', filePath: f1, pattern: 'a', replacement: 'A' },
        { operation: 'write', filePath: f2, content: 'Y' }
      ],
      transaction: 'best_effort',
      diagnostics: false,
    });
    expect(res.success).toBe(true);
    expect(res.batch).toBe(true);
    const c1 = await fs.readFile(f1, 'utf-8');
    const c2 = await fs.readFile(f2, 'utf-8');
    expect(c1).toBe('A');
    expect(c2).toBe('Y');
    await fs.unlink(f1).catch(() => {});
    await fs.unlink(f2).catch(() => {});
  });

  test('all_or_nothing batch rolls back on failure', async () => {
    const f1 = tmpFile('ao1.txt');
    const f2 = tmpFile('ao2.txt');
    await fs.writeFile(f1, 'a', 'utf-8');
    await fs.writeFile(f2, 'x', 'utf-8');
    // Second op will fail (pattern not found), should rollback first in all_or_nothing mode
    const res = await (fileEditTool as any).execute({
      ops: [
        { operation: 'replace', filePath: f1, pattern: 'a', replacement: 'A' },
        { operation: 'replace', filePath: f2, pattern: 'z', replacement: 'Z' }
      ],
      transaction: 'all_or_nothing',
      diagnostics: false,
    });
    expect(res.success).toBe(false);
    const c1 = await fs.readFile(f1, 'utf-8');
    const c2 = await fs.readFile(f2, 'utf-8');
    // Ensure rollback happened (f1 should remain 'a')
    expect(c1).toBe('a');
    expect(c2).toBe('x');
    await fs.unlink(f1).catch(() => {});
    await fs.unlink(f2).catch(() => {});
  });
});

