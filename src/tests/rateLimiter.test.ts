import { describe, it, expect, beforeEach, afterEach, spyOn } from 'bun:test';

describe('server/utils/rateLimiter TokenBucketLimiter', () => {
  let recordedDelays: number[] = [];
  let fakeNow = 0;
  let timeoutSpy: any;
  let dateNowSpy: any;
  let randomSpy: any;

  beforeEach(() => {
    recordedDelays = [];
    // Make "now" large so first call has big elapsed and skips spacing
    fakeNow = 10_000;

    // Configure rate limiter env (read at module import time through src/utils/config)
    process.env.RATE_LIMIT_ENABLED = 'true';
    process.env.RATE_LIMIT_TOKENS_PER_SECOND = '1';   // 1 token/sec
    process.env.RATE_LIMIT_MAX_TOKENS = '1';          // bucket capacity = 1
    process.env.RATE_MIN_SPACING_MS = '1000';         // 1s min spacing
    process.env.RATE_MAX_SPACING_MS = '2000';         // up to 2s spacing (for jitter test)

    // Fake Date.now
    dateNowSpy = spyOn(Date, 'now').mockImplementation(() => fakeNow);

    // Intercept setTimeout: record requested delay, advance fakeNow, execute immediately
    timeoutSpy = spyOn(globalThis as any, 'setTimeout').mockImplementation(((cb: (...args: any[]) => void, ms?: number) => {
      const delay = typeof ms === 'number' ? ms : 0;
      recordedDelays.push(delay);
      fakeNow += delay;
      // Execute callback immediately to resolve timers without real waiting
      cb();
      return 0 as any;
    }) as any);
  });

  afterEach(() => {
    timeoutSpy?.mockRestore();
    dateNowSpy?.mockRestore();
    if (randomSpy) {
      randomSpy.mockRestore();
      randomSpy = undefined;
    }
    // Clean up env
    delete process.env.RATE_LIMIT_ENABLED;
    delete process.env.RATE_LIMIT_TOKENS_PER_SECOND;
    delete process.env.RATE_LIMIT_MAX_TOKENS;
    delete process.env.RATE_MIN_SPACING_MS;
    delete process.env.RATE_MAX_SPACING_MS;
  });

  it('budgets tokens and refills at the configured rate', async () => {
    // Deterministic spacing (no jitter) so spacing delay = 1000ms
    randomSpy = spyOn(Math, 'random').mockReturnValue(0);

    const { TokenBucketLimiter } = await import('../../server/utils/rateLimiter');
    const limiter = new TokenBucketLimiter();

    // First consume uses 1 token (bucket from 1 -> 0), no spacing due to large initial fakeNow
    await limiter.consume('refillKey', 1);

    // Immediate second consume must wait for refill (1 token @ 1 tps => 1000ms), then spacing (1000ms)
    await limiter.consume('refillKey', 1);

    // The first recorded delay is the refill wait
    expect(recordedDelays.length).toBeGreaterThanOrEqual(1);
    expect(Math.round(recordedDelays[0])).toBe(1000);
  });

  it('enforces minimum spacing between consumes', async () => {
    // Deterministic spacing (min only)
    randomSpy = spyOn(Math, 'random').mockReturnValue(0);

    const { TokenBucketLimiter } = await import('../../server/utils/rateLimiter');
    const limiter = new TokenBucketLimiter();

    // First consume (no spacing due to large initial elapsed)
    await limiter.consume('spaceKey', 1);

    // Second consume triggers: refill delay (1000ms), then spacing delay (min 1000ms)
    await limiter.consume('spaceKey', 1);

    // The last delay recorded corresponds to spacing enforcement
    const spacingDelay = recordedDelays[recordedDelays.length - 1];
    expect(Math.round(spacingDelay)).toBe(1000);
  });

  it('applies jitter to spacing deterministically when Math.random is seeded', async () => {
    // Jitter: min=1000, max=2000, random=0.4 -> spacing = 1000 + 0.4*(2000-1000) = 1400
    randomSpy = spyOn(Math, 'random').mockReturnValue(0.4);

    const { TokenBucketLimiter } = await import('../../server/utils/rateLimiter');
    const limiter = new TokenBucketLimiter();

    await limiter.consume('jitterKey', 1);   // first consume (no spacing due to large initial elapsed)
    await limiter.consume('jitterKey', 1);   // triggers refill 1000ms, then spacing with jitter 1400ms

    const spacingDelay = recordedDelays[recordedDelays.length - 1];
    expect(Math.round(spacingDelay)).toBe(1400);
  });
});
