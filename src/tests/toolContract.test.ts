import { describe, test, expect } from 'bun:test';
import os from 'os';
import path from 'path';
import { promises as fs } from 'fs';

import {
  readFileTool,
  readFilesTool,
  fileEditTool,
  assertFileContainsTool,
  listDirectoryTool,
  executeCodeTool,
  analyzeProjectTool,
  gitOperationTool,
  searchCodeTool,
  setWorkingDirectoryTool,
  getWorkingDirectoryTool,
  getProjectContextTool,
} from '../tools/fileOperations';

function assertContract(result: any, expectedTool: string) {
  expect(typeof result).toBe('object');
  expect(result).not.toBeNull();
  expect(Array.isArray(result)).toBe(false);

  expect(typeof result.success).toBe('boolean');
  expect(result.tool).toBe(expectedTool);
  expect(result.version).toBe('v1');

  if (!result.success) {
    expect(result.error).toBeDefined();
    expect(typeof result.error.message).toBe('string');
  }
}

const repoFile = path.resolve(process.cwd(), 'src/tools/toolResult.ts');

function tmpFile(name: string) {
  const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
  return path.join(os.tmpdir(), `tool-contract-${id}-${name}`);
}

describe('v1 tool-output contract', () => {
  test('read_file returns v1 object with required keys and payload fields', async () => {
    const res = await (readFileTool as any).execute({
      filePath: repoFile,
      forceFullContent: true,
    });

    assertContract(res, 'read_file');
    expect(res.path).toBe(repoFile);
    expect(typeof res.content).toBe('string');
    expect(typeof res.lines).toBe('number');
  });

  test('read_files returns v1 object with aggregated results', async () => {
    const res = await (readFilesTool as any).execute({
      filePaths: [repoFile],
      forceFullContent: true,
    });

    assertContract(res, 'read_files');
    expect(typeof res.totalRequested).toBe('number');
    expect(typeof res.totalProcessed).toBe('number');
    expect(Array.isArray(res.results)).toBe(true);
  });

  test('file_edit (write, replace, patch), assert_file_contains sequence adheres to v1', async () => {
    const filePath = tmpFile('sequence.txt');
    try {
      // file_edit (write)
      const writeRes = await (fileEditTool as any).execute({
        operation: 'write',
        filePath,
        content: 'hello',
        createDirs: true,
      });
      assertContract(writeRes, 'file_edit');
      expect(writeRes.path).toBe(filePath);
      expect(typeof writeRes.bytesWritten).toBe('number');
      expect(typeof writeRes.hashes?.after).toBe('string');

      // file_edit (replace)
      const editRes = await (fileEditTool as any).execute({
        operation: 'replace',
        filePath,
        pattern: 'hello',
        replacement: 'hello world',
      });
      assertContract(editRes, 'file_edit');
      expect(editRes.path).toBe(filePath);
      expect(editRes.modified).toBe(true);
      expect(typeof editRes.replacements).toBe('number');

      // file_edit (patch - anchored)
      const patchRes = await (fileEditTool as any).execute({
        operation: 'patch',
        patchFormat: 'anchored',
        filePath,
        before: 'world',
        after: 'WORLD',
      });
      assertContract(patchRes, 'file_edit');
      expect(patchRes.path).toBe(filePath);
      expect(patchRes.modified).toBe(true);
      expect(typeof patchRes.replacements).toBe('number');

      // assert_file_contains
      const containsRes = await (assertFileContainsTool as any).execute({
        filePath,
        includes: [{ text: 'hello WORLD' }, { regex: 'WORLD' }],
      });
      assertContract(containsRes, 'assert_file_contains');
      expect(containsRes.path).toBe(filePath);
      expect(containsRes.allFound).toBe(true);
      expect(Array.isArray(containsRes.checks)).toBe(true);
    } finally {
      await fs.unlink(filePath).catch(() => {});
    }
  });

  test('list_directory returns v1 object for non-recursive listing', async () => {
    const dirPath = process.cwd();
    const res = await (listDirectoryTool as any).execute({
      dirPath,
      recursive: false,
    });

    assertContract(res, 'list_directory');
    expect(res.path).toBe(path.resolve(dirPath));
    expect(Array.isArray(res.files)).toBe(true);
  });

  test('execute_code (bash) returns v1 object regardless of output', async () => {
    const res = await (executeCodeTool as any).execute({
      language: 'bash',
      code: 'echo ok',
    });

    assertContract(res, 'execute_code');
    // Only shape assertions; payload fields sample
    expect(res.language).toBe('bash');
  });

  test('analyze_project returns v1 object with analysis payload', async () => {
    const res = await (analyzeProjectTool as any).execute({
      projectPath: process.cwd(),
    });

    assertContract(res, 'analyze_project');
    expect(typeof res.analysis).toBe('object');
    expect(res.analysis.path).toBe(path.resolve(process.cwd()));
  });

  test('git_operation (status) returns v1 object (success or error) with tool key', async () => {
    const res = await (gitOperationTool as any).execute({
      operation: 'status',
    });

    // Contract only; may fail in non-git environments
    assertContract(res, 'git_operation');
  });

  test('search_code returns v1 object with matches summary', async () => {
    const res = await (searchCodeTool as any).execute({
      pattern: 'makeSuccess',
      path: path.resolve(process.cwd(), 'src/tools'),
      fileTypes: ['ts'],
    });

    assertContract(res, 'search_code');
    expect(typeof res.matches).toBe('number');
    expect(Array.isArray(res.results)).toBe(true);
  });

  test('set_working_directory followed by get_working_directory preserves cwd', async () => {
    const cwd = process.cwd();

    const setRes = await (setWorkingDirectoryTool as any).execute({
      dirPath: cwd,
    });
    assertContract(setRes, 'set_working_directory');
    expect(setRes.path).toBe(path.resolve(cwd));

    const getRes = await (getWorkingDirectoryTool as any).execute({});
    assertContract(getRes, 'get_working_directory');
    expect(getRes.path).toBe(path.resolve(cwd));
  });

  test('get_project_context returns v1 object with context payload', async () => {
    const res = await (getProjectContextTool as any).execute({
      includeDependencies: false,
      includeGitInfo: false,
    });

    assertContract(res, 'get_project_context');
    expect(typeof res.context).toBe('object');
    expect(res.context.workingDirectory).toBe(process.cwd());
  });
});
