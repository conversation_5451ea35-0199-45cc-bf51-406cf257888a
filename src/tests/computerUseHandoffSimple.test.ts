import { describe, test, expect } from 'bun:test';
import DanteOrchestrator from '../agents/DanteOrchestrator';
import { computerUseAgent } from '../agents/vercel/ComputerUseAgent';

describe('Computer Use Handoff Simple Integration', () => {
  test('DanteOrchestrator should include Computer Use Agent handoff', () => {
    const handoffs = DanteOrchestrator.handoffs || [];

    expect(handoffs).toBeDefined();
    expect(handoffs.length).toBeGreaterThan(0);

    // Look for any handoff that could be computer use related
    const handoffNames = handoffs.map((h: any) => h.agent?.name || 'unknown');
    console.log('Available handoffs:', handoffNames);

    // Check if any handoff includes computer use agent
    const hasComputerUseHandoff = handoffs.some((handoff: any) =>
      handoff.agent?.name === 'ComputerUseAgent' ||
      (handoff.toolDescriptionOverride &&
       handoff.toolDescriptionOverride.toLowerCase().includes('computer'))
    );

    expect(hasComputerUseHandoff).toBe(true);
  });

  test('Computer Use Agent should be properly configured', () => {
    expect(computerUseAgent).toBeDefined();
    expect(computerUseAgent.name).toBe('ComputerUseAgent');
    expect(computerUseAgent.tools).toBeDefined();

    // Check that it has the computer use tool
    const tools = computerUseAgent.tools || [];
    const hasComputerUseTool = tools.some((tool: any) =>
      tool.name === 'computer_use'
    );

    expect(hasComputerUseTool).toBe(true);
  });

  test('Computer use capability should be recognized in instructions', () => {
    // Check that the main agent instructions mention computer use
    const instructions = typeof DanteOrchestrator.instructions === 'string'
      ? DanteOrchestrator.instructions
      : '';

    expect(instructions).toBeDefined();
    expect(instructions.toLowerCase()).toMatch(/computer.*use|browser.*automation/);
  });

  test('Handoff system is properly structured', () => {
    const handoffs = DanteOrchestrator.handoffs || [];

    // Each handoff should have the required structure
    handoffs.forEach((handoff: any, index: number) => {
      expect(handoff).toBeDefined();
      expect(handoff.agent).toBeDefined();
      if (handoff.toolDescriptionOverride) {
        expect(typeof handoff.toolDescriptionOverride).toBe('string');
      }
    });

    // Specifically check that ComputerUseAgent handoff exists
    const computerUseHandoff = handoffs.find((h: any) => h.agent?.name === 'ComputerUseAgent');
    expect(computerUseHandoff).toBeDefined();
  });
});
