import { describe, test, expect } from 'bun:test';
import { webSearchTool } from '../tools/webSearch';
import { webContentChunkTool } from '../tools/webContentChunk';

describe('Web Search Tool Integration', () => {
  test('should have correct parameter structure for webSearchTool', async () => {
    // Test that the tool has correct properties
    expect(webSearchTool.name).toBe('web_search');
    expect(webSearchTool.description).toContain('Search the internet');
    
    // The tool should accept these parameters without throwing
    const params = {
      query: 'test query',
      maxResults: 3,
      fetchContent: true,
      contentDepth: 'detailed',
      enableChunking: true
    };
    
    // This should not throw a parameter validation error
    expect(() => {
      // Just test that parameters are accepted - we won't run it due to network dependencies
      const paramKeys = Object.keys(params);
      expect(paramKeys).toContain('query');
      expect(paramKeys).toContain('contentDepth');
      expect(paramKeys).toContain('enableChunking');
    }).not.toThrow();
  });

  test('should have correct parameter structure for webContentChunkTool', async () => {
    expect(webContentChunkTool.name).toBe('web_content_chunk');
    expect(webContentChunkTool.description).toContain('chunk');
    
    // Test parameter structure
    const params = {
      url: 'https://example.com',
      chunkIds: ['intro', 'conclusion'],
      contentDepth: 'detailed',
      includeChunks: true
    };
    
    const paramKeys = Object.keys(params);
    expect(paramKeys).toContain('url');
    expect(paramKeys).toContain('chunkIds');
    expect(paramKeys).toContain('contentDepth');
    expect(paramKeys).toContain('includeChunks');
  });

  test('should handle tool parameter validation', () => {
    // Test that valid content depth values are accepted
    const validDepths = ['summary', 'detailed', 'full'];
    
    validDepths.forEach(depth => {
      expect(['summary', 'detailed', 'full']).toContain(depth);
    });
    
    // Test that chunk IDs are arrays
    const chunkIds = ['intro', 'section-1', 'conclusion'];
    expect(Array.isArray(chunkIds)).toBe(true);
    expect(chunkIds.length).toBeGreaterThan(0);
  });
});