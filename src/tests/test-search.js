#!/usr/bin/env bun

import { webSearchTool } from '../tools/webSearch.ts';

async function testSearch() {
  console.log('Testing web search functionality...\n');

  try {
    // Test 1: Basic search
    console.log('Test 1: Basic search for "artificial intelligence news 2024"');
    const result1 = await webSearchTool.execute({
      query: 'artificial intelligence news 2024',
      maxResults: 3,
      fetchContent: false
    });

    const data1 = JSON.parse(result1);
    console.log('Success:', data1.success);
    console.log('Results found:', data1.results?.length || 0);
    console.log('Sources used:', data1.sourcesUsed);

    if (data1.results && data1.results.length > 0) {
      console.log('\nFirst result:');
      console.log('- Title:', data1.results[0].title);
      console.log('- URL:', data1.results[0].url);
      console.log('- Source:', data1.results[0].source);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Search with content fetching
    console.log('Test 2: Search with content fetching for "OpenAI GPT-5"');
    const result2 = await webSearchTool.execute({
      query: 'OpenAI GPT-5',
      maxResults: 2,
      fetchContent: true
    });

    const data2 = JSON.parse(result2);
    console.log('Success:', data2.success);
    console.log('Results found:', data2.results?.length || 0);

    if (data2.results && data2.results.length > 0 && data2.results[0].fullContent) {
      console.log('\nFirst result has fetched content:', !!data2.results[0].fullContent);
      console.log('Content preview (first 200 chars):',
        data2.results[0].fullContent?.substring(0, 200) + '...');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testSearch();
