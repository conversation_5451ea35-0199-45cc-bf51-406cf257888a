import { describe, test, expect } from 'bun:test';
import { interruptBus } from '../utils/interruptBus';
import type { InterruptionEvent } from '../types/interruption';

const mkEvt = (type: 'pause'|'cancel'|'steer'|'replace-next', payload?: any): InterruptionEvent => ({
  event: 'agent.interruption',
  type,
  sessionId: `sess_${Math.random().toString(36).slice(2,8)}`,
  timestamp: new Date().toISOString(),
  payload
});

describe('interruptBus basic behavior', () => {
  test('cancel transitions to cancelled and aborts', () => {
    const evt = mkEvt('cancel');
    const status = interruptBus.handle(evt);
    expect(status).toBe('cancelled');
    // second time idempotent
    const status2 = interruptBus.handle(evt);
    expect(status2).toBe('cancelled');
  });

  test('pause transitions to paused and stores controller', () => {
    const evt = mkEvt('pause');
    const status = interruptBus.handle(evt);
    expect(status).toBe('paused');
    const rec = interruptBus.getRecord(evt.sessionId);
    expect(rec?.status).toBe('paused');
  });

  test('steer stores injected context and marks applied', () => {
    const evt = mkEvt('steer', { message: 'Keep answers concise', priority: 'high', constraints: { style: 'succinct' } });
    const status = interruptBus.handle(evt);
    expect(status).toBe('applied');
    const rec = interruptBus.getRecord(evt.sessionId);
    expect(rec?.lastAppliedPayload?.message).toContain('concise');
    const updates = interruptBus.consumeInjectedContext(evt.sessionId);
    expect(updates.length).toBe(1);
    expect(updates[0]?.constraints?.style).toBe('succinct');
  });

  test('replace-next overrides and is consumed once', () => {
    const evt = mkEvt('replace-next', { message: 'Do X next', priority: 'medium' });
    const status = interruptBus.handle(evt);
    expect(status).toBe('applied');
    const one = interruptBus.consumeReplaceNext(evt.sessionId);
    expect(one?.message).toBe('Do X next');
    const two = interruptBus.consumeReplaceNext(evt.sessionId);
    expect(two).toBeUndefined();
  });

  test('resume creates new controller and status updated', () => {
    const evt = mkEvt('pause');
    interruptBus.handle(evt);
    interruptBus.resume(evt.sessionId);
    const rec = interruptBus.getRecord(evt.sessionId);
    expect(rec?.status).toBe('resumed');
  });
});
