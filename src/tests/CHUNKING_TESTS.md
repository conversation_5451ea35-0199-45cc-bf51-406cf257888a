# Web Search Chunking Tests

Comprehensive production-level test suite for the web search chunking functionality.

## Overview

This test suite validates the chunking features added to the web search tools, ensuring they work correctly in all scenarios from unit functions to full agent integration.

## Test Structure

### 🧪 Unit Tests (`webContentChunking.test.ts`)
Tests core chunking and summarization functions:
- Content extraction from HTML
- Chunk generation with proper priorities
- Summary creation
- Text processing edge cases
- Content length limits

### 🔧 Integration Tests (`webSearchTool.integration.test.ts`)
Tests the webSearchTool with chunking parameters:
- Basic search functionality
- Content fetching with chunking enabled
- Parameter validation
- Response format consistency
- Error handling

### 🌍 End-to-End Tests (`webContentChunkTool.e2e.test.ts`)
Tests the complete webContentChunkTool workflow:
- URL content fetching
- Specific chunk retrieval
- Progressive content loading
- Different content depths
- Real-world scenarios

### 🤖 Agent Integration Tests (`agentChunkingIntegration.test.ts`)
Tests agent compatibility with chunking tools:
- Tool availability in agents
- Parameter passing
- Tool chaining workflows
- Error handling in agent context

### ⚠️ Error Handling Tests (`chunkingErrorHandling.test.ts`)
Comprehensive error scenario testing:
- Network failures (DNS, timeouts, SSL)
- HTTP error status codes
- Malformed content
- Invalid parameters
- Resource management

### ⚡ Performance Benchmarks (`chunkingPerformance.benchmark.test.ts`)
Performance validation and regression detection:
- Chunking vs non-chunking overhead
- Different content depths
- Concurrent operations
- Memory usage
- Response times

## Running Tests

### Quick Commands

```bash
# Run all chunking tests
bun run test:chunking

# Run specific test categories
bun run test:chunking:unit
bun run test:chunking:integration
bun run test:chunking:e2e
bun run test:chunking:agents
bun run test:chunking:errors
bun run test:chunking:performance
```

### Individual Test Files

```bash
# Run specific test files directly
bun test src/tests/webContentChunking.test.ts
bun test src/tests/webSearchTool.integration.test.ts
bun test src/tests/webContentChunkTool.e2e.test.ts
bun test src/tests/agentChunkingIntegration.test.ts
bun test src/tests/chunkingErrorHandling.test.ts
bun test src/tests/chunkingPerformance.benchmark.test.ts
```

## Test Reports

The comprehensive test runner generates detailed reports:

- **Console Output**: Real-time progress and summary
- **JSON Report**: `src/tests/chunking-test-report.json`

### Report Structure

```json
{
  "timestamp": "2024-08-17T...",
  "summary": {
    "totalTests": 6,
    "passedTests": 6,
    "failedTests": 0,
    "totalDuration": 15432,
    "successRate": 100
  },
  "results": [...],
  "environment": {
    "nodeVersion": "v18.18.0",
    "platform": "darwin",
    "arch": "x64"
  }
}
```

## Test Coverage

### Features Tested ✅

- **Content Chunking**
  - Intro/conclusion detection
  - Section identification
  - Priority assignment
  - Chunk metadata

- **Content Processing**
  - HTML parsing and cleaning
  - Text extraction
  - News site optimizations
  - Content summarization

- **Tool Integration**
  - Parameter validation
  - Response formatting
  - Error handling
  - Agent compatibility

- **Performance**
  - Response times
  - Memory usage
  - Concurrent operations
  - Scalability

- **Error Scenarios**
  - Network failures
  - HTTP errors
  - Invalid inputs
  - Edge cases

### Performance Baselines

- **Basic Search**: < 2 seconds
- **Search with Content**: < 4 seconds  
- **Search with Chunking**: < 5 seconds
- **Memory Usage**: < 50MB increase
- **Chunking Overhead**: < 50%

## Debugging Failed Tests

### Common Issues

1. **Network Mocking**: Tests use mocked fetch requests
2. **Timeout Issues**: Adjust timeout values for slow environments
3. **Memory Limits**: Monitor memory usage during benchmarks
4. **Type Errors**: Ensure all interfaces are properly defined

### Debugging Commands

```bash
# Run with verbose output
bun test --verbose src/tests/webContentChunking.test.ts

# Run specific test case
bun test --grep "should chunk content properly"

# Debug mode
bun test --inspect src/tests/webSearchTool.integration.test.ts
```

## Adding New Tests

### Test File Template

```typescript
import { describe, test, expect, mock, beforeEach } from 'bun:test';
import { /* import what you're testing */ } from '../tools/...';

describe('Your Feature Tests', () => {
  beforeEach(() => {
    // Setup mocks
    console.error = mock(() => {});
  });

  test('should do something specific', async () => {
    // Arrange
    const input = { /* test data */ };
    
    // Act
    const result = await yourFunction(input);
    
    // Assert
    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});
```

### Naming Conventions

- Test files: `*.test.ts` or `*.e2e.test.ts`
- Test descriptions: Use "should" statements
- Test categories: Group related tests in `describe` blocks

## Continuous Integration

### Pre-commit Hooks

Add to `.husky/pre-commit`:

```bash
#!/bin/sh
bun run test:chunking
```

### GitHub Actions

```yaml
- name: Run Chunking Tests
  run: bun run test:chunking
  
- name: Upload Test Report
  uses: actions/upload-artifact@v3
  with:
    name: chunking-test-report
    path: src/tests/chunking-test-report.json
```

## Maintenance

### Regular Tasks

- [ ] Update performance baselines quarterly
- [ ] Review test coverage after major changes
- [ ] Update mock data to reflect real-world scenarios
- [ ] Monitor test execution times

### When to Update Tests

- Adding new chunking features
- Changing tool parameters
- Modifying error handling
- Performance optimizations
- Agent integration changes

## Troubleshooting

### Common Solutions

1. **Tests timing out**: Increase timeout values in test files
2. **Mock fetch not working**: Ensure `global.fetch = mockFetch` is set
3. **Type errors**: Update interfaces in `webSearchHelpers.ts`
4. **Memory issues**: Reduce test iteration counts

### Support

For issues with the test suite:

1. Check the test report JSON for detailed error info
2. Run individual test categories to isolate issues
3. Review console output for specific error messages
4. Ensure all dependencies are installed: `bun install`

---

## Example Test Execution

```bash
$ bun run test:chunking

🚀 Web Search Chunking Test Suite
Testing comprehensive chunking functionality...

🧪 Running Unit Tests...
   Content chunking and summarization functions
   ✅ Unit Tests passed (2341ms)

🧪 Running Integration Tests...
   WebSearchTool with chunking parameters
   ✅ Integration Tests passed (4567ms)

🧪 Running End-to-End Tests...
   WebContentChunkTool complete workflows
   ✅ End-to-End Tests passed (3890ms)

🧪 Running Agent Integration Tests...
   Agent integration with chunking capabilities
   ✅ Agent Integration Tests passed (2134ms)

🧪 Running Error Handling Tests...
   Error scenarios and edge cases
   ✅ Error Handling Tests passed (3456ms)

🧪 Running Performance Benchmarks...
   Performance benchmarks and regression detection
   ✅ Performance Benchmarks passed (8912ms)

📊 Test Results Summary
==================================================
Total Test Suites: 6
Passed: 6
Failed: 0
Total Duration: 25300ms (25.30s)
Success Rate: 100.0%

🎉 All chunking tests passed successfully!
   Your web search chunking implementation is ready for production.

📄 Detailed report saved to: src/tests/chunking-test-report.json
```

This comprehensive test suite ensures the web search chunking functionality is production-ready and maintainable.