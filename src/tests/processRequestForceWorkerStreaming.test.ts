import { describe, test, expect } from 'bun:test';
import { ModelOrchestrator } from '../agents/ModelOrchestrator';

function makeVercelTextStream(chunks: string[]) {
  return {
    textStream: {
      async *[Symbol.asyncIterator]() {
        for (const c of chunks) {
          await new Promise((r) => setTimeout(r, 1));
          yield c;
        }
      },
    },
    completed: Promise.resolve(),
    state: { output: [{ content: chunks.join('') }] },
  } as any;
}

describe('ModelOrchestrator.processRequest with forceWorker', () => {
  test('returns streaming result from mocked research worker', async () => {
    const orchestrator = new ModelOrchestrator();

    const mockWorker = {
      execute: async (_input: any, _opts: any) => makeVercelTextStream(['Breaking', ' ', 'news']),
    };
    (orchestrator as any).workerRegistry = new Map<string, any>([['research', mockWorker]]);

    const result = await orchestrator.processRequest(
      'What\'s new in tech today?',
      'gpt-4o',
      {
        stream: true,
        forceWorker: 'research',
        agent: { name: 'DanteOrchestrator', model: 'gpt-4o', instructions: '' },
      }
    );

    expect(result).toBeDefined();

    // Support both shapes: result may itself be an AsyncIterable or expose a .textStream AsyncIterable
    const stream = (result as any).textStream ?? result;
    expect(stream).toBeDefined();

    let collected = '';
    for await (const delta of stream) {
      collected += String(delta);
    }
    expect(collected).toBe('Breaking news');
  });
});

