import { describe, test, expect, beforeEach, afterEach } from 'bun:test';
import { join } from 'path';
import { existsSync, rmSync } from 'fs';

// Lightweight copy of Conversation shape for test objects
type Conversation = {
  id: string;
  title: string;
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
};

describe('Conversations repository (persistent DB)', () => {
  let testDbPath: string;

  const makeUniqueDbPath = () => {
    const uid = `${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
    return join(process.cwd(), `test-conversations-${uid}.db`);
  };

  const deleteDbFiles = (base: string) => {
    if (!base || base === ':memory:') return;
    const candidates = [base, `${base}-wal`, `${base}-shm`, `${base}-journal`];
    for (const p of candidates) {
      try {
        if (existsSync(p)) rmSync(p, { force: true });
      } catch {}
    }
  };

  // Dynamically import a fresh copy of the conversations module per test.
  // Cache-bust via query string to avoid module singletons across tests.
  const importConversations = async () => {
    const mod = await import(`../repositories/conversations?ts=${Date.now()}-${Math.random()}`);
    return mod as {
      createConversation: (c: Conversation) => Promise<Conversation>;
      getConversationById: (id: string) => Promise<Conversation | undefined>;
      updateConversationSummary: (id: string, summary: string) => Promise<void>;
      listConversations: (limit?: number) => Promise<Conversation[]>;
    };
  };

  beforeEach(async () => {
    // Force sqlite for consistent tests
    process.env.DATABASE_TYPE = 'sqlite';
    delete process.env.DATABASE_URL;

    // Unique database file per test for isolation
    testDbPath = makeUniqueDbPath();
    process.env.SQLITE_DB_FILE = testDbPath;

    // Pre-clean in case of prior remnants (unlikely but safe)
    deleteDbFiles(testDbPath);
  });

  afterEach(async () => {
    // Attempt cleanup. The module keeps the connection open, so deletion may fail on some OSes.
    // It's acceptable as names are unique and won't affect isolation; ignore errors.
    deleteDbFiles(testDbPath);

    // Reset env to avoid any bleed-over
    delete process.env.SQLITE_DB_FILE;
    delete process.env.DATABASE_TYPE;
  });

  test('creates and retrieves a conversation', async () => {
    const conversations = await importConversations();

    const id = `conv-${crypto.randomUUID()}`;
    const now = new Date();
    const convo: Conversation = {
      id,
      title: 'First Conversation',
      createdAt: now,
      updatedAt: now
    };

    const created = await conversations.createConversation(convo);
    expect(created.id).toBe(id);
    expect(created.title).toBe('First Conversation');
    expect(created.summary).toBeUndefined();

    const fetched = await conversations.getConversationById(id);
    expect(fetched).toBeDefined();
    expect(fetched?.id).toBe(id);
    expect(fetched?.title).toBe('First Conversation');
  });

  test('updates summary and persists to database', async () => {
    const conversations = await importConversations();

    const id = `conv-${crypto.randomUUID()}`;
    const baseTime = new Date('2000-01-01T00:00:00Z');
    await conversations.createConversation({
      id,
      title: 'Needs Summary',
      createdAt: baseTime,
      updatedAt: baseTime
    });

    await conversations.updateConversationSummary(id, 'A concise summary');

    const fetched = await conversations.getConversationById(id);
    expect(fetched).toBeDefined();
    expect(fetched?.summary).toBe('A concise summary');
    // updatedAt should be at least as new as creation time
    expect((fetched!.updatedAt).getTime()).toBeGreaterThanOrEqual(baseTime.getTime());
  });

  test('updating summary for non-existent conversation does not throw and is handled gracefully', async () => {
    const conversations = await importConversations();

    const missingId = `missing-${crypto.randomUUID()}`;
    await expect(conversations.updateConversationSummary(missingId, 'No-op')).resolves.toBeUndefined();

    const fetched = await conversations.getConversationById(missingId);
    expect(fetched).toBeUndefined();
  });

  test('lists conversations with expected results and order', async () => {
    const conversations = await importConversations();

    // Create three conversations with controlled timestamps
    const t0 = new Date('2001-01-01T00:00:00Z');
    const t1 = new Date('2001-01-01T00:00:10Z');
    const t2 = new Date('2001-01-01T00:00:20Z');

    const idA = `conv-${crypto.randomUUID()}`;
    const idB = `conv-${crypto.randomUUID()}`;
    const idC = `conv-${crypto.randomUUID()}`;

    await conversations.createConversation({ id: idA, title: 'A', createdAt: t0, updatedAt: t0 });
    await conversations.createConversation({ id: idB, title: 'B', createdAt: t1, updatedAt: t1 });
    await conversations.createConversation({ id: idC, title: 'C', createdAt: t2, updatedAt: t2 });

    // Update B to bump its updated_at to "now" so it should appear first in DESC order
    await conversations.updateConversationSummary(idB, 'updated');

    const list = await conversations.listConversations(10);
    expect(list.length).toBe(3);

    // Expect the most recently updated (idB) first
    expect(list[0].id).toBe(idB);

    // Ensure all expected conversations are present
    const ids = new Set(list.map(c => c.id));
    expect(ids.has(idA)).toBe(true);
    expect(ids.has(idB)).toBe(true);
    expect(ids.has(idC)).toBe(true);
  });
});
