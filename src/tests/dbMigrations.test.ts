import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { join } from 'path';
import { existsSync, rmSync } from 'fs';
import { runMigrations } from '../db/migrate';
import { listConversations } from '../repositories/conversations';
import { getMessagesByConversationId } from '../repositories/messages';

describe('DB Migrations (centralized schema)', () => {
  let testDbPath: string;

  const makeUniqueDbPath = () => {
    const uid = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
    return join(process.cwd(), `test-migrations-${uid}.db`);
  };

  const deleteDbFiles = (base: string) => {
    if (!base || base === ':memory:') return;
    const candidates = [base, `${base}-wal`, `${base}-shm`, `${base}-journal`];
    for (const p of candidates) {
      try {
        if (existsSync(p)) rmSync(p, { force: true });
      } catch {}
    }
  };

  beforeAll(() => {
    // Use a unique SQLite file to isolate this test
    testDbPath = makeUniqueDbPath();
    process.env.DATABASE_TYPE = 'sqlite';
    process.env.SQLITE_DB_FILE = testDbPath;
  });

  afterAll(async () => {
    // Clean up database files
    deleteDbFiles(testDbPath);
  });

  test('runMigrations completes without error and allows basic queries', async () => {
    await expect(runMigrations()).resolves.toBeUndefined();

    // Repositories should open the same DB via env and read successfully
    const convs = await listConversations(1);
    expect(Array.isArray(convs)).toBe(true);

    // Query messages table for a random conversation; should return empty array
    const msgs = await getMessagesByConversationId('non-existent');
    expect(Array.isArray(msgs)).toBe(true);
    expect(msgs.length).toBe(0);
  });
});
