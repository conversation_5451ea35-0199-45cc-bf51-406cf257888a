import { describe, it, expect } from 'bun:test';
import { CodeReviewAgent } from '../agents/CodeReviewAgent';

describe('CodeReviewAgent line number tracking', () => {
  it('attaches correct line numbers for added lines across hunks', async () => {
    const diff = [
      'diff --git a/src/foo.ts b/src/foo.ts',
      'index 0000000..1111111 100644',
      '--- a/src/foo.ts',
      '+++ b/src/foo.ts',
      '@@ -1,3 +1,4 @@',
      ' const a = 1;',
      ' // comment',
      '+// @ts-ignore',
      ' const b = 2;',
      '@@ -10,2 +11,5 @@ function foo() {',
      '   // body',
      '-  old line removed',
      '+  console.log(\'debug\');',
      '+  const v: any = 123;',
      '+  // FIXME something',
      ' }',
      '@@ -24,1 +26,2 @@ tail',
      ' tail',
      '+const SECRET = process.env.API_KEY;',
    ].join('\n');

    const res = await CodeReviewAgent.execute({ diffs: diff });

    const file = 'src/foo.ts';

    // High: ts-ignore at line 3
    const tsIgnore = res.highIssues.find(i => i.rule === 'ts-no-ts-ignore' && i.file === file);
    expect(tsIgnore).toBeTruthy();
    expect(tsIgnore!.line).toBe(3);

    // Low: console.log at line 12
    const debugLog = res.lowIssues.find(i => i.rule === 'no-debug-logging' && i.file === file);
    expect(debugLog).toBeTruthy();
    expect(debugLog!.line).toBe(12);

    // Medium: any at line 13
    const anyType = res.mediumIssues.find(i => i.rule === 'ts-no-explicit-any' && i.file === file);
    expect(anyType).toBeTruthy();
    expect(anyType!.line).toBe(13);

    // High: FIXME at line 14
    const fixme = res.highIssues.find(i => i.rule === 'no-fixme' && i.file === file);
    expect(fixme).toBeTruthy();
    expect(fixme!.line).toBe(14);

    // Medium: process.env at line 27
    const envAccess = res.mediumIssues.find(i => i.rule === 'env-access' && i.file === file);
    expect(envAccess).toBeTruthy();
    expect(envAccess!.line).toBe(27);
  });
});

