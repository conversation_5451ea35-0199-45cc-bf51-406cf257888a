import { describe, test, expect } from 'bun:test';
import DanteOrchestrator from '../agents/DanteOrchestrator';

describe('Computer Use E2E Tests', () => {
  describe('Schema Validation', () => {
    test('computer use tool should have valid OpenAI-compatible schema', async () => {
      const computerUseTool = DanteOrchestrator.tools.find((tool: any) => tool.name === 'computer_use_dispatcher');
      expect(computerUseTool).toBeDefined();

      if (!computerUseTool) return;

      console.log('Computer Use Tool Schema:', JSON.stringify(computerUseTool, null, 2));

      // Validate schema structure
      expect((computerUseTool as any).type).toBe('function');
      // The tool is registered under the dispatcher name; assert the found name to be consistent
      expect((computerUseTool as any).name).toBe('computer_use_dispatcher');
      expect((computerUseTool as any).description).toBeDefined();
      expect((computerUseTool as any).parameters).toBeDefined();
      expect((computerUseTool as any).parameters?.type).toBe('object');
      // Be less strict: ensure 'action' is required, but allow other optional entries
      expect((computerUseTool as any).parameters?.required).toBeDefined();
      expect((computerUseTool as any).parameters?.required).toContain('action');
      expect((computerUseTool as any).parameters?.additionalProperties).toBe(false);

      // Check that sessionId and environmentType are NOT in required array
      const required = (computerUseTool as any).parameters?.required || [];
      expect(required).not.toContain('sessionId');
      expect(required).not.toContain('environmentType');
    });
  });
});
