import { test, expect } from 'bun:test';
import { searchCodeTool } from '../tools/fileOperations';
import { sanitizeToolOutput } from '../mcp/utils';

async function testFix() {
  console.log('🧪 Testing Search Tool Fix');
  console.log('=' .repeat(50));
  
  try {
    // Test the search tool directly with proper parameters
    console.log('Testing with direct execute...');
    
    const directResult = await (searchCodeTool as any).execute({
      pattern: 'gpt-4o',
      path: '/Users/<USER>/dante-gpt',
      fileTypes: ['ts', 'js'],
      caseSensitive: false
    });
    
    console.log('Direct result:', typeof directResult, directResult);
    
    // Also test with invoke
    console.log('\nTesting with invoke...');
    const result = await (searchCodeTool as any).invoke({
      pattern: 'gpt-4o',
      path: '/Users/<USER>/dante-gpt',
      fileTypes: ['ts', 'js'],
      caseSensitive: false
    });
    
    console.log('✅ Tool invocation succeeded');
    console.log('Result type:', typeof result);
    console.log('Result length:', result.length, 'characters');
    console.log('First 500 chars:', result.substring(0, 500));
    
    // Try to parse the result
    try {
      const parsed = JSON.parse(result);
      console.log('✅ Result is valid JSON');
      console.log('Success:', parsed.success);
      console.log('Matches:', parsed.matches);
      console.log('Has results:', parsed.results?.length > 0);
      
      if (parsed.results?.length > 0) {
        console.log('\n📋 Sample matches:');
        parsed.results.slice(0, 3).forEach((match: any, i: number) => {
          console.log(`  ${i + 1}. ${match.file?.split('/').pop()}:${match.line}`);
        });
        
        console.log('\n🎉 SUCCESS: Search tool is working correctly!');
        return true;
      } else {
        console.log('❌ No results found, but tool structure is correct');
        return false;
      }
      
    } catch (e) {
      console.log('❌ Result is not valid JSON:', e);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Tool invocation failed:', error);
    return false;
  }
}

// Wrap as a bun:test unit so running the test suite won't exit the process
test('Search Tool Fix - quickFixTest', async () => {
  const success = await testFix();
  expect(success).toBe(true);
}, 30000);
