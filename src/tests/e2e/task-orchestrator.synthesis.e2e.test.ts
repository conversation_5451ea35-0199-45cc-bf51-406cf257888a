import { expect, test } from "bun:test";
import { orchestrator } from "../../agents/DanteOrchestrator";
import { getSteps } from "../../utils/stepAggregator";

function hasAnyKey() {
  return Boolean(
    process.env.OPENAI_API_KEY ||
      process.env.GOOGLE_GENERATIVE_AI_API_KEY ||
      process.env.ANTHROPIC_API_KEY
  );
}

test(
  hasAnyKey()
    ? "task orchestrator: orchestrated_response with synthesis text fallback"
    : test.skip,
  async () => {
    // Force orchestration via context.files > 5
    const files = Array.from({ length: 6 }).map((_, i) => ({
      name: `file_${i}.md`,
      path: `docs/file_${i}.md`,
      type: "text/markdown",
      content: `# File ${i}\nThis is a small file for testing.`,
    }));

    const res: any = await orchestrator.planAndDelegate(
      "Provide a short analysis across the provided files.",
      {
        context: { files },
        maxSteps: 4,
        temperature: 0,
      }
    );

    expect(res).toBeTruthy();
    expect(res.type).toBe("orchestrated_response");
    // We attach synthesized text fallback at top-level `text` (for UI)
    expect(typeof res.text === "string").toBe(true);
    // runId present and usable with the step aggregator (may have 0+ steps depending on tool-calls)
    expect(typeof res.runId === "string").toBe(true);
    const recorded = getSteps(res.runId);
    expect(Array.isArray(recorded)).toBe(true);
    // stepResults present in delegated orchestration
    expect(Array.isArray(res.stepResults)).toBe(true);
  }
);
