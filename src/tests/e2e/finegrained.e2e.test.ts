import { describe, it, expect } from 'bun:test';
import { OrchestrationEngine } from '../../agents/orchestration/OrchestrationEngine';
import { AgentRegistry } from '../../agents/orchestration/agents/AgentRegistry';
import { ContextPackager } from '../../agents/orchestration/context/ContextPackager';
import {
  WorkCategory,
  OrchestrationRequest,
  AgentProfile,
  ProgressEventType,
  Assignment,
  ProgressEvent,
} from '../../agents/orchestration/types';
import { promises as fs } from 'fs';
import path from 'path';

function makeAgents(n: number): AgentProfile[] {
  const out: AgentProfile[] = [];
  for (let i = 0; i < n; i++) {
    out.push({
      id: `ag_${i + 1}`,
      name: `Agent ${i + 1}`,
      capabilities: ['typescript', 'jest'],
      maxTokenContext: 4096,
      concurrencyLimit: 1,
      categoryAffinity: [WorkCategory.Coding],
      historical: { successRate: 0.9 },
    });
  }
  return out;
}

describe('fine-grained orchestration e2e', () => {
  it('plans and executes with custom registry/executor; achieves high success rate', async () => {
    const runId = `test-run-${Date.now()}`;
    const engine = new OrchestrationEngine(runId);

    // Inject custom, fast packager and registry
    const registry = new AgentRegistry({ defaultAgents: makeAgents(3) });
    (engine as any).registry = registry;
    (engine as any).packager = new ContextPackager({
      tokenCap: 256,
      perFileHeaderLines: 20,
      perFileFooterLines: 10,
    });
    (engine as any).schedulerOptions = {
      perCategoryConcurrency: { [WorkCategory.Coding]: 3 },
      heartbeatIntervalMs: 5000,
      timeoutMsPerUnit: 10000,
    };

    // Custom executor: immediately yields started and done-like progress (scheduler also emits started/done)
    (engine as any).executor = (async function* (_assignment: Assignment): AsyncGenerator<ProgressEvent> {
      // Yield a minimal heartbeat to simulate activity; scheduler handles finalization
      yield {
        eventId: `evt_${Math.random().toString(36).slice(2, 8)}`,
        type: ProgressEventType.Heartbeat,
        workUnitId: _assignment.workUnitId,
        timestamp: Date.now(),
        percentComplete: 50,
      };
      return;
    }) as any;

    const request: OrchestrationRequest = {
      id: runId,
      objective: 'E2E: partition and execute over src/utils with fine-grained orchestration',
      scopeHints: {
        paths: ['src/utils'],
      },
      categoryMix: {
        coding: true,
        review: true,
      },
      constraints: {
        maxFilesPerUnit: 1, // ensure multiple coding units
        maxLOCPerUnit: 1000,
        concurrency: 3,
        tokenBudget: 2000,
      },
      flags: { fineGrainedOrchestration: true },
    };

    // Plan
    const plan = await engine.plan(request);
    const codingUnits = plan.units.filter((u) => u.category === WorkCategory.Coding);
    expect(codingUnits.length).toBeGreaterThanOrEqual(3);

    // Execute and drain events
    const started = new Set<string>();
    const done = new Set<string>();
    for await (const evt of engine.execute(plan, request)) {
      if (evt.type === ProgressEventType.Started) started.add(evt.workUnitId);
      if (evt.type === ProgressEventType.Done) done.add(evt.workUnitId);
    }

    // Validate multiple distinct units reported progress
    expect(started.size).toBeGreaterThanOrEqual(3);
    expect(done.size).toBeGreaterThanOrEqual(3);

    // Metrics
    const m = engine.metrics();
    expect(m.successRate).toBeGreaterThan(0.8);

    // Cleanup generated files
    const ledgerFile = path.join(process.cwd(), 'data', 'ledger', `${runId}.jsonl`);
    await fs.rm(ledgerFile, { force: true });
    const artifactsDir = path.join(process.cwd(), 'data', 'artifacts', runId);
    await fs.rm(artifactsDir, { recursive: true, force: true });
  });
});
