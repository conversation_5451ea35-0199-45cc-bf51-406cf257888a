import { expect, test } from "bun:test";
import { orchestrator } from "../../agents/DanteOrchestrator";

function hasAny<PERSON>ey() {
  return Boolean(
    process.env.OPENAI_API_KEY ||
      process.env.GOOGLE_GENERATIVE_AI_API_KEY ||
      process.env.ANTHROPIC_API_KEY
  );
}

test(hasAnyKey() ? "orchestrator: small request returns text" : test.skip, async () => {
  const steps: any[] = [];
  const res: any = await orchestrator.process("Reply with the word OK only.", {
    maxSteps: 2,
    temperature: 0,
    onStepFinish: (s) => steps.push(s),
  } as any);

  expect(res).toBeTruthy();
  // Non-stream path should include text
  expect(typeof res.text === "string").toBe(true);
  // Allow emptyish but present string; still capture usage or steps if available
  expect(res.agent).toBeTruthy();
  // Steps may not exist for trivial requests, but the field should be present when available
  if (res.steps) {
    expect(Array.isArray(res.steps)).toBe(true);
  }
  // Ensure our callback captured any steps while running
  expect(Array.isArray(steps)).toBe(true);
});

