import { describe, test, expect } from 'bun:test';
import { pdfExtractTool } from '../tools/pdfExtract';

const SAMPLE_PDF = 'uploads/1757963490637-389422180.pdf';

describe('pdf_extract tool', () => {
  test('reads PDFs when baseDir is uploads', async () => {
    const execute = (pdfExtractTool as any).execute;
    if (!execute) {
      throw new Error('pdfExtractTool.execute is not defined');
    }

    const resultString = await execute({
      filePath: SAMPLE_PDF,
      baseDir: 'uploads',
    });

    expect(typeof resultString).toBe('string');

    const result = JSON.parse(resultString);
    expect(result.file).toContain(SAMPLE_PDF);
    expect(result.numpages).toBeGreaterThan(0);
    expect(result.text.length).toBeGreaterThan(0);
  });

  test('allows file paths relative to the baseDir', async () => {
    const execute = (pdfExtractTool as any).execute;
    if (!execute) {
      throw new Error('pdfExtractTool.execute is not defined');
    }

    const relativePath = SAMPLE_PDF.replace(/^uploads\//, '');
    const resultString = await execute({
      filePath: relativePath,
      baseDir: 'uploads',
    });

    const result = JSON.parse(resultString);
    expect(result.file).toContain(SAMPLE_PDF);
    expect(result.text.length).toBeGreaterThan(0);
  });
});
