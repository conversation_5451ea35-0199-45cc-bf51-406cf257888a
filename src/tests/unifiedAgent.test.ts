import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { runDante, getDanteStatus, getModelRecommendation, orchestrator } from '../index';
import DanteOrchestrator from '../agents/DanteOrchestrator';

describe('Unified Dante Agent System', () => {
  describe('DanteOrchestrator', () => {
    test('should have correct properties', () => {
      expect(DanteOrchestrator.name).toBe('DanteOrchestrator');
      expect(DanteOrchestrator.model).toBeDefined();
    });
  });

  describe('Unified Orchestrator', () => {
    test('should provide a model recommendation', () => {
      const recommendation = getModelRecommendation(
        'Write a simple hello world script in Python',
        'coding',
        ['gpt-4o', 'gpt-5']
      );
      expect(recommendation.recommendedModel).toBeDefined();
      expect(recommendation.reason).toBeDefined();
      expect(recommendation.confidence).toBeGreaterThan(0);
    });

    test('should provide system status', () => {
      const status = orchestrator.getStatus();
      expect(status.totalRequestsProcessed).toBeGreaterThanOrEqual(0);
      expect(status.activeRequests).toBeGreaterThanOrEqual(0);
      expect(status.systemHealth).toMatch(/healthy|degraded|critical/);
    });
  });

  describe('Unified API Functions', () => {
    test('runDante should work', async () => {
      const result = await runDante('Hello, world!', { stream: false });
      expect(result).toBeDefined();
    });

    test('getDanteStatus should return comprehensive status', () => {
      const status = getDanteStatus();

      expect(status.agent).toBeDefined();
      expect(status.orchestrator).toBeDefined();
      expect(status.memory).toBeDefined();
      expect(status.mcp).toBeDefined();
    });
  });
});
