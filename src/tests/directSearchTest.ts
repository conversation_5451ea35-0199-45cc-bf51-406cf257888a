#!/usr/bin/env bun

/**
 * Direct search test bypassing the agent system to isolate the tool issue
 */

import * as fs from 'fs/promises';
import * as path from 'path';

async function directSearch() {
  console.log('🔍 Direct Search Test (Bypassing Tools)');
  console.log('=' .repeat(50));
  
  const searchPath = '/Users/<USER>/dante-gpt';
  const pattern = 'gpt-4o';
  const fileTypes = ['ts', 'js'];
  
  try {
    const results: Array<{ file: string; line: number; match: string }> = [];
    const regex = new RegExp(pattern, 'gi');
    
    const walk = async (dir: string): Promise<void> => {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.name.startsWith('.') || entry.name === 'node_modules') continue;
        
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await walk(fullPath);
        } else {
          const ext = path.extname(entry.name).slice(1);
          
          if (fileTypes.includes(ext)) {
            try {
              const content = await fs.readFile(fullPath, 'utf-8');
              const lines = content.split('\\n');
              
              lines.forEach((line, index) => {
                if (regex.test(line)) {
                  results.push({
                    file: fullPath,
                    line: index + 1,
                    match: line.trim(),
                  });
                }
              });
            } catch (error) {
              // Skip files that can't be read
              console.log(`Skipping ${fullPath}: ${error}`);
            }
          }
        }
      }
    };
    
    await walk(searchPath);
    
    console.log(`✅ Search completed`);
    console.log(`📊 Results: ${results.length} matches found`);
    
    if (results.length > 0) {
      console.log('\\n📋 First 10 matches:');
      results.slice(0, 10).forEach((result, i) => {
        console.log(`  ${i + 1}. ${result.file.split('/').pop()}:${result.line}`);
        console.log(`     ${result.match.substring(0, 80)}...`);
      });
      
      // Test the sanitization on this result
      const mockResult = {
        success: true,
        matches: results.length,
        results: results.slice(0, 10),
        truncated: results.length > 10
      };
      
      console.log('\\n🧹 Testing sanitizeToolOutput on real data:');
      const { sanitizeToolOutput } = await import('../mcp/utils');
      
      const originalJson = JSON.stringify(mockResult, null, 2);
      console.log(`Original JSON length: ${originalJson.length}`);
      
      const sanitized = sanitizeToolOutput(mockResult);
      console.log(`Sanitized length: ${sanitized.length}`);
      
      try {
        const parsed = JSON.parse(sanitized);
        console.log('✅ Sanitized result is valid JSON');
        console.log(`Preserved matches: ${parsed.matches}`);
        console.log(`Preserved results: ${parsed.results?.length || 0}`);
        
        return true;
      } catch (e) {
        console.log('❌ Sanitized result is corrupted:', e);
        console.log('Sanitized content:', sanitized);
        return false;
      }
      
    } else {
      console.log('❌ No matches found - this suggests the search pattern or path is wrong');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Direct search failed:', error);
    return false;
  }
}

async function main() {
  const success = await directSearch();
  console.log(`\\n🎯 Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
  process.exit(success ? 0 : 1);
}

main().catch(console.error);