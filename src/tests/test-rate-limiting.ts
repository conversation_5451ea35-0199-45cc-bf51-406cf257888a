#!/usr/bin/env bun

import {
  isRateLimited,
  updateRateLimit,
  getRateLimitDelay,
  shouldDelegateToOrchestrator,
  validateAndPruneMessages
} from '../utils/tokenLimiter';

console.log('🧪 Testing Enhanced Rate Limiting System\n');

// Test 1: Rate limiting functionality
console.log('1. Testing rate limit tracking...');
const testModel = 'gpt-5';

console.log(`Initial rate limit status for ${testModel}:`, isRateLimited(testModel));

// Simulate a large request that should trigger rate limiting
updateRateLimit(testModel, 28000);
console.log(`After 28k tokens:`, isRateLimited(testModel));

// Simulate another request that should block
updateRateLimit(testModel, 5000);
console.log(`After additional 5k tokens:`, isRateLimited(testModel));
console.log(`Rate limit delay:`, Math.round(getRateLimitDelay(testModel) / 1000), 'seconds');

// Test 2: Orchestrator delegation detection
console.log('\n2. Testing orchestrator delegation...');
const smallMessages = [
  { role: 'user', content: 'Hello, can you help me?' }
];

const largeMessages = [
  { role: 'user', content: 'A'.repeat(50000) } // Large message
];

console.log('Small message should delegate:', shouldDelegateToOrchestrator(smallMessages));
console.log('Large message should delegate:', shouldDelegateToOrchestrator(largeMessages));

// Test 3: Message pruning
console.log('\n3. Testing message pruning...');
const testMessages = [
  { role: 'system', content: 'You are a helpful assistant' },
  { role: 'user', content: 'B'.repeat(30000) }, // Very large content
  { role: 'assistant', content: 'I understand' },
  { role: 'user', content: 'Can you help with this?' }
];

const { messages, delegateToOrchestrator } = validateAndPruneMessages(
  testMessages,
  testModel,
  'Some memory context here'
);

console.log('Original messages:', testMessages.length);
console.log('Processed messages:', messages.length);
console.log('Should delegate to orchestrator:', delegateToOrchestrator);

console.log('\n✅ Rate limiting system tests completed');
