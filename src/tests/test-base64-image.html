<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Base64 Image Rendering</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
        }
        .test-message {
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .instructions {
            background: #e8f4ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 25px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.6;
        }
        code {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Base64 Image Rendering in Dante GPT</h1>
        <p class="description">Use this test message to verify that base64 images are rendering correctly in the chat interface.</p>
        
        <h2>Sample Message with Base64 Image:</h2>
        <div class="test-message">Got it — here's PEPE (USD) over the last 24 hours (UTC):

## PEPE Price Chart - 24 Hour Overview

The cryptocurrency PEPE has shown interesting price movements over the past 24 hours. Here's a detailed visualization:

Image: (data image — openable in most browsers/clients) data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC

### Key Observations:
- **Current Price**: $0.0000125
- **24h Change**: +5.2%
- **Volume**: $125M
- **Market Cap**: $5.2B

The chart above shows the price fluctuations throughout the day, with notable resistance at $0.0000130 and support at $0.0000120.

Here's another chart showing volume distribution:

Image: (data image — openable in most browsers/clients) data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==

Additional metrics and analysis are available upon request.</div>

        <div class="instructions">
            <h3>📋 Testing Instructions:</h3>
            <ol>
                <li>Copy the entire content from the test message box above</li>
                <li>Go to your Dante GPT interface at <code>http://localhost:3003/</code></li>
                <li>Paste the message as if it were a response from the AI</li>
                <li>Verify that:
                    <ul>
                        <li>The base64 images are rendered as actual images</li>
                        <li>Images have rounded corners and shadows</li>
                        <li>Clicking on images opens them in a new tab</li>
                        <li>The "Image: (data image...)" text is replaced with the actual image</li>
                        <li>Markdown formatting (headers, lists, bold text) is properly styled</li>
                    </ul>
                </li>
            </ol>
            
            <h3>🎨 Expected Features:</h3>
            <ul>
                <li><strong>Automatic Detection</strong>: The system automatically detects "Image: (data image...)" format</li>
                <li><strong>Click to Open</strong>: Base64 images open in a new tab when clicked</li>
                <li><strong>Hover Effects</strong>: Images scale slightly on hover with an "Open" button</li>
                <li><strong>Error Handling</strong>: Invalid images show an error message</li>
                <li><strong>Responsive Layout</strong>: Images scale to fit the chat bubble</li>
            </ul>
        </div>
    </div>
</body>
</html>