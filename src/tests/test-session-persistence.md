# Session Persistence Test Guide

## Features Implemented

### 1. Session Caching with IndexedDB

- **Location**: `src/utils/sessionManager.ts`
- Stores conversation messages in IndexedDB for persistence
- Fallback to localStorage if IndexedDB fails
- Auto-saves messages with 1-second debounce
- Survives hot reloads and browser refreshes

### 2. Conversation History UI

- **Location**: `src/ui/components/ConversationHistory.tsx`
- View all past conversations
- Switch between sessions
- Export sessions as JSON
- Import previously exported sessions
- Delete unwanted conversations
- Shows message count and last updated time

### 3. API Request Logging

- **Location**: Stored in IndexedDB alongside sessions
- Logs all API requests and responses
- Tracks request duration
- Records errors for debugging
- Exportable with session data

### 4. UI Integration

- **Updated Files**:
  - `src/ui/App.tsx` - Session initialization and management
  - `src/ui/components/Sidebar.tsx` - Added history button
  - `src/api/server.ts` - Already includes request/response logging

## How to Test

### Test 1: Basic Message Persistence

1. Open the app at <http://localhost:3003>
2. Send a message to <PERSON>
3. Wait for response
4. Refresh the page (Cmd+R or F5)
5. **Expected**: Messages should still be visible

### Test 2: Hot Reload Persistence

1. Send a message to Dante
2. Make a small change to any React component (e.g., add a comment)
3. Save the file (triggers Vite hot reload)
4. **Expected**: Conversation should persist through the hot reload

### Test 3: Conversation History

1. Click the sidebar menu button
2. Click "Conversation History" button
3. **Expected**: See current and past conversations
4. Try these actions:
   - Export a conversation (downloads JSON)
   - Start a new conversation
   - Switch between conversations
   - Delete a conversation

### Test 4: Self-Improvement Scenario

1. Ask Dante to modify his own code (e.g., "Add a new greeting to your capabilities")
2. When Dante saves the file, Vite will hot reload
3. **Expected**: The conversation continues without losing context
4. You can continue giving instructions without starting over

### Test 5: Import/Export

1. Export a conversation from history
2. Clear all data or use incognito mode
3. Import the previously exported conversation
4. **Expected**: Full conversation restored with all messages

## Technical Details

### Session Storage Structure

```typescript
{
  id: string,
  messages: [{
    role: string,
    content: string,
    timestamp: number,
    model?: string,
    agentEvents?: any[]
  }],
  createdAt: number,
  updatedAt: number,
  title?: string,
  metadata?: object
}
```

### API Log Structure

```typescript
{
  id: string,
  sessionId: string,
  request: any,
  response: any,
  timestamp: number,
  duration?: number,
  error?: any
}
```

## Benefits for Self-Improvement

1. **Continuous Context**: Dante can make multiple changes to his codebase without losing the conversation context
2. **Debugging**: API logs help understand what went wrong if issues occur
3. **Iteration**: Can refine and improve changes across multiple hot reloads
4. **History**: Can reference previous attempts and learn from them
5. **Collaboration**: Export/import allows sharing conversations for review

## Troubleshooting

If sessions aren't persisting:

1. Check browser console for IndexedDB errors
2. Ensure localStorage isn't disabled
3. Check that the session manager initialized (look for "Loading session..." on startup)
4. Verify the API server is running on port 3001

## Future Enhancements

Potential improvements:

- Search within conversation history
- Tag or categorize conversations
- Sync sessions across devices (requires backend)
- Auto-title conversations based on content
- Conversation templates for common tasks
