import { afterAll, expect, test } from 'bun:test';
import fs from 'fs/promises';
import path from 'path';
import { runFileAnalysisTool } from '../tools/fileAnalysis';

const tmpPath = path.join(process.cwd(), 'uploads', 'file-analysis-tool-test.txt');

afterAll(async () => {
  try {
    await fs.rm(tmpPath, { force: true });
  } catch {}
});

test('run_file_analysis provides structured file insights', async () => {
  await fs.mkdir(path.dirname(tmpPath), { recursive: true });
  await fs.writeFile(tmpPath, 'function greet(name) { return `hi ${name}`; }\n');

  const result = await (runFileAnalysisTool as any).execute({
    filePath: tmpPath,
    focusAreas: ['correctness', 'security'],
    instruction: 'Focus on potential runtime or security issues.',
  });

  expect(result?.success).toBe(true);
  expect(result?.path).toBe(tmpPath);
  expect(result?.analysis?.success).toBe(true);
  expect(typeof result?.analysis?.analysis).toBe('string');
  expect(Array.isArray(result?.analysis?.keyFindings)).toBe(true);
}, { timeout: 60000 });
