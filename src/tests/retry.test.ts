describe('server/utils/retry withRetries', () => {
  let timeoutSpy: any;
  let randomSpy: any;
  let dateNowSpy: any;
  let recordedDelays: number[] = [];
  let fakeNow = 0;

  beforeEach(() => {
    recordedDelays = [];
    fakeNow = 1_000_000; // large baseline

    // Configure env BEFORE first import (module reads at import time)
    process.env.RETRY_MAX_ATTEMPTS = '5';
    process.env.RETRY_BACKOFF_BASE_MS = '100'; // base backoff
    process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD = '2';
    process.env.CIRCUIT_BREAKER_WINDOW_MS = '60000';
    process.env.CIRCUIT_BREAKER_RESET_TIMEOUT_MS = '1000';

    // Spy Date.now used by circuit breaker timing
    dateNowSpy = spyOn(Date, 'now').mockImplementation(() => fakeNow);

    // Intercept setTimeout to record delays and advance fake time
    timeoutSpy = spyOn(globalThis as any, 'setTimeout').mockImplementation(((cb: (...args: any[]) => void, ms?: number) => {
      const delay = typeof ms === 'number' ? ms : 0;
      recordedDelays.push(delay);
      fakeNow += delay;
      cb();
      return 0 as any;
    }) as any);
  });

  afterEach(() => {
    timeoutSpy?.mockRestore();
    dateNowSpy?.mockRestore();
    if (randomSpy) {
      randomSpy.mockRestore();
      randomSpy = undefined;
    }

    delete process.env.RETRY_MAX_ATTEMPTS;
    delete process.env.RETRY_BACKOFF_BASE_MS;
    delete process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD;
    delete process.env.CIRCUIT_BREAKER_WINDOW_MS;
    delete process.env.CIRCUIT_BREAKER_RESET_TIMEOUT_MS;
  });

  it('calculates exponential backoff with full jitter correctly', async () => {
    // Deterministic jitter: delay = rand(0, base * 2^attempt); set rand=1 -> delay=base*2^attempt
    randomSpy = spyOn(Math, 'random').mockReturnValue(1);

    const { withRetries } = await import('../../server/utils/retry');

    let attempts = 0;
    const op = async () => {
      attempts++;
      if (attempts === 1) {
        const err: any = new Error('Server error');
        err.response = { status: 500 };
        throw err;
      }
      return 'ok';
    };

    const wrapped = withRetries(op, 'model-x');

    const result = await wrapped();
    expect(result).toBe('ok');

    // One failure => one sleep; attempt index 0 => delay = 100 * 2^0 = 100ms
    expect(recordedDelays.length).toBe(1);
    expect(Math.round(recordedDelays[0])).toBe(100);
  });

  it('respects Retry-After header (seconds) over backoff', async () => {
    // Even if random != 0, Retry-After should take precedence
    randomSpy = spyOn(Math, 'random').mockReturnValue(0.9);

    const { withRetries } = await import('../../server/utils/retry');

    let attempts = 0;
    const op = async () => {
      attempts++;
      if (attempts === 1) {
        const err: any = new Error('Rate limited');
        err.response = { status: 429, headers: { 'retry-after': '2' } }; // 2 seconds
        throw err;
      }
      return 'ok';
    };

    const wrapped = withRetries(op, 'model-y');

    const result = await wrapped();
    expect(result).toBe('ok');

    // Should sleep exactly 2000ms from Retry-After, ignoring backoff
    expect(recordedDelays.length).toBe(1);
    expect(Math.round(recordedDelays[0])).toBe(2000);
  });

  it('opens circuit after failures and closes after cooldown', async () => {
    randomSpy = spyOn(Math, 'random').mockReturnValue(0.5);

    const { withRetries } = await import('../../server/utils/retry');

    // Operation that always fails with retryable 500
    const failOp = async () => {
      const err: any = new Error('Server error');
      err.response = { status: 500 };
      throw err;
    };

    const failing = withRetries(failOp, 'model-z');

    // First call should fail and open circuit after reaching failure threshold (2)
    await expect(failing()).rejects.toThrow(); // error message variant depends on when it opens

    // Next immediate call should be blocked by OPEN circuit
    await expect(failing()).rejects.toThrow('Circuit breaker is open. Operation will not be attempted.');

    // Advance time beyond reset timeout to transition to HALF_OPEN
    fakeNow += 1001;

    // Next call in HALF_OPEN should allow one trial; make it succeed to close breaker
    let trialCount = 0;
    const succeedOnce = async () => {
      trialCount++;
      return 'recovered';
    };
    const halfOpenTrial = withRetries(succeedOnce, 'model-z');

    const result = await halfOpenTrial();
    expect(result).toBe('recovered');

    // After success in HALF_OPEN, breaker should be CLOSED; subsequent calls succeed normally
    const result2 = await halfOpenTrial();
    expect(result2).toBe('recovered');
  });
});
