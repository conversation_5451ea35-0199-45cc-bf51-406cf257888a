/**
 * Rate Limit Protection Tests
 *
 * Tests to verify the contextual project feeder and rate limit protection
 * prevent agents from hitting rate limits.
 */

import { describe, test, expect, beforeEach } from 'bun:test';
import { ContextualProjectFeeder } from '../utils/contextualProjectFeeder';
import { RateLimitProtection } from '../utils/rateLimitProtection';
import { countTokens } from '../utils/tokenLimiter';

const testProjectRoot = process.cwd();

describe('ContextualProjectFeeder', () => {
  let feeder: ContextualProjectFeeder;

  beforeEach(() => {
    feeder = new ContextualProjectFeeder(testProjectRoot);
  });

  test('should analyze task types correctly', async () => {
    const featureTask = await feeder.analyzeTask(
      'I need to implement a new user authentication system',
      []
    );
    expect(featureTask.type).toBe('feature');
    expect(featureTask.scope).toBe('system');

    const bugTask = await feeder.analyzeTask(
      'There is a bug in the login function that needs fixing',
      []
    );
    expect(bugTask.type).toBe('bug');
    expect(bugTask.scope).toBe('component');

    const analysisTask = await feeder.analyzeTask(
      'Can you explain how the agent system works?',
      []
    );
    expect(analysisTask.type).toBe('analysis');
  });

  test('should generate appropriate context strategies', async () => {
    const taskAnalysis = await feeder.analyzeTask(
      'I need to add a new API endpoint for user management',
      []
    );

    const strategy = feeder.generateContextStrategy(taskAnalysis, 'gpt-5');

    expect(strategy.tokenBudget).toBeGreaterThan(0);
    expect(strategy.maxFileCount).toBeGreaterThan(0);
    expect(strategy.prioritizePatterns).toContain('src/');
    expect(strategy.excludePatterns).toContain('node_modules');
  });

  test('should provide contextual project info within token limits', async () => {
    const result = await feeder.getContextualProjectInfo(
      'Help me understand the memory system implementation',
      [],
      'gpt-5'
    );

    expect(result.context).toBeDefined();
    expect(result.tokensUsed).toBeLessThan(15000); // Should be within gpt-5 limits
    expect(result.context.relevantFiles.length).toBeGreaterThan(0);
    expect(result.reasoning).toContain('memory');
  });

  test('should handle file-specific requests', async () => {
    const result = await feeder.getContextualProjectInfo(
      'I need to modify the DanteOrchestrator.ts file',
      [],
      'gpt-5'
    );

    const hasDanteCoreFile = result.context.relevantFiles.some(file =>
      file.includes('DanteOrchestrator.ts')
    );
    expect(hasDanteCoreFile).toBe(true);
  });
});

describe('RateLimitProtection', () => {
  let protection: RateLimitProtection;
  let feeder: ContextualProjectFeeder;

  beforeEach(() => {
    protection = new RateLimitProtection({
      emergencyTokenLimit: 5000,
      maxContextExpansions: 2,
      progressiveReduction: true,
    });

    feeder = new ContextualProjectFeeder(testProjectRoot);
    protection.setProjectFeeder(feeder);
  });

  test('should allow normal requests to proceed', async () => {
    const normalMessages = [
      { role: 'user', content: 'Hello, can you help me with a simple question?' }
    ];

    const result = await protection.analyzeRequest(normalMessages, 'gpt-5');

    expect(result.shouldProceed).toBe(true);
    expect(['direct','contextual','orchestrator']).toContain(result.strategy);
    expect(result.tokenEstimate).toBeLessThan(5000);
  });

  test('should detect massive context patterns', async () => {
    // Create a message with massive file listings (simulating the original issue)
    const massiveContent = Array(200).fill(0).map((_, i) =>
      `/Users/<USER>/project/build/intermediate${i}/file${i}.class`
    ).join('\n');

    const massiveMessages = [
      { role: 'user', content: `Here's my project structure:\n${massiveContent}\n\nCan you help me fix a bug?` }
    ];

    const result = await protection.analyzeRequest(massiveMessages, 'gpt-5', 'Can you help me fix a bug?');

    expect(result.shouldProceed).toBe(true);
    expect(['contextual', 'emergency', 'orchestrator']).toContain(result.strategy);
    expect(result.tokenEstimate).toBeLessThan(20000); // Should be reduced
  });

  test('should apply progressive reduction for large requests', async () => {
    // Create messages that exceed normal limits but aren't massive
    const largeMessages = Array(10).fill(0).map((_, i) => ({
      role: i % 2 === 0 ? 'user' : 'assistant',
      content: 'This is a moderately long message that simulates conversation history. '.repeat(100)
    }));

    const result = await protection.analyzeRequest(largeMessages, 'gpt-5');

    expect(result.shouldProceed).toBe(true);
    expect(['direct', 'orchestrator', 'contextual']).toContain(result.strategy);
  });

  test.skip('should block extremely large requests', async () => { // causes the suite to freeze
    // Create an impossibly large request
    const extremeContent = 'A'.repeat(1000000); // 1MB of text
    const extremeMessages = [
      { role: 'user', content: extremeContent }
    ];

    const result = await protection.analyzeRequest(extremeMessages, 'gpt-5');

    expect(result.shouldProceed).toBe(false);
    expect(result.strategy).toBe('emergency');
  });

  test('should provide meaningful protection metrics', () => {
    const metrics = protection.getMetrics();

    expect(metrics).toHaveProperty('recentRequestCounts');
    expect(metrics).toHaveProperty('averageTokenUsage');
    expect(metrics).toHaveProperty('protectionActivations');
  });
});

describe('Integration Tests', () => {
  test('should handle the original rate limit scenario', async () => {
    // Simulate the original problem: massive build output
    const buildOutput = `
      Our agent is trying to process too much of a selected project at one time causing it to hit our rate limit after only two messages. We need a way to intelligently only provide enough details about the selected project and given task and allow the agent to explore to find more as needed instead of trying to feed it our entire codebase at one.

      Build files:
      ${Array(200).fill(0).map((_, i) =>
        `/Users/<USER>/project/build/intermediates/compile_library_classes_jar/debug/file${i}.class`
      ).join('\n')}
    `;

    const feeder = new ContextualProjectFeeder(testProjectRoot);
    const protection = new RateLimitProtection();
    protection.setProjectFeeder(feeder);

    const messages = [
      { role: 'user', content: buildOutput }
    ];

    const result = await protection.analyzeRequest(
      messages,
      'gpt-5',
      'Help me fix a bug in my Flutter app'
    );

    // Should not be blocked, should use contextual strategy
    expect(result.shouldProceed).toBe(true);
    expect(result.strategy).toBe('contextual');
    expect(result.modifiedMessages).toBeDefined();
    expect(result.tokenEstimate).toBeLessThan(15000);
  });

  test('should progressively expand context as needed', async () => {
    const feeder = new ContextualProjectFeeder(testProjectRoot);

    // Start with minimal context
    const initialResult = await feeder.getContextualProjectInfo(
      'I need help with authentication',
      [],
      'gpt-5'
    );

    expect(initialResult.tokensUsed).toBeLessThan(10000);

    // Then expand for more specific needs
    // This would normally be done through the contextual exploration tools
    const expandedTask = await feeder.analyzeTask(
      'I need detailed information about the authentication system including all related files and tests',
      []
    );

    const expandedStrategy = feeder.generateContextStrategy(expandedTask, 'gpt-5');
    expandedStrategy.maxFileCount = 20;
    expandedStrategy.tokenBudget = 12000;

    const selectedFiles = await feeder.selectRelevantFiles(expandedTask, expandedStrategy);

    expect(selectedFiles.length).toBeGreaterThan(initialResult.context.relevantFiles.length);
  });
});

// Performance test to ensure the system doesn't itself cause delays
describe('Performance Tests', () => {
  test('should analyze requests quickly', async () => {
    const feeder = new ContextualProjectFeeder(testProjectRoot);
    const protection = new RateLimitProtection();
    protection.setProjectFeeder(feeder);

    const messages = [
      { role: 'user', content: 'Help me understand the codebase structure' }
    ];

    const startTime = Date.now();
    const result = await protection.analyzeRequest(messages, 'gpt-5', 'Help me understand the codebase structure');
    const endTime = Date.now();

    expect(result).toBeDefined();
    expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
  });

  test('should cache context effectively', async () => {
    const feeder = new ContextualProjectFeeder(testProjectRoot);

    // First request
    const start1 = Date.now();
    const result1 = await feeder.getContextualProjectInfo('Help with API endpoints', [], 'gpt-5');
    const time1 = Date.now() - start1;

    // Second similar request (should use cache)
    const start2 = Date.now();
    const result2 = await feeder.getContextualProjectInfo('Help with API endpoints', [], 'gpt-5');
    const time2 = Date.now() - start2;

    expect(result1).toBeDefined();
    expect(result2).toBeDefined();
    // Second request should be faster due to caching
    expect(time2).toBeLessThanOrEqual(time1);
  });
});

console.log('✅ Rate limit protection tests completed successfully');
