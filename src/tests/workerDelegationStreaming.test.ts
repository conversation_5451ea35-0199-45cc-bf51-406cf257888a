import { describe, test, expect } from 'bun:test';
import { ModelOrchestrator } from '../agents/ModelOrchestrator';

function makeVercelTextStream(chunks: string[]) {
  return {
    textStream: {
      async *[Symbol.asyncIterator]() {
        for (const c of chunks) {
          // tiny delay to simulate async streaming
          await new Promise((r) => setTimeout(r, 1));
          yield c;
        }
      },
    },
    completed: Promise.resolve(),
    state: { output: [{ content: chunks.join('') }] },
  } as any;
}

describe('ModelOrchestrator worker delegation streaming', () => {
  test('passes through Vercel AI textStream and emits agent selection', async () => {
    const orchestrator = new ModelOrchestrator();

    // Inject a mock worker into the private registry to avoid network calls
    const mockWorker = {
      execute: async (_input: any, _opts: any) => makeVercelTextStream(['Hello', ' ', 'world']),
    };

    (orchestrator as any).workerRegistry = new Map<string, any>([['research', mockWorker]]);

    const onAgentSelectedCalls: Array<{ agent: string; confidence: number }> = [];

    const result = await (orchestrator as any).processWorkerDelegation(
      'What\'s new in tech news today?',
      'research',
      { stream: true, onAgentSelected: (p: any) => onAgentSelectedCalls.push(p) },
      'req_test_1'
    );

    // Should be a Vercel AI StreamTextResult-like object
    expect(result).toBeDefined();
    expect(result.textStream).toBeDefined();
    expect(typeof result.textStream[Symbol.asyncIterator]).toBe('function');

    // Consume the stream and verify content
    let collected = '';
    for await (const delta of result.textStream) {
      collected += String(delta);
    }
    expect(collected).toBe('Hello world');

    // Verify UI handoff notification was emitted
    expect(onAgentSelectedCalls.length).toBeGreaterThan(0);
    expect(onAgentSelectedCalls[0].agent).toBe('Research Agent');
  });
});

