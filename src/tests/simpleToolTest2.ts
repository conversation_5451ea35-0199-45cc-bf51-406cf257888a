#!/usr/bin/env bun

import { searchCodeTool } from '../tools/fileOperations';
import { sanitizeToolOutput } from '../mcp/utils';

async function main() {
  console.log('🧪 Direct Tool Test');
  console.log('='.repeat(50));
  
  console.log('🔍 Testing searchCodeTool...');
  
  try {
    const result = await (searchCodeTool as any).invoke({
      pattern: 'gpt-4o',
      path: '/Users/<USER>/dante-gpt',
      fileTypes: ['ts', 'js'],
      caseSensitive: false
    });
    
    console.log('\n📊 Tool Result Analysis:');
    console.log('Type:', typeof result);
    console.log('Content (first 200 chars):', JSON.stringify(result).substring(0, 200));
    
    // Handle string results (common in agent tools)
    let parsedResult = result;
    if (typeof result === 'string') {
      try {
        parsedResult = JSON.parse(result);
        console.log('✅ Parsed string result successfully');
      } catch (e) {
        console.log('❌ String result is not JSON');
        console.log('Raw result:', result);
        return;
      }
    }
    
    console.log('\n🎯 Key Metrics:');
    console.log('Success:', parsedResult.success);
    console.log('Matches found:', parsedResult.matches);
    console.log('Results truncated:', parsedResult.truncated);
    console.log('Result entries:', parsedResult.results?.length);
    
    if (parsedResult.results?.length > 0) {
      console.log('\n📋 Sample matches:');
      parsedResult.results.slice(0, 5).forEach((match: any, i: number) => {
        console.log(`  ${i + 1}. ${match.file?.split('/').pop() || 'unknown'}:${match.line} - ${match.match?.substring(0, 60)}`);
      });
    }
    
    // Test the truncation system
    console.log('\n🔪 Sanitization Test:');
    const originalJson = JSON.stringify(parsedResult, null, 2);
    console.log('Original JSON size:', originalJson.length, 'chars');
    
    const sanitized = sanitizeToolOutput(parsedResult);
    console.log('Sanitized size:', sanitized.length, 'chars');
    console.log('Reduction:', ((originalJson.length - sanitized.length) / originalJson.length * 100).toFixed(1) + '%');
    
    // Check if sanitized result is still valid
    try {
      const reParsed = JSON.parse(sanitized);
      console.log('✅ Sanitized result is valid JSON');
      console.log('Data preserved - success:', reParsed.success === parsedResult.success);
      console.log('Data preserved - matches:', reParsed.matches === parsedResult.matches);
      
      if (parsedResult.matches > 0 && (!reParsed.results || reParsed.results.length === 0)) {
        console.log('⚠️  WARNING: Search found matches but sanitized result lost them!');
      }
      
    } catch (e) {
      console.log('❌ Sanitized result is corrupted JSON');
      console.log('First 500 chars of sanitized:', sanitized.substring(0, 500));
    }
    
  } catch (error) {
    console.error('❌ Tool invocation failed:', error);
  }
}

main().catch(console.error);