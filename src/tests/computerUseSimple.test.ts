describe('Computer Use Simple API Test', () => {
  test('Test computer use via API to see response sizes', async () => {
    console.log('🔍 Testing computer use via API...');
    
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Use computer_use tool to visit https://music.youtube.com and tell me what you see on the page. Keep your analysis very brief - just the main elements visible.'
          }
        ]
      })
    });

    if (!response.ok) {
      console.error('❌ API request failed:', response.status, response.statusText);
      return;
    }

    const responseText = await response.text();
    console.log('📊 Full response size:', responseText.length, 'characters');
    console.log('📄 Response preview (first 1000 chars):', responseText.substring(0, 1000));
    
    // Try to parse as JSON to see if it's structured
    try {
      const responseData = JSON.parse(responseText);
      console.log('✅ Response is valid JSON');
      if (responseData.message) {
        console.log('📝 Message length:', responseData.message.length);
      }
    } catch (e) {
      console.log('📄 Response is plain text, not JSON');
    }
  }, 60000);
});