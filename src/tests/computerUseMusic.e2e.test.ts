import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { browserUseOpenAndWaitTool, browserUseClickTool, browserUseGetStateTool } from '../tools/browserUseTools';
import { setupTestMCPManager, stopTestMCPManager } from './utils/testMCPManager';

describe('Computer Use Agent E2E', () => {
  beforeAll(async () => {
    await setupTestMCPManager();
  });

  afterAll(async () => {
    await stopTestMCPManager();
  });

  test('should open music.youtube.com and play the first song', async () => {
    // Open and wait for readiness
    if (!browserUseOpenAndWaitTool.execute) {
      throw new Error('browserUseOpenAndWaitTool.execute is not defined');
    }
    const openResult = await browserUseOpenAndWaitTool.execute({
      url: 'https://music.youtube.com',
      new_tab: false,
      include_screenshot: false,
      max_wait_ms: 90000,
      poll_ms: 1000,
      settle_ms: 1000,
      retries: 3,
      require_url_contains: 'music.youtube.com',
    }, {} as any);
    const openData = JSON.parse(openResult as string);
    expect(openData.success).toBe(true);
    expect(openData.ready).toBe(true);

    // Get current state (quick)
    if (!browserUseGetStateTool.execute) {
      throw new Error('browserUseGetStateTool.execute is not defined');
    }
    const stateResult = await browserUseGetStateTool.execute({
      wait_until_ready: true,
      include_screenshot: false,
      max_wait_ms: 30000,
      poll_ms: 1000,
    }, {} as any);
    const stateData = JSON.parse(stateResult as string);
    expect(stateData.success).toBe(true);

    // Extract interactive elements robustly
    const toText = (val: any): string => {
      if (typeof val === 'string') return val;
      try {
        if (val && Array.isArray(val.content)) {
          const t = val.content.find((i: any) => i?.type === 'text');
          return typeof t?.text === 'string' ? t.text : JSON.stringify(val);
        }
      } catch {}
      try { return JSON.stringify(val); } catch { return String(val); }
    };

    const tryParseInteractive = (state: any): any[] => {
      if (state?.interactive_elements) return state.interactive_elements;
      const text = toText(state);
      try {
        const obj = JSON.parse(text);
        return obj?.interactive_elements || [];
      } catch {
        return [];
      }
    };

    const elements = tryParseInteractive(stateData.state);
    expect(Array.isArray(elements)).toBe(true);
    expect(elements.length).toBeGreaterThan(0);

    // Find the first playable element
    const playable = elements.find(
      (el: any) => el.role === 'link' && /play/i.test(el.aria_label || el.title || '')
    ) || elements[0];
    expect(playable).toBeDefined();

    // Click the element
    if (!browserUseClickTool.execute) {
      throw new Error('browserUseClickTool.execute is not defined');
    }
    const clickResult = await browserUseClickTool.execute({
      index: playable.index ?? 0,
      new_tab: false,
    }, {} as any);
    const clickData = JSON.parse(clickResult as string);
    expect(clickData.success).toBe(true);

  }, 120000); // 120 second timeout for E2E test
  });

