import { describe, test, expect } from 'bun:test';
import type { Response } from 'express';
import { EnhancedStreamHandler } from '../utils/enhancedStreamHandler';
import { sessionStreams } from '../utils/sessionStreams';
import { interruptBus } from '../utils/interruptBus';

class MockRes implements Partial<Response> {
  public writes: string[] = [];
  public headers: Record<string, any> = {};
  public ended = false;
  setHeader(name: string, value: any) { this.headers[name] = value; }
  write(chunk: any) { this.writes.push(String(chunk)); return true as any; }
  end() { this.ended = true; return this as any; }
  flush() { /* no-op */ }
}

function mkStream(delayMs: number = 50) {
  return {
    async *[Symbol.asyncIterator]() {
      // Slow stream that would normally produce text later
      await new Promise((r) => setTimeout(r, delayMs));
      yield { type: 'text', content: 'late text' };
    },
    completed: Promise.resolve(),
  } as any;
}

describe('Interruption stops SSE quickly', () => {
  test('cancel ends stream within 200ms', async () => {
    const res = new MockRes();
    const sessionId = `sess_${Math.random().toString(36).slice(2,8)}`;
    const handler = new EnhancedStreamHandler(res as unknown as Response, { sessionId });
    sessionStreams.register(sessionId, handler);

    const started = Date.now();
    // Kick off stream handling asynchronously and do not await immediately
    const p = handler.handleAgentStream(mkStream(150), { finalize: false });

    // Fire cancel right away
    interruptBus.handle({ event: 'agent.interruption', type: 'cancel', sessionId, timestamp: new Date().toISOString() });

    // Allow microtasks to run
    await new Promise((r) => setTimeout(r, 50));

    const elapsed = Date.now() - started;

    // Should have ended fast due to quickComplete
    expect(res.ended).toBe(true);
    expect(elapsed).toBeLessThan(200);

    // Cleanup
    sessionStreams.unregister(sessionId);
    try { await p; } catch { /* ignore aborted */ }
  });
});
