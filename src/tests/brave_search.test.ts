import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';
import { ToolCallOptions } from 'ai';

mock.module('../tools/webSearchHelpers', () => ({
  searchBrave: mock(async () => []),
  aggregateResults: mock((results: any) => [].concat(...results)),
  fetchWebContent: mock(async () => ({ success: true, markdown: 'mock content' })),
}));

import { webResearchTool } from '../tools/webResearch';
import * as webSearchHelpers from '../tools/webSearchHelpers';

const mockedSearchBrave = webSearchHelpers.searchBrave as any;

const mockToolCallOptions = {
  toolCallId: 'test-id',
  messages: [],
} as ToolCallOptions;

describe('webResearchTool with Brave Search', () => {
  beforeEach(() => {
    mockedSearchBrave.mockClear();
    process.env.BRAVE_API_KEY = 'test-key';
  });

  afterEach(() => {
    delete process.env.BRAVE_API_KEY;
  });

  it('should call searchBrave and return formatted results', async () => {
    mockedSearchBrave.mockResolvedValue([
      {
        title: 'Test Title',
        url: 'https://example.com',
        snippet: 'Test snippet',
        source: 'Brave Search',
      },
    ]);

    const input = {
      query: 'test query',
      depth: 1,
      fetchContent: false,
    };

    const resultString = await webResearchTool.execute!(input, mockToolCallOptions);
    const result = JSON.parse(resultString as string);

    expect(mockedSearchBrave).toHaveBeenCalledWith('test query', 'test-key');
    expect(result.success).toBe(true);
    expect(result.sourcesCount).toBe(1);
    expect(result.sources[0].title).toBe('Test Title');
  });

  it('should handle no search results gracefully', async () => {
    mockedSearchBrave.mockResolvedValue([]);

    const input = {
      query: 'empty query',
    };

    const resultString = await webResearchTool.execute!(input, mockToolCallOptions);
    const result = JSON.parse(resultString as string);

    expect(mockedSearchBrave).toHaveBeenCalledWith('empty query', 'test-key');
    expect(result.success).toBe(true);
    expect(result.summary).toBe('');
    expect(result.sourcesCount).toBe(0);
  });

  it('should handle Brave API errors', async () => {
    mockedSearchBrave.mockRejectedValue(new Error('Brave API is down'));

    const input = {
      query: 'error query',
    };

    const resultString = await webResearchTool.execute!(input, mockToolCallOptions);
    const result = JSON.parse(resultString as string);

    expect(result.success).toBe(false);
    expect(result.error).toContain('Research failed: Brave API is down');
  });

  it('should not call searchBrave if API key is missing', async () => {
    delete process.env.BRAVE_API_KEY;
    mockedSearchBrave.mockResolvedValue([]);

    const input = {
      query: 'no api key',
    };

    const resultString = await webResearchTool.execute!(input, mockToolCallOptions);
    const result = JSON.parse(resultString as string);

    expect(mockedSearchBrave).toHaveBeenCalledWith('no api key', undefined);
    expect(result.success).toBe(true);
    expect(result.sourcesCount).toBe(0);
  });
});
