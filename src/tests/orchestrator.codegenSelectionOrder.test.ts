import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { orchestrator } from '../agents/DanteOrchestrator';

type Availability = Record<string, boolean>;

const ORIGINALS = {
  isModelAvailable: (orchestrator as any).isModelAvailable as (k: string) => boolean,
  doGenerate: (orchestrator as any).doGenerate as (k: string, cfg: any, stream: boolean) => Promise<any>,
};

function setAvailability(avail: Availability) {
  (orchestrator as any).isModelAvailable = (key: string) => {
    const norm = key.startsWith('models/') ? key.slice('models/'.length) : key;
    return !!avail[norm];
  };
}

function stubDoGenerateCaptureOrder(order: string[]) {
  (orchestrator as any).doGenerate = async (modelKey: string, _cfg: any, _stream: boolean) => {
    order.push(modelKey);
    const err: any = new Error('rate limit'); // triggers orchestrator fallback
    err.status = 429;
    throw err;
  };
}

function restorePatches() {
  (orchestrator as any).isModelAvailable = ORIGINALS.isModelAvailable;
  (orchestrator as any).doGenerate = ORIGINALS.doGenerate;
}

describe('Orchestrator CodeGeneration selection/fallback order', () => {
  beforeEach(() => {
    // no-op
  });
  afterEach(() => {
    restorePatches();
  });

  it('uses order: gpt-5 → gemini-2.5-flash → gemini-2.5-pro → gpt-4o-mini (all available)', async () => {
    const attempts: string[] = [];
    setAvailability({
      'gpt-5': true,
      'gemini-2.5-flash': true,
      'gemini-2.5-pro': true,
      'gpt-4o-mini': true,
    });
    stubDoGenerateCaptureOrder(attempts);

    try {
      await (orchestrator as any).executeWithAgent('CodeGenerationAgent', 'dummy codegen task', { stream: false, maxSteps: 1 });
    } catch {
      // expected: we stubbed doGenerate to always throw to force fallbacks
    }

    expect(attempts).toEqual(['gpt-5', 'gemini-2.5-flash', 'gemini-2.5-pro', 'gpt-4o-mini']);
  });

  it('skips Google when unavailable: gpt-5 → gpt-4o-mini', async () => {
    const attempts: string[] = [];
    setAvailability({
      'gpt-5': true,
      'gemini-2.5-flash': false,
      'gemini-2.5-pro': false,
      'gpt-4o-mini': true,
    });
    stubDoGenerateCaptureOrder(attempts);

    try {
      await (orchestrator as any).executeWithAgent('CodeGenerationAgent', 'dummy codegen task', { stream: false, maxSteps: 1 });
    } catch {}

    expect(attempts).toEqual(['gpt-5', 'gpt-4o-mini']);
  });

  it('starts with Gemini when OpenAI unavailable: gemini-2.5-flash → gemini-2.5-pro', async () => {
    const attempts: string[] = [];
    setAvailability({
      'gpt-5': false,
      'gpt-4o-mini': false,
      'gemini-2.5-flash': true,
      'gemini-2.5-pro': true,
    });
    stubDoGenerateCaptureOrder(attempts);

    try {
      await (orchestrator as any).executeWithAgent('CodeGenerationAgent', 'dummy codegen task', { stream: false, maxSteps: 1 });
    } catch {}

    expect(attempts.slice(0, 2)).toEqual(['gemini-2.5-flash', 'gemini-2.5-pro']);
  });
});
