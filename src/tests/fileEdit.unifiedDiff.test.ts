import { describe, test, expect } from 'bun:test';
import { promises as fs } from 'fs';
import os from 'os';
import path from 'path';
import { fileEditTool } from '../tools/fileOperations';

function tmpFile(name: string) {
  const id = `${Date.now()}-${Math.random().toString(36).slice(2)}`;
  return path.join(os.tmpdir(), `file-edit-udiff-${id}-${name}`);
}

describe('file_edit unified diff', () => {
  test('applies single-file unified diff', async () => {
    const filePath = tmpFile('udiff1.txt');
    await fs.writeFile(filePath, 'hello\nworld\n', 'utf-8');
    const rel = filePath; // use absolute path; parser strips a/ b/
    const diff = `--- a/${rel}\n+++ b/${rel}\n@@ -1,2 +1,2 @@\n hello\n-world\n+WORLD\n`;
    const res = await (fileEditTool as any).execute({ operation: 'patch', patchFormat: 'unified_diff', diff, diagnostics: false, fuzzy: true });
    if (!res.success) {
      console.error('Unified diff single-file result:', res);
    }
    expect(res.success).toBe(true);
    expect(res.operation).toBe('patch');
    expect(res.patchFormat).toBe('unified_diff');
    const content = await fs.readFile(filePath, 'utf-8');
    expect(content.includes('WORLD')).toBe(true);
    await fs.unlink(filePath).catch(() => {});
  });

  test('applies multi-file unified diff and returns batch', async () => {
    const a = tmpFile('a.txt');
    const b = tmpFile('b.txt');
    await fs.writeFile(a, 'foo\nbar\n', 'utf-8');
    await fs.writeFile(b, 'one\ntwo\n', 'utf-8');
    const diff = `--- a/${a}\n+++ b/${a}\n@@ -1,2 +1,2 @@\n foo\n-bar\n+BAR\n\n--- a/${b}\n+++ b/${b}\n@@ -1,2 +1,2 @@\n one\n-two\n+TWO\n`;
    const res = await (fileEditTool as any).execute({ operation: 'patch', patchFormat: 'unified_diff', diff, diagnostics: false, fuzzy: true });
    if (!res.success) {
      console.error('Unified diff multi-file result:', res);
    }
    expect(res.success).toBe(true);
    expect(res.batch).toBe(true);
    const aContent = await fs.readFile(a, 'utf-8');
    const bContent = await fs.readFile(b, 'utf-8');
    expect(aContent.includes('BAR')).toBe(true);
    expect(bContent.includes('TWO')).toBe(true);
    await fs.unlink(a).catch(() => {});
    await fs.unlink(b).catch(() => {});
  });
});
