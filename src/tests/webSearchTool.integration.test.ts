import { describe, test, expect, beforeEach, afterEach } from 'bun:test';
import { webSearchTool, executeWebSearch } from '../tools/webSearch';
import { webContentChunkTool } from '../tools/webContentChunk';

const originalConsoleError = console.error;

describe('WebSearchTool Integration Tests', () => {
  beforeEach(() => {
    // Suppress expected error logs in tests
    console.error = () => {};
  });

  afterEach(() => {
    console.error = originalConsoleError;
  });

  describe('Basic Search Functionality', () => {
    test('should return search results without content fetching', async () => {
      const result = await executeWebSearch({
        query: 'Google Pixel 10 release',
        maxResults: 3,
        fetchContent: false
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.results).toBeDefined();
      expect(Array.isArray(data.results)).toBe(true);
      expect(data.query).toBe('Google Pixel 10 release');
      expect(data.timestamp).toBeDefined();
      expect(data.sourcesUsed).toBeDefined();
      expect(Array.isArray(data.sourcesUsed)).toBe(true);
    });

    test('should limit results to maxResults parameter', async () => {
      const result = await executeWebSearch({
        query: 'test query',
        maxResults: 2,
        fetchContent: false
      });

      const data = JSON.parse(result);
      expect(data.results.length).toBeLessThanOrEqual(2);
    });

    test('should handle search errors gracefully', async () => {
      // Test with empty query to see error handling
      const result = await executeWebSearch({
        query: 'test query',
        maxResults: 5,
        fetchContent: false
      });

      const data = JSON.parse(result);
      // Should still return a valid response structure even if search fails
      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('query');
      expect(data).toHaveProperty('timestamp');
    });
  });

  describe('Content Fetching with Chunking', () => {
    test('should fetch content when fetchContent is true', async () => {
      const result = await executeWebSearch({
        query: 'Google Pixel 10',
        maxResults: 2,
        fetchContent: true,
        contentDepth: 'detailed',
        enableChunking: false
      });

      const data = JSON.parse(result);
      
      
      expect(data.success).toBe(true);
      expect(data.results).toBeDefined();
      
      // At least one result should have fullContent
      const resultsWithContent = data.results.filter((r: any) => r.fullContent);
      expect(resultsWithContent.length).toBeGreaterThan(0);
    });

    test('should respect contentDepth parameter', async () => {
      const tests = [
        { depth: 'summary', expectedMaxLength: 500 },
        { depth: 'detailed', expectedMaxLength: 2000 },
        { depth: 'full', expectedMaxLength: 10000 }
      ];

      for (const testCase of tests) {
        const result = await executeWebSearch({
          query: 'test query',
          maxResults: 1,
          fetchContent: true,
          contentDepth: testCase.depth,
          enableChunking: false
        });

        const data = JSON.parse(result);
        const resultWithContent = data.results.find((r: any) => r.fullContent);
        
        if (resultWithContent) {
          expect(resultWithContent.fullContent.length).toBeLessThanOrEqual(testCase.expectedMaxLength + 10); // Allow for '...' append
        }
      }
    });

    test('should enable chunking when requested', async () => {
      const result = await executeWebSearch({
        query: 'Google Pixel news',
        maxResults: 2,
        fetchContent: true,
        contentDepth: 'detailed',
        enableChunking: true
      });

      const data = JSON.parse(result);
      
      // Results with chunking should have chunk metadata
      const chunkedResults = data.results.filter((r: any) => r.chunks && r.chunkCount);
      
      expect(chunkedResults.length).toBeGreaterThan(0);
      
      const firstChunkedResult = chunkedResults[0];
      expect(firstChunkedResult.chunks).toBeDefined();
      expect(firstChunkedResult.chunkCount).toBeGreaterThan(0);
      expect(Array.isArray(firstChunkedResult.chunks)).toBe(true);
    });

    test('should process multiple results with chunking', async () => {
      const result = await executeWebSearch({
        query: 'comprehensive research topic',
        maxResults: 5,
        fetchContent: true,
        contentDepth: 'full',
        enableChunking: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.results.length).toBeGreaterThan(1);
      
      // Should attempt to process up to 5 results (as per our enhancement)
      const processedResults = data.results.slice(0, 5);
      expect(processedResults.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Parameter Validation and Defaults', () => {
    test('should use default values for optional parameters', async () => {
      const result = await executeWebSearch({
        query: 'minimal parameters test'
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBeDefined();
      expect(data.results).toBeDefined();
      expect(data.results.length).toBeLessThanOrEqual(5); // Default maxResults
    });

    test('should handle null and undefined parameters gracefully', async () => {
      const result = await executeWebSearch({
        query: 'test query',
        maxResults: null,
        fetchContent: null,
        contentDepth: null,
        enableChunking: null
      });

      const data = JSON.parse(result);
      expect(data.success).toBeDefined();
      // Should not throw errors and should use defaults
    });

    test('should validate contentDepth enum values', async () => {
      const validDepths = ['summary', 'detailed', 'full'];
      
      for (const depth of validDepths) {
        const result = await executeWebSearch({
          query: 'test query',
          fetchContent: true,
          contentDepth: depth
        });

        const data = JSON.parse(result);
        expect(data.success).toBeDefined();
      }
    });
  });

  describe('Response Format Validation', () => {
    test('should return valid JSON structure', async () => {
      const result = await executeWebSearch({
        query: 'json structure test',
        maxResults: 3,
        fetchContent: true
      });

      // Should be valid JSON
      expect(() => JSON.parse(result)).not.toThrow();
      
      const data = JSON.parse(result);
      
      // Required fields
      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('results');
      expect(data).toHaveProperty('query');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('sourcesUsed');
      
      // Types
      expect(typeof data.success).toBe('boolean');
      expect(Array.isArray(data.results)).toBe(true);
      expect(typeof data.query).toBe('string');
      expect(typeof data.timestamp).toBe('string');
      expect(Array.isArray(data.sourcesUsed)).toBe(true);
    });

    test('should include proper result structure for each search result', async () => {
      const result = await executeWebSearch({
        query: 'result structure test',
        maxResults: 2,
        fetchContent: false
      });

      const data = JSON.parse(result);
      
      if (data.results.length > 0) {
        const firstResult = data.results[0];
        
        // Required fields for each result
        expect(firstResult).toHaveProperty('title');
        expect(firstResult).toHaveProperty('url');
        expect(firstResult).toHaveProperty('snippet');
        expect(firstResult).toHaveProperty('source');
        
        // Types
        expect(typeof firstResult.title).toBe('string');
        expect(typeof firstResult.url).toBe('string');
        expect(typeof firstResult.snippet).toBe('string');
        expect(typeof firstResult.source).toBe('string');
      }
    });

    test('should include chunking metadata when enabled', async () => {
      const result = await executeWebSearch({
        query: 'chunking metadata test',
        maxResults: 1,
        fetchContent: true,
        enableChunking: true
      });

      const data = JSON.parse(result);
      const resultWithChunks = data.results.find((r: any) => r.chunks);
      
      if (resultWithChunks) {
        expect(resultWithChunks).toHaveProperty('chunks');
        expect(resultWithChunks).toHaveProperty('chunkCount');
        expect(Array.isArray(resultWithChunks.chunks)).toBe(true);
        expect(typeof resultWithChunks.chunkCount).toBe('number');
        
        // Validate chunk structure
        if (resultWithChunks.chunks.length > 0) {
          const firstChunk = resultWithChunks.chunks[0];
          expect(firstChunk).toHaveProperty('id');
          expect(firstChunk).toHaveProperty('content');
          expect(firstChunk).toHaveProperty('type');
          expect(firstChunk).toHaveProperty('priority');
          expect(firstChunk).toHaveProperty('wordCount');
        }
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle network timeouts gracefully', async () => {
      const result = await executeWebSearch({
        query: 'timeout test',
        fetchContent: true
      });

      const data = JSON.parse(result);
      
      // Should handle gracefully and still return search results
      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('results');
    });

    test('should provide meaningful error messages', async () => {
      // Test with invalid query to trigger error path
      const result = await executeWebSearch({
        query: '', // Empty query might trigger error
        maxResults: 1
      });

      const data = JSON.parse(result);
      
      if (!data.success) {
        expect(data).toHaveProperty('error');
        expect(typeof data.error).toBe('string');
        expect(data.error.length).toBeGreaterThan(0);
      }
    });
  });
});