// TODO: Replace with Vercel AI pattern;
import { z } from 'zod';
import chalk from 'chalk';
import { config } from 'dotenv';
import { extractReasoningFromResponse } from '../utils/extractReasoning';

config();

async function testStructuredReasoning() {
  console.log(chalk.cyan('Testing Structured Output with Reasoning\n'));
  
  // Create an agent with structured output including reasoning
  const analysisAgent = new Agent({
    name: 'Analysis Agent',
    model: 'gpt-4o',
    instructions: `You are an analytical assistant that provides structured responses with clear reasoning.
    
Always include:
1. Your step-by-step reasoning process
2. A confidence score between 0 and 1
3. The final analysis result`,
    outputType: z.object({
      reasoning: z.string().describe('Your step-by-step thought process'),
      confidence: z.number().min(0).max(1).describe('Confidence level (0-1)'),
      analysis: z.string().describe('The final analysis'),
      keyPoints: z.array(z.string()).describe('Key points from the analysis'),
    }) as any,
  });

  const question = 'Why might a Node.js application have high memory usage after running for several hours?';

  try {
    console.log(chalk.yellow('Question:'), question);
    console.log('\n' + '=' .repeat(60) + '\n');

    const runResult = await run(analysisAgent, [{
      role: 'user',
      content: question
    }]);

    console.log(chalk.green('Structured Response with Reasoning:\n'));
    
    // Extract reasoning data using utility function
    const reasoningData = extractReasoningFromResponse(runResult);
    
    if (reasoningData) {
      console.log(chalk.magenta('🧠 Reasoning Process:'));
      console.log(reasoningData.reasoning);
      console.log();
      
      if (reasoningData.confidence !== undefined) {
        console.log(chalk.blue(`📊 Confidence: ${(reasoningData.confidence * 100).toFixed(0)}%`));
        console.log();
      }
      
      console.log(chalk.green('📝 Analysis:'));
      console.log(reasoningData.data?.analysis || 'No analysis available');
      console.log();
      
      if (reasoningData.data?.keyPoints && reasoningData.data.keyPoints.length > 0) {
        console.log(chalk.yellow('🔑 Key Points:'));
        reasoningData.data.keyPoints.forEach((point: string, i: number) => {
          console.log(`  ${i + 1}. ${point}`);
        });
      }
    } else {
      console.log('Response:', JSON.stringify(runResult, null, 2));
    }
    
  } catch (error) {
    console.error(chalk.red('Error:'), error);
  }
}

testStructuredReasoning().catch(console.error);