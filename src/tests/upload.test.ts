import { expect, test } from 'bun:test';
import request from 'supertest';
import app from '../api/server';

const encoder = new TextEncoder();

test('POST /api/upload uploads a file and serves it', async () => {
  const buf = encoder.encode('hello world');

  const res = await request(app)
    .post('/api/upload')
    .attach('file', Buffer.from(buf), { filename: 'hello.txt', contentType: 'text/plain' });

  expect(res.status).toBe(200);
  expect(res.body?.success).toBe(true);
  const files = res.body?.files ?? [];
  expect(Array.isArray(files)).toBe(true);
  expect(files.length).toBeGreaterThan(0);

  const file = files[0];
  expect(typeof file.url).toBe('string');
  expect(file.url.startsWith('/uploads/')).toBe(true);

  const getRes = await request(app).get(file.url);
  expect(getRes.status).toBe(200);
  expect(getRes.text).toContain('hello world');
});
