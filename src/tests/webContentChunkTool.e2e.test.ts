import { describe, test, expect, mock, beforeEach, afterEach } from 'bun:test';
import { webContentChunkTool } from '../tools/webContentChunk';
import { mockNewsArticleHTML, mockFetchResponse } from './fixtures/webContent';

// Mock fetch for controlled testing
const mockFetch = mock(() => Promise.resolve({
  ...mockFetchResponse,
  text: () => Promise.resolve(mockNewsArticleHTML)
}));

// Ensure webSearchHelpers uses our mocked fetch (it imports 'node-fetch')
mock.module('node-fetch', () => ({
  default: mockFetch
}));

// Preserve the original console.error so we can restore it after tests
const originalConsoleError = console.error;

global.fetch = mockFetch as any;

describe('WebContentChunkTool End-to-End Tests', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    console.error = mock(() => {}); // Suppress error logs in tests
    // Reset module mock and global fetch before each test
    mock.module('node-fetch', () => ({ default: mockFetch }));
    global.fetch = mockFetch as any;
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  describe('Basic Content Fetching', () => {
    test('should fetch and chunk content from URL', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example-tech-news.com/pixel-10-release',
        contentDepth: 'detailed',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.url).toBe('https://example-tech-news.com/pixel-10-release');
      expect(data.title).toBeDefined();
      expect(data.content).toBeDefined();
      expect(data.timestamp).toBeDefined();
      expect(data.availableChunks).toBeDefined();
      expect(Array.isArray(data.availableChunks)).toBe(true);
    });

    test('should return appropriate content based on depth', async () => {
      const depths = [
        { depth: 'summary', maxLength: 500 },
        { depth: 'detailed', maxLength: 2000 },
        { depth: 'full', maxLength: 15000 }
      ];

      for (const { depth, maxLength } of depths) {
        const result = await (webContentChunkTool as any).execute({
          url: 'https://example.com/article',
          contentDepth: depth,
          includeChunks: false
        });

        const data = JSON.parse(result);
        
        expect(data.success).toBe(true);
        expect(data.content.length).toBeLessThanOrEqual(maxLength + 10); // Account for '...'
      }
    });

    test('should provide chunk preview information', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.availableChunks).toBeDefined();
      
      if (data.availableChunks.length > 0) {
        const chunk = data.availableChunks[0];
        expect(chunk).toHaveProperty('id');
        expect(chunk).toHaveProperty('title');
        expect(chunk).toHaveProperty('type');
        expect(chunk).toHaveProperty('priority');
        expect(chunk).toHaveProperty('wordCount');
        expect(chunk).toHaveProperty('preview');
        
        // Preview should be truncated
        expect(chunk.preview.length).toBeLessThanOrEqual(103); // 100 chars + '...'
      }
    });
  });

  describe('Specific Chunk Fetching', () => {
    test('should fetch specific chunks by ID', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        chunkIds: ['intro', 'conclusion'],
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.chunks).toBeDefined();
      expect(Array.isArray(data.chunks)).toBe(true);
      
      // Should only return requested chunks
      const chunkIds = data.chunks.map((c: any) => c.id);
      expect(chunkIds).toContain('intro');
      expect(chunkIds).toContain('conclusion');
      expect(chunkIds.length).toBeLessThanOrEqual(2);
    });

    test('should format content properly for specific chunks', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        chunkIds: ['intro'],
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.content).toBeDefined();
      
      // Content should be formatted with markdown headers
      expect(data.content).toContain('## ');
      expect(data.content).toContain('Introduction');
    });

    test('should handle non-existent chunk IDs gracefully', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        chunkIds: ['non-existent-chunk', 'another-fake-chunk'],
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.chunks).toBeDefined();
      expect(Array.isArray(data.chunks)).toBe(true);
      // Should return empty array for non-existent chunks
      expect(data.chunks.length).toBe(0);
    });

    test('should combine multiple specific chunks', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        chunkIds: ['intro', 'section-1', 'conclusion'],
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      if (data.chunks && data.chunks.length > 1) {
        // Content should contain multiple sections
        const sectionCount = (data.content.match(/## /g) || []).length;
        expect(sectionCount).toBeGreaterThanOrEqual(data.chunks.length);
      }
    });
  });

  describe('Content Processing Options', () => {
    test('should disable chunking when includeChunks is false', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        includeChunks: false,
        contentDepth: 'detailed'
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(true);
      expect(data.content).toBeDefined();
      expect(data.availableChunks).toBeUndefined();
      expect(data.chunks).toBeUndefined();
    });

    test('should include summary when available', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/article',
        includeChunks: true,
        contentDepth: 'summary'
      });

      const data = JSON.parse(result);
      
      if (data.summary) {
        expect(typeof data.summary).toBe('string');
        expect(data.summary.length).toBeGreaterThan(20);
        expect(data.summary.length).toBeLessThanOrEqual(500);
      }
    });

    test('should handle different content types', async () => {
      // Test with JSON response
      const jsonFetch = mock(() => Promise.resolve({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: () => Promise.resolve({ message: 'API response' })
      }));

      global.fetch = jsonFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://api.example.com/data',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(true);
      
      // Restore original fetch
      global.fetch = mockFetch as any;
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle HTTP errors gracefully', async () => {
      const errorFetch = mock(() => Promise.resolve({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }));

      global.fetch = errorFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/not-found',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(false);
      expect(data.error).toBeDefined();
      expect(data.error).toContain('404');
      
      // Restore fetch
      global.fetch = mockFetch as any;
    });

    test('should handle network failures', async () => {
      const networkError = mock(() => Promise.reject(new Error('Network unreachable')));
      global.fetch = networkError as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/unreachable',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.success).toBe(false);
      expect(data.error).toBeDefined();
      expect(data.error).toContain('Network unreachable');
      
      // Restore fetch
      global.fetch = mockFetch as any;
    });

    test('should validate URL parameter', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'invalid-url',
        includeChunks: true
      });

      const data = JSON.parse(result);
      // Should handle invalid URLs gracefully
      expect(data).toHaveProperty('success');
    });

    test('should handle empty content responses', async () => {
      const emptyFetch = mock(() => Promise.resolve({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve('')
      }));

      global.fetch = emptyFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/empty',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(true);
      
      // Restore fetch
      global.fetch = mockFetch as any;
    });

    test('should handle malformed HTML gracefully', async () => {
      const malformedHTML = '<html><body><p>Unclosed paragraph<div>Mixed tags</body>';
      const malformedFetch = mock(() => Promise.resolve({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'text/html']]),
        text: () => Promise.resolve(malformedHTML)
      }));

      global.fetch = malformedFetch as any;

      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/malformed',
        includeChunks: true
      });

      const data = JSON.parse(result);
      expect(data.success).toBe(true);
      
      // Restore fetch
      global.fetch = mockFetch as any;
    });
  });

  describe('Response Format Validation', () => {
    test('should return consistent response structure', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/test',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      // Required fields
      expect(data).toHaveProperty('success');
      expect(data).toHaveProperty('url');
      expect(data).toHaveProperty('timestamp');
      
      // Conditional fields based on success
      if (data.success) {
        expect(data).toHaveProperty('title');
        expect(data).toHaveProperty('content');
      } else {
        expect(data).toHaveProperty('error');
      }
    });

    test('should provide valid timestamps', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/test',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      expect(data.timestamp).toBeDefined();
      expect(() => new Date(data.timestamp)).not.toThrow();
      
      const timestamp = new Date(data.timestamp);
      const now = new Date();
      const timeDiff = Math.abs(now.getTime() - timestamp.getTime());
      
      // Should be within last 30 seconds
      expect(timeDiff).toBeLessThan(30000);
    });

    test('should return valid JSON in all scenarios', async () => {
      const testCases = [
        { includeChunks: true, chunkIds: ['intro'] },
        { includeChunks: false, contentDepth: 'summary' },
        { includeChunks: true, contentDepth: 'full' }
      ];

      for (const testCase of testCases) {
        const result = await (webContentChunkTool as any).execute({
          url: 'https://example.com/test',
          ...testCase
        });

        expect(() => JSON.parse(result)).not.toThrow();
        
        const data = JSON.parse(result);
        expect(typeof data).toBe('object');
        expect(data).not.toBeNull();
      }
    });
  });

  describe('Performance and Content Limits', () => {
    test('should respect content length limits', async () => {
      const result = await (webContentChunkTool as any).execute({
        url: 'https://example.com/long-article',
        contentDepth: 'full',
        includeChunks: true
      });

      const data = JSON.parse(result);
      
      if (data.success && data.content) {
        // Should not exceed reasonable limits
        expect(data.content.length).toBeLessThan(20000);
      }
    });

    test('should complete within reasonable time', async () => {
      const startTime = Date.now();
      
      await (webContentChunkTool as any).execute({
        url: 'https://example.com/test',
        includeChunks: true,
        contentDepth: 'full'
      });

      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 5 seconds (considering our 20s timeout)
      expect(duration).toBeLessThan(5000);
    });
  });
});