import { describe, test, expect } from 'bun:test';
import type { Response } from 'express';
import { EnhancedStreamHandler } from '../utils/enhancedStreamHandler';

function makeVercelTextStream(chunks: string[]) {
  return {
    textStream: {
      async *[Symbol.asyncIterator]() {
        for (const c of chunks) {
          await new Promise((r) => setTimeout(r, 1));
          yield c;
        }
      },
    },
    completed: Promise.resolve(),
    state: { output: [{ content: chunks.join('') }] },
  } as any;
}

class MockRes implements Partial<Response> {
  public writes: string[] = [];
  public headers: Record<string, any> = {};
  public ended = false;
  setHeader(name: string, value: any) { this.headers[name] = value; }
  write(chunk: any) { this.writes.push(String(chunk)); return true as any; }
  end() { this.ended = true; return this as any; }
  flush() { /* no-op */ }
}

describe('EnhancedStreamHandler SSE with Vercel textStream', () => {
  test('emits message chunks and completes', async () => {
    const res = new MockRes();
    const handler = new EnhancedStreamHandler(res as unknown as Response, { sessionId: 'sess_test' });

    const stream = makeVercelTextStream(['Hello', ' ', 'world']);
    const { finalOutput } = await handler.handleAgentStream(stream);

    const output = res.writes.join('');

    // Headers set for SSE
    expect(res.headers['Content-Type']).toBe('text/event-stream');

    // Should emit at least one message event and a complete event
    expect(output.includes('event: message')).toBe(true);
    expect(output.includes('event: complete')).toBe(true);

    // Should contain chunked content
    expect(output).toContain('Hello');
    expect(output).toContain('world');

    // Handler returns finalOutput as concatenated content
    expect(finalOutput).toBe('Hello world');
    expect(res.ended).toBe(true);
  });
});

