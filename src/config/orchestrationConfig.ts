/**
 * Orchestration configuration module.
 * Self-contained, no side effects. Uses strict TypeScript and JSDoc on public APIs.
 */

// Types
import type { WorkCategory } from '../agents/orchestration/types';

/**
 * Retry policy for orchestration operations.
 */
export interface OrchestrationRetryPolicy {
  maxRetries: number;
  baseDelayMs: number;
  backoffFactor: number;
  maxDelayMs: number;
}

/**
 * Concurrency controls for orchestration.
 */
export interface OrchestrationConcurrency {
  globalMaxInFlight: number;
  perCategory: Partial<Record<WorkCategory, number>>;
}

/**
 * Token and content caps to bound workload.
 */
export interface OrchestrationTokenCaps {
  packageTokenCap: number;
  perFileByteCap: number;
  perFileHeaderLines: number;
  perFileFooterLines: number;
}

/**
 * Feature flags to toggle orchestration behaviors.
 */
export interface OrchestrationFeatureFlags {
  fineGrainedOrchestration: boolean;
  autoCreateReviewUnits: boolean;
}

/**
 * Top-level orchestration config shape.
 */
export interface OrchestrationConfig {
  retry: OrchestrationRetryPolicy;
  concurrency: OrchestrationConcurrency;
  tokens: OrchestrationTokenCaps;
  partitioning: {
    maxFilesPerUnit: number;
    maxLOCPerUnit: number;
    includeTestsFromScope: boolean;
  };
  heartbeatIntervalMs: number;
  timeoutMsPerUnit: number;
  featureFlags: OrchestrationFeatureFlags;
}

/**
 * Default orchestration configuration.
 */
export const orchestrationConfig: OrchestrationConfig = {
  retry: {
    maxRetries: 2,
    baseDelayMs: 1000,
    backoffFactor: 2,
    maxDelayMs: 15000,
  },
  concurrency: {
    globalMaxInFlight: Infinity,
    perCategory: {
      coding: 3,
      review: 2,
      debug: 2,
      research: 2,
    } as Partial<Record<WorkCategory, number>>,
  },
  tokens: {
    packageTokenCap: 6000,
    perFileByteCap: 16000,
    perFileHeaderLines: 120,
    perFileFooterLines: 60,
  },
  partitioning: {
    maxFilesPerUnit: 10,
    maxLOCPerUnit: 400,
    includeTestsFromScope: true,
  },
  heartbeatIntervalMs: 2000,
  timeoutMsPerUnit: 300000,
  featureFlags: {
    fineGrainedOrchestration: false,
    autoCreateReviewUnits: true,
  },
};

// Internal helpers
const TRUE_VALUES = new Set(['true', '1', 'yes', 'on']);
const FALSE_VALUES = new Set(['false', '0', 'no', 'off']);

function parseNumber(envVar: string | undefined): number | undefined {
  if (envVar == null || envVar.trim() === '') return undefined;
  if (envVar.toLowerCase() === 'infinity') return Infinity;
  const n = Number(envVar);
  return Number.isFinite(n) ? n : undefined;
}

function parseBoolean(envVar: string | undefined): boolean | undefined {
  if (envVar == null) return undefined;
  const v = envVar.trim().toLowerCase();
  if (TRUE_VALUES.has(v)) return true;
  if (FALSE_VALUES.has(v)) return false;
  return undefined;
}

/**
 * Build a config fragment from ORCH_* environment variables.
 */
function envConfig(): Partial<OrchestrationConfig> {
  const {
    ORCH_MAX_RETRIES,
    ORCH_BASE_DELAY_MS,
    ORCH_BACKOFF_FACTOR,
    ORCH_MAX_DELAY_MS,
    ORCH_GLOBAL_MAX_IN_FLIGHT,
    ORCH_PER_CAT_CODING,
    ORCH_PER_CAT_REVIEW,
    ORCH_PER_CAT_DEBUG,
    ORCH_PER_CAT_RESEARCH,
    ORCH_TOKEN_CAP,
    ORCH_PER_FILE_BYTE_CAP,
    ORCH_HEADER_LINES,
    ORCH_FOOTER_LINES,
    ORCH_MAX_FILES_PER_UNIT,
    ORCH_MAX_LOC_PER_UNIT,
    ORCH_INCLUDE_TESTS,
    ORCH_HEARTBEAT_MS,
    ORCH_TIMEOUT_MS,
    ORCH_FINE_GRAINED,
    ORCH_AUTO_REVIEW_UNITS,
  } = process.env as Record<string, string | undefined>;

  const retry: Partial<OrchestrationRetryPolicy> = {};
  const r1 = parseNumber(ORCH_MAX_RETRIES); if (r1 !== undefined) retry.maxRetries = r1;
  const r2 = parseNumber(ORCH_BASE_DELAY_MS); if (r2 !== undefined) retry.baseDelayMs = r2;
  const r3 = parseNumber(ORCH_BACKOFF_FACTOR); if (r3 !== undefined) retry.backoffFactor = r3;
  const r4 = parseNumber(ORCH_MAX_DELAY_MS); if (r4 !== undefined) retry.maxDelayMs = r4;

  const perCategory: Partial<Record<WorkCategory, number>> = {};
  const g = parseNumber(ORCH_GLOBAL_MAX_IN_FLIGHT);
  const pcCoding = parseNumber(ORCH_PER_CAT_CODING);
  if (pcCoding !== undefined) (perCategory as any).coding = pcCoding;
  const pcReview = parseNumber(ORCH_PER_CAT_REVIEW);
  if (pcReview !== undefined) (perCategory as any).review = pcReview;
  const pcDebug = parseNumber(ORCH_PER_CAT_DEBUG);
  if (pcDebug !== undefined) (perCategory as any).debug = pcDebug;
  const pcResearch = parseNumber(ORCH_PER_CAT_RESEARCH);
  if (pcResearch !== undefined) (perCategory as any).research = pcResearch;

  const tokens: Partial<OrchestrationTokenCaps> = {};
  const t1 = parseNumber(ORCH_TOKEN_CAP); if (t1 !== undefined) tokens.packageTokenCap = t1;
  const t2 = parseNumber(ORCH_PER_FILE_BYTE_CAP); if (t2 !== undefined) tokens.perFileByteCap = t2;
  const t3 = parseNumber(ORCH_HEADER_LINES); if (t3 !== undefined) tokens.perFileHeaderLines = t3;
  const t4 = parseNumber(ORCH_FOOTER_LINES); if (t4 !== undefined) tokens.perFileFooterLines = t4;

  const partitioning: Partial<OrchestrationConfig['partitioning']> = {};
  const p1 = parseNumber(ORCH_MAX_FILES_PER_UNIT); if (p1 !== undefined) partitioning.maxFilesPerUnit = p1;
  const p2 = parseNumber(ORCH_MAX_LOC_PER_UNIT); if (p2 !== undefined) partitioning.maxLOCPerUnit = p2;
  const p3 = parseBoolean(ORCH_INCLUDE_TESTS); if (p3 !== undefined) partitioning.includeTestsFromScope = p3;

  const heartbeatIntervalMs = parseNumber(ORCH_HEARTBEAT_MS);
  const timeoutMsPerUnit = parseNumber(ORCH_TIMEOUT_MS);

  const featureFlags: Partial<OrchestrationFeatureFlags> = {};
  const f1 = parseBoolean(ORCH_FINE_GRAINED); if (f1 !== undefined) featureFlags.fineGrainedOrchestration = f1;
  const f2 = parseBoolean(ORCH_AUTO_REVIEW_UNITS); if (f2 !== undefined) featureFlags.autoCreateReviewUnits = f2;

  const out: Partial<OrchestrationConfig> = {};
  if (Object.keys(retry).length) (out as any).retry = retry;
  const concurrency: Partial<OrchestrationConcurrency> = {};
  if (g !== undefined) concurrency.globalMaxInFlight = g;
  if (Object.keys(perCategory).length) concurrency.perCategory = perCategory;
  if (Object.keys(concurrency).length) (out as any).concurrency = concurrency;
  if (Object.keys(tokens).length) (out as any).tokens = tokens;
  if (Object.keys(partitioning).length) (out as any).partitioning = partitioning as any;
  if (heartbeatIntervalMs !== undefined) (out as any).heartbeatIntervalMs = heartbeatIntervalMs;
  if (timeoutMsPerUnit !== undefined) (out as any).timeoutMsPerUnit = timeoutMsPerUnit;
  if (Object.keys(featureFlags).length) (out as any).featureFlags = featureFlags;

  return out;
}

/**
 * Deep merge of OrchestrationConfig-like objects.
 * Later objects override earlier ones.
 */
function mergeDeep<T extends Record<string, any>>(...parts: Partial<T>[]): T {
  const isObject = (v: any) => v && typeof v === 'object' && !Array.isArray(v);
  const result: any = {};
  for (const part of parts) {
    if (!part) continue;
    for (const [k, v] of Object.entries(part)) {
      if (isObject(v)) {
        result[k] = mergeDeep(result[k] ?? {}, v as any);
        continue;
      }
      if (v !== undefined) {
        result[k] = v;
      }
    }
  }
  return result as T;
}

/**
 * Load orchestration configuration.
 * Merge order: defaults <- env <- overrides
 */
export function loadOrchestrationConfig(
  overrides?: Partial<OrchestrationConfig>,
): OrchestrationConfig {
  const fromEnv = envConfig();
  return mergeDeep<OrchestrationConfig>(orchestrationConfig, fromEnv, overrides ?? {});
}

/**
 * Convenience check for fine-grained orchestration enablement.
 * Returns true if any of:
 *  - forceDelegation is true
 *  - context?.fineGrainedOrchestration === true
 *  - process.env.ORCH_FINE_GRAINED === 'true'
 */
export function isFineGrainedEnabled(
  context?: any,
  forceDelegation?: boolean,
): boolean {
  if (forceDelegation) return true;
  if (context?.fineGrainedOrchestration === true) return true;
  return parseBoolean(process.env.ORCH_FINE_GRAINED) === true;
}
