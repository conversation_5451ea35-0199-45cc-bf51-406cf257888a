import { MCPToolFactoryOptions } from '../mcp/MCPToolFactory';
import * as path from 'path';
import {
  createFilesystemMCPConfig,
  createGitMCPConfig,
  createDatabaseMCPConfig,
  createWebSearchMCPConfig,
  createDefaultMCPToolFactoryOptions
} from '../mcp/utils';

import { MCPServerConfig } from '../mcp/MCPServerManager';

export interface MCPConfiguration {
  enabled: boolean;
  servers: MCPServerConfig[];
  toolFactory: MCPToolFactoryOptions;
  autoConnect: boolean;
  healthCheckInterval: number; // in milliseconds
  maxConcurrentConnections: number;
  connectionTimeout: number; // in milliseconds
}

/**
 * Load MCP configuration from environment variables and defaults
 */
export function loadMCPConfiguration(): MCPConfiguration {
  const enabled = process.env.MCP_ENABLED !== 'false';
  const autoConnect = process.env.MCP_AUTO_CONNECT !== 'false';
  const healthCheckInterval = parseInt(process.env.MCP_HEALTH_CHECK_INTERVAL || '60000');
  const maxConcurrentConnections = parseInt(process.env.MCP_MAX_CONNECTIONS || '10');
  const connectionTimeout = parseInt(process.env.MCP_CONNECTION_TIMEOUT || '30000');

  const servers: MCPServerConfig[] = [];

  // Load default servers if enabled
  if (enabled) {
    servers.push(...getDefaultMCPServers());
    servers.push(...loadCustomMCPServers());
  }

  return {
    enabled,
    servers,
    toolFactory: createDefaultMCPToolFactoryOptions(),
    autoConnect,
    healthCheckInterval,
    maxConcurrentConnections,
    connectionTimeout
  };
}

/**
 * Get default MCP server configurations
 */
function getDefaultMCPServers(): MCPServerConfig[] {
  const servers: MCPServerConfig[] = [];
  const currentDir = process.cwd();

  // Filesystem server for SOURCE CODE directories only
  if (process.env.MCP_FILESYSTEM_ENABLED !== 'false') {
    servers.push(createFilesystemMCPConfig(
      'filesystem-local',
      'Local Filesystem (Source Only)',
      path.join(currentDir, 'src'), // ONLY scan src directory
      {
        priority: 100,
        enabled: true,
        tags: ['filesystem', 'local', 'files', 'source-only'],
        allowWrite: process.env.MCP_FILESYSTEM_ALLOW_WRITE === 'true'
      }
    ));
  }

  // Git server for current directory (enabled by default; can be disabled with MCP_GIT_ENABLED=false)
  if (process.env.MCP_GIT_ENABLED !== 'false') {
    servers.push(createGitMCPConfig(
      'git-local',
      'Local Git Repository',
      currentDir,
      {
        priority: 90,
        enabled: true,
        tags: ['git', 'local', 'version-control']
      }
    ));
  }

  // Web search server (if configured)
  if (process.env.MCP_WEB_SEARCH_URL) {
    servers.push(createWebSearchMCPConfig(
      'web-search',
      'Web Search Service',
      process.env.MCP_WEB_SEARCH_URL,
      {
        priority: 70,
        enabled: true,
        tags: ['web', 'search', 'research'],
        apiKey: process.env.MCP_WEB_SEARCH_API_KEY
      }
    ));
  }

  // Database server (if configured)
  if (process.env.MCP_DATABASE_URL && process.env.MCP_DATABASE_SERVER_URL) {
    servers.push(createDatabaseMCPConfig(
      'database-primary',
      'Primary Database',
      process.env.MCP_DATABASE_URL,
      process.env.MCP_DATABASE_SERVER_URL,
      {
        priority: 80,
        enabled: true,
        tags: ['database', 'sql', 'data']
      }
    ));
  }

  // Browser-Use MCP server (Python-based advanced browser automation)
  if (process.env.MCP_BROWSER_USE_ENABLED === 'true') {
    // Collect env vars to forward to the Python process (BROWSER_USE_* + optional OPENAI_API_KEY)
    const forwardedEnv: Record<string, string> = {};
    for (const [k, v] of Object.entries(process.env)) {
      if (k.startsWith('BROWSER_USE_') && typeof v === 'string') forwardedEnv[k] = v;
    }
    if (process.env.OPENAI_API_KEY) forwardedEnv.OPENAI_API_KEY = process.env.OPENAI_API_KEY;

    // Allow forcing the local MCP server script for advanced parameters
    const useLocal = process.env.BROWSER_USE_USE_LOCAL === 'true';
    const pythonBin = process.env.PYTHON || 'python';
    const localScriptCmd = `${pythonBin} ${path.join(currentDir, 'docs/mcp-servers/browser-use.py')}`;

    const browserUseConfig: MCPServerConfig = {
      id: 'browser-use',
      name: 'Browser-Use Advanced Automation',
      type: 'stdio',
      config: {
        // Prefer local server if requested; else use wrapper script
        fullCommand: useLocal
          ? localScriptCmd
          : (process.env.BROWSER_USE_COMMAND || `${process.env.HOME}/.dante-gpt/run-browser-use-mcp.sh`),
        cacheToolsList: true,
        connectionTimeout: 45000, // Longer timeout for Python startup
        reconnectionOptions: {
          maxRetries: 2,
          retryDelay: 10000
        },
        env: forwardedEnv
      },
      enabled: true,
      priority: 96, // Higher priority than Playwright for advanced features
      tags: ['browser', 'automation', 'web', 'python', 'browser-use', 'advanced']
    };

    servers.push(browserUseConfig);
  }


  return servers;
}

/**
 * Load custom MCP servers from environment variables
 */
function loadCustomMCPServers(): MCPServerConfig[] {
  const servers: MCPServerConfig[] = [];

  // Load custom servers from MCP_CUSTOM_SERVERS environment variable
  // Expected format: JSON array of server configurations
  const customServersEnv = process.env.MCP_CUSTOM_SERVERS;
  if (customServersEnv) {
    try {
      const customConfigs = JSON.parse(customServersEnv);
      if (Array.isArray(customConfigs)) {
        for (const config of customConfigs) {
          if (validateCustomServerConfig(config)) {
            servers.push(config);
          } else {
            console.warn('Invalid custom MCP server configuration:', config);
          }
        }
      }
    } catch (error) {
      console.error('Failed to parse MCP_CUSTOM_SERVERS:', error);
    }
  }

  // Load individual custom servers from numbered environment variables
  let i = 1;
  while (process.env[`MCP_SERVER_${i}_ID`]) {
    const serverConfig = loadCustomServerFromEnv(i);
    if (serverConfig) {
      servers.push(serverConfig);
    }
    i++;
  }

  return servers;
}

/**
 * Load a custom server configuration from numbered environment variables
 */
function loadCustomServerFromEnv(index: number): MCPServerConfig | null {
  const id = process.env[`MCP_SERVER_${index}_ID`];
  const name = process.env[`MCP_SERVER_${index}_NAME`];
  const type = process.env[`MCP_SERVER_${index}_TYPE`] as 'stdio' | 'streamable_http' | 'hosted';

  if (!id || !name || !type) {
    return null;
  }

  const enabled = process.env[`MCP_SERVER_${index}_ENABLED`] !== 'false';
  const priority = parseInt(process.env[`MCP_SERVER_${index}_PRIORITY`] || '50');
  const tags = process.env[`MCP_SERVER_${index}_TAGS`]?.split(',').map(t => t.trim()) || [];

  const config: any = {};

  switch (type) {
    case 'stdio':
      config.fullCommand = process.env[`MCP_SERVER_${index}_COMMAND`];
      if (!config.fullCommand) return null;
      break;

    case 'streamable_http':
      config.url = process.env[`MCP_SERVER_${index}_URL`];
      if (!config.url) return null;

      const apiKey = process.env[`MCP_SERVER_${index}_API_KEY`];
      if (apiKey) {
        config.authProvider = {
          type: 'bearer',
          token: apiKey
        };
      }
      break;

    default:
      return null;
  }

  config.cacheToolsList = process.env[`MCP_SERVER_${index}_CACHE_TOOLS`] !== 'false';

  return {
    id,
    name,
    type,
    config,
    enabled,
    priority,
    tags
  };
}

/**
 * Validate custom server configuration
 */
function validateCustomServerConfig(config: any): config is MCPServerConfig {
  return (
    typeof config === 'object' &&
    typeof config.id === 'string' &&
    typeof config.name === 'string' &&
    ['stdio', 'streamable_http', 'hosted'].includes(config.type) &&
    typeof config.enabled === 'boolean' &&
    typeof config.priority === 'number' &&
    Array.isArray(config.tags) &&
    typeof config.config === 'object'
  );
}

/**
 * Get MCP configuration for specific environment
 */
export function getMCPConfigForEnvironment(env: 'development' | 'production' | 'test'): Partial<MCPConfiguration> {
  switch (env) {
    case 'development':
      return {
        enabled: true,
        autoConnect: true,
        healthCheckInterval: 30000, // 30 seconds
        maxConcurrentConnections: 5,
        connectionTimeout: 10000, // 10 seconds
        toolFactory: {
          ...createDefaultMCPToolFactoryOptions(),
          enableCaching: true,
          cacheExpiry: 2 * 60 * 1000, // 2 minutes for faster development
          toolFilters: {
            blockedTools: [
              'system_shutdown',
              'format_disk',
              'delete_all',
              'sudo_command',
              'root_access',
              'directory_tree', // CRITICAL: Block massive output tool
              'list_files_recursive', // Block recursive listing
              'read_multiple_files', // Block bulk file reading
              'analyze_project' // Block project analysis that loads everything
            ]
          }
        }
      };

    case 'production':
      return {
        enabled: true,
        autoConnect: true,
        healthCheckInterval: 60000, // 1 minute
        maxConcurrentConnections: 10,
        connectionTimeout: 30000, // 30 seconds
        toolFactory: {
          ...createDefaultMCPToolFactoryOptions(),
          enableCaching: true,
          cacheExpiry: 10 * 60 * 1000, // 10 minutes
          toolFilters: {
            // More restrictive in production
            blockedTools: [
              'system_shutdown',
              'format_disk',
              'delete_all',
              'sudo_command',
              'root_access',
              'admin_access',
              'directory_tree', // CRITICAL: Block massive output tool
              'list_files_recursive', // Block recursive listing
              'read_multiple_files', // Block bulk file reading
              'analyze_project' // Block project analysis that loads everything
            ]
          }
        }
      };

    case 'test':
      return {
        enabled: false, // Disabled by default for tests
        autoConnect: false,
        healthCheckInterval: 5000, // 5 seconds
        maxConcurrentConnections: 2,
        connectionTimeout: 5000, // 5 seconds
        toolFactory: {
          ...createDefaultMCPToolFactoryOptions(),
          enableCaching: false // No caching in tests
        }
      };

    default:
      return {};
  }
}

/**
 * Create MCP configuration for specific use cases
 */
export function createMCPConfigForUseCase(useCase: string): Partial<MCPConfiguration> {
  const useCaseConfigs: { [key: string]: Partial<MCPConfiguration> } = {
    'code-analysis': {
      toolFactory: {
        ...createDefaultMCPToolFactoryOptions(),
        toolFilters: {
          requiredTags: ['filesystem', 'git']
        }
      }
    },
    'web-research': {
      toolFactory: {
        ...createDefaultMCPToolFactoryOptions(),
        toolFilters: {
          requiredTags: ['web', 'search']
        }
      }
    },
    'data-processing': {
      toolFactory: {
        ...createDefaultMCPToolFactoryOptions(),
        toolFilters: {
          requiredTags: ['database', 'filesystem']
        }
      }
    },
    'development': {
      toolFactory: {
        ...createDefaultMCPToolFactoryOptions(),
        toolFilters: {
          requiredTags: ['filesystem', 'git']
        }
      }
    }
  };

  return useCaseConfigs[useCase] || {};
}

/**
 * Merge MCP configurations
 */
export function mergeMCPConfigurations(...configs: Partial<MCPConfiguration>[]): MCPConfiguration {
  const defaultConfig = loadMCPConfiguration();

  return configs.reduce((merged: MCPConfiguration, config) => ({
    ...merged,
    ...config,
    servers: [
      ...(merged.servers || []),
      ...(config.servers || [])
    ],
    toolFactory: {
      ...(merged.toolFactory || {}),
      ...(config.toolFactory || {}),
      toolFilters: {
        ...(merged.toolFactory?.toolFilters || {}),
        ...(config.toolFactory?.toolFilters || {})
      }
    }
  } as MCPConfiguration), defaultConfig);
}

// Export the default configuration
export const mcpConfig = loadMCPConfiguration();
