import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import dotenv from 'dotenv';

dotenv.config();

// Configure Google OAuth strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3001/api/auth/google/callback',
      scope: ['profile', 'email'],
    },
    (
      accessToken: string,
      refreshToken: string,
      params: any,
      profile: any,
      done: any
    ) => {
      // Here you would typically find or create a user in your database.
      // Do NOT log tokens or store refresh tokens directly on the session/user object.

      // Build a minimal user representation to serialize into the session.
      const user = {
        id: profile?.id,
        displayName: profile?.displayName,
        emails: profile?.emails,
        photos: profile?.photos,
      };

      // Expose the raw tokens via authInfo so downstream handlers can persist them securely.
      const authInfo = {
        tokens: {
          accessToken,
          refreshToken,
          expires_in: params?.expires_in,
          scope: params?.scope,
          id_token: params?.id_token,
          token_type: params?.token_type,
        },
      };

      return done(null, user, authInfo);
    }
  )
);

passport.serializeUser((user: any, done: any) => {
  // Serialize minimal user info
  done(null, user);
});

passport.deserializeUser((user: any, done: any) => {
  done(null, user);
});

export default passport;
