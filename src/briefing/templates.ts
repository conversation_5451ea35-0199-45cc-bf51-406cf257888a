import type { BriefingScope, BriefingSection } from './types';

export const DEFAULT_SECTIONS: Record<BriefingScope, BriefingSection[]> = {
  'overview': [
    { id: 'summary', title: 'Executive Summary' },
    { id: 'highlights', title: 'Key Highlights' },
    { id: 'implications', title: 'Implications' },
    { id: 'next-steps', title: 'Recommended Next Steps' },
  ],
  'exec': [
    { id: 'summary', title: 'Executive Summary' },
    { id: 'market', title: 'Market Context' },
    { id: 'risk', title: 'Risks and Considerations' },
    { id: 'actions', title: 'Decision Options' },
  ],
  'engineering': [
    { id: 'summary', title: 'Technical Summary' },
    { id: 'architecture', title: 'Architecture/Approach' },
    { id: 'tradeoffs', title: 'Trade-offs' },
    { id: 'implementation', title: 'Implementation Notes' },
  ],
  'product': [
    { id: 'summary', title: 'Product Summary' },
    { id: 'user', title: 'User Impact' },
    { id: 'competition', title: 'Competitive Landscape' },
    { id: 'roadmap', title: 'Roadmap Considerations' },
  ],
  'security': [
    { id: 'summary', title: 'Security Summary' },
    { id: 'threats', title: 'Threat Model' },
    { id: 'vulns', title: 'Known Vulnerabilities' },
    { id: 'mitigations', title: 'Mitigations & Controls' },
  ],
  'deep-dive': [
    { id: 'summary', title: 'Summary' },
    { id: 'background', title: 'Background & Context' },
    { id: 'analysis', title: 'Detailed Analysis' },
    { id: 'appendix', title: 'Appendix' },
  ],
  'custom': [],
};
