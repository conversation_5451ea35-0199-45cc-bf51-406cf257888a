export type BriefingScope = 'overview' | 'deep-dive' | 'exec' | 'engineering' | 'product' | 'security' | 'custom';

export interface BriefingRequest {
  topic: string;
  scope?: BriefingScope;
  audience?: 'executive' | 'engineering' | 'product' | 'security' | 'general';
  timeframe?: { from?: string; to?: string };
  includeSources?: boolean;
  maxSources?: number;
  projectContextPath?: string; // optional: local project root path
  notes?: string; // optional extra instructions
}

export interface BriefingSection {
  id: string;
  title: string;
  summary?: string;
  content?: string;
  bullets?: string[];
}

export interface BriefingResult {
  topic: string;
  scope: BriefingScope;
  generatedAt: string; // ISO timestamp
  sections: BriefingSection[];
  sources?: Array<{ title?: string; url: string; snippet?: string }>;
  warnings?: string[];
}
