import { useEffect, useCallback } from 'react';
import { useAgentStore } from '../stores/agentStore';
import { useTaskStore } from '../stores/taskStore';
import { useTokenStore } from '../stores/tokenStore';
import { useThoughtStore } from '../stores/thoughtStore';

export interface AgentEventData {
  type: string;
  timestamp: string;
  data: {
    message?: string;
    agent?: string;
    tool?: string;
    args?: any;
    result?: any;
    task?: {
      id?: string;
      description?: string;
      progress?: number;
      status?: string;
    };
    tokens?: {
      used?: number;
      model?: string;
      isOutput?: boolean;
    };
    span?: any; // Span data for trace events
    trace?: any; // Trace data for trace events
    error?: any; // Error data for error events
  };
}

export const useAgentEvents = () => {
  const agentStore = useAgentStore();
  const taskStore = useTaskStore();
  const tokenStore = useTokenStore();
  const thoughtStore = useThoughtStore();
  
  const handleAgentEvent = useCallback((event: AgentEventData) => {
    const { type, data } = event;
    const timestamp = new Date(event.timestamp);
    
    switch (type) {
      case 'agent_switch':
      case 'agent_activated':
        if (data.agent) {
          agentStore.activateAgent(data.agent, data.message);
          agentStore.switchPrimaryAgent(data.agent);
          
          // Log activity
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'message',
            content: `Agent activated: ${data.message || 'Processing request'}`,
          });
        }
        break;
        
      case 'plan_created':
        try {
          thoughtStore.addThought({
            promptId: 'session',
            agentId: data.agent || agentStore.currentPrimaryAgent || 'Task Orchestrator',
            stage: 'start',
            content: (data as any).summary || 'Plan created',
          });
        } catch {}
        break;

      case 'thinking':
        if (data.agent) {
          agentStore.updateAgentStatus(data.agent, 'thinking');
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'thinking',
            content: data.message || 'Thinking...',
          });
        }
        break;
        
      case 'tool_call':
        if (data.agent && data.tool) {
          agentStore.setCurrentTool(data.agent, data.tool);
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'tool_call',
            content: `Using tool: ${data.tool}`,
            details: data.args,
          });
        }
        break;
        
      case 'tool_result':
        if (data.agent) {
          agentStore.setCurrentTool(data.agent, undefined);
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'message',
            content: `Tool completed: ${data.tool}`,
            details: data.result,
          });
        }
        break;
        
      case 'handoff':
        const fromAgent = data.agent || 'Dante';
        const toAgent = (data as any).targetAgent || data.result?.agent || data.message?.split(' to ')[1];
        
        if (fromAgent && toAgent) {
          // Deactivate source agent
          agentStore.updateAgentStatus(fromAgent, 'completed');
          agentStore.addAgentActivity(fromAgent, {
            timestamp,
            type: 'handoff',
            content: `Handed off to ${toAgent}`,
          });
          
          // Activate target agent
          agentStore.activateAgent(toAgent, data.message);
          agentStore.switchPrimaryAgent(toAgent);
          agentStore.addAgentActivity(toAgent, {
            timestamp,
            type: 'handoff',
            content: `Received handoff from ${fromAgent}`,
          });
        }
        break;
        
      case 'delegation_start':
        try {
          thoughtStore.addThought({
            promptId: 'session',
            agentId: data.agent || agentStore.currentPrimaryAgent || 'Agent',
            stepId: (data as any).stepId,
            stage: 'start',
            content: (data as any).title || (data as any).description || 'Delegation started',
          });
        } catch {}
        // Deactivate source agent
        break;

      case 'delegation_end':
        try {
          thoughtStore.addThought({
            promptId: 'session',
            agentId: data.agent || agentStore.currentPrimaryAgent || 'Agent',
            stepId: (data as any).stepId,
            stage: (data as any).success ? 'complete' : 'error',
            content: (data as any).success ? 'Delegation complete' : 'Delegation error',
          });
        } catch {}
        break;

      case 'task_created':
        if (data.task?.description) {
          const taskId = taskStore.createTask({
            description: data.task.description,
            status: 'pending',
            priority: 'medium',
            assignedAgent: data.agent,
            estimatedTokens: data.tokens?.used,
          });
          
          if (data.agent) {
            agentStore.addAgentActivity(data.agent, {
              timestamp,
              type: 'message',
              content: `Created task: ${data.task.description}`,
            });
          }
        }
        break;
        
      case 'task_progress':
        if (data.task?.id) {
          taskStore.updateTaskProgress(data.task.id, data.task.progress || 0);
          
          if (data.task.status === 'in_progress') {
            taskStore.startTask(data.task.id, data.agent);
          } else if (data.task.status === 'completed') {
            taskStore.completeTask(data.task.id, data.tokens?.used);
          } else if (data.task.status === 'failed') {
            taskStore.failTask(data.task.id, data.message || 'Task failed');
          }
        }
        break;
        
      case 'token_update':
        if (data.tokens) {
          tokenStore.updateUsage(
            data.tokens.used || 0,
            data.tokens.model || 'gpt-5',
            data.tokens.isOutput
          );
          
          if (data.agent) {
            agentStore.updateTokenUsage(data.agent, data.tokens.used || 0);
            tokenStore.addAgentUsage(data.agent, data.tokens.used || 0);
          }
        }
        break;

      case 'progress':
        if (data.agent) {
          const msg = (data as any).stage || data.message;
          const pct = (data as any).progress as number | undefined;
          agentStore.updateProgress(data.agent, pct, msg);
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'message',
            content: msg || 'Progress update',
            details: data.result,
          });
          if (msg && msg.trim()) {
            try { thoughtStore.addThought({ promptId: 'session', agentId: data.agent, stage: 'delta', content: msg }); } catch {}
          }
        }
        break;
        
      case 'complete':
        if (data.agent) {
          agentStore.updateAgentStatus(data.agent, 'completed');
          agentStore.deactivateAgent(data.agent);
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'message',
            content: 'Task completed',
          });
          try { thoughtStore.addThought({ promptId: 'session', agentId: data.agent, stage: 'complete', content: 'Task completed' }); } catch {}
        }
        
        // Take a snapshot of token usage
        tokenStore.takeSnapshot();
        break;
        
      case 'error':
        if (data.agent) {
          agentStore.updateAgentStatus(data.agent, 'error');
          agentStore.addAgentActivity(data.agent, {
            timestamp,
            type: 'error',
            content: data.message || 'An error occurred',
            details: data.result,
          });
          try { thoughtStore.addThought({ promptId: 'session', agentId: data.agent, stage: 'error', content: data.message || 'Error' }); } catch {}
        }
        break;
        
      default:
        // Log any unhandled events to the current agent
        const currentAgent = agentStore.currentPrimaryAgent;
        if (currentAgent && data.message) {
          agentStore.addAgentActivity(currentAgent, {
            timestamp,
            type: 'message',
            content: data.message,
          });
          try { thoughtStore.addThought({ promptId: 'session', agentId: currentAgent, stage: 'delta', content: data.message }); } catch {}
        }
    }
  }, [agentStore, taskStore, tokenStore, thoughtStore]);
  
  const processEventStream = useCallback((eventData: any) => {
    if (!eventData) return;
    
    // Handle different event formats
    if (typeof eventData === 'string') {
      try {
        const parsed = JSON.parse(eventData);
        handleAgentEvent(parsed);
      } catch {
        // If not JSON, treat as a message
        handleAgentEvent({
          type: 'message',
          timestamp: new Date().toISOString(),
          data: { message: eventData },
        });
      }
    } else if (typeof eventData === 'object') {
      handleAgentEvent(eventData);
    }
  }, [handleAgentEvent]);
  
  const resetStores = useCallback(() => {
    agentStore.clearAgents();
    taskStore.clearAllTasks();
    tokenStore.resetSession();
  }, [agentStore, taskStore, tokenStore]);
  
  return {
    processEventStream,
    handleAgentEvent,
    resetStores,
  };
};
