import { useEffect, useCallback, useRef } from 'react';
import { useTraceStore } from '../stores/traceStore';
import { useAgentEvents, AgentEventData } from './useAgentEvents';
import { 
  initializeTraceProcessor, 
  getTraceProcessor,
  setTraceEventCallback 
} from '../../utils/traceProcessor';

export const useTraceEvents = () => {
  const traceStore = useTraceStore();
  const { processEventStream, handleAgentEvent } = useAgentEvents();
  const processorInitialized = useRef(false);
  
  // Convert trace events to agent events and trace store updates
  const handleTraceEvent = useCallback((event: AgentEventData) => {
    const { type, timestamp, data } = event;
    
    switch (type) {
      case 'trace_started':
        if (data.trace) {
          traceStore.addTrace({
            id: data.trace.id,
            workflowName: data.trace.workflow_name || 'Unknown Workflow',
            groupId: data.trace.group_id,
            startTime: new Date(timestamp),
            status: 'running',
            rootSpanIds: []
          });
        }
        break;
        
      case 'trace_completed':
        if (data.trace) {
          traceStore.completeTrace(data.trace.id);
        }
        break;
        
      case 'trace_error':
        // Handle trace-level errors
        const runningTraces = traceStore.getRunningTraces();
        if (runningTraces.length > 0) {
          const lastTrace = runningTraces[runningTraces.length - 1];
          traceStore.updateTrace(lastTrace.id, { status: 'failed' });
        }
        break;
        
      case 'agent_span':
        if (data.span && data.agent) {
          traceStore.addSpan({
            id: data.span.id,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'agent',
            name: data.agent,
            startTime: new Date(timestamp),
            status: data.span.duration ? 'completed' : 'running',
            endTime: data.span.duration ? 
              new Date(new Date(timestamp).getTime() + data.span.duration) : 
              undefined,
            duration: data.span.duration,
            data: {
              agent: data.agent
            }
          });
          
          // Also emit as agent event for existing UI
          handleAgentEvent({
            type: 'agent_activated',
            timestamp,
            data: {
              agent: data.agent,
              message: data.message
            }
          });
        }
        break;
        
      case 'generation_span':
        if (data.span) {
          traceStore.addSpan({
            id: data.span.id,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'generation',
            name: `LLM Generation (${data.span.model || 'unknown'})`,
            startTime: new Date(timestamp),
            status: data.span.duration ? 'completed' : 'running',
            endTime: data.span.duration ? 
              new Date(new Date(timestamp).getTime() + data.span.duration) : 
              undefined,
            duration: data.span.duration,
            data: {
              model: data.span.model,
              tokens: data.span.tokens
            }
          });
          
          // Emit thinking event for existing UI
          handleAgentEvent({
            type: 'thinking',
            timestamp,
            data: {
              agent: data.agent || 'LLM',
              message: `Generating response with ${data.span.model || 'LLM'}`
            }
          });
          
          // Emit token update if available
          if (data.span.tokens) {
            handleAgentEvent({
              type: 'token_update',
              timestamp,
              data: {
                agent: data.agent || 'LLM',
                tokens: {
                  used: data.span.tokens.total,
                  model: data.span.model,
                  isOutput: true
                }
              }
            });
          }
        }
        break;
        
      case 'function_span':
        if (data.span && data.tool) {
          const spanId = data.span.id;
          const isCompleted = data.span.duration !== undefined;
          
          traceStore.addSpan({
            id: spanId,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'function',
            name: data.tool,
            startTime: new Date(timestamp),
            status: isCompleted ? 'completed' : 'running',
            endTime: isCompleted ? 
              new Date(new Date(timestamp).getTime() + data.span.duration!) : 
              undefined,
            duration: data.span.duration,
            data: {
              tool: data.tool,
              args: data.span.args,
              result: data.span.result
            }
          });
          
          if (isCompleted) {
            // Tool call completed
            handleAgentEvent({
              type: 'tool_result',
              timestamp,
              data: {
                agent: data.agent || 'Tool',
                tool: data.tool,
                result: data.span.result
              }
            });
          } else {
            // Tool call started
            handleAgentEvent({
              type: 'tool_call',
              timestamp,
              data: {
                agent: data.agent || 'Tool',
                tool: data.tool,
                args: data.span.args
              }
            });
          }
        }
        break;
        
      case 'handoff_span':
        if (data.span) {
          traceStore.addSpan({
            id: data.span.id,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'handoff',
            name: `Handoff: ${data.span.from_agent} → ${data.span.to_agent}`,
            startTime: new Date(timestamp),
            status: 'completed', // Handoffs are typically instantaneous
            duration: 0,
            data: {
              from_agent: data.span.from_agent,
              to_agent: data.span.to_agent,
              message: data.span.message
            }
          });
          
          // Emit handoff event for existing UI
          handleAgentEvent({
            type: 'handoff',
            timestamp,
            data: {
              agent: data.span.from_agent,
              message: `Handoff to ${data.span.to_agent}`,
              result: {
                agent: data.span.to_agent
              }
            }
          });
        }
        break;
        
      case 'guardrail_span':
        if (data.span) {
          traceStore.addSpan({
            id: data.span.id,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'guardrail',
            name: `Guardrail: ${data.span.name}`,
            startTime: new Date(timestamp),
            status: data.span.passed ? 'completed' : 'failed',
            duration: 50, // Assume quick guardrail check
            data: {
              name: data.span.name,
              passed: data.span.passed,
              details: data.span.details
            }
          });
          
          // Emit security/guardrail event
          handleAgentEvent({
            type: data.span.passed ? 'complete' : 'error',
            timestamp,
            data: {
              agent: 'Guardrail',
              message: `${data.span.name}: ${data.span.passed ? 'Passed' : 'Failed'}`,
              result: data.span.details
            }
          });
        }
        break;
        
      case 'custom_span':
        if (data.span) {
          traceStore.addSpan({
            id: data.span.id,
            traceId: data.span.trace_id || 'unknown',
            parentId: data.span.parent_id,
            type: 'custom',
            name: `Custom: ${data.span.type}`,
            startTime: new Date(timestamp),
            status: 'completed',
            data: data.span.data
          });
        }
        break;
    }
    
    // Always forward to existing agent events system
    processEventStream(event);
  }, [traceStore, handleAgentEvent, processEventStream]);
  
  // Initialize trace processor
  useEffect(() => {
    if (!processorInitialized.current) {
      initializeTraceProcessor(handleTraceEvent);
      processorInitialized.current = true;
    } else {
      // Update callback if already initialized
      setTraceEventCallback(handleTraceEvent);
    }
  }, [handleTraceEvent]);
  
  // Update span status when they complete
  const updateSpanStatus = useCallback((spanId: string, status: 'completed' | 'failed', result?: any) => {
    if (status === 'completed') {
      traceStore.completeSpan(spanId, result);
    } else {
      traceStore.failSpan(spanId, result);
    }
  }, [traceStore]);
  
  // Link parent-child spans
  const linkSpans = useCallback((parentId: string, childId: string) => {
    traceStore.linkSpans(parentId, childId);
  }, [traceStore]);
  
  // Access to trace processor for advanced operations
  const getProcessor = useCallback(() => {
    return getTraceProcessor();
  }, []);
  
  return {
    handleTraceEvent,
    updateSpanStatus,
    linkSpans,
    getProcessor,
    // Re-export trace store for convenience
    traceStore
  };
};