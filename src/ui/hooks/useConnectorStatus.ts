/**
 * Hook for managing Google Workspace connector status and operations
 * Provides real-time connection monitoring and OAuth flow management
 */

import { useEffect, useCallback, useState } from 'react';
import { useConnectorStore, ConnectionStatus } from '../stores/connectorStore';
import { ConnectorId } from '../../types/connectors';

interface UseConnectorStatusOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onConnectionChange?: (connectorId: ConnectorId, status: ConnectionStatus) => void;
  onTokenExpiring?: (connectorId: ConnectorId, expiresIn: number) => void;
}

interface ConnectorStatusHook {
  // Connection status
  isConnected: (connectorId: ConnectorId) => boolean;
  getStatus: (connectorId: ConnectorId) => ConnectionStatus;
  getConnectionInfo: (connectorId: ConnectorId) => any;
  allConnectors: Array<{
    id: ConnectorId;
    name: string;
    status: ConnectionStatus;
    isExpiring: boolean;
    error?: string;
  }>;

  // Authentication
  authenticate: (connectorId: ConnectorId) => Promise<void>;
  disconnect: (connectorId: ConnectorId) => Promise<void>;
  refreshToken: (connectorId: ConnectorId) => Promise<void>;
  isAuthenticating: boolean;
  authError?: string;

  // Token management
  checkTokenExpiry: (connectorId: ConnectorId) => { isExpiring: boolean; expiresIn?: number };
  getTokenExpiryTime: (connectorId: ConnectorId) => Date | undefined;

  // Activity monitoring
  hasActiveSearches: boolean;
  activeSearchCount: number;
  getActiveSearches: (connectorId?: ConnectorId) => any[];

  // Approval management
  hasPendingApprovals: boolean;
  pendingApprovalCount: number;
  getPendingApprovals: (connectorId?: ConnectorId) => any[];
}

export function useConnectorStatus(options: UseConnectorStatusOptions = {}): ConnectorStatusHook {
  const {
    autoRefresh = true,
    refreshInterval = 60000, // Check every minute
    onConnectionChange,
    onTokenExpiring
  } = options;

  const {
    connections,
    isAuthenticating,
    authError,
    activeSearches,
    pendingApprovals,
    setConnectionStatus,
    updateConnectorInfo,
    startAuthentication,
    completeAuthentication
  } = useConnectorStore();

  const [refreshTimers, setRefreshTimers] = useState<Map<ConnectorId, NodeJS.Timeout>>(new Map());
  
  // Function to load connector status from backend
  const loadConnectorStatus = useCallback(async () => {
    try {
      const apiBaseUrl = import.meta.env.VITE_API_TARGET || 'http://localhost:3001';
      const response = await fetch(`${apiBaseUrl}/api/connectors/status`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Loaded connector status:', data);
        
        // Update store with connector status
        if (data.connectors && Array.isArray(data.connectors)) {
          data.connectors.forEach((connector: any) => {
            const status: ConnectionStatus = connector.connected ? 'connected' : 'disconnected';
            setConnectionStatus(connector.id, status);
            
            if (connector.connected && connector.lastConnected) {
              updateConnectorInfo(connector.id, {
                status,
                lastConnected: new Date(connector.lastConnected),
                tokenExpiresAt: connector.tokenExpiresAt ? new Date(connector.tokenExpiresAt) : undefined
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('Failed to load connector status:', error);
    }
  }, [setConnectionStatus, updateConnectorInfo]);

  // Check if connector is connected
  const isConnected = useCallback((connectorId: ConnectorId): boolean => {
    const connection = connections.get(connectorId);
    return connection?.status === 'connected';
  }, [connections]);

  // Get connection status
  const getStatus = useCallback((connectorId: ConnectorId): ConnectionStatus => {
    const connection = connections.get(connectorId);
    return connection?.status || 'disconnected';
  }, [connections]);

  // Get full connection info
  const getConnectionInfo = useCallback((connectorId: ConnectorId) => {
    return connections.get(connectorId);
  }, [connections]);

  // Check token expiry
  const checkTokenExpiry = useCallback((connectorId: ConnectorId): { isExpiring: boolean; expiresIn?: number } => {
    const connection = connections.get(connectorId);
    if (!connection?.tokenExpiresAt) {
      return { isExpiring: false };
    }

    const now = new Date();
    const expiresAt = new Date(connection.tokenExpiresAt);
    const expiresIn = expiresAt.getTime() - now.getTime();

    // Consider expiring if less than 5 minutes left
    const isExpiring = expiresIn < 5 * 60 * 1000;

    return {
      isExpiring,
      expiresIn: expiresIn > 0 ? expiresIn : 0
    };
  }, [connections]);

  // Get all connectors with their status
  const allConnectors = Array.from(connections.values()).map(conn => {
    const expiry = checkTokenExpiry(conn.id);
    return {
      id: conn.id,
      name: conn.name,
      status: conn.status,
      isExpiring: expiry.isExpiring,
      error: conn.error
    };
  });

  // Get token expiry time
  const getTokenExpiryTime = useCallback((connectorId: ConnectorId): Date | undefined => {
    const connection = connections.get(connectorId);
    return connection?.tokenExpiresAt;
  }, [connections]);

  // Authenticate a connector
  const authenticate = useCallback(async (connectorId: ConnectorId): Promise<void> => {
    try {
      startAuthentication(connectorId);

      // Determine the service based on connector ID
      const apiBaseUrl = import.meta.env.VITE_API_TARGET || 'http://localhost:3001';
      const serviceMap: Record<ConnectorId, string> = {
        'connector_gmail': 'gmail',
        'connector_googlecalendar': 'calendar',
        'connector_googledrive': 'drive',
        'connector_dropbox': 'dropbox',
        'connector_microsoftteams': 'teams',
        'connector_outlookcalendar': 'calendar',
        'connector_outlookemail': 'email',
        'connector_sharepoint': 'sharepoint'
      };

      const service = serviceMap[connectorId];
      if (!service) {
        throw new Error(`No service configured for ${connectorId}`);
      }

      // Construct the auth URL and open in popup
      const authUrl = `${apiBaseUrl}/api/auth/google?connector=${service}`;
      
      // Open OAuth flow in a popup window
      const width = 500;
      const height = 600;
      const left = window.screen.width / 2 - width / 2;
      const top = window.screen.height / 2 - height / 2;

      const popup = window.open(
        authUrl,
        `${connectorId}_auth`,
        `width=${width},height=${height},left=${left},top=${top},toolbar=no,menubar=no`
      );

      if (!popup) {
        throw new Error('Failed to open authentication window. Please check your popup blocker.');
      }

      // Listen for OAuth messages from popup
      const handleMessage = (event: MessageEvent) => {
        console.log('useConnectorStatus: Received message:', event.data);
        
        if (event.data?.type === 'oauth_success' && event.data?.connectorId) {
          console.log(`useConnectorStatus: OAuth success for ${event.data.connectorId}`);
          window.removeEventListener('message', handleMessage);
          clearInterval(checkInterval);
          clearTimeout(timeoutId);

          // Mark as connected
          completeAuthentication(event.data.connectorId, true);

          // Update connection info with success
          updateConnectorInfo(event.data.connectorId, {
            status: 'connected',
            lastConnected: new Date()
          });
          
          // Force a refresh of connector status from backend
          loadConnectorStatus();
        } else if (event.data?.type === 'oauth_error') {
          console.log(`useConnectorStatus: OAuth error:`, event.data.error);
          window.removeEventListener('message', handleMessage);
          clearInterval(checkInterval);
          clearTimeout(timeoutId);

          // Mark as failed
          completeAuthentication(connectorId, false, event.data.error || 'Authentication failed');
        }
      };

      window.addEventListener('message', handleMessage);

      // Track when we started checking
      const startTime = Date.now();

      // Set a timeout to clean up after 2 minutes
      const timeoutId = setTimeout(() => {
        clearInterval(checkInterval);
        window.removeEventListener('message', handleMessage);
        completeAuthentication(connectorId, false, 'Authentication timeout');
      }, 120000); // 2 minutes

      // Also check if popup is closed without success
      const checkInterval = setInterval(() => {
        try {
          // Try to check if popup is closed
          // This might fail due to COOP (Cross-Origin-Opener-Policy) restrictions
          if (popup && !popup.closed) {
            // Popup is still open, continue checking
            return;
          }
        } catch (e) {
          // COOP restriction - can't check popup.closed
          // Just wait for the message event or timeout
          return;
        }

        // Popup was closed
        clearInterval(checkInterval);
        clearTimeout(timeoutId);
        window.removeEventListener('message', handleMessage);

        // If popup closed without success message, check status from backend
        setTimeout(() => {
          // Check authentication status with the connector service
          fetch(`${apiBaseUrl}/api/connectors/status`, { credentials: 'include' })
            .then(res => res.json())
            .then(data => {
              const connector = data.connectors?.find((c: any) => c.id === connectorId);
              if (connector?.connected) {
                completeAuthentication(connectorId, true);
              } else {
                completeAuthentication(connectorId, false, 'Authentication cancelled');
              }
            })
            .catch(() => {
              completeAuthentication(connectorId, false, 'Failed to verify authentication');
            });
        }, 500);
      }, 1000);

    } catch (error) {
      console.error(`Authentication failed for ${connectorId}:`, error);
      completeAuthentication(connectorId, false, error instanceof Error ? error.message : 'Authentication failed');
      throw error;
    }
  }, [startAuthentication, completeAuthentication, updateConnectorInfo]);

  // Disconnect a connector
  const disconnect = useCallback(async (connectorId: ConnectorId): Promise<void> => {
    try {
      // Call backend to revoke tokens
      const apiBaseUrl = import.meta.env.VITE_API_TARGET || 'http://localhost:3001';
      const response = await fetch(`${apiBaseUrl}/api/auth/revoke`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ connectorId }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to revoke tokens');
      }

      setConnectionStatus(connectorId, 'disconnected');
    } catch (error) {
      console.error(`Disconnect failed for ${connectorId}:`, error);
      throw error;
    }
  }, [setConnectionStatus]);

  // Refresh token for a connector
  const refreshToken = useCallback(async (connectorId: ConnectorId): Promise<void> => {
    try {
      setConnectionStatus(connectorId, 'refreshing');

      // Call backend to refresh token
      const apiBaseUrl = import.meta.env.VITE_API_TARGET || 'http://localhost:3001';
      const response = await fetch(`${apiBaseUrl}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ connectorId }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();

      // Update connection info with new expiry
      updateConnectorInfo(connectorId, {
        status: 'connected',
        tokenExpiresAt: new Date(data.expiresAt)
      });

    } catch (error) {
      console.error(`Token refresh failed for ${connectorId}:`, error);
      setConnectionStatus(connectorId, 'error', error instanceof Error ? error.message : 'Token refresh failed');
      throw error;
    }
  }, [setConnectionStatus, updateConnectorInfo]);

  // Check for active searches
  const hasActiveSearches = activeSearches.length > 0;
  const activeSearchCount = activeSearches.length;

  const getActiveSearches = useCallback((connectorId?: ConnectorId) => {
    if (connectorId) {
      return activeSearches.filter(search => search.connectorId === connectorId);
    }
    return activeSearches;
  }, [activeSearches]);

  // Check for pending approvals
  const hasPendingApprovals = pendingApprovals.filter(a => a.status === 'pending').length > 0;
  const pendingApprovalCount = pendingApprovals.filter(a => a.status === 'pending').length;

  const getPendingApprovals = useCallback((connectorId?: ConnectorId) => {
    const pending = pendingApprovals.filter(a => a.status === 'pending');
    if (connectorId) {
      return pending.filter(a => a.connectorId === connectorId);
    }
    return pending;
  }, [pendingApprovals]);

  // Load initial connector status on mount
  useEffect(() => {
    loadConnectorStatus();
  }, [loadConnectorStatus]);

  // Set up automatic token refresh checks
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      connections.forEach((connection, connectorId) => {
        if (connection.status === 'connected') {
          const expiry = checkTokenExpiry(connectorId);

          // Notify if token is expiring soon
          if (expiry.isExpiring && expiry.expiresIn && onTokenExpiring) {
            onTokenExpiring(connectorId, expiry.expiresIn);
          }

          // Auto-refresh if less than 2 minutes left
          if (expiry.expiresIn && expiry.expiresIn < 2 * 60 * 1000) {
            refreshToken(connectorId).catch(console.error);
          }

          // Mark as expiring if less than 5 minutes left
          if (expiry.isExpiring && connection.status === 'connected') {
            setConnectionStatus(connectorId, 'expiring');
          }
        }
      });
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, connections, checkTokenExpiry, onTokenExpiring, refreshToken, setConnectionStatus]);

  // Monitor connection changes
  useEffect(() => {
    if (!onConnectionChange) return;

    connections.forEach((connection, connectorId) => {
      onConnectionChange(connectorId, connection.status);
    });
  }, [connections, onConnectionChange]);

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      refreshTimers.forEach(timer => clearTimeout(timer));
    };
  }, [refreshTimers]);

  return {
    // Connection status
    isConnected,
    getStatus,
    getConnectionInfo,
    allConnectors,

    // Authentication
    authenticate,
    disconnect,
    refreshToken,
    isAuthenticating,
    authError,

    // Token management
    checkTokenExpiry,
    getTokenExpiryTime,

    // Activity monitoring
    hasActiveSearches,
    activeSearchCount,
    getActiveSearches,

    // Approval management
    hasPendingApprovals,
    pendingApprovalCount,
    getPendingApprovals
  };
}
