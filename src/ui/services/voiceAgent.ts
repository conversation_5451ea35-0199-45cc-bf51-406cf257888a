import { z } from 'zod';
import { voiceConfig } from '../../config/voiceConfig';
import { sessionManager } from '../../utils/sessionManager';

// Lazy load the realtime module to prevent initialization errors
let RealtimeAgent: any;
let RealtimeSession: any;
let tool: any;

// Patch global objects that might be missing in remote contexts
const patchGlobalObjects = () => {
  // Ensure WebSocket is available
  if (typeof window !== 'undefined' && !window.WebSocket && typeof WebSocket !== 'undefined') {
    (window as any).WebSocket = WebSocket;
  }
  
  // Patch any missing bind issues by ensuring Function.prototype.bind exists
  if (typeof Function.prototype.bind === 'undefined') {
    Function.prototype.bind = function(oThis: any) {
      if (typeof this !== 'function') {
        throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');
      }
      const aArgs = Array.prototype.slice.call(arguments, 1);
      const fToBind = this;
      const fNOP = function() {} as any;
      const fBound = function(this: any) {
        return fToBind.apply(this instanceof fNOP ? this : oThis,
          aArgs.concat(Array.prototype.slice.call(arguments)));
      };
      if (this.prototype) {
        fNOP.prototype = this.prototype;
      }
      fBound.prototype = new fNOP();
      return fBound;
    };
  }
  
  // Ensure performance.now is available
  if (typeof window !== 'undefined' && !window.performance) {
    (window as any).performance = {
      now: () => Date.now()
    };
  }
};

const loadRealtimeModule = async () => {
  if (!RealtimeAgent || !RealtimeSession || !tool) {
    try {
      // Apply patches before loading the module
      patchGlobalObjects();
      
      // Realtime functionality temporarily disabled during migration
      throw new Error('Realtime module temporarily disabled during OpenAI SDK to Vercel AI SDK migration');
      
      // Code temporarily disabled
    } catch (error) {
      console.error('Failed to load realtime module:', error);
      
      // Instead of completely failing, we could fall back to a mock implementation
      // that at least allows the UI to function
      throw new Error('Voice features require a modern browser with WebRTC support. Please use Chrome, Edge, or Safari.');
    }
  }
};

export type VoiceStatus = 'idle' | 'connecting' | 'listening' | 'speaking' | 'error' | 'stopped';

export interface VoiceCallbacks {
  onStatus?: (status: VoiceStatus, info?: any) => void;
  onHistoryUpdate?: (history: any[]) => void;
  onTranscription?: (text: string) => void;
}

class VoiceAgentService {
  private agent: any | null = null;
  private session: any | null = null;
  private mode: 'realtime' | 'chained' = 'realtime';
  private status: VoiceStatus = 'idle';
  private callbacks: VoiceCallbacks = {};
  private history: any[] = [];
  private audioEl: HTMLAudioElement | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  // Browser speech recognition (for live transcript preview)
  private recognition: any | null = null;
  private recognitionSupported: boolean = typeof window !== 'undefined' && (!!(window as any).SpeechRecognition || !!(window as any).webkitSpeechRecognition);
  // Language configuration for transcription
  private transcriptionLanguage: string = 'en';
  // Reusable AudioContext for TTS streaming to avoid performance issues
  private audioContext: AudioContext | null = null;
  private preferredVoice: string = voiceConfig.voice;

  constructor() {
    // Load saved language preference from localStorage
    const savedLanguage = localStorage.getItem('dante-transcription-language');
    if (savedLanguage) {
      this.transcriptionLanguage = savedLanguage;
    }
  }

  private getAudioContext(): AudioContext {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    return this.audioContext;
  }

  setCallbacks(cb: VoiceCallbacks) {
    this.callbacks = cb;
  }

  setTranscriptionLanguage(language: string) {
    this.transcriptionLanguage = language;
    // Persist to localStorage
    localStorage.setItem('dante-transcription-language', language);
  }

  getTranscriptionLanguage(): string {
    return this.transcriptionLanguage;
  }

  /**
   * Convert Whisper language code to browser SpeechRecognition language code
   * Browser uses BCP 47 tags (en-US) while Whisper uses ISO 639-1 (en)
   */
  private getBrowserLanguageCode(whisperCode: string): string {
    const languageMap: Record<string, string> = {
      'en': 'en-US',
      'es': 'es-ES', 
      'fr': 'fr-FR',
      'de': 'de-DE',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'zh': 'zh-CN',
      'ar': 'ar-SA',
      'hi': 'hi-IN',
      'tr': 'tr-TR',
      'pl': 'pl-PL',
      'nl': 'nl-NL',
      'sv': 'sv-SE',
      'da': 'da-DK',
      'no': 'nb-NO',
      'fi': 'fi-FI',
      'cs': 'cs-CZ',
      'sk': 'sk-SK',
      'hu': 'hu-HU',
      'ro': 'ro-RO',
      'bg': 'bg-BG',
      'hr': 'hr-HR',
      'sr': 'sr-RS',
      'sl': 'sl-SI',
      'et': 'et-EE',
      'lv': 'lv-LV',
      'lt': 'lt-LT',
      'uk': 'uk-UA',
      'el': 'el-GR',
      'iw': 'he-IL', // Hebrew: Whisper uses 'iw', browser uses 'he'
      'th': 'th-TH',
      'vi': 'vi-VN',
      'id': 'id-ID',
      'ms': 'ms-MY',
      'tl': 'tl-PH',
      'ta': 'ta-IN',
      'te': 'te-IN',
      'kn': 'kn-IN',
      'ml': 'ml-IN',
      'bn': 'bn-IN',
      'gu': 'gu-IN',
      'mr': 'mr-IN',
      'pa': 'pa-IN',
      'ur': 'ur-PK',
      'ne': 'ne-NP',
      'si': 'si-LK',
      'my': 'my-MM',
      'km': 'km-KH',
      'lo': 'lo-LA',
      'ka': 'ka-GE',
      'am': 'am-ET',
      'af': 'af-ZA',
      'sw': 'sw-TZ',
      'zu': 'zu-ZA',
      'fa': 'fa-IR',
      'uz': 'uz-UZ',
      'kk': 'kk-KZ',
      'mn': 'mn-MN',
      'cy': 'cy-GB',
      'ga': 'ga-IE',
      'eu': 'eu-ES',
      'ca': 'ca-ES',
      'gl': 'gl-ES',
      'mt': 'mt-MT',
      'is': 'is-IS',
      'fo': 'fo-FO',
    };

    // Return mapped language or fallback to English
    return languageMap[whisperCode] || 'en-US';
  }

  getSession() {
    return this.session;
  }

  getStatus(): VoiceStatus {
    return this.status;
  }

  private setStatus(next: VoiceStatus, info?: any) {
    this.status = next;
    this.callbacks.onStatus?.(next, info);

    // In realtime mode, run live speech recognition while listening
    if (this.mode === 'realtime') {
      if (next === 'listening') {
        this.startSpeechRecognition();
      } else if (next === 'speaking' || next === 'connecting' || next === 'error' || next === 'stopped' || next === 'idle') {
        this.stopSpeechRecognition();
      }
    }
  }

  private async ensureMicrophonePermission(): Promise<void> {
    console.log('🎤 ensureMicrophonePermission called');
    
    // Check if we're in a browser environment
    if (typeof navigator === 'undefined') {
      console.log('🎤 Navigator undefined, returning');
      return;
    }

    // Enforce secure context for microphone access
    if (typeof window !== 'undefined') {
      const isLocalhost = /^(localhost|127\.0\.0\.1)$/.test(window.location.hostname);
      console.log('🎤 Security check - isSecureContext:', window.isSecureContext, 'isLocalhost:', isLocalhost);
      if (!window.isSecureContext && !isLocalhost) {
        throw new Error('Microphone access requires a secure context. Open the app on https:// or use http://localhost.');
      }
    }

    // Check if getUserMedia is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.log('🎤 getUserMedia not available');
      throw new Error('Microphone API not supported in this browser.');
    }

    try {
      // Only check current permission status, don't fail on 'prompt' state
      if (navigator.permissions) {
        try {
          const permission = await navigator.permissions.query({ name: 'microphone' as any });
          console.log('🎤 Current permission state:', permission.state);
          if (permission.state === 'denied') {
            throw new Error('Microphone permission is blocked. Enable it in your browser settings and reload.');
          }
          if (permission.state === 'granted') {
            console.log('🎤 Permission already granted, skipping request');
            return; // Already granted, no need to request
          }
          // State is 'prompt' - continue to getUserMedia which will show the prompt
        } catch (permissionError) {
          // Permission API not fully supported, proceed with getUserMedia
          console.warn('🎤 Permission API query failed, proceeding with getUserMedia:', permissionError);
        }
      }

      console.log('🎤 About to request microphone access via getUserMedia...');
      
      // Create a timeout promise with clear guidance
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Permission dialog did not appear. Please check: 1) Click the lock icon in address bar and enable microphone, 2) Ensure site permissions allow microphone access, 3) Try refreshing the page.'));
        }, 15000); // Increased timeout to 15 seconds
      });

      // Request microphone access
      console.log('🎤 Requesting microphone permission...');
      const getUserMediaPromise = navigator.mediaDevices.getUserMedia({ 
        audio: true,
        video: false 
      });

      console.log('🎤 Waiting for permission dialog... (Check address bar for lock icon if no dialog appears)');
      const stream = await Promise.race([getUserMediaPromise, timeoutPromise]);

      console.log('🎤 Permission granted! Received stream:', stream, 'Active tracks:', stream.getTracks().length);

      // Permission granted! Clean up the test stream
      stream.getTracks().forEach(track => track.stop());
      console.log('🎤 Test stream cleaned up');

    } catch (error: any) {
      console.error('🎤 getUserMedia error:', error.name, error.message);
      
      // Handle specific getUserMedia errors that occur AFTER user interaction
      if (error.name === 'NotAllowedError') {
        throw new Error('Microphone access denied. Please allow microphone permissions in your browser.');
      } else if (error.name === 'NotFoundError') {
        throw new Error('No microphone found on this device.');
      } else if (error.name === 'NotSupportedError') {
        throw new Error('Microphone access not supported in this browser.');
      } else if (error.name === 'NotReadableError') {
        throw new Error('Microphone is already in use by another application.');
      } else if (error.name === 'OverconstrainedError') {
        throw new Error('Microphone constraints could not be satisfied.');
      } else if (error.name === 'SecurityError') {
        throw new Error('Microphone access blocked due to security restrictions.');
      } else if (error.name === 'AbortError') {
        throw new Error('Microphone access request was cancelled.');
      }
      
      // Re-throw the original error if it's already a custom error message
      throw error;
    }
  }

  private isRealtimeModel(model?: string): boolean {
    const m = (model || '').toLowerCase();
    return /^gpt-4o(-mini)?-realtime-preview/.test(m);
  }

  async start(options?: { voice?: string; model?: string; syncTypedHistory?: boolean }): Promise<void> {
    if (this.session) return; // already running

    this.setStatus('connecting');

    const modelToUse = options?.model || voiceConfig.model;
    const voiceToUse = options?.voice || this.preferredVoice || voiceConfig.voice;
    this.preferredVoice = voiceToUse;
    this.mode = this.isRealtimeModel(modelToUse) ? 'realtime' : 'chained';

    // Load realtime module if needed
    if (this.mode === 'realtime') {
      try {
        await loadRealtimeModule();
      } catch (error: any) {
        console.error('Failed to load realtime module, falling back to chained mode:', error);
        
        // Instead of failing completely, fall back to chained mode
        this.mode = 'chained';
        this.setStatus('connecting', { 
          message: 'Using fallback voice mode due to browser limitations',
          fallback: true 
        });
        
        // Don't throw - let it continue with chained mode
        // throw error;
      }
    }

    // Create delegate tool only for realtime mode
    let delegateToDante: any = null;
    if (this.mode === 'realtime' && tool) {
      const delegateParams = z.object({ request: z.string() });
      delegateToDante = tool({
        name: 'delegateToDante',
        description: 'Delegate complex tasks to the full Dante system and return the result summary.',
        parameters: delegateParams,
        execute: async ({ request }: { request: string }, details: any) => {
          const history = details?.context?.history ?? [];
          // Enrich with session id so server can route events
          let sessionId: string | undefined;
          try {
            const current = await sessionManager.getCurrentSession();
            sessionId = current?.id;
          } catch {}
          const resp = await fetch('/api/voice/delegate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ request, history, sessionId }),
          });
          const data = await resp.json();
          if (!data?.success) {
            throw new Error(data?.error || 'Delegation failed');
          }
          return data.text || 'Done.';
        },
      });
    }

    if (this.mode === 'realtime' && RealtimeAgent && RealtimeSession) {
      try {
        // Create voice agent
        this.agent = new RealtimeAgent({
          name: 'Dante Voice Agent',
          instructions: voiceConfig.instructions,
          tools: delegateToDante ? [delegateToDante] : [],
        });

        // Configure session
        this.session = new RealtimeSession(this.agent, {
        model: modelToUse,
        config: {
          inputAudioFormat: 'pcm16',
          outputAudioFormat: 'pcm16',
          turnDetection: {
            type: 'semantic_vad',
            eagerness: 'medium',
            createResponse: true,
            interruptResponse: true,
          },
        },
      });

      // Attach listeners
      this.session.on('history_updated', (history: any[]) => {
        this.callbacks.onHistoryUpdate?.(history);
        const last = [...history].reverse().find((h: any) => h?.type === 'message');
        if (last?.role === 'assistant') this.setStatus('speaking');
        else this.setStatus('listening');
      });
      this.session.on('audio_interrupted', () => {
        // Move back to listening after interruption
        this.setStatus('listening');
      });
      this.session.on('error', (err: any) => {
        console.error('Voice session error:', err);
        this.setStatus('error', err);
      });

      // Ensure mic permission before connecting so the browser prompts immediately
      try {
        await this.ensureMicrophonePermission();
      } catch (e: any) {
        this.setStatus('error', e);
        throw e;
      }

      // Fetch ephemeral token and connect (WebRTC by default in browser)
      const tokenResp = await fetch('/api/voice/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ model: modelToUse, voice: voiceToUse }),
      });
      const tokenJson = await tokenResp.json();
      if (!tokenResp.ok || !tokenJson?.success) {
        throw new Error(tokenJson?.message || 'Failed to create realtime session token');
      }
      const apiKey = tokenJson.session?.client_secret?.value;
      if (!apiKey) throw new Error('Ephemeral token missing in response');

      await this.session.connect({ apiKey });

      // Optionally sync the typed chat history into the voice session
      if (options?.syncTypedHistory !== false) {
        try {
          const current = await sessionManager.getCurrentSession();
          const historyItems: any[] = [];
          if (current?.messages?.length) {
            for (const m of current.messages.slice(-30)) {
              const role = m.role === 'assistant' ? 'assistant' : 'user';
              const text = typeof m.content === 'string' ? m.content : '';
              if (text) historyItems.push({ type: 'message', role, content: text });
            }
          }
          if (historyItems.length) {
            this.session.updateHistory((prev: any[]) => {
              const systemOnly = prev.filter((i: any) => i?.role === 'system');
              return [...systemOnly, ...historyItems];
            });
          }
        } catch (e) {
          console.warn('Failed to sync typed history into voice session:', e);
        }
      }
        // When connected, the WebRTC transport will automatically handle mic I/O
        this.setStatus('listening');
      } catch (error) {
        console.error('Failed to create realtime session:', error);
        this.setStatus('error', { message: 'Failed to initialize voice session' });
        // Fallback to chained mode
        this.mode = 'chained';
        this.agent = null;
        this.session = null;
        this.history = [];
        this.callbacks.onHistoryUpdate?.(this.history);
        this.setStatus('listening');
      }
    } else {
      // Chained TTS fallback: no realtime session, maintain local history and use server routes
      this.agent = null;
      this.session = null;
      this.history = [];

      // Optionally sync recent typed history to local voice history for continuity
      if (options?.syncTypedHistory !== false) {
        try {
          const current = await sessionManager.getCurrentSession();
          if (current?.messages?.length) {
            for (const m of current.messages.slice(-12)) {
              const role = m.role === 'assistant' ? 'assistant' : 'user';
              const text = typeof m.content === 'string' ? m.content : '';
              if (text) this.history.push({ type: 'message', role, content: text });
            }
          }
        } catch (e) {
          // non-fatal
        }
      }
      this.callbacks.onHistoryUpdate?.(this.history);
      this.setStatus('listening');
    }
  }

  async stop(): Promise<void> {
    try {
      if (this.session) {
        this.session.close();
      }
      if (this.audioEl) {
        try { this.audioEl.pause(); } catch {}
        this.audioEl = null;
      }
      if (this.audioContext && this.audioContext.state !== 'closed') {
        try { await this.audioContext.close(); } catch {}
        this.audioContext = null;
      }
    } finally {
      this.session = null;
      this.agent = null;
      this.history = [];
      this.setStatus('stopped');
    }
  }

  async interrupt(): Promise<void> {
    if (!this.session) return;
    this.session.interrupt();
    this.setStatus('listening');
  }

  async sendText(message: string, opts?: { mode?: 'ask' | 'say' }) {
    if (this.mode === 'realtime') {
      if (!this.session) return;
      this.session.sendMessage(message);
      return;
    }

    const mode = opts?.mode || 'say';
      const voice = this.preferredVoice || voiceConfig.voice;

    if (mode === 'ask') {
      // Push user turn to local history
      this.history.push({ type: 'message', role: 'user', content: message });
      this.callbacks.onHistoryUpdate?.(this.history);

      // Delegate to Dante with voice-integrated orchestration
      try {
        this.setStatus('connecting', { processing: true });
        
        const current = await sessionManager.getCurrentSession();
        const sessionId = current?.id;
        
        // The orchestration system will handle TTS callback automatically
        const resp = await fetch('/api/voice/delegate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            request: message, 
            history: this.history, 
            sessionId,
            voiceMode: true,
            voice,
          }),
        });
        const data = await resp.json();
        const finalText = data?.voiceText || data?.text || '';
        
        if (finalText) {
          // Update local history with assistant turn
          this.history.push({ type: 'message', role: 'assistant', content: finalText });
          this.callbacks.onHistoryUpdate?.(this.history);
          
          // Prefer the voice-optimized text if provided by the API
          await this.playTTS(finalText, voice);
        }
      } catch (e) {
        console.warn('Chained voice ask failed:', e);
        this.setStatus('error', e);
      }
      return;
    }

    // mode === 'say' → just speak the provided text
    try {
      await this.playTTS(message, voice);
    } catch (e) {
      console.warn('Chained voice say failed:', e);
      this.setStatus('error', e);
    }
  }

  private async playTTS(text: string, voice: string, useStreaming: boolean = true) {
    this.setStatus('speaking');
    if (this.audioEl) {
      try { this.audioEl.pause(); } catch {}
      this.audioEl = null;
    }

    if (useStreaming && text.length > 100) {
      // Use chunked streaming for better latency
      return this.playTTSStreaming(text, voice);
    } else {
      // Use regular TTS for short responses
      return this.playTTSRegular(text, voice);
    }
  }

  private async playTTSRegular(text: string, voice: string) {
    const resp = await fetch('/api/voice/say', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text, voice }),
    });
    if (!resp.ok) throw new Error('TTS synthesis failed');
    const blob = await resp.blob();
    const url = URL.createObjectURL(blob);
    const audio = new Audio(url);
    this.audioEl = audio;
    return new Promise<void>((resolve) => {
      audio.onended = () => {
        try { URL.revokeObjectURL(url); } catch {}
        if (this.audioEl === audio) this.audioEl = null;
        this.setStatus('listening');
        resolve();
      };
      audio.onerror = () => {
        try { URL.revokeObjectURL(url); } catch {}
        if (this.audioEl === audio) this.audioEl = null;
        this.setStatus('error');
        resolve();
      };
      audio.play().catch(() => {
        this.setStatus('error');
        resolve();
      });
    });
  }

  private async playTTSStreaming(text: string, voice: string) {
    return new Promise<void>((resolve) => {
      // Use fetch with streaming (EventSource doesn't support POST)
      fetch('/api/voice/say/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, voice, format: 'pcm' }),
      }).then(response => {
        if (!response.ok) throw new Error('Streaming TTS failed');
        
        const reader = response.body?.getReader();
        if (!reader) throw new Error('No response body');
        
        const decoder = new TextDecoder();
        const audioContext = this.getAudioContext();
        let isFirst = true;
        
        const processChunk = async ({ done, value }: any) => {
          if (done) {
            this.setStatus('listening');
            resolve();
            return;
          }
          
          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.type === 'chunk' && data.data) {
                  // Decode base64 audio and play it
                  const audioData = Uint8Array.from(atob(data.data), c => c.charCodeAt(0));
                  
                  if (data.format === 'pcm') {
                    // Play PCM audio directly using Web Audio API
                    const audioBuffer = await audioContext.decodeAudioData(audioData.buffer);
                    const source = audioContext.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(audioContext.destination);
                    
                    if (isFirst) {
                      // Start immediately on first chunk
                      source.start(0);
                      isFirst = false;
                    } else {
                      // Queue subsequent chunks
                      source.start(audioContext.currentTime + 0.1);
                    }
                  } else {
                    // For MP3/WAV, create blob and play with Audio element
                    const blob = new Blob([audioData], { 
                      type: data.format === 'wav' ? 'audio/wav' : 'audio/mpeg' 
                    });
                    const url = URL.createObjectURL(blob);
                    const audio = new Audio(url);
                    
                    if (isFirst) {
                      this.audioEl = audio;
                      isFirst = false;
                    }
                    
                    audio.play().catch(console.error);
                    audio.onended = () => URL.revokeObjectURL(url);
                  }
                } else if (data.type === 'complete') {
                  this.setStatus('listening');
                  resolve();
                  return;
                } else if (data.type === 'error') {
                  console.error('Streaming TTS error:', data.message);
                  this.setStatus('error');
                  resolve();
                  return;
                }
              } catch (e) {
                console.error('Failed to parse streaming chunk:', e);
              }
            }
          }
          
          reader.read().then(processChunk);
        };
        
        reader.read().then(processChunk);
      }).catch(error => {
        console.error('Streaming TTS failed:', error);
        this.setStatus('error');
        resolve();
      });
    });
  }

  // Push-to-talk recording methods for chained mode
  async startRecording(): Promise<void> {
    if (this.mode === 'realtime') {
      console.warn('Recording not needed in realtime mode');
      return;
    }

    try {
      await this.ensureMicrophonePermission();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/wav'
      });
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(this.recordedChunks, { 
          type: this.mediaRecorder?.mimeType || 'audio/webm' 
        });
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
        
        // Transcribe the audio
        await this.transcribeAndProcess(audioBlob);
      };

      this.mediaRecorder.start();
      this.setStatus('listening', { recording: true });
      // Start live speech recognition preview in chained mode
      if (this.mode === 'chained') {
        this.startSpeechRecognition();
      }
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.setStatus('error', error);
      throw error;
    }
  }

  async stopRecording(): Promise<void> {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.setStatus('idle', { recording: false });
    }
    // Stop live speech recognition for chained push-to-talk mode
    if (this.mode === 'chained') {
      this.stopSpeechRecognition();
    }
  }

  private async transcribeAndProcess(audioBlob: Blob): Promise<void> {
    try {
      this.setStatus('connecting', { transcribing: true });
      
      // Create FormData for upload
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      formData.append('language', this.transcriptionLanguage);

      // Transcribe the audio
      const response = await fetch('/api/voice/transcribe', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Transcription failed');
      }

      const data = await response.json();
      const transcribedText = data.text;
      
      if (transcribedText && transcribedText.trim()) {
        // Notify listeners of transcription
        this.callbacks.onTranscription?.(transcribedText);
        
        // Process the transcribed text as an "ask" request
        await this.sendText(transcribedText, { mode: 'ask' });
      } else {
        console.warn('No text transcribed from audio');
        this.setStatus('listening');
      }
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
      this.setStatus('error', error);
    }
  }

  getMode(): 'realtime' | 'chained' {
    return this.mode;
  }

  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording';
  }

  // --- Live speech recognition (client-side) ---
  private startSpeechRecognition() {
    try {
      if (!this.recognitionSupported) return;
      if (this.recognition) return; // already running

      const SR: any = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      if (!SR) return;

      const recognition = new SR();
      recognition.lang = this.getBrowserLanguageCode(this.transcriptionLanguage);
      recognition.continuous = true;
      recognition.interimResults = true;

      recognition.onresult = (event: any) => {
        let interim = '';
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const res = event.results[i];
          if (!res) continue;
          const text = res[0]?.transcript || '';
          if (!text) continue;
          if (res.isFinal) {
            // Send the final chunk too so the UI shows it before server result returns
            this.callbacks.onTranscription?.(text.trim());
          } else {
            interim += text + ' ';
          }
        }
        if (interim.trim()) {
          this.callbacks.onTranscription?.(interim.trim());
        }
      };

      recognition.onerror = (_e: any) => {
        // Non-fatal; stop preview on errors
        this.stopSpeechRecognition();
      };

      recognition.onend = () => {
        // Auto-restart only in realtime listening state
        if (this.mode === 'realtime' && this.status === 'listening') {
          try { recognition.start(); } catch { /* ignore */ }
        } else {
          this.stopSpeechRecognition();
        }
      };

      // For chained (push-to-talk), start only when actively recording
      if (this.mode === 'chained') {
        if (this.mediaRecorder?.state === 'recording') {
          recognition.start();
          this.recognition = recognition;
        }
      } else {
        recognition.start();
        this.recognition = recognition;
      }
    } catch {
      // ignore; fallback is just status indicators
      this.stopSpeechRecognition();
    }
  }

  private stopSpeechRecognition() {
    const rec = this.recognition;
    if (!rec) return;
    try { rec.onresult = null; } catch {}
    try { rec.onend = null; } catch {}
    try { rec.onerror = null; } catch {}
    try { rec.stop(); } catch {}
    this.recognition = null;
}

  setPreferredVoice(voice: string) {
    this.preferredVoice = voice;
  }
}

export const voiceAgentService = new VoiceAgentService();

// Make the voice agent globally accessible for settings
declare global {
  interface Window {
    voiceAgent: VoiceAgentService;
  }
}

// Only attach to window when DOM is fully loaded and in a proper browser context
if (typeof window !== 'undefined') {
  // Check if we're in a proper browser environment with all APIs available
  const isBrowserReady = () => {
    return !!(
      window.AudioContext || 
      (window as any).webkitAudioContext
    ) && !!(
      window.navigator &&
      window.navigator.mediaDevices
    );
  };

  if (isBrowserReady()) {
    window.voiceAgent = voiceAgentService;
  } else {
    // Defer initialization until the browser is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        if (isBrowserReady()) {
          window.voiceAgent = voiceAgentService;
        }
      });
    } else {
      // DOM already loaded, wait a bit for APIs to be available
      setTimeout(() => {
        if (isBrowserReady()) {
          window.voiceAgent = voiceAgentService;
        }
      }, 100);
    }
  }
}
