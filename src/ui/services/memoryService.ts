import { Memory, MemoryQuery, MemorySearchResult, MemoryStats, SemanticQuery, MemoryOperationResult } from '../../memory/types';

// Use a relative API base so the service works in production and with proxies/reverse-proxies
const API_BASE = '/api/memory';

class MemoryService {
  async search(query: MemoryQuery): Promise<MemorySearchResult> {
    const response = await fetch(`${API_BASE}/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(query),
    });
    
    if (!response.ok) {
      throw new Error('Failed to search memories');
    }
    
    return response.json();
  }

  async semanticSearch(query: SemanticQuery): Promise<MemorySearchResult> {
    const response = await fetch(`${API_BASE}/semantic-search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(query),
    });
    
    if (!response.ok) {
      throw new Error('Failed to search memories');
    }
    
    return response.json();
  }

  async getStats(): Promise<MemoryStats> {
    const response = await fetch(`${API_BASE}/stats`);
    
    if (!response.ok) {
      throw new Error('Failed to get memory stats');
    }
    
    return response.json();
  }

  async delete(memoryId: string): Promise<MemoryOperationResult> {
    const response = await fetch(`${API_BASE}/${memoryId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete memory');
    }
    
    return response.json();
  }

  async consolidate(): Promise<void> {
    const response = await fetch(`${API_BASE}/consolidate`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to consolidate memories');
    }
  }

  async exportMemories(): Promise<string> {
    const response = await fetch(`${API_BASE}/export`);
    
    if (!response.ok) {
      throw new Error('Failed to export memories');
    }
    
    const result = await response.json();
    return result.data;
  }

  async importMemories(data: string): Promise<MemoryOperationResult> {
    const response = await fetch(`${API_BASE}/import`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ data }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to import memories');
    }
    
    return response.json();
  }

  // Stub methods to match the MemoryManager interface
  isReady(): boolean {
    return true; // Always ready when using API
  }

  async initialize(): Promise<void> {
    // No-op for API-based service
  }
}

export const memoryService = new MemoryService();