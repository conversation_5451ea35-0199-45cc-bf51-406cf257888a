import { useState, useEffect, useRef, useCallback, lazy, Suspense } from 'react';
import ChatInterface, { AgentEvent } from './components/ChatInterface';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import { ToastContainer } from './components/Toast';
import { X } from 'lucide-react';
import { useProjectStore } from './stores/projectStore';
import { ModelInfo, getDefaultModel, getModelById } from '../types/models';
import { sessionManager } from '../utils/sessionManager';
import { useLayoutStore } from './stores/layoutStore';
import { useAgentEvents } from './hooks/useAgentEvents';
import { useThoughtStore } from './stores/thoughtStore';
import { useVoiceStore } from './stores/voiceStore';
import './styles/orchestration.css';

// Lazy load heavy/optional components
const ConversationHistory = lazy(() => import('./components/ConversationHistory'));
const MemoryPanel = lazy(() => import('./components/MemoryPanel').then(m => ({ default: m.MemoryPanel })));
const MCPPanel = lazy(() => import('./components/MCPPanel').then(m => ({ default: m.MCPPanel })));
const ProjectExplorer = lazy(() => import('./components/ProjectExplorer'));
const ShareSessionDialog = lazy(() => import('./components/ShareSessionDialog').then(m => ({ default: m.ShareSessionDialog })));
const VoiceSettings = lazy(() => import('./components/VoiceSettings'));

// Lazy load orchestration components (heavy features)
const FloatingChatWindow = lazy(() => import('./components/orchestration/FloatingChatWindow').then(m => ({ default: m.FloatingChatWindow })));
const TaskPanel = lazy(() => import('./components/orchestration/TaskPanel').then(m => ({ default: m.TaskPanel })));
const AgentGrid = lazy(() => import('./components/orchestration/AgentGrid').then(m => ({ default: m.AgentGrid })));
const ResourceMonitor = lazy(() => import('./components/orchestration/ResourceMonitor').then(m => ({ default: m.ResourceMonitor })));


// Loading component for Suspense fallbacks
const LoadingFallback = ({ message = "Loading..." }: { message?: string }) => (
  <div className="flex items-center justify-center p-4">
    <div className="text-gray-600 dark:text-gray-400">{message}</div>
  </div>
);

function App() {
  const [messages, setMessages] = useState<Array<{
    role: string;
    content: string;
    reasoning?: string;
    confidence?: number;
    isStreaming?: boolean;
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(
    localStorage.getItem('darkMode') === 'true' ||
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );
  const [agentEvents, setAgentEvents] = useState<AgentEvent[]>([]);
  const [isThinking, setIsThinking] = useState(false);
  const [currentAgent, setCurrentAgent] = useState('Dante');
  const currentAbortRef = useRef<AbortController | null>(null);
  const [selectedModel, setSelectedModel] = useState<ModelInfo>(() => {
    const savedModelId = localStorage.getItem('selectedModel');
    return savedModelId ? (getModelById(savedModelId) || getDefaultModel()) : getDefaultModel();
  });
  const [sessionInitialized, setSessionInitialized] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showMemory, setShowMemory] = useState(false);
  const [showMCP, setShowMCP] = useState(false);
  const [showProject, setShowProject] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showVoiceSettings, setShowVoiceSettings] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | undefined>();
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [orchestrationDetails, setOrchestrationDetails] = useState<{ contextUsage?: { used: number; total: number }; thinkingBudget?: { used: number; total: number } } | undefined>(undefined);

  // Orchestration state
  const { mode: layoutMode } = useLayoutStore();
  const { processEventStream, resetStores } = useAgentEvents();
  const { currentProject } = useProjectStore();
  const voice = useVoiceStore();
  const thoughtStore = useThoughtStore();
  const voiceProcessedRef = useRef<number>(0);
  const lastVoiceAssistantAppliedRef = useRef<string>('');
  const lastSpokenAssistantRef = useRef<string>('');
  const voiceEventsRef = useRef<EventSource | null>(null);
  const sessionEventSourceRef = useRef<EventSource | null>(null);
  const lastSessionEventTsRef = useRef<number>(0);
  const resumedAssistantBufferRef = useRef<string>('');
  const resumedReasoningRef = useRef<string | undefined>(undefined);

  const appendAssistantDelta = useCallback((delta: string, reasoning?: string) => {
    if (delta) {
      resumedAssistantBufferRef.current += delta;
    }
    if (typeof reasoning === 'string' && reasoning.length > 0) {
      resumedReasoningRef.current = reasoning;
    }
    if (!delta && !reasoning) return;

    setMessages(prev => {
      const next = [...prev];
      const last = next[next.length - 1];
      const updated = {
        role: 'assistant',
        content: resumedAssistantBufferRef.current,
        reasoning: resumedReasoningRef.current,
      } as any;

      if (last && last.role === 'assistant') {
        next[next.length - 1] = {
          ...last,
          content: updated.content || last.content,
          reasoning: updated.reasoning ?? last.reasoning,
        };
      } else {
        next.push(updated);
      }
      return next;
    });
  }, []);

  const closeSessionEventStream = useCallback(() => {
    if (sessionEventSourceRef.current) {
      try { sessionEventSourceRef.current.close(); } catch {}
      sessionEventSourceRef.current = null;
    }
  }, []);

  const processSessionEvent = useCallback((event: AgentEvent) => {
    if (!event) return;
    const parsedTs = event.timestamp ? Date.parse(event.timestamp) : NaN;
    if (Number.isFinite(parsedTs)) {
      if (parsedTs <= lastSessionEventTsRef.current) {
        return;
      }
      lastSessionEventTsRef.current = parsedTs;
    } else {
      lastSessionEventTsRef.current = Date.now();
    }

    setAgentEvents(prev => [...prev, event]);
    processEventStream(event);

    switch (event.type) {
      case 'thinking':
        setIsThinking(true);
        setIsLoading(true);
        resumedAssistantBufferRef.current = '';
        resumedReasoningRef.current = undefined;
        break;
      case 'message': {
        const messageDelta = typeof event.data?.message === 'string' ? event.data.message : '';
        const reasoningDelta = typeof event.data?.reasoning === 'string' ? event.data.reasoning : undefined;
        appendAssistantDelta(messageDelta, reasoningDelta);
        break;
      }
      case 'progress':
        setIsLoading(true);
        break;
      case 'complete':
        setIsThinking(false);
        setIsLoading(false);
        resumedAssistantBufferRef.current = '';
        resumedReasoningRef.current = undefined;
        break;
      case 'error':
        setIsThinking(false);
        setIsLoading(false);
        break;
      default:
        break;
    }
  }, [appendAssistantDelta, processEventStream]);

  const openSessionEventStream = useCallback(() => {
    if (!currentSessionId) return;
    if (sessionEventSourceRef.current) return;
    if (currentAbortRef.current) return;

    let url = `/api/session/events?sessionId=${encodeURIComponent(currentSessionId)}`;
    if (lastSessionEventTsRef.current) {
      url += `&since=${encodeURIComponent(String(lastSessionEventTsRef.current))}`;
    }

    try {
      const es = new EventSource(url);
      sessionEventSourceRef.current = es;
      es.onmessage = (ev) => {
        try {
          const data = JSON.parse(ev.data);
          if (data?.type) {
            processSessionEvent(data);
          }
        } catch (error) {
          console.warn('Failed to parse session event stream payload:', error);
        }
      };
      es.onerror = () => {
        // Allow browser to retry automatically; if it closes we'll clean up on reconnect
      };
    } catch (error) {
      console.warn('Failed to open session event stream:', error);
    }
  }, [currentSessionId, processSessionEvent]);

  // Derive active plan steps from agent events
  const activePlanSteps = (() => {
    try {
      const active = new Map<string, { title: string; agent?: string }>();
      for (const ev of agentEvents as any[]) {
        const type = (ev as any)?.type || (ev as any)?.data?.type;
        const data = (ev as any)?.data || {};
        if (type === 'delegation_start') {
          const id = String(data.stepId || '');
          if (id) active.set(id, { title: String(data.title || id), agent: data.agent });
        } else if (type === 'delegation_end') {
          const id = String(data.stepId || '');
          if (id) active.delete(id);
        }
      }
      return Array.from(active.values()).map(v => v.title).slice(0, 2);
    } catch {
      return [] as string[];
    }
  })();

  useEffect(() => {
    const initSession = async () => {
      await sessionManager.init();

      const applySessionState = (session: any) => {
        if (!session) return;
        if (Array.isArray(session.messages) && session.messages.length > 0) {
          setMessages(session.messages.map((m: any) => ({
            role: m.role,
            content: m.content,
            reasoning: m.reasoning,
            confidence: m.confidence,
          })));

          const lastWithEvents = [...session.messages].reverse().find((m: any) => Array.isArray(m.agentEvents) && m.agentEvents.length > 0);
          if (lastWithEvents) {
            setAgentEvents(lastWithEvents.agentEvents as AgentEvent[]);
            const lastEvent = (lastWithEvents.agentEvents as AgentEvent[])[(lastWithEvents.agentEvents as AgentEvent[]).length - 1];
            const parsedTs = lastEvent?.timestamp ? Date.parse(lastEvent.timestamp) : NaN;
            if (Number.isFinite(parsedTs)) {
              lastSessionEventTsRef.current = parsedTs;
            } else {
              lastSessionEventTsRef.current = Date.now();
            }
          } else {
            setAgentEvents([]);
            lastSessionEventTsRef.current = 0;
          }
        } else {
          setMessages([]);
          setAgentEvents([]);
          lastSessionEventTsRef.current = 0;
        }
      };

      // Check for shared session in URL first
      const sharedSessionId = await sessionManager.loadSessionFromUrl();
      if (sharedSessionId) {
        setCurrentSessionId(sharedSessionId);
        const session = await sessionManager.getSession(sharedSessionId);
        applySessionState(session);
      } else {
        // Load current session normally
        const currentSession = await sessionManager.getCurrentSession();
        if (currentSession) {
          setCurrentSessionId(currentSession.id);
          applySessionState(currentSession);
        }
      }
      setSessionInitialized(true);
    };
    initSession();

    return () => {
      sessionManager.destroy();
    };
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('darkMode', 'true');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('darkMode', 'false');
    }
  }, [darkMode]);

  // Poll agent status to surface thinking budget (Gemini) in Header badge
  useEffect(() => {
    let timer: any;
    const sleep = (ms: number) => new Promise(res => setTimeout(res, ms));
    let initialized = false;

    const fetchWithBackoff = async (url: string, attempts = 5, initialDelay = 500, factor = 1.7) => {
      let delay = initialDelay;
      for (let i = 0; i < attempts; i++) {
        try {
          const res = await fetch(url);
          if (res.ok) return res;
        } catch {}
        await sleep(delay);
        delay = Math.min(Math.floor(delay * factor), 3000);
      }
      return await fetch(url);
    };

    const fetchStatus = async () => {
      try {
        const res = initialized
          ? await fetch('/api/agent-status')
          : await fetchWithBackoff('/api/agent-status', 4, 600, 1.8);
        if (!res.ok) return;
        const data = await res.json();
        const lastModel = data?.status?.orchestrator?.lastModelUsed as string | undefined;
        const lastBudget = data?.status?.orchestrator?.lastGeminiThinkingBudget as number | undefined;
        if (lastModel && lastModel.startsWith('gemini-') && typeof lastBudget === 'number') {
          setOrchestrationDetails({ thinkingBudget: { used: lastBudget, total: 16384 } });
        } else {
          setOrchestrationDetails(undefined);
        }
        initialized = true;
      } catch {}
    };
    fetchStatus();
    timer = setInterval(fetchStatus, 8000);
    return () => { if (timer) clearInterval(timer); };
  }, []);

  // Live transcript: reflect voice session history into chat messages
  useEffect(() => {
    const hist = voice.history || [];
    if (!voice.enabled || hist.length === 0) return;

    const extractText = (item: any): string => {
      if (!item) return '';
      const c = item.content;
      if (typeof c === 'string') return c;
      if (Array.isArray(c)) {
        return c
          .map((p: any) => (typeof p === 'string' ? p : (p?.text || p?.content || '')))
          .join('');
      }
      if (c?.text) return c.text;
      if (c?.[0]?.text) return c[0].text;
      return '';
    };

    // Append any new items since last processed index
    for (let i = voiceProcessedRef.current; i < hist.length; i++) {
      const it = hist[i];
      if (it?.type === 'message' && (it.role === 'user' || it.role === 'assistant')) {
        const text = extractText(it);
        if (!text) continue;
        setMessages((prev) => [...prev, { role: it.role, content: text }]);
      }
    }
    voiceProcessedRef.current = hist.length;

    // Stream-like update for the latest assistant message
    const lastMsg = [...hist].reverse().find((x) => x?.type === 'message');
    if (lastMsg?.role === 'assistant') {
      const t = extractText(lastMsg);
      if (t && t !== lastVoiceAssistantAppliedRef.current) {
        lastVoiceAssistantAppliedRef.current = t;
        setMessages((prev) => {
          const next = [...prev];
          // Update last assistant message, else push new one
          if (next.length > 0 && next[next.length - 1].role === 'assistant') {
            next[next.length - 1] = { ...next[next.length - 1], content: t } as any;
          } else {
            next.push({ role: 'assistant', content: t } as any);
          }
          return next;
        });
      }
    }
  }, [voice.history, voice.enabled]);

  // Reset voice transcript trackers when voice is disabled
  useEffect(() => {
    if (!voice.enabled) {
      voiceProcessedRef.current = 0;
      lastVoiceAssistantAppliedRef.current = '';
      lastSpokenAssistantRef.current = '';
      if (voiceEventsRef.current) {
        try { voiceEventsRef.current.close(); } catch {}
        voiceEventsRef.current = null;
      }
    }
  }, [voice.enabled]);

  // Subscribe to voice orchestration SSE when voice is enabled
  useEffect(() => {
    const sub = async () => {
      if (!voice.enabled) return;
      const current = await sessionManager.getCurrentSession();
      const sessionId = current?.id;
      if (!sessionId) return;
      try {
        const es = new EventSource(`/api/voice/events?sessionId=${encodeURIComponent(sessionId)}`);
        voiceEventsRef.current = es;
        es.onmessage = (ev) => {
          try {
            const data = JSON.parse(ev.data);
            const withSource = { ...data, source: 'voice' } as any;
            // Add to activity panel and orchestration stores
            setAgentEvents(prev => [...prev, withSource]);
            processEventStream?.(withSource);
          } catch {}
        };
        es.addEventListener('error', () => {
          // Let it auto-reconnect; if closed, cleanup will run on disable
        });
      } catch (e) {
        console.warn('Failed to subscribe to voice events:', e);
      }
    };
    sub();
    // Cleanup when disabled handled in previous effect
  }, [voice.enabled]);

  useEffect(() => {
    if (!sessionInitialized || !currentSessionId) {
      closeSessionEventStream();
      return;
    }
    if (currentAbortRef.current) {
      closeSessionEventStream();
      return;
    }
    openSessionEventStream();
    return () => {
      closeSessionEventStream();
    };
  }, [sessionInitialized, currentSessionId, openSessionEventStream, closeSessionEventStream]);

  const handleModelChange = (model: ModelInfo) => {
    setSelectedModel(model);
    localStorage.setItem('selectedModel', model.id);
  };

  const handleSendMessage = async (message: string, attachments?: { url: string; file?: File }[], opts?: { forceWorker?: string }) => {
    closeSessionEventStream();
    const newMessage: any = { role: 'user', content: message };
    if (attachments && attachments.length > 0) {
      newMessage.attachments = attachments.map(a => ({
        filename: a.file?.name || a.url.split('/').pop() || 'file',
        url: a.url && !a.url.startsWith('data:') ? a.url : undefined,
        dataUrl: a.url && a.url.startsWith('data:') ? a.url : undefined,
      }));
    }
    setMessages(prev => [...prev, newMessage]);
    setIsLoading(true);
    setAgentEvents([]); // Clear previous events

    // Save user message to session
    await sessionManager.addMessage({
      role: 'user',
      content: message,
      timestamp: Date.now(),
      model: selectedModel.id,
    });

    const startTime = Date.now();

    try {
      // Call to backend API endpoint with events flag
      const requestBody = {
        messages: [...messages, newMessage],
        model: selectedModel.id,
        projectContext: currentProject,
      };

      let sessionIdForRequest = currentSessionId;
      if (!sessionIdForRequest) {
        const existingSession = await sessionManager.getCurrentSession();
        if (existingSession?.id) {
          sessionIdForRequest = existingSession.id;
        } else {
          sessionIdForRequest = sessionManager.createNewSession();
        }
        if (sessionIdForRequest && sessionIdForRequest !== currentSessionId) {
          setCurrentSessionId(sessionIdForRequest);
        }
      }

      const controller = new AbortController();
      currentAbortRef.current = controller;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Stream-Events': 'true',
      };
      if (sessionIdForRequest) headers['X-Session-ID'] = sessionIdForRequest;
      if (opts?.forceWorker) headers['X-Force-Worker'] = opts.forceWorker;

      const response = await fetch('/api/chat?events=true', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const responseSessionId = response.headers.get('X-Session-ID');
      if (responseSessionId) {
        sessionManager.switchToSession(responseSessionId);
        if (responseSessionId !== currentSessionId) {
          setCurrentSessionId(responseSessionId);
        }
        sessionIdForRequest = responseSessionId;
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let assistantMessage = '';
      let assistantReasoning: string | undefined;
      let assistantConfidence: number | undefined;
      let buffer = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Parse SSE events
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('event:')) {
              const eventType = line.slice(6).trim();

              // Handle different event types
              if (eventType === 'thinking') {
                setIsThinking(true);
              } else if (eventType === 'message') {
                setIsThinking(false);
              }
            } else if (line.startsWith('data:')) {
              try {
                const data = JSON.parse(line.slice(5));

                if (data?.timestamp) {
                  const parsedTs = Date.parse(data.timestamp);
                  if (Number.isFinite(parsedTs)) {
                    lastSessionEventTsRef.current = parsedTs;
                  }
                }

                if (data.type === 'agent_switch') {
                  setCurrentAgent(data.data.agent || 'Dante');
                } else if (data.type === 'message') {
                  assistantMessage += data.data.message || '';

                  // Check for reasoning data
                  if (data.data.reasoning) {
                    assistantReasoning = data.data.reasoning;
                  }
                  if (data.data.confidence !== undefined) {
                    assistantConfidence = data.data.confidence;
                  }

                  // Update the message in real-time
                  setMessages(prev => {
                    const newMessages = [...prev];
                    if (newMessages[newMessages.length - 1]?.role === 'assistant') {
                      newMessages[newMessages.length - 1].content = assistantMessage;
                      if (assistantReasoning) newMessages[newMessages.length - 1].reasoning = assistantReasoning;
                      if (assistantConfidence !== undefined) newMessages[newMessages.length - 1].confidence = assistantConfidence;
                      newMessages[newMessages.length - 1].isStreaming = true;
                    } else {
                      newMessages.push({
                        role: 'assistant',
                        content: assistantMessage,
                        reasoning: assistantReasoning,
                        confidence: assistantConfidence,
                        isStreaming: true
                      });
                    }
                    return newMessages;
                  });
                } else if (data.type === 'complete') {
                  // Clear streaming flag when streaming completes
                  setMessages(prev => {
                    const newMessages = [...prev];
                    const lastMessage = newMessages[newMessages.length - 1];
                    if (lastMessage && lastMessage.role === 'assistant') {
                      lastMessage.isStreaming = false;
                    }
                    return newMessages;
                  });

                  setIsThinking(false);
                  // Auto-speak final assistant reply if voice mode is active
                  try {
                    if (voice.enabled && assistantMessage && assistantMessage !== lastSpokenAssistantRef.current) {
                      lastSpokenAssistantRef.current = assistantMessage;
                      voice.sendText(assistantMessage, { mode: 'say' }).catch(() => {});
                    }
                  } catch {}
                } else {
                  // Debug: log received events
                  console.log('Received event data:', {
                    type: typeof data,
                    hasType: !!data?.type,
                    hasTimestamp: !!data?.timestamp,
                    data: data
                  });

                  // Add to events list for activity sidebar (handle multiple event formats)
                  // Events from enhanced stream handler have type and timestamp at top level
                  // Raw events from Gemini might be nested in different ways
                  if (data && typeof data === 'object') {
                    let eventToAdd = null;

                    // Format 1: Direct event with type and timestamp
                    if (data.type && data.timestamp) {
                      eventToAdd = data;
                    }
                    // Format 2: Event from EnhancedStreamHandler (SSE format)
                    else if (data.type && !data.timestamp) {
                      eventToAdd = {
                        type: data.type,
                        timestamp: new Date().toISOString(),
                        data: data.data || {}
                      };
                      lastSessionEventTsRef.current = Date.now();
                    }

                    if (eventToAdd) {
                      console.log('Adding event to agentEvents:', eventToAdd);
                      setAgentEvents(prev => [...prev, eventToAdd]);
                    }
                  }
                  // Process event for orchestration stores
                  processEventStream(data);
                }
              } catch (e) {
                // If not JSON, treat as regular text
                if (line.slice(5).trim()) {
                  const text = line.slice(5);

                  // Check if this is reasoning data (marked with ---REASONING---)
                  if (text.includes('---REASONING---')) {
                    // Start capturing reasoning
                    const parts = assistantMessage.split('---REASONING---');
                    assistantMessage = parts[0] || assistantMessage;
                    assistantReasoning = '';
                  } else if (assistantReasoning !== undefined) {
                    // We're in reasoning mode, capture reasoning text
                    assistantReasoning += text;
                  } else {
                    // Regular message text
                    assistantMessage += text;
                  }

                  setMessages(prev => {
                    const newMessages = [...prev];
                    if (newMessages[newMessages.length - 1]?.role === 'assistant') {
                      newMessages[newMessages.length - 1].content = assistantMessage;
                      if (assistantReasoning) newMessages[newMessages.length - 1].reasoning = assistantReasoning;
                      if (assistantConfidence !== undefined) newMessages[newMessages.length - 1].confidence = assistantConfidence;
                      newMessages[newMessages.length - 1].isStreaming = true;
                    } else {
                      newMessages.push({
                        role: 'assistant',
                        content: assistantMessage,
                        reasoning: assistantReasoning,
                        confidence: assistantConfidence,
                        isStreaming: true
                      });
                    }
                    return newMessages;
                  });
                }
              }
            }
          }
        }
      }

      // Save assistant message and log API call
      if (assistantMessage) {
        await sessionManager.addMessage({
          role: 'assistant',
          content: assistantMessage,
          timestamp: Date.now(),
          model: selectedModel.id,
          agentEvents: agentEvents,
        });
      }

      const duration = Date.now() - startTime;
      await sessionManager.logApiCall(
        requestBody,
        { message: assistantMessage, events: agentEvents },
        duration
      );
    } catch (error: any) {
      if (error?.name === 'AbortError') {
        console.warn('Request aborted by user.');
        return; // Swallow aborts without user-facing error
      }
      console.error('Error:', error);
      const errorMessage = 'Sorry, I encountered an error. Please try again.';
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: errorMessage,
      }]);

      await sessionManager.addMessage({
        role: 'assistant',
        content: errorMessage,
        timestamp: Date.now(),
      });

      const duration = Date.now() - startTime;
      await sessionManager.logApiCall(
        { messages, model: selectedModel },
        null,
        duration,
        error
      );
    } finally {
      setIsLoading(false);
      setIsThinking(false);
      currentAbortRef.current = null;
      openSessionEventStream();
    }
  };

  const cancelCurrentRun = (agentName?: string) => {
    if (currentAbortRef.current) {
      currentAbortRef.current.abort();
      currentAbortRef.current = null;
    }
    setIsLoading(false);
    setIsThinking(false);
    // Notify backend to cancel long-running tools for this session
    if (currentSessionId) {
      fetch('/api/run/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: currentSessionId, agent: agentName }),
      }).catch(() => {});
    }
  };

  const retryAgentRun = (agentName?: string) => {
    // Cancel current stream and resend the last user message
    cancelCurrentRun(agentName);
    // Find last user message
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i]?.role === 'user') {
        const last = messages[i];
        const nameToWorker: Record<string, string> = {
          'Research Agent': 'research',
          'Code Generation Agent': 'code-generation',
          'Code Refactor Agent': 'code-refactor',
          'Debug Agent': 'debug',
          'Security Analysis Agent': 'security',
          'Code Review Agent': 'code-review',
          'Weather Agent': 'weather',
          'Task Orchestrator': 'orchestrator',
          'Planning Agent': 'planning',
          'Reminder Agent': 'reminder',
          'Diagnostic Agent': 'diagnostic',
        };
        const worker = agentName ? (nameToWorker[agentName] || agentName?.toLowerCase().replace(/\s+agent$/, '').replace(/\s+/g, '-')) : undefined;
        handleSendMessage(last.content, undefined, worker ? { forceWorker: worker } : undefined);
        break;
      }
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    const newSessionId = sessionManager.createNewSession();
    setCurrentSessionId(newSessionId);
    resetStores(); // Reset orchestration stores
    try { thoughtStore.clearRun('session'); } catch {}
  };

  const handleSelectSession = (_sessionId: string) => {
    // Session switch will trigger a reload, handled in ConversationHistory component
  };

  if (!sessionInitialized) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900 items-center justify-center">
        <div className="text-gray-600 dark:text-gray-400">Loading session...</div>
      </div>
    );
  }

  // Render different layouts based on mode
  if (layoutMode === 'orchestration') {
    return (
      <div className="h-screen overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950 orchestration-mode">
        <Header
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          darkMode={darkMode}
          onToggleDarkMode={() => setDarkMode(!darkMode)}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          onShareSession={() => setShowShareDialog(true)}
          currentSessionId={currentSessionId}
          isActiveProcessing={isLoading}
          orchestrationDetails={orchestrationDetails}
        />

        <Sidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          onClearChat={handleClearChat}
          onOpenHistory={() => setShowHistory(true)}
          onOpenMemory={() => setShowMemory(true)}
          onOpenMCP={() => setShowMCP(true)}
          onOpenProject={() => setShowProject(true)}
          onOpenVoiceSettings={() => setShowVoiceSettings(true)}
        />

        {/* Orchestration Layout Container */}
        <div className="relative w-full" style={{ height: 'calc(100vh - 64px)' }}>
          {/* Background layer for panels */}
          <div className="absolute inset-0 pointer-events-none" />

          {/* Task Panel - Fixed position */}
          <Suspense fallback={<LoadingFallback message="Loading task panel..." />}>
            <TaskPanel />
          </Suspense>

          {/* Resource Monitor - Fixed position */}
          <Suspense fallback={<LoadingFallback message="Loading resource monitor..." />}>
            <ResourceMonitor />
          </Suspense>

          {/* Agent Grid - Draggable */}
          <Suspense fallback={<LoadingFallback message="Loading agent grid..." />}>
            <AgentGrid />
          </Suspense>

          {/* Floating Chat Window - Top layer */}
          <Suspense fallback={<LoadingFallback message="Loading chat window..." />}>
            <FloatingChatWindow
              messages={messages}
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              agentEvents={agentEvents}
              isThinking={isThinking}
              currentAgent={currentAgent}
              onCancelRun={cancelCurrentRun}
              onRetryRun={retryAgentRun}
            />
          </Suspense>
        </div>

        {showHistory && (
          <Suspense fallback={<LoadingFallback message="Loading conversation history..." />}>
            <ConversationHistory
              isOpen={showHistory}
              onClose={() => setShowHistory(false)}
              onSelectSession={handleSelectSession}
              currentSessionId={currentSessionId}
            />
          </Suspense>
        )}

        {showMemory && (
          <Suspense fallback={<LoadingFallback message="Loading memory panel..." />}>
            <MemoryPanel
              isOpen={showMemory}
              onClose={() => setShowMemory(false)}
            />
          </Suspense>
        )}

        {showMCP && (
          <Suspense fallback={<LoadingFallback message="Loading MCP panel..." />}>
            <MCPPanel
              isOpen={showMCP}
              onClose={() => setShowMCP(false)}
            />
          </Suspense>
        )}

        {showProject && (
          <Suspense fallback={<LoadingFallback message="Loading project explorer..." />}>
            <ProjectExplorer
              isOpen={showProject}
              onClose={() => setShowProject(false)}
            />
          </Suspense>
        )}

        {showShareDialog && (
          <Suspense fallback={<LoadingFallback message="Loading share dialog..." />}>
            <ShareSessionDialog
              isOpen={showShareDialog}
              onClose={() => setShowShareDialog(false)}
              sessionId={currentSessionId}
            />
          </Suspense>
        )}

        {showVoiceSettings && (
          <Suspense fallback={<LoadingFallback message="Loading voice settings..." />}>
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto relative">
                <button
                  onClick={() => setShowVoiceSettings(false)}
                  className="absolute top-4 right-4 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg z-10"
                >
                  <X className="w-5 h-5" />
                </button>
                <VoiceSettings />
              </div>
            </div>
          </Suspense>
        )}

        {/* Toast Notifications */}
        <ToastContainer />
      </div>
    );
  }

  // Classic layout (original)
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onClearChat={handleClearChat}
        onOpenHistory={() => setShowHistory(true)}
        onOpenMemory={() => setShowMemory(true)}
        onOpenMCP={() => setShowMCP(true)}
        onOpenVoiceSettings={() => setShowVoiceSettings(true)}
      />

      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        <Header
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          darkMode={darkMode}
          onToggleDarkMode={() => setDarkMode(!darkMode)}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          onRateLimitChange={setIsRateLimited}
          onShareSession={() => setShowShareDialog(true)}
          currentSessionId={currentSessionId}
          isActiveProcessing={isLoading || isThinking}
          orchestrationDetails={undefined}
          activePlanSteps={activePlanSteps}
        />

        <div className="flex-1 overflow-hidden">
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            agentEvents={agentEvents}
            isThinking={isThinking}
            currentAgent={currentAgent}
            isRateLimited={isRateLimited}
            onCancelRun={cancelCurrentRun}
            onRetryRun={retryAgentRun}
          />
        </div>
      </div>

      {showHistory && (
        <Suspense fallback={<LoadingFallback message="Loading conversation history..." />}>
          <ConversationHistory
            isOpen={showHistory}
            onClose={() => setShowHistory(false)}
            onSelectSession={handleSelectSession}
            currentSessionId={currentSessionId}
          />
        </Suspense>
      )}

      {showMemory && (
        <Suspense fallback={<LoadingFallback message="Loading memory panel..." />}>
          <MemoryPanel
            isOpen={showMemory}
            onClose={() => setShowMemory(false)}
            darkMode={darkMode}
          />
        </Suspense>
      )}

      {showMCP && (
        <Suspense fallback={<LoadingFallback message="Loading MCP panel..." />}>
          <MCPPanel
            isOpen={showMCP}
            onClose={() => setShowMCP(false)}
          />
        </Suspense>
      )}

      {showProject && (
        <Suspense fallback={<LoadingFallback message="Loading project explorer..." />}>
          <ProjectExplorer
            isOpen={showProject}
            onClose={() => setShowProject(false)}
          />
        </Suspense>
      )}

      {showShareDialog && (
        <Suspense fallback={<LoadingFallback message="Loading share dialog..." />}>
          <ShareSessionDialog
            isOpen={showShareDialog}
            onClose={() => setShowShareDialog(false)}
            sessionId={currentSessionId}
          />
        </Suspense>
      )}

      {showVoiceSettings && (
        <Suspense fallback={<LoadingFallback message="Loading voice settings..." />}>
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto relative">
              <button
                onClick={() => setShowVoiceSettings(false)}
                className="absolute top-4 right-4 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg z-10"
              >
                <X className="w-5 h-5" />
              </button>
              <VoiceSettings />
            </div>
          </div>
        </Suspense>
      )}

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  );
}

export default App;
