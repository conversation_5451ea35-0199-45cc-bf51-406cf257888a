import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface ComputerAction {
  type: 'click' | 'scroll' | 'keypress' | 'type' | 'wait' | 'screenshot';
  x?: number;
  y?: number;
  button?: 'left' | 'right' | 'middle';
  scrollX?: number;
  scrollY?: number;
  keys?: string[];
  text?: string;
  timestamp: Date;
}

export interface SafetyCheck {
  id: string;
  code: 'malicious_instructions' | 'irrelevant_domain' | 'sensitive_domain' | 'custom';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ComputerUseSession {
  id: string;
  environmentType: 'browser' | 'docker';
  displayWidth: number;
  displayHeight: number;
  isActive: boolean;
  startTime: Date;
  currentUrl?: string;
  screenshot?: string; // base64
  actionHistory: ComputerAction[];
  safetyChecks: SafetyCheck[];
  domains: string[];
  status: 'idle' | 'executing' | 'waiting_for_confirmation' | 'paused' | 'error';
  lastError?: string;
  reasoning?: string;
}

export interface ComputerUseResponse {
  sessionId: string;
  status: 'success' | 'error' | 'safety_check_required' | 'confirmation_required';
  screenshot?: string;
  currentUrl?: string;
  actions?: ComputerAction[];
  safetyChecks?: SafetyCheck[];
  message?: string;
  error?: string;
  reasoning?: string;
}

interface ComputerUseStore {
  // State
  sessions: Map<string, ComputerUseSession>;
  activeSessionId?: string;
  isMonitoringVisible: boolean;
  pendingSafetyChecks: SafetyCheck[];

  // Getters
  getActiveSession: () => ComputerUseSession | undefined;
  getSession: (id: string) => ComputerUseSession | undefined;
  getAllSessions: () => ComputerUseSession[];

  // Actions
  createSession: (config: {
    environmentType: 'browser' | 'docker';
    displayWidth?: number;
    displayHeight?: number;
    initialUrl?: string;
  }) => string;
  
  updateSession: (sessionId: string, updates: Partial<ComputerUseSession>) => void;
  setActiveSession: (sessionId?: string) => void;
  closeSession: (sessionId: string) => void;
  
  addAction: (sessionId: string, action: Omit<ComputerAction, 'timestamp'>) => void;
  updateScreenshot: (sessionId: string, screenshot: string, url?: string) => void;
  addSafetyChecks: (sessionId: string, checks: SafetyCheck[]) => void;
  clearSafetyChecks: (sessionId: string, checkIds?: string[]) => void;
  
  setSessionStatus: (sessionId: string, status: ComputerUseSession['status']) => void;
  setSessionError: (sessionId: string, error: string) => void;
  clearSessionError: (sessionId: string) => void;
  
  toggleMonitoringVisibility: () => void;
  setMonitoringVisibility: (visible: boolean) => void;
  
  // Batch operations
  handleComputerUseResponse: (response: ComputerUseResponse) => void;
  
  // Cleanup
  clearAllSessions: () => void;
  
  // Session statistics
  getSessionStats: (sessionId: string) => {
    totalActions: number;
    safetyViolations: number;
    uniqueDomains: number;
    uptime: number; // in milliseconds
  } | null;
}

export const useComputerUseStore = create<ComputerUseStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    sessions: new Map(),
    activeSessionId: undefined,
    isMonitoringVisible: false,
    pendingSafetyChecks: [],

    // Getters
    getActiveSession: () => {
      const { sessions, activeSessionId } = get();
      return activeSessionId ? sessions.get(activeSessionId) : undefined;
    },

    getSession: (id: string) => {
      return get().sessions.get(id);
    },

    getAllSessions: () => {
      return Array.from(get().sessions.values());
    },

    // Actions
    createSession: (config) => {
      const sessionId = `computer-use-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      const session: ComputerUseSession = {
        id: sessionId,
        environmentType: config.environmentType,
        displayWidth: config.displayWidth || 1024,
        displayHeight: config.displayHeight || 768,
        isActive: true,
        startTime: new Date(),
        currentUrl: config.initialUrl,
        actionHistory: [],
        safetyChecks: [],
        domains: config.initialUrl ? [new URL(config.initialUrl).hostname] : [],
        status: 'idle'
      };

      set(state => {
        const newSessions = new Map(state.sessions);
        newSessions.set(sessionId, session);
        return {
          sessions: newSessions,
          activeSessionId: sessionId
        };
      });

      return sessionId;
    },

    updateSession: (sessionId, updates) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const existingSession = newSessions.get(sessionId);
        if (existingSession) {
          newSessions.set(sessionId, { ...existingSession, ...updates });
        }
        return { sessions: newSessions };
      });
    },

    setActiveSession: (sessionId) => {
      set({ activeSessionId: sessionId });
    },

    closeSession: (sessionId) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          newSessions.set(sessionId, { ...session, isActive: false, status: 'idle' });
        }
        
        return {
          sessions: newSessions,
          activeSessionId: state.activeSessionId === sessionId ? undefined : state.activeSessionId
        };
      });
    },

    addAction: (sessionId, action) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          const actionWithTimestamp: ComputerAction = {
            ...action,
            timestamp: new Date()
          };
          newSessions.set(sessionId, {
            ...session,
            actionHistory: [...session.actionHistory, actionWithTimestamp]
          });
        }
        return { sessions: newSessions };
      });
    },

    updateScreenshot: (sessionId, screenshot, url) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          const updates: Partial<ComputerUseSession> = { screenshot };
          
          // Update URL and domains if provided
          if (url && url !== session.currentUrl) {
            updates.currentUrl = url;
            try {
              const domain = new URL(url).hostname;
              if (!session.domains.includes(domain)) {
                updates.domains = [...session.domains, domain];
              }
            } catch {
              // Invalid URL, ignore
            }
          }
          
          newSessions.set(sessionId, { ...session, ...updates });
        }
        return { sessions: newSessions };
      });
    },

    addSafetyChecks: (sessionId, checks) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          const uniqueChecks = checks.filter(check => 
            !session.safetyChecks.some(existing => existing.id === check.id)
          );
          newSessions.set(sessionId, {
            ...session,
            safetyChecks: [...session.safetyChecks, ...uniqueChecks],
            status: 'waiting_for_confirmation'
          });
        }
        return { sessions: newSessions };
      });
    },

    clearSafetyChecks: (sessionId, checkIds) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          const filteredChecks = checkIds
            ? session.safetyChecks.filter(check => !checkIds.includes(check.id))
            : [];
          newSessions.set(sessionId, {
            ...session,
            safetyChecks: filteredChecks,
            status: filteredChecks.length > 0 ? 'waiting_for_confirmation' : 'idle'
          });
        }
        return { sessions: newSessions };
      });
    },

    setSessionStatus: (sessionId, status) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          newSessions.set(sessionId, { ...session, status });
        }
        return { sessions: newSessions };
      });
    },

    setSessionError: (sessionId, error) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          newSessions.set(sessionId, { ...session, lastError: error, status: 'error' });
        }
        return { sessions: newSessions };
      });
    },

    clearSessionError: (sessionId) => {
      set(state => {
        const newSessions = new Map(state.sessions);
        const session = newSessions.get(sessionId);
        if (session) {
          newSessions.set(sessionId, { ...session, lastError: undefined, status: 'idle' });
        }
        return { sessions: newSessions };
      });
    },

    toggleMonitoringVisibility: () => {
      set(state => ({ isMonitoringVisible: !state.isMonitoringVisible }));
    },

    setMonitoringVisibility: (visible) => {
      set({ isMonitoringVisible: visible });
    },

    handleComputerUseResponse: (response) => {
      const { updateSession, updateScreenshot, addSafetyChecks, addAction, setSessionError } = get();
      
      // Update screenshot if provided
      if (response.screenshot) {
        updateScreenshot(response.sessionId, response.screenshot, response.currentUrl);
      }

      // Add actions to history
      if (response.actions) {
        response.actions.forEach(action => {
          addAction(response.sessionId, action);
        });
      }

      // Handle safety checks
      if (response.safetyChecks && response.safetyChecks.length > 0) {
        addSafetyChecks(response.sessionId, response.safetyChecks);
      }

      // Update session based on response status
      switch (response.status) {
        case 'success':
          updateSession(response.sessionId, { 
            status: 'idle',
            reasoning: response.reasoning,
            lastError: undefined
          });
          break;
        case 'safety_check_required':
        case 'confirmation_required':
          updateSession(response.sessionId, { 
            status: 'waiting_for_confirmation',
            reasoning: response.reasoning
          });
          break;
        case 'error':
          setSessionError(response.sessionId, response.error || 'Unknown error occurred');
          break;
      }
    },

    clearAllSessions: () => {
      set({ 
        sessions: new Map(), 
        activeSessionId: undefined,
        pendingSafetyChecks: [] 
      });
    },

    getSessionStats: (sessionId) => {
      const session = get().sessions.get(sessionId);
      if (!session) return null;

      const now = new Date().getTime();
      const uptime = now - session.startTime.getTime();
      
      return {
        totalActions: session.actionHistory.length,
        safetyViolations: session.safetyChecks.length,
        uniqueDomains: session.domains.length,
        uptime
      };
    }
  }))
);

// Selectors for convenience
export const useActiveSession = () => useComputerUseStore(state => state.getActiveSession());
export const useSession = (id: string) => useComputerUseStore(state => state.getSession(id));
export const useAllSessions = () => useComputerUseStore(state => state.getAllSessions());
export const useMonitoringVisibility = () => useComputerUseStore(state => state.isMonitoringVisible);