import { create } from 'zustand';

export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
export type TaskPriority = 'low' | 'medium' | 'high' | 'critical';

export interface Task {
  id: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignedAgent?: string;
  parentTaskId?: string;
  subtasks: string[];
  progress: number; // 0-100
  estimatedTokens?: number;
  actualTokens?: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

interface TaskStore {
  tasks: Map<string, Task>;
  taskOrder: string[]; // Ordered list of task IDs
  activeTaskId: string | null;
  
  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'subtasks' | 'progress'>) => string;
  updateTask: (id: string, updates: Partial<Task>) => void;
  deleteTask: (id: string) => void;
  
  startTask: (id: string, agentName?: string) => void;
  completeTask: (id: string, actualTokens?: number) => void;
  failTask: (id: string, error: string) => void;
  cancelTask: (id: string) => void;
  
  updateTaskProgress: (id: string, progress: number) => void;
  assignTaskToAgent: (id: string, agentName: string) => void;
  
  addSubtask: (parentId: string, subtaskId: string) => void;
  removeSubtask: (parentId: string, subtaskId: string) => void;
  
  reorderTasks: (taskIds: string[]) => void;
  clearCompletedTasks: () => void;
  clearAllTasks: () => void;
  
  getTasksByStatus: (status: TaskStatus) => Task[];
  getTasksByAgent: (agentName: string) => Task[];
  getSubtasks: (parentId: string) => Task[];
}

export const useTaskStore = create<TaskStore>((set, get) => ({
  tasks: new Map(),
  taskOrder: [],
  activeTaskId: null,
  
  createTask: (taskData) => {
    const id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const task: Task = {
      ...taskData,
      id,
      createdAt: new Date(),
      subtasks: [],
      progress: 0,
    };
    
    set((state) => {
      const tasks = new Map(state.tasks);
      tasks.set(id, task);
      const taskOrder = [...state.taskOrder, id];
      return { tasks, taskOrder };
    });
    
    return id;
  },
  
  updateTask: (id, updates) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        tasks.set(id, { ...task, ...updates });
      }
      return { tasks };
    });
  },
  
  deleteTask: (id) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      
      // Remove from parent's subtasks if it's a subtask
      if (task?.parentTaskId) {
        const parent = tasks.get(task.parentTaskId);
        if (parent) {
          parent.subtasks = parent.subtasks.filter(sid => sid !== id);
          tasks.set(task.parentTaskId, parent);
        }
      }
      
      // Delete the task and its subtasks recursively
      const deleteRecursive = (taskId: string) => {
        const t = tasks.get(taskId);
        if (t) {
          t.subtasks.forEach(deleteRecursive);
          tasks.delete(taskId);
        }
      };
      
      deleteRecursive(id);
      
      const taskOrder = state.taskOrder.filter(tid => tid !== id);
      const activeTaskId = state.activeTaskId === id ? null : state.activeTaskId;
      
      return { tasks, taskOrder, activeTaskId };
    });
  },
  
  startTask: (id, agentName) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.status = 'in_progress';
        task.startedAt = new Date();
        if (agentName) task.assignedAgent = agentName;
        tasks.set(id, task);
      }
      return { tasks, activeTaskId: id };
    });
  },
  
  completeTask: (id, actualTokens) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.status = 'completed';
        task.completedAt = new Date();
        task.progress = 100;
        if (actualTokens) task.actualTokens = actualTokens;
        tasks.set(id, task);
      }
      const activeTaskId = state.activeTaskId === id ? null : state.activeTaskId;
      return { tasks, activeTaskId };
    });
  },
  
  failTask: (id, error) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.status = 'failed';
        task.error = error;
        task.completedAt = new Date();
        tasks.set(id, task);
      }
      const activeTaskId = state.activeTaskId === id ? null : state.activeTaskId;
      return { tasks, activeTaskId };
    });
  },
  
  cancelTask: (id) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.status = 'cancelled';
        task.completedAt = new Date();
        tasks.set(id, task);
      }
      const activeTaskId = state.activeTaskId === id ? null : state.activeTaskId;
      return { tasks, activeTaskId };
    });
  },
  
  updateTaskProgress: (id, progress) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.progress = Math.min(100, Math.max(0, progress));
        tasks.set(id, task);
      }
      return { tasks };
    });
  },
  
  assignTaskToAgent: (id, agentName) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const task = tasks.get(id);
      if (task) {
        task.assignedAgent = agentName;
        tasks.set(id, task);
      }
      return { tasks };
    });
  },
  
  addSubtask: (parentId, subtaskId) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const parent = tasks.get(parentId);
      const subtask = tasks.get(subtaskId);
      
      if (parent && subtask) {
        parent.subtasks.push(subtaskId);
        subtask.parentTaskId = parentId;
        tasks.set(parentId, parent);
        tasks.set(subtaskId, subtask);
      }
      
      return { tasks };
    });
  },
  
  removeSubtask: (parentId, subtaskId) => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const parent = tasks.get(parentId);
      const subtask = tasks.get(subtaskId);
      
      if (parent) {
        parent.subtasks = parent.subtasks.filter(id => id !== subtaskId);
        tasks.set(parentId, parent);
      }
      
      if (subtask) {
        subtask.parentTaskId = undefined;
        tasks.set(subtaskId, subtask);
      }
      
      return { tasks };
    });
  },
  
  reorderTasks: (taskIds) => {
    set({ taskOrder: taskIds });
  },
  
  clearCompletedTasks: () => {
    set((state) => {
      const tasks = new Map(state.tasks);
      const completedIds: string[] = [];
      
      tasks.forEach((task, id) => {
        if (task.status === 'completed') {
          completedIds.push(id);
        }
      });
      
      completedIds.forEach(id => tasks.delete(id));
      
      const taskOrder = state.taskOrder.filter(id => !completedIds.includes(id));
      
      return { tasks, taskOrder };
    });
  },
  
  clearAllTasks: () => {
    set({ tasks: new Map(), taskOrder: [], activeTaskId: null });
  },
  
  getTasksByStatus: (status) => {
    const tasks = get().tasks;
    return Array.from(tasks.values()).filter(task => task.status === status);
  },
  
  getTasksByAgent: (agentName) => {
    const tasks = get().tasks;
    return Array.from(tasks.values()).filter(task => task.assignedAgent === agentName);
  },
  
  getSubtasks: (parentId) => {
    const tasks = get().tasks;
    const parent = tasks.get(parentId);
    if (!parent) return [];
    
    return parent.subtasks
      .map(id => tasks.get(id))
      .filter((task): task is Task => task !== undefined);
  },
}));