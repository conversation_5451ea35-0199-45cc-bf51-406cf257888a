/**
 * Zustand store for managing Google Workspace connector state
 * Handles connection status, search operations, and approval workflows
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ConnectorId } from '../../types/connectors';

export type ConnectionStatus = 'connected' | 'disconnected' | 'expiring' | 'refreshing' | 'error';

export interface ConnectorInfo {
  id: ConnectorId;
  name: string;
  status: ConnectionStatus;
  lastConnected?: Date;
  tokenExpiresAt?: Date;
  error?: string;
  scopes?: string[];
}

export interface SearchOperation {
  id: string;
  connectorId: ConnectorId;
  query: string;
  status: 'searching' | 'found' | 'processing' | 'complete' | 'error';
  startTime: Date;
  endTime?: Date;
  itemsFound?: number;
  preview?: any[];
  error?: string;
}

export interface ApprovalRequest {
  id: string;
  connectorId: ConnectorId;
  operation: string;
  arguments: any;
  timestamp: Date;
  status: 'pending' | 'approved' | 'denied';
  expiresAt?: Date;
}

export interface SearchResult {
  id: string;
  connectorId: ConnectorId;
  query: string;
  timestamp: Date;
  results: any[];
  totalCount: number;
  nextPageToken?: string;
}

export interface ConnectorActivity {
  id: string;
  connectorId: ConnectorId;
  type: 'auth' | 'search' | 'fetch' | 'update' | 'delete';
  description: string;
  timestamp: Date;
  status: 'success' | 'error' | 'pending';
  details?: any;
}

interface ConnectorState {
  // Connection Management
  connections: Map<ConnectorId, ConnectorInfo>;
  isAuthenticating: boolean;
  authError?: string;
  
  // Search Operations
  activeSearches: SearchOperation[];
  searchHistory: SearchResult[];
  maxSearchHistory: number;
  
  // Approval Workflow
  pendingApprovals: ApprovalRequest[];
  autoApprove: boolean;
  approvalPreferences: Map<string, 'always' | 'never' | 'ask'>;
  
  // Activity Tracking
  recentActivity: ConnectorActivity[];
  maxActivityItems: number;
  
  // Preview State
  showPreview: boolean;
  currentPreview?: SearchResult;
  
  // Actions
  setConnectionStatus: (connectorId: ConnectorId, status: ConnectionStatus, error?: string) => void;
  updateConnectorInfo: (connectorId: ConnectorId, info: Partial<ConnectorInfo>) => void;
  startAuthentication: (connectorId: ConnectorId) => void;
  completeAuthentication: (connectorId: ConnectorId, success: boolean, error?: string) => void;
  
  addSearchOperation: (operation: Omit<SearchOperation, 'id'>) => string;
  updateSearchOperation: (id: string, updates: Partial<SearchOperation>) => void;
  completeSearchOperation: (id: string, results?: any[], error?: string) => void;
  
  addApprovalRequest: (request: Omit<ApprovalRequest, 'id' | 'timestamp' | 'status'>) => string;
  handleApproval: (requestId: string, approved: boolean) => void;
  setApprovalPreference: (operation: string, preference: 'always' | 'never' | 'ask') => void;
  
  addActivity: (activity: Omit<ConnectorActivity, 'id' | 'timestamp'>) => void;
  clearActivity: (connectorId?: ConnectorId) => void;
  
  setPreview: (preview: SearchResult | undefined) => void;
  togglePreview: () => void;
  
  addToSearchHistory: (result: SearchResult) => void;
  clearSearchHistory: (connectorId?: ConnectorId) => void;
  
  reset: () => void;
}

const initialState = {
  connections: new Map<ConnectorId, ConnectorInfo>([
    ['connector_gmail', { 
      id: 'connector_gmail', 
      name: 'Gmail', 
      status: 'disconnected' as ConnectionStatus 
    }],
    ['connector_googlecalendar', { 
      id: 'connector_googlecalendar', 
      name: 'Google Calendar', 
      status: 'disconnected' as ConnectionStatus 
    }],
    ['connector_googledrive', { 
      id: 'connector_googledrive', 
      name: 'Google Drive', 
      status: 'disconnected' as ConnectionStatus 
    }]
  ]),
  isAuthenticating: false,
  activeSearches: [],
  searchHistory: [],
  maxSearchHistory: 50,
  pendingApprovals: [],
  autoApprove: false,
  approvalPreferences: new Map(),
  recentActivity: [],
  maxActivityItems: 100,
  showPreview: false,
};

export const useConnectorStore = create<ConnectorState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setConnectionStatus: (connectorId, status, error) => {
        set(state => {
          const connections = new Map(state.connections);
          const existing = connections.get(connectorId);
          if (existing) {
            connections.set(connectorId, {
              ...existing,
              status,
              error,
              lastConnected: status === 'connected' ? new Date() : existing.lastConnected
            });
          }
          return { connections };
        });
        
        // Log activity
        get().addActivity({
          connectorId,
          type: 'auth',
          description: `Connection ${status}`,
          status: status === 'error' ? 'error' : 'success',
          details: { error }
        });
      },
      
      updateConnectorInfo: (connectorId, info) => {
        set(state => {
          const connections = new Map(state.connections);
          const existing = connections.get(connectorId);
          if (existing) {
            connections.set(connectorId, { ...existing, ...info });
          }
          return { connections };
        });
      },
      
      startAuthentication: (connectorId) => {
        set({ isAuthenticating: true, authError: undefined });
        get().addActivity({
          connectorId,
          type: 'auth',
          description: 'Starting authentication',
          status: 'pending'
        });
      },
      
      completeAuthentication: (connectorId, success, error) => {
        set({ isAuthenticating: false, authError: error });
        get().setConnectionStatus(
          connectorId, 
          success ? 'connected' : 'error',
          error
        );
      },
      
      addSearchOperation: (operation) => {
        const id = `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newOperation: SearchOperation = {
          ...operation,
          id,
          startTime: new Date()
        };
        
        set(state => ({
          activeSearches: [...state.activeSearches, newOperation]
        }));
        
        get().addActivity({
          connectorId: operation.connectorId,
          type: 'search',
          description: `Searching: ${operation.query}`,
          status: 'pending',
          details: { query: operation.query }
        });
        
        return id;
      },
      
      updateSearchOperation: (id, updates) => {
        set(state => ({
          activeSearches: state.activeSearches.map(op =>
            op.id === id ? { ...op, ...updates } : op
          )
        }));
      },
      
      completeSearchOperation: (id, results, error) => {
        const operation = get().activeSearches.find(op => op.id === id);
        if (!operation) return;
        
        set(state => ({
          activeSearches: state.activeSearches.map(op =>
            op.id === id
              ? {
                  ...op,
                  status: error ? 'error' : 'complete',
                  endTime: new Date(),
                  itemsFound: results?.length || 0,
                  preview: results?.slice(0, 5),
                  error
                }
              : op
          )
        }));
        
        // Add to search history if successful
        if (results && !error) {
          get().addToSearchHistory({
            id: `result_${Date.now()}`,
            connectorId: operation.connectorId,
            query: operation.query,
            timestamp: new Date(),
            results: results,
            totalCount: results.length
          });
        }
        
        // Log activity
        get().addActivity({
          connectorId: operation.connectorId,
          type: 'search',
          description: error ? `Search failed: ${operation.query}` : `Found ${results?.length || 0} results`,
          status: error ? 'error' : 'success',
          details: { query: operation.query, resultCount: results?.length, error }
        });
        
        // Clean up completed searches after 30 seconds
        setTimeout(() => {
          set(state => ({
            activeSearches: state.activeSearches.filter(op => op.id !== id)
          }));
        }, 30000);
      },
      
      addApprovalRequest: (request) => {
        const id = `approval_${Date.now()}`;
        const newRequest: ApprovalRequest = {
          ...request,
          id,
          timestamp: new Date(),
          status: 'pending',
          expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5 minute expiry
        };
        
        set(state => ({
          pendingApprovals: [...state.pendingApprovals, newRequest]
        }));
        
        return id;
      },
      
      handleApproval: (requestId, approved) => {
        set(state => ({
          pendingApprovals: state.pendingApprovals.map(req =>
            req.id === requestId
              ? { ...req, status: approved ? 'approved' : 'denied' }
              : req
          )
        }));
        
        // Clean up after a delay
        setTimeout(() => {
          set(state => ({
            pendingApprovals: state.pendingApprovals.filter(req => req.id !== requestId)
          }));
        }, 5000);
      },
      
      setApprovalPreference: (operation, preference) => {
        set(state => {
          const approvalPreferences = new Map(state.approvalPreferences);
          approvalPreferences.set(operation, preference);
          return { approvalPreferences };
        });
      },
      
      addActivity: (activity) => {
        const newActivity: ConnectorActivity = {
          ...activity,
          id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date()
        };
        
        set(state => {
          const recentActivity = [newActivity, ...state.recentActivity];
          // Keep only the most recent items
          if (recentActivity.length > state.maxActivityItems) {
            recentActivity.pop();
          }
          return { recentActivity };
        });
      },
      
      clearActivity: (connectorId) => {
        set(state => ({
          recentActivity: connectorId
            ? state.recentActivity.filter(a => a.connectorId !== connectorId)
            : []
        }));
      },
      
      setPreview: (preview) => {
        set({ currentPreview: preview, showPreview: !!preview });
      },
      
      togglePreview: () => {
        set(state => ({ showPreview: !state.showPreview }));
      },
      
      addToSearchHistory: (result) => {
        set(state => {
          const searchHistory = [result, ...state.searchHistory];
          // Keep only the most recent searches
          if (searchHistory.length > state.maxSearchHistory) {
            searchHistory.pop();
          }
          return { searchHistory };
        });
      },
      
      clearSearchHistory: (connectorId) => {
        set(state => ({
          searchHistory: connectorId
            ? state.searchHistory.filter(h => h.connectorId !== connectorId)
            : []
        }));
      },
      
      reset: () => {
        set(initialState);
      }
    }),
    {
      name: 'connector-store'
    }
  )
);