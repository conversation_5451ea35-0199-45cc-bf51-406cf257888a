import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type LayoutMode = 'orchestration' | 'classic' | 'compact';
export type PanelPosition = 'left' | 'right' | 'top' | 'bottom' | 'floating';

export interface PanelConfig {
  visible: boolean;
  position: PanelPosition;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  collapsed?: boolean;
  opacity?: number;
}

export interface LayoutPreferences {
  mode: LayoutMode;
  panels: {
    chat: PanelConfig;
    tasks: PanelConfig;
    agents: PanelConfig;
    resources: PanelConfig;
  };
  theme: 'light' | 'dark' | 'auto';
  animations: boolean;
  autoHideInactive: boolean;
  showConnectionLines: boolean;
  agentCardSize: 'small' | 'medium' | 'large';
}

interface LayoutStore extends LayoutPreferences {
  updatePanel: (panel: keyof LayoutPreferences['panels'], config: Partial<PanelConfig>) => void;
  setLayoutMode: (mode: LayoutMode) => void;
  togglePanel: (panel: keyof LayoutPreferences['panels']) => void;
  resetLayout: () => void;
  saveLayout: () => void;
  loadLayout: (layout: Partial<LayoutPreferences>) => void;

  setTheme: (theme: LayoutPreferences['theme']) => void;
  toggleAnimations: () => void;
  toggleAutoHide: () => void;
  toggleConnectionLines: () => void;
  setAgentCardSize: (size: LayoutPreferences['agentCardSize']) => void;

  // Drag and resize handlers
  updatePanelPosition: (panel: keyof LayoutPreferences['panels'], x: number, y: number) => void;
  updatePanelSize: (panel: keyof LayoutPreferences['panels'], width: number, height: number) => void;
}

const defaultLayout: LayoutPreferences = {
  mode: 'orchestration',
  panels: {
    chat: {
      visible: true,
      position: 'floating',
      width: 600,
      height: 500,
      x: window.innerWidth / 2 - 300,
      y: 120,
      collapsed: false,
      opacity: 0.98,
    },
    tasks: {
      visible: true,
      position: 'left',
      width: 350,
      height: 500,
      collapsed: false,
    },
    agents: {
      visible: true,
      position: 'floating',
      width: 400,
      height: 300,
      x: window.innerWidth - 450,
      y: 80,
      collapsed: false,
    },
    resources: {
      visible: true,
      position: 'bottom',
      width: 380,
      height: 180,
      collapsed: false,
    },
  },
  theme: 'auto',
  animations: true,
  autoHideInactive: false,
  showConnectionLines: true,
  agentCardSize: 'medium',
};

export const useLayoutStore = create<LayoutStore>()(
  persist(
    (set, get) => ({
      ...defaultLayout,

      updatePanel: (panel, config) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panel]: {
              ...state.panels[panel],
              ...config,
            },
          },
        }));
      },

      setLayoutMode: (mode) => {
        set({ mode });

        // Apply preset layouts based on mode
        if (mode === 'classic') {
          set({
            panels: {
              chat: {
                visible: true,
                position: 'floating',
                width: window.innerWidth,
                height: window.innerHeight,
                x: 0,
                y: 0,
                collapsed: false,
                opacity: 1,
              },
              tasks: { visible: false, position: 'left' },
              agents: { visible: false, position: 'floating' },
              resources: { visible: false, position: 'bottom' },
            },
          });
        } else if (mode === 'compact') {
          set({
            panels: {
              chat: {
                visible: true,
                position: 'left',
                width: 400,
                collapsed: false,
              },
              tasks: {
                visible: true,
                position: 'top',
                height: 100,
                collapsed: false,
              },
              agents: {
                visible: true,
                position: 'right',
                width: 350,
                collapsed: false,
              },
              resources: {
                visible: true,
                position: 'bottom',
                height: 100,
                collapsed: false,
              },
            },
          });
        }
      },

      togglePanel: (panel) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panel]: {
              ...state.panels[panel],
              visible: !state.panels[panel].visible,
            },
          },
        }));
      },

      resetLayout: () => {
        set(defaultLayout);
      },

      saveLayout: () => {
        const state = get();
        localStorage.setItem('dante-layout-backup', JSON.stringify(state));
      },

      loadLayout: (layout) => {
        set((state) => ({
          ...state,
          ...layout,
        }));
      },

      setTheme: (theme) => {
        set({ theme });
      },

      toggleAnimations: () => {
        set((state) => ({ animations: !state.animations }));
      },

      toggleAutoHide: () => {
        set((state) => ({ autoHideInactive: !state.autoHideInactive }));
      },

      toggleConnectionLines: () => {
        set((state) => ({ showConnectionLines: !state.showConnectionLines }));
      },

      setAgentCardSize: (size) => {
        set({ agentCardSize: size });
      },

      updatePanelPosition: (panel, x, y) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panel]: {
              ...state.panels[panel],
              x,
              y,
            },
          },
        }));
      },

      updatePanelSize: (panel, width, height) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panel]: {
              ...state.panels[panel],
              width,
              height,
            },
          },
        }));
      },
    }),
    {
      name: 'dante-layout-preferences',
      partialize: (state) => ({
        mode: state.mode,
        panels: state.panels,
        theme: state.theme,
        animations: state.animations,
        autoHideInactive: state.autoHideInactive,
        showConnectionLines: state.showConnectionLines,
        agentCardSize: state.agentCardSize,
      }),
    }
  )
);
