import { create } from 'zustand';

export interface AgentState {
  id: string;
  name: string;
  status: 'idle' | 'active' | 'thinking' | 'completed' | 'error';
  currentTask?: string;
  currentTool?: string;
  progress?: number;
  progressMessage?: string;
  activityLog: ActivityLogEntry[];
  startTime?: Date;
  endTime?: Date;
  tokensUsed: number;
}

export interface ActivityLogEntry {
  timestamp: Date;
  type: 'tool_call' | 'message' | 'handoff' | 'error' | 'thinking';
  content: string;
  details?: any;
}

interface AgentStore {
  agents: Map<string, AgentState>;
  activeAgents: string[];
  currentPrimaryAgent: string;
  
  activateAgent: (name: string, task?: string) => void;
  deactivateAgent: (name: string) => void;
  updateAgentStatus: (name: string, status: AgentState['status']) => void;
  addAgentActivity: (name: string, entry: ActivityLogEntry) => void;
  setCurrentTool: (name: string, tool?: string) => void;
  updateTokenUsage: (name: string, tokens: number) => void;
  updateProgress: (name: string, progress?: number, message?: string) => void;
  clearAgents: () => void;
  switchPrimaryAgent: (name: string) => void;
}

export const useAgentStore = create<AgentStore>((set, get) => ({
  agents: new Map(),
  activeAgents: [],
  currentPrimaryAgent: 'Dante',
  
  activateAgent: (name, task) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent: AgentState = agents.get(name) || {
        id: `agent-${Date.now()}`,
        name,
        status: 'active',
        currentTask: task,
        activityLog: [],
        startTime: new Date(),
        tokensUsed: 0,
      };
      
      agent.status = 'active';
      agent.currentTask = task;
      agent.startTime = new Date();
      agents.set(name, agent);
      
      const activeAgents = [...state.activeAgents];
      if (!activeAgents.includes(name)) {
        activeAgents.push(name);
      }
      
      return { agents, activeAgents };
    });
  },
  
  deactivateAgent: (name) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        agent.status = 'completed';
        agent.endTime = new Date();
        agent.currentTool = undefined;
        agents.set(name, agent);
      }
      
      const activeAgents = state.activeAgents.filter(a => a !== name);
      return { agents, activeAgents };
    });
  },
  
  updateAgentStatus: (name, status) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        agent.status = status;
        agents.set(name, agent);
      }
      return { agents };
    });
  },
  
  addAgentActivity: (name, entry) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        agent.activityLog.push(entry);
        agents.set(name, agent);
      }
      return { agents };
    });
  },
  
  setCurrentTool: (name, tool) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        agent.currentTool = tool;
        agents.set(name, agent);
      }
      return { agents };
    });
  },
  
  updateTokenUsage: (name, tokens) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        agent.tokensUsed += tokens;
        agents.set(name, agent);
      }
      return { agents };
    });
  },

  updateProgress: (name, progress, message) => {
    set((state) => {
      const agents = new Map(state.agents);
      const agent = agents.get(name);
      if (agent) {
        if (typeof progress === 'number') agent.progress = Math.max(0, Math.min(100, progress));
        if (message) agent.progressMessage = message;
        agents.set(name, agent);
      }
      return { agents };
    });
  },
  
  clearAgents: () => {
    set({ agents: new Map(), activeAgents: [] });
  },
  
  switchPrimaryAgent: (name) => {
    set({ currentPrimaryAgent: name });
  },
}));
