import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface ProjectMetadata {
  packageJson?: any;
  dependencies?: string[];
  devDependencies?: string[];
  scripts?: string[];
  gitRepo?: boolean;
  lastModified?: number;
}

export interface ProjectContext {
  path: string;
  name: string;
  type: 'react' | 'node' | 'python' | 'rust' | 'go' | 'java' | 'cpp' | 'generic';
  metadata: ProjectMetadata;
  recentFiles: string[];
  lastAccessed: number;
}

interface ProjectStore {
  currentProject: ProjectContext | null;
  recentProjects: ProjectContext[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentProject: (project: ProjectContext | null) => void;
  addRecentProject: (project: ProjectContext) => void;
  removeRecentProject: (path: string) => void;
  clearRecentProjects: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateProjectMetadata: (path: string, metadata: Partial<ProjectMetadata>) => void;
  addRecentFile: (filePath: string) => void;
}

// Helper function to detect project type
export const detectProjectType = (metadata: ProjectMetadata): ProjectContext['type'] => {
  const deps = [...(metadata.dependencies || []), ...(metadata.devDependencies || [])];
  
  if (deps.includes('react') || deps.includes('@types/react')) return 'react';
  if (metadata.packageJson) return 'node';
  // Add more detection logic as needed
  return 'generic';
};

// Helper function to extract project name from path
export const extractProjectName = (path: string): string => {
  const segments = path.split('/').filter(Boolean);
  return segments[segments.length - 1] || 'Unknown Project';
};

// Helper function to analyze project metadata
export const analyzeProject = async (path: string): Promise<ProjectMetadata> => {
  const metadata: ProjectMetadata = {
    lastModified: Date.now(),
  };

  try {
    // Check for package.json
    const response = await fetch('/api/analyze-project', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ path }),
    });
    
    if (response.ok) {
      const analysis = await response.json();
      metadata.packageJson = analysis.packageJson;
      metadata.dependencies = analysis.dependencies;
      metadata.devDependencies = analysis.devDependencies;
      metadata.scripts = analysis.scripts;
      metadata.gitRepo = analysis.gitRepo;
    }
  } catch (error) {
    console.warn('Failed to analyze project:', error);
  }

  return metadata;
};

export const useProjectStore = create<ProjectStore>()(
  persist(
    (set, get) => ({
      currentProject: null,
      recentProjects: [],
      isLoading: false,
      error: null,

      setCurrentProject: (project) => {
        set({ currentProject: project, error: null });
        
        // Add to recent projects if it's a new project
        if (project) {
          const { addRecentProject } = get();
          addRecentProject(project);
        }
      },

      addRecentProject: (project) => {
        set((state) => {
          const existing = state.recentProjects.findIndex(p => p.path === project.path);
          let newRecent = [...state.recentProjects];
          
          if (existing >= 0) {
            // Update existing project and move to front
            newRecent[existing] = { ...project, lastAccessed: Date.now() };
            newRecent = [newRecent[existing], ...newRecent.slice(0, existing), ...newRecent.slice(existing + 1)];
          } else {
            // Add new project to front
            newRecent = [{ ...project, lastAccessed: Date.now() }, ...newRecent];
          }
          
          // Keep only the 10 most recent projects
          return { recentProjects: newRecent.slice(0, 10) };
        });
      },

      removeRecentProject: (path) => {
        set((state) => ({
          recentProjects: state.recentProjects.filter(p => p.path !== path),
        }));
      },

      clearRecentProjects: () => {
        set({ recentProjects: [] });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },

      updateProjectMetadata: (path, metadata) => {
        set((state) => {
          const updatedRecent = state.recentProjects.map(project => 
            project.path === path 
              ? { ...project, metadata: { ...project.metadata, ...metadata } }
              : project
          );
          
          const updatedCurrent = state.currentProject?.path === path
            ? { ...state.currentProject, metadata: { ...state.currentProject.metadata, ...metadata } }
            : state.currentProject;
            
          return {
            recentProjects: updatedRecent,
            currentProject: updatedCurrent,
          };
        });
      },

      addRecentFile: (filePath) => {
        set((state) => {
          if (!state.currentProject) return state;
          
          const updatedProject = {
            ...state.currentProject,
            recentFiles: [
              filePath,
              ...state.currentProject.recentFiles.filter(f => f !== filePath)
            ].slice(0, 20), // Keep only 20 most recent files
          };
          
          return { currentProject: updatedProject };
        });
      },
    }),
    {
      name: 'dante-project-store',
      partialize: (state) => ({
        recentProjects: state.recentProjects,
        currentProject: state.currentProject,
      }),
    }
  )
);