import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface TraceSpan {
  id: string;
  traceId: string;
  parentId?: string;
  type: 'agent' | 'generation' | 'function' | 'handoff' | 'guardrail' | 'custom';
  name: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  status: 'running' | 'completed' | 'failed';
  data?: any;
  children: string[]; // IDs of child spans
}

export interface TraceInfo {
  id: string;
  workflowName: string;
  groupId?: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed';
  rootSpanIds: string[]; // Top-level spans in this trace
  totalSpans: number;
  totalDuration?: number;
}

export interface SpanMetrics {
  totalFunctionCalls: number;
  totalGenerations: number;
  totalHandoffs: number;
  averageDuration: number;
  totalTokens?: {
    input: number;
    output: number;
    total: number;
  };
}

interface TraceStore {
  // Trace data
  traces: Map<string, TraceInfo>;
  spans: Map<string, TraceSpan>;
  
  // UI state
  selectedTraceId?: string;
  selectedSpanId?: string;
  isTracingPanelOpen: boolean;
  showCompletedTraces: boolean;
  filterByAgent?: string;
  
  // Live tracking
  activeSpans: Set<string>; // Currently running spans
  recentActivity: Array<{
    spanId: string;
    timestamp: Date;
    type: 'started' | 'completed' | 'failed';
  }>;
  
  // Actions
  addTrace: (trace: Omit<TraceInfo, 'totalSpans'>) => void;
  updateTrace: (traceId: string, updates: Partial<TraceInfo>) => void;
  completeTrace: (traceId: string) => void;
  
  addSpan: (span: Omit<TraceSpan, 'children'>) => void;
  updateSpan: (spanId: string, updates: Partial<TraceSpan>) => void;
  completeSpan: (spanId: string, result?: any) => void;
  failSpan: (spanId: string, error?: any) => void;
  
  linkSpans: (parentId: string, childId: string) => void;
  
  selectTrace: (traceId?: string) => void;
  selectSpan: (spanId?: string) => void;
  
  toggleTracingPanel: () => void;
  setTracingPanelOpen: (open: boolean) => void;
  
  setShowCompletedTraces: (show: boolean) => void;
  setFilterByAgent: (agent?: string) => void;
  
  clearTraces: () => void;
  clearOldTraces: (olderThan: Date) => void;
  
  // Computed properties
  getSpansByTrace: (traceId: string) => TraceSpan[];
  getChildSpans: (spanId: string) => TraceSpan[];
  getTraceMetrics: (traceId: string) => SpanMetrics;
  getRunningTraces: () => TraceInfo[];
  getRecentSpans: (limit?: number) => TraceSpan[];
}

export const useTraceStore = create<TraceStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    traces: new Map(),
    spans: new Map(),
    selectedTraceId: undefined,
    selectedSpanId: undefined,
    isTracingPanelOpen: false,
    showCompletedTraces: true,
    filterByAgent: undefined,
    activeSpans: new Set<string>(),
    recentActivity: [],
    
    // Trace management
    addTrace: (trace) => {
      set((state) => {
        const newTraces = new Map(state.traces);
        newTraces.set(trace.id, {
          ...trace,
          totalSpans: 0
        });
        return { traces: newTraces };
      });
    },
    
    updateTrace: (traceId, updates) => {
      set((state) => {
        const newTraces = new Map(state.traces);
        const existing = newTraces.get(traceId);
        if (existing) {
          newTraces.set(traceId, { ...existing, ...updates });
        }
        return { traces: newTraces };
      });
    },
    
    completeTrace: (traceId) => {
      set((state) => {
        const newTraces = new Map(state.traces);
        const trace = newTraces.get(traceId);
        if (trace) {
          const traceSpans = Array.from(state.spans.values())
            .filter(span => span.traceId === traceId);
          
          const endTime = new Date();
          const totalDuration = endTime.getTime() - trace.startTime.getTime();
          
          newTraces.set(traceId, {
            ...trace,
            endTime,
            totalDuration,
            status: 'completed',
            totalSpans: traceSpans.length
          });
        }
        return { traces: newTraces };
      });
    },
    
    // Span management
    addSpan: (span) => {
      set((state) => {
        const newSpans = new Map(state.spans);
        const newActiveSpans = new Set(state.activeSpans);
        
        const spanWithChildren: TraceSpan = {
          ...span,
          children: []
        };
        
        newSpans.set(span.id, spanWithChildren);
        
        if (span.status === 'running') {
          newActiveSpans.add(span.id);
        }
        
        // Update trace span count
        const newTraces = new Map(state.traces);
        const trace = newTraces.get(span.traceId);
        if (trace) {
          newTraces.set(span.traceId, {
            ...trace,
            totalSpans: trace.totalSpans + 1
          });
          
          // Add to root spans if no parent
          if (!span.parentId && !trace.rootSpanIds.includes(span.id)) {
            trace.rootSpanIds.push(span.id);
          }
        }
        
        // Add to recent activity
        const newActivity = [...state.recentActivity, {
          spanId: span.id,
          timestamp: span.startTime,
          type: 'started' as const
        }].slice(-50); // Keep last 50 activities
        
        return { 
          spans: newSpans, 
          activeSpans: newActiveSpans,
          traces: newTraces,
          recentActivity: newActivity
        };
      });
    },
    
    updateSpan: (spanId, updates) => {
      set((state) => {
        const newSpans = new Map(state.spans);
        const existing = newSpans.get(spanId);
        if (existing) {
          newSpans.set(spanId, { ...existing, ...updates });
        }
        return { spans: newSpans };
      });
    },
    
    completeSpan: (spanId, result) => {
      set((state) => {
        const newSpans = new Map(state.spans);
        const newActiveSpans = new Set(state.activeSpans);
        
        const span = newSpans.get(spanId);
        if (span) {
          const endTime = new Date();
          const duration = endTime.getTime() - span.startTime.getTime();
          
          newSpans.set(spanId, {
            ...span,
            endTime,
            duration,
            status: 'completed',
            data: { ...span.data, result }
          });
          
          newActiveSpans.delete(spanId);
          
          // Add to recent activity
          const newActivity = [...state.recentActivity, {
            spanId,
            timestamp: endTime,
            type: 'completed' as const
          }].slice(-50);
          
          return { 
            spans: newSpans, 
            activeSpans: newActiveSpans,
            recentActivity: newActivity
          };
        }
        return state;
      });
    },
    
    failSpan: (spanId, error) => {
      set((state) => {
        const newSpans = new Map(state.spans);
        const newActiveSpans = new Set(state.activeSpans);
        
        const span = newSpans.get(spanId);
        if (span) {
          const endTime = new Date();
          const duration = endTime.getTime() - span.startTime.getTime();
          
          newSpans.set(spanId, {
            ...span,
            endTime,
            duration,
            status: 'failed',
            data: { ...span.data, error }
          });
          
          newActiveSpans.delete(spanId);
          
          // Add to recent activity
          const newActivity = [...state.recentActivity, {
            spanId,
            timestamp: endTime,
            type: 'failed' as const
          }].slice(-50);
          
          return { 
            spans: newSpans, 
            activeSpans: newActiveSpans,
            recentActivity: newActivity
          };
        }
        return state;
      });
    },
    
    linkSpans: (parentId, childId) => {
      set((state) => {
        const newSpans = new Map(state.spans);
        const parent = newSpans.get(parentId);
        const child = newSpans.get(childId);
        
        if (parent && child && !parent.children.includes(childId)) {
          newSpans.set(parentId, {
            ...parent,
            children: [...parent.children, childId]
          });
          
          newSpans.set(childId, {
            ...child,
            parentId
          });
        }
        
        return { spans: newSpans };
      });
    },
    
    // UI state management
    selectTrace: (traceId) => {
      set({ selectedTraceId: traceId, selectedSpanId: undefined });
    },
    
    selectSpan: (spanId) => {
      set({ selectedSpanId: spanId });
    },
    
    toggleTracingPanel: () => {
      set((state) => ({ isTracingPanelOpen: !state.isTracingPanelOpen }));
    },
    
    setTracingPanelOpen: (open) => {
      set({ isTracingPanelOpen: open });
    },
    
    setShowCompletedTraces: (show) => {
      set({ showCompletedTraces: show });
    },
    
    setFilterByAgent: (agent) => {
      set({ filterByAgent: agent });
    },
    
    // Cleanup
    clearTraces: () => {
      set({
        traces: new Map(),
        spans: new Map(),
        activeSpans: new Set<string>(),
        recentActivity: [],
        selectedTraceId: undefined,
        selectedSpanId: undefined
      });
    },
    
    clearOldTraces: (olderThan) => {
      set((state) => {
        const newTraces = new Map();
        const newSpans = new Map();
        const newActiveSpans = new Set<string>();
        
        // Keep traces newer than the cutoff
        for (const [traceId, trace] of state.traces) {
          if (trace.startTime >= olderThan) {
            newTraces.set(traceId, trace);
            
            // Keep associated spans
            for (const [spanId, span] of state.spans) {
              if (span.traceId === traceId) {
                newSpans.set(spanId, span);
                if (state.activeSpans.has(spanId)) {
                  newActiveSpans.add(spanId);
                }
              }
            }
          }
        }
        
        return {
          ...state,
          traces: newTraces,
          spans: newSpans,
          activeSpans: newActiveSpans,
          recentActivity: state.recentActivity.filter(
            activity => activity.timestamp >= olderThan
          )
        };
      });
    },
    
    // Computed selectors
    getSpansByTrace: (traceId) => {
      const state = get();
      return Array.from(state.spans.values())
        .filter(span => span.traceId === traceId)
        .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
    },
    
    getChildSpans: (spanId) => {
      const state = get();
      const span = state.spans.get(spanId);
      if (!span) return [];
      
      return span.children
        .map(childId => state.spans.get(childId))
        .filter(Boolean) as TraceSpan[];
    },
    
    getTraceMetrics: (traceId) => {
      const state = get();
      const spans = state.getSpansByTrace(traceId);
      
      const metrics: SpanMetrics = {
        totalFunctionCalls: 0,
        totalGenerations: 0,
        totalHandoffs: 0,
        averageDuration: 0,
        totalTokens: {
          input: 0,
          output: 0,
          total: 0
        }
      };
      
      let totalDuration = 0;
      let completedSpans = 0;
      
      for (const span of spans) {
        switch (span.type) {
          case 'function':
            metrics.totalFunctionCalls++;
            break;
          case 'generation':
            metrics.totalGenerations++;
            if (span.data?.tokens) {
              metrics.totalTokens!.input += span.data.tokens.input || 0;
              metrics.totalTokens!.output += span.data.tokens.output || 0;
              metrics.totalTokens!.total += span.data.tokens.total || 0;
            }
            break;
          case 'handoff':
            metrics.totalHandoffs++;
            break;
        }
        
        if (span.duration) {
          totalDuration += span.duration;
          completedSpans++;
        }
      }
      
      if (completedSpans > 0) {
        metrics.averageDuration = totalDuration / completedSpans;
      }
      
      return metrics;
    },
    
    getRunningTraces: () => {
      const state = get();
      return Array.from(state.traces.values())
        .filter(trace => trace.status === 'running')
        .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
    },
    
    getRecentSpans: (limit = 10) => {
      const state = get();
      return state.recentActivity
        .slice(-limit)
        .map(activity => state.spans.get(activity.spanId))
        .filter(Boolean) as TraceSpan[];
    }
  }))
);