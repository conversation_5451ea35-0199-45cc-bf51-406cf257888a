import { create } from 'zustand';

export interface TokenUsage {
  used: number;
  limit: number;
  cost: number;
  model: string;
  timestamp: Date;
}

export interface TokenSnapshot {
  timestamp: Date;
  totalUsed: number;
  totalCost: number;
  byAgent: Map<string, number>;
  byModel: Map<string, number>;
}

interface TokenStore {
  currentUsage: TokenUsage;
  sessionTotal: number;
  sessionCost: number;
  history: TokenSnapshot[];
  rateLimitWarning: boolean;
  rateLimitResetTime?: Date;
  // Running breakdown by model for current session
  sessionByModel: Map<string, { input: number; output: number; total: number; cost: number }>;

  // Model pricing (per 1M tokens)
  modelPricing: Map<string, { input: number; output: number }>;

  updateUsage: (tokens: number, model: string, isOutput?: boolean) => void;
  addAgentUsage: (agentName: string, tokens: number) => void;
  setRateLimit: (limit: number, resetTime?: Date) => void;
  clearRateLimitWarning: () => void;

  takeSnapshot: () => void;
  getUsageByTimeRange: (start: Date, end: Date) => TokenSnapshot[];
  getCostEstimate: (tokens: number, model: string, isOutput?: boolean) => number;

  resetSession: () => void;
  setModelPricing: (model: string, inputPrice: number, outputPrice: number) => void;
}

export const useTokenStore = create<TokenStore>((set, get) => ({
  currentUsage: {
    used: 0,
    limit: 128000, // Default GPT-5-mini limit
    cost: 0,
    model: 'gpt-5-mini',
    timestamp: new Date(),
  },
  sessionTotal: 0,
  sessionCost: 0,
  history: [],
  rateLimitWarning: false,
  sessionByModel: new Map(),

  modelPricing: new Map([
    // GPT-5 series (Standard tier prices per 1M tokens)
    ['gpt-5', { input: 1.25, output: 10.00 }],
    ['gpt-5-codex', { input: 1.25, output: 10.00 }],
    ['gpt-5-mini', { input: 0.25, output: 2.00 }],
    ['gpt-5-nano', { input: 0.05, output: 0.40 }],
    ['gpt-5-chat-latest', { input: 1.25, output: 10.00 }],

    // GPT-4.1 series
    ['gpt-4.1', { input: 2.00, output: 8.00 }],
    ['gpt-4.1-mini', { input: 0.40, output: 1.60 }],
    ['gpt-4.1-nano', { input: 0.10, output: 0.40 }],

    // GPT-4o series
    ['gpt-4o', { input: 2.50, output: 10.00 }],
    ['gpt-4o-2024-05-13', { input: 5.00, output: 15.00 }],
    ['gpt-4o-mini', { input: 0.15, output: 0.60 }],
    ['gpt-4o-audio-preview', { input: 2.50, output: 10.00 }],
    ['gpt-4o-realtime-preview-2025-06-03', { input: 5.00, output: 20.00 }],
    ['gpt-4o-realtime-preview', { input: 5.00, output: 20.00 }],
    ['gpt-4o-mini-audio-preview', { input: 0.15, output: 0.60 }],
    ['gpt-4o-mini-realtime-preview', { input: 0.60, output: 2.40 }],
    ['gpt-4o-mini-search-preview', { input: 0.15, output: 0.60 }],
    ['gpt-4o-search-preview', { input: 2.50, output: 10.00 }],

    // O-series
    ['o1', { input: 15.00, output: 60.00 }],
    ['o1-pro', { input: 150.00, output: 600.00 }],
    ['o1-mini', { input: 1.10, output: 4.40 }],
    ['o3', { input: 2.00, output: 8.00 }],
    ['o3-pro', { input: 20.00, output: 80.00 }],
    ['o3-deep-research', { input: 10.00, output: 40.00 }],
    ['o3-mini', { input: 1.10, output: 4.40 }],
    ['o4-mini', { input: 1.10, output: 4.40 }],
    ['o4-mini-deep-research', { input: 2.00, output: 8.00 }],

    // Other models
    ['computer-use-preview', { input: 3.00, output: 12.00 }],
    ['codex-mini-latest', { input: 1.50, output: 6.00 }],
    ['gpt-image-1', { input: 5.00, output: 0 }],

    // Gemini models (support both provider ids)
    ['models/gemini-2.5-pro', { input: 2.50, output: 15.00 }],
    ['models/gemini-2.5-flash', { input: 0.30, output: 2.50 }],
    ['models/gemini-flash-latest', { input: 0.30, output: 2.50 }],
    ['models/gemini-2.5-flash-lite', { input: 0.10, output: 0.40 }],
    ['models/gemini-flash-lite-latest', { input: 0.10, output: 0.40 }],
    ['gemini-2.5-pro', { input: 2.50, output: 15.00 }],
    ['gemini-2.5-flash', { input: 0.30, output: 2.50 }],
    ['gemini-2.5-flash-lite', { input: 0.10, output: 0.40 }],

    // Legacy models (Standard tier)
    ['chatgpt-4o-latest', { input: 5.00, output: 15.00 }],
    ['gpt-4-turbo-2024-04-09', { input: 10.00, output: 30.00 }],
    ['gpt-4-0125-preview', { input: 10.00, output: 30.00 }],
    ['gpt-4-1106-preview', { input: 10.00, output: 30.00 }],
    ['gpt-4-1106-vision-preview', { input: 10.00, output: 30.00 }],
    ['gpt-4-0613', { input: 30.00, output: 60.00 }],
    ['gpt-4-0314', { input: 30.00, output: 60.00 }],
    ['gpt-4-32k', { input: 60.00, output: 120.00 }],
    ['gpt-3.5-turbo', { input: 0.50, output: 1.50 }],
    ['gpt-3.5-turbo-0125', { input: 0.50, output: 1.50 }],
    ['gpt-3.5-turbo-1106', { input: 1.00, output: 2.00 }],
    ['gpt-3.5-turbo-0613', { input: 1.50, output: 2.00 }],
    ['gpt-3.5-0301', { input: 1.50, output: 2.00 }],
    ['gpt-3.5-turbo-instruct', { input: 1.50, output: 2.00 }],
    ['gpt-3.5-turbo-16k-0613', { input: 3.00, output: 4.00 }],
    ['davinci-002', { input: 2.00, output: 2.00 }],
    ['babbage-002', { input: 0.40, output: 0.40 }],
  ]),

  updateUsage: (tokens, model, isOutput = false) => {
    set((state) => {
      const pricing = state.modelPricing.get(model) || { input: 1.25, output: 10.00 }; // Default to gpt-5 pricing
      const rate = isOutput ? pricing.output : pricing.input;
      const cost = (tokens / 1000000) * rate; // Rate is per 1M tokens

      const newUsed = state.currentUsage.used + tokens;
      const newCost = state.currentUsage.cost + cost;

      // Check for rate limit warning (80% threshold)
      const rateLimitWarning = newUsed >= state.currentUsage.limit * 0.8;

      // Update session model breakdown
      const byModel = new Map(state.sessionByModel);
      const prev = byModel.get(model) || { input: 0, output: 0, total: 0, cost: 0 };
      if (isOutput) prev.output += tokens; else prev.input += tokens;
      prev.total += tokens;
      prev.cost += cost;
      byModel.set(model, prev);

      return {
        currentUsage: {
          ...state.currentUsage,
          used: newUsed,
          cost: newCost,
          model,
          timestamp: new Date(),
        },
        sessionTotal: state.sessionTotal + tokens,
        sessionCost: state.sessionCost + cost,
        rateLimitWarning,
        sessionByModel: byModel,
      };
    });
  },

  addAgentUsage: (agentName, tokens) => {
    // This is tracked separately in snapshots
    const state = get();
    const lastSnapshot = state.history[state.history.length - 1];

    if (lastSnapshot) {
      const agentUsage = lastSnapshot.byAgent.get(agentName) || 0;
      lastSnapshot.byAgent.set(agentName, agentUsage + tokens);
    }
  },

  setRateLimit: (limit, resetTime) => {
    set((state) => ({
      currentUsage: {
        ...state.currentUsage,
        limit,
      },
      rateLimitResetTime: resetTime,
      rateLimitWarning: state.currentUsage.used >= limit * 0.8,
    }));
  },

  clearRateLimitWarning: () => {
    set({ rateLimitWarning: false });
  },

  takeSnapshot: () => {
    set((state) => {
      const snapshot: TokenSnapshot = {
        timestamp: new Date(),
        totalUsed: state.sessionTotal,
        totalCost: state.sessionCost,
        byAgent: new Map(),
        byModel: new Map(
          Array.from(state.sessionByModel.entries()).map(([m, v]) => [m, v.total])
        ),
      };

      return {
        history: [...state.history, snapshot],
      };
    });
  },

  getUsageByTimeRange: (start, end) => {
    const state = get();
    return state.history.filter(
      snapshot => snapshot.timestamp >= start && snapshot.timestamp <= end
    );
  },

  getCostEstimate: (tokens, model, isOutput = false) => {
    const state = get();
    const pricing = state.modelPricing.get(model) || { input: 1.25, output: 10.00 }; // Default to gpt-5 pricing
    const rate = isOutput ? pricing.output : pricing.input;
    return (tokens / 1000000) * rate; // Rate is per 1M tokens
  },

  resetSession: () => {
    set((state) => ({
      currentUsage: {
        used: 0,
        limit: state.currentUsage.limit,
        cost: 0,
        model: state.currentUsage.model,
        timestamp: new Date(),
      },
      sessionTotal: 0,
      sessionCost: 0,
      history: [],
      rateLimitWarning: false,
      rateLimitResetTime: undefined,
      sessionByModel: new Map(),
    }));
  },

  setModelPricing: (model, inputPrice, outputPrice) => {
    set((state) => {
      const modelPricing = new Map(state.modelPricing);
      modelPricing.set(model, { input: inputPrice, output: outputPrice });
      return { modelPricing };
    });
  },
}));
