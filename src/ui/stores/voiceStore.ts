import { create } from 'zustand';
import { voiceAgentService, VoiceStatus } from '../services/voiceAgent';

interface VoiceState {
  enabled: boolean;
  status: VoiceStatus;
  history: any[];
  error?: string;
  selectedVoice: string;
  mode: 'realtime' | 'chained';
  isRecording: boolean;
  transcription?: string;
  setSelectedVoice: (voice: string) => void;
  start: () => Promise<void>;
  stop: () => Promise<void>;
  interrupt: () => Promise<void>;
  sendText: (text: string, opts?: { mode?: 'ask' | 'say' }) => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
}

export const useVoiceStore = create<VoiceState>((set, get) => ({
  enabled: false,
  status: 'idle',
  history: [],
  error: undefined,
  selectedVoice: (typeof localStorage !== 'undefined' && localStorage.getItem('dante_voice')) || 'verse',
  mode: 'realtime',
  isRecording: false,
  transcription: undefined,
  setSelectedVoice: (voice: string) => {
    try { if (typeof localStorage !== 'undefined') localStorage.setItem('dante_voice', voice); } catch {}
    try { voiceAgentService.setPreferredVoice(voice); } catch {}
    set({ selectedVoice: voice });
  },

  start: async () => {
    try {
      // Check if voiceAgentService is available
      if (!voiceAgentService) {
        throw new Error('Voice service is not available in this environment');
      }
      
      voiceAgentService.setCallbacks({
        onStatus: (status, info) => {
          set({ 
            status, 
            enabled: status !== 'stopped' && status !== 'idle', 
            error: status === 'error' ? (info?.message || 'Voice error') : undefined,
            isRecording: info?.recording || false
          });
        },
        onHistoryUpdate: (history) => set({ history }),
        onTranscription: (text) => set({ transcription: text }),
      });
      const voice = get().selectedVoice;
      await voiceAgentService.start({ voice, syncTypedHistory: true });
      set({ 
        enabled: true, 
        mode: voiceAgentService.getMode(),
        isRecording: voiceAgentService.isRecording() 
      });
    } catch (e: any) {
      console.error('Failed to start voice agent:', e);
      set({ status: 'error', error: e?.message || 'Failed to start voice agent', enabled: false });
    }
  },

  stop: async () => {
    await voiceAgentService.stop();
    set({ enabled: false, status: 'stopped' });
  },

  interrupt: async () => {
    await voiceAgentService.interrupt();
  },

  sendText: async (text: string, opts?: { mode?: 'ask' | 'say' }) => {
    await voiceAgentService.sendText(text, opts);
  },

  startRecording: async () => {
    try {
      await voiceAgentService.startRecording();
      set({ isRecording: true, transcription: undefined });
    } catch (e: any) {
      console.error('Failed to start recording:', e);
      set({ error: e?.message || 'Failed to start recording' });
    }
  },

  stopRecording: async () => {
    try {
      await voiceAgentService.stopRecording();
      set({ isRecording: false });
    } catch (e: any) {
      console.error('Failed to stop recording:', e);
      set({ error: e?.message || 'Failed to stop recording' });
    }
  },
}));
