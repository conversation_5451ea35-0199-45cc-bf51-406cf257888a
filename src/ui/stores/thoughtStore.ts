import { create } from 'zustand';

export type ThoughtStage = 'start' | 'delta' | 'complete' | 'error';

export interface UIThoughtMessage {
  id: string;
  promptId: string; // use sessionId when runId not available
  agentId: string;
  stepId?: string;
  parentId?: string;
  stage: ThoughtStage;
  content?: string;
  ts: number;
  seq: number;
}

type ThoughtKey = string; // `${promptId}:${stepId || 'root'}:${agentId}`

interface ThoughtState {
  threads: Map<ThoughtKey, UIThoughtMessage[]>;
  latestByAgent: Map<string, UIThoughtMessage>; // agentId -> last thought
  addThought: (t: Omit<UIThoughtMessage, 'id' | 'seq' | 'ts'> & Partial<Pick<UIThoughtMessage, 'id' | 'seq' | 'ts'>>) => void;
  clearRun: (promptId: string) => void;
  getThread: (promptId: string, agentId: string, stepId?: string) => UIThoughtMessage[];
  getLatestForAgent: (agentId: string) => UIThoughtMessage | undefined;
}

export const useThoughtStore = create<ThoughtState>((set, get) => ({
  threads: new Map(),
  latestByAgent: new Map(),
  addThought: (msg) => set((state) => {
    const ts = msg.ts ?? Date.now();
    const pid = msg.promptId || 'session';
    const agent = msg.agentId || 'Agent';
    const sid = msg.stepId || 'root';
    const key: ThoughtKey = `${pid}:${sid}:${agent}`;
    const arr = state.threads.get(key) ? [...(state.threads.get(key) as UIThoughtMessage[])] : [];
    const seq = (arr.length > 0 ? arr[arr.length - 1].seq + 1 : 1);
    const id = msg.id || `${key}#${seq}`;
    const t: UIThoughtMessage = { id, promptId: pid, agentId: agent, stepId: msg.stepId, parentId: msg.parentId, stage: msg.stage, content: msg.content, ts, seq };
    arr.push(t);
    const threads = new Map(state.threads);
    threads.set(key, arr.slice(-500)); // cap per-thread history
    const latestByAgent = new Map(state.latestByAgent);
    latestByAgent.set(agent, t);
    return { threads, latestByAgent };
  }),
  clearRun: (promptId: string) => set((state) => {
    const threads = new Map(state.threads);
    for (const k of Array.from(threads.keys())) {
      if (k.startsWith(`${promptId}:`)) threads.delete(k);
    }
    return { threads } as any;
  }),
  getThread: (promptId: string, agentId: string, stepId?: string) => {
    const sid = stepId || 'root';
    const key: ThoughtKey = `${promptId}:${sid}:${agentId}`;
    return get().threads.get(key) || [];
  },
  getLatestForAgent: (agentId: string) => {
    return get().latestByAgent.get(agentId);
  },
}));

