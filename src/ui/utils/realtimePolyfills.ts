/**
 * Polyfills and patches for @openai/agents/realtime module
 * This ensures compatibility with remote devices and various browser environments
 */

export function applyRealtimePolyfills() {
  if (typeof window === 'undefined') return;

  // Store original console methods for debugging
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // Patch console methods to catch and handle specific errors
  console.error = function(...args: any[]) {
    const errorString = args.join(' ');
    
    // Catch the specific bind error and try to fix it
    if (errorString.includes("Cannot read properties of undefined (reading 'bind')")) {
      console.warn('[Realtime Polyfill] Caught bind error, attempting to patch...');
      
      // Try to patch the issue
      try {
        // Ensure all prototype methods exist
        if (!Function.prototype.bind) {
          Function.prototype.bind = createBindPolyfill();
        }
        
        // Check for other potential issues
        ensureWebAPIsExist();
      } catch (patchError) {
        console.warn('[Realtime Polyfill] Patch failed:', patchError);
      }
      
      // Don't propagate this specific error
      return;
    }
    
    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };

  // Ensure Function.prototype.bind exists
  if (!Function.prototype.bind) {
    Function.prototype.bind = createBindPolyfill();
  }

  // Ensure all required Web APIs exist
  ensureWebAPIsExist();

  // Patch potential timing issues with module loading
  patchModuleLoader();
}

function createBindPolyfill() {
  return function(this: Function, oThis: any) {
    if (typeof this !== 'function') {
      throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');
    }

    const aArgs = Array.prototype.slice.call(arguments, 1);
    const fToBind = this;
    const fNOP = function() {} as any;
    const fBound = function(this: any) {
      return fToBind.apply(
        this instanceof fNOP ? this : oThis,
        aArgs.concat(Array.prototype.slice.call(arguments))
      );
    };

    if (this.prototype) {
      fNOP.prototype = this.prototype;
    }
    fBound.prototype = new fNOP();

    return fBound;
  };
}

function ensureWebAPIsExist() {
  if (typeof window === 'undefined') return;

  // Ensure WebSocket exists
  if (!window.WebSocket) {
    console.warn('[Realtime Polyfill] WebSocket not found, voice features may not work');
  }

  // Ensure AudioContext exists
  if (!window.AudioContext && !(window as any).webkitAudioContext) {
    console.warn('[Realtime Polyfill] AudioContext not found, voice features may not work');
    
    // Create a minimal stub to prevent errors
    (window as any).AudioContext = class AudioContextStub {
      createBufferSource() { return {}; }
      createGain() { return { connect: () => {}, gain: { value: 1 } }; }
      decodeAudioData() { return Promise.reject('AudioContext not supported'); }
      get destination() { return {}; }
      get currentTime() { return 0; }
      close() { return Promise.resolve(); }
      get state() { return 'closed'; }
    };
  }

  // Ensure MediaRecorder exists
  if (!window.MediaRecorder) {
    console.warn('[Realtime Polyfill] MediaRecorder not found, recording features may not work');
  }

  // Ensure performance.now exists
  if (!window.performance || !window.performance.now) {
    window.performance = window.performance || {} as any;
    window.performance.now = window.performance.now || (() => Date.now());
  }

  // Ensure navigator.mediaDevices exists
  if (!navigator.mediaDevices) {
    console.warn('[Realtime Polyfill] navigator.mediaDevices not found, microphone access may not work');
    (navigator as any).mediaDevices = {
      getUserMedia: () => Promise.reject(new Error('MediaDevices not supported')),
      enumerateDevices: () => Promise.resolve([])
    };
  }

  // Ensure RTCPeerConnection exists (needed for WebRTC)
  const RTCPeerConnection = (window as any).RTCPeerConnection || 
                           (window as any).webkitRTCPeerConnection || 
                           (window as any).mozRTCPeerConnection;
  
  if (!RTCPeerConnection) {
    console.warn('[Realtime Polyfill] RTCPeerConnection not found, WebRTC features may not work');
  } else if (!(window as any).RTCPeerConnection) {
    (window as any).RTCPeerConnection = RTCPeerConnection;
  }
}

function patchModuleLoader() {
  // Intercept dynamic imports to handle errors gracefully
  const originalImport = (window as any).__import || ((m: string) => /* @vite-ignore */ import(m));
  
  (window as any).__import = async function(moduleSpecifier: string) {
    if (moduleSpecifier.includes('@openai/agents/realtime') || moduleSpecifier.includes('deprecated-realtime')) {
      try {
        // Ensure polyfills are applied before loading
        ensureWebAPIsExist();
        
        // Add a small delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 10));
        
        return await originalImport(moduleSpecifier);
      } catch (error) {
        console.warn('[Realtime Polyfill] Module load error, applying fixes and retrying...', error);
        
        // Apply more aggressive patches
        applyAggressivePatches();
        
        // Retry after patches
        return await originalImport(moduleSpecifier);
      }
    }
    
    return originalImport(moduleSpecifier);
  };
}

function applyAggressivePatches() {
  // Ensure all Object methods exist
  if (!Object.assign) {
    Object.assign = function(target: any, ...sources: any[]) {
      sources.forEach(source => {
        for (const key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      });
      return target;
    };
  }

  // Ensure Promise exists
  if (typeof Promise === 'undefined') {
    console.error('[Realtime Polyfill] Promise not found, this browser is too old');
  }

  // Ensure Array methods exist
  if (!Array.prototype.find) {
    Array.prototype.find = function(predicate: any) {
      for (let i = 0; i < this.length; i++) {
        if (predicate(this[i], i, this)) {
          return this[i];
        }
      }
      return undefined;
    };
  }

  if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement: any, fromIndex?: number) {
      return this.indexOf(searchElement, fromIndex) !== -1;
    };
  }
}

// Auto-apply polyfills when this module is imported
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  // Apply immediately if DOM is ready, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyRealtimePolyfills);
  } else {
    applyRealtimePolyfills();
  }
}

export default applyRealtimePolyfills;
