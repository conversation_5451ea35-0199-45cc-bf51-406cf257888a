/* Orchestration Dashboard Styles */
/* stylelint-disable at-rule-no-unknown */

/* Layout Transitions */
.orchestration-mode {
  @apply transition-all duration-300 ease-in-out;
}

/* Floating Panel Base */
.floating-panel {
  @apply shadow-2xl rounded-lg border bg-white dark:bg-gray-900;
  backdrop-filter: blur(10px);
  transition: opacity 0.3s ease;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.floating-panel.dragging {
  opacity: 0.9;
  cursor: move !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Panel Headers */
.panel-header {
  @apply flex items-center justify-between px-4 py-2 rounded-t-lg;
  @apply bg-gradient-to-r text-white cursor-move select-none;
}

.panel-header.chat {
  @apply from-blue-500 to-purple-600;
}

.panel-header.tasks {
  @apply from-green-500 to-teal-600;
}

.panel-header.agents {
  @apply from-purple-500 to-indigo-600;
}

.panel-header.resources {
  @apply from-orange-500 to-red-600;
}

/* Agent Cards */
.agent-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg;
  @apply border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-200;
}

.agent-card:hover {
  @apply shadow-xl transform scale-105;
}

.agent-card.active {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.agent-card.thinking {
  @apply ring-2 ring-yellow-500 ring-opacity-50;
  animation: pulse-thinking 2s infinite;
}

.agent-card.error {
  @apply ring-2 ring-red-500 ring-opacity-50;
}

/* Task Cards */
.task-card {
  @apply bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm;
  @apply border-l-4 transition-all duration-200;
}

.task-card:hover {
  @apply shadow-md;
  transform: translateX(0.25rem);
}

.task-priority-low {
  @apply border-gray-300;
}

.task-priority-medium {
  @apply border-blue-400;
}

.task-priority-high {
  @apply border-orange-400;
}

.task-priority-critical {
  @apply border-red-500;
  animation: pulse-critical 1s infinite;
}

/* Progress Bars */
.progress-bar {
  @apply relative h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-bar-fill {
  @apply absolute top-0 left-0 h-full rounded-full;
  @apply bg-gradient-to-r from-blue-500 to-purple-600;
  transition: width 0.3s ease;
}

.progress-bar-fill.animated {
  animation: progress-pulse 1.5s ease-in-out infinite;
}

/* Token Usage Gauge */
.token-gauge {
  @apply relative w-20 h-20;
}

.token-gauge-track {
  @apply absolute inset-0 rounded-full;
  @apply bg-gray-200 dark:bg-gray-700;
}

.token-gauge-fill {
  @apply absolute inset-0 rounded-full;
  transition: stroke-dashoffset 0.5s ease;
}

.token-gauge-fill.safe {
  @apply text-green-500;
}

.token-gauge-fill.warning {
  @apply text-yellow-500;
}

.token-gauge-fill.danger {
  @apply text-red-500;
  animation: pulse-danger 1s infinite;
}

/* Connection Lines */
.agent-connection {
  @apply absolute pointer-events-none;
  z-index: 1;
}

.agent-connection-line {
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
  stroke-dasharray: 5, 5;
  animation: dash-flow 1s linear infinite;
}

.agent-connection-line.active {
  @apply text-blue-500;
  stroke-width: 3;
  stroke-dasharray: 10, 5;
}

.agent-connection-line.handoff {
  @apply text-purple-500;
  animation: handoff-pulse 0.5s ease;
}

/* Activity Log */
.activity-log {
  @apply overflow-y-auto max-h-64;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.activity-log::-webkit-scrollbar {
  width: 6px;
}

.activity-log::-webkit-scrollbar-track {
  background: transparent;
}

.activity-log::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

.activity-log-item {
  @apply flex items-start gap-2 px-3 py-2;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors;
}

.activity-log-item.enter {
  animation: slide-in-left 0.3s ease;
}

/* Resize Handles */
.resize-handle {
  @apply absolute w-4 h-4 cursor-se-resize;
  bottom: 0;
  right: 0;
}

.resize-handle::before {
  content: '';
  @apply absolute bottom-0 right-0;
  @apply border-b-2 border-r-2 border-gray-400;
  width: 10px;
  height: 10px;
  border-radius: 0 0 4px 0;
}

/* Floating Chat Window Resize Handles */
.floating-chat-resizable .react-resizable-handle {
  position: absolute;
  background-color: transparent;
  z-index: 10;
}

.floating-chat-resizable .react-resizable-handle::after {
  content: '';
  position: absolute;
  box-sizing: border-box;
  background: transparent;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

/* Corner handles */
.floating-chat-resizable .react-resizable-handle-se,
.floating-chat-resizable .react-resizable-handle-sw,
.floating-chat-resizable .react-resizable-handle-ne,
.floating-chat-resizable .react-resizable-handle-nw {
  width: 20px;
  height: 20px;
}

.floating-chat-resizable .react-resizable-handle-se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.floating-chat-resizable .react-resizable-handle-se::after {
  bottom: 3px;
  right: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid #94a3b8;
  border-bottom: 2px solid #94a3b8;
}

.floating-chat-resizable .react-resizable-handle-sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}

.floating-chat-resizable .react-resizable-handle-sw::after {
  bottom: 3px;
  left: 3px;
  width: 5px;
  height: 5px;
  border-left: 2px solid #94a3b8;
  border-bottom: 2px solid #94a3b8;
}

.floating-chat-resizable .react-resizable-handle-ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.floating-chat-resizable .react-resizable-handle-ne::after {
  top: 3px;
  right: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid #94a3b8;
  border-top: 2px solid #94a3b8;
}

.floating-chat-resizable .react-resizable-handle-nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.floating-chat-resizable .react-resizable-handle-nw::after {
  top: 3px;
  left: 3px;
  width: 5px;
  height: 5px;
  border-left: 2px solid #94a3b8;
  border-top: 2px solid #94a3b8;
}

/* Edge handles */
.floating-chat-resizable .react-resizable-handle-e,
.floating-chat-resizable .react-resizable-handle-w {
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 50px;
}

.floating-chat-resizable .react-resizable-handle-e {
  right: 0;
  cursor: e-resize;
}

.floating-chat-resizable .react-resizable-handle-e::after {
  right: 3px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 20px;
  background: #94a3b8;
  border-radius: 2px;
}

.floating-chat-resizable .react-resizable-handle-w {
  left: 0;
  cursor: w-resize;
}

.floating-chat-resizable .react-resizable-handle-w::after {
  left: 3px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 20px;
  background: #94a3b8;
  border-radius: 2px;
}

.floating-chat-resizable .react-resizable-handle-n,
.floating-chat-resizable .react-resizable-handle-s {
  left: 50%;
  transform: translateX(-50%);
  height: 10px;
  width: 50px;
}

.floating-chat-resizable .react-resizable-handle-n {
  top: 0;
  cursor: n-resize;
}

.floating-chat-resizable .react-resizable-handle-n::after {
  top: 3px;
  left: 50%;
  transform: translateX(-50%);
  height: 2px;
  width: 20px;
  background: #94a3b8;
  border-radius: 2px;
}

.floating-chat-resizable .react-resizable-handle-s {
  bottom: 0;
  cursor: s-resize;
}

.floating-chat-resizable .react-resizable-handle-s::after {
  bottom: 3px;
  left: 50%;
  transform: translateX(-50%);
  height: 2px;
  width: 20px;
  background: #94a3b8;
  border-radius: 2px;
}

/* Hover effects for resize handles */
.floating-chat-resizable:hover .react-resizable-handle::after {
  border-color: #3b82f6;
  background-color: #3b82f6;
}

.floating-chat-resizable .react-resizable-handle:hover::after {
  border-color: #2563eb !important;
  background-color: #2563eb !important;
}

/* Dark mode adjustments */
.dark .floating-chat-resizable .react-resizable-handle::after {
  border-color: #64748b;
  background-color: #64748b;
}

.dark .floating-chat-resizable:hover .react-resizable-handle::after {
  border-color: #60a5fa;
  background-color: #60a5fa;
}

.dark .floating-chat-resizable .react-resizable-handle:hover::after {
  border-color: #93c5fd !important;
  background-color: #93c5fd !important;
}

/* Animations */
@keyframes pulse-thinking {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(234, 179, 8, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(234, 179, 8, 0);
  }
}

@keyframes pulse-critical {
  0%, 100% {
    border-color: rgb(239, 68, 68);
  }
  50% {
    border-color: rgb(239, 68, 68, 0.5);
  }
}

@keyframes pulse-danger {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes progress-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes dash-flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -15;
  }
}

@keyframes handoff-pulse {
  0% {
    stroke-width: 2;
    opacity: 1;
  }
  50% {
    stroke-width: 5;
    opacity: 0.7;
  }
  100% {
    stroke-width: 2;
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .floating-panel {
    @apply relative w-full h-auto;
    position: relative !important;
  }
  
  .agent-grid {
    grid-template-columns: 1fr !important;
  }
  
  .task-columns {
    @apply flex-col;
  }
  
  .resource-monitor {
    @apply bottom-0 left-0 right-0;
    border-radius: 0;
  }
}

/* Dark Mode Overrides */
.dark {
  .floating-panel {
    backdrop-filter: blur(20px);
  }
  
  .agent-card.active {
    @apply ring-blue-400 ring-opacity-60;
  }
  
  .progress-bar-fill {
    @apply from-blue-400 to-purple-500;
  }
}

/* Layout Mode Classes */
.layout-orchestration {
  .main-chat {
    @apply opacity-95;
  }
  
  /* Ensure proper stacking */
  .task-panel {
    z-index: 30;
  }
  
  .resource-monitor {
    z-index: 30;
  }
  
  .agent-grid {
    z-index: 45;
  }
  
  .chat-window {
    z-index: 60;
  }
}

.layout-classic {
  .orchestration-panels {
    @apply hidden;
  }
  
  .main-chat {
    @apply w-full h-full opacity-100;
  }
}

.layout-compact {
  .floating-panel {
    @apply relative;
  }
  
  .main-content {
    @apply grid grid-cols-3 gap-2;
  }
}