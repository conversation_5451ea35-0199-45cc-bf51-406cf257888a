/* Import orchestration styles first */
@import './styles/orchestration.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
}

.message-appear {
  animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  animation: typing 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}

/* Enhanced Markdown Styling */
.markdown-content {
  line-height: 1.7;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  position: relative;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child {
  margin-top: 0;
}

.markdown-content h1 {
  font-size: 1.75rem;
  border-bottom: 2px solid;
  padding-bottom: 0.5rem;
  @apply border-gray-200 dark:border-gray-700;
}

.markdown-content h2 {
  font-size: 1.5rem;
  @apply text-gray-800 dark:text-gray-100;
}

.markdown-content h3 {
  font-size: 1.25rem;
  @apply text-gray-700 dark:text-gray-200;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.markdown-content li {
  margin: 0.5rem 0;
  line-height: 1.7;
}

.markdown-content ul li {
  position: relative;
}

.markdown-content ul li::before {
  content: "•";
  position: absolute;
  left: -1rem;
  @apply text-blue-500;
  font-weight: bold;
}

.markdown-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 py-2 my-4 italic bg-gray-50 dark:bg-gray-800/50 rounded-r-lg;
}

.markdown-content pre {
  @apply rounded-lg overflow-x-auto my-4 p-4 bg-gray-900 dark:bg-black;
  max-width: 100%;
}

.markdown-content pre code {
  @apply text-sm;
  background: transparent !important;
  padding: 0 !important;
}

.markdown-content :not(pre) > code {
  @apply px-1.5 py-0.5 rounded bg-gray-100 dark:bg-gray-800 text-sm font-mono text-blue-600 dark:text-blue-400;
}

.markdown-content table {
  @apply w-full border-collapse my-4 overflow-x-auto block;
}

.markdown-content table thead {
  @apply bg-gray-50 dark:bg-gray-800;
}

.markdown-content table th,
.markdown-content table td {
  @apply border border-gray-200 dark:border-gray-700 px-4 py-2 text-left;
}

.markdown-content table th {
  @apply font-semibold;
}

.markdown-content hr {
  @apply my-8 border-t-2 border-gray-200 dark:border-gray-700;
}

.markdown-content a {
  @apply text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline transition-colors;
}

.markdown-content img {
  @apply rounded-lg shadow-md my-4 max-w-full h-auto;
}

/* Syntax highlighting improvements */
.hljs {
  @apply !bg-gray-900;
}

/* Scrollbar styling for code blocks */
.markdown-content pre::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.markdown-content pre::-webkit-scrollbar-track {
  @apply bg-gray-800 rounded;
}

.markdown-content pre::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded hover:bg-gray-500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .markdown-content h1 {
    font-size: 1.5rem;
  }
  
  .markdown-content h2 {
    font-size: 1.25rem;
  }
  
  .markdown-content h3 {
    font-size: 1.1rem;
  }
  
  .markdown-content pre {
    @apply p-3 text-xs;
  }
}

/* Custom Scrollbar Styles */
/* Light Theme Scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400;
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

::-webkit-scrollbar-thumb:active {
  @apply bg-gray-600;
}

/* Dark Theme Scrollbar */
.dark ::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

.dark ::-webkit-scrollbar-thumb {
  @apply bg-gray-600;
}

.dark ::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

.dark ::-webkit-scrollbar-thumb:active {
  @apply bg-gray-400;
}

/* Firefox Scrollbar Support */
* {
  scrollbar-width: thin;
  scrollbar-color: #9ca3af #f3f4f6;
}

.dark * {
  scrollbar-color: #4b5563 #1f2937;
}

/* Specific scrollbar for message list */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-transparent;
  margin: 4px 0;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-700;
  border-radius: 8px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-600;
}

/* Code block scrollbar override to keep existing style */
.markdown-content pre::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.markdown-content pre::-webkit-scrollbar-track {
  @apply bg-gray-800 rounded;
}

.markdown-content pre::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded hover:bg-gray-500;
}