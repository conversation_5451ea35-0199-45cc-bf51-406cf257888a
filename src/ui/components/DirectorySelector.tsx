import React, { useState, useEffect } from 'react';
import { 
  Folder, 
  FolderOpen, 
  File, 
  ChevronRight, 
  ChevronDown, 
  Home, 
  Clock,
  X,
  Search,
  Star,
  GitBranch
} from 'lucide-react';
import { useProjectStore, analyzeProject, detectProjectType, extractProjectName, ProjectContext } from '../stores/projectStore';

interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileSystemItem[];
  expanded?: boolean;
}

interface DirectorySelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDirectory: (path: string) => void;
}

const DirectorySelector: React.FC<DirectorySelectorProps> = ({ 
  isOpen, 
  onClose, 
  onSelectDirectory 
}) => {
  const { recentProjects, addRecentProject, setCurrentProject, setLoading, setError } = useProjectStore();
  const [currentPath, setCurrentPath] = useState('/Users');
  const [fileTree, setFileTree] = useState<FileSystemItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isTreeLoading, setIsTreeLoading] = useState(false);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());

  // Load directory contents
  const loadDirectory = async (path: string): Promise<FileSystemItem[]> => {
    try {
      const response = await fetch('/api/list-directory', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          dirPath: path,
          includeHidden: false,
          recursive: false 
        }),
      });

      if (!response.ok) throw new Error('Failed to load directory');
      
      const result = await response.json();
      
      if (result.success) {
        return result.files
          .filter((item: any) => !item.name.startsWith('.') && item.name !== 'node_modules')
          .sort((a: any, b: any) => {
            // Directories first, then files
            if (a.type !== b.type) {
              return a.type === 'directory' ? -1 : 1;
            }
            return a.name.localeCompare(b.name);
          })
          .map((item: any) => ({
            name: item.name,
            path: item.path,
            type: item.type,
            children: item.type === 'directory' ? [] : undefined,
            expanded: false,
          }));
      }
      
      return [];
    } catch (error) {
      console.error('Error loading directory:', error);
      return [];
    }
  };

  // Initialize file tree
  useEffect(() => {
    if (isOpen) {
      setIsTreeLoading(true);
      loadDirectory(currentPath).then(items => {
        setFileTree(items);
        setIsTreeLoading(false);
      });
    }
  }, [isOpen, currentPath]);

  // Toggle directory expansion
  const toggleDirectory = async (path: string) => {
    const newExpanded = new Set(expandedPaths);
    
    if (expandedPaths.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
      // Load children if not already loaded
      const updateTree = (items: FileSystemItem[]): FileSystemItem[] => {
        return items.map(item => {
          if (item.path === path && item.type === 'directory') {
            if (!item.children || item.children.length === 0) {
              // Load children asynchronously
              loadDirectory(path).then(children => {
                setFileTree(prevTree => updateTreeWithChildren(prevTree, path, children));
              });
            }
            return { ...item, expanded: true };
          }
          if (item.children) {
            return { ...item, children: updateTree(item.children) };
          }
          return item;
        });
      };
      
      setFileTree(updateTree(fileTree));
    }
    
    setExpandedPaths(newExpanded);
  };

  // Helper to update tree with loaded children
  const updateTreeWithChildren = (items: FileSystemItem[], targetPath: string, children: FileSystemItem[]): FileSystemItem[] => {
    return items.map(item => {
      if (item.path === targetPath) {
        return { ...item, children, expanded: true };
      }
      if (item.children) {
        return { ...item, children: updateTreeWithChildren(item.children, targetPath, children) };
      }
      return item;
    });
  };

  // Navigate to parent directory
  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    setCurrentPath(parentPath);
  };

  // Navigate to specific path
  const navigateToPath = (path: string) => {
    setCurrentPath(path);
    setExpandedPaths(new Set());
  };

  // Select a directory as project
  const selectDirectory = async (path: string) => {
    setLoading(true);
    setError(null);

    try {
      const metadata = await analyzeProject(path);
      const projectType = detectProjectType(metadata);
      const projectName = extractProjectName(path);

      const project: ProjectContext = {
        path,
        name: projectName,
        type: projectType,
        metadata,
        recentFiles: [],
        lastAccessed: Date.now(),
      };

      setCurrentProject(project);
      addRecentProject(project);
      onSelectDirectory(path);
      onClose();
    } catch (error) {
      setError('Failed to analyze project directory');
      console.error('Error selecting directory:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render file tree item
  const renderTreeItem = (item: FileSystemItem, depth: number = 0) => {
    const isExpanded = expandedPaths.has(item.path);
    const paddingLeft = `${depth * 20 + 12}px`;

    return (
      <div key={item.path}>
        <div
          className="flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm"
          style={{ paddingLeft }}
          onClick={() => {
            if (item.type === 'directory') {
              toggleDirectory(item.path);
            }
          }}
        >
          <div className="flex items-center flex-1 min-w-0">
            {item.type === 'directory' && (
              <button className="p-0.5 mr-1">
                {isExpanded ? (
                  <ChevronDown className="w-3 h-3 text-gray-400" />
                ) : (
                  <ChevronRight className="w-3 h-3 text-gray-400" />
                )}
              </button>
            )}
            {item.type === 'directory' ? (
              isExpanded ? (
                <FolderOpen className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
              ) : (
                <Folder className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
              )
            ) : (
              <File className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0 ml-4" />
            )}
            <span className="truncate text-gray-700 dark:text-gray-300">
              {item.name}
            </span>
          </div>
          {item.type === 'directory' && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                selectDirectory(item.path);
              }}
              className="ml-2 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Select
            </button>
          )}
        </div>
        
        {item.type === 'directory' && isExpanded && item.children && (
          <div>
            {item.children.map(child => renderTreeItem(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  // Filter recent projects based on search
  const filteredRecentProjects = recentProjects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.path.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-4/5 h-4/5 max-w-4xl max-h-3xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
            Select Project Directory
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Recent Projects Sidebar */}
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 flex flex-col">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 bg-gray-50 dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-3">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Recent Projects
                </h3>
                
                {filteredRecentProjects.length === 0 ? (
                  <p className="text-sm text-gray-500 dark:text-gray-400 py-4">
                    {searchQuery ? 'No matching projects found' : 'No recent projects'}
                  </p>
                ) : (
                  filteredRecentProjects.map((project) => (
                    <div
                      key={project.path}
                      onClick={() => selectDirectory(project.path)}
                      className="p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Folder className="w-4 h-4 text-blue-500 flex-shrink-0" />
                            <span className="font-medium text-sm text-gray-800 dark:text-white truncate">
                              {project.name}
                            </span>
                            {project.metadata.gitRepo && (
                              <GitBranch className="w-3 h-3 text-gray-400" />
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {project.path}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded">
                              {project.type}
                            </span>
                            {project.metadata.dependencies && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {project.metadata.dependencies.length} deps
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* File Browser */}
          <div className="flex-1 flex flex-col">
            {/* Navigation */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <button
                  onClick={() => navigateToPath('/Users')}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title="Home"
                >
                  <Home className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                </button>
                <button
                  onClick={navigateUp}
                  disabled={currentPath === '/'}
                  className="px-2 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
                >
                  Up
                </button>
                <button
                  onClick={() => selectDirectory(currentPath)}
                  className="px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Select Current
                </button>
              </div>
              
              {/* Breadcrumb */}
              <div className="text-sm text-gray-600 dark:text-gray-400 font-mono bg-gray-50 dark:bg-gray-900 p-2 rounded">
                {currentPath}
              </div>
            </div>

            {/* File Tree */}
            <div className="flex-1 overflow-y-auto">
              {isTreeLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-gray-500 dark:text-gray-400">Loading...</div>
                </div>
              ) : (
                <div className="py-2">
                  {fileTree.map(item => renderTreeItem(item))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectorySelector;