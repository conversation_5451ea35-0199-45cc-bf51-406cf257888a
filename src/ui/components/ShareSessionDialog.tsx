import React, { useState } from 'react';
import { sessionManager } from '../../utils/sessionManager';

interface ShareSessionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId?: string;
}

export function ShareSessionDialog({ isOpen, onClose, sessionId }: ShareSessionDialogProps) {
  const [copied, setCopied] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [loading, setLoading] = useState(true);
  
  React.useEffect(() => {
    if (isOpen) {
      console.log('[ShareDialog] Dialog opened, generating URL for session:', sessionId);
      setLoading(true);
      setShareUrl('');
      
      sessionManager.generateShareableSessionUrl(sessionId)
        .then(url => {
          console.log('[ShareDialog] ✅ Generated share URL:', url);
          setShareUrl(url);
          setLoading(false);
        })
        .catch(error => {
          console.error('[ShareDialog] ❌ Failed to generate share URL:', error);
          // Fallback to current location if network fetch fails
          const fallbackUrl = `${window.location.protocol}//${window.location.host}?session=${encodeURIComponent(sessionId || '')}`;
          console.log('[ShareDialog] Using fallback URL:', fallbackUrl);
          setShareUrl(fallbackUrl);
          setLoading(false);
        });
    }
  }, [isOpen, sessionId]);
  
  if (!isOpen) return null;
  
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const generateQRCode = () => {
    // Simple QR code generation using a service
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(shareUrl)}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Share Conversation
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Share this URL with other devices on your private network to continue this conversation:
        </p>
        
        <div className="mb-4">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                Generating network URL...
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
              <button
                onClick={copyToClipboard}
                disabled={!shareUrl}
                className="px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-md text-sm transition-colors"
              >
                {copied ? 'Copied!' : 'Copy'}
              </button>
            </div>
          )}
        </div>
        
        <div className="text-center mb-4">
          {loading || !shareUrl ? (
            <div className="w-[200px] h-[200px] mx-auto bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Generating QR code...
                </p>
              </div>
            </div>
          ) : (
            <>
              <img
                src={generateQRCode()}
                alt="QR Code for session URL"
                className="mx-auto rounded-lg"
                width="200"
                height="200"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Scan with your phone's camera
              </p>
            </>
          )}
        </div>
        
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3 mb-4">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Note:</strong> This link only works on your private network. 
            Make sure all devices are connected to the same WiFi.
          </p>
        </div>
        
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}