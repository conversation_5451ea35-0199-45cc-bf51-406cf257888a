import React, { useState, useRef, useEffect, lazy, Suspense } from 'react';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { ThinkingIndicator } from '../../components/ThinkingIndicator';
import { useTraceStore } from '../stores/traceStore';
import { useTraceEvents } from '../hooks/useTraceEvents';
import { Activity, ChevronLeft, ChevronRight, Layers, X } from 'lucide-react';
import VoiceStatusBar from './VoiceStatusBar';
import VoiceLiveTranscript from './VoiceLiveTranscript';

// Import connector components
import ConnectorStatusBar from './connectors/ConnectorStatusBar';
import GoogleWorkspacePanel from './connectors/GoogleWorkspacePanel';
import SearchPreviewModal from './connectors/SearchPreviewModal';
import { useConnectorStore } from '../stores/connectorStore';

// Lazy load heavy components
const AgentActivity = lazy(() => import('../../components/AgentActivity').then(m => ({ default: m.AgentActivity })));
const TracingPanel = lazy(() => import('./tracing/TracingPanel').then(m => ({ default: m.TracingPanel })));

// Loading component
const ActivityLoadingFallback = () => (
  <div className="flex items-center justify-center p-4">
    <div className="text-gray-600 dark:text-gray-400 text-sm">Loading activity...</div>
  </div>
);

export interface AgentEvent {
  type: string;
  timestamp: string;
  data: {
    message?: string;
    agent?: string;
    tool?: string;
    args?: any;
    result?: any;
    reasoning?: string;
    tokens?: {
      used?: number;
      model?: string;
      isOutput?: boolean;
    };
  };
}

interface ChatInterfaceProps {
  messages: Array<{ 
    role: string; 
    content: string;
    reasoning?: string;
    confidence?: number;
    model?: string;
    orchestrator?: string;
    isStreaming?: boolean;
  }>;
  onSendMessage: (message: string, attachments: { url: string; file?: File }[]) => void;
  isLoading: boolean;
  agentEvents?: AgentEvent[];
  isThinking?: boolean;
  currentAgent?: string;
  isRateLimited?: boolean;
  currentOrchestrator?: string;
  orchestrationDetails?: {
    contextUsage?: { used: number; total: number };
    thinkingBudget?: { used: number; total: number };
    activeAgents?: string[];
  };
  onCancelRun?: (agentName?: string) => void;
  onRetryRun?: (agentName?: string) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ 
  messages, 
  onSendMessage, 
  isLoading,
  agentEvents = [],
  isThinking = false,
  currentAgent = 'Dante',
  isRateLimited = false,
  currentOrchestrator,
  orchestrationDetails,
  onCancelRun,
  onRetryRun
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { isTracingPanelOpen, toggleTracingPanel } = useTraceStore();
  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth > 768); // Default open on desktop
  const [showWorkspacePanel, setShowWorkspacePanel] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  
  // Connector state
  const { 
    currentPreview, 
    activeSearches, 
    pendingApprovals 
  } = useConnectorStore();
  
  const hasActiveSearches = activeSearches.length > 0;
  const hasPendingApprovals = pendingApprovals.filter(a => a.status === 'pending').length > 0;
  
  // Initialize trace events integration
  useTraceEvents();

  // Handle window resize to auto-collapse on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Show preview modal when there's a new preview
  useEffect(() => {
    if (currentPreview) {
      setShowPreviewModal(true);
    }
  }, [currentPreview]);

  return (
    <div className="flex-1 flex bg-gray-50 dark:bg-gray-900 h-full overflow-hidden relative">
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <div className="flex-1 overflow-hidden flex flex-col">
          <VoiceStatusBar />
          
          {/* Connector Status Bar */}
          <ConnectorStatusBar 
            compact={false}
            showDetails={true}
            onSettingsClick={() => setShowWorkspacePanel(!showWorkspacePanel)}
          />
          
          {/* Mobile Activity Toggle */}
          <div className="lg:hidden flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Chat</span>
            <div className="flex items-center gap-2">
              {/* Workspace Panel Toggle */}
              <button
                onClick={() => setShowWorkspacePanel(!showWorkspacePanel)}
                className={`p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 transition-colors ${
                  (hasActiveSearches || hasPendingApprovals) ? 'relative' : ''
                }`}
                title="Toggle workspace panel"
              >
                <Layers size={16} />
                {(hasActiveSearches || hasPendingApprovals) && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                )}
              </button>
              
              {/* Activity Panel Toggle */}
              <button
                onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                className="p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 transition-colors"
                title="Toggle activity panel"
              >
                <Activity size={16} />
                {agentEvents.length > 0 && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full" />
                )}
              </button>
            </div>
          </div>

          <MessageList 
            messages={messages} 
            isLoading={isLoading} 
            currentOrchestrator={currentOrchestrator}
          />
          {/* Live transcription bubble while listening/recording */}
          <VoiceLiveTranscript />
          {(isThinking || isRateLimited) && (
            <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
              <ThinkingIndicator 
                message={isRateLimited 
                  ? "Request queued due to rate limits..."
                  : `${currentAgent} is thinking...`
                }
                variant="default"
              />
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700">
          <MessageInput onSendMessage={onSendMessage} isLoading={isLoading} />
        </div>
      </div>
      
      {/* Agent Activity Sidebar */}
      <div className={`
        ${isSidebarOpen ? 'w-80' : 'w-0'} 
        ${isSidebarOpen ? 'lg:w-80' : 'lg:w-0'}
        border-l border-gray-200 dark:border-gray-700 flex flex-col h-full 
        transition-all duration-300 ease-in-out overflow-hidden
        ${!isSidebarOpen && 'lg:border-l-0'}
      `}>
        {/* Collapse/Expand Button for Desktop */}
        <div className="hidden lg:block absolute -left-6 top-1/2 transform -translate-y-1/2 z-10">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="p-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-l-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            title={isSidebarOpen ? "Collapse activity panel" : "Expand activity panel"}
          >
            {isSidebarOpen ? (
              <ChevronRight size={14} className="text-gray-500 dark:text-gray-400" />
            ) : (
              <ChevronLeft size={14} className="text-gray-500 dark:text-gray-400" />
            )}
          </button>
        </div>

        {/* Activity Header with Tracing Toggle */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">Agent Activity</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleTracingPanel}
              className={`p-2 rounded-md transition-colors ${
                isTracingPanelOpen
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                  : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
              }`}
              title="Toggle detailed tracing view"
            >
              <Activity size={16} />
            </button>
            {/* Mobile close button */}
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="lg:hidden p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800 transition-colors"
              title="Close activity panel"
            >
              <ChevronRight size={16} />
            </button>
          </div>
        </div>
        
        <div className="flex-1 p-4 overflow-y-auto min-w-0">
          <Suspense fallback={<ActivityLoadingFallback />}>
            <AgentActivity 
              events={agentEvents} 
              isActive={isLoading || isThinking}
              currentOrchestrator={currentOrchestrator}
              orchestrationDetails={orchestrationDetails}
              onCancelRun={onCancelRun}
              onRetryRun={onRetryRun}
            />
          </Suspense>
        </div>
      </div>

      {/* Mobile overlay when sidebar is open */}
      {isSidebarOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div className={`
        lg:hidden fixed right-0 top-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl z-30
        transform transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}
      `}>
        {/* Activity Header with Tracing Toggle */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">Agent Activity</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleTracingPanel}
              className={`p-2 rounded-md transition-colors ${
                isTracingPanelOpen
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                  : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
              }`}
              title="Toggle detailed tracing view"
            >
              <Activity size={16} />
            </button>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800 transition-colors"
              title="Close activity panel"
            >
              <ChevronRight size={16} />
            </button>
          </div>
        </div>
        
        <div className="flex-1 p-4 overflow-y-auto">
          <Suspense fallback={<ActivityLoadingFallback />}>
            <AgentActivity 
              events={agentEvents} 
              isActive={isLoading || isThinking}
              currentOrchestrator={currentOrchestrator}
              orchestrationDetails={orchestrationDetails}
            />
          </Suspense>
        </div>
      </div>
      
      {/* Google Workspace Panel */}
      {showWorkspacePanel && (
        <div className="absolute top-0 right-0 z-30 w-96 h-full shadow-xl">
          <GoogleWorkspacePanel
            className="h-full"
            maxHeight="100%"
            showHistory={true}
            autoScroll={true}
          />
          <button
            onClick={() => setShowWorkspacePanel(false)}
            className="absolute top-3 right-3 p-1 bg-white dark:bg-gray-800 rounded-md shadow hover:bg-gray-100 dark:hover:bg-gray-700"
            title="Close workspace panel"
          >
            <X size={16} />
          </button>
        </div>
      )}
      
      {/* Search Preview Modal */}
      <SearchPreviewModal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        searchResult={currentPreview || undefined}
        onConfirm={(selectedItems) => {
          console.log('Confirmed items:', selectedItems);
          // TODO: Send confirmed items to Dante for processing
        }}
        onRefine={(newQuery) => {
          console.log('Refine search with:', newQuery);
          // TODO: Trigger new search with refined query
        }}
        onReject={() => {
          console.log('Results rejected');
          // TODO: Notify Dante that results were incorrect
        }}
      />
      
      {/* Tracing Panel Overlay */}
      {isTracingPanelOpen && (
        <div className="absolute inset-0 z-40 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="w-full h-full max-w-7xl">
            <Suspense fallback={<ActivityLoadingFallback />}>
              <TracingPanel />
            </Suspense>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
