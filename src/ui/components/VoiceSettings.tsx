import React, { useState, useEffect } from 'react';
import { 
  WHISPER_LANGUAGES, 
  getPopularLanguages, 
  searchLanguages, 
  getLanguageByCode,
  type WhisperLanguage 
} from '../../config/whisperLanguages';

interface VoiceSettingsProps {
  className?: string;
}

export const VoiceSettings: React.FC<VoiceSettingsProps> = ({ className = '' }) => {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showAllLanguages, setShowAllLanguages] = useState<boolean>(false);
  const [filteredLanguages, setFilteredLanguages] = useState<WhisperLanguage[]>(getPopularLanguages());

  // Load saved language preference from localStorage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('dante-transcription-language');
    if (savedLanguage) {
      setSelectedLanguage(savedLanguage);
    }
  }, []);

  // Filter languages based on search query
  useEffect(() => {
    if (searchQuery.trim()) {
      const results = searchLanguages(searchQuery);
      setFilteredLanguages(results);
    } else {
      setFilteredLanguages(showAllLanguages ? WHISPER_LANGUAGES : getPopularLanguages());
    }
  }, [searchQuery, showAllLanguages]);

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    localStorage.setItem('dante-transcription-language', languageCode);
    
    // Update the voice agent with the new language
    if (window.voiceAgent) {
      window.voiceAgent.setTranscriptionLanguage(languageCode);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const toggleShowAllLanguages = () => {
    setShowAllLanguages(!showAllLanguages);
    setSearchQuery(''); // Clear search when toggling
  };

  const selectedLangInfo = getLanguageByCode(selectedLanguage);

  return (
    <div className={`voice-settings ${className}`}>
      <div className="voice-settings-header">
        <h2>Voice & Transcription Settings</h2>
        <p className="settings-description">
          Configure your preferred language for voice transcription. This affects how Dante 
          transcribes your voice input when using voice mode.
        </p>
      </div>

      {/* Current Selection */}
      <div className="current-selection">
        <h3>Current Transcription Language</h3>
        <div className="selected-language-card">
          <div className="language-flag">🌐</div>
          <div className="language-info">
            <div className="language-name">{selectedLangInfo?.name || 'English'}</div>
            <div className="language-native">{selectedLangInfo?.nativeName || 'English'}</div>
            <div className="language-code">Code: {selectedLanguage}</div>
          </div>
        </div>
      </div>

      {/* Language Selection */}
      <div className="language-selection">
        <h3>Choose Transcription Language</h3>
        
        {/* Search Bar */}
        <div className="search-container">
          <input
            type="text"
            placeholder="Search languages..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="language-search"
          />
          <button 
            onClick={toggleShowAllLanguages}
            className={`show-all-btn ${showAllLanguages ? 'active' : ''}`}
          >
            {showAllLanguages ? 'Show Popular Only' : `Show All ${WHISPER_LANGUAGES.length} Languages`}
          </button>
        </div>

        {/* Results Info */}
        <div className="results-info">
          Showing {filteredLanguages.length} language{filteredLanguages.length !== 1 ? 's' : ''}
          {searchQuery && ` for "${searchQuery}"`}
        </div>

        {/* Language List */}
        <div className="language-list">
          {filteredLanguages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageSelect(language.code)}
              className={`language-option ${selectedLanguage === language.code ? 'selected' : ''}`}
            >
              <div className="language-primary">
                <span className="language-name">{language.name}</span>
                <span className="language-code">({language.code})</span>
              </div>
              {language.nativeName && language.nativeName !== language.name && (
                <div className="language-native">{language.nativeName}</div>
              )}
            </button>
          ))}
        </div>

        {filteredLanguages.length === 0 && (
          <div className="no-results">
            <p>No languages found matching "{searchQuery}"</p>
            <button onClick={() => setSearchQuery('')} className="clear-search-btn">
              Clear search
            </button>
          </div>
        )}
      </div>

      {/* Additional Info */}
      <div className="settings-info">
        <h4>About Language Support</h4>
        <ul>
          <li>Dante uses OpenAI's Whisper model for transcription, supporting {WHISPER_LANGUAGES.length} languages</li>
          <li>Accuracy may vary by language - English has the highest accuracy</li>
          <li>Language setting applies to voice transcription only, not text responses</li>
          <li>Changes take effect immediately for new voice inputs</li>
        </ul>
      </div>
    </div>
  );
};

// Add CSS styles
const styles = `
.voice-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.voice-settings-header {
  margin-bottom: 2rem;
}

.voice-settings-header h2 {
  color: #1a202c;
  margin-bottom: 0.5rem;
  font-size: 1.75rem;
  font-weight: 600;
}

.dark .voice-settings-header h2 {
  color: #f7fafc;
}

.settings-description {
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.5;
}

.dark .settings-description {
  color: #a0aec0;
}

.current-selection {
  margin-bottom: 2rem;
}

.current-selection h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.dark .current-selection h3 {
  color: #e2e8f0;
}

.selected-language-card {
  display: flex;
  align-items: center;
  background: #f7fafc;
  border: 2px solid #4299e1;
  border-radius: 0.75rem;
  padding: 1rem;
  gap: 1rem;
}

.dark .selected-language-card {
  background: #2d3748;
  border-color: #4299e1;
}

.language-flag {
  font-size: 2rem;
}

.language-info .language-name {
  font-weight: 600;
  color: #1a202c;
  font-size: 1.125rem;
}

.dark .language-info .language-name {
  color: #f7fafc;
}

.language-info .language-native {
  color: #4a5568;
  font-size: 1rem;
  margin-top: 0.25rem;
}

.dark .language-info .language-native {
  color: #a0aec0;
}

.language-info .language-code {
  color: #718096;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.dark .language-info .language-code {
  color: #718096;
}

.language-selection h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.dark .language-selection h3 {
  color: #e2e8f0;
}

.search-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.language-search {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.5rem;
  font-size: 1rem;
  background: white;
  color: #1a202c;
}

.dark .language-search {
  background: #4a5568;
  border-color: #718096;
  color: #f7fafc;
}

.dark .language-search::placeholder {
  color: #a0aec0;
}

.language-search:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.dark .language-search:focus {
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.show-all-btn {
  padding: 0.75rem 1rem;
  background: #edf2f7;
  border: 1px solid #cbd5e0;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  white-space: nowrap;
  color: #2d3748;
}

.dark .show-all-btn {
  background: #4a5568;
  border-color: #718096;
  color: #f7fafc;
}

.show-all-btn:hover {
  background: #e2e8f0;
}

.dark .show-all-btn:hover {
  background: #2d3748;
}

.show-all-btn.active {
  background: #4299e1;
  color: white;
  border-color: #4299e1;
}

.dark .show-all-btn.active {
  background: #4299e1;
  color: white;
  border-color: #4299e1;
}

.results-info {
  color: #4a5568;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.dark .results-info {
  color: #a0aec0;
}

.language-list {
  display: grid;
  gap: 0.5rem;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.5rem;
  background: #f9fafb;
}

.dark .language-list {
  border-color: #4a5568;
  background: #2d3748;
}

.language-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  width: 100%;
}

.dark .language-option {
  background: #4a5568;
  border-color: #718096;
}

.language-option:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.dark .language-option:hover {
  background: #2d3748;
  border-color: #4a5568;
}

.language-option.selected {
  background: #ebf8ff;
  border-color: #4299e1;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
}

.dark .language-option.selected {
  background: #1a365d;
  border-color: #4299e1;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
}

.language-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.language-primary .language-name {
  font-weight: 500;
  color: #1a202c;
}

.dark .language-primary .language-name {
  color: #f7fafc;
}

.language-primary .language-code {
  color: #718096;
  font-size: 0.875rem;
}

.dark .language-primary .language-code {
  color: #a0aec0;
}

.language-native {
  color: #4a5568;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.dark .language-native {
  color: #a0aec0;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #4a5568;
}

.dark .no-results {
  color: #a0aec0;
}

.clear-search-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

.clear-search-btn:hover {
  background: #3182ce;
}

.settings-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.dark .settings-info {
  border-top-color: #4a5568;
}

.settings-info h4 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.dark .settings-info h4 {
  color: #e2e8f0;
}

.settings-info ul {
  color: #4a5568;
  line-height: 1.6;
}

.dark .settings-info ul {
  color: #a0aec0;
}

.settings-info li {
  margin-bottom: 0.5rem;
}

/* Custom scrollbar for dark mode */
.dark .language-list::-webkit-scrollbar {
  width: 8px;
}

.dark .language-list::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 4px;
}

.dark .language-list::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

.dark .language-list::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('voice-settings-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'voice-settings-styles';
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}

export default VoiceSettings;