import React, { useState, useRef, KeyboardEvent, useEffect, DragEvent } from 'react';
import { useProjectStore } from '../stores/projectStore';
import { Folder, FolderX, AlertTriangle, Mic, MicOff, Megaphone, Play, Square, Info, Paperclip, X, StopCircle, Target } from 'lucide-react';
import { sessionManager } from '../../utils/sessionManager';
import { useVoiceStore } from '../stores/voiceStore';
import type { InterruptionEvent, InterruptionStatus, InterruptionType } from '../../types/interruption';

interface Attachment {
  filename: string;
  url: string;
  file?: File;
}

interface MessageInputProps {
  onSendMessage: (message: string, attachments: Attachment[]) => void;
  isLoading: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage, isLoading }) => {
  const [draggedFiles, setDraggedFiles] = useState<FileList | null>(null);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    handleUpload(e.dataTransfer.files);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleUpload(e.target.files);
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async (files: FileList | null) => {
    if (!files) return;

    const newAttachments: Attachment[] = [];
    for (const file of Array.from(files)) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          const url = result?.files?.[0]?.url || result?.url;
          if (typeof url === 'string' && url.length > 0) {
            newAttachments.push({ filename: file.name, url, file });
          } else {
            // Fallback to embedding as data URL if API didn't return expected shape
            const reader = new FileReader();
            reader.onload = (e) => {
              if (e.target?.result) {
                newAttachments.push({ filename: file.name, url: e.target.result as string, file });
              }
            };
            reader.readAsDataURL(file);
          }
        } else {
          const reader = new FileReader();
          reader.onload = (e) => {
            if (e.target?.result) {
              newAttachments.push({ filename: file.name, url: e.target.result as string, file });
            }
          };
          reader.readAsDataURL(file);
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            newAttachments.push({ filename: file.name, url: e.target.result as string, file });
          }
        };
        reader.readAsDataURL(file);
      }
    }
    setAttachments((prev) => [...prev, ...newAttachments]);
  };
  const { currentProject } = useProjectStore();
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [sizeWarning, setSizeWarning] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const voice = useVoiceStore();
  const [isPreviewing, setIsPreviewing] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [showMicTooltip, setShowMicTooltip] = useState(false);
  // Interruption UI state
  const [showInterrupt, setShowInterrupt] = useState(false);
  const [interrupting, setInterrupting] = useState(false);
  const [interruptType, setInterruptType] = useState<InterruptionType>('pause');
  const [interruptMessage, setInterruptMessage] = useState('');
  const [interruptPriority, setInterruptPriority] = useState<'low'|'medium'|'high'>('medium');
  const [interruptConstraints, setInterruptConstraints] = useState<string>('');
  const [interruptStatus, setInterruptStatus] = useState<InterruptionStatus | 'unknown'>('unknown');
  const [lastApplied, setLastApplied] = useState<any>(null);
  const [statusPoller, setStatusPoller] = useState<any>(null);

  useEffect(() => {
    try {
      const dismissed = typeof localStorage !== 'undefined' && localStorage.getItem('dante_voice_mic_tooltip_dismissed') === 'true';
      if (dismissed) return;

      const isSecure = typeof window !== 'undefined' && (window.isSecureContext || /^(localhost|127\.0\.0\.1)$/.test(window.location.hostname));
      if (!isSecure) {
        setShowMicTooltip(true);
        return;
      }

      const nav: any = typeof navigator !== 'undefined' ? navigator : undefined;
      if (nav?.permissions?.query) {
        nav.permissions.query({ name: 'microphone' as any }).then((status: any) => {
          if (status?.state === 'granted') {
            setShowMicTooltip(false);
          } else {
            setShowMicTooltip(true);
          }
        }).catch(() => {
          setShowMicTooltip(true);
        });
      } else {
        setShowMicTooltip(true);
      }
    } catch {
      // Ignore and hide tooltip by default
    }
  }, []);

  // Keyboard shortcut: Ctrl/Cmd + . to open/close interruption panel
  useEffect(() => {
    const handler = (e: any) => {
      const isCmd = (e.metaKey || e.ctrlKey) && e.key === '.';
      if (isCmd) {
        e.preventDefault?.();
        setShowInterrupt((v) => !v);
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);


  const checkMessageSize = (text: string) => {
    const charCount = text.length;

    if (charCount > 100000) { // 100KB
      setSizeWarning(`⚠️ Large message (${Math.round(charCount/1000)}KB) - may hit rate limits`);
    } else if (charCount > 500000) { // 500KB
      setSizeWarning(`🚨 Massive content (${Math.round(charCount/1000)}KB) - will be automatically reduced`);
    } else {
      setSizeWarning(null);
    }
  };

  const handleSubmit = () => {
    if ((message.trim() || attachments.length > 0) && !isLoading) {
      // Final size check before sending
      const messageSize = message.length;
      if (messageSize > 1000000) { // 1MB hard limit
        alert(`Message too large (${Math.round(messageSize/1000)}KB). Please reduce the content or break it into smaller parts.`);
        return;
      }
      onSendMessage(message.trim(), attachments);
      setMessage('');
      setAttachments([]);
      setSizeWarning(null);
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  // Interruption helpers
  const buildInterruptionEvent = async (type: InterruptionType): Promise<InterruptionEvent> => {
    const sess = await sessionManager.getCurrentSession();
    const sessionId = sess?.id || `session_${Date.now()}`;
    let constraints: any = undefined;
    try { if (interruptConstraints.trim()) constraints = JSON.parse(interruptConstraints); } catch {
      alert('Invalid JSON in constraints field. Please correct it and try again.');
      throw new Error('Invalid JSON in constraints');
    }
    return {
      event: 'agent.interruption',
      type,
      sessionId,
      stepId: undefined,
      payload: (type === 'steer' || type === 'replace-next') ? {
        message: interruptMessage || undefined,
        priority: interruptPriority,
        constraints
      } : undefined,
      timestamp: new Date().toISOString()
    };
  };

  const sendInterruption = async (type: InterruptionType) => {
    if (interrupting) return;
    setInterrupting(true);
    try {
      const evt = await buildInterruptionEvent(type);
      // Telemetry log request
      const start = Date.now();
      const resp = await fetch('/api/interruptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(evt)
      });
      const json = await resp.json().catch(() => ({}));
      const duration = Date.now() - start;
      try { await sessionManager.logApiCall({ path: '/api/interruptions', body: evt }, json, duration, !resp.ok ? json : undefined); } catch {}
      if (resp.ok) {
        setInterruptStatus(String(json.status || 'acknowledged') as InterruptionStatus);
        if (type === 'steer' || type === 'replace-next') {
          setLastApplied({ message: evt.payload?.message, priority: evt.payload?.priority, constraints: evt.payload?.constraints });
        } else {
          setLastApplied(null);
        }
      } else {
        alert('Failed to send interruption');
      }
      // Begin polling status for real-time updates
      const sess = await sessionManager.getCurrentSession();
      const sid = sess?.id;
      if (sid) {
        if (statusPoller) clearInterval(statusPoller);
        const poll = setInterval(async () => {
          try {
            const r = await fetch(`/api/interruptions/status?sessionId=${encodeURIComponent(sid)}`);
            const j = await r.json().catch(() => ({}));
            if (j?.success) {
              setInterruptStatus(j.status || 'unknown');
              setLastApplied(j.lastAppliedPayload || lastApplied);
              // Stop polling once terminal states
              if (['paused', 'cancelled', 'applied', 'resumed'].includes(j.status)) {
                clearInterval(poll);
                setStatusPoller(null);
              }
            }
          } catch {}
        }, 500);
        setStatusPoller(poll);
      }
    } catch (e) {
      console.warn('Interruption failed:', e);
      alert('Interruption failed');
    } finally {
      setInterrupting(false);
    }
  };

  const resumeAfterPause = async () => {
    try {
      const sess = await sessionManager.getCurrentSession();
      const sid = sess?.id;
      if (!sid) return;
      const r = await fetch('/api/interruptions/resume', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ sessionId: sid }) });
      const j = await r.json().catch(() => ({}));
      await sessionManager.logApiCall({ path: '/api/interruptions/resume', body: { sessionId: sid } }, j, 0);
      setInterruptStatus('resumed');
    } catch (e) {
      console.warn('Resume failed:', e);
    }
  };

  const undoSteer = async () => {
    try {
      const sess = await sessionManager.getCurrentSession();
      const sid = sess?.id;
      if (!sid) return;
      const r = await fetch('/api/interruptions/clear', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ sessionId: sid }) });
      const j = await r.json().catch(() => ({}));
      await sessionManager.logApiCall({ path: '/api/interruptions/clear', body: { sessionId: sid } }, j, 0);
      setInterruptStatus('acknowledged');
      setLastApplied(null);
      setInterruptMessage('');
      setInterruptConstraints('');
    } catch (e) {
      console.warn('Undo failed:', e);
    }
  };

  const handleSendLastToVoice = async () => {
    if (isLoading) return;
    try {
      const session = await sessionManager.getCurrentSession();
      const msgs = session?.messages || [];
      let lastUser = '';
      for (let i = msgs.length - 1; i >= 0; i--) {
        if (msgs[i]?.role === 'user' && typeof msgs[i]?.content === 'string' && msgs[i].content.trim()) {
          lastUser = msgs[i].content.trim();
          break;
        }
      }
      if (!lastUser) return;
      if (!voice.enabled) {
        await voice.start();
      }
      await voice.sendText(lastUser, { mode: 'ask' });
    } catch (e) {
      console.warn('Failed to send last typed message to voice:', e);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handlePreviewVoice = async () => {
    if (audioRef.current) {
      try { audioRef.current.pause(); } catch {}
      audioRef.current = null;
      setIsPreviewing(false);
      return;
    }
    try {
      setIsPreviewing(true);
      const previewText = `Hi, I'm the ${voice.selectedVoice} voice. Let's get to work.`;
      const resp = await fetch(`/api/voice/preview?voice=${encodeURIComponent(voice.selectedVoice)}&text=${encodeURIComponent(previewText)}`);
      if (!resp.ok) throw new Error('Preview failed');
      const blob = await resp.blob();
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);
      audioRef.current = audio;
      audio.onended = () => {
        URL.revokeObjectURL(url);
        audioRef.current = null;
        setIsPreviewing(false);
      };
      await audio.play();
    } catch (e) {
      setIsPreviewing(false);
      console.warn('Failed to play voice preview:', e);
      alert('Failed to play voice preview. Please try again.');
    }
  };

  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setMessage(newValue);
    checkMessageSize(newValue);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 200)}px`;
    }
  };

  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  return (
    <div
      className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-4 relative"
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-dashed border-blue-600 rounded-lg flex items-center justify-center">
          <span className="text-blue-600 font-bold">Drop files to attach</span>
        </div>
      )}
      <div className="max-w-4xl mx-auto">
        {attachments.length > 0 && (
          <div className="mb-2 flex flex-wrap gap-2">
            {attachments.map((att, index) => (
              <div key={index} className="bg-gray-200 dark:bg-gray-700 rounded-full px-3 py-1 text-sm flex items-center gap-2">
                <span>{att.filename}</span>
                <button onClick={() => handleRemoveAttachment(index)} className="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200">
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>
        )}
        <div className="flex items-end space-x-2">
          {/* Stop/Steer button */}
          <button
            onClick={() => setShowInterrupt(true)}
            className={`p-2 rounded-lg transition-colors ${isLoading ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'}`}
            aria-label="Stop or steer the agent"
            title="Stop/Steer (Ctrl/Cmd+.)"
          >
            <StopCircle className="w-5 h-5" />
          </button>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="p-2 rounded-lg transition-colors bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"
            title="Attach files"
          >
            <Paperclip className="w-5 h-5" />
          </button>
          <input
            type="file"
            multiple
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleInput}
              onKeyDown={handleKeyDown}
              placeholder="Ask Dante anything..."
              disabled={isLoading}
              className="w-full px-4 py-3 pr-12 bg-gray-50 dark:bg-gray-900 border border-gray-300 dark:border-gray-600
                       rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none
                       disabled:opacity-50 disabled:cursor-not-allowed"
              rows={1}
              style={{ maxHeight: '200px' }}
            />
            {/* Voice toggle button */}
            <button
              onClick={() => (voice.enabled ? voice.stop() : voice.start())}
              className={`absolute right-12 bottom-2 p-2 rounded-lg transition-colors ${
                voice.enabled
                  ? 'bg-indigo-500 text-white hover:bg-indigo-600'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
              title={voice.enabled ? 'End Dante Speaks' : 'Start Dante Speaks'}
            >
              {voice.enabled ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
            </button>

            {/* One-time mic permission tooltip */}
            {showMicTooltip && !voice.enabled && (
              <div className="absolute right-2 bottom-[60px] z-20 max-w-xs p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
                <div className="flex items-start gap-2">
                  <Info className="w-4 h-4 text-indigo-500 mt-0.5" />
                  <div className="text-xs text-gray-700 dark:text-gray-300">
                    <div>Your browser will ask for microphone access.</div>
                    <div className="mt-1">Microphone requires https or localhost.</div>
                    <div className="mt-2 flex justify-end gap-2">
                      <button
                        className="px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                        onClick={() => setShowMicTooltip(false)}
                      >
                        Dismiss
                      </button>
                      <button
                        className="px-2 py-1 rounded bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-200 dark:hover:bg-indigo-800"
                        onClick={() => { try { localStorage.setItem('dante_voice_mic_tooltip_dismissed', 'true'); } catch {}; setShowMicTooltip(false); }}
                      >
                        Don’t show again
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Send last typed message to voice */}
            <button
              onClick={handleSendLastToVoice}
              className={`absolute right-24 bottom-2 p-2 rounded-lg transition-colors bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600`}
              title="Speak last typed message"
            >
              <Megaphone className="w-5 h-5" />
            </button>

            <button
              onClick={handleSubmit}
              disabled={(!message.trim() && attachments.length === 0) || isLoading}
              className="absolute right-2 bottom-2 p-2 bg-blue-500 text-white rounded-lg
                       hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                  d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>

        {/* Size Warning */}
        {sizeWarning && (
          <div className="mt-2 flex items-center space-x-2 px-3 py-1.5 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg border border-amber-200 dark:border-amber-800">
            <AlertTriangle className="w-3 h-3" />
            <span className="text-xs font-medium">{sizeWarning}</span>
          </div>
        )}

        {/* Project Context Indicator */}
        {currentProject && (
          <div className="mt-2 flex items-center justify-between">
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 rounded-lg border border-orange-200 dark:border-orange-800">
              <Folder className="w-3 h-3" />
              <span className="text-xs font-medium">
                Working in: {currentProject.name}
              </span>
              <span className="text-xs text-orange-600 dark:text-orange-400">
                ({currentProject.type})
              </span>
            </div>
          </div>
        )}

        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between gap-2">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="opacity-75">Voice:</span>
              <select
                value={voice.selectedVoice}
                onChange={(e) => voice.setSelectedVoice(e.target.value)}
                className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs"
                disabled={voice.enabled}
                title={voice.enabled ? 'Stop Dante Speaks to change voice' : 'Select speaking voice'}
              >
                <option value="verse">Verse</option>
                <option value="alloy">Alloy</option>
                <option value="ash">Ash</option>
                <option value="ballad">Ballad</option>
                <option value="coral">Coral</option>
                <option value="echo">Echo</option>
                <option value="sage">Sage</option>
                <option value="shimmer">Shimmer</option>
              </select>
              <button
                onClick={handlePreviewVoice}
                disabled={voice.enabled}
                className={`px-2 py-1 text-xs rounded ${isPreviewing ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'}`}
                title={isPreviewing ? 'Stop preview' : 'Preview this voice'}
              >
                {isPreviewing ? <Square size={12} /> : <Play size={12} />}
              </button>
            </div>
            {!currentProject && (
              <div className="flex items-center space-x-1 text-amber-600 dark:text-amber-400">
                <FolderX className="w-3 h-3" />
                <span>No project selected</span>
              </div>
            )}
          </div>
        </div>
      </div>
    {showInterrupt && (
      <div role="dialog" aria-modal="true" aria-label="Interruption Controls" className="fixed inset-0 z-40 flex items-end sm:items-center justify-center">
        <div className="absolute inset-0 bg-black bg-opacity-40" onClick={() => setShowInterrupt(false)} aria-hidden="true" />
        <div className="relative bg-white dark:bg-gray-800 rounded-t-lg sm:rounded-lg shadow-xl w-full sm:max-w-lg mx-auto p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-indigo-500" />
              <h3 className="font-medium">Interrupt Orchestration</h3>
            </div>
            <button onClick={() => setShowInterrupt(false)} aria-label="Close" className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700">
              <X size={16} />
            </button>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2" role="radiogroup" aria-label="Interruption type">
              {(['cancel','pause','steer','replace-next'] as InterruptionType[]).map((t) => (
                <label key={t} className={`px-2 py-1 rounded border cursor-pointer text-xs ${interruptType===t? 'bg-indigo-100 dark:bg-indigo-900 border-indigo-300 dark:border-indigo-700 text-indigo-800 dark:text-indigo-200':'border-gray-300 dark:border-gray-600'}`}>
                  <input aria-label={t} type="radio" name="interruptType" className="sr-only" checked={interruptType===t} onChange={() => setInterruptType(t)} />
                  {t}
                </label>
              ))}
            </div>
            {(interruptType === 'steer' || interruptType === 'replace-next') && (
              <div className="space-y-2">
                <div>
                  <label className="block text-xs mb-1">Message</label>
                  <input aria-label="Interruption message" value={interruptMessage} onChange={(e)=>setInterruptMessage(e.target.value)} className="w-full px-2 py-1 border rounded bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600" placeholder={interruptType==='steer' ? 'New context/constraints' : 'Override next action'} />
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-xs">Priority</label>
                  <select aria-label="Interruption priority" value={interruptPriority} onChange={(e)=>setInterruptPriority(e.target.value as any)} className="px-2 py-1 border rounded bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-xs">
                    <option value="low">low</option>
                    <option value="medium">medium</option>
                    <option value="high">high</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs mb-1">Constraints (JSON)</label>
                  <textarea aria-label="Interruption constraints" value={interruptConstraints} onChange={(e)=>setInterruptConstraints(e.target.value)} rows={3} className="w-full px-2 py-1 border rounded bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-xs" placeholder='{"timebox":"2 min","style":"succinct"}' />
                </div>
              </div>
            )}
            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-600 dark:text-gray-300">Status: <span className="font-medium capitalize">{interruptStatus}</span></div>
              <div className="flex items-center gap-2">
                {(interruptType==='cancel' || interruptType==='pause') && (
                  <button disabled={interrupting} onClick={() => sendInterruption(interruptType)} className={`px-3 py-1 text-xs rounded ${interruptType==='cancel' ? 'bg-red-600 text-white hover:bg-red-700':'bg-amber-500 text-white hover:bg-amber-600'}`}>{interruptType==='cancel'?'Cancel now':'Pause'}</button>
                )}
                {(interruptType==='steer' || interruptType==='replace-next') && (
                  <>
                    <button disabled={interrupting} onClick={() => sendInterruption(interruptType)} className="px-3 py-1 text-xs rounded bg-indigo-600 text-white hover:bg-indigo-700">Apply</button>
                    {lastApplied && <button onClick={undoSteer} className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700">Undo</button>}
                  </>
                )}
                {(interruptStatus==='paused') && (
                  <button onClick={resumeAfterPause} className="px-3 py-1 text-xs rounded bg-green-600 text-white hover:bg-green-700">Resume</button>
                )}
              </div>
            </div>
            {lastApplied && (
              <div className="text-xs bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded p-2">
                <div className="font-medium mb-1">Last update applied</div>
                <pre className="whitespace-pre-wrap break-words">{JSON.stringify(lastApplied, null, 2)}</pre>
              </div>
            )}
          </div>
        </div>
      </div>
    )}
    </div>
  );
};

export default MessageInput;
