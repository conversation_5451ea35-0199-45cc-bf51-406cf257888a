import React, { useState } from 'react';
import { Clock, Info, X, Brain, Server, FolderOpen, Mic } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onClearChat: () => void;
  onOpenHistory?: () => void;
  onOpenMemory?: () => void;
  onOpenMCP?: () => void;
  onOpenProject?: () => void;
  onOpenVoiceSettings?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, onClearChat, onOpenHistory, onOpenMemory, onOpenMCP, onOpenProject, onOpenVoiceSettings }) => {
  const [selectedCapability, setSelectedCapability] = useState<number | null>(null);
  
  const capabilities = [
    { 
      icon: '🔍', 
      title: 'Research', 
      description: 'Web search & documentation',
      details: 'I can search the web, read documentation, and find information to help answer your questions.',
      example: 'Try: "Search for the latest React best practices"'
    },
    { 
      icon: '💻', 
      title: 'Code Generation', 
      description: 'Create apps & websites',
      details: 'I can write complete applications, components, and websites in any programming language.',
      example: 'Try: "Create a todo app with React and TypeScript"'
    },
    { 
      icon: '⚡', 
      title: 'Refactoring', 
      description: 'Optimize performance',
      details: 'I can refactor code to improve performance, readability, and maintainability.',
      example: 'Try: "Refactor this function to be more efficient"'
    },
    { 
      icon: '🐛', 
      title: 'Debugging', 
      description: 'Fix bugs & errors',
      details: 'I can help identify and fix bugs in your code, trace errors, and suggest solutions.',
      example: 'Try: "Help me debug this error message"'
    },
    { 
      icon: '🔒', 
      title: 'Security', 
      description: 'Vulnerability analysis',
      details: 'I can analyze code for security vulnerabilities and suggest best practices.',
      example: 'Try: "Check this code for security issues"'
    },
    { 
      icon: '✅', 
      title: 'Code Review', 
      description: 'Quality assessment',
      details: 'I can review code for quality, suggest improvements, and ensure best practices.',
      example: 'Try: "Review this component for improvements"'
    },
    { 
      icon: '☀️', 
      title: 'Weather', 
      description: 'Forecasts & conditions',
      details: 'I can provide weather forecasts and current conditions for any location.',
      example: 'Try: "What\'s the weather in San Francisco?"'
    },
    { 
      icon: '🔌', 
      title: 'MCP Integration', 
      description: 'External tool servers',
      details: 'I can connect to Model Context Protocol servers to access additional tools and capabilities.',
      example: 'Try: "List files using MCP filesystem tools"'
    },
  ];

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-72 
        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                Capabilities
              </h2>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                aria-label="Close sidebar"
              >
                <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {capabilities.map((cap, index) => (
                <div key={index}>
                  <button
                    type="button"
                    onClick={() => setSelectedCapability(selectedCapability === index ? null : index)}
                    className="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all cursor-pointer
                             border border-transparent hover:border-gray-200 dark:hover:border-gray-600"
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl flex-shrink-0">{cap.icon}</span>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {cap.title}
                          </h3>
                          <Info className={`w-4 h-4 text-gray-400 transition-transform ${
                            selectedCapability === index ? 'rotate-180' : ''
                          }`} />
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {cap.description}
                        </p>
                      </div>
                    </div>
                  </button>
                  
                  {/* Expanded Details */}
                  {selectedCapability === index && (
                    <div className="mt-2 ml-11 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        {cap.details}
                      </p>
                      <div className="p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                        <p className="text-xs font-mono text-gray-600 dark:text-gray-400">
                          {cap.example}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
            {onOpenProject && (
              <button
                onClick={onOpenProject}
                className="w-full px-4 py-2 bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 
                         rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors flex items-center justify-center gap-2"
              >
                <FolderOpen className="w-4 h-4" />
                Project Explorer
              </button>
            )}
            {onOpenHistory && (
              <button
                onClick={onOpenHistory}
                className="w-full px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 
                         rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors flex items-center justify-center gap-2"
              >
                <Clock className="w-4 h-4" />
                Conversation History
              </button>
            )}
            {onOpenMemory && (
              <button
                onClick={onOpenMemory}
                className="w-full px-4 py-2 bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 
                         rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors flex items-center justify-center gap-2"
              >
                <Brain className="w-4 h-4" />
                Memory Management
              </button>
            )}
            {onOpenMCP && (
              <button
                onClick={onOpenMCP}
                className="w-full px-4 py-2 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 
                         rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors flex items-center justify-center gap-2"
              >
                <Server className="w-4 h-4" />
                MCP Servers
              </button>
            )}
            {onOpenVoiceSettings && (
              <button
                onClick={onOpenVoiceSettings}
                className="w-full px-4 py-2 bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 
                         rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors flex items-center justify-center gap-2"
              >
                <Mic className="w-4 h-4" />
                Voice Settings
              </button>
            )}
            <button
              onClick={onClearChat}
              className="w-full px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 
                       rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
            >
              Clear Current Chat
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;