/**
 * GoogleWorkspacePanel Component
 * Real-time display of Google Workspace connector activity
 * Shows searches, operations, and results preview
 */

import React, { useState, useEffect, useRef } from 'react';
import { useConnectorStore, ConnectorActivity, SearchOperation } from '../../stores/connectorStore';
import { ConnectorId } from '../../../types/connectors';
import {
  Search,
  FileText,
  Mail,
  Calendar,
  FolderOpen,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  Filter,
  X,
  ChevronDown,
  ChevronRight,
  Activity,
  History,
  Shield,
  Zap
} from 'lucide-react';

interface GoogleWorkspacePanelProps {
  className?: string;
  maxHeight?: string;
  showHistory?: boolean;
  autoScroll?: boolean;
}

const GoogleWorkspacePanel: React.FC<GoogleWorkspacePanelProps> = ({
  className = '',
  maxHeight = '400px',
  showHistory = true,
  autoScroll = true
}) => {
  const [selectedConnector, setSelectedConnector] = useState<ConnectorId | 'all'>('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [showPreview, setShowPreview] = useState(true);
  const activityEndRef = useRef<HTMLDivElement>(null);
  
  const {
    activeSearches,
    recentActivity,
    searchHistory,
    pendingApprovals,
    handleApproval,
    setPreview,
    clearActivity
  } = useConnectorStore();
  
  // Filter activities based on selected connector
  const filteredActivities = selectedConnector === 'all'
    ? recentActivity
    : recentActivity.filter(a => a.connectorId === selectedConnector);
  
  const filteredSearches = selectedConnector === 'all'
    ? activeSearches
    : activeSearches.filter(s => s.connectorId === selectedConnector);
  
  const filteredApprovals = selectedConnector === 'all'
    ? pendingApprovals.filter(a => a.status === 'pending')
    : pendingApprovals.filter(a => a.status === 'pending' && a.connectorId === selectedConnector);
  
  // Auto-scroll to bottom when new activity
  useEffect(() => {
    if (autoScroll && activityEndRef.current) {
      activityEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [recentActivity, autoScroll]);
  
  // Get connector icon
  const getConnectorIcon = (connectorId: ConnectorId) => {
    switch (connectorId) {
      case 'connector_gmail':
        return Mail;
      case 'connector_googlecalendar':
        return Calendar;
      case 'connector_googledrive':
        return FolderOpen;
      default:
        return FileText;
    }
  };
  
  // Get connector name
  const getConnectorName = (connectorId: ConnectorId) => {
    switch (connectorId) {
      case 'connector_gmail':
        return 'Gmail';
      case 'connector_googlecalendar':
        return 'Calendar';
      case 'connector_googledrive':
        return 'Drive';
      default:
        return connectorId;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'complete':
        return CheckCircle;
      case 'error':
        return XCircle;
      case 'pending':
      case 'searching':
      case 'processing':
        return Loader2;
      default:
        return AlertCircle;
    }
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
      case 'complete':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'pending':
      case 'searching':
      case 'processing':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };
  
  // Toggle expanded state
  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };
  
  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };
  
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Activity size={18} />
            Workspace Activity
          </h3>
          
          <div className="flex items-center gap-2">
            {/* Connector Filter */}
            <select
              value={selectedConnector}
              onChange={(e) => setSelectedConnector(e.target.value as ConnectorId | 'all')}
              className="text-xs px-2 py-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            >
              <option value="all">All Services</option>
              <option value="connector_gmail">Gmail</option>
              <option value="connector_googlecalendar">Calendar</option>
              <option value="connector_googledrive">Drive</option>
            </select>
            
            {/* Preview Toggle */}
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"
              title={showPreview ? 'Hide previews' : 'Show previews'}
            >
              {showPreview ? <Eye size={16} /> : <EyeOff size={16} />}
            </button>
            
            {/* Clear Activity */}
            <button
              onClick={() => clearActivity(selectedConnector === 'all' ? undefined : selectedConnector)}
              className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"
              title="Clear activity"
            >
              <X size={16} />
            </button>
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-4 mt-2 text-xs text-gray-600 dark:text-gray-400">
          <span className="flex items-center gap-1">
            <Zap size={12} />
            {filteredSearches.length} active
          </span>
          <span className="flex items-center gap-1">
            <Shield size={12} />
            {filteredApprovals.length} pending
          </span>
          <span className="flex items-center gap-1">
            <History size={12} />
            {filteredActivities.length} events
          </span>
        </div>
      </div>
      
      {/* Content */}
      <div className="overflow-y-auto" style={{ maxHeight }}>
        {/* Pending Approvals */}
        {filteredApprovals.length > 0 && (
          <div className="px-4 py-3 bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
            <h4 className="font-medium text-sm text-yellow-800 dark:text-yellow-200 mb-2">
              Pending Approvals
            </h4>
            <div className="space-y-2">
              {filteredApprovals.map((approval) => (
                <div
                  key={approval.id}
                  className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-yellow-300 dark:border-yellow-700"
                >
                  <div className="flex items-center gap-2">
                    <Shield size={16} className="text-yellow-600 dark:text-yellow-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {approval.operation}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {getConnectorName(approval.connectorId)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleApproval(approval.id, true)}
                      className="px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => handleApproval(approval.id, false)}
                      className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
                    >
                      Deny
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Active Searches */}
        {filteredSearches.length > 0 && (
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">
              Active Searches
            </h4>
            <div className="space-y-2">
              {filteredSearches.map((search) => {
                const Icon = getConnectorIcon(search.connectorId);
                const StatusIcon = getStatusIcon(search.status);
                const isExpanded = expandedItems.has(search.id);
                
                return (
                  <div
                    key={search.id}
                    className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-2"
                  >
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => toggleExpanded(search.id)}
                        className="flex items-center gap-2 flex-1 text-left"
                      >
                        <Icon size={16} className="text-gray-600 dark:text-gray-400" />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {search.query}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">
                            {getConnectorName(search.connectorId)} • {formatTimeAgo(search.startTime)}
                          </div>
                        </div>
                        {search.itemsFound !== undefined && (
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            {search.itemsFound} items
                          </span>
                        )}
                        {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                      </button>
                      
                      <StatusIcon
                        size={16}
                        className={`${getStatusColor(search.status)} ${
                          search.status === 'searching' || search.status === 'processing' 
                            ? 'animate-spin' 
                            : ''
                        } ml-2`}
                      />
                    </div>
                    
                    {isExpanded && search.preview && showPreview && (
                      <div className="mt-2 pl-6 space-y-1">
                        {search.preview.slice(0, 3).map((item, idx) => (
                          <div
                            key={idx}
                            className="text-xs p-1.5 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600"
                          >
                            <div className="font-medium text-gray-700 dark:text-gray-300">
                              {item.subject || item.title || item.name || 'Untitled'}
                            </div>
                            {item.snippet && (
                              <div className="text-gray-500 dark:text-gray-400 truncate">
                                {item.snippet}
                              </div>
                            )}
                          </div>
                        ))}
                        {search.preview.length > 3 && (
                          <button
                            onClick={() => setPreview({
                              id: `preview_${search.id}`,
                              connectorId: search.connectorId,
                              query: search.query,
                              timestamp: search.startTime,
                              results: search.preview || [],
                              totalCount: search.itemsFound || 0
                            })}
                            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                          >
                            View all {search.itemsFound} results
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        {/* Recent Activity */}
        <div className="px-4 py-3">
          <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">
            Recent Activity
          </h4>
          
          {filteredActivities.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
              No activity yet
            </div>
          ) : (
            <div className="space-y-1">
              {filteredActivities.slice(0, 20).map((activity) => {
                const Icon = getConnectorIcon(activity.connectorId);
                const StatusIcon = getStatusIcon(activity.status);
                
                return (
                  <div
                    key={activity.id}
                    className="flex items-start gap-2 py-1.5 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded px-1"
                  >
                    <Icon size={14} className="text-gray-400 dark:text-gray-500 mt-0.5" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-gray-900 dark:text-gray-100">
                        {activity.description}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatTimeAgo(activity.timestamp)}
                      </div>
                    </div>
                    <StatusIcon size={12} className={getStatusColor(activity.status)} />
                  </div>
                );
              })}
            </div>
          )}
          
          <div ref={activityEndRef} />
        </div>
      </div>
    </div>
  );
};

export default GoogleWorkspacePanel;