/**
 * ConnectorStatusBar Component
 * Displays connection status for all Google Workspace services
 * Shows real-time connection state, token expiry warnings, and quick actions
 */

import React, { useState, useEffect } from 'react';
import { useConnectorStatus } from '../../hooks/useConnectorStatus';
import { ConnectorId } from '../../../types/connectors';
import { 
  Mail, 
  Calendar, 
  FolderOpen, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw, 
  LogOut,
  Clock,
  Loader2,
  Link2,
  Link2Off,
  ChevronDown,
  ChevronUp,
  Activity,
  Shield,
  Settings
} from 'lucide-react';

interface ConnectorStatusBarProps {
  className?: string;
  compact?: boolean;
  showDetails?: boolean;
  onSettingsClick?: () => void;
}

const ConnectorStatusBar: React.FC<ConnectorStatusBarProps> = ({
  className = '',
  compact = false,
  showDetails = true,
  onSettingsClick
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showingTooltip, setShowingTooltip] = useState<ConnectorId | null>(null);
  
  const {
    allConnectors,
    isConnected,
    authenticate,
    disconnect,
    refreshToken,
    isAuthenticating,
    hasActiveSearches,
    activeSearchCount,
    hasPendingApprovals,
    pendingApprovalCount,
    checkTokenExpiry
  } = useConnectorStatus({
    onTokenExpiring: (connectorId, expiresIn) => {
      console.log(`Token for ${connectorId} expiring in ${Math.round(expiresIn / 1000 / 60)} minutes`);
    }
  });
  
  // Get icon for connector
  const getConnectorIcon = (connectorId: ConnectorId) => {
    switch (connectorId) {
      case 'connector_gmail':
        return Mail;
      case 'connector_googlecalendar':
        return Calendar;
      case 'connector_googledrive':
        return FolderOpen;
      default:
        return Link2;
    }
  };
  
  // Get status color
  const getStatusColor = (status: string, isExpiring: boolean) => {
    if (isExpiring) return 'text-yellow-500 dark:text-yellow-400';
    
    switch (status) {
      case 'connected':
        return 'text-green-500 dark:text-green-400';
      case 'disconnected':
        return 'text-gray-400 dark:text-gray-500';
      case 'refreshing':
        return 'text-blue-500 dark:text-blue-400';
      case 'error':
        return 'text-red-500 dark:text-red-400';
      case 'expiring':
        return 'text-yellow-500 dark:text-yellow-400';
      default:
        return 'text-gray-400 dark:text-gray-500';
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string, isExpiring: boolean) => {
    if (isExpiring) return Clock;
    
    switch (status) {
      case 'connected':
        return CheckCircle;
      case 'disconnected':
        return Link2Off;
      case 'refreshing':
        return RefreshCw;
      case 'error':
        return AlertCircle;
      case 'expiring':
        return Clock;
      default:
        return Link2Off;
    }
  };
  
  // Get status text
  const getStatusText = (status: string, isExpiring: boolean) => {
    if (isExpiring) return 'Token Expiring';
    
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Not Connected';
      case 'refreshing':
        return 'Refreshing';
      case 'error':
        return 'Error';
      case 'expiring':
        return 'Expiring Soon';
      default:
        return 'Unknown';
    }
  };
  
  // Handle connector action
  const handleConnectorAction = async (connectorId: ConnectorId, action: 'connect' | 'disconnect' | 'refresh') => {
    try {
      switch (action) {
        case 'connect':
          await authenticate(connectorId);
          break;
        case 'disconnect':
          await disconnect(connectorId);
          break;
        case 'refresh':
          await refreshToken(connectorId);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} ${connectorId}:`, error);
    }
  };
  
  // Calculate overall connection status
  const connectedCount = allConnectors.filter(c => c.status === 'connected').length;
  const hasErrors = allConnectors.some(c => c.status === 'error');
  const hasExpiring = allConnectors.some(c => c.isExpiring);
  
  if (compact) {
    return (
      <div className={`flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center gap-1">
          {allConnectors.map((connector) => {
            const Icon = getConnectorIcon(connector.id);
            const statusColor = getStatusColor(connector.status, connector.isExpiring);
            
            return (
              <button
                key={connector.id}
                onClick={() => connector.status === 'connected' 
                  ? handleConnectorAction(connector.id, 'disconnect')
                  : handleConnectorAction(connector.id, 'connect')
                }
                className={`p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${statusColor}`}
                title={`${connector.name}: ${getStatusText(connector.status, connector.isExpiring)}`}
              >
                <Icon size={16} />
              </button>
            );
          })}
        </div>
        
        {(hasActiveSearches || hasPendingApprovals) && (
          <div className="flex items-center gap-2 ml-auto text-xs">
            {hasActiveSearches && (
              <span className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                <Activity size={12} />
                {activeSearchCount}
              </span>
            )}
            {hasPendingApprovals && (
              <span className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
                <Shield size={12} />
                {pendingApprovalCount}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className={`bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Summary Bar */}
      <div className="px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
          >
            <span>Google Workspace</span>
            {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </button>
          
          <div className="flex items-center gap-2 text-xs">
            {connectedCount > 0 ? (
              <span className="flex items-center gap-1 text-green-600 dark:text-green-400">
                <CheckCircle size={12} />
                {connectedCount}/{allConnectors.length} connected
              </span>
            ) : (
              <span className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                <Link2Off size={12} />
                Not connected
              </span>
            )}
            
            {hasExpiring && (
              <span className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
                <Clock size={12} />
                Token expiring
              </span>
            )}
            
            {hasErrors && (
              <span className="flex items-center gap-1 text-red-600 dark:text-red-400">
                <AlertCircle size={12} />
                Connection error
              </span>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {hasActiveSearches && (
            <span className="flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs">
              <Activity size={12} className="animate-pulse" />
              {activeSearchCount} active
            </span>
          )}
          
          {hasPendingApprovals && (
            <span className="flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded text-xs">
              <Shield size={12} />
              {pendingApprovalCount} pending
            </span>
          )}
          
          {onSettingsClick && (
            <button
              onClick={onSettingsClick}
              className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"
              title="Connector Settings"
            >
              <Settings size={16} />
            </button>
          )}
        </div>
      </div>
      
      {/* Expanded Details */}
      {isExpanded && showDetails && (
        <div className="px-4 pb-3 space-y-2 border-t border-gray-100 dark:border-gray-700/50 pt-3">
          {allConnectors.map((connector) => {
            const Icon = getConnectorIcon(connector.id);
            const StatusIcon = getStatusIcon(connector.status, connector.isExpiring);
            const statusColor = getStatusColor(connector.status, connector.isExpiring);
            const expiry = checkTokenExpiry(connector.id);
            
            return (
              <div
                key={connector.id}
                className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Icon size={20} className="text-gray-600 dark:text-gray-400" />
                  <div>
                    <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                      {connector.name}
                    </div>
                    <div className={`flex items-center gap-1 text-xs ${statusColor}`}>
                      <StatusIcon size={12} />
                      <span>{getStatusText(connector.status, connector.isExpiring)}</span>
                      {connector.isExpiring && expiry.expiresIn && (
                        <span className="text-gray-500 dark:text-gray-400">
                          ({Math.round(expiry.expiresIn / 1000 / 60)}m)
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {connector.status === 'connected' ? (
                    <>
                      {connector.isExpiring && (
                        <button
                          onClick={() => handleConnectorAction(connector.id, 'refresh')}
                          className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 text-blue-600 dark:text-blue-400"
                          title="Refresh Token"
                        >
                          <RefreshCw size={14} />
                        </button>
                      )}
                      <button
                        onClick={() => handleConnectorAction(connector.id, 'disconnect')}
                        className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-400"
                        title="Disconnect"
                      >
                        <LogOut size={14} />
                      </button>
                    </>
                  ) : connector.status === 'disconnected' ? (
                    <button
                      onClick={() => handleConnectorAction(connector.id, 'connect')}
                      disabled={isAuthenticating}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md text-xs font-medium flex items-center gap-1"
                    >
                      {isAuthenticating ? (
                        <>
                          <Loader2 size={12} className="animate-spin" />
                          Connecting...
                        </>
                      ) : (
                        <>
                          <Link2 size={12} />
                          Connect
                        </>
                      )}
                    </button>
                  ) : connector.status === 'error' ? (
                    <button
                      onClick={() => handleConnectorAction(connector.id, 'connect')}
                      className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md text-xs font-medium flex items-center gap-1"
                    >
                      <RefreshCw size={12} />
                      Retry
                    </button>
                  ) : connector.status === 'refreshing' ? (
                    <span className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md text-xs flex items-center gap-1">
                      <Loader2 size={12} className="animate-spin" />
                      Refreshing...
                    </span>
                  ) : null}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ConnectorStatusBar;