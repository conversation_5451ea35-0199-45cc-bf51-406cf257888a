/**
 * SearchPreviewModal Component
 * Shows detailed preview of search results before <PERSON> processes them
 * Allows users to verify and refine searches
 */

import React, { useState, useEffect } from 'react';
import { useConnectorStore, SearchResult } from '../../stores/connectorStore';
import { ConnectorId } from '../../../types/connectors';
import {
  X,
  Search,
  Mail,
  Calendar,
  FolderOpen,
  FileText,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  ChevronDown,
  ChevronRight,
  Eye,
  Download,
  ExternalLink,
  Clock,
  User,
  Tag,
  Paperclip,
  AlertCircle,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';

interface SearchPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  searchResult?: SearchResult;
  onConfirm?: (selectedItems?: any[]) => void;
  onRefine?: (newQuery: string) => void;
  onReject?: () => void;
}

const SearchPreviewModal: React.FC<SearchPreviewModalProps> = ({
  isOpen,
  onClose,
  searchResult,
  onConfirm,
  onRefine,
  onReject
}) => {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<'all' | 'selected'>('all');
  const [refinedQuery, setRefinedQuery] = useState('');
  const [showRawData, setShowRawData] = useState(false);
  
  const { currentPreview } = useConnectorStore();
  const preview = searchResult || currentPreview;
  
  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedItems(new Set());
      setExpandedItems(new Set());
      setFilterType('all');
      setRefinedQuery('');
      setShowRawData(false);
    }
  }, [isOpen]);
  
  if (!isOpen || !preview) return null;
  
  // Get connector icon
  const getConnectorIcon = () => {
    switch (preview.connectorId) {
      case 'connector_gmail':
        return Mail;
      case 'connector_googlecalendar':
        return Calendar;
      case 'connector_googledrive':
        return FolderOpen;
      default:
        return FileText;
    }
  };
  
  // Get connector name
  const getConnectorName = () => {
    switch (preview.connectorId) {
      case 'connector_gmail':
        return 'Gmail';
      case 'connector_googlecalendar':
        return 'Google Calendar';
      case 'connector_googledrive':
        return 'Google Drive';
      default:
        return 'Unknown';
    }
  };
  
  const ConnectorIcon = getConnectorIcon();
  
  // Toggle item selection
  const toggleSelection = (itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };
  
  // Toggle item expansion
  const toggleExpansion = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };
  
  // Select all items
  const selectAll = () => {
    const allIds = preview.results.map((item: any, index: number) => item.id || index.toString());
    setSelectedItems(new Set(allIds));
  };
  
  // Deselect all items
  const deselectAll = () => {
    setSelectedItems(new Set());
  };
  
  // Handle confirmation
  const handleConfirm = () => {
    if (selectedItems.size > 0) {
      const selected = preview.results.filter((item: any, index: number) => 
        selectedItems.has(item.id || index.toString())
      );
      onConfirm?.(selected);
    } else {
      onConfirm?.(preview.results);
    }
    onClose();
  };
  
  // Handle search refinement
  const handleRefine = () => {
    if (refinedQuery.trim()) {
      onRefine?.(refinedQuery);
      onClose();
    }
  };
  
  // Handle rejection
  const handleReject = () => {
    onReject?.();
    onClose();
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return dateString;
    }
  };
  
  // Filter results
  const filteredResults = filterType === 'selected' 
    ? preview.results.filter((item: any, index: number) => selectedItems.has(item.id || index.toString()))
    : preview.results;
  
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75"
          onClick={onClose}
        />
        
        {/* Modal */}
        <div className="inline-block w-full max-w-4xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
          {/* Header */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ConnectorIcon size={24} className="text-gray-600 dark:text-gray-400" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Search Results Preview
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {getConnectorName()} • {preview.totalCount} results for "{preview.query}"
                  </p>
                </div>
              </div>
              
              <button
                onClick={onClose}
                className="p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
              >
                <X size={20} />
              </button>
            </div>
            
            {/* Action Bar */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-2">
                <button
                  onClick={selectAll}
                  className="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Select All
                </button>
                <button
                  onClick={deselectAll}
                  className="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Deselect All
                </button>
                
                <div className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs">
                  {selectedItems.size} selected
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as 'all' | 'selected')}
                  className="text-xs px-2 py-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                >
                  <option value="all">Show All</option>
                  <option value="selected">Show Selected</option>
                </select>
                
                <button
                  onClick={() => setShowRawData(!showRawData)}
                  className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"
                  title={showRawData ? 'Show formatted' : 'Show raw data'}
                >
                  <Eye size={16} />
                </button>
              </div>
            </div>
          </div>
          
          {/* Content */}
          <div className="px-6 py-4 max-h-96 overflow-y-auto">
            {filteredResults.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No results to display
              </div>
            ) : showRawData ? (
              <pre className="text-xs bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-x-auto">
                {JSON.stringify(filteredResults, null, 2)}
              </pre>
            ) : (
              <div className="space-y-2">
                {filteredResults.map((item: any, index: number) => {
                  const itemId = item.id || index.toString();
                  const isSelected = selectedItems.has(itemId);
                  const isExpanded = expandedItems.has(itemId);
                  
                  return (
                    <div
                      key={itemId}
                      className={`border rounded-lg transition-colors ${
                        isSelected 
                          ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                    >
                      <div className="p-3 flex items-start gap-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleSelection(itemId)}
                          className="mt-1 rounded text-blue-600 focus:ring-blue-500"
                        />
                        
                        <div className="flex-1">
                          <button
                            onClick={() => toggleExpansion(itemId)}
                            className="flex items-start gap-2 w-full text-left"
                          >
                            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                            
                            <div className="flex-1">
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                {item.subject || item.title || item.name || `Item ${index + 1}`}
                              </div>
                              
                              {/* Metadata */}
                              <div className="flex items-center gap-3 mt-1 text-xs text-gray-600 dark:text-gray-400">
                                {item.from && (
                                  <span className="flex items-center gap-1">
                                    <User size={12} />
                                    {item.from}
                                  </span>
                                )}
                                {item.date && (
                                  <span className="flex items-center gap-1">
                                    <Clock size={12} />
                                    {formatDate(item.date)}
                                  </span>
                                )}
                                {item.labels && item.labels.length > 0 && (
                                  <span className="flex items-center gap-1">
                                    <Tag size={12} />
                                    {item.labels.join(', ')}
                                  </span>
                                )}
                                {item.hasAttachments && (
                                  <Paperclip size={12} />
                                )}
                              </div>
                              
                              {/* Snippet */}
                              {item.snippet && (
                                <p className="mt-2 text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                                  {item.snippet}
                                </p>
                              )}
                            </div>
                          </button>
                          
                          {/* Expanded Content */}
                          {isExpanded && (
                            <div className="mt-3 pl-6 space-y-2">
                              {item.body && (
                                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded text-xs text-gray-700 dark:text-gray-300">
                                  <div className="max-h-32 overflow-y-auto">
                                    {item.body}
                                  </div>
                                </div>
                              )}
                              
                              {item.url && (
                                <a
                                  href={item.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="inline-flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                                >
                                  <ExternalLink size={12} />
                                  Open in {getConnectorName()}
                                </a>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          
          {/* Refine Search */}
          <div className="px-6 py-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <Search size={16} className="text-gray-400" />
              <input
                type="text"
                value={refinedQuery}
                onChange={(e) => setRefinedQuery(e.target.value)}
                placeholder="Refine search query..."
                className="flex-1 px-3 py-1.5 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleRefine}
                disabled={!refinedQuery.trim()}
                className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md text-sm font-medium flex items-center gap-1"
              >
                <RefreshCw size={14} />
                Refine
              </button>
            </div>
          </div>
          
          {/* Footer */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Verify these results before Dante processes them
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={handleReject}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium flex items-center gap-1"
                >
                  <ThumbsDown size={14} />
                  Wrong Results
                </button>
                
                <button
                  onClick={handleConfirm}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm font-medium flex items-center gap-1"
                >
                  <ThumbsUp size={14} />
                  {selectedItems.size > 0 
                    ? `Process ${selectedItems.size} Selected` 
                    : 'Process All Results'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPreviewModal;