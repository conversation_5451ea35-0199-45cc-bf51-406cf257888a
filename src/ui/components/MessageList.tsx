import React, { useState, useEffect, lazy, Suspense } from 'react';
import { ThinkingVisualization, extractThinkingContent, hasThinkingContent } from './ThinkingVisualization';

// Lazy load only the React markdown component
const ReactMarkdown = lazy(() => import('react-markdown'));

// Import styles directly (they need to be available immediately for styling)
import 'highlight.js/styles/github-dark.css';
import { Reasoning, ReasoningContent, ReasoningTrigger } from '@/components/ai-elements/reasoning';
import { ChevronDownIcon } from 'lucide-react';

// Loading component for markdown
const MarkdownLoadingFallback = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
  </div>
);

interface MessageListProps {
  messages: Array<{
    role: string;
    content: string;
    reasoning?: string;  // Optional reasoning field
    confidence?: number; // Optional confidence score
    model?: string;      // Which model generated this response
    orchestrator?: string; // Which orchestrator was used
    isStreaming?: boolean; // Whether this message is being streamed
  }>;
  isLoading: boolean;
  currentOrchestrator?: string; // Current orchestrator being used
}

// Custom Image component for handling base64 and regular images
const ImageRenderer: React.FC<{ src?: string; alt?: string }> = ({ src, alt }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imageError, setImageError] = useState(false);

  if (!src) return null;

  const isBase64 = src.startsWith('data:image');

  const handleImageClick = () => {
    if (isBase64) {
      // Open base64 image in new tab
      const newWindow = window.open();
      if (newWindow) {
        newWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>${alt || 'Image'}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  background: #1a1a1a;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 100vh;
                }
                img {
                  max-width: 100%;
                  height: auto;
                  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
                  border-radius: 8px;
                }
              </style>
            </head>
            <body>
              <img src="${src}" alt="${alt || 'Image'}" />
            </body>
          </html>
        `);
        newWindow.document.close();
      }
    } else {
      setIsModalOpen(true);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (imageError) {
    return (
      <div className="my-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <p className="text-red-600 dark:text-red-400 text-sm">
          Failed to load image: {alt || 'Unknown image'}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="my-4 relative group">
        <img
          src={src}
          alt={alt || 'Image'}
          className="rounded-lg shadow-md max-w-full h-auto cursor-pointer transition-transform hover:scale-[1.02]"
          onClick={handleImageClick}
          onError={handleImageError}
        />
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleImageClick}
            className="bg-black/70 text-white px-3 py-1 rounded-md text-sm flex items-center gap-1 hover:bg-black/90"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M10 3H3v7M21 14v7h-7M21 3l-11 11M3 21l11-11" />
            </svg>
            Open
          </button>
        </div>
        {isBase64 && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 italic">
            Click image to open in new tab
          </p>
        )}
      </div>

      {/* Modal for non-base64 images */}
      {isModalOpen && !isBase64 && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
          onClick={() => setIsModalOpen(false)}
        >
          <div className="relative max-w-[90vw] max-h-[90vh]">
            <img
              src={src}
              alt={alt || 'Image'}
              className="max-w-full max-h-[90vh] object-contain rounded-lg"
            />
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-4 right-4 bg-black/70 text-white p-2 rounded-full hover:bg-black/90"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

// Custom SVG renderer component
const SVGRenderer: React.FC<{ code: string; language?: string }> = ({ code, language }) => {
  const [showPreview, setShowPreview] = useState(false);
  const [svgError, setSvgError] = useState(false);

  // Validate if the code contains COMPLETE and valid SVG
  const hasOpenTag = code.includes('<svg');
  const hasCloseTag = code.includes('</svg>');

  if (!hasOpenTag || !hasCloseTag) {
    return null; // Don't render if SVG is incomplete
  }

  // Additional validation: Check if SVG is properly formed
  const svgOpenCount = (code.match(/<svg/g) || []).length;
  const svgCloseCount = (code.match(/<\/svg>/g) || []).length;

  // Only render if we have balanced SVG tags
  if (svgOpenCount !== svgCloseCount || svgOpenCount === 0) {
    return null;
  }

  // Check for common incomplete patterns that suggest streaming is still in progress
  const incompletePatterns = [
    /<[^>]*$/,           // Unclosed tag at the end
    /<!--[^-]*$/,        // Unclosed comment at the end
    /"[^"]*$/,           // Unclosed attribute at the end
  ];

  for (const pattern of incompletePatterns) {
    if (pattern.test(code.trim())) {
      return null; // SVG appears to be incomplete
    }
  }

  const renderSVG = () => {
    try {
      // Create a safe wrapper for the SVG
      const svgWithStyles = code.replace(
        '<svg',
        '<svg style="max-width: 100%; height: auto;"'
      );

      return (
        <div className="my-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              SVG Preview
            </span>
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            >
              {showPreview ? 'Hide' : 'Show'} Preview
            </button>
          </div>

          {showPreview && (
            <div className="relative bg-white dark:bg-gray-900 border-2 border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
              <div
                className="flex justify-center items-center min-h-[200px]"
                dangerouslySetInnerHTML={{ __html: svgWithStyles }}
              />
              <div className="absolute top-2 right-2 flex gap-2">
                <button
                  onClick={() => {
                    const blob = new Blob([code], { type: 'image/svg+xml' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'image.svg';
                    a.click();
                    URL.revokeObjectURL(url);
                  }}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                >
                  Download SVG
                </button>
                <button
                  onClick={() => {
                    const newWindow = window.open();
                    if (newWindow) {
                      newWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                          <head>
                            <title>SVG Preview</title>
                            <style>
                              body {
                                margin: 0;
                                padding: 20px;
                                background: #f0f0f0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                              }
                            </style>
                          </head>
                          <body>
                            ${code}
                          </body>
                        </html>
                      `);
                      newWindow.document.close();
                    }
                  }}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                >
                  Open in New Tab
                </button>
              </div>
            </div>
          )}
        </div>
      );
    } catch (error) {
      setSvgError(true);
      return (
        <div className="my-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-600 dark:text-red-400">
          Error rendering SVG preview
        </div>
      );
    }
  };

  return renderSVG();
};

// Markdown renderer component with lazy loading
const LazyMarkdownRenderer: React.FC<{
  content: string;
  components?: any;
}> = ({ content, components }) => {
  const [plugins, setPlugins] = useState<{
    remarkGfm?: any;
    rehypeHighlight?: any;
    rehypeRaw?: any;
  }>({});
  const [pluginsLoaded, setPluginsLoaded] = useState(false);

  useEffect(() => {
    const loadMarkdownPlugins = async () => {
      try {
        // Load plugins dynamically
        const [
          remarkGfmModule,
          rehypeHighlightModule,
          rehypeRawModule,
        ] = await Promise.all([
          import('remark-gfm'),
          import('rehype-highlight'),
          import('rehype-raw'),
        ]);

        setPlugins({
          remarkGfm: remarkGfmModule.default,
          rehypeHighlight: rehypeHighlightModule.default,
          rehypeRaw: rehypeRawModule.default,
        });
        setPluginsLoaded(true);
      } catch (error) {
        console.error('Failed to load markdown plugins:', error);
        setPluginsLoaded(true); // Proceed without plugins
      }
    };

    loadMarkdownPlugins();
  }, []);

  if (!pluginsLoaded) {
    return <MarkdownLoadingFallback />;
  }

  return (
    <Suspense fallback={<MarkdownLoadingFallback />}>
      <ReactMarkdown
        remarkPlugins={plugins.remarkGfm ? [plugins.remarkGfm] : []}
        rehypePlugins={[plugins.rehypeHighlight, plugins.rehypeRaw].filter(Boolean)}
        className="prose prose-sm md:prose-base dark:prose-invert max-w-none
          prose-headings:font-bold prose-headings:mb-4 prose-headings:mt-6
          prose-h1:text-2xl prose-h1:border-b prose-h1:pb-2 prose-h1:border-gray-200 dark:prose-h1:border-gray-700
          prose-h2:text-xl prose-h2:mt-8 prose-h2:mb-4
          prose-h3:text-lg prose-h3:mt-6 prose-h3:mb-3
          prose-p:leading-relaxed prose-p:mb-4
          prose-ul:my-4 prose-ul:list-disc prose-ul:pl-6
          prose-ol:my-4 prose-ol:list-decimal prose-ol:pl-6
          prose-li:mb-2 prose-li:leading-relaxed
          prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:bg-gray-100 dark:prose-code:bg-gray-900
          prose-pre:bg-gray-900 prose-pre:shadow-lg prose-pre:rounded-lg prose-pre:my-4
          prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:pl-4 prose-blockquote:italic
          prose-strong:font-bold prose-strong:text-gray-900 dark:prose-strong:text-gray-100
          prose-a:text-blue-500 hover:prose-a:text-blue-600 prose-a:underline
          prose-hr:my-8 prose-hr:border-gray-200 dark:prose-hr:border-gray-700
          prose-table:my-4 prose-table:overflow-x-auto
          prose-th:bg-gray-50 dark:prose-th:bg-gray-900 prose-th:px-4 prose-th:py-2
          prose-td:px-4 prose-td:py-2 prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-700"
        components={components}
      >
        {content}
      </ReactMarkdown>
    </Suspense>
  );
};

const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, currentOrchestrator }) => {
  // Helper function to determine if a model is Gemini
  const isGeminiModel = (model?: string): boolean => {
    return model?.startsWith('gemini-') || false;
  };

  // Helper function to get orchestrator badge
  const getOrchestratorBadge = (model?: string, orchestrator?: string) => {
    const isGemini = isGeminiModel(model || orchestrator);

    if (isGemini) {
      return (
        <div className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-800 dark:to-blue-800 rounded-full border border-purple-200 dark:border-purple-600">
          <span className="text-lg">🎭</span>
          <span className="text-xs font-medium text-purple-700 dark:text-purple-300">
            Gemini Orchestrator
          </span>
        </div>
      );
    } else if (model) {
      return (
        <div className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-800 dark:to-blue-900 rounded-full border border-blue-200 dark:border-blue-600">
          <span className="text-lg">🤖</span>
          <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
            OpenAI Agent
          </span>
        </div>
      );
    }

    return null;
  };

  // Preprocess message content to handle various image formats
  const preprocessContent = (content: string): string => {
    // First, check if the content mentions a truncated image
    if (content.includes('TRUNCATED FOR BREVITY')) {
      // Add a note about the truncated image
      content = content.replace(
        /—TRUNCATED FOR BREVITY \(image data URI continued\)/g,
        '\n\n⚠️ *Note: The image data was truncated by the API. Full image display requires complete base64 data.*'
      );
    }

    // Pattern to match "Image: (data image — openable in most browsers/clients) data:image..."
    // This pattern is more flexible and handles partial matches
    const imagePattern = /Image:\s*\([^)]*\)\s*(data:image\/[^;\s]+;base64,[A-Za-z0-9+/=]+)/gi;

    // Also handle standalone data URIs that might appear in the text
    const standaloneDataUri = /^(data:image\/[^;\s]+;base64,[A-Za-z0-9+/=]+)$/gm;

    // Replace with markdown image syntax
    let processed = content.replace(imagePattern, (_match, dataUri) => {
      return `![Generated Image](${dataUri})`;
    });

    // Handle standalone data URIs
    processed = processed.replace(standaloneDataUri, (_match, dataUri) => {
      return `![Image](${dataUri})`;
    });

    return processed;
  };

  return (
    <div className="flex-1 overflow-y-auto px-4 py-6 h-full">
      <div className="max-w-7xl mx-auto space-y-4">
        {messages.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
              <span className="text-white font-bold text-2xl">D</span>
            </div>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-2">
              Welcome to Dante AI
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Your advanced AI assistant powered by GPT-5
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-4">
              Ask me anything - from simple queries to complex development tasks!
            </p>
          </div>
        )}

        {messages.map((message, index) => {
          // Extract thinking content for Gemini responses
          // Prefer explicit reasoning field (set by SSE when available),
          // otherwise attempt to parse from message content.
          const hasReasoningField = !!(message.reasoning && message.reasoning.trim().length > 0);
          const hasThinkingTagsOrHints = message.role === 'assistant' && hasThinkingContent(message.content);
          const hasThinking = hasReasoningField || hasThinkingTagsOrHints;
          const { thinking, response } = hasReasoningField
            ? { thinking: message.reasoning || '', response: message.content }
            : (hasThinkingTagsOrHints ? extractThinkingContent(message.content) : { thinking: '', response: message.content });

          return (
            <div key={index} className="space-y-3 message-appear">
              {/* Thinking Visualization (if present) */}
              {hasThinking && (
                <ThinkingVisualization
                  content={hasReasoningField ? `<Thought>${thinking}</Thought>` : message.content}
                  isStreaming={message.isStreaming}
                  className="mx-4"
                />
              )}

              {/* Main Message */}
              <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div
                  className={`rounded-xl px-5 py-4 shadow-sm transition-all hover:shadow-md ${
                    message.role === 'user'
                      ? 'max-w-[70%] bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                      : 'max-w-[95%] w-fit min-w-[60%] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                  }`}
                  style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}
                >
                  {/* Orchestrator Badge */}
                  {message.role === 'assistant' && (message.model || message.orchestrator) && (
                    <div className="mb-3">
                      {getOrchestratorBadge(message.model, message.orchestrator)}
                    </div>
                  )}

                  <div className="flex items-start gap-3">
                    {message.role === 'assistant' && (
                      <div className={`w-8 h-8 rounded-lg flex-shrink-0 flex items-center justify-center shadow-sm ${
                        isGeminiModel(message.model || message.orchestrator)
                          ? 'bg-gradient-to-r from-purple-500 to-blue-600'
                          : 'bg-gradient-to-r from-blue-500 to-purple-600'
                      }`}>
                        <span className="text-white text-sm font-bold">
                          {isGeminiModel(message.model || message.orchestrator) ? '🎭' : 'D'}
                        </span>
                      </div>
                    )}
                    <div className={`flex-1 ${message.role === 'assistant' ? 'markdown-content' : ''}`}>
                  {message.role === 'assistant' ? (
                    <LazyMarkdownRenderer
                      content={preprocessContent(hasThinking ? response : message.content)}
                      components={{
                        img: ImageRenderer,
                        pre: ({ children, ...props }: any) => {
                          // Check if this pre contains SVG code
                          const codeElement = children?.props;
                          const codeContent = codeElement?.children ? String(codeElement.children).replace(/\n$/, '') : '';

                          // Validate complete SVG with balanced tags
                          const hasOpenTag = codeContent.includes('<svg');
                          const hasCloseTag = codeContent.includes('</svg>');
                          const svgOpenCount = (codeContent.match(/<svg/g) || []).length;
                          const svgCloseCount = (codeContent.match(/<\/svg>/g) || []).length;

                          // Check for incomplete patterns
                          const incompletePatterns = [/<[^>]*$/, /<!--[^-]*$/, /"[^"]*$/];
                          const isIncomplete = incompletePatterns.some(pattern => pattern.test(codeContent.trim()));

                          // Only process as SVG if it's complete and balanced
                          const isCompleteSVG = hasOpenTag && hasCloseTag &&
                                                svgOpenCount === svgCloseCount &&
                                                svgOpenCount > 0 &&
                                                !isIncomplete;

                          if (isCompleteSVG) {
                            const className = codeElement?.className || '';
                            const match = /language-(\w+)/.exec(className);
                            const language = match ? match[1] : 'svg';

                            return (
                              <>
                                <div className="relative group">
                                  <pre {...props}>{children}</pre>
                                  <button
                                    className="absolute top-2 right-2 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
                                    onClick={() => navigator.clipboard.writeText(codeContent)}
                                  >
                                    Copy
                                  </button>
                                </div>
                                <SVGRenderer code={codeContent} language={language} />
                              </>
                            );
                          }

                          // For non-SVG code blocks: add collapsible wrapper for large blocks
                          const codeText = codeElement?.children ? String(codeElement.children) : '';
                          const lineCount = codeText.split('\n').length;
                          const large = lineCount > 30 || codeText.length > 2000;
                          const [collapsed, setCollapsed] = React.useState(large);
                          const toggle = () => setCollapsed(v => !v);

                          return (
                            <div className="relative group">
                              {large && (
                                <div className="flex items-center justify-between mb-1 text-xs text-gray-500">
                                  <span>{lineCount} lines</span>
                                  <div className="flex gap-2">
                                    <button
                                      className="px-2 py-0.5 rounded border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
                                      onClick={() => navigator.clipboard.writeText(codeText)}
                                      title="Copy code"
                                    >
                                      Copy
                                    </button>
                                    <button
                                      className="px-2 py-0.5 rounded border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
                                      onClick={toggle}
                                    >
                                      {collapsed ? 'Expand' : 'Collapse'}
                                    </button>
                                  </div>
                                </div>
                              )}
                              <div className={collapsed ? 'max-h-[360px] overflow-auto rounded-lg' : ''}>
                                <pre {...props}>{children}</pre>
                              </div>
                              {!large && (
                                <button
                                  className="absolute top-2 right-2 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={() => navigator.clipboard.writeText(codeText)}
                                >
                                  Copy
                                </button>
                              )}
                            </div>
                          );
                        },
                        code: ({ className, children, ...props }: any) => {
                          // This handles inline code only
                          // Block code is handled by the pre component above
                          return (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          );
                        },
                      }}
                    />
                  ) : (
                    <p className="m-0 whitespace-pre-wrap">{message.content}</p>
                  )}
                    </div>
                  </div>

                  {/* Display reasoning if available for assistant messages */}
                  {message.role === 'assistant' && message.reasoning && (
                    <div className="mt-4">
                      <Reasoning
                        isStreaming={message.isStreaming}
                        className="w-full"
                      >
                        <ReasoningTrigger className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-700 hover:from-purple-100 hover:to-blue-100 dark:hover:from-purple-900/30 dark:hover:to-blue-900/30">
                          <div className="flex items-start gap-2">
                            <span className="text-purple-600 dark:text-purple-400 mt-0.5">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                                <path d="M12 7v5l3 3"/>
                                <circle cx="12" cy="12" r="1"/>
                              </svg>
                            </span>
                            <div className="flex-1">
                              <h4 className="text-sm font-semibold text-purple-700 dark:text-purple-300 mb-2">
                                Reasoning Process
                              </h4>
                              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                                <span>Click to expand</span>
                                <ChevronDownIcon className="size-3 transition-transform" />
                              </div>
                            </div>
                          </div>
                        </ReasoningTrigger>
                        <ReasoningContent className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                          <div className="flex items-start gap-2">
                            <span className="text-purple-600 dark:text-purple-400 mt-0.5">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                                <path d="M12 7v5l3 3"/>
                                <circle cx="12" cy="12" r="1"/>
                              </svg>
                            </span>
                            <div className="flex-1">
                              <h4 className="text-sm font-semibold text-purple-700 dark:text-purple-300 mb-2">
                                Reasoning Process
                              </h4>
                              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap mb-3">
                                {message.reasoning}
                              </p>
                              {message.confidence !== undefined && (
                                <div className="mt-3">
                                  <div className="flex items-center gap-2">
                                    <span className="text-xs text-gray-600 dark:text-gray-400">Confidence:</span>
                                    <div className="flex-1 max-w-[200px]">
                                      <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div
                                          className={`h-full transition-all ${
                                            message.confidence >= 0.8
                                              ? 'bg-green-500'
                                              : message.confidence >= 0.5
                                              ? 'bg-yellow-500'
                                              : 'bg-red-500'
                                          }`}
                                          style={{ width: `${message.confidence * 100}%` }}
                                        />
                                      </div>
                                    </div>
                                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                      {(message.confidence * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </ReasoningContent>
                      </Reasoning>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white dark:bg-gray-800 rounded-lg px-4 py-3 border border-gray-200 dark:border-gray-700">
              <div className="typing-indicator">
                <span className="typing-dot"></span>
                <span className="typing-dot"></span>
                <span className="typing-dot"></span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageList;
