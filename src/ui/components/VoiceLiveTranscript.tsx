import React from 'react';
import { Mic } from 'lucide-react';
import { useVoiceStore } from '../stores/voiceStore';

const VoiceLiveTranscript: React.FC = () => {
  const { enabled, status, mode, isRecording, transcription } = useVoiceStore();

  if (!enabled) return null;
  const isRealtimeListening = mode === 'realtime' && status === 'listening';
  const isChainedActive = mode === 'chained' && isRecording;
  const shouldShow = !!transcription && (isRealtimeListening || isChainedActive);
  if (!shouldShow) return null;

  return (
    <div className="px-4 py-2">
      <div className="inline-flex max-w-full items-start gap-2 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-100 px-3 py-2 rounded-md shadow-sm">
        <Mic size={14} className="mt-0.5 opacity-80" />
        <div className="text-sm max-w-[75vw] truncate">{transcription}</div>
      </div>
    </div>
  );
};

export default VoiceLiveTranscript;

