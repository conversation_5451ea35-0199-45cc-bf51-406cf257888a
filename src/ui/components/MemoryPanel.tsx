import React, { useState, useEffect } from 'react';
import { memoryService } from '../services/memoryService';
import { Memory, MemoryType, MemoryPriority } from '../../memory/types';
import { Brain, Database, Search, Trash2, Download, Upload, RefreshCw, Star } from 'lucide-react';
import { ConfirmationModal } from './ConfirmationModal';
import { showToast } from './Toast';

interface MemoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  darkMode?: boolean;
}

export const MemoryPanel: React.FC<MemoryPanelProps> = ({ isOpen, onClose, darkMode = false }) => {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<MemoryType | 'all'>('all');
  const [stats, setStats] = useState<any>(null);
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; memoryId: string | null }>({ isOpen: false, memoryId: null });
  const [consolidateModal, setConsolidateModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isConsolidating, setIsConsolidating] = useState(false);

  // Load data when panel opens
  useEffect(() => {
    if (isOpen) {
      loadMemories();
      loadStats();
    }
  }, [isOpen, selectedType]);

  const loadMemories = async () => {
    setLoading(true);
    try {
      const result = await memoryService.search({
        type: selectedType === 'all' ? undefined : selectedType,
        limit: 100
      });
      setMemories(result.memories);
    } catch (error) {
      console.error('Failed to load memories:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const memoryStats = await memoryService.getStats();
      setStats(memoryStats);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadMemories();
      return;
    }

    setLoading(true);
    try {
      const results = await memoryService.semanticSearch({
        query: searchQuery,
        topK: 20
      });
      setMemories(results.memories);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!deleteModal.memoryId) return;
    
    setIsDeleting(true);
    try {
      const result = await memoryService.delete(deleteModal.memoryId);
      if (result.success) {
        showToast('Memory deleted successfully', 'success');
        loadMemories();
        loadStats();
      } else {
        showToast(`Failed to delete memory: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast('An error occurred while deleting the memory', 'error');
      console.error('Delete failed:', error);
    } finally {
      setIsDeleting(false);
      setDeleteModal({ isOpen: false, memoryId: null });
    }
  };

  const handleExport = async () => {
    const json = await memoryService.exportMemories();
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dante-memories-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const text = await file.text();
          const result = await memoryService.importMemories(text);
          if (result.success) {
            showToast(`Successfully imported ${result.affectedCount} memories`, 'success');
            loadMemories();
            loadStats();
          } else {
            showToast(`Import failed: ${result.error}`, 'error');
          }
        } catch (error) {
          showToast('Failed to read the import file', 'error');
          console.error('Import error:', error);
        }
      }
    };
    input.click();
  };

  const handleConsolidate = async () => {
    setIsConsolidating(true);
    try {
      await memoryService.consolidate();
      showToast('Memory consolidation completed successfully', 'success');
      loadMemories();
      loadStats();
    } catch (error) {
      showToast('Failed to consolidate memories', 'error');
      console.error('Consolidation error:', error);
    } finally {
      setIsConsolidating(false);
      setConsolidateModal(false);
    }
  };

  const getPriorityIcon = (priority: MemoryPriority) => {
    const count = {
      [MemoryPriority.CRITICAL]: 5,
      [MemoryPriority.HIGH]: 4,
      [MemoryPriority.MEDIUM]: 3,
      [MemoryPriority.LOW]: 2,
      [MemoryPriority.TRIVIAL]: 1
    }[priority];

    return (
      <div className="flex gap-0.5">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            size={12}
            className={i < count ? 'fill-yellow-500 text-yellow-500' : (darkMode ? 'text-gray-600' : 'text-gray-300')}
          />
        ))}
      </div>
    );
  };

  const getTypeColor = (type: MemoryType) => {
    if (darkMode) {
      return {
        [MemoryType.EPISODIC]: 'bg-blue-900 text-blue-200',
        [MemoryType.SEMANTIC]: 'bg-green-900 text-green-200',
        [MemoryType.PROCEDURAL]: 'bg-purple-900 text-purple-200'
      }[type];
    } else {
      return {
        [MemoryType.EPISODIC]: 'bg-blue-100 text-blue-800',
        [MemoryType.SEMANTIC]: 'bg-green-100 text-green-800',
        [MemoryType.PROCEDURAL]: 'bg-purple-100 text-purple-800'
      }[type];
    }
  };

  const formatContent = (content: any, maxLength = 200) => {
    try {
      const str = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      if (str.length > maxLength) return str.substring(0, maxLength) + '...';
      return str;
    } catch (e) {
      // Fallback if serialization fails
      return String(content).substring(0, maxLength) + (String(content).length > maxLength ? '...' : '');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl w-full max-w-6xl h-[80vh] flex flex-col`}>
        {/* Header */}
        <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
          <div className="flex items-center gap-2">
            <Brain className="text-purple-600" size={24} />
            <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>Memory Management</h2>
            {stats && (
              <span className={`text-sm ml-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {stats.totalMemories} memories stored
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className={`${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'}`}
          >
            ✕
          </button>
        </div>

        {/* Controls */}
        <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} flex flex-wrap gap-2 items-center`}>
          <div className="flex gap-2 flex-1">
            <input
              type="text"
              placeholder="Search memories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className={`flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                darkMode 
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center gap-2"
            >
              <Search size={16} />
              Search
            </button>
          </div>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as MemoryType | 'all')}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="all">All Types</option>
            <option value={MemoryType.EPISODIC}>Episodic</option>
            <option value={MemoryType.SEMANTIC}>Semantic</option>
            <option value={MemoryType.PROCEDURAL}>Procedural</option>
          </select>

          <div className="flex gap-2">
            <button
              onClick={handleExport}
              className={`px-3 py-2 border rounded-lg flex items-center gap-1 ${
                darkMode 
                  ? 'border-gray-600 hover:bg-gray-700 text-gray-300' 
                  : 'border-gray-300 hover:bg-gray-50 text-gray-700'
              }`}
              title="Export memories"
            >
              <Download size={16} />
            </button>
            <button
              onClick={handleImport}
              className={`px-3 py-2 border rounded-lg flex items-center gap-1 ${
                darkMode 
                  ? 'border-gray-600 hover:bg-gray-700 text-gray-300' 
                  : 'border-gray-300 hover:bg-gray-50 text-gray-700'
              }`}
              title="Import memories"
            >
              <Upload size={16} />
            </button>
            <button
              onClick={() => setConsolidateModal(true)}
              className={`px-3 py-2 border rounded-lg flex items-center gap-1 ${
                darkMode 
                  ? 'border-gray-600 hover:bg-gray-700 text-gray-300' 
                  : 'border-gray-300 hover:bg-gray-50 text-gray-700'
              }`}
              title="Consolidate memories"
              disabled={isConsolidating}
            >
              <RefreshCw size={16} className={isConsolidating ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        {/* Memory List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Loading memories...</div>
            </div>
          ) : memories.length === 0 ? (
            <div className={`flex flex-col items-center justify-center h-full ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              <Database size={48} className={`mb-4 ${darkMode ? 'text-gray-600' : 'text-gray-300'}`} />
              <p>No memories found</p>
              <p className="text-sm mt-2">Dante will remember important information as you interact</p>
            </div>
          ) : (
            <div className="space-y-3">
              {memories.map((memory) => (
                <div
                  key={memory.metadata.id}
                  className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' 
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(memory.metadata.type)}`}>
                        {memory.metadata.type}
                      </span>
                      {getPriorityIcon(memory.metadata.priority)}
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        Confidence: {Math.round(memory.metadata.confidence * 100)}%
                      </span>
                    </div>
                    <button
                      onClick={() => setDeleteModal({ isOpen: true, memoryId: memory.metadata.id })}
                      className="text-red-500 hover:text-red-700"
                      title="Delete memory"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>

                  <div className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {/* Render content with safe truncation */}
                    {formatContent(memory.content)}
                  </div>

                  <div className={`flex items-center gap-4 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    <span>Created: {new Date(memory.metadata.createdAt).toLocaleDateString()}</span>
                    <span>Accessed: {memory.metadata.accessCount} times</span>
                    {memory.metadata.tags.length > 0 && (
                      <div className="flex gap-1">
                        {memory.metadata.tags.map((tag, i) => (
                          <span key={i} className={`px-2 py-0.5 rounded ${
                            darkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-100 text-gray-700'
                          }`}>
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Stats Footer */}
        {stats && (
          <div className={`p-4 border-t text-xs ${
            darkMode 
              ? 'border-gray-700 bg-gray-800 text-gray-400' 
              : 'border-gray-200 bg-gray-50 text-gray-600'
          }`}>
            <div className="flex justify-around">
              <div>
                <span className="font-semibold">Episodic:</span> {stats.byType[MemoryType.EPISODIC]}
              </div>
              <div>
                <span className="font-semibold">Semantic:</span> {stats.byType[MemoryType.SEMANTIC]}
              </div>
              <div>
                <span className="font-semibold">Procedural:</span> {stats.byType[MemoryType.PROCEDURAL]}
              </div>
              <div>
                <span className="font-semibold">Avg Confidence:</span> {Math.round(stats.averageConfidence * 100)}%
              </div>
              <div>
                <span className="font-semibold">Storage:</span> {(stats.storageSize / 1024).toFixed(1)} KB
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, memoryId: null })}
        onConfirm={handleDelete}
        title="Delete Memory"
        message="Are you sure you want to delete this memory? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
        loading={isDeleting}
      />

      {/* Consolidate Confirmation Modal */}
      <ConfirmationModal
        isOpen={consolidateModal}
        onClose={() => setConsolidateModal(false)}
        onConfirm={handleConsolidate}
        title="Consolidate Memories"
        message="This will optimize and reorganize your memory storage. This process may take a moment. Continue?"
        confirmText="Consolidate"
        cancelText="Cancel"
        variant="info"
        loading={isConsolidating}
      />
    </div>
  );
};