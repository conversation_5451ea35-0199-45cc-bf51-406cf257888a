import React, { useMemo, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronRight, 
  ChevronDown,
  Clock,
  Zap,
  MessageSquare,
  ArrowRight,
  Shield,
  Settings,
  Play,
  CheckCircle,
  XCircle,
  AlertCircle,
  Circle
} from 'lucide-react';
import { useTraceStore, TraceSpan } from '../../stores/traceStore';

interface TraceVisualizationProps {
  traceId?: string;
  className?: string;
  viewMode?: 'tree' | 'timeline';
  showMetrics?: boolean;
}

interface SpanNodeProps {
  span: TraceSpan;
  depth: number;
  isExpanded: boolean;
  onToggle: (spanId: string) => void;
  onSelect: (spanId: string) => void;
  isSelected: boolean;
  children: TraceSpan[];
}

const getSpanIcon = (type: TraceSpan['type'], status: TraceSpan['status']) => {
  const iconProps = { size: 16 };
  
  switch (type) {
    case 'agent':
      return <MessageSquare {...iconProps} />;
    case 'generation':
      return <Zap {...iconProps} />;
    case 'function':
      return <Settings {...iconProps} />;
    case 'handoff':
      return <ArrowRight {...iconProps} />;
    case 'guardrail':
      return <Shield {...iconProps} />;
    default:
      return <Circle {...iconProps} />;
  }
};

const getStatusIcon = (status: TraceSpan['status']) => {
  const iconProps = { size: 14 };
  
  switch (status) {
    case 'running':
      return <Play {...iconProps} className="text-blue-500 animate-pulse" />;
    case 'completed':
      return <CheckCircle {...iconProps} className="text-green-500" />;
    case 'failed':
      return <XCircle {...iconProps} className="text-red-500" />;
    default:
      return <AlertCircle {...iconProps} className="text-yellow-500" />;
  }
};

const getSpanColor = (type: TraceSpan['type']) => {
  switch (type) {
    case 'agent':
      return 'border-blue-300 bg-blue-50 hover:bg-blue-100';
    case 'generation':
      return 'border-purple-300 bg-purple-50 hover:bg-purple-100';
    case 'function':
      return 'border-green-300 bg-green-50 hover:bg-green-100';
    case 'handoff':
      return 'border-orange-300 bg-orange-50 hover:bg-orange-100';
    case 'guardrail':
      return 'border-red-300 bg-red-50 hover:bg-red-100';
    default:
      return 'border-gray-300 bg-gray-50 hover:bg-gray-100';
  }
};

const formatDuration = (ms?: number) => {
  if (ms == null) return '...';
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
};

const SpanNode: React.FC<SpanNodeProps> = ({
  span,
  depth,
  isExpanded,
  onToggle,
  onSelect,
  isSelected,
  children
}) => {
  const hasChildren = children.length > 0;
  const paddingLeft = depth * 24 + 12;
  
  return (
    <div>
      <motion.div
        layout
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        className={`
          border rounded-lg p-3 mb-2 cursor-pointer transition-all duration-200
          ${getSpanColor(span.type)}
          ${isSelected ? 'ring-2 ring-blue-400 shadow-md' : ''}
        `}
        style={{ marginLeft: paddingLeft }}
        onClick={() => onSelect(span.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onToggle(span.id);
                }}
                className="text-gray-500 hover:text-gray-700 flex-shrink-0"
              >
                {isExpanded ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </button>
            )}
            
            <div className="flex items-center space-x-2 flex-shrink-0">
              {getSpanIcon(span.type, span.status)}
              {getStatusIcon(span.status)}
            </div>
            
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm truncate">
                {span.name}
              </div>
              {span.data?.tool && (
                <div className="text-xs text-gray-600">
                  Tool: {span.data.tool}
                </div>
              )}
              {span.data?.model && (
                <div className="text-xs text-gray-600">
                  Model: {span.data.model}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-3 text-xs text-gray-500 flex-shrink-0">
            <div className="flex items-center space-x-1">
              <Clock size={12} />
              <span>{formatDuration(span.duration)}</span>
            </div>
            
            {span.data?.tokens?.total != null && (
              <div className="text-blue-600">
                {span.data.tokens.total} tokens
              </div>
            )}
            
            <div className="text-gray-400">
              {span.startTime.toLocaleTimeString()}
            </div>
          </div>
        </div>
        
        {/* Progress bar for running spans */}
        {span.status === 'running' && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div className="bg-blue-500 h-1 rounded-full animate-pulse w-1/3"></div>
            </div>
          </div>
        )}
      </motion.div>
      
      {/* Children */}
      <AnimatePresence>
        {hasChildren && isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            {children.map((child) => (
              <SpanNodeContainer 
                key={child.id} 
                span={child} 
                depth={depth + 1} 
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const SpanNodeContainer: React.FC<{ span: TraceSpan; depth: number }> = ({ span, depth }) => {
  const { selectedSpanId, selectSpan, getChildSpans } = useTraceStore();
  const [expandedSpans, setExpandedSpans] = useState<Set<string>>(new Set());
  
  const children = getChildSpans(span.id);
  const isExpanded = expandedSpans.has(span.id);
  const isSelected = selectedSpanId === span.id;
  
  const handleToggle = useCallback((spanId: string) => {
    setExpandedSpans(prev => {
      const newSet = new Set(prev);
      if (newSet.has(spanId)) {
        newSet.delete(spanId);
      } else {
        newSet.add(spanId);
      }
      return newSet;
    });
  }, []);
  
  return (
    <SpanNode
      span={span}
      depth={depth}
      isExpanded={isExpanded}
      onToggle={handleToggle}
      onSelect={selectSpan}
      isSelected={isSelected}
      children={children}
    />
  );
};

const TimelineView: React.FC<{ spans: TraceSpan[] }> = ({ spans }) => {
  const sortedSpans = useMemo(() => {
    return [...spans].sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }, [spans]);
  
  const totalDuration = useMemo(() => {
    if (sortedSpans.length === 0) return 0;
    const start = sortedSpans[0].startTime.getTime();
    const end = Math.max(...sortedSpans.map(s => 
      s.endTime ? s.endTime.getTime() : Date.now()
    ));
    return end - start;
  }, [sortedSpans]);
  
  const startTime = sortedSpans.length > 0 ? sortedSpans[0].startTime.getTime() : Date.now();
  
  return (
    <div className="space-y-2">
      {sortedSpans.map((span, index) => {
        const relativeStart = span.startTime.getTime() - startTime;
        const duration = span.duration ?? 
          (span.endTime ? span.endTime.getTime() - span.startTime.getTime() : 1000);
        
        const leftPercent = totalDuration > 0 ? (relativeStart / totalDuration) * 100 : 0;
        const widthPercent = totalDuration > 0 ? (duration / totalDuration) * 100 : 1;
        
        return (
          <div key={span.id} className="relative">
            <div className="flex items-center text-xs text-gray-600 mb-1">
              <div className="w-40 truncate">
                {getSpanIcon(span.type, span.status)}
                <span className="ml-2">{span.name}</span>
              </div>
              <div className="flex-1 mx-4 relative h-6 bg-gray-100 rounded">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${widthPercent}%` }}
                  className={`
                    absolute h-full rounded transition-all duration-300
                    ${span.status === 'running' ? 'bg-blue-400 animate-pulse' :
                      span.status === 'completed' ? 'bg-green-400' :
                      span.status === 'failed' ? 'bg-red-400' : 'bg-gray-400'}
                  `}
                  style={{ left: `${leftPercent}%` }}
                />
              </div>
              <div className="w-16 text-right">
                {formatDuration(span.duration)}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export const TraceVisualization: React.FC<TraceVisualizationProps> = ({
  traceId,
  className = '',
  viewMode = 'tree',
  showMetrics = true
}) => {
  const { 
    traces, 
    getSpansByTrace, 
    getTraceMetrics,
    selectedTraceId 
  } = useTraceStore();
  
  const currentTraceId = traceId || selectedTraceId;
  const trace = currentTraceId ? traces.get(currentTraceId) : null;
  const spans = currentTraceId ? getSpansByTrace(currentTraceId) : [];
  const metrics = currentTraceId ? getTraceMetrics(currentTraceId) : null;
  
  const rootSpans = useMemo(() => {
    return spans.filter(span => !span.parentId);
  }, [spans]);
  
  if (!currentTraceId || !trace) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-500 ${className}`}>
        <div className="text-center">
          <MessageSquare size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No trace selected</p>
          <p className="text-sm">Select a trace to view its execution flow</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Trace Header */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="font-semibold text-lg">{trace.workflowName}</h3>
            <p className="text-sm text-gray-600">
              Started: {trace.startTime.toLocaleString()}
              {trace.endTime && (
                <span className="ml-3">
                  Duration: {formatDuration(trace.totalDuration)}
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {trace.status === 'running' && (
              <div className="flex items-center text-blue-600">
                <Play size={16} className="animate-pulse mr-1" />
                Running
              </div>
            )}
            {trace.status === 'completed' && (
              <div className="flex items-center text-green-600">
                <CheckCircle size={16} className="mr-1" />
                Completed
              </div>
            )}
            {trace.status === 'failed' && (
              <div className="flex items-center text-red-600">
                <XCircle size={16} className="mr-1" />
                Failed
              </div>
            )}
          </div>
        </div>
        
        {/* Metrics */}
        {showMetrics && metrics && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-700">{metrics.totalGenerations}</div>
              <div className="text-blue-600">LLM Calls</div>
            </div>
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="font-semibold text-green-700">{metrics.totalFunctionCalls}</div>
              <div className="text-green-600">Tool Calls</div>
            </div>
            <div className="text-center p-2 bg-orange-50 rounded">
              <div className="font-semibold text-orange-700">{metrics.totalHandoffs}</div>
              <div className="text-orange-600">Handoffs</div>
            </div>
            <div className="text-center p-2 bg-purple-50 rounded">
              <div className="font-semibold text-purple-700">
                {metrics.totalTokens?.total || 0}
              </div>
              <div className="text-purple-600">Tokens</div>
            </div>
          </div>
        )}
      </div>
      
      {/* Visualization */}
      <div className="bg-white rounded-lg border">
        {viewMode === 'tree' ? (
          <div className="p-4">
            <h4 className="font-medium mb-4">Execution Tree</h4>
            <div className="space-y-2">
              {rootSpans.map((span) => (
                <SpanNodeContainer 
                  key={span.id} 
                  span={span} 
                  depth={0} 
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="p-4">
            <h4 className="font-medium mb-4">Timeline View</h4>
            <TimelineView spans={spans} />
          </div>
        )}
      </div>
    </div>
  );
};