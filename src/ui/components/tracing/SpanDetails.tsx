import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>,
  Zap,
  MessageSquare,
  ArrowRight,
  Shield,
  Settings,
  Info,
  CheckCircle,
  XCircle,
  Play,
  Copy,
  ExternalLink,
  Code,
  User,
  Cpu
} from 'lucide-react';
import { useTraceStore, TraceSpan } from '../../stores/traceStore';

interface SpanDetailsProps {
  spanId?: string;
  className?: string;
}

interface DetailSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

const DetailSection: React.FC<DetailSectionProps> = ({ 
  title, 
  icon, 
  children, 
  defaultOpen = true 
}) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors"
      >
        <div className="flex items-center space-x-2">
          {icon}
          <span className="font-medium">{title}</span>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ArrowRight size={16} />
        </motion.div>
      </button>
      
      <motion.div
        initial={false}
        animate={{ height: isOpen ? 'auto' : 0 }}
        className="overflow-hidden"
      >
        <div className="p-3 border-t bg-white">
          {children}
        </div>
      </motion.div>
    </div>
  );
};

const CodeBlock: React.FC<{ content: any; language?: string }> = ({ 
  content, 
  language = 'json' 
}) => {
  const formatted = useMemo(() => {
    try {
      return typeof content === 'string' 
        ? content 
        : JSON.stringify(content, null, 2);
    } catch {
      return String(content);
    }
  }, [content]);
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(formatted);
  };
  
  return (
    <div className="relative">
      <div className="absolute top-2 right-2 z-10">
        <button
          onClick={copyToClipboard}
          className="p-1 bg-gray-800 hover:bg-gray-700 text-white rounded opacity-70 hover:opacity-100 transition-opacity"
          title="Copy to clipboard"
        >
          <Copy size={14} />
        </button>
      </div>
      <pre className="bg-gray-900 text-gray-100 p-3 rounded overflow-x-auto text-sm">
        <code>{formatted}</code>
      </pre>
    </div>
  );
};

const getSpanIcon = (type: TraceSpan['type']) => {
  switch (type) {
    case 'agent':
      return <User size={20} className="text-blue-600" />;
    case 'generation':
      return <Zap size={20} className="text-purple-600" />;
    case 'function':
      return <Settings size={20} className="text-green-600" />;
    case 'handoff':
      return <ArrowRight size={20} className="text-orange-600" />;
    case 'guardrail':
      return <Shield size={20} className="text-red-600" />;
    default:
      return <Info size={20} className="text-gray-600" />;
  }
};

const getStatusBadge = (status: TraceSpan['status']) => {
  switch (status) {
    case 'running':
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
          <Play size={12} className="mr-1 animate-pulse" />
          Running
        </span>
      );
    case 'completed':
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
          <CheckCircle size={12} className="mr-1" />
          Completed
        </span>
      );
    case 'failed':
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
          <XCircle size={12} className="mr-1" />
          Failed
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
          <Info size={12} className="mr-1" />
          Unknown
        </span>
      );
  }
};

const formatDuration = (ms?: number) => {
  if (!ms) return 'N/A';
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
};

export const SpanDetails: React.FC<SpanDetailsProps> = ({ 
  spanId, 
  className = '' 
}) => {
  const { spans, selectedSpanId, getChildSpans } = useTraceStore();
  
  const currentSpanId = spanId || selectedSpanId;
  const span = currentSpanId ? spans.get(currentSpanId) : null;
  const childSpans = currentSpanId ? getChildSpans(currentSpanId) : [];
  const parentSpan = span?.parentId ? spans.get(span.parentId) : null;
  
  if (!span) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-500 ${className}`}>
        <div className="text-center">
          <Info size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No span selected</p>
          <p className="text-sm">Click on a span in the trace view to see details</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Span Header */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start space-x-3">
            {getSpanIcon(span.type)}
            <div>
              <h3 className="font-semibold text-lg">{span.name}</h3>
              <p className="text-sm text-gray-600 capitalize">
                {span.type.replace('_', ' ')} Span
              </p>
            </div>
          </div>
          {getStatusBadge(span.status)}
        </div>
        
        {/* Basic Info Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-gray-600">Duration</div>
            <div className="font-medium">{formatDuration(span.duration)}</div>
          </div>
          <div>
            <div className="text-gray-600">Started</div>
            <div className="font-medium">{span.startTime.toLocaleTimeString()}</div>
          </div>
          <div>
            <div className="text-gray-600">Status</div>
            <div className="font-medium capitalize">{span.status}</div>
          </div>
          <div>
            <div className="text-gray-600">Children</div>
            <div className="font-medium">{childSpans.length}</div>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      {(parentSpan || childSpans.length > 0) && (
        <div className="bg-white rounded-lg border p-4">
          <h4 className="font-medium mb-3 flex items-center">
            <ArrowRight size={16} className="mr-2" />
            Navigation
          </h4>
          
          {parentSpan && (
            <div className="mb-3">
              <div className="text-sm text-gray-600 mb-1">Parent Span</div>
              <button className="flex items-center space-x-2 p-2 bg-gray-50 hover:bg-gray-100 rounded transition-colors">
                {getSpanIcon(parentSpan.type)}
                <span className="text-sm">{parentSpan.name}</span>
              </button>
            </div>
          )}
          
          {childSpans.length > 0 && (
            <div>
              <div className="text-sm text-gray-600 mb-2">Child Spans ({childSpans.length})</div>
              <div className="space-y-1">
                {childSpans.slice(0, 5).map((child) => (
                  <button 
                    key={child.id}
                    className="flex items-center justify-between w-full p-2 bg-gray-50 hover:bg-gray-100 rounded transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      {getSpanIcon(child.type)}
                      <span className="text-sm">{child.name}</span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {formatDuration(child.duration)}
                    </span>
                  </button>
                ))}
                {childSpans.length > 5 && (
                  <div className="text-sm text-gray-500 text-center pt-2">
                    +{childSpans.length - 5} more children
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Type-specific Details */}
      <div className="space-y-3">
        
        {/* Agent Span Details */}
        {span.type === 'agent' && span.data && (
          <DetailSection
            title="Agent Information"
            icon={<User size={16} />}
          >
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium text-gray-700">Agent Name</div>
                <div className="text-sm">{span.data.agent || 'Unknown'}</div>
              </div>
            </div>
          </DetailSection>
        )}
        
        {/* Generation Span Details */}
        {span.type === 'generation' && span.data && (
          <DetailSection
            title="LLM Generation"
            icon={<Zap size={16} />}
          >
            <div className="space-y-3">
              {span.data.model && (
                <div>
                  <div className="text-sm font-medium text-gray-700">Model</div>
                  <div className="text-sm font-mono">{span.data.model}</div>
                </div>
              )}
              
              {span.data.tokens && (
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">Token Usage</div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="font-semibold text-blue-700">
                        {span.data.tokens.input || 0}
                      </div>
                      <div className="text-blue-600">Input</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="font-semibold text-green-700">
                        {span.data.tokens.output || 0}
                      </div>
                      <div className="text-green-600">Output</div>
                    </div>
                    <div className="text-center p-2 bg-purple-50 rounded">
                      <div className="font-semibold text-purple-700">
                        {span.data.tokens.total || 0}
                      </div>
                      <div className="text-purple-600">Total</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </DetailSection>
        )}
        
        {/* Function Span Details */}
        {span.type === 'function' && span.data && (
          <>
            <DetailSection
              title="Tool Call"
              icon={<Settings size={16} />}
            >
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-gray-700">Tool Name</div>
                  <div className="text-sm font-mono">{span.data.tool || span.data.name}</div>
                </div>
                
                {span.data.args && (
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-2">Arguments</div>
                    <CodeBlock content={span.data.args} />
                  </div>
                )}
              </div>
            </DetailSection>
            
            {span.data.result && (
              <DetailSection
                title="Tool Result"
                icon={<CheckCircle size={16} />}
              >
                <CodeBlock content={span.data.result} />
              </DetailSection>
            )}
          </>
        )}
        
        {/* Handoff Span Details */}
        {span.type === 'handoff' && span.data && (
          <DetailSection
            title="Agent Handoff"
            icon={<ArrowRight size={16} />}
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded">
                <div className="flex items-center space-x-3">
                  <User size={20} className="text-orange-600" />
                  <span className="font-medium">{span.data.from_agent}</span>
                </div>
                <ArrowRight size={20} className="text-orange-500" />
                <div className="flex items-center space-x-3">
                  <User size={20} className="text-orange-600" />
                  <span className="font-medium">{span.data.to_agent}</span>
                </div>
              </div>
              
              {span.data.message && (
                <div>
                  <div className="text-sm font-medium text-gray-700">Message</div>
                  <div className="text-sm p-2 bg-gray-50 rounded">{span.data.message}</div>
                </div>
              )}
            </div>
          </DetailSection>
        )}
        
        {/* Guardrail Span Details */}
        {span.type === 'guardrail' && span.data && (
          <DetailSection
            title="Guardrail Check"
            icon={<Shield size={16} />}
          >
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium text-gray-700">Guardrail Name</div>
                <div className="text-sm">{span.data.name}</div>
              </div>
              
              <div>
                <div className="text-sm font-medium text-gray-700">Result</div>
                <div className="flex items-center space-x-2">
                  {span.data.passed ? (
                    <CheckCircle size={16} className="text-green-500" />
                  ) : (
                    <XCircle size={16} className="text-red-500" />
                  )}
                  <span className={span.data.passed ? 'text-green-700' : 'text-red-700'}>
                    {span.data.passed ? 'Passed' : 'Failed'}
                  </span>
                </div>
              </div>
              
              {span.data.details && (
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">Details</div>
                  <CodeBlock content={span.data.details} />
                </div>
              )}
            </div>
          </DetailSection>
        )}
        
        {/* Raw Data */}
        {span.data && Object.keys(span.data).length > 0 && (
          <DetailSection
            title="Raw Data"
            icon={<Code size={16} />}
            defaultOpen={false}
          >
            <CodeBlock content={span.data} />
          </DetailSection>
        )}
      </div>
    </div>
  );
};