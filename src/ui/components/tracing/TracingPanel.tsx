import React, { useState, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Draggable from 'react-draggable';
import { ResizableBox as ResizableBoxComponent } from 'react-resizable';
import {
  Activity,
  X,
  Maximize2,
  Minimize2,
  Search,
  Clock,
  Play,
  CheckCircle,
  XCircle,
  RotateCcw,
  Download,
  Eye,
  EyeOff,
  Calendar,
} from 'lucide-react';
import { useTraceStore, TraceInfo } from '../../stores/traceStore';
import { useLayoutStore } from '../../stores/layoutStore';
import { TraceVisualization } from './TraceVisualization';
import { SpanDetails } from './SpanDetails';
import 'react-resizable/css/styles.css';

const ResizableBox = ResizableBoxComponent as any;

interface TracingPanelProps {
  className?: string;
}

interface TraceListItemProps {
  trace: TraceInfo;
  isSelected: boolean;
  onSelect: (traceId: string) => void;
}

const TraceListItem: React.FC<TraceListItemProps> = ({ 
  trace, 
  isSelected, 
  onSelect 
}) => {
  const { getTraceMetrics } = useTraceStore();
  const metrics = getTraceMetrics(trace.id);
  
  const getStatusIcon = (status: TraceInfo['status']) => {
    switch (status) {
      case 'running':
        return <Play size={14} className="text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'failed':
        return <XCircle size={14} className="text-red-500" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };
  
  const formatDuration = (ms?: number) => {
    if (!ms) return '...';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className={`
        p-3 border rounded cursor-pointer transition-all duration-200
        ${isSelected 
          ? 'border-blue-400 bg-blue-50 shadow-md' 
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'}
      `}
      onClick={() => onSelect(trace.id)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="min-w-0 flex-1">
          <div className="font-medium text-sm truncate">
            {trace.workflowName}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-2">
            <span>{trace.startTime.toLocaleTimeString()}</span>
            {trace.groupId && (
              <span className="text-xs bg-gray-100 px-1 rounded">
                Group: {trace.groupId.slice(-6)}
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2 flex-shrink-0">
          {getStatusIcon(trace.status)}
        </div>
      </div>
      
      <div className="flex items-center justify-between text-xs text-gray-600">
        <div className="flex items-center space-x-3">
          <span>{trace.totalSpans} spans</span>
          <span>{metrics.totalGenerations} LLM</span>
          <span>{metrics.totalFunctionCalls} tools</span>
        </div>
        <div className="text-right">
          {formatDuration(trace.totalDuration)}
        </div>
      </div>
    </motion.div>
  );
};

const TracesList: React.FC<{
  searchTerm: string;
  statusFilter: string;
  onTraceSelect: (traceId: string) => void;
}> = ({ searchTerm, statusFilter, onTraceSelect }) => {
  const { 
    traces, 
    selectedTraceId, 
    showCompletedTraces 
  } = useTraceStore();
  
  const filteredTraces = useMemo(() => {
    const traceArray = Array.from(traces.values());
    
    return traceArray
      .filter(trace => {
        // Search filter
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          if (!trace.workflowName.toLowerCase().includes(searchLower)) {
            return false;
          }
        }
        
        // Status filter
        if (statusFilter !== 'all') {
          if (trace.status !== statusFilter) {
            return false;
          }
        }
        
        // Show completed filter
        if (!showCompletedTraces && trace.status === 'completed') {
          return false;
        }
        
        return true;
      })
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }, [traces, searchTerm, statusFilter, showCompletedTraces]);
  
  return (
    <div className="space-y-2 max-h-96 overflow-y-auto">
      <AnimatePresence>
        {filteredTraces.map((trace) => (
          <TraceListItem
            key={trace.id}
            trace={trace}
            isSelected={selectedTraceId === trace.id}
            onSelect={onTraceSelect}
          />
        ))}
      </AnimatePresence>
      
      {filteredTraces.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Activity size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No traces found</p>
          <p className="text-sm">Traces will appear here as agents execute</p>
        </div>
      )}
    </div>
  );
};

export const TracingPanel: React.FC<TracingPanelProps> = ({ 
  className = '' 
}) => {
  const { 
    isTracingPanelOpen, 
    setTracingPanelOpen, 
    selectedTraceId,
    selectedSpanId,
    selectTrace,
    clearTraces,
    clearOldTraces,
    showCompletedTraces,
    setShowCompletedTraces
  } = useTraceStore();
  
  // Panel state
  const [isMinimized, setIsMinimized] = useState(false);
  const [isDocked, setIsDocked] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'tree' | 'timeline'>('tree');
  const [showSpanDetails, setShowSpanDetails] = useState(false);
  
  // Panel dimensions
  const [panelSize, setPanelSize] = useState({
    width: Math.min(600, window.innerWidth * 0.4),
    height: Math.min(800, window.innerHeight * 0.7)
  });
  
  // Update panel size when window resizes
  useEffect(() => {
    const handleResize = () => {
      setPanelSize({
        width: Math.min(600, window.innerWidth * 0.4),
        height: Math.min(800, window.innerHeight * 0.7)
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const handleTraceSelect = (traceId: string) => {
    selectTrace(traceId);
    setShowSpanDetails(false); // Reset span details when switching traces
  };
  
  const handleExport = () => {
    // Export current trace data
    if (selectedTraceId) {
      // Implementation for exporting trace data
      console.log('Exporting trace:', selectedTraceId);
    }
  };
  
  const handleClearOld = () => {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    clearOldTraces(oneDayAgo);
  };
  
  if (!isTracingPanelOpen) {
    return null;
  }
  
  const panelContent = (
    <div className="bg-white border shadow-lg rounded-lg overflow-hidden flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-gray-50">
        <div className="flex items-center space-x-2">
          <Activity size={20} className="text-blue-600" />
          <span className="font-semibold">Agent Tracing</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setShowSpanDetails(!showSpanDetails)}
            className="p-1 hover:bg-gray-200 rounded"
            title={showSpanDetails ? 'Hide span details' : 'Show span details'}
          >
            {showSpanDetails ? <EyeOff size={16} /> : <Eye size={16} />}
          </button>
          
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-gray-200 rounded"
            title={isMinimized ? 'Maximize' : 'Minimize'}
          >
            {isMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>
          
          <button
            onClick={() => setTracingPanelOpen(false)}
            className="p-1 hover:bg-gray-200 rounded text-gray-600"
            title="Close"
          >
            <X size={16} />
          </button>
        </div>
      </div>
      
      {!isMinimized && (
        <div className="flex flex-1 overflow-hidden">
          {/* Left Sidebar - Traces List */}
          <div className="w-80 border-r flex flex-col">
            {/* Controls */}
            <div className="p-3 border-b space-y-3">
              {/* Search */}
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search traces..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              {/* Filters */}
              <div className="flex items-center space-x-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex-1 px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="running">Running</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </select>
                
                <button
                  onClick={() => setShowCompletedTraces(!showCompletedTraces)}
                  className={`p-1 rounded ${
                    showCompletedTraces 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                  title="Toggle completed traces"
                >
                  <CheckCircle size={16} />
                </button>
              </div>
              
              {/* Actions */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleClearOld}
                  className="flex items-center space-x-1 px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
                  title="Clear traces older than 24h"
                >
                  <Calendar size={14} />
                  <span>Clear Old</span>
                </button>
                
                <button
                  onClick={clearTraces}
                  className="flex items-center space-x-1 px-2 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded"
                  title="Clear all traces"
                >
                  <RotateCcw size={14} />
                  <span>Clear All</span>
                </button>
              </div>
            </div>
            
            {/* Traces List */}
            <div className="flex-1 p-3 overflow-hidden">
              <TracesList
                searchTerm={searchTerm}
                statusFilter={statusFilter}
                onTraceSelect={handleTraceSelect}
              />
            </div>
          </div>
          
          {/* Main Content */}
          <div className="flex-1 flex">
            {/* Trace Visualization */}
            <div className={showSpanDetails ? 'flex-1' : 'w-full'}>
              {selectedTraceId ? (
                <div className="h-full flex flex-col">
                  {/* View Mode Controls */}
                  <div className="flex items-center justify-between p-3 border-b bg-gray-50">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setViewMode('tree')}
                        className={`px-3 py-1 rounded text-sm ${
                          viewMode === 'tree'
                            ? 'bg-blue-100 text-blue-700'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        Tree View
                      </button>
                      <button
                        onClick={() => setViewMode('timeline')}
                        className={`px-3 py-1 rounded text-sm ${
                          viewMode === 'timeline'
                            ? 'bg-blue-100 text-blue-700'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        Timeline
                      </button>
                    </div>
                    
                    <button
                      onClick={handleExport}
                      className="flex items-center space-x-1 px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
                      title="Export trace data"
                    >
                      <Download size={14} />
                      <span>Export</span>
                    </button>
                  </div>
                  
                  {/* Trace Content */}
                  <div className="flex-1 p-4 overflow-auto">
                    <TraceVisualization
                      traceId={selectedTraceId}
                      viewMode={viewMode}
                      showMetrics={true}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Activity size={48} className="mx-auto mb-4 text-gray-300" />
                    <p>Select a trace to view details</p>
                    <p className="text-sm">Choose a trace from the list to see its execution flow</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Span Details Sidebar */}
            {showSpanDetails && (
              <div className="w-96 border-l">
                <div className="h-full overflow-auto">
                  <SpanDetails spanId={selectedSpanId} className="p-4" />
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
  
  if (isDocked) {
    // Docked mode - integrated into layout
    return (
      <div className={`h-full ${className}`}>
        {panelContent}
      </div>
    );
  }
  
  // Floating mode - draggable panel
  return (
    <Draggable
      handle=".tracing-panel-header"
      defaultPosition={{ x: 50, y: 50 }}
      bounds="parent"
    >
      <div
        className="fixed z-50"
        style={{
          width: panelSize.width,
          height: isMinimized ? 'auto' : panelSize.height,
        }}
      >
        <ResizableBox
          width={panelSize.width}
          height={isMinimized ? 60 : panelSize.height}
          minConstraints={[400, 300]}
          maxConstraints={[800, 900]}
          onResize={(e: any, { size }: any) => {
            setPanelSize({ width: size.width, height: size.height });
          }}
          resizeHandles={['se', 'e', 's']}
        >
          <div className="tracing-panel-header">
            {panelContent}
          </div>
        </ResizableBox>
      </div>
    </Draggable>
  );
};