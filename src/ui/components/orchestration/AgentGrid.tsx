import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAgentStore } from '../../stores/agentStore';
import { useLayoutStore } from '../../stores/layoutStore';
import { AgentCard } from './AgentCard';
import { Users, Grid3x3, Maximize, X } from 'lucide-react';
import Draggable from 'react-draggable';
import { ResizableBox as ResizableBoxComponent } from 'react-resizable';

const ResizableBox = ResizableBoxComponent as any;

export const AgentGrid: React.FC = () => {
  const { agents, activeAgents } = useAgentStore();
  const { panels, updatePanel, updatePanelPosition, updatePanelSize } = useLayoutStore();
  const agentPanel = panels.agents;
  const [expandedAgent, setExpandedAgent] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  const activeAgentList = activeAgents.map(name => agents.get(name)).filter(Boolean);
  
  if (!agentPanel.visible || activeAgentList.length === 0) {
    return null;
  }
  
  const handleDragStop = (e: any, data: any) => {
    setIsDragging(false);
    updatePanelPosition('agents', data.x, data.y);
  };
  
  const handleResize = (e: any, data: any) => {
    updatePanelSize('agents', data.size.width, data.size.height);
  };
  
  const handleClose = () => {
    updatePanel('agents', { visible: false });
  };
  
  const gridCols = expandedAgent ? 1 : Math.min(3, Math.ceil(Math.sqrt(activeAgentList.length)));
  
  return (
    <Draggable
      handle=".agent-grid-handle"
      defaultPosition={{ x: agentPanel.x || window.innerWidth - 450, y: agentPanel.y || 80 }}
      onStart={() => setIsDragging(true)}
      onStop={handleDragStop}
    >
      <div className={`fixed ${isDragging ? 'cursor-move' : ''}`} style={{ zIndex: 45 }}>
        <ResizableBox
          width={agentPanel.width || 400}
          height={agentPanel.height || 300}
          onResize={handleResize}
          minConstraints={[350, 250]}
          maxConstraints={[600, 500]}
          resizeHandles={['se', 'e', 's']}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="h-full bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="agent-grid-handle px-4 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 text-white flex items-center justify-between cursor-move">
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                <span className="font-semibold">Active Agents</span>
                <span className="bg-white/20 px-2 py-0.5 rounded text-sm">
                  {activeAgentList.length}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setExpandedAgent(null)}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                  title="Grid View"
                >
                  <Grid3x3 className="w-4 h-4" />
                </button>
                <button
                  onClick={handleClose}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                  title="Close"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            {/* Agent Grid */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div 
                className={`grid gap-4 ${
                  expandedAgent 
                    ? 'grid-cols-1' 
                    : `grid-cols-${gridCols}`
                }`}
                style={{
                  gridTemplateColumns: expandedAgent ? '1fr' : `repeat(${gridCols}, minmax(0, 1fr))`,
                }}
              >
                <AnimatePresence mode="popLayout">
                  {activeAgentList.map((agent) => agent && (
                    <motion.div
                      key={agent.id}
                      layout
                      className={expandedAgent === agent.name ? 'col-span-full row-span-full' : ''}
                    >
                      <AgentCard
                        agent={agent}
                        onExpand={() => setExpandedAgent(expandedAgent === agent.name ? null : agent.name)}
                        isExpanded={expandedAgent === agent.name}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>
            
            {/* Resize Handle */}
            <div className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize">
              <svg className="w-full h-full text-gray-400" viewBox="0 0 16 16" fill="currentColor">
                <path d="M13 13L3 13L13 3Z" opacity="0.2" />
                <path d="M13 9L9 13L13 13Z" opacity="0.4" />
              </svg>
            </div>
          </motion.div>
        </ResizableBox>
      </div>
    </Draggable>
  );
};