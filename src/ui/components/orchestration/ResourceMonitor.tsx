import React, { useMemo, useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import Draggable from 'react-draggable';
import { ResizableBox as ResizableBoxComponent } from 'react-resizable';

const ResizableBox = ResizableBoxComponent as any;
import { 
  Zap, 
  DollarSign, 
  AlertTriangle, 
  TrendingUp,
  Clock,
  Cpu,
  Activity,
  Move,
  X
} from 'lucide-react';
import { useTokenStore } from '../../stores/tokenStore';
import { useLayoutStore } from '../../stores/layoutStore';
import { useAgentStore } from '../../stores/agentStore';
import { 
  Pie<PERSON>hart, 
  Pie, 
  Cell, 
  ResponsiveContainer,
  Tooltip,
  RadialBarChart,
  RadialBar,
  Legend
} from 'recharts';
import 'react-resizable/css/styles.css';

export const ResourceMonitor: React.FC = () => {
  const { 
    currentUsage, 
    sessionTotal, 
    sessionCost, 
    rateLimitWarning,
    rateLimitResetTime,
    sessionByModel
  } = useTokenStore();
  
  const { panels, updatePanel, updatePanelPosition, updatePanelSize } = useLayoutStore();
  const { agents, activeAgents } = useAgentStore();
  const resourcePanel = panels.resources;
  const [isDragging, setIsDragging] = useState(false);
  const nodeRef = useRef(null);
  
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const defaultPosition = {
    x: resourcePanel.x !== undefined ? resourcePanel.x : windowSize.width - 400,
    y: resourcePanel.y !== undefined ? resourcePanel.y : windowSize.height - 220,
  };
  
  const defaultSize = {
    width: resourcePanel.width || 380,
    height: resourcePanel.height || 180,
  };
  
  const handleDragStop = (e: any, data: any) => {
    setIsDragging(false);
    updatePanelPosition('resources', data.x, data.y);
  };
  
  const handleResize = (e: any, data: any) => {
    updatePanelSize('resources', data.size.width, data.size.height);
  };
  
  const handleClose = () => {
    updatePanel('resources', { visible: false });
  };
  
  if (!resourcePanel.visible) {
    return null;
  }
  
  const usagePercentage = (currentUsage.used / currentUsage.limit) * 100;
  const isNearLimit = usagePercentage > 80;
  
  // Calculate agent token distribution
  const agentTokenData = useMemo(() => {
    const data: { name: string; value: number; color: string }[] = [];
    const colors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444'];
    
    let index = 0;
    agents.forEach((agent) => {
      if (agent.tokensUsed > 0) {
        data.push({
          name: agent.name,
          value: agent.tokensUsed,
          color: colors[index % colors.length],
        });
        index++;
      }
    });
    
    return data;
  }, [agents]);
  
  // Radial bar data for usage gauge
  const gaugeData = [{
    name: 'Usage',
    value: Math.min(usagePercentage, 100),
    fill: isNearLimit ? '#EF4444' : usagePercentage > 50 ? '#F59E0B' : '#10B981',
  }];

  const formatCost = (cost: number) => {
    return cost < 0.01 ? '<$0.01' : `$${cost.toFixed(2)}`;
  };

  // Model distribution data
  const modelTokenData = useMemo(() => {
    const entries = Array.from(sessionByModel?.entries?.() || []);
    const colors = ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'];
    return entries
      .filter(([_, v]) => (v?.total || 0) > 0)
      .slice(0, 5)
      .map(([model, v], i) => ({ name: String(model), value: v.total, color: colors[i % colors.length] }));
  }, [sessionByModel]);
  
  const formatTime = (resetTime?: Date) => {
    if (!resetTime) return 'N/A';
    const resetTimeMs = resetTime instanceof Date ? resetTime.getTime() : new Date(resetTime).getTime();
    const diff = resetTimeMs - Date.now();
    const minutes = Math.floor(diff / 60000);
    return `${minutes}m`;
  };
  
  return (
    <Draggable
      nodeRef={nodeRef}
      handle=".resource-monitor-handle"
      defaultPosition={defaultPosition}
      onStart={() => setIsDragging(true)}
      onStop={handleDragStop}
    >
      <div
        ref={nodeRef}
        className={`fixed ${isDragging ? 'cursor-move' : ''}`}
        style={{ zIndex: 30 }}
      >
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
        >
          <ResizableBox
            width={defaultSize.width}
            height={defaultSize.height}
            onResize={handleResize}
            minConstraints={[280, 120]}
            maxConstraints={[windowSize.width - 40, windowSize.height - 120]}
            resizeHandles={['se', 'e', 's', 'w', 'n', 'sw', 'nw', 'ne']}
            className="resource-monitor-resizable"
          >
            <div className="h-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Header */}
              <div className="resource-monitor-handle px-4 py-3 border-b border-gray-200 dark:border-gray-700 cursor-move bg-gradient-to-r from-green-500 to-teal-600 text-white">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Move size={16} className="opacity-50" />
                    <h3 className="text-sm font-semibold flex items-center gap-2">
                      <Activity className="w-4 h-4" />
                      Resource Monitor
                    </h3>
                  </div>
                  <div className="flex items-center gap-2">
                    {rateLimitWarning && (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                        className="flex items-center gap-1 text-xs text-orange-200"
                      >
                        <AlertTriangle className="w-3 h-3" />
                        Rate limit warning
                      </motion.div>
                    )}
                    <button 
                      onClick={handleClose}
                      className="text-white/70 hover:text-white p-1 hover:bg-white/20 rounded transition-colors"
                      title="Close"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Content */}
              <div className="p-4">
                <div className="grid grid-cols-3 gap-4">
        {/* Token Usage Gauge */}
        <div className="flex flex-col items-center">
          <ResponsiveContainer width={80} height={80}>
            <RadialBarChart 
              cx="50%" 
              cy="50%" 
              innerRadius="60%" 
              outerRadius="90%" 
              data={gaugeData}
              startAngle={180} 
              endAngle={0}
            >
              <RadialBar dataKey="value" cornerRadius={10} />
              <text 
                x="50%" 
                y="50%" 
                textAnchor="middle" 
                dominantBaseline="middle" 
                className="text-sm font-bold fill-current text-gray-900 dark:text-white"
              >
                {Math.round(usagePercentage)}%
              </text>
            </RadialBarChart>
          </ResponsiveContainer>
          <div className="text-center mt-1">
            <p className="text-xs text-gray-600 dark:text-gray-400">Token Usage</p>
            <p className="text-xs font-medium text-gray-900 dark:text-white">
              {currentUsage.used.toLocaleString()} / {currentUsage.limit.toLocaleString()}
            </p>
          </div>
        </div>
        
        {/* Cost & Stats */}
        <div className="flex flex-col justify-center space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <DollarSign className="w-3 h-3" />
              <span>Session Cost</span>
            </div>
            <span className="text-sm font-bold text-gray-900 dark:text-white">
              {formatCost(sessionCost)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <Zap className="w-3 h-3" />
              <span>Total Tokens</span>
            </div>
            <span className="text-sm font-bold text-gray-900 dark:text-white">
              {sessionTotal.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
              <Cpu className="w-3 h-3" />
              <span>Model</span>
            </div>
            <span className="text-xs font-medium text-gray-900 dark:text-white">
              {currentUsage.model}
            </span>
          </div>
          
          {rateLimitResetTime && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                <Clock className="w-3 h-3" />
                <span>Reset In</span>
              </div>
              <span className="text-xs font-medium text-orange-500">
                {formatTime(rateLimitResetTime)}
              </span>
            </div>
          )}
        </div>
        
        {/* Agent Distribution */}
        {agentTokenData.length > 0 && (
          <div className="flex flex-col items-center">
            <ResponsiveContainer width={80} height={80}>
              <PieChart>
                <Pie
                  data={agentTokenData}
                  cx="50%"
                  cy="50%"
                  innerRadius={20}
                  outerRadius={35}
                  dataKey="value"
                >
                  {agentTokenData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => value.toLocaleString()}
                  contentStyle={{ 
                    backgroundColor: 'rgba(0,0,0,0.8)', 
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '11px'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">By Agent</p>
          </div>
        )}

        {/* Model Distribution */}
        {modelTokenData.length > 0 && (
          <div className="flex flex-col items-center">
            <ResponsiveContainer width={80} height={80}>
              <PieChart>
                <Pie
                  data={modelTokenData}
                  cx="50%"
                  cy="50%"
                  innerRadius={20}
                  outerRadius={35}
                  dataKey="value"
                >
                  {modelTokenData.map((entry, index) => (
                    <Cell key={`model-cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => value.toLocaleString()}
                  contentStyle={{ 
                    backgroundColor: 'rgba(0,0,0,0.8)', 
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '11px'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">By Model</p>
          </div>
        )}
      </div>
      
                {/* Active Agents Indicator */}
                {activeAgents.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Active:</span>
                      <div className="flex gap-1">
                        {activeAgents.slice(0, 5).map(name => (
                          <span 
                            key={name}
                            className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded text-xs"
                          >
                            {name}
                          </span>
                        ))}
                        {activeAgents.length > 5 && (
                          <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                            +{activeAgents.length - 5}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </ResizableBox>
        </motion.div>
      </div>
    </Draggable>
  );
};
