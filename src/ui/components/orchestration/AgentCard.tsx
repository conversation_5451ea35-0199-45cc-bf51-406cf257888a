import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  Z<PERSON>, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Activity,
  Wrench,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { AgentState, ActivityLogEntry } from '../../stores/agentStore';
import { useThoughtStore } from '../../stores/thoughtStore';
import { FixedSizeList } from 'react-window';

const List = FixedSizeList as any;
import { useLayoutStore } from '../../stores/layoutStore';

interface AgentCardProps {
  agent: AgentState;
  onExpand?: () => void;
  isExpanded?: boolean;
}

const ActivityLogItem: React.FC<{ entry: ActivityLogEntry; index: number }> = ({ entry, index }) => {
  const iconMap: Record<string, React.ReactNode> = {
    tool_call: <Wrench className="w-3 h-3 text-blue-500" />,
    message: <Activity className="w-3 h-3 text-green-500" />,
    handoff: <Bot className="w-3 h-3 text-purple-500" />,
    error: <AlertCircle className="w-3 h-3 text-red-500" />,
    thinking: <Brain className="w-3 h-3 text-yellow-500" />,
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.05 }}
      className="flex items-start gap-2 px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      <div className="mt-0.5">{iconMap[entry.type] || <Activity className="w-3 h-3" />}</div>
      <div className="flex-1 min-w-0">
        <p className="text-xs text-gray-700 dark:text-gray-300 break-words">
          {entry.content}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
          {new Date(entry.timestamp).toLocaleTimeString()}
        </p>
      </div>
    </motion.div>
  );
};

export const AgentCard: React.FC<AgentCardProps> = ({ agent, onExpand, isExpanded = false }) => {
  const [showDetails, setShowDetails] = useState(false);
  const { agentCardSize } = useLayoutStore();
  const latestThought = useThoughtStore((s) => s.getLatestForAgent(agent.name));
  
  const statusColors = {
    idle: 'bg-gray-400',
    active: 'bg-blue-500',
    thinking: 'bg-yellow-500',
    completed: 'bg-green-500',
    error: 'bg-red-500',
  };
  
  const statusIcons = {
    idle: <Clock className="w-4 h-4" />,
    active: <Activity className="w-4 h-4 animate-pulse" />,
    thinking: <Brain className="w-4 h-4 animate-pulse" />,
    completed: <CheckCircle className="w-4 h-4" />,
    error: <AlertCircle className="w-4 h-4" />,
  };
  
  const sizeClasses = {
    small: 'w-64 h-32',
    medium: 'w-80 h-48',
    large: 'w-96 h-64',
  };
  
  const duration = agent.endTime && agent.startTime
    ? Math.round((agent.endTime.getTime() - agent.startTime.getTime()) / 1000)
    : agent.startTime
    ? Math.round((Date.now() - agent.startTime.getTime()) / 1000)
    : 0;
  
  return (
    <motion.div
      layout
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      whileHover={{ scale: 1.02 }}
      className={`${isExpanded ? 'w-full h-full' : sizeClasses[agentCardSize]} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all`}
    >
      {/* Header */}
      <div className={`px-4 py-3 ${statusColors[agent.status]} bg-opacity-10 border-b border-gray-200 dark:border-gray-700`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-1.5 rounded-full ${statusColors[agent.status]} bg-opacity-20`}>
              {statusIcons[agent.status]}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">{agent.name}</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {agent.currentTask || 'No active task'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            >
              {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>
            {onExpand && (
              <button
                onClick={onExpand}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              >
                {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </button>
            )}
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-4 mt-2 text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <Zap className="w-3 h-3" />
            <span>{agent.tokensUsed.toLocaleString()} tokens</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>{duration}s</span>
          </div>
          {agent.currentTool && (
            <div className="flex items-center gap-1">
              <Wrench className="w-3 h-3" />
              <span>{agent.currentTool}</span>
            </div>
          )}
        </div>
      </div>
      
      {/* Activity Log */}
      <AnimatePresence>
        {(showDetails || isExpanded) && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: isExpanded ? 'calc(100% - 120px)' : 200 }}
            exit={{ height: 0 }}
            className="overflow-hidden"
          >
            {agent.activityLog.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-400 text-sm">
                No activity yet
              </div>
            ) : isExpanded ? (
              <List
                height={400}
                itemCount={agent.activityLog.length}
                itemSize={60}
                width="100%"
              >
                {({ index, style }: { index: number; style: React.CSSProperties }) => (
                  <div style={style}>
                    <ActivityLogItem entry={agent.activityLog[index]} index={index} />
                  </div>
                )}
              </List>
            ) : (
              <div className="overflow-y-auto max-h-48">
                {agent.activityLog.slice(-5).map((entry, index) => (
                  <ActivityLogItem key={index} entry={entry} index={index} />
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Subtle live thought snippet */}
      {latestThought && latestThought.content && (
        <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400 truncate">
          <span className="font-medium mr-1">Thought:</span>
          <span title={latestThought.content}>{latestThought.content}</span>
        </div>
      )}
      
      {/* Status Bar */}
      {agent.status === 'active' && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 to-purple-600"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      )}
    </motion.div>
  );
};
