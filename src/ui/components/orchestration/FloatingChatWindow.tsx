import React, { useState, useRef, useEffect } from 'react';
import Draggable from 'react-draggable';
import { ResizableBox as ResizableBoxComponent } from 'react-resizable';

const ResizableBox = ResizableBoxComponent as any;
import { X, Minimize2, Maximize2, Move, Minus } from 'lucide-react';
import ChatInterface from '../ChatInterface';
import { useLayoutStore } from '../../stores/layoutStore';
import { motion, AnimatePresence } from 'framer-motion';
import 'react-resizable/css/styles.css';

interface FloatingChatWindowProps {
  messages: Array<{ role: string; content: string }>;
  onSendMessage: (message: string, attachments: { url: string; file?: File }[]) => void;
  isLoading: boolean;
  agentEvents?: any[];
  isThinking?: boolean;
  currentAgent?: string;
  onCancelRun?: (agentName?: string) => void;
  onRetryRun?: (agentName?: string) => void;
}

export const FloatingChatWindow: React.FC<FloatingChatWindowProps> = ({
  messages,
  onSendMessage,
  isLoading,
  agentEvents,
  isThinking,
  currentAgent,
  onCancelRun,
  onRetryRun,
}) => {
  const { panels, updatePanel, updatePanelPosition, updatePanelSize } = useLayoutStore();
  const chatPanel = panels.chat;
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const nodeRef = useRef<HTMLDivElement | null>(null);
  
  const defaultPosition = {
    x: chatPanel.x !== undefined ? chatPanel.x : (typeof window !== 'undefined' ? window.innerWidth / 2 - 300 : 100),
    y: chatPanel.y !== undefined ? chatPanel.y : 120,
  };
  
  const defaultSize = {
    width: chatPanel.width || 600,
    height: chatPanel.height || 500,
  };

  const safeWindowWidth = typeof window !== 'undefined' ? window.innerWidth : 1024;
  const safeWindowHeight = typeof window !== 'undefined' ? window.innerHeight : 768;
  
  const handleDragStop = (e: any, data: any) => {
    setIsDragging(false);
    updatePanelPosition('chat', data.x, data.y);
  };
  
  const handleResize = (e: any, data: any) => {
    updatePanelSize('chat', data.size.width, data.size.height);
  };
  
  const handleMinimize = () => {
    setIsMinimized(!isMinimized);
    updatePanel('chat', { collapsed: !isMinimized });
  };
  
  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to previous size
      updatePanelSize('chat', defaultSize.width, defaultSize.height);
      updatePanelPosition('chat', defaultPosition.x, defaultPosition.y);
    } else {
      // Maximize to full screen
      updatePanelSize('chat', window.innerWidth - 100, window.innerHeight - 100);
      updatePanelPosition('chat', 50, 50);
    }
    setIsMaximized(!isMaximized);
  };
  
  const handleClose = () => {
    updatePanel('chat', { visible: false });
  };
  
  if (!chatPanel.visible) {
    return null;
  }
  
  return (
    <AnimatePresence>
      <Draggable
        nodeRef={nodeRef}
        handle=".chat-window-handle"
        defaultPosition={defaultPosition}
        position={isMaximized ? { x: 50, y: 50 } : undefined}
        onStart={() => setIsDragging(true)}
        onStop={handleDragStop}
        disabled={isMaximized}
      >
        <div
          ref={nodeRef}
          className={`fixed ${isDragging ? 'cursor-move' : ''}`}
          style={{
            opacity: chatPanel.opacity || 0.98,
            zIndex: 60,
          }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <ResizableBox
              width={isMaximized ? safeWindowWidth - 100 : defaultSize.width}
              height={isMaximized ? safeWindowHeight - 100 : (isMinimized ? 50 : defaultSize.height)}
              onResize={handleResize}
              minConstraints={[400, isMinimized ? 50 : 300]}
              maxConstraints={[safeWindowWidth - 100, safeWindowHeight - 100]}
              resizeHandles={isMinimized || isMaximized ? [] : ['se', 'e', 's', 'w', 'n', 'sw', 'nw', 'ne']}
              className="relative floating-chat-resizable"
            >
              <div className="flex flex-col h-full bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Window Header */}
                <div className="chat-window-handle flex items-center justify-between px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white cursor-move">
                  <div className="flex items-center gap-2">
                    <Move size={16} className="opacity-50" />
                    <span className="font-semibold">Dante Chat</span>
                    {currentAgent && currentAgent !== 'Dante' && (
                      <span className="text-xs bg-white/20 px-2 py-0.5 rounded">
                        via {currentAgent}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleMinimize}
                      className="p-1 hover:bg-white/20 rounded transition-colors"
                      title={isMinimized ? "Restore" : "Minimize"}
                    >
                      <Minus size={16} />
                    </button>
                    <button
                      onClick={handleMaximize}
                      className="p-1 hover:bg-white/20 rounded transition-colors"
                      title={isMaximized ? "Restore" : "Maximize"}
                    >
                      {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                    </button>
                    <button
                      onClick={handleClose}
                      className="p-1 hover:bg-white/20 rounded transition-colors"
                      title="Close"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
                
                {/* Chat Content */}
                {!isMinimized && (
                  <div className="flex-1 overflow-hidden">
                    <ChatInterface
                      messages={messages}
                      onSendMessage={onSendMessage}
                      isLoading={isLoading}
                      agentEvents={agentEvents}
                      isThinking={isThinking}
                      currentAgent={currentAgent}
                      onCancelRun={onCancelRun}
                      onRetryRun={onRetryRun}
                    />
                  </div>
                )}
                
                {/* Minimized State */}
                {isMinimized && (
                  <div
                    onClick={handleMinimize}
                    className="px-4 py-1 text-sm text-gray-600 dark:text-gray-400 cursor-pointer"
                    role="button"
                    aria-label="Restore chat window"
                  >
                    {isLoading ? (
                      <span className="flex items-center gap-2">
                        <span className="animate-pulse">●</span>
                        {currentAgent} is responding...
                      </span>
                    ) : (
                      <span>Click to expand chat</span>
                    )}
                  </div>
                )}
                
                {/* Resize Handle Indicator */}
                {!isMinimized && !isMaximized && (
                  <div className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize">
                    <svg
                      className="w-full h-full text-gray-400"
                      viewBox="0 0 16 16"
                      fill="currentColor"
                    >
                      <path d="M13 13L3 13L13 3Z" opacity="0.2" />
                      <path d="M13 9L9 13L13 13Z" opacity="0.4" />
                    </svg>
                  </div>
                )}
              </div>
            </ResizableBox>
          </motion.div>
        </div>
      </Draggable>
    </AnimatePresence>
  );
};
