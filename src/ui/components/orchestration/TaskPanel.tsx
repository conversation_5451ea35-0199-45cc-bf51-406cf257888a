import React, { useMemo, useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Draggable from 'react-draggable';
import { ResizableBox as ResizableBoxComponent } from 'react-resizable';

const ResizableBox = ResizableBoxComponent as any;
import { 
  Clock, 
  CheckCircle, 
  Circle, 
  AlertCircle, 
  XCircle,
  ChevronRight,
  User,
  Zap,
  Activity,
  Move,
  X
} from 'lucide-react';
import { useTaskStore, Task, TaskStatus } from '../../stores/taskStore';
import { useLayoutStore } from '../../stores/layoutStore';
import { useThoughtStore } from '../../stores/thoughtStore';
import 'react-resizable/css/styles.css';

interface TaskCardProps {
  task: Task;
  onTaskClick?: (task: Task) => void;
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onTaskClick }) => {
  const latestThought = useThoughtStore((s) => s.getLatestForAgent(task.assignedAgent || ''));
  
  const statusIcon = {
    pending: <Circle className="w-4 h-4 text-gray-400" />,
    in_progress: <Clock className="w-4 h-4 text-blue-500 animate-pulse" />,
    completed: <CheckCircle className="w-4 h-4 text-green-500" />,
    failed: <XCircle className="w-4 h-4 text-red-500" />,
    cancelled: <AlertCircle className="w-4 h-4 text-yellow-500" />,
  };
  
  const priorityColor = {
    low: 'border-gray-300',
    medium: 'border-blue-400',
    high: 'border-orange-400',
    critical: 'border-red-500',
  };
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className={`bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border-l-4 ${priorityColor[task.priority]} cursor-pointer transition-all`}
      onClick={() => onTaskClick?.(task)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          {statusIcon[task.status]}
          <span className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
            {task.description}
          </span>
        </div>
        {task.estimatedTokens && (
          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
            <Zap className="w-3 h-3" />
            {task.estimatedTokens.toLocaleString()}
          </span>
        )}
      </div>
      
      {task.assignedAgent && (
        <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 mb-2">
          <User className="w-3 h-3" />
          {task.assignedAgent}
        </div>
      )}
      
      {task.status === 'in_progress' && (
        <div className="mt-2">
          <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
            <span>Progress</span>
            <span>{task.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-1.5 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${task.progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      )}
      
      {latestThought?.content && (
        <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
          <span className="font-medium">Thought:</span> <span className="truncate" title={latestThought.content}>{latestThought.content}</span>
        </div>
      )}
      
      {task.subtasks.length > 0 && (
        <div className="mt-2 flex items-center gap-1 text-xs text-gray-500">
          <ChevronRight className="w-3 h-3" />
          {task.subtasks.length} subtasks
        </div>
      )}
      
      {task.error && (
        <div className="mt-2 text-xs text-red-500 dark:text-red-400">
          Error: {task.error}
        </div>
      )}
    </motion.div>
  );
};

interface TaskColumnProps {
  title: string;
  status: TaskStatus[];
  tasks: Task[];
  accentColor: string;
  onTaskClick?: (task: Task) => void;
}

const TaskColumn: React.FC<TaskColumnProps> = ({ 
  title, 
  status, 
  tasks, 
  accentColor,
  onTaskClick 
}) => {
  const filteredTasks = tasks.filter(task => status.includes(task.status));
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <div className={`px-4 py-2 ${accentColor} text-white font-semibold rounded-t-lg flex items-center justify-between`}>
        <span>{title}</span>
        <span className="bg-white/20 px-2 py-0.5 rounded text-sm">
          {filteredTasks.length}
        </span>
      </div>
      
      <div className="flex-1 bg-gray-50 dark:bg-gray-900 p-3 rounded-b-lg overflow-y-auto space-y-2">
        <AnimatePresence mode="popLayout">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-8 text-gray-400 text-sm">
              No tasks
            </div>
          ) : (
            filteredTasks.map(task => (
              <TaskCard 
                key={task.id} 
                task={task} 
                onTaskClick={onTaskClick}
              />
            ))
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export const TaskPanel: React.FC = () => {
  const { tasks, taskOrder } = useTaskStore();
  const { panels, updatePanel, updatePanelPosition, updatePanelSize } = useLayoutStore();
  const taskPanel = panels.tasks;
  const [isDragging, setIsDragging] = useState(false);
  const nodeRef = useRef(null);
  
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const defaultPosition = {
    x: taskPanel.x !== undefined ? taskPanel.x : 20,
    y: taskPanel.y !== undefined ? taskPanel.y : 80,
  };
  
  const defaultSize = {
    width: taskPanel.width || 350,
    height: taskPanel.height || 400,
  };
  
  const handleDragStop = (e: any, data: any) => {
    setIsDragging(false);
    updatePanelPosition('tasks', data.x, data.y);
  };
  
  const handleResize = (e: any, data: any) => {
    updatePanelSize('tasks', data.size.width, data.size.height);
  };
  
  const handleClose = () => {
    updatePanel('tasks', { visible: false });
  };
  
  const orderedTasks = useMemo(() => {
    return taskOrder
      .map(id => tasks.get(id))
      .filter((task): task is Task => task !== undefined);
  }, [tasks, taskOrder]);
  
  const handleTaskClick = (task: Task) => {
    // TODO: Implement task detail view or focus on agent handling this task
    console.log('Task clicked:', task);
  };
  
  if (!taskPanel.visible) {
    return null;
  }
  
  const totalTokens = orderedTasks.reduce((sum, task) => sum + (task.actualTokens || task.estimatedTokens || 0), 0);
  const completedCount = orderedTasks.filter(t => t.status === 'completed').length;
  const inProgressCount = orderedTasks.filter(t => t.status === 'in_progress').length;
  
  return (
    <Draggable
      nodeRef={nodeRef}
      handle=".task-panel-handle"
      defaultPosition={defaultPosition}
      onStart={() => setIsDragging(true)}
      onStop={handleDragStop}
    >
      <div
        ref={nodeRef}
        className={`fixed ${isDragging ? 'cursor-move' : ''}`}
        style={{ zIndex: 30 }}
      >
        <motion.div
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -300, opacity: 0 }}
        >
          <ResizableBox
            width={defaultSize.width}
            height={defaultSize.height}
            onResize={handleResize}
            minConstraints={[250, 200]}
            maxConstraints={[windowSize.width - 40, windowSize.height - 120]}
            resizeHandles={['se', 'e', 's', 'w', 'n', 'sw', 'nw', 'ne']}
            className="task-panel-resizable"
          >
            <div className="h-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
              {/* Header */}
              <div className="task-panel-handle px-4 py-3 border-b border-gray-200 dark:border-gray-700 cursor-move bg-gradient-to-r from-gray-500 to-blue-600 text-white">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Move size={16} className="opacity-50" />
                    <h2 className="text-lg font-semibold">Task Queue</h2>
                  </div>
                  <button 
                    onClick={handleClose}
                    className="text-white/70 hover:text-white p-1 hover:bg-white/20 rounded transition-colors"
                    title="Close"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                
                {/* Stats */}
                <div className="flex items-center gap-4 text-xs text-white/80">
                  <div className="flex items-center gap-1">
                    <CheckCircle className="w-3 h-3 text-green-200" />
                    <span>{completedCount} done</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3 text-blue-200" />
                    <span>{inProgressCount} active</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="w-3 h-3 text-yellow-200" />
                    <span>{totalTokens.toLocaleString()} tokens</span>
                  </div>
                </div>
              </div>
              
              {/* Task Columns */}
              <div className="flex-1 flex gap-2 p-3 overflow-hidden">
                <TaskColumn
                  title="Upcoming"
                  status={['pending']}
                  tasks={orderedTasks}
                  accentColor="bg-gray-500"
                  onTaskClick={handleTaskClick}
                />
                
                <TaskColumn
                  title="In Progress"
                  status={['in_progress']}
                  tasks={orderedTasks}
                  accentColor="bg-blue-500"
                  onTaskClick={handleTaskClick}
                />
                
                <TaskColumn
                  title="Completed"
                  status={['completed', 'failed', 'cancelled']}
                  tasks={orderedTasks}
                  accentColor="bg-green-500"
                  onTaskClick={handleTaskClick}
                />
              </div>
            </div>
          </ResizableBox>
        </motion.div>
      </div>
    </Draggable>
  );
};
