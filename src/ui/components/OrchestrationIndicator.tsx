import React, { useState, useEffect } from 'react';
import { Brain, Cpu, Database, Clock, Activity, Zap, Eye, EyeOff } from 'lucide-react';

interface OrchestrationIndicatorProps {
  currentModel: string;
  isActive: boolean;
  contextUsage?: {
    used: number;
    total: number;
  };
  thinkingBudget?: {
    used: number;
    total: number;
  };
  className?: string;
  showDetails?: boolean;
}

/**
 * OrchestrationIndicator Component
 * Shows real-time status of the current AI orchestrator
 */
export const OrchestrationIndicator: React.FC<OrchestrationIndicatorProps> = ({
  currentModel,
  isActive,
  contextUsage,
  thinkingBudget,
  className = '',
  showDetails = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);

  // Determine if current model is Gemini
  const isGeminiModel = currentModel.startsWith('gemini-') || currentModel.startsWith('models/gemini-');
  
  // Get model capabilities
  const getModelCapabilities = (modelId: string) => {
    const capabilities: Record<string, any> = {
      'models/gemini-2.5-pro': {
        name: 'Gemini 2.5 Pro',
        contextWindow: '1M+',
        maxTokens: 1048576,
        thinkingCapable: true,
        orchestrator: true,
        specialties: ['Complex reasoning', 'Large context', 'Advanced planning'],
        color: 'from-purple-600 to-indigo-600',
        icon: '🎭'
      },
      'models/gemini-2.5-flash': {
        name: 'Gemini 2.5 Flash',
        contextWindow: '1M+',
        maxTokens: 1048576,
        thinkingCapable: true,
        orchestrator: true,
        specialties: ['Balanced performance', 'Cost efficiency', 'Multi-modal'],
        color: 'from-purple-500 to-blue-600',
        icon: '⚡'
      },
      'models/gemini-2.5-flash-lite': {
        name: 'Gemini 2.5 Flash-Lite',
        contextWindow: '1M+',
        maxTokens: 1048576,
        thinkingCapable: true,
        orchestrator: true,
        specialties: ['High throughput', 'Low cost', 'Fast responses'],
        color: 'from-blue-500 to-purple-500',
        icon: '🏃'
      },
      'gpt-5': {
        name: 'GPT-5',
        contextWindow: '25K',
        maxTokens: 25000,
        thinkingCapable: false,
        orchestrator: false,
        specialties: ['Code generation', 'Complex reasoning', 'Multi-step tasks'],
        color: 'from-blue-600 to-blue-800',
        icon: '🧠'
      },
      'gpt-4o': {
        name: 'GPT-4o',
        contextWindow: '128K',
        maxTokens: 128000,
        thinkingCapable: false,
        orchestrator: false,
        specialties: ['General purpose', 'Balanced performance'],
        color: 'from-blue-500 to-blue-700',
        icon: '🚀'
      }
    };

    return capabilities[modelId] || {
      name: modelId,
      contextWindow: 'Unknown',
      maxTokens: 0,
      thinkingCapable: false,
      orchestrator: false,
      specialties: [],
      color: 'from-gray-500 to-gray-700',
      icon: '🤖'
    };
  };

  const modelInfo = getModelCapabilities(currentModel);

  // Animation for thinking/active state
  useEffect(() => {
    if (isActive && modelInfo.thinkingCapable) {
      const interval = setInterval(() => {
        setAnimationPhase((prev) => (prev + 1) % 4);
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isActive, modelInfo.thinkingCapable]);

  // Calculate context usage percentage
  const contextPercentage = contextUsage 
    ? Math.round((contextUsage.used / contextUsage.total) * 100)
    : 0;

  // Calculate thinking budget percentage
  const thinkingPercentage = thinkingBudget
    ? Math.round((thinkingBudget.used / thinkingBudget.total) * 100)
    : 0;

  // Get status indicator color
  const getStatusColor = () => {
    if (!isActive) return 'bg-gray-400';
    if (modelInfo.orchestrator) return 'bg-purple-500';
    return 'bg-blue-500';
  };

  // Format numbers with K/M suffixes
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className={`orchestration-indicator ${className}`}>
      {/* Compact Status Bar */}
      <div 
        className={`flex items-center gap-3 p-3 rounded-lg border transition-all duration-300 cursor-pointer ${
          modelInfo.orchestrator
            ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-700'
            : 'bg-gradient-to-r from-blue-50 to-blue-50 dark:from-blue-900/20 dark:to-blue-900/20 border-blue-200 dark:border-blue-700'
        } ${isExpanded ? 'rounded-b-none' : ''}`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Model Icon with Status */}
        <div className="relative">
          <div className={`w-10 h-10 bg-gradient-to-r ${modelInfo.color} rounded-lg flex items-center justify-center text-white text-lg font-bold shadow-sm`}>
            {modelInfo.icon}
          </div>
          
          {/* Active/Thinking Status Dot */}
          <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${getStatusColor()} ${
            isActive && modelInfo.thinkingCapable ? 'animate-pulse' : ''
          }`} />
        </div>

        {/* Model Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-semibold text-sm text-gray-800 dark:text-gray-200 truncate">
              {modelInfo.name}
            </h4>
            {modelInfo.orchestrator && (
              <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 text-xs font-medium rounded-full">
                Orchestrator
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-4 mt-1">
            <div className="flex items-center gap-1">
              <Database className="w-3 h-3 text-gray-500" />
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {modelInfo.contextWindow} context
              </span>
            </div>
            
            {isActive && (
              <div className="flex items-center gap-1">
                <Activity className="w-3 h-3 text-green-500" />
                <span className="text-xs text-green-600 dark:text-green-400">
                  Active
                </span>
              </div>
            )}
            
            {modelInfo.thinkingCapable && (
              <div className="flex items-center gap-1">
                <Brain className="w-3 h-3 text-purple-500" />
                <span className="text-xs text-purple-600 dark:text-purple-400">
                  Thinking
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Expand/Collapse Toggle */}
        {showDetails && (
          <button className="p-1 rounded hover:bg-black/5 dark:hover:bg-white/5 transition-colors">
            {isExpanded ? (
              <EyeOff className="w-4 h-4 text-gray-500" />
            ) : (
              <Eye className="w-4 h-4 text-gray-500" />
            )}
          </button>
        )}
      </div>

      {/* Expanded Details Panel */}
      {isExpanded && showDetails && (
        <div className={`border-t-0 border rounded-t-none rounded-b-lg p-4 space-y-4 ${
          modelInfo.orchestrator
            ? 'bg-white dark:bg-gray-800 border-purple-200 dark:border-purple-700'
            : 'bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-700'
        }`}>
          {/* Model Specialties */}
          <div>
            <h5 className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2">Specialties</h5>
            <div className="flex flex-wrap gap-2">
              {modelInfo.specialties.map((specialty: string, index: number) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>

          {/* Context Usage */}
          {contextUsage && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h5 className="text-xs font-semibold text-gray-700 dark:text-gray-300">Context Usage</h5>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {formatNumber(contextUsage.used)} / {formatNumber(contextUsage.total)} ({contextPercentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    contextPercentage > 80 ? 'bg-red-500' : 
                    contextPercentage > 60 ? 'bg-yellow-500' : 
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(contextPercentage, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Thinking Budget */}
          {thinkingBudget && modelInfo.thinkingCapable && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h5 className="text-xs font-semibold text-gray-700 dark:text-gray-300">Thinking Budget</h5>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {formatNumber(thinkingBudget.used)} / {formatNumber(thinkingBudget.total)} ({thinkingPercentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 bg-purple-500 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(thinkingPercentage, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Performance Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <Cpu className="w-5 h-5 text-gray-600 dark:text-gray-400 mx-auto mb-1" />
              <p className="text-xs font-medium text-gray-800 dark:text-gray-200">Performance</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {modelInfo.orchestrator ? 'Orchestrator' : 'Specialist'}
              </p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <Clock className="w-5 h-5 text-gray-600 dark:text-gray-400 mx-auto mb-1" />
              <p className="text-xs font-medium text-gray-800 dark:text-gray-200">Speed</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {currentModel.includes('flash') ? 'Fast' : 
                 currentModel.includes('lite') ? 'Very Fast' : 'Balanced'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrchestrationIndicator;