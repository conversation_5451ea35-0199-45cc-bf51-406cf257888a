import React, { useState, useEffect } from 'react';
import { Brain, Target, Lightbulb, Clock, Activity, BarChart3, TrendingUp, Cpu } from 'lucide-react';

interface ThinkingStep {
  id: string;
  title: string;
  content: string;
  type: 'analysis' | 'planning' | 'reasoning' | 'execution';
  status: 'pending' | 'active' | 'completed';
  confidence?: number;
  duration?: number;
  timestamp: number;
}

interface ThinkingDashboardProps {
  isVisible: boolean;
  currentModel: string;
  isStreaming: boolean;
  thinkingSteps: ThinkingStep[];
  currentStep?: string;
  overallProgress?: number;
  tokenUsage?: {
    thinking: number;
    output: number;
    total: number;
  };
  onClose: () => void;
  className?: string;
}

/**
 * ThinkingDashboard Component
 * Real-time visualization of the model's reasoning process during streaming
 */
export const ThinkingDashboard: React.FC<ThinkingDashboardProps> = ({
  isVisible,
  currentModel,
  isStreaming,
  thinkingSteps,
  currentStep,
  overallProgress = 0,
  tokenUsage,
  onClose,
  className = ''
}) => {
  const [animationFrame, setAnimationFrame] = useState(0);

  // Animation for active thinking
  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(() => {
        setAnimationFrame(prev => (prev + 1) % 4);
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  // Get icon for thinking step type
  const getStepIcon = (type: ThinkingStep['type'], status: ThinkingStep['status']) => {
    const iconClass = `w-5 h-5 ${
      status === 'completed' ? 'text-green-500' :
      status === 'active' ? 'text-blue-500' :
      'text-gray-400'
    }`;

    switch (type) {
      case 'analysis':
        return <Brain className={iconClass} />;
      case 'planning':
        return <Target className={iconClass} />;
      case 'reasoning':
        return <Lightbulb className={iconClass} />;
      case 'execution':
        return <Cpu className={iconClass} />;
      default:
        return <Clock className={iconClass} />;
    }
  };

  // Get status color
  const getStatusColor = (status: ThinkingStep['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-700';
      case 'active':
        return 'bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700';
    }
  };

  // Calculate thinking efficiency
  const thinkingEfficiency = tokenUsage && tokenUsage.thinking > 0
    ? Math.round((tokenUsage.output / tokenUsage.thinking) * 100)
    : 0;

  if (!isVisible) return null;

  return (
    <div className={`thinking-dashboard fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 ${className}`}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900 dark:text-white">
                🎭 Thinking Dashboard
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Live reasoning process for {currentModel}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {isStreaming && (
              <div className="flex items-center gap-2 px-3 py-1.5 bg-green-100 dark:bg-green-900/20 rounded-full border border-green-200 dark:border-green-700">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-xs font-medium text-green-700 dark:text-green-300">
                  Thinking Live
                </span>
              </div>
            )}

            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex h-[70vh]">
          {/* Thinking Steps Panel */}
          <div className="flex-1 p-6 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Thinking Process
                </h3>

                {/* Overall Progress */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Progress:</span>
                  <div className="w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-500"
                      style={{ width: `${overallProgress}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {Math.round(overallProgress)}%
                  </span>
                </div>
              </div>

              {/* Thinking Steps */}
              {thinkingSteps.map((step, index) => (
                <div key={step.id} className="relative">
                  {/* Connection Line */}
                  {index < thinkingSteps.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-gradient-to-b from-purple-300 to-transparent dark:from-purple-600" />
                  )}

                  <div className={`p-4 rounded-lg border transition-all duration-300 ${getStatusColor(step.status)}`}>
                    <div className="flex items-start gap-4">
                      {/* Step Icon */}
                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center border-2 ${
                        step.status === 'active' ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/30' :
                        step.status === 'completed' ? 'border-green-400 bg-green-50 dark:bg-green-900/30' :
                        'border-gray-300 bg-gray-50 dark:bg-gray-900/30'
                      }`}>
                        {getStepIcon(step.type, step.status)}
                      </div>

                      {/* Step Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {step.title}
                          </h4>

                          {step.status === 'active' && isStreaming && (
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-ping" />
                              <span className="text-xs text-blue-600 dark:text-blue-400">Active</span>
                            </div>
                          )}

                          {step.status === 'completed' && step.duration && (
                            <span className="text-xs text-gray-500">
                              {step.duration}ms
                            </span>
                          )}
                        </div>

                        <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                          {step.content}
                        </p>

                        {/* Confidence Indicator */}
                        {step.confidence !== undefined && (
                          <div className="flex items-center gap-2 mt-3">
                            <span className="text-xs text-gray-600 dark:text-gray-400">Confidence:</span>
                            <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className={`h-full transition-all duration-300 ${
                                  step.confidence >= 0.8 ? 'bg-green-500' :
                                  step.confidence >= 0.6 ? 'bg-yellow-500' :
                                  'bg-red-500'
                                }`}
                                style={{ width: `${step.confidence * 100}%` }}
                              />
                            </div>
                            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                              {Math.round(step.confidence * 100)}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Empty State */}
              {thinkingSteps.length === 0 && (
                <div className="text-center py-12">
                  <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Waiting for thinking process to begin...
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Analytics Panel */}
          <div className="w-80 p-6 bg-gray-50 dark:bg-gray-900/50 overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              Analytics
            </h3>

            {/* Token Usage */}
            {tokenUsage && (
              <div className="space-y-4 mb-6">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Token Usage</h4>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600 dark:text-gray-400">Thinking</span>
                      <span className="font-medium">{tokenUsage.thinking.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="h-2 bg-purple-500 rounded-full transition-all"
                        style={{ width: `${(tokenUsage.thinking / tokenUsage.total) * 100}%` }}
                      />
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600 dark:text-gray-400">Output</span>
                      <span className="font-medium">{tokenUsage.output.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="h-2 bg-blue-500 rounded-full transition-all"
                        style={{ width: `${(tokenUsage.output / tokenUsage.total) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Efficiency</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      {thinkingEfficiency}%
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Thinking Statistics */}
            <div className="space-y-4 mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Statistics</h4>

              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-1">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Steps</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {thinkingSteps.length}
                  </p>
                </div>

                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-1">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Avg Confidence</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {thinkingSteps.length > 0
                      ? Math.round(thinkingSteps.reduce((sum, step) => sum + (step.confidence || 0), 0) / thinkingSteps.length * 100)
                      : 0}%
                  </p>
                </div>

                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-1">
                    <Activity className="w-4 h-4 text-purple-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Active</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {thinkingSteps.filter(s => s.status === 'active').length}
                  </p>
                </div>

                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-1">
                    <BarChart3 className="w-4 h-4 text-yellow-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Complete</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {thinkingSteps.filter(s => s.status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>

            {/* Current Focus */}
            {currentStep && (
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Current Focus</h4>
                <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <Brain className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      Active Reasoning
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {currentStep}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThinkingDashboard;
