import React, { useState } from 'react';
import ModelSelector from './ModelSelector';
import DirectorySelector from './DirectorySelector';
import RateLimitStatus from './RateLimitStatus';
import { DeviceIndicator } from './DeviceIndicator';
import { OrchestrationIndicator } from './OrchestrationIndicator';
import { ModelInfo } from '@/types/models';
import { useLayoutStore } from '../stores/layoutStore';
import { useProjectStore } from '../stores/projectStore';
import { Grid3x3, Layout, Monitor, Folder, ChevronDown, Share, Brain, Activity } from 'lucide-react';

interface HeaderProps {
  onToggleSidebar: () => void;
  darkMode: boolean;
  onToggleDarkMode: () => void;
  selectedModel: ModelInfo;
  onModelChange: (model: ModelInfo) => void;
  onRateLimitChange?: (isLimited: boolean) => void;
  onShareSession?: () => void;
  currentSessionId?: string;
  isActiveProcessing?: boolean;
  orchestrationDetails?: {
    contextUsage?: { used: number; total: number };
    thinkingBudget?: { used: number; total: number };
  };
  activePlanSteps?: string[];
}

const Header: React.FC<HeaderProps> = ({ 
  onToggleSidebar, 
  darkMode, 
  onToggleDarkMode, 
  selectedModel, 
  onModelChange, 
  onRateLimitChange, 
  onShareSession, 
  currentSessionId,
  isActiveProcessing = false,
  orchestrationDetails,
  activePlanSteps = []
}) => {
  const { mode, setLayoutMode, togglePanel, panels } = useLayoutStore();
  const { currentProject } = useProjectStore();
  const [showDirectorySelector, setShowDirectorySelector] = useState(false);
  const [showOrchestratorDetails, setShowOrchestratorDetails] = useState(false);

  // Helper to determine if current model is Gemini
  // Normalize ids that may include an optional `models/` prefix (see src/types/models.getModelById)
  const normalizedModelId = selectedModel.id.replace(/^models\//, '');
  const isGeminiModel = normalizedModelId.startsWith('gemini-');

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onToggleSidebar}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div className="flex items-center space-x-4">
            {/* Main Title */}
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 bg-gradient-to-r ${
                isGeminiModel ? 'from-purple-500 to-blue-600' : 'from-blue-500 to-purple-600'
              } rounded-lg flex items-center justify-center`}>
                <span className="text-white font-bold text-sm">
                  {isGeminiModel ? '🎭' : 'D'}
                </span>
              </div>
              <h1 className="text-xl font-semibold text-gray-800 dark:text-white">
                Dante AI Assistant
              </h1>
            </div>

            {/* Orchestration Status Badge + Plan pill */}
            <div className="hidden lg:flex items-center">
              <div className={`px-3 py-1.5 rounded-full text-xs font-medium border ${
                isGeminiModel
                  ? 'bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700'
                  : 'bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900 dark:to-blue-950 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700'
              }`}>
                <div className="flex items-center gap-2">
                  <span className={`w-2 h-2 rounded-full ${
                    isActiveProcessing ? 'animate-pulse bg-green-400' : 'bg-gray-400'
                  }`} />
                  <span>
                    {isGeminiModel ? '🎭 Gemini Orchestrator' : '🤖 OpenAI Agent'}
                  </span>
                </div>
              </div>
              {activePlanSteps.length > 0 && (
                <div className="ml-2 px-3 py-1.5 rounded-full text-xs font-medium border bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700">
                  <div className="flex items-center gap-1">
                    <span className="w-2 h-2 rounded-full bg-amber-500" />
                    <span className="truncate max-w-[220px]" title={activePlanSteps.join(', ')}>
                      Plan: {activePlanSteps.join(', ')}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Orchestration Details Toggle */}
          {(isGeminiModel || isActiveProcessing) && (
            <button
              onClick={() => setShowOrchestratorDetails(!showOrchestratorDetails)}
              className={`p-2 rounded-lg transition-colors ${
                showOrchestratorDetails
                  ? 'bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}
              title="Toggle orchestration details"
            >
              <Brain className="w-5 h-5" />
            </button>
          )}

          {/* Device Indicator */}
          <DeviceIndicator
            sessionId={currentSessionId}
            className="hidden sm:block"
          />

          {/* Rate Limit Status */}
          <RateLimitStatus
            model={selectedModel.id}
            onRateLimitChange={onRateLimitChange}
          />

          {/* Resource Monitor Toggle */}
          <button
            onClick={() => togglePanel('resources')}
            className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${panels.resources.visible ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-300'}`}
            title={panels.resources.visible ? 'Hide Resource Monitor' : 'Show Resource Monitor'}
          >
            <Activity className="w-5 h-5" />
          </button>

          <ModelSelector
            selectedModel={selectedModel}
            onModelChange={onModelChange}
          />

          {/* Project Directory Selector */}
          <div className="relative">
            <button
              onClick={() => setShowDirectorySelector(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              title={currentProject ? `Current: ${currentProject.name}` : 'Select Project Directory'}
            >
              <Folder className="w-4 h-4" />
              <span className="text-sm max-w-32 truncate">
                {currentProject ? currentProject.name : 'Select Project'}
              </span>
              <ChevronDown className="w-3 h-3" />
            </button>
          </div>

          {/* Layout Mode Toggle */}
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setLayoutMode('classic')}
              className={`p-1.5 rounded ${mode === 'classic' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600'} transition-all`}
              title="Classic Layout"
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => setLayoutMode('orchestration')}
              className={`p-1.5 rounded ${mode === 'orchestration' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600'} transition-all`}
              title="Orchestration Layout"
            >
              <Grid3x3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setLayoutMode('compact')}
              className={`p-1.5 rounded ${mode === 'compact' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600'} transition-all`}
              title="Compact Layout"
            >
              <Layout className="w-4 h-4" />
            </button>
          </div>

          {/* Share Session Button */}
          {onShareSession && (
            <button
              onClick={onShareSession}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Share this conversation"
            >
              <Share className="w-5 h-5" />
            </button>
          )}

          <button
            onClick={onToggleDarkMode}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            {darkMode ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Directory Selector Modal */}
      <DirectorySelector
        isOpen={showDirectorySelector}
        onClose={() => setShowDirectorySelector(false)}
        onSelectDirectory={(path) => {
          console.log('Selected directory:', path);
        }}
      />

      {/* Orchestration Details Panel */}
      {showOrchestratorDetails && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <OrchestrationIndicator
            currentModel={selectedModel.id}
            isActive={isActiveProcessing}
            contextUsage={orchestrationDetails?.contextUsage}
            thinkingBudget={orchestrationDetails?.thinkingBudget}
            className="max-w-7xl mx-auto p-4"
            showDetails={true}
          />
        </div>
      )}
    </header>
  );
};

export default Header;
