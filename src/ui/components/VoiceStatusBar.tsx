import React from 'react';
import { Mic, Square, Pause, Alert<PERSON>riangle, MicIcon, Radio } from 'lucide-react';
import { useVoiceStore } from '../stores/voiceStore';

export const VoiceStatusBar: React.FC = () => {
  const { 
    enabled, 
    status, 
    stop, 
    interrupt, 
    start, 
    error, 
    mode,
    isRecording,
    startRecording,
    stopRecording,
    transcription 
  } = useVoiceStore();

  if (!enabled) return null;

  const isSpeaking = status === 'speaking';
  const isListening = status === 'listening' || status === 'connecting';
  const isChainedMode = mode === 'chained';
  
  const handlePushToTalk = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  return (
    <div className="px-4 py-2 bg-indigo-50 dark:bg-indigo-900/30 border-b border-indigo-200 dark:border-indigo-800 flex items-center justify-between text-indigo-700 dark:text-indigo-200">
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2">
          {isChainedMode ? <Radio size={16} /> : <Mic size={16} />}
          <span className="font-medium">Dante Speaks</span>
          <span className="text-xs px-2 py-0.5 rounded bg-indigo-100 dark:bg-indigo-800">
            {isChainedMode ? 'Chained' : 'Realtime'}
          </span>
        </div>
        <span className="text-xs opacity-75">
          {isRecording ? 'Recording…' : isListening ? 'Listening…' : isSpeaking ? 'Speaking…' : status}
        </span>
        {transcription && (
          <span className="text-xs bg-green-100 dark:bg-green-900 px-2 py-1 rounded max-w-xs truncate">
            "{transcription}"
          </span>
        )}
      </div>
      <div className="flex items-center gap-2">
        {isChainedMode && (
          <button
            onMouseDown={handlePushToTalk}
            onMouseUp={isRecording ? handlePushToTalk : undefined}
            onMouseLeave={isRecording ? handlePushToTalk : undefined}
            className={`px-2 py-1 text-xs rounded flex items-center gap-1 transition-colors ${
              isRecording 
                ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800' 
                : 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800'
            }`}
            title={isRecording ? "Release to send" : "Hold to record"}
          >
            <MicIcon size={12} className={isRecording ? 'animate-pulse' : ''} />
            {isRecording ? 'Recording' : 'Push to Talk'}
          </button>
        )}
        <button
          onClick={interrupt}
          className="px-2 py-1 text-xs rounded bg-indigo-100 dark:bg-indigo-800 hover:bg-indigo-200 dark:hover:bg-indigo-700 flex items-center gap-1"
          title="Interrupt"
        >
          <Pause size={12} /> Stop voice
        </button>
        <button
          onClick={stop}
          className="px-2 py-1 text-xs rounded bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-700 dark:text-red-200 flex items-center gap-1"
          title="End Dante Speaks"
        >
          <Square size={12} /> End
        </button>
      </div>
      {status === 'error' && (
        <div className="absolute left-1/2 -translate-x-1/2 top-full mt-1 w-[90%] max-w-2xl">
          <div className="flex items-center gap-2 px-3 py-2 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded">
            <AlertTriangle size={14} />
            <div className="text-xs truncate">
              <div>{error || 'Voice encountered an error. Please allow microphone permissions and try again.'}</div>
              {!window.isSecureContext && !/^(localhost|127\.0\.0\.1)$/.test(window.location.hostname) && (
                <div className="mt-1">
                  Tip: Microphone requires a secure context. Use https:// or open on http://localhost.
                </div>
              )}
            </div>
            <button
              onClick={() => start().catch(() => {})}
              className="ml-auto px-2 py-1 text-xs rounded bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceStatusBar;
