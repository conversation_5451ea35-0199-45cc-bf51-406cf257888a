import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronRight, Brain, Lightbulb, Target, Clock } from 'lucide-react';

interface ThinkingStep {
  id: string;
  content: string;
  type: 'analysis' | 'reasoning' | 'planning' | 'conclusion';
  timestamp?: number;
  confidence?: number;
}

interface ThinkingVisualizationProps {
  content: string;
  isStreaming?: boolean;
  onThinkingComplete?: (steps: ThinkingStep[]) => void;
  className?: string;
}

// Configuration constants
const AUTO_EXPAND_STEP_THRESHOLD = 3;

/**
 * ThinkingVisualization Component
 * Parses and displays the model's reasoning/thinking process from <Thinking></Thinking> tags or detected structure
 */
export const ThinkingVisualization: React.FC<ThinkingVisualizationProps> = ({
  content,
  isStreaming = false,
  onThinkingComplete,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [thinkingSteps, setThinkingSteps] = useState<ThinkingStep[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Parse thinking content from Gemini response
  const parseThinkingContent = (rawContent: string): ThinkingStep[] => {
    // First try to find explicit thought tags (Gemini format)
    const thoughtMatch = rawContent.match(/<[Tt]hought>(.*?)<\/[Tt]hought>/s);
    let thinkingText = '';

    if (thoughtMatch) {
      thinkingText = thoughtMatch[1].trim();
    } else {
      // For Gemini via OpenAI endpoint, look for structured reasoning patterns
      // Detect step-by-step reasoning, bullet points, or numbered lists
      const hasStructuredReasoning =
        rawContent.match(/(\d+\.\s+|\*\s+|-\s+|Step \d+|First,|Then,|Next,|Finally,)/i) ||
        rawContent.match(/(Here's.*process|reasoning|thinking|analysis|approach)/i) ||
        rawContent.match(/(Let me think|I need to|To solve this|My approach)/i);

      if (hasStructuredReasoning) {
        // Extract structured sections as "thinking"
        const lines = rawContent.split('\n');
        const structuredLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed.match(/(\d+\.\s+|\*\s+|-\s+|•\s+)/) ||
                 trimmed.match(/^(Step|First|Then|Next|Finally|Analysis|Reasoning)/i) ||
                 (trimmed.length > 20 && trimmed.includes(':'));
        });

        if (structuredLines.length >= 2) {
          thinkingText = structuredLines.join('\n');
        }
      }
    }

    if (!thinkingText) return [];

    const steps: ThinkingStep[] = [];

    // Split thinking content into logical steps
    const lines = thinkingText.split('\n').filter(line => line.trim());
    let currentStep: Partial<ThinkingStep> = {};
    let stepCounter = 0;

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Detect step markers or significant content changes
      const isNewStep = trimmedLine.match(/^(\d+\.|Step \d+|First,|Then,|Next,|Finally,|Analysis:|Planning:|Reasoning:|Conclusion:)/i) ||
                       (trimmedLine.length > 50 && index > 0);

      if (isNewStep || index === lines.length - 1) {
        // Save previous step if it exists
        if (currentStep.content && currentStep.content.trim()) {
          steps.push({
            id: `step-${stepCounter}`,
            content: currentStep.content.trim(),
            type: currentStep.type || 'reasoning',
            timestamp: Date.now() + stepCounter * 100
          });
          stepCounter++;
        }

        // Start new step
        currentStep = {
          content: trimmedLine,
          type: determineStepType(trimmedLine)
        };
      } else {
        // Append to current step
        currentStep.content = (currentStep.content || '') + '\n' + trimmedLine;
      }
    });

    // Add final step if not already added
    if (currentStep.content && currentStep.content.trim()) {
      steps.push({
        id: `step-${stepCounter}`,
        content: currentStep.content.trim(),
        type: currentStep.type || 'reasoning',
        timestamp: Date.now() + stepCounter * 100
      });
    }

    return steps;
  };

  // Determine the type of thinking step based on content
  const determineStepType = (content: string): ThinkingStep['type'] => {
    const lowerContent = content.toLowerCase();

    if (lowerContent.includes('analysis') || lowerContent.includes('analyze')) {
      return 'analysis';
    } else if (lowerContent.includes('plan') || lowerContent.includes('approach') || lowerContent.includes('strategy')) {
      return 'planning';
    } else if (lowerContent.includes('conclusion') || lowerContent.includes('therefore') || lowerContent.includes('result')) {
      return 'conclusion';
    }

    return 'reasoning';
  };

  // Parse thinking content when content changes
  useEffect(() => {
    if (content) {
      setIsProcessing(true);
      const steps = parseThinkingContent(content);

      if (steps.length > 0) {
        setThinkingSteps(steps);
        onThinkingComplete?.(steps);

        // Auto-expand only once when first receiving significant thinking content
        // Only expand if we haven't seen thinking steps before (first time)
        if (!isExpanded && steps.length >= AUTO_EXPAND_STEP_THRESHOLD && thinkingSteps.length === 0) {
          setIsExpanded(true);
        }
      }

      setIsProcessing(false);
    }
  }, [content, onThinkingComplete]);

  // Get icon for step type
  const getStepIcon = (type: ThinkingStep['type']) => {
    switch (type) {
      case 'analysis':
        return <Brain className="w-4 h-4 text-purple-500" />;
      case 'planning':
        return <Target className="w-4 h-4 text-blue-500" />;
      case 'conclusion':
        return <Lightbulb className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  // Get step type label
  const getStepTypeLabel = (type: ThinkingStep['type']) => {
    switch (type) {
      case 'analysis':
        return 'Analysis';
      case 'planning':
        return 'Planning';
      case 'conclusion':
        return 'Conclusion';
      default:
        return 'Reasoning';
    }
  };

  // Get confidence color
  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'bg-gray-300';
    if (confidence >= 0.8) return 'bg-green-400';
    if (confidence >= 0.6) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  if (thinkingSteps.length === 0 && !isProcessing) {
    return null;
  }

  return (
    <div className={`thinking-visualization border border-purple-200 dark:border-purple-700 rounded-lg bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 ${className}`}>
      {/* Thinking Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-purple-100/50 dark:hover:bg-purple-800/30 transition-colors rounded-t-lg"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Brain className="w-4 h-4 text-white" />
          </div>
          <div>
            <h4 className="font-semibold text-purple-700 dark:text-purple-300">
              🎭 Reasoning Process
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {thinkingSteps.length} structured reasoning step{thinkingSteps.length !== 1 ? 's' : ''} detected
              {isStreaming && <span className="ml-2 text-blue-500">● Live</span>}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {isProcessing && (
            <div className="w-4 h-4 border-2 border-purple-300 border-t-purple-600 rounded-full animate-spin" />
          )}
          {isExpanded ? (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-500" />
          )}
        </div>
      </div>

      {/* Thinking Content */}
      {isExpanded && (
        <div className="border-t border-purple-200 dark:border-purple-700">
          <div className="p-4 space-y-4">
            {thinkingSteps.map((step, index) => (
              <div key={step.id} className="relative">
                {/* Step connector line */}
                {index < thinkingSteps.length - 1 && (
                  <div className="absolute left-4 top-8 w-0.5 h-full bg-gradient-to-b from-purple-300 to-transparent" />
                )}

                <div className="flex items-start gap-3">
                  {/* Step icon */}
                  <div className="w-8 h-8 bg-white dark:bg-gray-800 border-2 border-purple-300 dark:border-purple-600 rounded-full flex items-center justify-center relative z-10">
                    {getStepIcon(step.type)}
                  </div>

                  {/* Step content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-xs font-medium px-2 py-1 bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 rounded-full">
                        {getStepTypeLabel(step.type)}
                      </span>
                      {step.confidence && (
                        <div className="flex items-center gap-1">
                          <div className="w-12 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${getConfidenceColor(step.confidence)} transition-all duration-300`}
                              style={{ width: `${step.confidence * 100}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500">
                            {Math.round(step.confidence * 100)}%
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed">
                        {step.content}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isStreaming && (
              <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                Thinking in progress...
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to extract thinking content from message
export const extractThinkingContent = (content: string): { thinking: string; response: string } => {
  // Check for Gemini's thought tags first
  const thoughtMatch = content.match(/<[Tt]hought>(.*?)<\/[Tt]hought>/s);

  if (thoughtMatch) {
    const thinking = thoughtMatch[1].trim();
    const response = content.replace(/<[Tt]hought>.*?<\/[Tt]hought>/s, '').trim();
    return { thinking, response };
  }

  // For Gemini responses without explicit tags, be very conservative
  // Only extract thinking if we have clear indicators this is reasoning, not the main response
  const hasExplicitThinkingIndicators =
    content.match(/(Let me think through this|My approach|Here's my reasoning process|Analysis:.*Step)/i);

  // Don't extract "thinking" from regular numbered lists or structured responses
  // Only do it if there are explicit thinking/reasoning keywords
  if (!hasExplicitThinkingIndicators) {
    return { thinking: '', response: content };
  }

  // Very conservative extraction - only take content that's clearly labeled as thinking
  const lines = content.split('\n');
  const thinkingLines: string[] = [];
  const responseLines: string[] = [];

  let inThinkingSection = false;

  lines.forEach(line => {
    const trimmed = line.trim();

    // Only detect explicit thinking sections
    if (trimmed.match(/(Let me think|My reasoning|Analysis:|Here's my thought process)/i)) {
      inThinkingSection = true;
      thinkingLines.push(line);
    } else if (inThinkingSection && trimmed.match(/^(Step \d+|First|Then|Next)/i)) {
      thinkingLines.push(line);
    } else if (trimmed.match(/^(Based on|In summary|Here are|The following)/i)) {
      // Transition to main response
      inThinkingSection = false;
      responseLines.push(line);
    } else if (inThinkingSection) {
      thinkingLines.push(line);
    } else {
      responseLines.push(line);
    }
  });

  // Only return thinking if we have a reasonable amount AND a separate response
  if (thinkingLines.length >= 3 && responseLines.length >= 3) {
    const thinking = thinkingLines.join('\n').trim();
    const response = responseLines.join('\n').trim();
    return { thinking, response };
  }

  return { thinking: '', response: content };
};

// Helper function to check if content has thinking tags or structured reasoning
export const hasThinkingContent = (content: string): boolean => {
  // Check for Gemini's thought tags first
  if (/<[Tt]hought>.*?<\/[Tt]hought>/s.test(content)) {
    return true;
  }

  // Check for explicit thinking language only - be very conservative
  const hasExplicitThinking =
    content.match(/(Let me think through this|My reasoning process|Analysis:.*Step|Here's my thought process)/i);

  return !!hasExplicitThinking;
};

export default ThinkingVisualization;
