import React, { useState, useRef, useEffect } from 'react';
import { ModelInfo, AVAILABLE_MODELS } from '../../types/models';

interface ModelSelectorProps {
  selectedModel: ModelInfo;
  onModelChange: (model: ModelInfo) => void;
  className?: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'below' | 'above'>('below');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - buttonRect.bottom;
      const spaceAbove = buttonRect.top;
      const dropdownHeight = 400; // Approximate height of dropdown
      
      // Determine if dropdown should open above or below
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPosition('above');
      } else {
        setDropdownPosition('below');
      }
    }
  }, [isOpen]);

  const getSpeedColor = (speed: string) => {
    switch (speed) {
      case 'fast': return 'text-green-600 dark:text-green-400';
      case 'balanced': return 'text-yellow-600 dark:text-yellow-400';
      case 'slow': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'high': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  // Helper to determine if model is Gemini
  const isGeminiModel = (modelId: string) => modelId.startsWith('gemini-') || modelId.startsWith('models/gemini-');
  
  // Helper to get model category badge
  const getModelBadge = (model: ModelInfo) => {
    if (isGeminiModel(model.id)) {
      return (
        <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 text-xs font-medium rounded-full">
          🎭 Orchestrator
        </span>
      );
    } else {
      return (
        <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-full">
          🤖 Specialist
        </span>
      );
    }
  };

  // Group models by category
  const groupedModels = AVAILABLE_MODELS.reduce((groups, model) => {
    const category = isGeminiModel(model.id) ? 'orchestrators' : 'specialists';
    if (!groups[category]) groups[category] = [];
    groups[category].push(model);
    return groups;
  }, {} as Record<string, ModelInfo[]>);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg 
                   bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600
                   hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors
                   focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
      >
        <span className="text-lg">{selectedModel.icon}</span>
        <div className="flex flex-col items-start">
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {selectedModel.name}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {selectedModel.contextWindow}
          </span>
        </div>
        <svg
          className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className={`absolute right-0 w-80 z-50 ${
          dropdownPosition === 'above' 
            ? 'bottom-full mb-1' 
            : 'top-full mt-1'
        }`}>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
                         rounded-lg shadow-lg overflow-hidden">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                Select AI Model
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Choose between orchestrators and specialists
              </p>
            </div>
            
            <div className="max-h-80 overflow-y-auto">
              {/* Orchestrators Section */}
              {groupedModels.orchestrators && (
                <div>
                  <div className="px-3 py-2 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700">
                    <h4 className="text-xs font-bold text-purple-700 dark:text-purple-300 uppercase tracking-wide">
                      <span role="img" aria-label="Orchestrator">🎭</span> Gemini Orchestrators
                    </h4>
                    <p className="text-xs text-purple-600 dark:text-purple-400 mt-0.5">
                      1M+ context • Advanced thinking • Complex coordination
                    </p>
                  </div>
                  {groupedModels.orchestrators.map((model) => (
                    <ModelOption 
                      key={model.id} 
                      model={model} 
                      isSelected={selectedModel.id === model.id}
                      onSelect={() => {
                        onModelChange(model);
                        setIsOpen(false);
                      }}
                      badge={getModelBadge(model)}
                      getSpeedColor={getSpeedColor}
                      getCostColor={getCostColor}
                    />
                  ))}
                </div>
              )}

              {/* Specialists Section */}
              {groupedModels.specialists && (
                <div>
                  <div className="px-3 py-2 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-700">
                    <h4 className="text-xs font-bold text-blue-700 dark:text-blue-300 uppercase tracking-wide">
                      🤖 OpenAI Specialists
                    </h4>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-0.5">
                      Domain experts • Optimized performance • Specialized tasks
                    </p>
                  </div>
                  {groupedModels.specialists.map((model) => (
                    <ModelOption 
                      key={model.id} 
                      model={model} 
                      isSelected={selectedModel.id === model.id}
                      onSelect={() => {
                        onModelChange(model);
                        setIsOpen(false);
                      }}
                      badge={getModelBadge(model)}
                      getSpeedColor={getSpeedColor}
                      getCostColor={getCostColor}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Extracted ModelOption component for cleaner code
const ModelOption: React.FC<{
  model: ModelInfo;
  isSelected: boolean;
  onSelect: () => void;
  badge: React.ReactNode;
  getSpeedColor: (speed: string) => string;
  getCostColor: (cost: string) => string;
}> = ({ model, isSelected, onSelect, badge, getSpeedColor, getCostColor }) => {
  const isGemini = model.id.startsWith('gemini-') || model.id.startsWith('models/gemini-');
  
  return (
    <button
      onClick={onSelect}
      className={`w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                 transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0
                 ${isSelected ? 
                   (isGemini 
                     ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800' 
                     : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                   ) : ''}`}
    >
      <div className="flex items-start space-x-3">
        <span className="text-xl mt-0.5">{model.icon}</span>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                {model.name}
              </h4>
              {isSelected && (
                <div className={`w-2 h-2 rounded-full ${
                  isGemini ? 'bg-purple-500' : 'bg-blue-500'
                }`} />
              )}
            </div>
            {badge}
          </div>
          
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
            {model.description}
          </p>
          
          <div className="flex items-center space-x-4 mt-2 text-xs">
            <span className="text-gray-500 dark:text-gray-400">
              {model.contextWindow}
            </span>
            <span className={`font-medium ${getSpeedColor(model.speed)}`}>
              {model.speed} speed
            </span>
            <span className={`font-medium ${getCostColor(model.cost)}`}>
              {model.cost} cost
            </span>
          </div>
          
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {model.capabilities}
          </p>
        </div>
      </div>
    </button>
  );
};

export default ModelSelector;