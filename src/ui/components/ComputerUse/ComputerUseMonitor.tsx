import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Monitor, AlertTriangle, CheckCircle, X, Play, Pause, RefreshCw, Clock } from 'lucide-react';

interface ComputerUseSession {
  id: string;
  environmentType: 'browser' | 'docker';
  isActive: boolean;
  currentUrl?: string;
  actionCount: number;
  safetyViolations: number;
  domains: string[];
  lastAction?: string;
  screenshot?: string;
}

interface ComputerUseTask {
  id: string;
  type: 'website_check' | 'playlist_play' | 'form_fill' | 'navigation' | 'data_extract';
  target: string;
  objective: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'ongoing';
  lastActivity?: string;
  result?: string;
  error?: string;
  checkpoints?: string[];
}

interface SafetyCheck {
  id: string;
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface ComputerUseMonitorProps {
  session?: ComputerUseSession;
  safetyChecks?: SafetyCheck[];
  onAcknowledgeChecks?: (checks: SafetyCheck[]) => void;
  onPauseSession?: () => void;
  onResumeSession?: () => void;
  onCloseSession?: () => void;
  className?: string;
  showIsolatedTasks?: boolean;
  refreshInterval?: number;
  expectedDomain?: string; // Optional expected domain for verification (e.g., "music.youtube.com")
}

export const ComputerUseMonitor: React.FC<ComputerUseMonitorProps> = ({
  session,
  safetyChecks = [],
  onAcknowledgeChecks,
  onPauseSession,
  onResumeSession,
  onCloseSession,
  className = '',
  showIsolatedTasks = true,
  refreshInterval = 5000,
  expectedDomain
}) => {
  const [showScreenshot, setShowScreenshot] = useState(true);
  const [selectedChecks, setSelectedChecks] = useState<Set<string>>(new Set());
  const [isolatedTasks, setIsolatedTasks] = useState<ComputerUseTask[]>([]);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [ackInFlight, setAckInFlight] = useState(false);

  // Fetch isolated tasks
  const fetchIsolatedTasks = async () => {
    if (!showIsolatedTasks) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/computer-use/tasks');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.activeTasks) {
          setIsolatedTasks(data.activeTasks);
        }
      }
    } catch (error) {
      console.error('Error fetching isolated tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const cancelTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/computer-use/tasks/${taskId}`, {
        method: 'DELETE'
      });
      if (response.ok) {
        await fetchIsolatedTasks(); // Refresh list
      }
    } catch (error) {
      console.error('Error canceling task:', error);
    }
  };

  const getTaskResult = async (taskId: string) => {
    try {
      const response = await fetch(`/api/computer-use/tasks/${taskId}/result`);
      if (response.ok) {
        const data = await response.json();
        // Update task with result
        setIsolatedTasks(prev => 
          prev.map(task => 
            task.id === taskId 
              ? { ...task, result: data.result, status: data.status }
              : task
          )
        );
      }
    } catch (error) {
      console.error('Error getting task result:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'ongoing': return <Monitor className="h-4 w-4 text-green-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <Monitor className="h-4 w-4 text-gray-400" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'website_check': return '🔍 Website Check';
      case 'playlist_play': return '🎵 Playlist Play';
      case 'form_fill': return '📝 Form Fill';
      case 'navigation': return '🧭 Navigation';
      case 'data_extract': return '📊 Data Extract';
      default: return '🤖 Computer Task';
    }
  };

  useEffect(() => {
    if (showIsolatedTasks) {
      fetchIsolatedTasks();
      const interval = setInterval(fetchIsolatedTasks, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [showIsolatedTasks, refreshInterval]);

  // Domain verification helpers
  const normalizeDomain = (d?: string) => {
    if (!d) return undefined;
    try {
      // Accept raw host or URL
      const host = d.includes('://') ? new URL(d).hostname : d;
      return host.replace(/^www\./i, '').toLowerCase();
    } catch { return d.replace(/^www\./i, '').toLowerCase(); }
  };
  const currentDomain = normalizeDomain(session?.currentUrl);
  const expected = normalizeDomain(expectedDomain);
  const domainMatches = expected ? (currentDomain === expected || (currentDomain?.endsWith('.' + expected))) : true;

  // If no session but showing isolated tasks, show task monitor
  if (!session && showIsolatedTasks) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-4 py-3 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Monitor className="h-5 w-5" />
              <h3 className="font-medium">Computer Use Tasks</h3>
              <span className="text-xs bg-white/20 px-2 py-1 rounded">
                {isolatedTasks.length} active
              </span>
            </div>
            
            <button
              onClick={fetchIsolatedTasks}
              className={`p-1 text-white/70 hover:text-white ${loading ? 'animate-spin' : ''}`}
              title="Refresh tasks"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Task List */}
        <div className="max-h-80 overflow-y-auto">
          {isolatedTasks.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Monitor className="mx-auto h-8 w-8 mb-2 text-gray-300" />
              <p>No active computer use tasks</p>
              <p className="text-xs mt-1">Tasks will appear here when browser automation is running</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {isolatedTasks.map((task) => (
                <div key={task.id} className="p-3 hover:bg-gray-50">
                  {/* Task Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      {getStatusIcon(task.status)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            {getTypeLabel(task.type)}
                          </span>
                          <span className="text-xs text-gray-500">
                            #{task.id.slice(-8)}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 truncate" title={task.target}>
                          {task.target}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => getTaskResult(task.id)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Get latest result"
                      >
                        <RefreshCw className="h-3 w-3" />
                      </button>
                      
                      <button
                        onClick={() => {
                          const newExpanded = new Set(expandedTasks);
                          if (newExpanded.has(task.id)) {
                            newExpanded.delete(task.id);
                          } else {
                            newExpanded.add(task.id);
                          }
                          setExpandedTasks(newExpanded);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Toggle details"
                      >
                        <Eye className="h-3 w-3" />
                      </button>
                      
                      {(task.status === 'running' || task.status === 'ongoing') && (
                        <button
                          onClick={() => cancelTask(task.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Cancel task"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Task Details (Expanded) */}
                  {expandedTasks.has(task.id) && (
                    <div className="mt-3 pt-3 border-t border-gray-200 space-y-2">
                      <div>
                        <span className="text-xs font-medium text-gray-500">Objective:</span>
                        <p className="text-xs text-gray-700 mt-1">{task.objective}</p>
                      </div>
                      
                      {task.result && (
                        <div>
                          <span className="text-xs font-medium text-gray-500">Latest Result:</span>
                          <p className="text-xs text-gray-700 mt-1 font-mono bg-gray-100 p-2 rounded max-h-20 overflow-y-auto">
                            {task.result}
                          </p>
                        </div>
                      )}
                      
                      {task.error && (
                        <div>
                          <span className="text-xs font-medium text-red-500">Error:</span>
                          <p className="text-xs text-red-700 mt-1 font-mono bg-red-50 p-2 rounded">
                            {task.error}
                          </p>
                        </div>
                      )}
                      
                      {task.checkpoints && task.checkpoints.length > 0 && (
                        <div>
                          <span className="text-xs font-medium text-gray-500">Checkpoints:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {task.checkpoints.map((checkpoint) => (
                              <span key={checkpoint} className="text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded">
                                {checkpoint}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {task.lastActivity && (
                        <div className="text-xs text-gray-500">
                          Last activity: {new Date(task.lastActivity).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <Monitor className="mx-auto h-8 w-8 mb-2" />
          <p>No computer use session active</p>
        </div>
      </div>
    );
  }

  const handleCheckToggle = (checkId: string) => {
    const newSelected = new Set(selectedChecks);
    if (newSelected.has(checkId)) {
      newSelected.delete(checkId);
    } else {
      newSelected.add(checkId);
    }
    setSelectedChecks(newSelected);
  };

  const handleAcknowledgeSelected = () => {
    if (selectedChecks.size === 0 || !session) return;

    const checksToAcknowledge = safetyChecks.filter(check => selectedChecks.has(check.id));

    if (onAcknowledgeChecks) {
      onAcknowledgeChecks(checksToAcknowledge);
      setSelectedChecks(new Set());
      return;
    }

    // Default behavior: call API to acknowledge and continue
    setAckInFlight(true);
    fetch(`/api/computer-use/sessions/${session.id}/acknowledge-safety`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ safetyChecks: checksToAcknowledge })
    })
      .then(async (res) => {
        if (!res.ok) throw new Error(`Ack failed (${res.status})`);
        const data = await res.json();
        console.log('✅ Acknowledged safety checks and continued:', data);
      })
      .catch(err => {
        console.error('❌ Failed to acknowledge safety checks:', err);
      })
      .finally(() => {
        setAckInFlight(false);
        setSelectedChecks(new Set());
      });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${session.isActive ? 'bg-green-400' : 'bg-gray-400'}`} />
            <h3 className="font-medium text-gray-900">
              Computer Use Session - {session.environmentType}
            </h3>
            <span className="text-sm text-gray-500">#{session.id.slice(-8)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {session.isActive ? (
              <button
                onClick={onPauseSession}
                className="p-1 text-gray-500 hover:text-gray-700"
                title="Pause session"
              >
                <Pause className="h-4 w-4" />
              </button>
            ) : (
              <button
                onClick={onResumeSession}
                className="p-1 text-gray-500 hover:text-gray-700"
                title="Resume session"
              >
                <Play className="h-4 w-4" />
              </button>
            )}
            
            <button
              onClick={() => setShowScreenshot(!showScreenshot)}
              className="p-1 text-gray-500 hover:text-gray-700"
              title={showScreenshot ? "Hide screenshot" : "Show screenshot"}
            >
              {showScreenshot ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
            
            <button
              onClick={onCloseSession}
              className="p-1 text-gray-500 hover:text-red-700"
              title="Close session"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Session Info */}
      <div className="px-4 py-3 space-y-2">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Current URL:</span>
            <div className="font-mono text-xs mt-1 p-2 bg-gray-50 rounded">
              {session.currentUrl || 'Not available'}
            </div>
          </div>
          <div>
            <span className="text-gray-500">Last Action:</span>
            <div className="font-mono text-xs mt-1 p-2 bg-gray-50 rounded">
              {session.lastAction || 'None'}
            </div>
          </div>
        </div>

        <div className="flex space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">Actions:</span>
            <span className="font-medium">{session.actionCount}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">Safety Issues:</span>
            <span className={`font-medium ${session.safetyViolations > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {session.safetyViolations}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">Domains:</span>
            <span className="font-medium">{session.domains.length}</span>
          </div>
        </div>

        {session.domains.length > 0 && (
          <div>
            <span className="text-gray-500 text-sm">Visited domains:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {session.domains.map((domain) => (
                <span key={domain} className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                  {domain}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Safety Checks */}
      {safetyChecks.length > 0 && (
        <div className="border-t border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <h4 className="font-medium text-gray-900 flex items-center">
                <AlertTriangle className="h-4 w-4 text-orange-500 mr-2" />
                Safety Checks Required
              </h4>
              {expected && (
                <div className={`text-xs px-2 py-1 rounded border ${domainMatches ? 'text-green-700 bg-green-50 border-green-200' : 'text-red-700 bg-red-50 border-red-200'}`}>
                  {domainMatches ? 'Domain OK' : 'Domain Mismatch'}
                  <span className="ml-2 text-gray-600">
                    expected: <span className="font-mono">{expected}</span>
                    {currentDomain ? (
                      <>
                        , current: <span className="font-mono">{currentDomain}</span>
                      </>
                    ) : ' (no current URL yet)'}
                  </span>
                </div>
              )}
            </div>
            {selectedChecks.size > 0 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleAcknowledgeSelected}
                  disabled={ackInFlight}
                  className={`px-3 py-1 text-sm rounded text-white ${ackInFlight ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                >
                  {ackInFlight ? 'Acknowledging…' : `Acknowledge Selected (${selectedChecks.size})`}
                </button>
                {selectedChecks.size !== safetyChecks.length && (
                  <button
                    onClick={() => setSelectedChecks(new Set(safetyChecks.map(c => c.id)))}
                    disabled={ackInFlight}
                    className="px-3 py-1 text-sm rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                    title="Select all safety checks"
                  >
                    Select All
                  </button>
                )}
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            {safetyChecks.map((check) => (
              <div
                key={check.id}
                className={`p-3 rounded-lg border ${getSeverityColor(check.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs font-semibold uppercase tracking-wide">
                        {check.severity}
                      </span>
                      <span className="text-xs text-gray-500">
                        {check.code}
                      </span>
                    </div>
                    <p className="mt-1 text-sm">{check.message}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedChecks.has(check.id)}
                    onChange={() => handleCheckToggle(check.id)}
                    className="ml-2"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Screenshot */}
      {showScreenshot && session.screenshot && (
        <div className="border-t border-gray-200">
          <div className="p-3">
            <h4 className="font-medium text-gray-900 mb-2">Current View</h4>
            <div className="bg-gray-100 rounded-lg p-2">
              <img
                src={`data:image/png;base64,${session.screenshot}`}
                alt="Computer Use Screenshot"
                className="w-full h-auto rounded border border-gray-300"
              />
            </div>
          </div>
        </div>
      )}

      {/* No Screenshot Available */}
      {showScreenshot && !session.screenshot && (
        <div className="border-t border-gray-200">
          <div className="p-6 text-center text-gray-500">
            <Monitor className="mx-auto h-8 w-8 mb-2" />
            <p>No screenshot available</p>
          </div>
        </div>
      )}

      {/* Isolated Tasks Section */}
      {showIsolatedTasks && isolatedTasks.length > 0 && (
        <div className="border-t border-gray-200">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 px-4 py-2 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Monitor className="h-4 w-4" />
                <span className="text-sm font-medium">Isolated Tasks ({isolatedTasks.length})</span>
              </div>
              
              <button
                onClick={fetchIsolatedTasks}
                className={`p-1 text-white/70 hover:text-white ${loading ? 'animate-spin' : ''}`}
                title="Refresh tasks"
              >
                <RefreshCw className="h-3 w-3" />
              </button>
            </div>
          </div>
          
          <div className="max-h-48 overflow-y-auto divide-y divide-gray-200">
            {isolatedTasks.map((task) => (
              <div key={task.id} className="p-3 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 flex-1">
                    {getStatusIcon(task.status)}
                    <div className="flex-1 min-w-0">
                      <span className="text-xs font-medium text-gray-900">
                        {getTypeLabel(task.type)}
                      </span>
                      <p className="text-xs text-gray-600 truncate">
                        {task.target}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => getTaskResult(task.id)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="Get result"
                    >
                      <RefreshCw className="h-3 w-3" />
                    </button>
                    
                    {(task.status === 'running' || task.status === 'ongoing') && (
                      <button
                        onClick={() => cancelTask(task.id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Cancel"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ComputerUseMonitor;
