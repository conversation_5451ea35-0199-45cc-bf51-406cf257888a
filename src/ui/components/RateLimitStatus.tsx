import React, { useState, useEffect, useRef } from 'react';

interface RateLimitInfo {
  model: string;
  isLimited: boolean;
  delaySeconds: number;
  requestsInLastMinute: number;
  tokenUsage: number;
}

interface RateLimitStatusProps {
  model: string;
  onRateLimitChange?: (isLimited: boolean) => void;
}

const RateLimitStatus: React.FC<RateLimitStatusProps> = ({ 
  model, 
  onRateLimitChange 
}) => {
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const initializedRef = useRef(false);

  useEffect(() => {
    const sleep = (ms: number) => new Promise(res => setTimeout(res, ms));

    const fetchWithBackoff = async (url: string, attempts = 5, initialDelay = 500, factor = 1.7) => {
      let delay = initialDelay;
      for (let i = 0; i < attempts; i++) {
        try {
          const res = await fetch(url);
          if (res.ok) return res;
        } catch {}
        await sleep(delay);
        delay = Math.min(Math.floor(delay * factor), 3000);
      }
      // Final attempt (let error surface to catch below)
      return await fetch(url);
    };

    const checkRateLimit = async () => {
      try {
        const response = initializedRef.current
          ? await fetch(`/api/rate-limit-status?model=${model}`)
          : await fetchWithBackoff(`/api/rate-limit-status?model=${model}`, 4, 600, 1.8);
        if (response.ok) {
          const info: RateLimitInfo = await response.json();
          setRateLimitInfo(info);
          setLastUpdated(new Date());
          
          // Notify parent of rate limit status change
          if (onRateLimitChange) {
            onRateLimitChange(info.isLimited);
          }
          initializedRef.current = true;
        }
      } catch (error) {
        console.warn('Failed to fetch rate limit status:', error);
      }
    };

    // Check immediately and then every 5 seconds
    checkRateLimit();
    const interval = setInterval(checkRateLimit, 5000);

    return () => clearInterval(interval);
  }, [model, onRateLimitChange]);

  if (!rateLimitInfo) {
    return null;
  }

  const { isLimited, delaySeconds, tokenUsage } = rateLimitInfo;

  if (!isLimited && tokenUsage < 15000) {
    return null; // Don't show anything when everything is normal
  }

  return (
    <div className={`px-3 py-2 rounded-lg text-sm font-medium ${
      isLimited 
        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' 
        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }`}>
      <div className="flex items-center gap-2">
        {isLimited ? (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 6.5c-.77.833-.192 2.5 1.732 2.5z" />
            </svg>
            <span>Rate Limited</span>
            <span className="text-xs opacity-75">
              ({Math.ceil(delaySeconds)}s remaining)
            </span>
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>High Usage</span>
            <span className="text-xs opacity-75">
              ({Math.round(tokenUsage / 1000)}k tokens/min)
            </span>
          </>
        )}
      </div>
      
      {isLimited && (
        <div className="mt-1 text-xs opacity-75">
          Requests are being queued to prevent API limits
        </div>
      )}
    </div>
  );
};

export default RateLimitStatus;
