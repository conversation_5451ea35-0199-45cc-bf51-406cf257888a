import React, { useState, useEffect } from 'react';
import { Server, Plug, Activity, Wrench, RefreshCw, Play, Square, AlertCircle, CheckCircle } from 'lucide-react';

interface MCPServerStatus {
  id: string;
  name: string;
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: string;
  lastError?: string;
  toolCount?: number;
  responseTime?: number;
}

interface MCPStatus {
  enabled: boolean;
  serverCount: number;
  connectedCount: number;
  servers: MCPServerStatus[];
  toolFactory: {
    totalTools: number;
    totalEntries: number;
    cacheHitRate: number;
  };
}

interface MCPTool {
  name: string;
  description: string;
}

export const MCPPanel: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ isOpen, onClose }) => {
  const [mcpStatus, setMcpStatus] = useState<MCPStatus | null>(null);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<'status' | 'servers' | 'tools'>('status');

  // Fetch MCP status
  const fetchMCPStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/mcp/status');
      if (!response.ok) throw new Error('Failed to fetch MCP status');
      const data = await response.json();
      setMcpStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MCP status');
    } finally {
      setLoading(false);
    }
  };

  // Fetch MCP tools
  const fetchMCPTools = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mcp/tools');
      if (!response.ok) throw new Error('Failed to fetch MCP tools');
      const data = await response.json();
      setTools(data.tools || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MCP tools');
    } finally {
      setLoading(false);
    }
  };

  // Connect to server
  const connectServer = async (serverId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mcp/servers/${serverId}/connect`, {
        method: 'POST'
      });
      if (!response.ok) throw new Error('Failed to connect to server');
      await fetchMCPStatus(); // Refresh status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  // Disconnect from server
  const disconnectServer = async (serverId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mcp/servers/${serverId}/disconnect`, {
        method: 'POST'
      });
      if (!response.ok) throw new Error('Failed to disconnect from server');
      await fetchMCPStatus(); // Refresh status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect from server');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchMCPStatus();
      if (selectedTab === 'tools') {
        fetchMCPTools();
      }
    }
  }, [isOpen, selectedTab]);

  if (!isOpen) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Square className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600 bg-green-50';
      case 'connecting': return 'text-blue-600 bg-blue-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-3/4 max-h-[600px] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Server className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              MCP (Model Context Protocol)
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>

        {/* Status Bar */}
        {mcpStatus && (
          <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${mcpStatus.enabled ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm font-medium">
                MCP {mcpStatus.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {mcpStatus.connectedCount}/{mcpStatus.serverCount} servers connected
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {mcpStatus.toolFactory.totalTools} tools available
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {['status', 'servers', 'tools'].map((tab) => (
            <button
              key={tab}
              onClick={() => setSelectedTab(tab as any)}
              className={`px-4 py-2 text-sm font-medium capitalize ${
                selectedTab === tab
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {loading && (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600 dark:text-gray-300">Loading...</span>
            </div>
          )}

          {/* Status Tab */}
          {selectedTab === 'status' && mcpStatus && !loading && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Server className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Servers</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {mcpStatus.serverCount}
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Plug className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Connected</span>
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {mcpStatus.connectedCount}
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Wrench className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Available Tools</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {mcpStatus.toolFactory.totalTools}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Cache Statistics</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Cache Entries:</span>
                    <span className="ml-2 font-medium">{mcpStatus.toolFactory.totalEntries}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Hit Rate:</span>
                    <span className="ml-2 font-medium">{mcpStatus.toolFactory.cacheHitRate.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Servers Tab */}
          {selectedTab === 'servers' && mcpStatus && !loading && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">MCP Servers</h3>
                <button
                  onClick={fetchMCPStatus}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </button>
              </div>

              {mcpStatus.servers.map((server) => (
                <div key={server.id} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(server.status)}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{server.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{server.id}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(server.status)}`}>
                        {server.status}
                      </span>
                      
                      {server.status === 'disconnected' && (
                        <button
                          onClick={() => connectServer(server.id)}
                          disabled={loading}
                          className="flex items-center gap-1 px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                        >
                          <Play className="w-3 h-3" />
                          Connect
                        </button>
                      )}
                      
                      {server.status === 'connected' && (
                        <button
                          onClick={() => disconnectServer(server.id)}
                          disabled={loading}
                          className="flex items-center gap-1 px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                        >
                          <Square className="w-3 h-3" />
                          Disconnect
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-600 dark:text-gray-400">
                    {server.toolCount !== undefined && (
                      <div>
                        <span className="font-medium">Tools:</span> {server.toolCount}
                      </div>
                    )}
                    {server.responseTime && (
                      <div>
                        <span className="font-medium">Response:</span> {server.responseTime}ms
                      </div>
                    )}
                    {server.lastConnected && (
                      <div>
                        <span className="font-medium">Last Connected:</span> {new Date(server.lastConnected).toLocaleTimeString()}
                      </div>
                    )}
                    {server.lastError && (
                      <div className="col-span-2 md:col-span-4">
                        <span className="font-medium text-red-500">Error:</span> {server.lastError}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {mcpStatus.servers.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <Server className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No MCP servers configured</p>
                  <p className="text-sm">Add servers via environment variables or API</p>
                </div>
              )}
            </div>
          )}

          {/* Tools Tab */}
          {selectedTab === 'tools' && !loading && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Available Tools</h3>
                <button
                  onClick={fetchMCPTools}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  <RefreshCw className="w-4 h-4" />
                  Discover
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tools.map((tool, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <Wrench className="w-4 h-4 text-blue-500 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white text-sm">{tool.name}</h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{tool.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {tools.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <Wrench className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>No tools discovered</p>
                  <p className="text-sm">Connect to MCP servers to discover tools</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <p className="text-xs text-gray-600 dark:text-gray-400">
            MCP enables dynamic tool discovery from external servers. 
            Connected servers provide additional capabilities to Dante.
          </p>
        </div>
      </div>
    </div>
  );
};