import { useState, useEffect } from 'react';
import { Smartphone, Monitor, Tablet, Users, Wifi } from 'lucide-react';
import { deviceSyncManager, DeviceInfo } from '../../utils/deviceSync';

interface DeviceIndicatorProps {
  sessionId?: string;
  className?: string;
}

export function DeviceIndicator({ sessionId, className = '' }: DeviceIndicatorProps) {
  const [connectedDevices, setConnectedDevices] = useState<DeviceInfo[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [showDeviceList, setShowDeviceList] = useState(false);

  useEffect(() => {
    if (!sessionId) return;

    const handleSyncEvent = () => {
      setConnectedDevices(deviceSyncManager.getConnectedDevices());
    };

    const handleConnection = () => {
      setIsConnected(true);
      setConnectedDevices(deviceSyncManager.getConnectedDevices());
    };

    const handleDisconnection = () => {
      setIsConnected(false);
      setConnectedDevices([]);
    };

    // Set up event listeners
    deviceSyncManager.addEventListener('device_connected', handleSyncEvent);
    deviceSyncManager.addEventListener('device_disconnected', handleSyncEvent);
    deviceSyncManager.addEventListener('connected', handleConnection);
    deviceSyncManager.addEventListener('*', handleSyncEvent);

    // Connect to sync manager
    deviceSyncManager.connect(sessionId).then(() => {
      setIsConnected(true);
      setConnectedDevices(deviceSyncManager.getConnectedDevices());
    }).catch(() => {
      setIsConnected(false);
    });

    return () => {
      deviceSyncManager.removeEventListener('device_connected', handleSyncEvent);
      deviceSyncManager.removeEventListener('device_disconnected', handleSyncEvent);
      deviceSyncManager.removeEventListener('connected', handleConnection);
      deviceSyncManager.removeEventListener('*', handleSyncEvent);
    };
  }, [sessionId]);

  const getDeviceIcon = (userAgent: string) => {
    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return <Smartphone className="w-4 h-4" />;
    } else if (userAgent.includes('iPad')) {
      return <Tablet className="w-4 h-4" />;
    } else {
      return <Monitor className="w-4 h-4" />;
    }
  };

  const currentDevice = deviceSyncManager.getCurrentDevice();
  const totalDevices = connectedDevices.length + 1; // +1 for current device

  if (!sessionId) return null;

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDeviceList(!showDeviceList)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          isConnected 
            ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/30'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
        }`}
        title={`${totalDevices} device(s) connected`}
      >
        <Wifi className={`w-4 h-4 ${isConnected ? 'text-green-500' : 'text-gray-400'}`} />
        <Users className="w-4 h-4" />
        <span className="text-sm font-medium">{totalDevices}</span>
      </button>

      {showDeviceList && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              Connected Devices
            </h3>
            
            {/* Current Device */}
            <div className="flex items-center space-x-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-2">
              {getDeviceIcon(currentDevice.userAgent)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100 truncate">
                  {currentDevice.name} <span className="text-xs">(You)</span>
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-300">
                  This device
                </p>
              </div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>

            {/* Other Devices */}
            {connectedDevices.length > 0 ? (
              <div className="space-y-2">
                {connectedDevices.map(device => (
                  <div key={device.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg">
                    {getDeviceIcon(device.userAgent)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {device.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {device.ipAddress && `${device.ipAddress} • `}
                        Active {Math.round((Date.now() - device.lastSeen) / 1000)}s ago
                      </p>
                    </div>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  No other devices connected
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  Share the session URL to connect more devices
                </p>
              </div>
            )}

            <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <Wifi className="w-3 h-3" />
                <span>
                  {isConnected ? 'Real-time sync active' : 'Sync disconnected'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {showDeviceList && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDeviceList(false)}
        />
      )}
    </div>
  );
}