import React from 'react';
import { Server } from 'lucide-react';

interface MCPToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  className?: string;
}

export const MCPToggle: React.FC<MCPToggleProps> = ({ enabled, onChange, className = '' }) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Server className={`w-4 h-4 ${enabled ? 'text-green-500' : 'text-gray-400'}`} />
      <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
        <input
          type="checkbox"
          checked={enabled}
          onChange={(e) => onChange(e.target.checked)}
          className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
        />
        <span>Enable MCP</span>
      </label>
    </div>
  );
};