import React, { useState, useEffect } from 'react';
import { 
  X, 
  Folder, 
  FolderOpen, 
  File, 
  ChevronRight, 
  ChevronDown, 
  RefreshCw,
  GitBranch,
  Package,
  Settings,
  Search,
  Star,
  Clock
} from 'lucide-react';
import { useProjectStore } from '../stores/projectStore';

interface ProjectFile {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: ProjectFile[];
  expanded?: boolean;
}

interface ProjectExplorerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProjectExplorer: React.FC<ProjectExplorerProps> = ({ isOpen, onClose }) => {
  const { currentProject, recentProjects, addRecentFile } = useProjectStore();
  const [projectFiles, setProjectFiles] = useState<ProjectFile[]>([]);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Load project files when component opens
  useEffect(() => {
    if (isOpen && currentProject) {
      loadProjectFiles();
    }
  }, [isOpen, currentProject]);

  const loadProjectFiles = async () => {
    if (!currentProject) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/list-directory', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          dirPath: currentProject.path,
          includeHidden: false 
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const files = result.files
            .filter((item: any) => item.name !== 'node_modules' && !item.name.startsWith('.'))
            .sort((a: any, b: any) => {
              // Directories first
              if (a.type !== b.type) {
                return a.type === 'directory' ? -1 : 1;
              }
              return a.name.localeCompare(b.name);
            })
            .map((item: any) => ({
              name: item.name,
              path: item.path,
              type: item.type,
              children: item.type === 'directory' ? [] : undefined,
              expanded: false,
            }));
          
          setProjectFiles(files);
        }
      }
    } catch (error) {
      console.error('Failed to load project files:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadDirectoryChildren = async (dirPath: string): Promise<ProjectFile[]> => {
    try {
      const response = await fetch('/api/list-directory', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          dirPath,
          includeHidden: false 
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          return result.files
            .filter((item: any) => item.name !== 'node_modules' && !item.name.startsWith('.'))
            .sort((a: any, b: any) => {
              if (a.type !== b.type) {
                return a.type === 'directory' ? -1 : 1;
              }
              return a.name.localeCompare(b.name);
            })
            .map((item: any) => ({
              name: item.name,
              path: item.path,
              type: item.type,
              children: item.type === 'directory' ? [] : undefined,
              expanded: false,
            }));
        }
      }
    } catch (error) {
      console.error('Failed to load directory:', error);
    }
    
    return [];
  };

  const toggleDirectory = async (path: string) => {
    const newExpanded = new Set(expandedPaths);
    
    if (expandedPaths.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
      
      // Load children if not already loaded
      const updateTree = async (items: ProjectFile[]): Promise<ProjectFile[]> => {
        return Promise.all(items.map(async item => {
          if (item.path === path && item.type === 'directory') {
            if (!item.children || item.children.length === 0) {
              const children = await loadDirectoryChildren(path);
              return { ...item, children, expanded: true };
            }
            return { ...item, expanded: true };
          }
          if (item.children) {
            return { ...item, children: await updateTree(item.children) };
          }
          return item;
        }));
      };
      
      const updatedFiles = await updateTree(projectFiles);
      setProjectFiles(updatedFiles);
    }
    
    setExpandedPaths(newExpanded);
  };

  const handleFileClick = (file: ProjectFile) => {
    if (file.type === 'file') {
      addRecentFile(file.path);
      // Could open file in editor or show preview
      console.log('File clicked:', file.path);
    }
  };

  const getFileIcon = (fileName: string, isDirectory: boolean) => {
    if (isDirectory) {
      return expandedPaths.has(fileName) ? 
        <FolderOpen className="w-4 h-4 text-blue-500" /> : 
        <Folder className="w-4 h-4 text-blue-500" />;
    }
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <File className="w-4 h-4 text-yellow-500" />;
      case 'json':
        return <File className="w-4 h-4 text-green-500" />;
      case 'md':
        return <File className="w-4 h-4 text-blue-500" />;
      case 'css':
      case 'scss':
        return <File className="w-4 h-4 text-pink-500" />;
      default:
        return <File className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderFileTree = (files: ProjectFile[], depth: number = 0) => {
    return files
      .filter(file => 
        searchQuery === '' || 
        file.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      .map(file => (
        <div key={file.path}>
          <div
            className="flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm"
            style={{ paddingLeft: `${depth * 16 + 8}px` }}
            onClick={() => {
              if (file.type === 'directory') {
                toggleDirectory(file.path);
              } else {
                handleFileClick(file);
              }
            }}
          >
            {file.type === 'directory' && (
              <button className="p-0.5 mr-1">
                {expandedPaths.has(file.path) ? (
                  <ChevronDown className="w-3 h-3 text-gray-400" />
                ) : (
                  <ChevronRight className="w-3 h-3 text-gray-400" />
                )}
              </button>
            )}
            
            <div className="flex items-center flex-1 min-w-0">
              {getFileIcon(file.path, file.type === 'directory')}
              <span className="ml-2 truncate text-gray-700 dark:text-gray-300">
                {file.name}
              </span>
            </div>
          </div>
          
          {file.type === 'directory' && 
           expandedPaths.has(file.path) && 
           file.children && 
           file.children.length > 0 && (
            <div>
              {renderFileTree(file.children, depth + 1)}
            </div>
          )}
        </div>
      ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-4/5 h-4/5 max-w-4xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <FolderOpen className="w-6 h-6 text-orange-500" />
            <div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                Project Explorer
              </h2>
              {currentProject && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentProject.name} • {currentProject.type}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadProjectFiles}
              disabled={isLoading}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Refresh"
            >
              <RefreshCw className={`w-5 h-5 text-gray-600 dark:text-gray-400 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Project Info Sidebar */}
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 flex flex-col">
            {/* Search */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 bg-gray-50 dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Project Details */}
            {currentProject && (
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-800 dark:text-white mb-2">
                  Project Details
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Folder className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400 truncate">
                      {currentProject.path}
                    </span>
                  </div>
                  
                  {currentProject.metadata.gitRepo && (
                    <div className="flex items-center gap-2">
                      <GitBranch className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">
                        Git Repository
                      </span>
                    </div>
                  )}
                  
                  {currentProject.metadata.dependencies && (
                    <div className="flex items-center gap-2">
                      <Package className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {currentProject.metadata.dependencies.length} dependencies
                      </span>
                    </div>
                  )}
                  
                  {currentProject.metadata.scripts && (
                    <div className="flex items-center gap-2">
                      <Settings className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {currentProject.metadata.scripts.length} scripts
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Recent Files */}
            {currentProject && currentProject.recentFiles.length > 0 && (
              <div className="p-3">
                <h3 className="font-medium text-gray-800 dark:text-white mb-2 flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Recent Files
                </h3>
                <div className="space-y-1">
                  {currentProject.recentFiles.slice(0, 10).map((filePath) => (
                    <div
                      key={filePath}
                      onClick={() => handleFileClick({ name: filePath.split('/').pop() || '', path: filePath, type: 'file' })}
                      className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <File className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                          {filePath.replace(currentProject.path, '').replace(/^\//, '')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* File Tree */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-y-auto">
              {!currentProject ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600 dark:text-gray-400">
                      No project selected
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-500">
                      Select a project directory to explore its files
                    </p>
                  </div>
                </div>
              ) : isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-gray-500 dark:text-gray-400">Loading files...</div>
                </div>
              ) : (
                <div className="py-2">
                  {renderFileTree(projectFiles)}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectExplorer;