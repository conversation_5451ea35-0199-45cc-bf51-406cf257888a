/**
 * Selector Discovery Utilities
 * 
 * Intelligent functions to discover and generate robust CSS selectors
 * for web elements, enabling autonomous navigation and interaction.
 */

export interface ElementSelector {
  selector: string;
  confidence: number; // 0-1, how confident we are this selector is unique and stable
  type: 'id' | 'class' | 'text' | 'attribute' | 'position' | 'combined';
  description: string;
}

export interface ElementDiscoveryResult {
  element: string; // Description of the element
  selectors: ElementSelector[];
  recommended: string; // The best selector to use
  alternates: string[]; // Backup selectors
}

/**
 * Generate multiple selector strategies for an element
 */
export function generateSelectors(elementInfo: {
  tagName: string;
  id?: string;
  className?: string;
  textContent?: string;
  attributes?: { [key: string]: string };
  parentTag?: string;
  index?: number;
}): ElementSelector[] {
  const selectors: ElementSelector[] = [];
  const { tagName, id, className, textContent, attributes, parentTag, index } = elementInfo;
  
  // ID selector (highest confidence)
  if (id && !id.includes(':') && !id.includes(' ')) {
    selectors.push({
      selector: `#${id}`,
      confidence: 0.95,
      type: 'id',
      description: 'ID selector (most reliable)'
    });
  }
  
  // Class selector
  if (className) {
    const classes = className.split(' ').filter(c => c && !c.includes(':'));
    if (classes.length > 0) {
      const classSelector = `.${classes.join('.')}`;
      selectors.push({
        selector: tagName + classSelector,
        confidence: 0.7,
        type: 'class',
        description: 'Class-based selector'
      });
    }
  }
  
  // Text content selector (for buttons, links)
  if (textContent && textContent.trim().length > 0 && textContent.length < 100) {
    const text = textContent.trim();
    
    // Exact text match
    selectors.push({
      selector: `${tagName}:has-text("${text}")`,
      confidence: 0.6,
      type: 'text',
      description: 'Text content selector'
    });
    
    // Partial text match for longer text
    if (text.length > 20) {
      const partialText = text.substring(0, 20);
      selectors.push({
        selector: `${tagName}:has-text("${partialText}")`,
        confidence: 0.5,
        type: 'text',
        description: 'Partial text selector'
      });
    }
  }
  
  // Attribute selectors
  if (attributes) {
    // Common attributes for selection
    const importantAttrs = ['data-testid', 'data-test', 'aria-label', 'name', 'type', 'role', 'href'];
    
    for (const attr of importantAttrs) {
      if (attributes[attr]) {
        selectors.push({
          selector: `${tagName}[${attr}="${attributes[attr]}"]`,
          confidence: attr.startsWith('data-test') ? 0.85 : 0.65,
          type: 'attribute',
          description: `${attr} attribute selector`
        });
      }
    }
  }
  
  // Position-based selector (lower confidence)
  if (parentTag && index !== undefined) {
    selectors.push({
      selector: `${parentTag} > ${tagName}:nth-of-type(${index + 1})`,
      confidence: 0.4,
      type: 'position',
      description: 'Position-based selector'
    });
  }
  
  // Combined selectors for better specificity
  if (className && textContent) {
    const classes = className.split(' ').filter(c => c && !c.includes(':')).slice(0, 2);
    if (classes.length > 0) {
      selectors.push({
        selector: `${tagName}.${classes.join('.')}:has-text("${textContent.substring(0, 30)}")`,
        confidence: 0.75,
        type: 'combined',
        description: 'Combined class and text selector'
      });
    }
  }
  
  // Sort by confidence
  return selectors.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Find elements by text content with fuzzy matching
 */
export function createTextSearchSelectors(searchText: string, options: {
  exactMatch?: boolean;
  caseSensitive?: boolean;
  elementTypes?: string[];
} = {}): string[] {
  const { exactMatch = false, caseSensitive = false, elementTypes = ['button', 'a', 'span', 'div', 'p', 'h1', 'h2', 'h3', 'label'] } = options;
  
  const selectors: string[] = [];
  const text = caseSensitive ? searchText : searchText.toLowerCase();
  
  for (const tag of elementTypes) {
    if (exactMatch) {
      // Exact text match
      selectors.push(`${tag}:has-text("${text}")`);
      
      // For buttons and links, also check aria-label
      if (tag === 'button' || tag === 'a') {
        selectors.push(`${tag}[aria-label="${text}"]`);
      }
    } else {
      // Partial text match
      selectors.push(`${tag}:has-text("${text}")`);
      
      // Contains text (more flexible)
      selectors.push(`${tag}:text-matches("${text}", "${caseSensitive ? '' : 'i'}")`);
    }
  }
  
  // Add role-based selectors for buttons
  if (elementTypes.includes('button')) {
    selectors.push(`[role="button"]:has-text("${text}")`);
  }
  
  return selectors;
}

/**
 * Find elements by their role in the page
 */
export function createRoleBasedSelectors(role: 'navigation' | 'form' | 'button' | 'link' | 'input' | 'heading'): string[] {
  const roleMap: { [key: string]: string[] } = {
    navigation: [
      'nav',
      '[role="navigation"]',
      '.navigation',
      '.nav',
      '#navigation',
      '#nav'
    ],
    form: [
      'form',
      '[role="form"]',
      '.form',
      '#form'
    ],
    button: [
      'button',
      'input[type="button"]',
      'input[type="submit"]',
      '[role="button"]',
      '.btn',
      '.button'
    ],
    link: [
      'a[href]',
      '[role="link"]',
      '.link'
    ],
    input: [
      'input:not([type="button"]):not([type="submit"])',
      'textarea',
      'select',
      '[role="textbox"]',
      '[role="combobox"]'
    ],
    heading: [
      'h1',
      'h2', 
      'h3',
      'h4',
      'h5',
      'h6',
      '[role="heading"]'
    ]
  };
  
  return roleMap[role] || [];
}

/**
 * Generate selectors for finding elements near another element
 */
export function createProximitySelectors(anchorSelector: string, targetType: string, position: 'before' | 'after' | 'parent' | 'child' | 'sibling'): string[] {
  const selectors: string[] = [];
  
  switch (position) {
    case 'before':
      selectors.push(`${anchorSelector} ~ ${targetType}`);
      selectors.push(`${anchorSelector} + ${targetType}`);
      break;
      
    case 'after':
      // CSS doesn't have a previous sibling selector, need to use JavaScript
      selectors.push(`${targetType}:has(~ ${anchorSelector})`);
      break;
      
    case 'parent':
      selectors.push(`${anchorSelector} ${targetType}`);
      selectors.push(`${anchorSelector} > ${targetType}`);
      break;
      
    case 'child':
      selectors.push(`${targetType}:has(${anchorSelector})`);
      selectors.push(`${targetType}:has(> ${anchorSelector})`);
      break;
      
    case 'sibling':
      selectors.push(`${anchorSelector} ~ ${targetType}`);
      selectors.push(`${targetType} ~ ${anchorSelector}`);
      break;
  }
  
  return selectors;
}

/**
 * Find form field selectors by label text
 */
export function createFormFieldSelectors(labelText: string): string[] {
  const selectors: string[] = [];
  
  // Direct label association
  selectors.push(`label:has-text("${labelText}") + input`);
  selectors.push(`label:has-text("${labelText}") + textarea`);
  selectors.push(`label:has-text("${labelText}") + select`);
  
  // Label with for attribute
  selectors.push(`input[id="${labelText.toLowerCase().replace(/\s+/g, '-')}"]`);
  selectors.push(`input[name="${labelText.toLowerCase().replace(/\s+/g, '_')}"]`);
  
  // Placeholder text
  selectors.push(`input[placeholder*="${labelText}"]`);
  selectors.push(`textarea[placeholder*="${labelText}"]`);
  
  // Aria-label
  selectors.push(`input[aria-label*="${labelText}"]`);
  selectors.push(`textarea[aria-label*="${labelText}"]`);
  selectors.push(`select[aria-label*="${labelText}"]`);
  
  return selectors;
}

/**
 * Validate if a selector is likely to be stable across page loads
 */
export function assessSelectorStability(selector: string): {
  isStable: boolean;
  reason: string;
  score: number; // 0-1
} {
  let score = 0.5; // Start neutral
  const reasons: string[] = [];
  
  // ID selectors are most stable
  if (selector.startsWith('#')) {
    score += 0.3;
    reasons.push('ID selector (stable)');
  }
  
  // Data-testid attributes are very stable
  if (selector.includes('data-testid') || selector.includes('data-test')) {
    score += 0.4;
    reasons.push('Test ID attribute (very stable)');
  }
  
  // Class names can be stable
  if (selector.includes('.') && !selector.includes(':nth')) {
    score += 0.1;
    reasons.push('Class selector (moderately stable)');
  }
  
  // Position-based selectors are less stable
  if (selector.includes(':nth') || selector.includes(':first') || selector.includes(':last')) {
    score -= 0.3;
    reasons.push('Position-based (less stable)');
  }
  
  // Text-based selectors depend on content
  if (selector.includes(':has-text') || selector.includes(':text')) {
    score -= 0.1;
    reasons.push('Text-based (content dependent)');
  }
  
  // Generated or dynamic-looking IDs
  if (selector.match(/#[a-f0-9]{8,}|#\d{10,}/)) {
    score -= 0.4;
    reasons.push('Generated ID (unstable)');
  }
  
  // Clamp score between 0 and 1
  score = Math.max(0, Math.min(1, score));
  
  return {
    isStable: score >= 0.5,
    reason: reasons.join(', ') || 'No specific indicators',
    score
  };
}

/**
 * Create a robust selector strategy with fallbacks
 */
export function createSelectorStrategy(elementDescription: string, context?: {
  pageType?: 'spa' | 'static' | 'dynamic';
  framework?: 'react' | 'vue' | 'angular' | 'vanilla';
}): {
  primary: string;
  fallbacks: string[];
  strategy: string;
} {
  const strategies: { primary: string; fallbacks: string[]; strategy: string }[] = [];
  
  // Parse element description for clues
  const descLower = elementDescription.toLowerCase();
  
  if (descLower.includes('button') || descLower.includes('click')) {
    strategies.push({
      primary: 'button',
      fallbacks: [
        '[role="button"]',
        'input[type="button"]',
        'input[type="submit"]',
        '.btn',
        '.button'
      ],
      strategy: 'Button element search'
    });
  }
  
  if (descLower.includes('link') || descLower.includes('navigate')) {
    strategies.push({
      primary: 'a[href]',
      fallbacks: [
        '[role="link"]',
        '.link',
        'span[onclick]'
      ],
      strategy: 'Link element search'
    });
  }
  
  if (descLower.includes('input') || descLower.includes('field') || descLower.includes('text')) {
    strategies.push({
      primary: 'input[type="text"]',
      fallbacks: [
        'input:not([type="hidden"])',
        'textarea',
        '[role="textbox"]',
        '[contenteditable="true"]'
      ],
      strategy: 'Input field search'
    });
  }
  
  if (descLower.includes('form') || descLower.includes('submit')) {
    strategies.push({
      primary: 'form',
      fallbacks: [
        '[role="form"]',
        '.form',
        'div[class*="form"]'
      ],
      strategy: 'Form element search'
    });
  }
  
  // Default strategy if no specific match
  if (strategies.length === 0) {
    strategies.push({
      primary: '*',
      fallbacks: [
        'div',
        'span',
        'section',
        'article'
      ],
      strategy: 'Generic element search'
    });
  }
  
  // Adjust for framework-specific patterns
  if (context?.framework === 'react') {
    strategies[0].fallbacks.push('[data-reactid]', '[data-react-component]');
  } else if (context?.framework === 'vue') {
    strategies[0].fallbacks.push('[v-on]', '[v-bind]');
  } else if (context?.framework === 'angular') {
    strategies[0].fallbacks.push('[ng-click]', '[ng-model]');
  }
  
  return strategies[0];
}

/**
 * Main discovery function that combines all strategies
 */
export function discoverElement(description: string, options: {
  preferredStrategy?: 'text' | 'structure' | 'position';
  context?: any;
} = {}): ElementDiscoveryResult {
  const { preferredStrategy = 'text' } = options;
  
  // Generate multiple selector options
  const selectors: ElementSelector[] = [];
  
  // Text-based search
  if (preferredStrategy === 'text' || !preferredStrategy) {
    const textSelectors = createTextSearchSelectors(description, { exactMatch: false });
    textSelectors.forEach(sel => {
      selectors.push({
        selector: sel,
        confidence: 0.6,
        type: 'text',
        description: 'Text-based search'
      });
    });
  }
  
  // Structure-based search
  const strategy = createSelectorStrategy(description, options.context);
  selectors.push({
    selector: strategy.primary,
    confidence: 0.7,
    type: 'combined',
    description: strategy.strategy
  });
  
  strategy.fallbacks.forEach((fallback, index) => {
    selectors.push({
      selector: fallback,
      confidence: 0.5 - (index * 0.05),
      type: 'combined',
      description: `Fallback: ${strategy.strategy}`
    });
  });
  
  // Sort by confidence
  const sortedSelectors = selectors.sort((a, b) => b.confidence - a.confidence);
  
  return {
    element: description,
    selectors: sortedSelectors,
    recommended: sortedSelectors[0]?.selector || '*',
    alternates: sortedSelectors.slice(1, 4).map(s => s.selector)
  };
}