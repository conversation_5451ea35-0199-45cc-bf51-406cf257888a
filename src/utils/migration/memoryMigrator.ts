#!/usr/bin/env bun

import { memoryManager } from '../../memory/MemoryManager';
import { QdrantMemoryStore } from '../../memory/stores/QdrantMemoryStore';
import { FileMemoryStore } from '../../memory/stores/FileMemoryStore';
import { VectorMemoryStore } from '../../memory/stores/VectorMemoryStore';
import { Memory } from '../../memory/types';
import chalk from 'chalk';
import ora, { type Ora } from 'ora';
import { config } from 'dotenv';

// Load environment variables
config();

interface MigrationOptions {
  source: 'file' | 'local' | 'json';
  target: 'qdrant';
  sourcePath?: string;
  batchSize?: number;
  qdrantUrl?: string;
  qdrantApiKey?: string;
  collectionName?: string;
  dryRun?: boolean;
}

export class MemoryMigrator {
  private options: MigrationOptions;
  private qdrantStore: QdrantMemoryStore;
  private totalMigrated: number = 0;
  private failedMigrations: Array<{ id: string; error: string }> = [];

  constructor(options: MigrationOptions) {
    this.options = {
      batchSize: 50,
      qdrantUrl: process.env.QDRANT_URL || 'http://localhost:6333',
      collectionName: 'dante_memories',
      dryRun: false,
      ...options,
    };

    this.qdrantStore = new QdrantMemoryStore({
      url: this.options.qdrantUrl,
      apiKey: this.options.qdrantApiKey,
      collectionName: this.options.collectionName,
    });
  }

  async migrate(): Promise<void> {
    const spinner = ora('Initializing migration...').start();

    try {
      // Initialize Qdrant store
      await this.qdrantStore.initialize();
      spinner.succeed('Qdrant connection established');

      // Get memories from source
      spinner.start('Loading memories from source...');
      const memories = await this.loadMemoriesFromSource();
      spinner.succeed(`Loaded ${memories.length} memories from source`);

      if (this.options.dryRun) {
        console.log(chalk.yellow('\n🔍 DRY RUN MODE - No actual migration will occur\n'));
        this.analyzeMemories(memories);
        return;
      }

      // Migrate memories in batches
      spinner.start('Migrating memories to Qdrant...');
      await this.migrateInBatches(memories, spinner);

      // Show results
      this.showMigrationResults();

    } catch (error) {
      spinner.fail('Migration failed');
      console.error(chalk.red('Error:'), error);
      throw error;
    }
  }

  private async loadMemoriesFromSource(): Promise<Memory[]> {
    switch (this.options.source) {
      case 'file':
        return this.loadFromFileStore();
      case 'local':
        return this.loadFromLocalStore();
      case 'json':
        return this.loadFromJsonFile();
      default:
        throw new Error(`Unsupported source: ${this.options.source}`);
    }
  }

  private async loadFromFileStore(): Promise<Memory[]> {
    const fileStore = new FileMemoryStore();
    await fileStore.initialize();
    
    const searchResult = await fileStore.search({ limit: 10000 });
    return searchResult.memories;
  }

  private async loadFromLocalStore(): Promise<Memory[]> {
    // Initialize memory manager to load from local store
    await memoryManager.initialize();
    const searchResult = await memoryManager.search({ limit: 10000 });
    return searchResult.memories;
  }

  private async loadFromJsonFile(): Promise<Memory[]> {
    if (!this.options.sourcePath) {
      throw new Error('Source path required for JSON import');
    }

    const fs = require('fs/promises');
    const data = await fs.readFile(this.options.sourcePath, 'utf-8');
    const parsed = JSON.parse(data);

    // Handle different JSON formats
    if (Array.isArray(parsed)) {
      return parsed;
    } else if (parsed.memories) {
      return parsed.memories;
    } else {
      throw new Error('Invalid JSON format - expected array or object with memories property');
    }
  }

  private async migrateInBatches(memories: Memory[], spinner: Ora): Promise<void> {
    const batchSize = this.options.batchSize!;
    const totalBatches = Math.ceil(memories.length / batchSize);

    for (let i = 0; i < memories.length; i += batchSize) {
      const batch = memories.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      spinner.text = `Migrating batch ${batchNumber}/${totalBatches} (${this.totalMigrated}/${memories.length} memories)`;

      try {
        // Filter memories that have embeddings
        const memoriesWithEmbeddings = batch.filter(m => m.metadata.embedding);
        
        if (memoriesWithEmbeddings.length > 0) {
          await this.qdrantStore.bulkStore(memoriesWithEmbeddings);
          this.totalMigrated += memoriesWithEmbeddings.length;
        }

        // Track memories without embeddings
        const withoutEmbeddings = batch.filter(m => !m.metadata.embedding);
        withoutEmbeddings.forEach(m => {
          this.failedMigrations.push({
            id: m.metadata.id,
            error: 'Missing embedding',
          });
        });

      } catch (error) {
        // Track failed batch
        batch.forEach(m => {
          this.failedMigrations.push({
            id: m.metadata.id,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        });
      }

      // Add small delay to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    spinner.succeed(`Migration complete: ${this.totalMigrated} memories migrated`);
  }

  private analyzeMemories(memories: Memory[]): void {
    const stats = {
      total: memories.length,
      withEmbeddings: memories.filter(m => m.metadata.embedding).length,
      withoutEmbeddings: memories.filter(m => !m.metadata.embedding).length,
      byType: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      avgEmbeddingDim: 0,
    };

    memories.forEach(m => {
      // Count by type
      stats.byType[m.metadata.type] = (stats.byType[m.metadata.type] || 0) + 1;
      
      // Count by priority
      stats.byPriority[m.metadata.priority] = (stats.byPriority[m.metadata.priority] || 0) + 1;
      
      // Calculate average embedding dimension
      if (m.metadata.embedding) {
        stats.avgEmbeddingDim += m.metadata.embedding.length;
      }
    });

    if (stats.withEmbeddings > 0) {
      stats.avgEmbeddingDim = Math.round(stats.avgEmbeddingDim / stats.withEmbeddings);
    }

    console.log(chalk.cyan('\n📊 Memory Analysis:'));
    console.log(`  Total memories: ${chalk.bold(stats.total)}`);
    console.log(`  With embeddings: ${chalk.green(stats.withEmbeddings)}`);
    console.log(`  Without embeddings: ${chalk.yellow(stats.withoutEmbeddings)}`);
    console.log(`  Average embedding dimension: ${stats.avgEmbeddingDim}`);
    
    console.log(chalk.cyan('\n  By Type:'));
    Object.entries(stats.byType).forEach(([type, count]) => {
      console.log(`    ${type}: ${count}`);
    });
    
    console.log(chalk.cyan('\n  By Priority:'));
    Object.entries(stats.byPriority).forEach(([priority, count]) => {
      console.log(`    ${priority}: ${count}`);
    });
  }

  private showMigrationResults(): void {
    console.log(chalk.green('\n✅ Migration Results:'));
    console.log(`  Successfully migrated: ${chalk.bold.green(this.totalMigrated)} memories`);
    
    if (this.failedMigrations.length > 0) {
      console.log(`  Failed migrations: ${chalk.bold.red(this.failedMigrations.length)}`);
      
      // Show first 10 failures
      console.log(chalk.yellow('\n  Failed Memory IDs (first 10):'));
      this.failedMigrations.slice(0, 10).forEach(failure => {
        console.log(`    ${failure.id}: ${chalk.red(failure.error)}`);
      });
      
      if (this.failedMigrations.length > 10) {
        console.log(chalk.gray(`    ... and ${this.failedMigrations.length - 10} more`));
      }
    }

    // Get and display Qdrant stats
    this.displayQdrantStats();
  }

  private async displayQdrantStats(): Promise<void> {
    try {
      const stats = await this.qdrantStore.getStats();
      console.log(chalk.cyan('\n📈 Qdrant Stats:'));
      console.log(`  Total vectors: ${chalk.bold(stats.totalVectors)}`);
      console.log(`  Collections: ${stats.collections?.join(', ')}`);
      
      if (stats.indexInfo) {
        console.log(`  Index status: ${stats.indexInfo.status}`);
        console.log(`  Segments: ${stats.indexInfo.segmentsCount}`);
      }
    } catch (error) {
      console.error(chalk.red('Failed to get Qdrant stats:'), error);
    }
  }

  async verify(): Promise<void> {
    console.log(chalk.cyan('\n🔍 Verifying migration...'));
    
    const stats = await this.qdrantStore.getStats();
    console.log(`  Vectors in Qdrant: ${chalk.bold(stats.totalVectors)}`);
    
    // Test a sample search
    const testQuery = 'test query';
    const results = await this.qdrantStore.hybridSearch(testQuery, {}, 5);
    console.log(`  Sample search returned: ${results.memories.length} results`);
    
    console.log(chalk.green('\n✅ Verification complete'));
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    console.log(chalk.cyan('Memory Migration Tool'));
    console.log('\nUsage: bun run migrate-memories [options]\n');
    console.log('Options:');
    console.log('  --source <type>     Source type: file, local, json');
    console.log('  --source-path <path> Path to JSON file (for json source)');
    console.log('  --qdrant-url <url>  Qdrant server URL (default: http://localhost:6333)');
    console.log('  --collection <name>  Collection name (default: dante_memories)');
    console.log('  --batch-size <n>    Batch size for migration (default: 50)');
    console.log('  --dry-run           Analyze without migrating');
    console.log('  --verify            Verify migration after completion');
    console.log('\nExamples:');
    console.log('  bun run migrate-memories --source file');
    console.log('  bun run migrate-memories --source json --source-path ./memories.json');
    console.log('  bun run migrate-memories --source local --dry-run');
    process.exit(0);
  }

  const options: MigrationOptions = {
    source: 'file',
    target: 'qdrant',
  };

  // Parse arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--source':
        options.source = args[++i] as any;
        break;
      case '--source-path':
        options.sourcePath = args[++i];
        break;
      case '--qdrant-url':
        options.qdrantUrl = args[++i];
        break;
      case '--collection':
        options.collectionName = args[++i];
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
    }
  }

  const shouldVerify = args.includes('--verify');

  // Run migration
  const migrator = new MemoryMigrator(options);
  
  migrator.migrate()
    .then(async () => {
      if (shouldVerify && !options.dryRun) {
        await migrator.verify();
      }
      console.log(chalk.green('\n✨ Migration completed successfully!'));
      process.exit(0);
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Migration failed:'), error);
      process.exit(1);
    });
}