import { spawn, execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

export interface ProcessConfig {
  name: string;
  command: string;
  args?: string[];
  cwd?: string;
  env?: Record<string, string>;
  port?: number;
  pidFile?: string;
}

export interface ProcessInfo {
  pid: number;
  name: string;
  command: string;
  status: 'running' | 'stopped' | 'unknown';
  port?: number;
  startTime?: Date;
}

/**
 * Manages process lifecycle for service restarts
 */
export class ProcessManager {
  private processes: Map<string, ProcessInfo> = new Map();
  private pidDir = path.join(process.cwd(), '.pids');

  constructor() {
    // Ensure PID directory exists
    if (!fs.existsSync(this.pidDir)) {
      fs.mkdirSync(this.pidDir, { recursive: true });
    }
    this.loadProcessInfo();
  }

  /**
   * Load process information from PID files
   */
  private loadProcessInfo(): void {
    try {
      if (!fs.existsSync(this.pidDir)) return;
      
      const files = fs.readdirSync(this.pidDir);
      for (const file of files) {
        if (file.endsWith('.pid')) {
          const pidFile = path.join(this.pidDir, file);
          const content = fs.readFileSync(pidFile, 'utf-8');
          const info = JSON.parse(content);
          
          // Check if process is still running
          if (this.isProcessRunning(info.pid)) {
            this.processes.set(info.name, info);
          } else {
            // Clean up stale PID file
            fs.unlinkSync(pidFile);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load process info:', error);
    }
  }

  /**
   * Check if a process is running
   */
  private isProcessRunning(pid: number): boolean {
    try {
      process.kill(pid, 0);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Start a process
   */
  async startProcess(config: ProcessConfig): Promise<ProcessInfo> {
    const existing = this.processes.get(config.name);
    
    // If process already running, return existing info
    if (existing && this.isProcessRunning(existing.pid)) {
      return existing;
    }

    return new Promise((resolve, reject) => {
      const env = {
        ...process.env,
        ...config.env,
      };

      const proc = spawn(config.command, config.args || [], {
        cwd: config.cwd || process.cwd(),
        env,
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe'],
      });

      const info: ProcessInfo = {
        pid: proc.pid!,
        name: config.name,
        command: `${config.command} ${(config.args || []).join(' ')}`,
        status: 'running',
        port: config.port,
        startTime: new Date(),
      };

      // Save PID file
      const pidFile = path.join(this.pidDir, `${config.name}.pid`);
      fs.writeFileSync(pidFile, JSON.stringify(info, null, 2));

      this.processes.set(config.name, info);

      // Unref the process so it can run independently
      proc.unref();

      // Give process time to start
      setTimeout(() => {
        if (this.isProcessRunning(proc.pid!)) {
          resolve(info);
        } else {
          reject(new Error(`Process ${config.name} failed to start`));
        }
      }, 2000);

      // Handle process errors
      proc.on('error', (error) => {
        reject(error);
      });

      // Log output for debugging
      proc.stdout?.on('data', (data) => {
        console.log(`[${config.name}]: ${data}`);
      });

      proc.stderr?.on('data', (data) => {
        console.error(`[${config.name} ERROR]: ${data}`);
      });
    });
  }

  /**
   * Stop a process
   */
  async stopProcess(name: string, force = false): Promise<boolean> {
    const info = this.processes.get(name);
    if (!info) {
      return false;
    }

    try {
      if (this.isProcessRunning(info.pid)) {
        process.kill(info.pid, force ? 'SIGKILL' : 'SIGTERM');
        
        // Wait for process to stop
        let attempts = 0;
        while (attempts < 10 && this.isProcessRunning(info.pid)) {
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
        }

        // Force kill if still running
        if (this.isProcessRunning(info.pid)) {
          process.kill(info.pid, 'SIGKILL');
        }
      }

      // Remove PID file
      const pidFile = path.join(this.pidDir, `${name}.pid`);
      if (fs.existsSync(pidFile)) {
        fs.unlinkSync(pidFile);
      }

      this.processes.delete(name);
      return true;
    } catch (error) {
      console.error(`Failed to stop process ${name}:`, error);
      return false;
    }
  }

  /**
   * Restart a process
   */
  async restartProcess(config: ProcessConfig): Promise<ProcessInfo> {
    await this.stopProcess(config.name);
    return await this.startProcess(config);
  }

  /**
   * Get process info
   */
  getProcessInfo(name: string): ProcessInfo | undefined {
    const info = this.processes.get(name);
    if (info && !this.isProcessRunning(info.pid)) {
      // Process died, update status
      info.status = 'stopped';
      this.processes.delete(name);
      
      // Clean up PID file
      const pidFile = path.join(this.pidDir, `${name}.pid`);
      if (fs.existsSync(pidFile)) {
        fs.unlinkSync(pidFile);
      }
    }
    return info;
  }

  /**
   * List all managed processes
   */
  listProcesses(): ProcessInfo[] {
    const processes: ProcessInfo[] = [];
    
    for (const [name, info] of this.processes) {
      if (this.isProcessRunning(info.pid)) {
        processes.push(info);
      } else {
        // Clean up dead process
        this.processes.delete(name);
      }
    }
    
    return processes;
  }

  /**
   * Kill process by port
   */
  async killProcessByPort(port: number): Promise<boolean> {
    try {
      if (process.platform === 'win32') {
        // Windows command to find and kill process by port
        execSync(`netstat -ano | findstr :${port} | findstr LISTENING | for /f "tokens=5" %a in ('more') do taskkill /PID %a /F`, { stdio: 'ignore' });
      } else {
        // Unix-like command to find and kill process by port
        execSync(`lsof -ti:${port} | xargs kill -9`, { stdio: 'ignore' });
      }
      return true;
    } catch {
      // Process might not exist or already stopped
      return false;
    }
  }

  /**
   * Find process using port
   */
  findProcessByPort(port: number): ProcessInfo | undefined {
    for (const info of this.processes.values()) {
      if (info.port === port && this.isProcessRunning(info.pid)) {
        return info;
      }
    }
    return undefined;
  }

  /**
   * Restart API server
   */
  async restartAPIServer(): Promise<ProcessInfo> {
    const config: ProcessConfig = {
      name: 'dante-api',
      command: 'bun',
      args: ['run', 'dev:api'],
      port: 3001,
      env: {
        PORT: '3001',
      },
    };

    return await this.restartProcess(config);
  }

  /**
   * Restart web UI server
   */
  async restartWebUI(): Promise<ProcessInfo> {
    const config: ProcessConfig = {
      name: 'dante-web',
      command: 'bun',
      args: ['run', 'dev'],
      port: 3002,
      env: {
        PORT: '3002',
      },
    };

    return await this.restartProcess(config);
  }

  /**
   * Restart tool server (e.g., browser-use)
   */
  async restartToolServer(serverName: string, port?: number): Promise<ProcessInfo> {
    const config: ProcessConfig = {
      name: `tool-${serverName}`,
      command: 'bun',
      args: ['run', `${serverName}:start`],
      port,
    };

    // Special handling for known tool servers
    if (serverName === 'browser-use') {
      config.command = 'npx';
      config.args = ['@modelcontextprotocol/server-browser-use'];
      config.port = port || 5173;
    }

    return await this.restartProcess(config);
  }

  /**
   * Clean up all processes
   */
  async cleanup(): Promise<void> {
    for (const name of this.processes.keys()) {
      await this.stopProcess(name);
    }
  }
}

// Export singleton instance
export const processManager = new ProcessManager();