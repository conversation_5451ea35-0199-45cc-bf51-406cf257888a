import fs from 'fs/promises';
import path from 'path';

// Lightweight AI summarizer that prefers <PERSON> (if configured) and falls back to
// an extractive summarizer when no LLM is available. Saves summaries via the
// news DB wrapper at ../db/news-db.js

async function tryUseGemini(content, title) {
  try {
    const { geminiClient } = await import('../utils/geminiClient.js');
    if (!geminiClient || !geminiClient.isConfigured()) return null;

    const prompt = `You are an expert news summarizer. Produce a JSON object with two keys:\n` +
      `- bullets: an array of 3-5 concise bullet points (short sentences) that summarize the most important facts of the article.\n` +
      `- why_it_matters: a single concise sentence (15-30 words) explaining why this article matters and its likely implications for the industry or users.\n\n` +
      `Article Title: ${title || 'Untitled'}\n\nArticle content:\n${content}\n\nRespond ONLY with valid JSON.`;

    const response = await geminiClient.createChatCompletion({
      model: 'gemini-2.5-flash',
      messages: [
        { role: 'system', content: 'You are a concise summarization assistant.' },
        { role: 'user', content: prompt }
      ],

      max_tokens: 8000
    });

    const text = response?.choices?.[0]?.message?.content || response?.choices?.[0]?.text || response?.text;
    if (!text || typeof text !== 'string') return null;

    // Attempt to extract JSON from the model output
    const jsonMatch = text.match(/```json\n?([\s\S]*?)\n?```/) || text.match(/\{[\s\S]*\}/);
    const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : null;
    if (!jsonStr) return null;

    try {
      const parsed = JSON.parse(jsonStr);
      if (parsed.bullets && parsed.why_it_matters) return parsed;
      return null;
    } catch (err) {
      console.warn('Gemini returned non-JSON or malformed JSON for summarization');
      return null;
    }
  } catch (err) {
    // Gemini not available or failed
    return null;
  }
}

function extractiveSummarize(content, title, maxBullets = 4) {
  if (!content || typeof content !== 'string') return { bullets: [], why_it_matters: '' };

  // Simple sentence split
  const sentences = content
    .replace(/\s+/g, ' ')
    .split(/(?<=[.!?])\s+/)
    .map(s => s.trim())
    .filter(s => s.length > 30);

  // Score sentences for relevance by keyword overlap with title and length
  const titleWords = (title || '').toLowerCase().split(/\W+/).filter(Boolean);

  const scored = sentences.map(s => {
    const low = s.toLowerCase();
    let score = 0;
    for (const w of titleWords) if (w.length > 2 && low.includes(w)) score += 2;
    // penalize very long sentences slightly
    score += Math.min(3, s.length / 140);
    return { sentence: s, score };
  }).sort((a, b) => b.score - a.score);

  const bullets = Array.from(new Set(scored.slice(0, maxBullets).map(s => s.sentence))).map(s => {
    // Ensure bullets are concise
    if (s.length > 300) return s.slice(0, 297) + '...';
    return s;
  });

  // Build a why it matters sentence using heuristics
  let why = '';
  const contentLower = content.toLowerCase();
  if (contentLower.match(/security|vulnerab|exploit|breach/)) {
    why = 'Why it matters: This report reveals security risks that could affect users, infrastructure, or trust in the affected service.';
  } else if (contentLower.match(/fund|investment|raise|acquir|merg|ipo|funding/)) {
    why = 'Why it matters: Financial moves may shift market positioning, impact competitors, and influence future investments in the space.';
  } else if (contentLower.match(/launch|released|debut|announce|rollout/)) {
    why = 'Why it matters: The new product/event could change user experience, competitive dynamics, or adoption trends in the industry.';
  } else if (contentLower.match(/policy|regulat|law|govern/)) {
    why = 'Why it matters: Regulatory changes may affect compliance requirements and long-term business strategy for stakeholders.';
  } else if (bullets.length > 0) {
    why = `Why it matters: ${bullets[0].replace(/\.+$/, '')}. It could influence stakeholders, strategy, or future developments.`;
  } else {
    why = 'Why it matters: This update contains important information that could impact stakeholders and future developments in the area.';
  }

  return { bullets, why_it_matters: why };
}

async function saveSummaryToDb(articleId, summary) {
  try {
    const db = await import('../db/news-db.js');
    if (db && typeof db.saveArticleSummary === 'function') {
      await db.saveArticleSummary(articleId, summary);
      return true;
    }
  } catch (err) {
    console.warn('Failed to save summary via news-db:', err.message || err);
  }

  // Fallback: write to local data file
  try {
    const dataDir = path.join(process.cwd(), 'data');
    await fs.mkdir(dataDir, { recursive: true });
    const filePath = path.join(dataDir, 'news-summaries.json');
    let existing = {};
    try {
      const raw = await fs.readFile(filePath, 'utf-8');
      existing = JSON.parse(raw);
    } catch (e) {
      existing = {};
    }
    existing[articleId] = { ...summary, savedAt: new Date().toISOString() };
    await fs.writeFile(filePath, JSON.stringify(existing, null, 2), 'utf-8');
    return true;
  } catch (err) {
    console.error('Failed to persist summary to local file:', err);
    return false;
  }
}

export async function summarizeArticle(article) {
  if (!article || (!article.content && !article.fullContent)) {
    throw new Error('Missing article content');
  }

  const content = article.fullContent || article.content || '';
  const title = article.title || '';

  // Try Gemini first
  const geminiRes = await tryUseGemini(content, title);
  const usedModel = geminiRes ? 'gemini-2.5-flash' : 'extractive-fallback';

  const result = geminiRes || extractiveSummarize(content, title, 4);

  // Normalize bullets to array of strings
  const bullets = Array.isArray(result.bullets) ? result.bullets.map(b => (typeof b === 'string' ? b : String(b))) : [];
  const why_it_matters = result.why_it_matters || result.whyItMatters || '';

  const summaryObj = {
    bullets,
    why_it_matters,
    model: usedModel,
    generatedAt: new Date().toISOString()
  };

  // Persist
  try {
    await saveSummaryToDb(article.id || `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`, summaryObj);
  } catch (err) {
    console.warn('Failed to persist summary:', err);
  }

  return summaryObj;
}

export default { summarizeArticle };
