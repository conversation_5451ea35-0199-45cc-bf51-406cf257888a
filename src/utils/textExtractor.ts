/**
 * Centralized text extraction and processing utilities for consistent handling of various result formats
 * Used across ModelOrchestrator, voiceIntegration, and other components
 */

/**
 * Extract text content from various result formats.
 * Always returns a string (never an object/undefined) to keep downstream safe.
 */
export function extractTextContent(result: any): string {
  if (result == null) return '';

  // Plain string
  if (typeof result === 'string') return result;

  // Vercel AI SDK DefaultStreamTextResult exposes a `text` getter
  try {
    const maybeText = (result as any).text;
    if (typeof maybeText === 'string') return maybeText;
  } catch {
    // ignore getter errors
  }

  // Standard shapes
  if (typeof result?.content === 'string') return result.content;

  // content as array of blocks (e.g., SDK outputs)
  if (Array.isArray(result?.content)) {
    const parts = (result.content as any[])
      .map((p) =>
        typeof p === 'string'
          ? p
          : typeof p?.text === 'string'
          ? p.text
          : typeof p?.content === 'string'
          ? p.content
          : ''
      )
      .filter(Boolean);
    if (parts.length) return parts.join(' ');
  }

  if (typeof result?.message === 'string') return result.message;

  // OpenAI chat completion format
  if (typeof result?.choices?.[0]?.message?.content === 'string') {
    return result.choices[0].message.content;
  }

  // Agents SDK-like shape
  if (typeof result?.output?.content === 'string') return result.output.content;
  if (Array.isArray(result?.output?.content)) {
    const parts = (result.output.content as any[])
      .map((p) => (typeof p === 'string' ? p : typeof p?.text === 'string' ? p.text : ''))
      .filter(Boolean);
    if (parts.length) return parts.join(' ');
  }

  // As a conservative fallback, avoid JSON blobs in voice flows; return empty string
  // but if a primitive convertible value exists, coerce it.
  if (typeof result.valueOf === 'function') {
    const v = result.valueOf();
    if (typeof v === 'string') return v;
  }

  return '';
}

/**
 * Clean and prepare text for voice output by removing formatting and optimizing for speech
 */
export function prepareTextForVoice(text: unknown): string {
  const str = typeof text === 'string' ? text : text == null ? '' : String(text);
  return str
    // Remove markdown formatting
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/`(.*?)`/g, '$1')
    .replace(/```[\s\S]*?```/g, '[code block]')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove markdown links
    // Remove excessive formatting symbols
    .replace(/[#]+\s*/g, '')
    .replace(/^\s*[-*]\s+/gm, '') // Remove bullet points
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered lists
    // Clean up spacing and structure for speech
    .replace(/\n\n+/g, '. ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/\.\s*\./g, '.') // Remove duplicate periods
    .replace(/([.!?])\s*([.!?])/g, '$1 $2') // Fix punctuation spacing
    .trim();
}
