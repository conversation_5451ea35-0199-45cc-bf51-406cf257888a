import * as fs from 'fs/promises';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export type UnifiedHunk = { oldStart: number; oldLines: number; newStart: number; newLines: number; lines: string[] };
export type ParsedUnifiedFile = { path: string; hunks: UnifiedHunk[] };

export function stripABPrefix(p: string): string { return p.replace(/^([ab]\/)+/, ''); }

export function parseUnifiedDiffText(diff: string): { files: ParsedUnifiedFile[] } {
  const lines = diff.split(/\r?\n/);
  const files: ParsedUnifiedFile[] = [];
  let current: ParsedUnifiedFile | null = null;
  let hunk: UnifiedHunk | null = null;
  for (const line of lines) {
    if (line.startsWith('+++ ')) {
      const m = line.match(/^\+\+\+\s+([^\s].*)$/);
      if (m) {
        const p = m[1];
        current = { path: p, hunks: [] };
        files.push(current);
        hunk = null;
      }
      continue;
    }
    if (line.startsWith('@@ ')) {
      const hm = line.match(/^@@\s+-(\d+)(?:,(\d+))?\s+\+(\d+)(?:,(\d+))?\s+@@/);
      if (hm) {
        const oldStart = parseInt(hm[1], 10);
        const oldLines = hm[2] ? parseInt(hm[2], 10) : 0;
        const newStart = parseInt(hm[3], 10);
        const newLines = hm[4] ? parseInt(hm[4], 10) : 0;
        hunk = { oldStart, oldLines, newStart, newLines, lines: [] };
        current?.hunks.push(hunk);
      }
      continue;
    }
    if (hunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-') || line === '\\ No newline at end of file')) {
      if (line !== '\\ No newline at end of file') hunk.lines.push(line);
      continue;
    }
  }
  return { files };
}

export function applyUnifiedHunks(original: string, hunks: UnifiedHunk[], opts: { fuzzy?: boolean }): { ok: true; next: string } | { ok: false; error: string } {
  const hadTrailingNewline = original.endsWith('\n');
  const orig = (hadTrailingNewline ? original.slice(0, -1) : original).split('\n');
  let cursor = 0; // 0-based index
  const out: string[] = [];
  for (const h of hunks) {
    const start = Math.max(0, h.oldStart - 1);
    while (cursor < start) { out.push(orig[cursor++] ?? ''); }
    let exp = start;
    for (const l of h.lines) {
      const tag = l[0];
      const body = l.slice(1);
      if (tag === ' ') {
        const src = orig[exp] ?? '';
        if (src !== body && !opts.fuzzy) return { ok: false, error: `context mismatch at line ${exp + 1}` };
        out.push(body);
        exp++; cursor = exp;
      } else if (tag === '-') {
        const src = orig[exp] ?? '';
        if (src !== body && !opts.fuzzy) return { ok: false, error: `deletion mismatch at line ${exp + 1}` };
        exp++; cursor = exp;
      } else if (tag === '+') {
        out.push(body);
      }
    }
  }
  while (cursor < orig.length) out.push(orig[cursor++] ?? '');
  const joined = out.join('\n') + (hadTrailingNewline ? '\n' : '');
  return { ok: true, next: joined };
}

export type MergeStrategy = 'strict'|'fuzzy'|'conflict_markers';

export function applyUnifiedHunksStrategy(original: string, hunks: UnifiedHunk[], strategy: MergeStrategy):
  { ok: true; next: string; conflicts?: boolean } | { ok: false; error: string } {
  if (strategy === 'strict') return applyUnifiedHunks(original, hunks, { fuzzy: false });
  if (strategy === 'fuzzy') return applyUnifiedHunks(original, hunks, { fuzzy: true });
  // conflict_markers
  const hadTrailingNewline = original.endsWith('\n');
  const orig = (hadTrailingNewline ? original.slice(0, -1) : original).split('\n');
  let cursor = 0;
  const out: string[] = [];
  let hadConflicts = false;
  for (const h of hunks) {
    const start = Math.max(0, h.oldStart - 1);
    while (cursor < start) out.push(orig[cursor++] ?? '');
    const oldSegFromFile = orig.slice(start, start + h.oldLines);
    const expectedOld: string[] = [];
    const newSeg: string[] = [];
    for (const l of h.lines) {
      const tag = l[0]; const body = l.slice(1);
      if (tag === ' ') { expectedOld.push(body); newSeg.push(body); }
      else if (tag === '-') { expectedOld.push(body); }
      else if (tag === '+') { newSeg.push(body); }
    }
    const matches = expectedOld.length === oldSegFromFile.length && expectedOld.every((v, i) => v === oldSegFromFile[i]);
    if (matches) {
      out.push(...newSeg);
    } else {
      hadConflicts = true;
      out.push('<<<<<<< ORIGINAL');
      out.push(...oldSegFromFile);
      out.push('=======');
      out.push(...newSeg);
      out.push('>>>>>>> PATCH');
    }
    cursor = start + h.oldLines;
  }
  while (cursor < orig.length) out.push(orig[cursor++] ?? '');
  const joined = out.join('\n') + (hadTrailingNewline ? '\n' : '');
  return { ok: true, next: joined, conflicts: hadConflicts };
}

export function serializeUnifiedDiffFromMap(perFile: Map<string, { hunks: UnifiedHunk[] }>): string {
  const parts: string[] = [];
  for (const [absPath, { hunks }] of perFile.entries()) {
    parts.push(`--- ${absPath}`);
    parts.push(`+++ ${absPath}`);
    for (const h of hunks) {
      const oldLen = typeof h.oldLines === 'number' ? h.oldLines : 0;
      const newLen = typeof h.newLines === 'number' ? h.newLines : 0;
      parts.push(`@@ -${h.oldStart}${oldLen ? ',' + oldLen : ''} +${h.newStart}${newLen ? ',' + newLen : ''} @@`);
      for (const l of h.lines) parts.push(l);
    }
  }
  return parts.join('\n') + (parts.length ? '\n' : '');
}

export async function tryGitApplyThreeWay(diff: string): Promise<boolean> {
  try {
    const { stdout } = await execAsync('git rev-parse --show-toplevel');
    const root = String(stdout || '').trim();
    if (!root) return false;
    const rewrite = (input: string): string => {
      const lines = input.split(/\r?\n/);
      return lines.map(l => {
        if (l.startsWith('--- ')) {
          const m = l.match(/^---\s+(.+)$/); const p = m ? stripABPrefix(m[1]) : '';
          const rel = path.isAbsolute(p) ? path.relative(root, p) : p;
          return `--- a/${rel}`;
        }
        if (l.startsWith('+++ ')) {
          const m = l.match(/^\+\+\+\s+(.+)$/); const p = m ? stripABPrefix(m[1]) : '';
          const rel = path.isAbsolute(p) ? path.relative(root, p) : p;
          return `+++ b/${rel}`;
        }
        return l;
      }).join('\n');
    };
    const rewritten = rewrite(diff);
    const tmp = path.join(root, `.file_edit_${Date.now()}_${Math.random().toString(36).slice(2)}.patch`);
    await fs.writeFile(tmp, rewritten, 'utf-8');
    try {
      await execAsync(`git apply --check '${tmp.replace(/'/g, "'\\''")}'`, { cwd: root } as any);
      await execAsync(`git apply --3way '${tmp.replace(/'/g, "'\\''")}'`, { cwd: root } as any);
      return true;
    } finally {
      try { await fs.unlink(tmp); } catch {}
    }
  } catch {
    return false;
  }
}

