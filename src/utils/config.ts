import dotenv from 'dotenv';

// Only load dotenv in Node.js environment
if (typeof process !== 'undefined' && process.env) {
  dotenv.config();
}

type VerificationStepConfig = { id: string; command: string };

// Safe environment variable access that works in both Node and browser
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key] as string;
  }
  // In browser, these would come from the API server
  return defaultValue;
};

const defaultVerificationSteps: VerificationStepConfig[] = [
  { id: 'typecheck', command: 'bun run typecheck' },
  { id: 'build', command: 'bun run build' },
  { id: 'test', command: 'bun test' },
];

const parseVerificationSteps = (raw: string): VerificationStepConfig[] => {
  const normalized = (inputs: any[]): VerificationStepConfig[] => {
    const seen = new Set<string>();
    const steps: VerificationStepConfig[] = [];
    for (const item of inputs) {
      let id: string | undefined;
      let command: string | undefined;

      if (!item) continue;
      if (typeof item === 'string') {
        const [rawId, ...rest] = item.split(':');
        const joined = rest.join(':').trim();
        id = (rawId || '').trim();
        command = joined || undefined;
      } else if (Array.isArray(item) && item.length >= 2) {
        id = typeof item[0] === 'string' ? item[0].trim() : undefined;
        command = typeof item[1] === 'string' ? item[1].trim() : undefined;
      } else if (typeof item === 'object') {
        id = typeof item.id === 'string' ? item.id.trim() : undefined;
        command = typeof item.command === 'string' ? item.command.trim() : undefined;
      }

      if (!id || !command || seen.has(id)) continue;
      seen.add(id);
      steps.push({ id, command });
    }
    return steps;
  };

  if (!raw) {
    return defaultVerificationSteps.slice();
  }

  // Try JSON first
  try {
    const parsed = JSON.parse(raw);
    if (Array.isArray(parsed)) {
      const steps = normalized(parsed);
      if (steps.length > 0) return steps;
      if (parsed.length === 0) return [];
    }
  } catch {
    // ignore JSON errors - try fallback parsing
  }

  const tokens = raw
    .split(/[,;\n]+/)
    .map(token => token.trim())
    .filter(Boolean);
  if (tokens.length === 0) {
    return defaultVerificationSteps.slice();
  }
  const steps = normalized(tokens);
  return steps.length > 0 ? steps : defaultVerificationSteps.slice();
};

// New function to dynamically get config
export const getConfig = () => ({
  openai: {
    apiKey: getEnvVar('OPENAI_API_KEY'),
    tracingKey: getEnvVar('OPENAI_TRACING_API_KEY'),
    endpoint: getEnvVar('OPENAI_API_ENDPOINT', 'https://api.openai.com'),
    defaultModel: getEnvVar('DEFAULT_MODEL', 'gpt-5'),
  },
  azure: {
    apiKey: getEnvVar('AZURE_API_KEY'),
    endpoint: getEnvVar('AZURE_API_ENDPOINT'),
    deploymentName: getEnvVar('AZURE_DEPLOYMENT_NAME'),
    defaultModel: getEnvVar('DEFAULT_AZURE_MODEL', 'gpt-5'),
  },
  anthropic: {
    apiKey: getEnvVar('ANTHROPIC_API_KEY'),
    endpoint: getEnvVar('ANTHROPIC_API_ENDPOINT', 'https://api.anthropic.com'),
    defaultModel: getEnvVar('DEFAULT_ANTHROPIC_MODEL', 'claude-3-opus-20240229'),
  },
  gemini: {
    apiKey: getEnvVar('GEMINI_API_KEY'),
    // With Vercel AI SDK Google provider, the default base URL is v1beta
    endpoint: getEnvVar('GEMINI_API_ENDPOINT', 'https://generativelanguage.googleapis.com/v1beta'),
    defaultModel: getEnvVar('DEFAULT_GEMINI_MODEL', 'gemini-2.5-flash'),
    // Context consolidation configuration
    consolidation: {
      enabled: getEnvVar('GEMINI_CONSOLIDATION_ENABLED', 'true') === 'true',
      autoConsolidate: getEnvVar('GEMINI_AUTO_CONSOLIDATE', 'true') === 'true',
      thresholds: {
        fileSize: parseInt(getEnvVar('GEMINI_FILE_SIZE_THRESHOLD', '100000')), // 100KB
        lineCount: parseInt(getEnvVar('GEMINI_LINE_COUNT_THRESHOLD', '3000')),
        tokenCount: parseInt(getEnvVar('GEMINI_TOKEN_COUNT_THRESHOLD', '20000'))
      },
      caching: {
        enabled: getEnvVar('GEMINI_CACHE_ENABLED', 'true') === 'true',
        ttl: parseInt(getEnvVar('GEMINI_CACHE_TTL', '3600')) // 1 hour in seconds
      }
    },
    // Summarization configuration
    summarization: {
      enabled: getEnvVar('GEMINI_SUMMARIZATION_ENABLED', 'true') === 'true',
    },
    // Strategic planning configuration
    planning: {
      enabled: getEnvVar('GEMINI_PLANNING_ENABLED', 'true') === 'true',
      model: getEnvVar('GEMINI_PLANNING_MODEL', 'gemini-2.5-pro'), // Use Pro for planning
      thinkingBudget: {
        simple: parseInt(getEnvVar('GEMINI_THINKING_SIMPLE', '128')),
        medium: parseInt(getEnvVar('GEMINI_THINKING_MEDIUM', '512')),
        complex: parseInt(getEnvVar('GEMINI_THINKING_COMPLEX', '2048')),
        orchestration: parseInt(getEnvVar('GEMINI_THINKING_ORCHESTRATION', '8192'))
      }
    },
  },
  ai: {
    provider: getEnvVar('AI_PROVIDER', 'openai'),
  },
  embeddings: {
    provider: getEnvVar('EMBEDDING_PROVIDER', 'auto'),
    openaiModel: getEnvVar('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
    openaiDimensions: (() => {
      const raw = parseInt(getEnvVar('OPENAI_EMBEDDING_DIMENSIONS', '1536'), 10);
      return Number.isFinite(raw) && raw > 0 ? raw : 1536;
    })(),
    ollamaModel: getEnvVar('OLLAMA_EMBEDDING_MODEL', 'nomic-embed-text'),
    ollamaDimensions: (() => {
      const raw = parseInt(getEnvVar('OLLAMA_EMBEDDING_DIMENSIONS', '768'), 10);
      return Number.isFinite(raw) && raw > 0 ? raw : 768;
    })(),
    cacheTTL: (() => {
      const raw = parseInt(
        getEnvVar('EMBEDDING_CACHE_TTL', String(24 * 60 * 60 * 1000)),
        10
      );
      return Number.isFinite(raw) && raw > 0 ? raw : 24 * 60 * 60 * 1000;
    })(),
    maxCacheSize: (() => {
      const raw = parseInt(getEnvVar('EMBEDDING_CACHE_MAX_SIZE', '1000'), 10);
      return Number.isFinite(raw) && raw > 0 ? raw : 1000;
    })(),
  },
  orchestrator: {
    offlineProvider: getEnvVar('ORCHESTRATOR_OFFLINE_PROVIDER', 'ollama'),
    offlineModel: getEnvVar('ORCHESTRATOR_OFFLINE_MODEL', 'gemma3:12b'),
    verification: {
      steps: (() => {
        const raw = getEnvVar('ORCH_VERIFICATION_STEPS', '');
        const overrideTypecheck = getEnvVar('ORCH_TYPECHECK_CMD', '').trim();
        const overrideBuild = getEnvVar('ORCH_BUILD_CMD', '').trim();
        const overrideTest = getEnvVar('ORCH_TEST_CMD', '').trim();

        if (raw.trim()) {
          return parseVerificationSteps(raw.trim());
        }

        if (overrideTypecheck || overrideBuild || overrideTest) {
          const steps = defaultVerificationSteps.map(step => ({ ...step }));

          if (overrideTypecheck) {
            const target = steps.find(step => step.id === 'typecheck');
            if (target) target.command = overrideTypecheck;
            else steps.push({ id: 'typecheck', command: overrideTypecheck });
          }

          if (overrideBuild) {
            const target = steps.find(step => step.id === 'build');
            if (target) target.command = overrideBuild;
            else steps.push({ id: 'build', command: overrideBuild });
          }

          if (overrideTest) {
            const target = steps.find(step => step.id === 'test');
            if (target) target.command = overrideTest;
            else steps.push({ id: 'test', command: overrideTest });
          }

          return steps;
        }

        return defaultVerificationSteps.slice();
      })(),
    },
    resilience: {
      maxHandoffDepth: parseInt(getEnvVar('ORCH_MAX_HANDOFF_DEPTH', '3')),
      coolingWindowMs: parseInt(getEnvVar('ORCH_COOLING_WINDOW_MS', '10000')),
      journalDir: getEnvVar('ORCH_JOURNAL_DIR', 'data/run-journal'),
      enableLoopGuard: getEnvVar('ORCH_ENABLE_LOOP_GUARD', 'false') === 'true',
      // 2PC Coordinator + Concurrency feature flags
      coordinator: {
        enabled: getEnvVar('ORCH_COORDINATOR_ENABLED', 'true') === 'true',
      },
      concurrency: {
        enabled: getEnvVar('ORCH_CONCURRENCY_ENABLED', 'true') === 'true',
        parallelEnabled: getEnvVar('ORCH_CONC_PARALLEL_ENABLED', 'true') === 'true',
        maxParallel: (() => {
          const raw = parseInt(getEnvVar('ORCH_CONC_MAX_PARALLEL', '4'));
          return Number.isFinite(raw) && raw > 0 ? raw : 4;
        })(),
        join: {
          strategy: getEnvVar('ORCH_CONC_JOIN_STRATEGY', 'deterministic'),
        },
        conflictPolicy: getEnvVar('ORCH_CONC_CONFLICT_POLICY', 'resolve-prefer-older'),
      },
      // Timeouts and lease settings
      timeouts: {
        prepareMs: parseInt(getEnvVar('ORCH_PREPARE_MS', '5000')),
        commitAckMs: parseInt(getEnvVar('ORCH_COMMIT_ACK_MS', '3000')),
        lockLeaseMs: parseInt(getEnvVar('ORCH_LOCK_LEASE_MS', '6000')),
        lockRenewIntervalMs: parseInt(getEnvVar('ORCH_LOCK_RENEW_MS', '3000')),
      },
      // Deadlock policy for the LockManager
      deadlockPolicy: getEnvVar('ORCH_DEADLOCK_POLICY', 'wound-wait'),
      retry: {
        maxAttempts: parseInt(getEnvVar('ORCH_RETRY_MAX_ATTEMPTS', '5')),
        baseDelayMs: parseInt(getEnvVar('ORCH_RETRY_BASE_MS', '500')),
        maxDelayMs: parseInt(getEnvVar('ORCH_RETRY_MAX_MS', '8000')),
        jitter: parseFloat(getEnvVar('ORCH_RETRY_JITTER', '0.2')),
        classifyRs404AsTransient: getEnvVar('ORCH_RETRY_RS404_TRANSIENT', 'true') === 'true'
      },
      commit: {
        enabled: getEnvVar('ORCH_COMMIT_ENABLED', 'true') === 'true',
        branchPrefix: getEnvVar('ORCH_COMMIT_BRANCH_PREFIX', 'orch/'),
        conventionalType: getEnvVar('ORCH_COMMIT_TYPE', 'feat'),
        enablePR: getEnvVar('ORCH_COMMIT_ENABLE_PR', 'false') === 'true'
      }
    }
  },
  ollama: {
    baseURL: getEnvVar('OLLAMA_BASE_URL', 'http://localhost:11434'),
  },
  summarizer: {
    enabled: getEnvVar('SUMMARIZER_ENABLED', 'true') === 'true',
    provider: getEnvVar('SUMMARIZER_PROVIDER', 'ollama'),
    model: getEnvVar('SUMMARIZER_MODEL', 'gpt-oss:20b'),
  },
  weather: {
    apiKey: getEnvVar('WEATHER_API_KEY'),
  },
  app: {
    port: parseInt(getEnvVar('PORT', '3000')),
    nodeEnv: getEnvVar('NODE_ENV', 'development'),
    maxTurns: parseInt(getEnvVar('MAX_TURNS', '15')),
  },
  api: {
    enabled: getEnvVar('API_ENABLED', 'true') === 'true',
    port: parseInt(getEnvVar('API_PORT', '3001')),
    host: getEnvVar('API_HOST', 'localhost'),
  },
  mcp: {
    enabled: getEnvVar('MCP_ENABLED', 'false') === 'true',
  },
  computerUse: {
    // Disable desktop/Docker computer-use by default in dev to avoid Bun segfault paths
    enabled: getEnvVar('COMPUTER_USE_ENABLED', 'false') === 'true',
    // Only auto-build Docker image when explicitly enabled
    autoBuild: getEnvVar('COMPUTER_USE_AUTOBUILD', 'false') === 'true',
    // Allow using environmentType=browser in computer_use tool (not recommended)
    allowBrowserEnv: getEnvVar('COMPUTER_USE_ALLOW_BROWSER', 'false') === 'true',
  },
  rateLimit: {
    enabled: getEnvVar('RATE_LIMIT_ENABLED', 'true') === 'true',
    minSpacingMs: parseInt(getEnvVar('RATE_MIN_SPACING_MS', '100')),
    maxSpacingMs: parseInt(getEnvVar('RATE_MAX_SPACING_MS', '1000')),
    tokensPerSecond: parseInt(getEnvVar('RATE_LIMIT_TOKENS_PER_SECOND', '10')),
    maxTokens: parseInt(getEnvVar('RATE_LIMIT_MAX_TOKENS', '100')),
  },
  fileStorage: {
    provider: getEnvVar('FILE_STORAGE_PROVIDER', 'local'),
    localStoragePath: getEnvVar('LOCAL_STORAGE_PATH', 'uploads'),
  },
  fileEdit: {
    // Optional path guard: comma-separated absolute roots; empty => unrestricted
    allowedRoots: (() => {
      const raw = getEnvVar('FILE_EDIT_ALLOWED_ROOTS', '').trim();
      if (!raw) return [] as string[];
      return raw
        .split(',')
        .map(s => s.trim())
        .filter(Boolean);
    })()
  },
  // Ratio for initial context budget in progressive disclosure (0 < r ≤ 1)
  initialContextBudgetRatio: (() => {
    const raw = parseFloat(getEnvVar('INITIAL_CONTEXT_BUDGET_RATIO', '0.5'));
    const ratio = isNaN(raw) ? 0.5 : raw;
    // Clamp to 0 < r ≤ 1
    return Math.min(1, Math.max(1e-6, ratio));
  })()
});

export const config = getConfig();


export function validateProviderConfig(provider: string, config: any) {
  if (!provider || typeof provider !== 'string') {
    throw new Error('Provider must be a non-empty string.');
  }
  if (!config || typeof config !== 'object') {
    throw new Error('Config must be a non-null object.');
  }
  const requiredFields: { [key: string]: string[] } = {
    openai: ['apiKey'],
    azure: ['apiKey', 'endpoint', 'deploymentName'],
    anthropic: ['apiKey'],
    gemini: ['apiKey'],
    ollama: ['baseURL'],
    custom: ['endpoint'],
  };

  const fields = requiredFields[provider];
  if (!fields) {
    throw new Error(`Invalid provider: ${provider}.`);
  }

  const missingFields = fields.filter(field => !config[field]);

  if (missingFields.length > 0) {
    throw new Error(
      `Missing required configuration for provider "${provider}": ${missingFields.join(
        ', '
      )}. Please set the corresponding environment variables.`
    );
  }
}


export function validateConfig() {
  const currentConfig = getConfig();
  const aiProvider = (currentConfig.ai.provider || '').toLowerCase();

  if (aiProvider) {
    validateProviderConfig(aiProvider, currentConfig[aiProvider as keyof typeof currentConfig]);
  }

  if (currentConfig.summarizer && currentConfig.summarizer.enabled) {
    const summarizerProvider = (currentConfig.summarizer.provider || '').toLowerCase();
    if (summarizerProvider) {
      validateProviderConfig(
        summarizerProvider,
        currentConfig[summarizerProvider as keyof typeof currentConfig]
      );
    }
  }
}


export function validateGeminiConfig() {
  const currentConfig = getConfig(); // Get the latest config
  if (!currentConfig.gemini.apiKey) {
    console.warn('GEMINI_API_KEY not found. Gemini orchestration features will be disabled.');
    return false;
  }
  // Bridge env var naming: ensure Vercel AI SDK google provider picks up API key
  if (typeof process !== 'undefined' && process.env) {
    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY && currentConfig.gemini.apiKey) {
      process.env.GOOGLE_GENERATIVE_AI_API_KEY = currentConfig.gemini.apiKey;
    }
  }
  return true;
}

export function isGeminiModel(modelId: string): boolean {
  return modelId.startsWith('gemini-') || modelId.startsWith('models/gemini-');
}
