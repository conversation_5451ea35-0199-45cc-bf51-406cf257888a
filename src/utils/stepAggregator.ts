// Minimal in-memory step aggregator for UI playback/logs

export type StepRecord = {
  t: number;
  agent?: string;
  step: any;
};

const store = new Map<string, StepRecord[]>();

export function newRunId(prefix: string = 'run'): string {
  const rand = Math.random().toString(36).slice(2, 8);
  return `${prefix}-${Date.now()}-${rand}`;
}

export function appendStep(runId: string, record: StepRecord): void {
  const arr = store.get(runId) || [];
  arr.push(record);
  store.set(runId, arr);
}

export function getSteps(runId: string): StepRecord[] {
  return store.get(runId) || [];
}

export function clearRun(runId: string): void {
  store.delete(runId);
}

export function listRuns(): string[] {
  return Array.from(store.keys());
}

