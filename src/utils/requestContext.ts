import { AsyncLocalStorage } from 'node:async_hooks';

export type Ctx = Record<string, any>;

const als = new AsyncLocalStorage<Ctx>();

export function runWithContext<T>(ctx: Ctx, fn: () => Promise<T> | T): Promise<T> | T {
  return als.run(ctx, fn as any) as any;
}

export function setContextValue(key: string, value: any) {
  const store = als.getStore();
  if (store) store[key] = value;
}

export function getContextValue<T = any>(key: string): T | undefined {
  const store = als.getStore();
  return store ? (store[key] as T) : undefined;
}

export function getAllContext(): Ctx | undefined {
  return als.getStore();
}

