/**
 * Centralized sanitizer for provider params to remove unsupported fields for reasoning models.
 *
 * Notes:
 * - The DISALLOWED_REASONING_PARAMS set is conservative and may be expanded based on provider rules.
 * - We avoid importing from geminiClient.ts to prevent circular deps; we mirror known Gemini 2.5 models here.
 * - Heuristics cover common "reasoning" model families (OpenAI o-series, explicit "reason" in id, DeepSeek Reasoner, etc.).
 */

/**
 * Determine if a model should be treated as a "reasoning" model (i.e., params like temperature are unsupported).
 *
 * Strategy:
 * - Prefer explicit knowledge for Gemini 2.5 models that support thinking.
 * - Fallback to conservative heuristics for other providers (OpenAI o-series, models containing "reason"/"reasoning", etc.).
 */
export function isReasoningModel(model: string): boolean {
  if (!model) return false;
  const m = model.toLowerCase().trim();

  // Explicit Gemini 2.5 models that support "thinking"
  // Mirrors entries from GeminiClient.getModelConfig() without importing to avoid cycles.
  const geminiReasoningModels = new Set([
    'gemini-2.5-pro',
    'gemini-2.5-flash',
    'gemini-2.5-flash-lite',
    'models/gemini-2.5-pro',
    'models/gemini-2.5-flash',
    'models/gemini-2.5-flash-lite',
  ]);
  if (geminiReasoningModels.has(m)) return true;

  // OpenAI o-series reasoning family (o3, o4, etc.)
  if (/^o\d/.test(m) || /^o\d-/.test(m)) return true;

  // Common explicit reasoning identifiers across providers
  if (m.includes('reason') || m.includes('reasoning')) return true;

  // Known alternates
  // DeepSeek Reasoner
  if (m.includes('deepseek') && m.includes('reason')) return true;
  // Qwen reasoning
  if (m.includes('qwen') && m.includes('reason')) return true;

  return false;
}

/**
 * Params disallowed for reasoning models (conservative superset).
 */
export const DISALLOWED_REASONING_PARAMS: ReadonlySet<string> = new Set([
  'temperature',
  'top_p',
  'top_k',
  'frequency_penalty',
  'presence_penalty',
  'seed',
  'random_seed',
]);

/**
 * Remove unsupported params for reasoning models while preserving the original type shape.
 *
 * Example:
 *   const sanitized = cleanParamsForModel({ model: 'o3', temperature: 0.7, ... });
 *   // sanitized = { model: 'o3', ... }  (no temperature)
 */
export function cleanParamsForModel<T extends { model: string }>(params: T): T {
  try {
    if (!params || !params.model) return params;
    if (!isReasoningModel(params.model)) return params;

    // Shallow clone and strip disallowed keys
    const clone: Record<string, any> = { ...params };
    for (const key of DISALLOWED_REASONING_PARAMS) {
      if (key in clone) {
        delete clone[key];
      }
    }
    return clone as T;
  } catch {
    // Fail-safe: never mutate on sanitizer errors
    return params;
  }
}
