/**
 * Tool Parameter Schemas and Validation Definitions
 * Defines expected parameters for all tools to enable proper validation
 */

export interface ToolParameterConfig {
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  defaultValue?: any;
}

/**
 * Parameter definitions for all file operation tools
 */
export const FILE_OPERATION_TOOL_SCHEMAS: Record<string, Record<string, ToolParameterConfig>> = {
  grep_code: {
    pattern: { required: true, type: 'string', description: 'Search pattern (regex supported by rg/grep)' },
    searchPath: { required: false, type: 'string', description: 'Path to search in', defaultValue: 'src' },
    fileTypes: { required: false, type: 'array', description: 'File extensions to include', defaultValue: ['ts','tsx','js','jsx'] },
    caseSensitive: { required: false, type: 'boolean', description: 'Case sensitive search', defaultValue: false },
    contextLines: { required: false, type: 'number', description: 'Context lines to include in output', defaultValue: 0 },
    windowBefore: { required: false, type: 'number', description: 'Suggested lines before match for region extraction', defaultValue: 10 },
    windowAfter: { required: false, type: 'number', description: 'Suggested lines after match for region extraction', defaultValue: 30 },
    maxResults: { required: false, type: 'number', description: 'Max number of matches to return', defaultValue: 200 }
  },
  
  read_file: {
    filePath: {
      required: true,
      type: 'string',
      description: 'The path to the file to read'
    },
    // Keep schema in sync with implementation in src/tools/fileOperations.ts
    forceFullContent: {
      required: false,
      type: 'boolean',
      description: 'Force reading full content without consolidation',
      defaultValue: false
    },
    focusAreas: {
      required: false,
      type: 'array',
      description: 'Specific areas to focus on when consolidating (hints)'
    }
  },
  
  list_directory: {
    dirPath: {
      required: true,
      type: 'string',
      description: 'The directory path to list'
    },
    recursive: {
      required: false,
      type: 'boolean',
      description: 'Whether to list recursively',
      defaultValue: false
    },
    // Keep schema in sync with implementation in src/tools/fileOperations.ts
    includeHidden: {
      required: false,
      type: 'boolean',
      description: 'Include hidden files (dotfiles) in results',
      defaultValue: false
    },
    maxFiles: {
      required: false,
      type: 'number',
      description: 'Maximum number of entries/files to return',
      defaultValue: 1000
    }
  },
  
  file_edit: {
    operation: { required: true, type: 'string', description: "'write' | 'replace' | 'patch'" },
    filePath: { required: false, type: 'string', description: 'Path to the file' },
    // Common
    createDirs: { required: false, type: 'boolean', description: 'Create parent directories', defaultValue: false },
    atomic: { required: false, type: 'boolean', description: 'Atomic write via temp + rename', defaultValue: false },
    ifMatchHash: { required: false, type: 'string', description: 'Precondition CAS hash' },
    diagnostics: { required: false, type: 'boolean', description: 'Run diagnostics after write', defaultValue: true },
    idempotent: { required: false, type: 'boolean', description: 'Treat already-applied changes as success', defaultValue: false },
    audit: { required: false, type: 'object', description: 'Optional audit metadata' },
    // Write
    content: { required: false, type: 'string', description: 'Content for write/append/prepend' },
    mode: { required: false, type: 'string', description: "'overwrite' | 'append' | 'prepend'", defaultValue: 'overwrite' },
    // Replace
    pattern: { required: false, type: 'string', description: 'Search pattern' },
    replacement: { required: false, type: 'string', description: 'Replacement text' },
    useRegex: { required: false, type: 'boolean', description: 'Interpret pattern/before as regex', defaultValue: false },
    scope: { required: false, type: 'string', description: "'one' | 'all'", defaultValue: 'one' },
    // Patch
    patchFormat: { required: false, type: 'string', description: "'anchored' | 'unified_diff'", defaultValue: 'anchored' },
    before: { required: false, type: 'string', description: 'Exact before segment (anchored patch)' },
    after: { required: false, type: 'string', description: 'Replacement segment (anchored patch)' },
    diff: { required: false, type: 'string', description: 'Unified diff string' },
    fuzzy: { required: false, type: 'boolean', description: 'Allow best-effort context matching', defaultValue: false },
    // Batch
    ops: { required: false, type: 'array', description: 'Batch operations' },
    transaction: { required: false, type: 'string', description: "'all_or_nothing' | 'best_effort'", defaultValue: 'best_effort' },
  },
  
  create_directory: {
    dirPath: {
      required: true,
      type: 'string',
      description: 'The directory path to create'
    },
    recursive: {
      required: false,
      type: 'boolean',
      description: 'Create parent directories if they don\'t exist',
      defaultValue: true
    }
  },
  
  delete_file: {
    filePath: {
      required: true,
      type: 'string',
      description: 'The path to the file to delete'
    }
  },
  
  move_file: {
    sourcePath: {
      required: true,
      type: 'string',
      description: 'The source file path'
    },
    destinationPath: {
      required: true,
      type: 'string',
      description: 'The destination file path'
    }
  },
  
  copy_file: {
    sourcePath: {
      required: true,
      type: 'string',
      description: 'The source file path'
    },
    destinationPath: {
      required: true,
      type: 'string',
      description: 'The destination file path'
    }
  },
  
  find_files: {
    pattern: {
      required: true,
      type: 'string',
      description: 'File name pattern to search for'
    },
    directory: {
      required: false,
      type: 'string',
      description: 'Directory to search in',
      defaultValue: '.'
    },
    recursive: {
      required: false,
      type: 'boolean',
      description: 'Search recursively',
      defaultValue: true
    }
  },
  
  get_file_info: {
    filePath: {
      required: true,
      type: 'string',
      description: 'The path to the file to get info for'
    }
  },
  
  execute_git_command: {
    command: {
      required: true,
      type: 'string',
      description: 'Git command to execute'
    },
    args: {
      required: false,
      type: 'array',
      description: 'Additional arguments for the git command',
      defaultValue: []
    }
  },
  
  // edit_file/write_file/apply_text_patch consolidated into file_edit
  
  execute_code: {
    code: {
      required: true,
      type: 'string',
      description: 'Code to execute'
    },
    language: {
      required: false,
      type: 'string',
      description: 'Programming language',
      defaultValue: 'javascript'
    }
  },
  
  analyze_project: {
    projectPath: {
      required: false,
      type: 'string',
      description: 'Path to project to analyze',
      defaultValue: '.'
    },
    includeTests: {
      required: false,
      type: 'boolean',
      description: 'Include test files in analysis',
      defaultValue: false
    }
  },
  
  git_operation: {
    operation: {
      required: true,
      type: 'string',
      description: 'Git operation to perform (status, add, commit, etc.)'
    },
    args: {
      required: false,
      type: 'array',
      description: 'Additional arguments for git command',
      defaultValue: []
    }
  },
  
  set_working_directory: {
    dirPath: {
      required: true,
      type: 'string',
      description: 'Path to set as working directory'
    }
  },
  
  get_working_directory: {} as Record<string, ToolParameterConfig>,
  
  get_project_context: {
    includeTests: {
      required: false,
      type: 'boolean',
      description: 'Include test files in context',
      defaultValue: false
    },
    maxDepth: {
      required: false,
      type: 'number',
      description: 'Maximum directory depth to scan',
      defaultValue: 3
    }
  }
};

// Add schemas for composite/batch tools to remove validation warnings
FILE_OPERATION_TOOL_SCHEMAS['read_files'] = {
  filePaths: { required: true, type: 'array', description: 'List of file paths to read' },
  pattern: { required: false, type: 'string', description: 'Optional regex pattern to filter lines' },
  contextLines: { required: false, type: 'number', description: 'Context lines around matches', defaultValue: 1 },
  forceFullContent: { required: false, type: 'boolean', description: 'Disable consolidation for full reads', defaultValue: false },
  maxFiles: { required: false, type: 'number', description: 'Limit number of files to process' },
  regions: { required: false, type: 'array', description: 'Explicit regions to extract: [{filePath,startLine,endLine}]' },
  windowBefore: { required: false, type: 'number', description: 'Lines before match for windows', defaultValue: 10 },
  windowAfter: { required: false, type: 'number', description: 'Lines after match for windows', defaultValue: 30 },
  summarizeChunks: { required: false, type: 'boolean', description: 'Summarize each chunk via Gemini', defaultValue: false },
  maxCharsPerChunk: { required: false, type: 'number', description: 'Cap chunk size in characters', defaultValue: 4000 },
};

// read_files aliases are appended after PARAMETER_ALIASES declaration below

/**
 * Parameter definitions for other common tools
 */
export const WEB_TOOL_SCHEMAS: Record<string, Record<string, ToolParameterConfig>> = {
  web_search: {
    query: {
      required: true,
      type: 'string',
      description: 'Search query'
    },
    maxResults: {
      required: false,
      type: 'number',
      description: 'Maximum number of results',
      defaultValue: 10
    }
  },
  
  fetch_web_content: {
    url: {
      required: true,
      type: 'string',
      description: 'URL to fetch content from'
    },
    chunkSize: {
      required: false,
      type: 'number',
      description: 'Size of content chunks',
      defaultValue: 5000
    }
  }
};

/**
 * Combined schema registry
 */
export const TOOL_SCHEMAS: Record<string, Record<string, ToolParameterConfig>> = {
  ...FILE_OPERATION_TOOL_SCHEMAS,
  ...WEB_TOOL_SCHEMAS
};

/**
 * Common parameter name aliases and corrections
 */
export const PARAMETER_ALIASES: Record<string, Record<string, string>> = {
  grep_code: {
    'query': 'pattern',
    'search': 'pattern',
    'term': 'pattern',
    'directory': 'searchPath',
    'folder': 'searchPath',
    'path': 'searchPath',
    'extensions': 'fileTypes',
    'types': 'fileTypes'
  },
  
  read_file: {
    'file': 'filePath',
    'path': 'filePath',
    'filename': 'filePath'
  },
  
  read_files: {
    'files': 'filePaths',
    'paths': 'filePaths',
    'list': 'filePaths',
    'regex': 'pattern',
    'before': 'windowBefore',
    'after': 'windowAfter'
  },
  
  list_directory: {
    'directory': 'dirPath',
    'path': 'dirPath',
    'folder': 'dirPath',
    'dir': 'dirPath'
  },
  
  file_edit: {
    'file': 'filePath',
    'path': 'filePath',
    'filename': 'filePath',
    'data': 'content',
    'text': 'content'
  },
  
  web_search: {
    'search': 'query',
    'term': 'query',
    'q': 'query'
  }
};

/**
 * Get parameter schema for a tool
 */
export function getToolSchema(toolName: string): Record<string, ToolParameterConfig> | undefined {
  return TOOL_SCHEMAS[toolName];
}

/**
 * Get parameter aliases for a tool
 */
export function getToolAliases(toolName: string): Record<string, string> | undefined {
  return PARAMETER_ALIASES[toolName];
}

/**
 * Suggest parameter corrections based on common mistakes
 */
export function suggestParameterCorrections(
  toolName: string,
  invalidParams: string[]
): string[] {
  const schema = getToolSchema(toolName);
  const aliases = getToolAliases(toolName);
  const validParams = schema ? Object.keys(schema) : [];
  const suggestions: string[] = [];
  
  for (const invalid of invalidParams) {
    // Check direct alias mapping
    if (aliases && aliases[invalid]) {
      suggestions.push(`'${invalid}' should be '${aliases[invalid]}'`);
      continue;
    }
    
    // Check for similar names
    const similar = validParams.find(valid => 
      // Exact substring match
      valid.toLowerCase().includes(invalid.toLowerCase()) ||
      invalid.toLowerCase().includes(valid.toLowerCase()) ||
      // Levenshtein distance of 1-2 characters
      calculateSimilarity(valid.toLowerCase(), invalid.toLowerCase()) > 0.6
    );
    
    if (similar) {
      suggestions.push(`'${invalid}' should be '${similar}'`);
    }
  }
  
  return suggestions;
}

/**
 * Simple similarity calculation (normalized Levenshtein distance)
 */
function calculateSimilarity(a: string, b: string): number {
  const matrix: number[][] = [];
  const aLen = a.length;
  const bLen = b.length;
  
  if (aLen === 0) return bLen === 0 ? 1 : 0;
  if (bLen === 0) return 0;
  
  // Initialize matrix
  for (let i = 0; i <= aLen; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= bLen; j++) {
    matrix[0][j] = j;
  }
  
  // Fill matrix
  for (let i = 1; i <= aLen; i++) {
    for (let j = 1; j <= bLen; j++) {
      if (a[i - 1] === b[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,    // deletion
          matrix[i][j - 1] + 1,    // insertion
          matrix[i - 1][j - 1] + 1 // substitution
        );
      }
    }
  }
  
  const distance = matrix[aLen][bLen];
  const maxLen = Math.max(aLen, bLen);
  return 1 - (distance / maxLen);
}
