import { Response } from 'express';
import { getTraceProcessor } from './traceProcessor';
import { progressBus } from './progressBus';
import { setContextValue } from './requestContext';
import { tokenBus } from './tokenBus';
import { sessionEventBus } from './sessionEventBus';
import type { AgentEvent, AgentEventType } from '../types/agentEvents';

// Vercel AI SDK compatible types
export type StreamResult = AsyncIterable<any> & {
  completed?: Promise<any>;
  state?: any;
  [Symbol.asyncIterator]: () => AsyncIterator<any>;
};

export class EnhancedStreamHandler {
  private res: Response;
  public quickComplete(reason: string = 'Stopped') {
    try {
      this.sendEvent(this.createEvent('complete', { message: reason, agent: this.currentAgent }));
    } catch {}
    try {
      if (this.unsubscribeToken) this.unsubscribeToken();
      if (this.unsubscribeProgress) this.unsubscribeProgress();
    } catch {}
    try {
      if (this.keepAliveTimer) { clearInterval(this.keepAliveTimer); this.keepAliveTimer = undefined; }
    } catch {}
    try { this.res.end(); } catch {}
  }
  private eventHistory: AgentEvent[] = [];
  private currentAgent: string = 'Dante';
  private isThinking: boolean = false;
  private keepAliveTimer?: NodeJS.Timeout;
  private lastActivityAt: number = Date.now();
  private sessionId?: string;
  private unsubscribeProgress?: () => void;
  private unsubscribeToken?: () => void;
  // When tools are called, buffer textual output until results arrive
  private pendingToolCalls: number = 0;
  private bufferedOutput: string = '';
  private postRunSummarySent: boolean = false;
  // Best-effort record of model used for this stream (for pricing/usage)
  private lastModelUsed: string | undefined;
  // Buffer and flags for reasoning/thinking streaming
  private reasoningBuffer: string = '';
  private hasEmittedReasoning: boolean = false;

  constructor(res: Response, context?: { sessionId?: string }) {
    this.res = res;
    this.sessionId = context?.sessionId;
    if (this.sessionId) {
      sessionEventBus.reset(this.sessionId);
    }
    this.setupSSEHeaders();
    this.startKeepAlive();
    this.subscribeToProgress();
    this.subscribeToTokenBus();
  }

  private setupSSEHeaders() {
    this.res.setHeader('Content-Type', 'text/event-stream');
    this.res.setHeader('Cache-Control', 'no-cache');
    this.res.setHeader('Connection', 'keep-alive');
    this.res.setHeader('X-Accel-Buffering', 'no'); // Disable Nginx buffering
  }

  private subscribeToProgress() {
    const listener = (evt: any) => {
      if (this.sessionId && evt.sessionId && evt.sessionId !== this.sessionId) return;
      // Emit a structured progress event for the UI stores
      this.sendEvent(this.createEvent('progress', {
        agent: this.currentAgent,
        progress: evt.progress,
        stage: evt.stage,
        detail: evt.detail,
      } as any));
    };
    progressBus.on('progress', listener);
    this.unsubscribeProgress = () => progressBus.off('progress', listener);
  }

  // Subscribe to global token usage events and forward as SSE token_update
  private subscribeToTokenBus() {
    const listener = (evt: any) => {
      try {
        // Optional session scoping: only forward if session matches (when provided)
        if (this.sessionId && evt?.sessionId && evt.sessionId !== this.sessionId) return;
        const used = Number(evt?.used || 0);
        if (!used || used <= 0) return;

        // Prefer model provided by event, otherwise last known model
        const model = (typeof evt?.model === 'string' && evt.model.length > 0)
          ? evt.model
          : this.lastModelUsed;

        // Forward as SSE token_update using existing helper
        this.tokenUpdate({ total: used }, model);
      } catch {
        // ignore forwarding errors
      }
    };
    tokenBus.on('token_update', listener);
    this.unsubscribeToken = () => tokenBus.off('token_update', listener);
  }

  private formatAgentName(name?: string): string {
    const trimmed = (name || '').trim();
    if (!trimmed) return 'Agent';
    const withSpaces = trimmed
      .replace(/Agent$/g, ' Agent')
      .replace(/([a-z0-9])([A-Z])/g, '$1 $2')
      .replace(/\s+/g, ' ')
      .trim();
    return withSpaces || 'Agent';
  }

  private sendEvent(event: AgentEvent) {
    this.eventHistory.push(event);
    this.lastActivityAt = Date.now();

    try {
      sessionEventBus.record(this.sessionId, event);
    } catch (error) {
      console.warn('Failed to record session event:', error);
    }

    // Send as SSE format
    this.res.write(`event: ${event.type}\n`);
    this.res.write(`data: ${JSON.stringify(event)}\n\n`);

    // Flush the response to ensure immediate delivery
    if ((this.res as any).flush) {
      (this.res as any).flush();
    }
  }

  private startKeepAlive() {
    // Periodically send an SSE comment line so intermediaries/browsers
    // don't time out during long tool operations with no output.
    // Uses comment syntax ": ..." which our UI parser ignores safely.
    const INTERVAL_MS = 2000; // check every 2s
    const IDLE_THRESHOLD_MS = 3000; // send keepalive if idle >3s
    this.keepAliveTimer = setInterval(() => {
      const now = Date.now();
      if (now - this.lastActivityAt > IDLE_THRESHOLD_MS) {
        try {
          // Emit as a structured heartbeat event to traverse proxies reliably
          this.sendEvent(this.createEvent('trace_event', {
            type: 'heartbeat',
            message: 'keepalive',
            agent: this.currentAgent,
          }));
          // Update last activity so we don't spam if still idle
          this.lastActivityAt = now;
        } catch (e) {
          // If writing fails, stop the timer to avoid noisy logs
          if (this.keepAliveTimer) clearInterval(this.keepAliveTimer);
        }
      }
    }, INTERVAL_MS);
  }

  // Emit token usage updates for the UI Resource Monitor
  public tokenUpdate(tokens: { input?: number; output?: number; total?: number }, model?: string) {
    try {
      const used = typeof tokens.total === 'number'
        ? tokens.total
        : (tokens.input || 0) + (tokens.output || 0);

      const modelId = model || this.lastModelUsed || 'unknown-model';

      if (typeof tokens.input === 'number' && tokens.input > 0) {
        this.sendEvent(this.createEvent('token_update', {
          agent: this.currentAgent,
          tokens: { used: tokens.input, model: modelId, isOutput: false },
        } as any));
      }
      if (typeof tokens.output === 'number' && tokens.output > 0) {
        this.sendEvent(this.createEvent('token_update', {
          agent: this.currentAgent,
          tokens: { used: tokens.output, model: modelId, isOutput: true },
        } as any));
      }
      if (
        (typeof tokens.input !== 'number' || tokens.input === 0) &&
        (typeof tokens.output !== 'number' || tokens.output === 0) &&
        typeof tokens.total === 'number' && tokens.total > 0
      ) {
        this.sendEvent(this.createEvent('token_update', {
          agent: this.currentAgent,
          tokens: { used: tokens.total, model: modelId, isOutput: true },
        } as any));
      }
    } catch (e) {
      console.warn('Failed to emit token_update:', e);
    }
  }

  // Build a short fallback assistant message when the model produced
  // only tool events but no natural-language text. Keeps the UI from
  // appearing empty when tools completed successfully.
  private buildFallbackText(): string {
    try {
      // Find the last few tool_result events
      const recentToolResults = this.eventHistory
        .filter(e => e.type === 'tool_result')
        .slice(-3);

      if (recentToolResults.length === 0) {
        // Try tool_call events as a minimal hint
        const lastCall = this.eventHistory.slice().reverse().find(e => e.type === 'tool_call');
        if (lastCall?.data?.tool) {
          return `Used ${lastCall.data.tool} to gather information. Preparing summary...`;
        }
        return 'Completed tool execution. Preparing summary...';
      }

      const lines: string[] = [];
      for (const ev of recentToolResults) {
        const tool = (ev.data as any)?.tool || 'tool';
        const raw = (ev.data as any)?.result;
        let snippet = '';
        if (typeof raw === 'string') {
          // Try to parse JSON results for a nicer hint; fall back to a safe slice
          try {
            const obj = JSON.parse(raw);
            const keys = Object.keys(obj || {});
            if (keys.length > 0) {
              snippet = `keys: ${keys.slice(0, 5).join(', ')}`;
            }
          } catch {
            snippet = raw.replace(/\s+/g, ' ').slice(0, 200);
          }
        }
        lines.push(`• ${tool} completed${snippet ? ` (${snippet})` : ''}`);
      }

      return `I finished running tools and have results:\n${lines.join('\n')}\nSynthesizing an answer...`;
    } catch {
      return 'Completed tool execution. Preparing summary...';
    }
  }

  // Create a concise post-run synthesis summarizing tool usage and outcomes,
  // plus suggested next actions. Designed to be short and always safe.
  private buildPostRunSynthesis(): string {
    try {
      // Pair tool calls and results in order
      type Call = { tool: string; args?: any; ts: number };
      type Pair = { tool: string; args?: any; result?: string };
      const pending: Call[] = [];
      const timeline: Pair[] = [];

      for (const ev of this.eventHistory) {
        if (ev.type === 'tool_call') {
          const tool = (ev.data as any)?.tool || 'tool';
          pending.push({ tool, args: (ev.data as any)?.args, ts: Date.parse(ev.timestamp) || Date.now() });
        }
        if (ev.type === 'tool_result') {
          const tool = (ev.data as any)?.tool || 'tool';
          const res = (ev.data as any)?.result;
          // Find the most recent pending call for this tool
          let idx = -1;
          for (let i = pending.length - 1; i >= 0; i--) {
            if (pending[i].tool === tool) { idx = i; break; }
          }
          if (idx >= 0) {
            const call = pending.splice(idx, 1)[0];
            timeline.push({ tool, args: call.args, result: res });
          } else {
            timeline.push({ tool, result: res });
          }
        }
      }

      if (timeline.length === 0) {
        return 'No tool outputs to summarize.';
      }

      // Helper: trim and normalize
      const clean = (s: string) => s.replace(/\s+/g, ' ').trim();
      const preview = (s: string, n: number = 220) => clean(s).slice(0, n);

      const usedTools = Array.from(new Set(timeline.map(t => t.tool)));

      // Extract observations for all tool results (trimmed per item)
      const observations: string[] = [];
      for (const item of timeline) {
        let obs = '';
        if (typeof item.result === 'string') {
          // Try to parse JSON for a more meaningful observation
          try {
            const obj = JSON.parse(item.result);
            if (obj && typeof obj === 'object') {
              // Prefer common fields
              const fields: string[] = [];
              if (typeof obj.success === 'boolean') fields.push(`success: ${obj.success}`);
              if (typeof obj.matches === 'number') fields.push(`matches: ${obj.matches}`);
              if (typeof obj.totalProcessed === 'number') fields.push(`processed: ${obj.totalProcessed}`);
              if (obj.analysis && obj.analysis.files && typeof obj.analysis.files === 'object') {
                const keys = Object.keys(obj.analysis.files).slice(0, 4).join(', ');
                fields.push(`file types: ${keys}`);
              }
              obs = fields.length > 0 ? fields.join(', ') : preview(item.result);
            } else {
              obs = preview(item.result);
            }
          } catch {
            obs = preview(item.result);
          }
        }
        observations.push(`- ${item.tool}${obs ? ` → ${obs}` : ''}`);
      }

      // Suggested next steps (simple heuristics)
      const nextSteps: string[] = [];
      const fullText = timeline.map(r => String(r.result || '')).join(' ').toLowerCase();
      if (fullText.includes('error') || fullText.includes('failed') || fullText.includes('success: false')) {
        nextSteps.push('- Investigate failing tool outputs; retry with corrected parameters.');
      }
      if (fullText.includes('matches: 0') || fullText.includes('results: []')) {
        nextSteps.push('- Refine search terms or broaden file scope.');
      }
      if (nextSteps.length === 0) {
        nextSteps.push('- If more detail is needed, run a targeted follow-up tool call.');
      }

      // Agent-specific checklist
      const checklist = this.buildAgentChecklist(this.currentAgent || 'Agent', timeline);

      const header = 'Post-run synthesis';
      const toolsLine = `Tools used: ${usedTools.join(', ')}`;
      const obsHeader = 'Tool results overview:';
      const stepsHeader = 'Suggested next steps:';
      const checklistHeader = 'Completion checklist:';

      // Guard against excessively large payloads
      const MAX_TOTAL_CHARS = 12000;
      let lines = [header, toolsLine, obsHeader, ...observations, stepsHeader, ...nextSteps, checklistHeader, ...checklist];
      let total = lines.reduce((n, l) => n + l.length + 1, 0);
      if (total > MAX_TOTAL_CHARS) {
        // Trim observations first, preserving start/end
        const head = observations.slice(0, 40);
        const tail = observations.slice(-40);
        const trimmedObs = head.concat(['… (trimmed for length) …'], tail);
        lines = [header, toolsLine, obsHeader, ...trimmedObs, stepsHeader, ...nextSteps, checklistHeader, ...checklist];
      }
      return lines.join('\n');
    } catch {
      return 'Post-run synthesis unavailable.';
    }
  }

  // Agent-specific completion checklist: provides a compact, role-tailored
  // list of items that verify work quality and completeness.
  private buildAgentChecklist(agentName: string, timeline: Array<{ tool: string; args?: any; result?: string }>): string[] {
    const name = (agentName || '').toLowerCase();
    const used = new Set(timeline.map(t => t.tool));
    const has = (tool: string) => used.has(tool);

    if (name.includes('task orchestrator')) {
      return [
        '- Confirmed chunking or direct path selected',
        `- Tools executed: ${Array.from(used).join(', ')}`,
        '- Aggregated findings into coherent answer',
        '- Noted follow-up steps or retries if needed'
      ];
    }
    if (name.includes('research')) {
      return [
        '- Summarized key findings clearly',
        '- Cited sources or file references when applicable',
        '- Listed gaps/open questions',
        '- Proposed next queries or angles'
      ];
    }
    if (name.includes('debug')) {
      return [
        '- Repro steps described',
        '- Root-cause hypothesis stated',
        '- Impacted files/lines identified',
        '- Fix approach or patch outline provided',
        '- Validation steps/tests suggested'
      ];
    }
    if (name.includes('codegeneration') || name.includes('code generation')) {
      return [
        '- Files changed listed with paths',
        '- Compiles/typechecks locally (if applicable)',
        '- Tests added/updated (if applicable)',
        '- Edge cases and error handling addressed',
        '- Follow-up manual steps (if any) noted'
      ];
    }
    if (name.includes('planning')) {
      return [
        '- Objective clarified',
        '- Milestones and tasks enumerated',
        '- Dependencies/risks identified',
        '- Time/effort estimation provided'
      ];
    }
    if (name.includes('security')) {
      return [
        '- Findings summarized by severity',
        '- Affected components listed',
        '- Concrete remediation steps provided',
        '- Verification/monitoring steps outlined'
      ];
    }
    if (name.includes('googleworkspace') || name.includes('google workspace')) {
      return [
        '- Auth status surfaced (authUrl if needed)',
        '- Operation summary (calendar/gmail/drive)',
        '- Items found/changed counts',
        '- Any access limitations or next auth steps'
      ];
    }
    // Generic fallback
    return [
      '- Summarized all tool results',
      '- Clarified outcome and next steps',
      '- Noted any errors and recovery plans'
    ];
  }

  private createEvent(type: AgentEventType, data: any): AgentEvent {
    return {
      type,
      timestamp: new Date().toISOString(),
      data,
    };
  }

  // Extract provider-agnostic reasoning/thinking text from stream state
  private extractReasoningFromState(state: any): string | undefined {
    try {
      if (!state) return;
      const resp = state.response || state;
      const candidates = [
        resp?.reasoning,
        resp?.thinking,
        resp?.providerMetadata?.google?.reasoning,
        resp?.providerMetadata?.openai?.reasoning,
        resp?.providerMetadata?.openai?.reasoningTokens,
        resp?.providerMetadata?.openai?.thinking,
        resp?.providerMetadata?.anthropic?.reasoning,
        resp?.providerMetadata?.anthropic?.thinking,
        resp?.providerMetadata?.deepseek?.reasoning,
        resp?.providerMetadata?.mistral?.reasoning,
        resp?.providerMetadata?.meta?.reasoning,
        state?.reasoning,
        state?.thinking,
        state?.output?.[0]?.content?.reasoning,
        state?.output?.[0]?.content?.thinking
      ];
      for (const c of candidates) {
        if (typeof c === 'string' && c.trim().length > 0) return c;
      }
    } catch {}
    return undefined;
  }

  // Emit one-time reasoning summary if available in stream state
  private maybeEmitReasoningFromState(stream: any) {
    try {
      if (this.hasEmittedReasoning) return;
      const state = (stream as any)?.state || (stream as any);
      const r = this.extractReasoningFromState(state);
      if (typeof r === 'string' && r.trim().length > 0) {
        this.sendEvent(this.createEvent('message', { message: '', reasoning: r, agent: this.currentAgent }));
        this.hasEmittedReasoning = true;
      }
    } catch {}
  }

  startThinking(message: string = 'Processing your request...') {
    this.isThinking = true;
    // Send immediate heartbeat for UI responsiveness
    this.sendEvent(this.createEvent('trace_event', {
      type: 'heartbeat',
      message: 'thinking_heartbeat',
      agent: this.currentAgent,
    }));
    this.sendEvent(this.createEvent('thinking', { message, agent: this.currentAgent }));
  }

  stopThinking() {
    this.isThinking = false;
  }

  agentSwitch(agentName: string) {
    const formatted = this.formatAgentName(agentName);
    if (formatted === this.currentAgent) return;
    this.currentAgent = formatted;
    setContextValue('agent', agentName);
    // Send immediate heartbeat for UI responsiveness
    this.sendEvent(this.createEvent('trace_event', {
      type: 'heartbeat',
      message: 'agent_switch_heartbeat',
      agent: this.currentAgent,
    }));
    this.sendEvent(this.createEvent('agent_switch', {
      agent: this.currentAgent,
      message: `Switched to ${this.currentAgent}`,
    }));
  }

  // Public: emit an immediate heartbeat with a tag. Useful at request start.
  public heartbeat(tag: string = 'heartbeat') {
    try {
      this.sendEvent(this.createEvent('trace_event', {
        type: 'heartbeat',
        message: tag,
        agent: this.currentAgent,
      }));
    } catch {}
  }

  toolCall(toolName: string, args?: any) {
    if (!toolName) return;

    console.log('📤 Sending tool call event for:', toolName);

    // Send immediate heartbeat to ensure UI responsiveness
    this.sendEvent(this.createEvent('trace_event', {
      type: 'heartbeat',
      message: 'tool_call_heartbeat',
      agent: this.currentAgent,
    }));

    // Don't buffer for streaming tools - they handle their own output
    // Mark that we are waiting on a tool result
    // this.pendingToolCalls++; // Temporarily disable buffering

    // Map tool names to user-friendly descriptions
    const toolDescriptions: Record<string, string> = {
      'web_search': 'Searching the web',
      'read_file': 'Reading file',
      'file_edit': 'Editing file',
      'list_directory': 'Browsing directory',
      'execute_code': 'Executing code',
      'analyze_project': 'Analyzing project structure',
      'git_operation': 'Performing git operation',
      'grep_code': 'Searching codebase (shell-backed)',
      'search_and_inspect': 'Searching and inspecting code chunks',
      'guided_code_search': 'Guided code search',
      'transfer_to_File_Analysis_Worker': 'Delegating to file analysis worker',
      'transfer_to_Synthesis_Worker': 'Synthesizing results',
      'transfer_to_Task_Orchestrator': 'Orchestrating complex task',
    };

    const description = toolDescriptions[toolName] || `Using ${toolName}`;

    // Send specific event based on tool type
    if (toolName.includes('search') || toolName === 'grep_code') {
      this.sendEvent(this.createEvent('search', {
        tool: toolName,
        message: description,
        args,
        agent: this.currentAgent,
      }));
    } else if (toolName.includes('file') || toolName === 'list_directory') {
      let eventType = 'file_read';
      if (toolName.includes('read')) eventType = 'file_read';
      else if (toolName.includes('write')) eventType = 'file_write';
      else if (toolName === 'file_edit') eventType = 'file_write';
      this.sendEvent(this.createEvent(eventType as any, {
        tool: toolName,
        message: description,
        args,
        agent: this.currentAgent,
      }));
    } else if (toolName === 'execute_code') {
      this.sendEvent(this.createEvent('code_execution', {
        tool: toolName,
        message: description,
        args,
        agent: this.formatAgentName(this.currentAgent),
      }));
    } else if (toolName === 'web_search') {
      this.sendEvent(this.createEvent('web_search', {
        tool: toolName,
        message: description,
        args,
        agent: this.formatAgentName(this.currentAgent),
      }));
    } else if (toolName.startsWith('transfer_to_')) {
      const targetAgent = toolName.replace('transfer_to_', '').replace(/_/g, ' ');
      this.sendEvent(this.createEvent('handoff', {
        tool: toolName,
        message: description,
        agent: this.formatAgentName(this.currentAgent),
        targetAgent,
      }));
    } else {
      this.sendEvent(this.createEvent('tool_call', {
        tool: toolName,
        message: description,
        args,
        agent: this.formatAgentName(this.currentAgent),
      }));
    }
    // Emit a concise chat update for notable tools to improve UX
    try {
      const msg = this.renderToolCallMessage(toolName, args);
      if (msg) this.sendMessage(msg);
    } catch {}
  }

  toolResult(toolName: string, result: any) {
    this.sendEvent(this.createEvent('tool_result', {
      tool: toolName,
      result: typeof result === 'string' ? result : JSON.stringify(result),
      agent: this.formatAgentName(this.currentAgent),
    }));

    // Decrement pending tool call counter and flush any buffered text
    if (this.pendingToolCalls > 0) {
      this.pendingToolCalls--;
    }
    if (this.pendingToolCalls === 0 && this.bufferedOutput.length > 0) {
      this.res.write(`event: message\n`);
      this.res.write(`data: ${JSON.stringify(this.createEvent('message', { message: this.bufferedOutput, agent: this.formatAgentName(this.currentAgent) }).data)}\n\n`);
      this.bufferedOutput = '';
    }
    // Emit short chat note for notable tool results
    try {
      const msg = this.renderToolResultMessage(toolName, result);
      if (msg) this.sendMessage(msg);
    } catch {}
  }

  // Public helpers for emitting orchestration-specific events when
  // they are produced inside tool results (e.g., delegate_to_agents)
  planCreated(payload: { summary?: string; steps: any[] }) {
    this.sendEvent(this.createEvent('plan_created', {
      ...(payload || {}),
      agent: this.currentAgent,
    }));
  }

  delegationStart(payload: { stepId: string; agent: string; title?: string; description?: string }) {
    const agentName = this.formatAgentName(payload?.agent || this.currentAgent);
    this.agentSwitch(agentName);
    this.sendEvent(this.createEvent('delegation_start', {
      ...(payload || {}),
      agent: agentName,
    }));
  }

  delegationEnd(payload: { stepId: string; agent: string; success: boolean }) {
    const agentName = this.formatAgentName(payload?.agent || this.currentAgent);
    this.sendEvent(this.createEvent('delegation_end', {
      ...(payload || {}),
      agent: agentName,
    }));
  }

  sendMessage(content: string) {
    // Send message directly without buffering for now
    // The buffering logic was preventing streaming after tool calls
    this.sendEvent(this.createEvent('message', {
      message: content,
      agent: this.formatAgentName(this.currentAgent),
    }));
  }

  sendError(error: string | Error) {
    this.sendEvent(this.createEvent('error', {
      message: error instanceof Error ? error.message : error,
      details: error instanceof Error ? error.stack : undefined,
      agent: this.currentAgent,
    }));
  }

  complete(finalMessage?: string) {
    this.sendEvent(this.createEvent('complete', {
      message: finalMessage || 'Task completed',
      agent: this.currentAgent,
    }));
    if (this.keepAliveTimer) {
      clearInterval(this.keepAliveTimer);
      this.keepAliveTimer = undefined;
    }
    if (this.unsubscribeToken) this.unsubscribeToken();
    this.res.end();
  }

  // Helpers: readable tool call/result summaries for chat
  private parseJSONMaybe(v: any): any {
    if (typeof v === 'string') {
      try { return JSON.parse(v); } catch { return undefined; }
    }
    return v;
  }

  private renderToolCallMessage(name: string, args: any): string | null {
    const n = (name || '').toLowerCase();
    const a = this.parseJSONMaybe(args) || {};
    const agent = this.formatAgentName(this.currentAgent);
    if (n === 'web_search' || n === 'enhanced_web_search') {
      const q = a.query || a.q || a.search || '';
      const k = a.maxResults || a.top_k || a.topn || a.limit;
      const engine = Array.isArray(a.searchEngines) ? a.searchEngines.join(', ') : (a.engine || undefined);
      const parts = [`${agent}: searching "${String(q).slice(0, 180)}"`];
      if (k) parts.push(`top ${k}`);
      if (engine) parts.push(`via ${engine}`);
      return `${parts.join(' · ')}\n`;
    }
    if (n === 'web_fetch') {
      const url = a.url || a.href;
      if (url) return `${agent}: opening ${String(url).slice(0, 200)}\n`;
    }
    if (n === 'grep_code') {
      const pattern = a.pattern || a.query || a.searchPattern;
      const paths = a.paths || a.filePaths;
      return `${agent}: scanning code for ${String(pattern || '').slice(0, 160)}${paths ? ' in ' + JSON.stringify(paths).slice(0, 100) : ''}\n`;
    }
    if (n === 'read_file') {
      const fp = a.filePath || a.path;
      if (fp) return `${agent}: reading file ${fp}\n`;
    }
    return null;
  }

  private renderToolResultMessage(name: string, result: any): string | null {
    const n = (name || '').toLowerCase();
    const r = this.parseJSONMaybe(result) || {};
    const agent = this.formatAgentName(this.currentAgent);
    if (n === 'web_search' || n === 'enhanced_web_search') {
      const ok = r.success !== false;
      const results = Array.isArray(r.results) ? r.results : (Array.isArray(r.data) ? r.data : []);
      if (!ok) return `${agent}: search returned no results — refining…\n`;
      const count = results.length;
      if (count === 0) return `${agent}: search returned 0 results — refining…\n`;
      const hosts: string[] = [];
      for (const item of results.slice(0, 3)) {
        const url = (item?.url || item?.link || '').toString();
        try { const u = new URL(url); hosts.push(u.hostname.replace(/^www\./, '')); } catch {}
      }
      const hostLine = hosts.length ? ` (e.g., ${hosts.join(', ')})` : '';
      return `${agent}: found ${count} result${count === 1 ? '' : 's'}${hostLine}\n`;
    }
    if (n === 'web_fetch') {
      const ok = r.success !== false;
      const title = r.title || r.pageTitle || r.metadata?.title;
      if (ok && title) return `${agent}: read "${String(title).slice(0, 140)}"\n`;
      if (!ok) return `${agent}: failed to load page — trying alternatives…\n`;
    }
    return null;
  }

  async handleAgentStream(
    stream: StreamResult | any,
    options?: { finalize?: boolean; onRateLimit?: (reason: string) => Promise<StreamResult | any> }
  ): Promise<any> {
    try {
      // Initialize trace processor for this stream
      const traceProcessor = getTraceProcessor();
      let traceId: string | null = null;
      if (traceProcessor) {
        traceId = traceProcessor.startTrace('Dante Assistant Chat');
        traceProcessor.setEventCallback((traceEvent) => {
          this.sendEvent(this.createEvent('trace_event', {
            ...traceEvent.data,
            traceId
          }));
        });
      }

      this.startThinking();
      if (this.sessionId) {
        setContextValue('sessionId', this.sessionId);
      }
      let buffer = '';
      let lastToolCall: string | null = null;
      let hasReceivedContent = false;

      // Case 1: Vercel AI SDK streamText result exposing textStream (preferred)
      if (stream && (stream as any).textStream && typeof (stream as any).textStream[Symbol.asyncIterator] === 'function') {
        console.log('📡 Handling Vercel AI SDK textStream');
        try {
          for await (const chunk of (stream as any).textStream) {
            if (typeof chunk === 'string' && chunk.length > 0) {
              buffer += chunk;
              hasReceivedContent = true;
              if (this.isThinking) this.stopThinking();
              this.sendMessage(chunk);
              // Try to surface reasoning incrementally when provider updates state
              this.maybeEmitReasoningFromState(stream);
            }
          }
        } catch (streamErr: any) {
          const msg = (streamErr?.message || String(streamErr || '')) as string;
          const lower = msg.toLowerCase();
          const isRateLimit = lower.includes('rate limit') || lower.includes('rate_limit') || lower.includes('429') || lower.includes('tokens per') || lower.includes('tpm');
          if (isRateLimit && options?.onRateLimit) {
            try {
              this.sendEvent(this.createEvent('handoff', {
                message: 'Rate limit encountered during streaming. Switching model…',
                agent: this.currentAgent,
              }));
              const fallbackStream = await options.onRateLimit(msg);
              if (fallbackStream) {
                // Hand over to fallback stream handling
                return await this.handleAgentStream(fallbackStream, { finalize: options?.finalize });
              }
            } catch (e) {
              // If fallback fails, proceed to normal error handling below
            }
          }
          throw streamErr;
        }

        // Remember model if present on stream state
        try {
          const modelFromState = (stream as any).state?.model || (stream as any).state?.response?.model;
          if (typeof modelFromState === 'string' && modelFromState.length > 0) {
            this.lastModelUsed = modelFromState;
          }
        } catch {}

        // If no textual chunks arrived, try alternative extraction paths
        if (!hasReceivedContent) {
          console.log('ℹ️ No text chunks received; attempting fallback extraction');

          // 1) Try falling back to structured event stream if available
          try {
            const full = (stream as any).fullStream;
            if (full && typeof full[Symbol.asyncIterator] === 'function') {
              console.log('📡 Falling back to fullStream event parsing');
              for await (const event of full) {
                const eventAsAny = event as any;
                if (eventAsAny?.type === 'raw_model_stream_event') {
                  if (eventAsAny.data?.type === 'output_text_delta') {
                    const text = eventAsAny.data.delta;
                    if (text) {
                      buffer += text;
                      hasReceivedContent = true;
                      if (this.isThinking) this.stopThinking();
                      this.sendMessage(text);
                    }
                  } else if (eventAsAny.data?.type === 'output_text') {
                    const text = eventAsAny.data.text;
                    if (text) {
                      buffer += text;
                      hasReceivedContent = true;
                      if (this.isThinking) this.stopThinking();
                      this.sendMessage(text);
                    }
                  } else if (eventAsAny.data?.type === 'response.completed') {
                    // Some providers include usage on the completion event
                    try {
                      const usage = eventAsAny.data?.response?.usage || eventAsAny.data?.usage;
                      const model = eventAsAny.data?.response?.model || eventAsAny.data?.model;
                      if (usage) {
                        const input = usage.inputTokens ?? usage.input ?? usage.promptTokens ?? undefined;
                        const output = usage.outputTokens ?? usage.output ?? usage.completionTokens ?? undefined;
                        const total = usage.totalTokens ?? usage.total ?? ((input || 0) + (output || 0));
                        this.lastModelUsed = typeof model === 'string' ? model : (this.lastModelUsed || undefined);
                        this.tokenUpdate({ input, output, total }, this.lastModelUsed);
                      }
                    } catch {}
                  }
                } else if (eventAsAny?.type === 'run_item_stream_event') {
                  const item = eventAsAny.item;
                  if (item && (item.type === 'text' || item.type === 'message' || item.type === 'assistant_message')) {
                    const textContent = item.text || item.content || item.message;
                    if (textContent && typeof textContent === 'string') {
                      buffer += textContent;
                      hasReceivedContent = true;
                      if (this.isThinking) this.stopThinking();
                      this.sendMessage(textContent);
                    }
                  }
                }
              }
            }
          } catch (e) {
            console.warn('fullStream fallback parse error:', e);
          }

          // 2) If still nothing, ensure the underlying stream is fully consumed
          if (!hasReceivedContent) {
            try {
              if (typeof (stream as any).consumeStream === 'function') {
                await (stream as any).consumeStream();
              } else if ((stream as any).completed && typeof (stream as any).completed.then === 'function') {
                await (stream as any).completed;
              }
            } catch {
              // ignore
            }
          }

          // 3) Attempt to read consolidated text
          try {
            const maybeText: any = (stream as any).text;
            let finalText = '';
            if (typeof maybeText === 'function') {
              // Some SDK versions expose text() as a function
              finalText = await maybeText();
            } else if (maybeText && typeof maybeText.then === 'function') {
              // Others expose text as a promise
              finalText = await maybeText;
            }

            if (finalText && typeof finalText === 'string' && finalText.length > 0) {
              buffer = finalText;
              if (this.isThinking) this.stopThinking();
              this.sendMessage(finalText);
              hasReceivedContent = true;
            }
          } catch {
            // Fall back to state inspection
            const stateText = (stream as any).state?.output?.[0]?.content;
            if (stateText) {
              const text = typeof stateText === 'string' ? stateText : JSON.stringify(stateText);
              buffer = text;
              if (this.isThinking) this.stopThinking();
              this.sendMessage(text);
              hasReceivedContent = true;
            }
          }

          // 4) As a last resort, generate a short summary from tool events
          if (!hasReceivedContent) {
            const fallback = this.buildFallbackText();
            buffer = fallback;
            if (this.isThinking) this.stopThinking();
            this.sendMessage(fallback);
            hasReceivedContent = true;
          }
        }

        // Best-effort: emit usage if exposed on stream.usage
        try {
          const maybeUsage = (stream as any).usage;
          if (maybeUsage) {
            const usage = typeof maybeUsage === 'function' ? await maybeUsage() : await maybeUsage;
            if (usage) {
              const input = usage.inputTokens ?? usage.input ?? usage.promptTokens ?? undefined;
              const output = usage.outputTokens ?? usage.output ?? usage.completionTokens ?? undefined;
              const total = usage.totalTokens ?? usage.total ?? ((input || 0) + (output || 0));
              const modelFromState = (stream as any).state?.model || (stream as any).state?.response?.model;
              if (typeof modelFromState === 'string' && modelFromState.length > 0) {
                this.lastModelUsed = modelFromState;
              }
              this.tokenUpdate({ input, output, total }, this.lastModelUsed);
            }
          }
        } catch {}

        // Ensure provider stream fully settles before returning
        try {
          const completed = (stream as any)?.completed;
          if (completed && typeof completed.then === 'function') {
            await completed;
          }
        } catch {
          // Ignore completion errors here; they will surface via stream events
        }

        // Complete trace (even when not finalizing, so we record the run)
        if (traceProcessor && traceId) {
          traceProcessor.completeTrace(traceId);
        }

        // Only finalize the SSE when explicitly allowed
        if (options?.finalize !== false) {
          if (this.unsubscribeProgress) this.unsubscribeProgress();
          if (this.unsubscribeToken) this.unsubscribeToken();
          this.complete(buffer || 'Task completed successfully');
        }

        return { finalOutput: buffer, events: this.eventHistory };
      }

      // Case 2: Raw chat completion stream (from direct OpenAI/Gemini calls)
      if (stream && typeof stream[Symbol.asyncIterator] === 'function' && !stream.completed) {
        console.log('📡 Handling raw chat completion stream');
        return await this.handleRawChatStream(stream, buffer, hasReceivedContent, traceProcessor, traceId);
      }

      // Check if stream is Agent SDK format
      if (!stream || typeof stream[Symbol.asyncIterator] !== 'function') {
        console.warn('Stream is not iterable, checking for completed state');
        return await this.handleCompletedStream(stream, buffer, hasReceivedContent, traceProcessor, traceId);
      }

      for await (const event of stream) {
        // Check if this is a raw OpenAI completion chunk (from Gemini) or an Agent SDK event
        // Use type assertion to handle the union type properly
        const eventAsAny = event as any;

        if (!eventAsAny.type && eventAsAny.object === 'chat.completion.chunk') {
          // Handle raw OpenAI completion chunks (Gemini streaming)
          const content = eventAsAny.choices?.[0]?.delta?.content;
          if (content) {
            buffer += content;
            hasReceivedContent = true;

            // Stop thinking indicator when we start getting text
            if (this.isThinking) {
              this.stopThinking();
            }

            // Send text in chunks for real-time display
            this.sendMessage(content);
          }

          // Check for completion
          const finishReason = eventAsAny.choices?.[0]?.finish_reason;
          if (finishReason) {
            break; // End of stream
          }

          continue; // Skip the switch statement for raw chunks
        }

        // Debug: log all stream events
        if (event.type !== 'raw_model_stream_event') { // This is generated text / responses
          console.log('Stream event type:', event.type);
        }
        // if (event.type === 'run_item_stream_event') {
        //   console.log('Tool event details:', {
        //     item: event.item,
        //     itemType: event.item?.type,
        //     itemName: event.item?.name,
        //     itemToolName: event.item?.tool_name
        //   });
        // }

        switch (event.type) {
          case 'raw_model_stream_event':
            // Handle reasoning/thinking deltas if present (provider-agnostic heuristic)
            {
              const d = (event.data as any);
              if (d && typeof d.type === 'string') {
                const t = String(d.type).toLowerCase();
                if ((t.includes('reason') || t.includes('think')) && typeof d.delta === 'string') {
                  this.reasoningBuffer += d.delta;
                  this.sendEvent(this.createEvent('message', { message: '', reasoning: this.reasoningBuffer, agent: this.currentAgent }));
                  break;
                }
              }
            }
            // Handle text output
            if ((event.data as any).type === 'output_text_delta') {
              const text = (event.data as any).delta;
              buffer += text;
              hasReceivedContent = true;

              // Stop thinking indicator when we start getting text
              if (this.isThinking) {
                this.stopThinking();
              }

              // Send text in chunks for real-time display
              this.sendMessage(text);
              // Opportunistically emit reasoning if state exposes it mid-stream
              this.maybeEmitReasoningFromState(stream);
            } else if ((event.data as any).type === 'output_text') {
              // Handle complete text output (non-delta)
              const text = (event.data as any).text;
              if (text) {
                buffer += text;
                hasReceivedContent = true;
                if (this.isThinking) {
                  this.stopThinking();
                }
                this.sendMessage(text);
                this.maybeEmitReasoningFromState(stream);
              }
            } else if ((event.data as any).type === 'response.completed') {
              // Try to surface tokens when available in the stream
              try {
                const usage = (event.data as any)?.response?.usage || (event.data as any)?.usage;
                const model = (event.data as any)?.response?.model || (event.data as any)?.model;
                if (usage) {
                  const input = usage.inputTokens ?? usage.input ?? usage.promptTokens ?? undefined;
                  const output = usage.outputTokens ?? usage.output ?? usage.completionTokens ?? undefined;
                  const total = usage.totalTokens ?? usage.total ?? ((input || 0) + (output || 0));
                  this.lastModelUsed = typeof model === 'string' ? model : (this.lastModelUsed || undefined);
                  this.tokenUpdate({ input, output, total }, this.lastModelUsed);
                }
              } catch {}
            }
            break;

          // SafeStreamWrapper simple text chunks
          case 'text': {
            const text = (event as any).content || '';
            if (typeof text === 'string' && text.length > 0) {
              buffer += text;
              hasReceivedContent = true;
              if (this.isThinking) {
                this.stopThinking();
              }
              this.sendMessage(text);
              this.maybeEmitReasoningFromState(stream);
            }
            break;
          }

          case 'plan_created':
          case 'delegation_start':
          case 'delegation_end':
            // Forward orchestration plan/delegation events directly to UI
            this.sendEvent(this.createEvent(event.type as any, {
              ...(event.data || {}),
              agent: event.data?.agent || this.currentAgent,
            }));
            break;

          case 'agent_updated_stream_event':
            // Handle agent switches
            const agentName = event.agent.name;
            if (agentName !== this.currentAgent) {
              this.agentSwitch(agentName);
            }
            break;

          // Signal emitted by SafeStreamWrapper when the underlying stream ends
          case 'stream_complete':
            // Nothing to do; loop will finish naturally
            break;

          case 'error': {
            // Surface wrapped stream errors to UI for visibility
            let msg = 'Stream error';
            try { msg = (event as any)?.error || (event as any)?.message || msg; } catch {}
            this.sendError(String(msg));

            // If this looks like a rate limit and a recovery callback is provided, attempt fallback
            try {
              const lower = String(msg).toLowerCase();
              const isRateLimit = lower.includes('rate limit') || lower.includes('rate_limit') || lower.includes('429') || lower.includes('tokens per') || lower.includes('tpm');
              if (isRateLimit && options?.onRateLimit) {
                this.sendEvent(this.createEvent('handoff', {
                  message: 'Rate limit encountered mid-run. Switching model…',
                  agent: this.currentAgent,
                }));
                const fallbackStream = await options.onRateLimit(String(msg));
                if (fallbackStream) {
                  return await this.handleAgentStream(fallbackStream, { finalize: options?.finalize });
                }
              }
            } catch {}
            break;
          }

          case 'run_item_stream_event':
            // Handle tool calls and text outputs
            const item = event.item as any;

            // Debug log all run item types to understand structure
            if (item && item.type && !['tool_call_item', 'tool_call_output', 'tool_output'].includes(item.type)) {
              console.log('📋 Run item type:', item.type, 'content preview:',
                JSON.stringify(item).substring(0, 200));
            }

            // Check for text output items (assistant messages after tool calls)
            if (item && (item.type === 'text' || item.type === 'message' || item.type === 'assistant_message')) {
              const textContent = item.text || item.content || item.message;
              if (textContent && typeof textContent === 'string') {
                buffer += textContent;
                hasReceivedContent = true;
                if (this.isThinking) {
                  this.stopThinking();
                }
                this.sendMessage(textContent);
                this.maybeEmitReasoningFromState(stream);
                console.log('📝 Streamed text from run_item:', textContent.substring(0, 100));
              }
            // Surface explicit reasoning items if provider emits them
            } else if (item && typeof item.type === 'string' && item.type.toLowerCase().includes('reason')) {
              const rText = item.text || item.content || item.message || item.summary || '';
              if (typeof rText === 'string' && rText.trim()) {
                this.reasoningBuffer += (this.reasoningBuffer ? '\n' : '') + rText;
                this.sendEvent(this.createEvent('message', { message: '', reasoning: this.reasoningBuffer, agent: this.currentAgent }));
                this.hasEmittedReasoning = true;
              }
            } else if (item && item.type === 'tool_call_item') {
              const toolName = item.name || item.tool_name || item.function?.name || item.rawItem?.name || 'unknown_tool';
              const toolArgs = item.arguments || item.args || item.function?.arguments || item.rawItem?.arguments;
              console.log('🔨 Tool call detected:', toolName, 'with args:', toolArgs);
              if (toolName && toolName !== 'unknown_tool') {
                lastToolCall = toolName;
                this.toolCall(toolName, toolArgs);
                setContextValue('tool', toolName);
              }
            } else if (item && (item.type === 'tool_call_output' || item.type === 'tool_output') && lastToolCall) {
              console.log('✅ Tool result for:', lastToolCall, 'result:', item.output || item.result || item.content);
              this.toolResult(lastToolCall, item.output || item.result || item.content);
              lastToolCall = null;
              setContextValue('tool', undefined);
            }
            break;
        }
      }

      // Wait for completion
      await stream.completed;

      // If we haven't received any content through streaming,
      // try to extract from the completed stream state
      if (!hasReceivedContent && (stream as any).state) {
        // Try to get the final output from the stream state
        const finalOutput = (stream as any).state?.output?.[0]?.content;
        if (finalOutput) {
          buffer = typeof finalOutput === 'string' ? finalOutput : JSON.stringify(finalOutput);
          console.log('📝 Extracted non-streamed content from completed state');

          // Stop thinking and send the complete message
          if (this.isThinking) {
            this.stopThinking();
          }
          this.sendMessage(buffer);
          hasReceivedContent = true;
        }
      }

      // Final fallback: if still no content at all, synthesize a tiny summary
      if (!hasReceivedContent) {
        const fallback = this.buildFallbackText();
        buffer = fallback;
        this.sendMessage(fallback);
      }

      // Surface provider reasoning/thinking summary if available (Gemini, GPT-5, etc.)
      try {
        this.maybeEmitReasoningFromState(stream);
      } catch {}

      // Post-run synthesis: if any tools were used, send a short summary
      try {
        const isCodeGen = (this.currentAgent || '').toLowerCase().includes('codegeneration');
        const hadTools = this.eventHistory.some(e => e.type === 'tool_result');
        // Only emit if we didn't already stream human-readable content
        if (!isCodeGen && hadTools && !this.postRunSummarySent && !hasReceivedContent) {
          const synthesis = this.buildPostRunSynthesis();
          if (synthesis && synthesis.length > 0) {
            this.sendMessage(synthesis);
            this.postRunSummarySent = true;
          }
        }
      } catch {}

      // Complete trace
      if (traceProcessor && traceId) {
        traceProcessor.completeTrace(traceId);
      }

      // Send final complete event (unless caller wants to chain another run)
      if (options?.finalize !== false) {
        if (this.unsubscribeProgress) this.unsubscribeProgress();
        if (this.unsubscribeToken) this.unsubscribeToken();
        this.complete(buffer || 'Task completed successfully');
      }

      return {
        finalOutput: buffer,
        events: this.eventHistory,
      };
    } catch (error) {
      this.sendError(error as Error);
      if (this.keepAliveTimer) {
        clearInterval(this.keepAliveTimer);
        this.keepAliveTimer = undefined;
      }
      if (this.unsubscribeProgress) this.unsubscribeProgress();
      if (this.unsubscribeToken) this.unsubscribeToken();
      this.complete('An error occurred');
      throw error;
    }
  }

  private async handleRawChatStream(stream: any, buffer: string, _hasReceivedContent: boolean, traceProcessor: any, traceId: string | null) {
    try {
      for await (const chunk of stream) {
        const content = chunk.choices?.[0]?.delta?.content;
        if (content) {
          buffer += content;
          _hasReceivedContent = true;

          // Stop thinking indicator when we start getting text
          if (this.isThinking) {
            this.stopThinking();
          }

          // Send text in chunks for real-time display
          this.sendMessage(content);
          this.maybeEmitReasoningFromState(stream);
        }

        // Check for completion
        const finishReason = chunk.choices?.[0]?.finish_reason;
        if (finishReason) {
          break; // End of stream
        }
      }

      // Post-run synthesis: if any tools were used, send a short summary
      try {
        const isCodeGen = (this.currentAgent || '').toLowerCase().includes('codegeneration');
        const hadTools = this.eventHistory.some(e => e.type === 'tool_result');
        if (!isCodeGen && hadTools && !this.postRunSummarySent && !_hasReceivedContent) {
          const synthesis = this.buildPostRunSynthesis();
          if (synthesis && synthesis.length > 0) {
            this.sendMessage(synthesis);
            this.postRunSummarySent = true;
          }
        }
      } catch {}

      // Complete trace
      if (traceProcessor && traceId) {
        traceProcessor.completeTrace(traceId);
      }

      // Send final complete event
      if (this.unsubscribeProgress) this.unsubscribeProgress();
      if (this.unsubscribeToken) this.unsubscribeToken();
      this.complete(buffer || 'Task completed successfully');

      return {
        finalOutput: buffer,
        events: this.eventHistory,
      };
    } catch (error) {
      console.error('Raw chat stream error:', error);
      throw error;
    }
  }

  private async handleCompletedStream(stream: any, buffer: string, _hasReceivedContent: boolean, traceProcessor: any, traceId: string | null) {
    try {
      // Try to extract content from completed stream state
      if (stream && (stream as any).state) {
        const finalOutput = (stream as any).state?.output?.[0]?.content;
        if (finalOutput) {
          buffer = typeof finalOutput === 'string' ? finalOutput : JSON.stringify(finalOutput);
          console.log('📝 Extracted content from completed stream state');

          // Stop thinking and send the complete message
          if (this.isThinking) {
            this.stopThinking();
          }
          this.sendMessage(buffer);
          _hasReceivedContent = true;
          this.maybeEmitReasoningFromState(stream);
        }
      }

      // If still nothing, try common fields on plain objects
      if (!_hasReceivedContent && stream && typeof stream === 'object') {
        const pick = (obj: any): string | null => {
          if (!obj) return null;
          if (typeof obj.finalMessage === 'string' && obj.finalMessage.length > 0) return obj.finalMessage;
          if (typeof obj.text === 'string' && obj.text.length > 0) return obj.text;
          if (typeof obj.content === 'string' && obj.content.length > 0) return obj.content;
          if (typeof obj.message === 'string' && obj.message.length > 0) return obj.message;
          if (obj.synthesizedResult?.synthesizedResponse) return String(obj.synthesizedResult.synthesizedResponse);
          if (obj.plan && Array.isArray(obj.stepResults)) {
            try { return `Completed ${obj.stepResults.length} steps.\nPlan summary: ${obj.plan.summary || ''}`.trim(); } catch {}
          }
          return null;
        };
        const text = pick(stream);
        if (text) {
          buffer = text;
          if (this.isThinking) this.stopThinking();
          this.sendMessage(text);
          _hasReceivedContent = true;
        }
      }

      // Complete trace
      if (traceProcessor && traceId) {
        traceProcessor.completeTrace(traceId);
      }

      // Send final complete event
      if (this.unsubscribeProgress) this.unsubscribeProgress();
      if (this.unsubscribeToken) this.unsubscribeToken();
      this.complete(buffer || 'Task completed successfully');

      return {
        finalOutput: buffer,
        events: this.eventHistory,
      };
    } catch (error) {
      console.error('Completed stream handling error:', error);
      throw error;
    }
  }
}
