import { EventEmitter } from 'events';

export interface TokenUpdateEvent {
  sessionId?: string;
  key?: string;
  model?: string;
  used: number;
  timestamp?: string;
}

class TokenBus extends EventEmitter {
  emitTokenUsage(evt: TokenUpdateEvent) {
    const payload: TokenUpdateEvent = {
      ...evt,
      timestamp: evt.timestamp || new Date().toISOString(),
    };
    this.emit('token_update', payload);
  }
}

export const tokenBus = new TokenBus();
