import { AgentEventData } from '../ui/hooks/useAgentEvents';

export interface TraceEventCallback {
  (event: AgentEventData): void;
}

export interface LocalTraceData {
  id: string;
  workflowName: string;
  groupId?: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed';
  spans: any[];
}

class LocalTraceProcessor {
  private eventCallback: TraceEventCallback | null = null;
  private traces = new Map<string, LocalTraceData>();
  private currentTraceId: string | null = null;

  constructor(eventCallback?: TraceEventCallback) {
    this.eventCallback = eventCallback || null;
  }

  setEventCallback(callback: TraceEventCallback) {
    this.eventCallback = callback;
  }

  // Simulate trace processing for agent runs
  startTrace(workflowName: string, groupId?: string): string {
    const traceId = `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.currentTraceId = traceId;
    
    const trace: LocalTraceData = {
      id: traceId,
      workflowName,
      groupId,
      startTime: new Date(),
      status: 'running',
      spans: []
    };
    
    this.traces.set(traceId, trace);

    // Emit trace start event
    this.emitEvent({
      type: 'trace_started',
      timestamp: new Date().toISOString(),
      data: {
        message: `Started workflow: ${workflowName}`,
        agent: 'System'
      }
    });

    return traceId;
  }

  addSpan(type: string, name: string, data?: any): string {
    const spanId = `span_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const span = {
      id: spanId,
      traceId: this.currentTraceId,
      type,
      name,
      startTime: new Date(),
      data
    };

    if (this.currentTraceId) {
      const trace = this.traces.get(this.currentTraceId);
      if (trace) {
        trace.spans.push(span);
      }
    }

    // Emit span event based on type
    switch (type) {
      case 'agent':
        this.emitEvent({
          type: 'agent_span',
          timestamp: new Date().toISOString(),
          data: {
            message: `Agent ${name} processing`,
            agent: name
          }
        });
        break;

      case 'generation':
        this.emitEvent({
          type: 'generation_span',
          timestamp: new Date().toISOString(),
          data: {
            message: `LLM generation`,
            agent: 'LLM'
          }
        });
        break;

      case 'function':
        this.emitEvent({
          type: 'function_span',
          timestamp: new Date().toISOString(),
          data: {
            message: `Tool call: ${name}`,
            agent: 'Tool',
            tool: name
          }
        });
        break;

      case 'handoff':
        this.emitEvent({
          type: 'handoff_span',
          timestamp: new Date().toISOString(),
          data: {
            message: `Agent handoff`,
            agent: 'System'
          }
        });
        break;
    }

    return spanId;
  }

  completeTrace(traceId?: string): void {
    const id = traceId || this.currentTraceId;
    if (!id) return;

    const trace = this.traces.get(id);
    if (!trace) return;

    trace.endTime = new Date();
    trace.status = 'completed';

    // Emit completion event
    this.emitEvent({
      type: 'trace_completed',
      timestamp: new Date().toISOString(),
      data: {
        message: `Completed workflow: ${trace.workflowName}`,
        agent: 'System'
      }
    });

    if (id === this.currentTraceId) {
      this.currentTraceId = null;
    }
  }

  failTrace(error: Error, traceId?: string): void {
    const id = traceId || this.currentTraceId;
    if (!id) return;

    const trace = this.traces.get(id);
    if (trace) {
      trace.status = 'failed';
      trace.endTime = new Date();
    }

    this.emitEvent({
      type: 'trace_error',
      timestamp: new Date().toISOString(),
      data: {
        message: `Trace error: ${error.message}`,
        agent: 'System'
      }
    });

    if (id === this.currentTraceId) {
      this.currentTraceId = null;
    }
  }

  private emitEvent(event: AgentEventData): void {
    if (this.eventCallback) {
      this.eventCallback(event);
    }
  }

  // Public methods for trace access
  getTrace(traceId: string): LocalTraceData | undefined {
    return this.traces.get(traceId);
  }

  getAllTraces(): LocalTraceData[] {
    return Array.from(this.traces.values());
  }

  getRunningTraces(): LocalTraceData[] {
    return Array.from(this.traces.values()).filter(t => t.status === 'running');
  }

  clearTraces(): void {
    this.traces.clear();
  }

  getCurrentTraceId(): string | null {
    return this.currentTraceId;
  }
}

// Singleton instance
let localTraceProcessor: LocalTraceProcessor | null = null;

export function initializeTraceProcessor(eventCallback?: TraceEventCallback): LocalTraceProcessor {
  if (localTraceProcessor) {
    if (eventCallback) {
      localTraceProcessor.setEventCallback(eventCallback);
    }
    return localTraceProcessor;
  }

  localTraceProcessor = new LocalTraceProcessor(eventCallback);
  
  return localTraceProcessor;
}

export function getTraceProcessor(): LocalTraceProcessor | null {
  return localTraceProcessor;
}

export function setTraceEventCallback(callback: TraceEventCallback): void {
  if (localTraceProcessor) {
    localTraceProcessor.setEventCallback(callback);
  }
}