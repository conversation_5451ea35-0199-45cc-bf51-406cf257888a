// Utility helpers for mapping Vercel AI SDK tools to a record keyed by name
// Ensures stable, human-readable tool identifiers in logs and UI events.

export type ToolLike = { name?: string; toolName?: string } & Record<string, any>;

/**
 * Create a record of tools keyed by their human-readable names.
 * - Prefers `tool.name`, then `tool.toolName`.
 * - Guards against name collisions by suffixing with _2, _3, ...
 * - Falls back to `tool_<index>` if no name is present.
 */
export function mapToolsByName<T extends ToolLike>(tools: T[], options?: {
  onCollision?: (base: string, newKey: string) => void;
  fallbackPrefix?: string; // default: 'tool'
}): Record<string, T> {
  const out: Record<string, T> = {};
  const seen = new Set<string>();
  const fallbackPrefix = options?.fallbackPrefix ?? 'tool';

  const uniqueKey = (base: string) => {
    if (!seen.has(base) && !out[base]) {
      seen.add(base);
      return base;
    }
    let n = 2;
    while (seen.has(`${base}_${n}`) || out[`${base}_${n}`]) n++;
    const key = `${base}_${n}`;
    seen.add(key);
    options?.onCollision?.(base, key);
    return key;
  };

  tools.forEach((tool, index) => {
    const raw = (tool as any)?.name || (tool as any)?.toolName;
    const base = typeof raw === 'string' && raw.trim().length > 0
      ? raw.trim()
      : `${fallbackPrefix}_${index}`;
    const key = uniqueKey(base);
    out[key] = tool;
  });

  return out;
}

