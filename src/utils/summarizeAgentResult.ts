export interface SummarizeAgentResultOptions {
  fallbackLogLabel?: string;
  fallbackDetailsLabel?: string;
}

const TEXT_FIELDS = ['synthesizedResponse', 'text', 'finalMessage', 'summary'] as const;

const clampSummary = (text: string) => {
  const trimmed = text.trim();
  return trimmed.length > 600 ? `${trimmed.slice(0, 600)}…` : trimmed;
};

const extractStructuredPayload = (result: any): any | undefined => {
  if (!result || typeof result !== 'object') return undefined;
  if ((result as any).structuredSynthesis) return (result as any).structuredSynthesis;
  if ((result as any).structuredDebug) return (result as any).structuredDebug;
  if ((result as any).structuredCodegen) return (result as any).structuredCodegen;
  if ((result as any).structuredAnswer) return (result as any).structuredAnswer;

  const toolCalls = Array.isArray((result as any).toolCalls) ? (result as any).toolCalls : [];
  for (const call of toolCalls) {
    const name = call?.toolName || call?.name;
    if (!name || String(name).toLowerCase() !== 'answer') continue;
    const raw = call?.args ?? call?.input;
    if (!raw) continue;
    if (typeof raw === 'string') {
      try {
        return JSON.parse(raw);
      } catch {
        continue;
      }
    }
    if (typeof raw === 'object') return raw;
  }

  const steps = Array.isArray((result as any).steps) ? (result as any).steps : [];
  for (let i = steps.length - 1; i >= 0; i -= 1) {
    const step = steps[i];
    const stepName = step?.toolName || step?.name;
    const stepType = step?.type;
    if (typeof stepName === 'string' && stepName.toLowerCase() === 'answer' && (!stepType || stepType === 'tool-call' || stepType === 'tool_call')) {
      const raw = step?.args ?? step?.arguments ?? step?.input ?? step?.result ?? step?.output;
      if (!raw) continue;
      if (typeof raw === 'string') {
        try {
          return JSON.parse(raw);
        } catch {
          continue;
        }
      }
      if (typeof raw === 'object') return raw;
    }
  }

  return undefined;
};

const collectLines = (structured: any) => {
  const lines: string[] = [];
  if (typeof structured.summary === 'string' && structured.summary.trim()) {
    lines.push(structured.summary.trim());
  }

  const collectArray = (label: string, value: any) => {
    if (!Array.isArray(value) || value.length === 0) return;
    lines.push(`${label}:`);
    for (const item of value.slice(0, 5)) {
      if (typeof item === 'string') {
        lines.push(`- ${item}`);
      } else if (item && typeof item === 'object') {
        const descriptor = item.path || item.title || item.url || JSON.stringify(item);
        lines.push(`- ${descriptor}`);
      }
    }
  };

  collectArray('Key Findings', structured.keyFindings || structured.findings);
  collectArray('Files Modified', structured.filesModified);
  collectArray('Recommendations', structured.recommendations);

  return lines.length > 0 ? lines.join('\n') : undefined;
};

export const summarizeAgentResult = (
  result: any,
  step?: { id?: string | number | null },
  options: SummarizeAgentResultOptions = {},
): string => {
  const fallbackId = step?.id != null ? String(step.id) : 'unknown-step';
  const fallbackLogLabel = options.fallbackLogLabel ?? 'activity log';
  const fallbackDetailsLabel = options.fallbackDetailsLabel ?? 'full details';
  const fallback = `Step ${fallbackId} completed. Review the ${fallbackLogLabel} or repository state for ${fallbackDetailsLabel}.`;

  try {
    if (!result) return fallback;

    if (typeof result === 'string') {
      return clampSummary(result);
    }

    if (typeof result === 'object') {
      for (const key of TEXT_FIELDS) {
        const val = (result as any)[key];
        if (typeof val === 'string' && val.trim()) {
          return clampSummary(val);
        }
      }

      const structured = extractStructuredPayload(result);
      if (structured && typeof structured === 'object') {
        const structuredSummary = collectLines(structured);
        if (structuredSummary) {
          return clampSummary(structuredSummary);
        }
      }
    }

    return fallback;
  } catch {
    return fallback;
  }
};
