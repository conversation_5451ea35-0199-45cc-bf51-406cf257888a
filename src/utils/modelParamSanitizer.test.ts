import { describe, test, expect } from 'bun:test';
import { cleanParamsForModel, isReasoningModel, DISALLOWED_REASONING_PARAMS } from '../utils/modelParamSanitizer';

describe('modelParamSanitizer', () => {
  test('isReasoningModel detects Gemini 2.5 reasoning models', () => {
    expect(isReasoningModel('gemini-2.5-pro')).toBe(true);
    expect(isReasoningModel('gemini-2.5-flash')).toBe(true);
    expect(isReasoningModel('gemini-2.5-flash-lite')).toBe(true);
    expect(isReasoningModel('models/gemini-2.5-pro')).toBe(true);
  });

  test('isReasoningModel detects common o-series and "reasoning" identifiers', () => {
    expect(isReasoningModel('o3')).toBe(true);
    expect(isReasoningModel('o4-mini')).toBe(true);
    expect(isReasoningModel('gpt-4o-reasoning')).toBe(true);
    expect(isReasoningModel('deepseek-reasoner')).toBe(true);
    expect(isReasoningModel('qwen-reasoning')).toBe(true);
  });

  test('Non-reasoning model preserves temperature/top_* etc', () => {
    const original = {
      model: 'gpt-4o',
      temperature: 0.7,
      top_p: 0.95,
      top_k: 40,
      presence_penalty: 0.1,
      frequency_penalty: 0.2,
      seed: 42,
      random_seed: 7,
      extra: 'ok',
    };
    const out = cleanParamsForModel(original);
    // Same reference not guaranteed, but content should be preserved
    expect(out.model).toBe('gpt-4o');
    expect(out.temperature).toBe(0.7);
    expect(out.top_p).toBe(0.95);
    expect(out.top_k).toBe(40);
    expect(out.presence_penalty).toBe(0.1);
    expect(out.frequency_penalty).toBe(0.2);
    expect(out.seed).toBe(42);
    expect(out.random_seed).toBe(7);
    expect(out.extra).toBe('ok');
  });

  test('Reasoning model removes disallowed params', () => {
    const original = {
      model: 'gemini-2.5-pro',
      temperature: 0.7,
      top_p: 0.95,
      top_k: 40,
      presence_penalty: 0.1,
      frequency_penalty: 0.2,
      seed: 42,
      random_seed: 7,
      max_tokens: 1234,
      anyOther: true,
    };
    const out = cleanParamsForModel(original);

    // Disallowed keys should be removed
    for (const key of Array.from(DISALLOWED_REASONING_PARAMS)) {
      expect(key in out).toBe(false);
    }

    // Allowed/other keys remain
    expect(out.model).toBe('gemini-2.5-pro');
    expect(out.max_tokens).toBe(1234);
    expect(out.anyOther).toBe(true);
  });

  test('Unknown model does not strip params', () => {
    const original = {
      model: 'custom-foo-123',
      temperature: 0.3,
      top_p: 0.9,
      random_seed: 1,
    };
    const out = cleanParamsForModel(original);
    expect(out.temperature).toBe(0.3);
    expect(out.top_p).toBe(0.9);
    expect(out.random_seed).toBe(1);
  });
});
