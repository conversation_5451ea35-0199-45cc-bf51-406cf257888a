import { EventEmitter } from 'events';

export type ProgressEvent = {
  sessionId?: string;
  agent?: string;
  tool?: string;
  stage?: string;
  progress?: number; // 0-100
  detail?: any;
  timestamp?: string;
};

class ProgressBus extends EventEmitter {
  emitProgress(evt: ProgressEvent) {
    const payload = {
      ...evt,
      timestamp: evt.timestamp || new Date().toISOString(),
    };
    this.emit('progress', payload);
  }
}

export const progressBus = new ProgressBus();

