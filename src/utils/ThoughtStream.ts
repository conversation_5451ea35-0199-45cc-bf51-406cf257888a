import { EventEmitter } from 'events';

export type ThoughtStage = 'start' | 'delta' | 'complete' | 'error';

export interface ThoughtMessage {
  id: string; // unique event id per stream
  promptId: string; // top-level run/request id
  agentId: string; // agent name/id producing the thought
  stepId?: string; // sub-delegation step id (if any)
  parentId?: string; // optional parent step id for nested chains
  stage: ThoughtStage; // lifecycle stage
  content?: string; // optional textual content (delta or summary)
  ts: number; // timestamp (ms)
  seq: number; // per (promptId+agentId+stepId) monotonic sequence number
}

export interface ThoughtStreamOptions {
  bufferLimit?: number; // max pending events retained in buffer
  dedupeWindowMs?: number; // time window for basic dedupe
}

/**
 * Lightweight typed event stream for incremental agent "thoughts".
 *
 * - Emits ordered ThoughtMessage events with per-(promptId,agentId,stepId) seq.
 * - Provides AsyncIterable subscription with AbortSignal-based cancellation.
 * - Performs basic dedupe for repeated identical delta payloads within a window.
 */
export class ThoughtStream extends EventEmitter {
  private buffer: ThoughtMessage[] = [];
  private waiters: Array<(ev: IteratorResult<ThoughtMessage>) => void> = [];
  private seqCounters = new Map<string, number>();
  private lastPayloadByKey = new Map<string, { content?: string; ts: number }>();
  private bufferLimit: number;
  private dedupeWindowMs: number;

  constructor(options?: ThoughtStreamOptions) {
    super();
    this.bufferLimit = Math.max(1, options?.bufferLimit ?? 1000);
    this.dedupeWindowMs = Math.max(0, options?.dedupeWindowMs ?? 500);
  }

  /** Subscribe as an async iterator. Supports optional filtering and cancellation. */
  subscribe(options?: { signal?: AbortSignal; filter?: (m: ThoughtMessage) => boolean }): AsyncIterable<ThoughtMessage> {
    const self = this;
    const filter = options?.filter;
    const signal = options?.signal;

    let done = false;

    const onAbort = () => {
      if (done) return;
      done = true;
      while (self.waiters.length) {
        const w = self.waiters.shift();
        try { w && w({ done: true, value: undefined as any }); } catch {}
      }
    };
    if (signal) {
      if (signal.aborted) onAbort();
      else signal.addEventListener('abort', onAbort, { once: true });
    }

    // Local cursor for buffered replays
    let cursor = 0;

    const iterator: AsyncIterator<ThoughtMessage> & AsyncIterable<ThoughtMessage> = {
      [Symbol.asyncIterator]() { return this; },
      async next(): Promise<IteratorResult<ThoughtMessage>> {
        if (done) return { done: true, value: undefined as any };

        // Drain any matching buffered event
        while (cursor < self.buffer.length) {
          const ev = self.buffer[cursor++];
          if (!filter || filter(ev)) return { done: false, value: ev };
        }

        // Otherwise, wait for the next event
        return new Promise<IteratorResult<ThoughtMessage>>((resolve) => {
          if (done) return resolve({ done: true, value: undefined as any });
          const waiter = (res: IteratorResult<ThoughtMessage>) => resolve(res);
          self.waiters.push(waiter);
        });
      },
      async return(): Promise<IteratorResult<ThoughtMessage>> {
        done = true;
        onAbort();
        return { done: true, value: undefined as any };
      }
    };

    return iterator;
  }

  /** Pipe all messages from a child ThoughtStream, optionally mapping fields. */
  pipeFrom(child: ThoughtStream, map?: (m: ThoughtMessage) => ThoughtMessage): () => void {
    const listener = (m: ThoughtMessage) => {
      try { this.emitThought(map ? map(m) : m); } catch {}
    };
    child.on('thought', listener);
    return () => child.off('thought', listener);
  }

  /** Emit a thought with ordering and dedupe guarantees. */
  emitThought(msg: Omit<ThoughtMessage, 'id' | 'seq' | 'ts'> & Partial<Pick<ThoughtMessage, 'id' | 'seq' | 'ts'>>): ThoughtMessage | null {
    const ts = msg.ts ?? Date.now();
    const key = `${msg.promptId}::${msg.agentId}::${msg.stepId || ''}`;
    const seq = msg.seq ?? (this.seqCounters.get(key) || 0) + 1;
    this.seqCounters.set(key, seq);

    // Dedupe identical deltas within window
    if (msg.stage === 'delta') {
      const last = this.lastPayloadByKey.get(key);
      const c = (msg.content || '').trim();
      if (last && last.content === c && ts - last.ts <= this.dedupeWindowMs) {
        return null; // drop duplicate
      }
      this.lastPayloadByKey.set(key, { content: c, ts });
    }

    const full: ThoughtMessage = {
      id: msg.id || `${key}#${seq}`,
      ts,
      seq,
      promptId: msg.promptId,
      agentId: msg.agentId,
      stepId: msg.stepId,
      parentId: msg.parentId,
      stage: msg.stage,
      content: msg.content,
    };

    // Buffer and emit
    this.buffer.push(full);
    if (this.buffer.length > this.bufferLimit) this.buffer.splice(0, this.buffer.length - this.bufferLimit);
    this.emit('thought', full);

    // Unblock one waiter
    const waiter = this.waiters.shift();
    if (waiter) {
      try { waiter({ done: false, value: full }); } catch {}
    }
    return full;
  }
}

export function toSSE(data: ThoughtMessage): string {
  // Serialize as JSON line with event: thought
  const payload = JSON.stringify(data);
  return `event: thought\n` + `data: ${payload}\n\n`;
}
