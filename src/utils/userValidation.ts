/**
 * User validation utilities for connector security
 * Ensures proper user identification and prevents token collision
 */

/**
 * Validates and returns a user ID, throwing an error if invalid
 * This prevents the security issue of using 'default' as a fallback
 * which could lead to token collision and unauthorized access
 */
export function validateUserId(userId: string | null | undefined, context: string = 'operation'): string {
  if (!userId) {
    throw new Error(`User authentication required for ${context}. No user ID provided.`);
  }

  // Trim whitespace
  const trimmedId = userId.trim();
  
  if (!trimmedId) {
    throw new Error(`Invalid user ID for ${context}. User ID cannot be empty.`);
  }

  // Prevent use of reserved/dangerous IDs
  const reservedIds = ['default', 'admin', 'system', 'root', 'anonymous', 'guest'];
  if (reservedIds.includes(trimmedId.toLowerCase())) {
    throw new Error(`Invalid user ID for ${context}. '${trimmedId}' is a reserved identifier.`);
  }

  // For email-based user IDs, validate format
  if (trimmedId.includes('@')) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedId)) {
      throw new Error(`Invalid email format for user ID in ${context}.`);
    }
  }

  // Ensure ID doesn't contain dangerous characters
  const dangerousChars = /[<>\"'`;()]/;
  if (dangerousChars.test(trimmedId)) {
    throw new Error(`Invalid user ID for ${context}. Contains forbidden characters.`);
  }

  return trimmedId;
}

/**
 * Gets the current authenticated user from the request context
 * In production, this should integrate with your auth system
 */
export function getCurrentUserId(req: any): string | null {
  // Priority 1: Check authenticated session (production auth)
  if (req.user?.id) {
    return req.user.id;
  }
  
  // Priority 2: Check JWT token
  if (req.auth?.userId) {
    return req.auth.userId;
  }
  
  // Priority 3: Check session
  if (req.session?.userId) {
    return req.session.userId;
  }
  
  // Priority 4: Check header (for API keys)
  const apiUserId = req.headers['x-user-id'];
  if (apiUserId) {
    return apiUserId;
  }
  
  // No authenticated user found
  return null;
}

/**
 * Ensures a request has a valid authenticated user
 * Throws an error if no valid user is found
 */
export function requireAuthenticatedUser(req: any, operation: string = 'this operation'): string {
  const userId = getCurrentUserId(req);
  return validateUserId(userId, operation);
}

/**
 * Validates that a user has permission to access a specific connector
 * Can be extended with role-based permissions
 */
export function validateUserConnectorAccess(
  userId: string,
  connectorId: string,
  operation: string = 'access'
): void {
  // Validate user ID first
  validateUserId(userId, `${connectorId} ${operation}`);
  
  // In production, add additional permission checks here
  // For example:
  // - Check if user has access to this connector type
  // - Check if user's subscription level allows this connector
  // - Check if user has been granted access by an admin
  // - Check rate limits for this user/connector combination
  
  // For now, just ensure the user is authenticated
  // Additional business logic can be added as needed
}

/**
 * Sanitizes user ID for safe storage and display
 */
export function sanitizeUserId(userId: string): string {
  return validateUserId(userId, 'sanitization')
    .toLowerCase()
    .trim()
    .substring(0, 255); // Limit length for database storage
}