import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface ConversationMessage {
  role: string;
  content: string;
  timestamp: number;
  model?: string;
  agentEvents?: any[];
  agentState?: AgentStateInfo;
}

interface AgentStateInfo {
  currentModel: string;
  previousModel?: string;
  modelSwitchId?: string;
  continuityScore?: number;
  activeTasks?: string[];
  toolStates?: Record<string, any>;
}

interface Session {
  id: string;
  messages: ConversationMessage[];
  createdAt: number;
  updatedAt: number;
  title?: string;
  metadata?: {
    model?: string;
    agent?: string;
    agentType?: 'unified' | 'legacy';
    currentModel?: string;
    modelSwitches?: number;
    lastModelSwitch?: number;
    continuityScore?: number;
    [key: string]: any;
  };
}

interface ConversationDB extends DBSchema {
  sessions: {
    key: string;
    value: Session;
    indexes: { 'by-date': number };
  };
  apiLogs: {
    key: string;
    value: {
      id: string;
      sessionId: string;
      request: any;
      response: any;
      timestamp: number;
      duration?: number;
      error?: any;
    };
    indexes: { 'by-session': string; 'by-date': number };
  };
}

class SessionManager {
  private db: IDBPDatabase<ConversationDB> | null = null;
  private currentSessionId: string | null = null;
  private autoSaveInterval: NodeJS.Timeout | null = null;
  private pendingMessages: ConversationMessage[] = [];

  private readonly DB_NAME = 'DanteConversations';
  private readonly DB_VERSION = 1;
  private readonly SESSION_KEY = 'dante_current_session';
  private readonly AUTO_SAVE_DELAY = 1000;

  async init() {
    try {
      this.db = await openDB<ConversationDB>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          if (!db.objectStoreNames.contains('sessions')) {
            const sessionStore = db.createObjectStore('sessions', { keyPath: 'id' });
            sessionStore.createIndex('by-date', 'updatedAt');
          }

          if (!db.objectStoreNames.contains('apiLogs')) {
            const logStore = db.createObjectStore('apiLogs', { keyPath: 'id' });
            logStore.createIndex('by-session', 'sessionId');
            logStore.createIndex('by-date', 'timestamp');
          }
        },
      });

      const savedSessionId = typeof localStorage !== 'undefined' ? localStorage.getItem(this.SESSION_KEY) : null;
      if (savedSessionId) {
        const session = await this.getSession(savedSessionId);
        if (session) {
          this.currentSessionId = savedSessionId;
        } else {
          this.createNewSession();
        }
      } else {
        this.createNewSession();
      }
    } catch (error) {
      console.error('Failed to initialize SessionManager:', error);
      this.useFallbackStorage();
    }
  }

  private useFallbackStorage() {
    console.warn('Using localStorage fallback for session management');
    const savedSession = typeof localStorage !== 'undefined' ? localStorage.getItem('dante_session_fallback') : null;
    if (savedSession) {
      try {
        const session = JSON.parse(savedSession);
        this.currentSessionId = session.id;
      } catch {
        this.createNewSession();
      }
    } else {
      this.createNewSession();
    }
  }

  createNewSession(title?: string): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: Session = {
      id: sessionId,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      title: title || `Session ${new Date().toLocaleString()}`,
      metadata: {},
    };

    this.currentSessionId = sessionId;
    
    // Only use localStorage if available (browser environment)
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(this.SESSION_KEY, sessionId);
    }

    if (this.db) {
      this.db.put('sessions', session).catch(console.error);
    } else if (typeof localStorage !== 'undefined') {
      localStorage.setItem('dante_session_fallback', JSON.stringify(session));
    }

    return sessionId;
  }

  async getCurrentSession(): Promise<Session | null> {
    if (!this.currentSessionId) return null;
    return this.getSession(this.currentSessionId);
  }

  async getSession(sessionId: string): Promise<Session | null> {
    if (this.db) {
      try {
        return await this.db.get('sessions', sessionId) || null;
      } catch (error) {
        console.error('Failed to get session:', error);
        return this.getFallbackSession(sessionId);
      }
    }
    return this.getFallbackSession(sessionId);
  }

  private getFallbackSession(sessionId: string): Session | null {
    // Check if localStorage is available (browser environment)
    if (typeof localStorage === 'undefined') {
      return null; // Server environment - no fallback storage
    }
    
    const saved = localStorage.getItem('dante_session_fallback');
    if (saved) {
      try {
        const session = JSON.parse(saved);
        if (session.id === sessionId) return session;
      } catch {}
    }
    return null;
  }

  async addMessage(message: ConversationMessage) {
    if (!this.currentSessionId) {
      this.createNewSession();
    }

    const timestampedMessage = {
      ...message,
      timestamp: message.timestamp || Date.now(),
    };

    this.pendingMessages.push(timestampedMessage);
    this.scheduleAutoSave();
  }

  private scheduleAutoSave() {
    if (this.autoSaveInterval) {
      clearTimeout(this.autoSaveInterval);
    }

    this.autoSaveInterval = setTimeout(() => {
      this.saveMessages();
    }, this.AUTO_SAVE_DELAY);
  }

  private async saveMessages() {
    if (!this.currentSessionId || this.pendingMessages.length === 0) return;

    const session = await this.getCurrentSession();
    if (!session) return;

    session.messages.push(...this.pendingMessages);
    session.updatedAt = Date.now();

    if (this.db) {
      try {
        await this.db.put('sessions', session);
      } catch (error) {
        console.error('Failed to save messages to IndexedDB:', error);
        this.saveFallbackSession(session);
      }
    } else {
      this.saveFallbackSession(session);
    }

    this.pendingMessages = [];
  }

  private saveFallbackSession(session: Session) {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('dante_session_fallback', JSON.stringify(session));
      }
    } catch (error) {
      console.error('Failed to save session to localStorage:', error);
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        this.trimOldMessages(session);
      }
    }
  }

  private trimOldMessages(session: Session) {
    const maxMessages = 50;
    if (session.messages.length > maxMessages) {
      session.messages = session.messages.slice(-maxMessages);
      session.metadata = {
        ...session.metadata,
        trimmed: true,
        trimmedAt: Date.now(),
      };
      this.saveFallbackSession(session);
    }
  }

  async getAllSessions(): Promise<Session[]> {
    if (this.db) {
      try {
        const sessions = await this.db.getAllFromIndex('sessions', 'by-date');
        return sessions.reverse();
      } catch (error) {
        console.error('Failed to get all sessions:', error);
        return [];
      }
    }
    return [];
  }

  async deleteSession(sessionId: string): Promise<void> {
    if (this.db) {
      try {
        await this.db.delete('sessions', sessionId);
        const logs = await this.db.getAllFromIndex('apiLogs', 'by-session', sessionId);
        for (const log of logs) {
          await this.db.delete('apiLogs', log.id);
        }
      } catch (error) {
        console.error('Failed to delete session:', error);
      }
    }

    if (sessionId === this.currentSessionId) {
      this.createNewSession();
    }
  }

  async logApiCall(request: any, response: any, duration?: number, error?: any) {
    if (!this.currentSessionId || !this.db) return;

    const log = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sessionId: this.currentSessionId,
      request,
      response,
      timestamp: Date.now(),
      duration,
      error,
    };

    try {
      await this.db.put('apiLogs', log);
    } catch (error) {
      console.error('Failed to log API call:', error);
    }
  }

  async getSessionLogs(sessionId: string) {
    if (!this.db) return [];

    try {
      return await this.db.getAllFromIndex('apiLogs', 'by-session', sessionId);
    } catch (error) {
      console.error('Failed to get session logs:', error);
      return [];
    }
  }

  async exportSession(sessionId: string): Promise<string> {
    const session = await this.getSession(sessionId);
    const logs = await this.getSessionLogs(sessionId);

    const exportData = {
      session,
      logs,
      exportedAt: Date.now(),
      version: '1.0.0',
    };

    return JSON.stringify(exportData, null, 2);
  }

  async importSession(jsonData: string): Promise<string | null> {
    try {
      const data = JSON.parse(jsonData);
      if (!data.session) throw new Error('Invalid session data');

      const newSessionId = this.createNewSession(data.session.title + ' (Imported)');
      const session = await this.getCurrentSession();
      
      if (session && this.db) {
        session.messages = data.session.messages;
        session.metadata = { ...data.session.metadata, imported: true };
        await this.db.put('sessions', session);

        if (data.logs && Array.isArray(data.logs)) {
          for (const log of data.logs) {
            await this.db.put('apiLogs', {
              ...log,
              sessionId: newSessionId,
            });
          }
        }
      }

      return newSessionId;
    } catch (error) {
      console.error('Failed to import session:', error);
      return null;
    }
  }

  switchToSession(sessionId: string) {
    this.currentSessionId = sessionId;
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(this.SESSION_KEY, sessionId);
    }
  }

  async generateShareableSessionUrl(sessionId?: string): Promise<string> {
    const id = sessionId || this.currentSessionId;
    if (!id) return '';
    
    // Try to get the network IP from the server
    try {
      console.log('[SessionManager] Fetching network info from /api/network-info');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      const response = await fetch('/api/network-info', {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      
      clearTimeout(timeoutId);
      console.log('[SessionManager] Network info response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('[SessionManager] Network info data:', data);
        
        if (data.networkUrl && data.available) {
          const networkUrl = `${data.networkUrl}?session=${encodeURIComponent(id)}`;
          console.log('[SessionManager] ✅ Generated network URL:', networkUrl);
          return networkUrl;
        } else {
          console.warn('[SessionManager] No valid networkUrl in response data:', data);
        }
      } else {
        const errorText = await response.text();
        console.warn('[SessionManager] Network info request failed:', response.status, errorText);
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.warn('[SessionManager] Network info request timed out');
      } else {
        console.warn('[SessionManager] Network info request failed:', error);
      }
    }
    
    // Fallback to current location
    const baseUrl = typeof window !== 'undefined' 
      ? `${window.location.protocol}//${window.location.host}`
      : 'http://localhost:3002';
    
    console.log('[SessionManager] ⚠️ Using fallback URL:', baseUrl);
    return `${baseUrl}?session=${encodeURIComponent(id)}`;
  }

  async generateQRCodeData(sessionId?: string): Promise<string> {
    return await this.generateShareableSessionUrl(sessionId);
  }

  async loadSessionFromUrl(): Promise<string | null> {
    if (typeof window === 'undefined') return null;
    
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session');
    
    if (sessionId) {
      const session = await this.getSession(sessionId);
      if (session) {
        this.switchToSession(sessionId);
      } else {
        // Adopt the shared session ID locally so device sync can join immediately
        await this.adoptSharedSession(sessionId);
      }
      // Clear URL parameter for cleaner UI
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('session');
      window.history.replaceState({}, '', newUrl.toString());
      return sessionId;
    }
    
    return null;
  }

  private async adoptSharedSession(sessionId: string) {
    // Create a local placeholder for the shared session ID if it doesn't exist
    try {
      const now = Date.now();
      const placeholder = {
        id: sessionId,
        messages: [],
        createdAt: now,
        updatedAt: now,
        title: `Shared Session (${new Date(now).toLocaleString()})`,
        metadata: { imported: true },
      } as any;

      if (this.db) {
        const existing = await this.db.get('sessions', sessionId);
        if (!existing) await this.db.put('sessions', placeholder);
      } else {
        // Fallback: store to localStorage
        this.saveFallbackSession(placeholder);
      }

      this.switchToSession(sessionId);
    } catch (e) {
      console.warn('Failed to adopt shared session ID:', e);
      this.switchToSession(sessionId);
    }
  }

  async clearAll() {
    if (this.db) {
      try {
        const tx = this.db.transaction(['sessions', 'apiLogs'], 'readwrite');
        await Promise.all([
          tx.objectStore('sessions').clear(),
          tx.objectStore('apiLogs').clear(),
        ]);
      } catch (error) {
        console.error('Failed to clear all data:', error);
      }
    }

    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(this.SESSION_KEY);
      localStorage.removeItem('dante_session_fallback');
    }
    this.createNewSession();
  }

  /**
   * Update session metadata for unified agent
   */
  async updateUnifiedAgentMetadata(metadata: {
    currentModel?: string;
    modelSwitches?: number;
    continuityScore?: number;
    agentStatus?: any;
  }): Promise<void> {
    if (!this.currentSessionId) return;

    try {
      const session = await this.getCurrentSession();
      if (session) {
        session.metadata = {
          ...session.metadata,
          agentType: 'unified',
          currentModel: metadata.currentModel,
          modelSwitches: metadata.modelSwitches,
          continuityScore: metadata.continuityScore,
          lastModelSwitch: Date.now(),
          agentStatus: metadata.agentStatus
        };

        session.updatedAt = Date.now();

        if (this.db) {
          await this.db.put('sessions', session);
        } else {
          this.saveFallbackSession(session);
        }

        console.log('📊 Updated unified agent metadata for session:', this.currentSessionId);
      }
    } catch (error) {
      console.error('Failed to update unified agent metadata:', error);
    }
  }

  /**
   * Add a message with unified agent state information
   */
  async addMessageWithAgentState(message: ConversationMessage & {
    agentState?: AgentStateInfo;
  }): Promise<void> {
    if (!this.currentSessionId) {
      this.createNewSession();
    }

    const timestampedMessage: ConversationMessage = {
      ...message,
      timestamp: message.timestamp || Date.now(),
      agentState: message.agentState
    };

    this.pendingMessages.push(timestampedMessage);
    this.scheduleAutoSave();

    // Update session metadata if this is a model switch
    if (message.agentState?.modelSwitchId) {
      await this.updateUnifiedAgentMetadata({
        currentModel: message.agentState.currentModel,
        modelSwitches: (await this.getModelSwitchCount()) + 1,
        continuityScore: message.agentState.continuityScore
      });
    }
  }

  /**
   * Get model switch history for current session
   */
  async getModelSwitchHistory(): Promise<Array<{
    timestamp: number;
    fromModel?: string;
    toModel: string;
    switchId?: string;
    continuityScore?: number;
    reason?: string;
  }>> {
    const session = await this.getCurrentSession();
    if (!session) return [];

    const modelSwitches: Array<{
      timestamp: number;
      fromModel?: string;
      toModel: string;
      switchId?: string;
      continuityScore?: number;
      reason?: string;
    }> = [];

    for (const message of session.messages) {
      if (message.agentEvents) {
        for (const event of message.agentEvents) {
          if (event.type === 'model_switch') {
            modelSwitches.push({
              timestamp: message.timestamp,
              fromModel: event.fromModel,
              toModel: event.toModel,
              switchId: event.switchId,
              continuityScore: event.continuityScore,
              reason: event.reason
            });
          }
        }
      }
    }

    return modelSwitches.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get agent state summary for current session
   */
  async getAgentStateSummary(): Promise<{
    agentType: 'unified' | 'legacy' | 'unknown';
    currentModel?: string;
    totalModelSwitches: number;
    averageContinuityScore: number;
    lastActivity: number;
    conversationLength: number;
    hasActiveTools: boolean;
  }> {
    const session = await this.getCurrentSession();
    if (!session) {
      return {
        agentType: 'unknown',
        totalModelSwitches: 0,
        averageContinuityScore: 0,
        lastActivity: 0,
        conversationLength: 0,
        hasActiveTools: false
      };
    }

    const modelSwitches = await this.getModelSwitchHistory();
    const averageContinuityScore = modelSwitches.length > 0 
      ? modelSwitches.reduce((sum, s) => sum + (s.continuityScore || 0), 0) / modelSwitches.length
      : 0;

    const hasActiveTools = session.messages.some(m => {
      return !!(m.agentEvents && m.agentEvents.some(e => e.type === 'tool_execution' || e.type === 'tool_call'));
    });

    return {
      agentType: session.metadata?.agentType || 'unknown',
      currentModel: session.metadata?.currentModel,
      totalModelSwitches: modelSwitches.length,
      averageContinuityScore,
      lastActivity: session.updatedAt,
      conversationLength: session.messages.length,
      hasActiveTools
    };
  }

  // Private helper methods for unified agent support

  private async getModelSwitchCount(): Promise<number> {
    const session = await this.getCurrentSession();
    return session?.metadata?.modelSwitches || 0;
  }

  destroy() {
    if (this.autoSaveInterval) {
      clearTimeout(this.autoSaveInterval);
      this.saveMessages();
    }
    if (this.db) {
      this.db.close();
    }
  }
}

export const sessionManager = new SessionManager();
export type { Session, ConversationMessage };
