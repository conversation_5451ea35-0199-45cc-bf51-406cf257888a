/**
 * Contextual Project Feeder
 *
 * Intelligent system to provide only relevant project context to prevent rate limits
 * while maintaining agent effectiveness.
 */

import { countTokens, getModelLimits } from './tokenLimiter';
import { promises as fs } from 'fs';
import path from 'path';
import { getConfig } from './config';

interface ProjectContext {
  summary: string;
  relevantFiles: string[];
  dependencies: string[];
  architecture: string;
  recentChanges: string[];
  maxTokens: number;
}

interface TaskAnalysis {
  type: 'feature' | 'bug' | 'refactor' | 'analysis' | 'setup' | 'documentation';
  scope: 'file' | 'component' | 'module' | 'system' | 'project';
  relevantDomains: string[];
  keyTerms: string[];
  confidenceScore: number;
}

interface ContextSelectionStrategy {
  includeArchitecture: boolean;
  maxFileCount: number;
  maxDirectoryDepth: number;
  prioritizePatterns: string[];
  excludePatterns: string[];
  tokenBudget: number;
}

export class ContextualProjectFeeder {
  private projectRoot: string;
  private contextCache = new Map<string, ProjectContext>();
  private fileRelevanceCache = new Map<string, number>();

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot;
  }

  /**
   * Analyze the user's task to determine what type of context is needed
   */
  async analyzeTask(userMessage: string, conversationHistory: Array<{role: string, content: string}>): Promise<TaskAnalysis> {
    const message = userMessage.toLowerCase();
    const recentContext = conversationHistory.slice(-3).map(m => m.content).join(' ').toLowerCase();

    // Determine task type
    // Determine task type
    let type: TaskAnalysis['type'] = 'analysis';
    if (/\b(?:implement|add|create)\b/.test(message)) {
      type = 'feature';
    } else if (/\b(?:fix|bug|error)\b/.test(message)) {
      type = 'bug';
    } else if (/\b(?:refactor|clean|optimize)\b/.test(message)) {
      type = 'refactor';
    } else if (/\b(?:setup|configure|install)\b/.test(message)) {
      type = 'setup';
    } else if (/\b(?:document|describe)\b/.test(message)) {
      type = 'documentation';
    }
    // Determine scope
    let scope: TaskAnalysis['scope'] = 'module';
    if (message.includes('entire project') || message.includes('whole codebase') || message.includes('architecture')) {
      scope = 'project';
    } else if (message.includes('system') || message.includes('across multiple') || message.includes('authentication')) {
      scope = 'system';
    } else if (message.includes('component') || message.includes('service') || message.includes('function') || message.includes('login')) {
      scope = 'component';
    } else if (message.includes('file') || message.includes('specific') || message.includes('.ts') || message.includes('.js')) {
      scope = 'file';
    }

    // Extract relevant domains and key terms
    const relevantDomains = this.extractDomains(message + ' ' + recentContext);
    const keyTerms = this.extractKeyTerms(message);

    // Calculate confidence score based on specificity
    const confidenceScore = this.calculateConfidenceScore(message, keyTerms);

    return {
      type,
      scope,
      relevantDomains,
      keyTerms,
      confidenceScore
    };
  }

  /**
   * Generate context selection strategy based on task analysis
   */
  generateContextStrategy(taskAnalysis: TaskAnalysis, model: string = 'gpt-5'): ContextSelectionStrategy {
    const modelLimits = getModelLimits(model);
    const baseTokenBudget = Math.floor(modelLimits.maxInputTokens * 0.6); // 60% for context

    let strategy: ContextSelectionStrategy = {
      includeArchitecture: false,
      maxFileCount: 10,
      maxDirectoryDepth: 3,
      prioritizePatterns: [],
      excludePatterns: ['node_modules', '.git', 'build', 'dist', '.next', 'coverage'],
      tokenBudget: baseTokenBudget
    };

    // Adjust strategy based on task type
    switch (taskAnalysis.type) {
      case 'feature':
        strategy.maxFileCount = 15;
        strategy.includeArchitecture = taskAnalysis.scope === 'system' || taskAnalysis.scope === 'project';
        strategy.prioritizePatterns = ['src/', 'lib/', 'components/', 'services/'];
        break;

      case 'bug':
        strategy.maxFileCount = 8;
        strategy.maxDirectoryDepth = 4;
        strategy.prioritizePatterns = ['src/', 'test/', 'spec/'];
        break;

      case 'refactor':
        strategy.maxFileCount = 20;
        strategy.includeArchitecture = true;
        strategy.prioritizePatterns = ['src/', 'lib/'];
        break;

      case 'analysis':
        strategy.maxFileCount = 25;
        strategy.includeArchitecture = true;
        strategy.maxDirectoryDepth = 4;
        break;

      case 'setup':
        strategy.maxFileCount = 5;
        strategy.prioritizePatterns = ['package.json', 'tsconfig.json', '.env', 'README', 'CLAUDE.md'];
        break;

      case 'documentation':
        strategy.maxFileCount = 12;
        strategy.includeArchitecture = true;
        strategy.prioritizePatterns = ['README', 'docs/', 'CLAUDE.md'];
        break;
    }

    // Adjust based on scope
    switch (taskAnalysis.scope) {
      case 'file':
        strategy.maxFileCount = Math.min(strategy.maxFileCount, 3);
        strategy.includeArchitecture = false;
        strategy.tokenBudget = modelLimits.maxInputTokens; // Full budget for file-specific requests to ensure inclusion
        break;

      case 'component':
        strategy.maxFileCount = Math.min(strategy.maxFileCount, 8);
        break;

      case 'system':
        strategy.maxFileCount = Math.min(strategy.maxFileCount * 1.5, 30);
        strategy.includeArchitecture = true;
        break;

      case 'project':
        strategy.maxFileCount = Math.min(strategy.maxFileCount * 2, 40);
        strategy.includeArchitecture = true;
        strategy.maxDirectoryDepth = 5;
        break;
    }

    // Add domain-specific patterns
    for (const domain of taskAnalysis.relevantDomains) {
      strategy.prioritizePatterns.push(`${domain}/`, `*${domain}*`);
    }

    return strategy;
  }

  /**
   * Select relevant files based on task analysis and strategy
   */
  async selectRelevantFiles(
    taskAnalysis: TaskAnalysis,
    strategy: ContextSelectionStrategy
  ): Promise<string[]> {
    const allFiles = await this.getAllProjectFiles(strategy.maxDirectoryDepth);
    const scoredFiles = await this.scoreFileRelevance(allFiles, taskAnalysis, strategy);

    // Sort by relevance score (highest first)
    const sortedFiles = scoredFiles.sort((a, b) => b.score - a.score);

    // Greedily fill up to maxFileCount while staying under tokenBudget.
    // Do not pre-slice; this allows smaller files beyond the top-N to be considered.
    const selectedFiles: string[] = [];
    let currentTokens = 0;

    for (const file of sortedFiles) {
      if (selectedFiles.length >= strategy.maxFileCount) break;
      try {
        const content = await fs.readFile(file.path, 'utf-8');
        const fileTokens = countTokens(content);

        if (currentTokens + fileTokens <= strategy.tokenBudget) {
          selectedFiles.push(file.path);
          currentTokens += fileTokens;
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return selectedFiles;
  }

  /**
   * Generate project context based on selected files and strategy
   */
  async generateProjectContext(
    selectedFiles: string[],
    taskAnalysis: TaskAnalysis,
    strategy: ContextSelectionStrategy
  ): Promise<ProjectContext> {
    const cacheKey = `${selectedFiles.join(',')}:${JSON.stringify(strategy)}`;

    if (this.contextCache.has(cacheKey)) {
      return this.contextCache.get(cacheKey)!;
    }

    let summary = '';
    const dependencies: string[] = [];
    const recentChanges: string[] = [];

    // Generate summary based on task type
    if (strategy.includeArchitecture) {
      summary += await this.generateArchitectureSummary();
    }

    // Add task-specific context
    summary += await this.generateTaskSpecificContext(taskAnalysis, selectedFiles);

    // Extract dependencies from package.json and other config files
    try {
      const packageJsonPath = path.join(this.projectRoot, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      dependencies.push(...Object.keys(packageJson.dependencies || {}));
      dependencies.push(...Object.keys(packageJson.devDependencies || {}));
    } catch (error) {
      // No package.json or parsing error
    }

    const context: ProjectContext = {
      summary,
      relevantFiles: selectedFiles,
      dependencies: dependencies.slice(0, 20), // Limit dependencies
      architecture: strategy.includeArchitecture ? await this.generateArchitectureSummary() : '',
      recentChanges,
      maxTokens: strategy.tokenBudget
    };

    this.contextCache.set(cacheKey, context);
    return context;
  }

  /**
   * Main method to get contextual project information
   */
  async getContextualProjectInfo(
    userMessage: string,
    conversationHistory: Array<{role: string, content: string}> = [],
    model: string = 'gpt-5'
  ): Promise<{
    context: ProjectContext;
    reasoning: string;
    tokensUsed: number;
  }> {
    const taskAnalysis = await this.analyzeTask(userMessage, conversationHistory);
    const strategy = this.generateContextStrategy(taskAnalysis, model);

    // Minimize initial context for first request (progressive disclosure)
    if (conversationHistory.length === 0 && taskAnalysis.scope !== 'file') {
      // Start smaller so later expansions can add more
      strategy.maxFileCount = Math.min(strategy.maxFileCount, 12);
      const limits = getModelLimits(model);
      // Use a smaller initial budget (ratio of model input) to keep context minimal
      const ratio = getConfig().initialContextBudgetRatio;
      strategy.tokenBudget = Math.min(
        strategy.tokenBudget,
        Math.floor(limits.maxInputTokens * ratio)
      );
    }

    const selectedFiles = await this.selectRelevantFiles(taskAnalysis, strategy);
    const context = await this.generateProjectContext(selectedFiles, taskAnalysis, strategy);

    const reasoning = this.generateReasoningExplanation(taskAnalysis, strategy, selectedFiles);
    const tokensUsed = countTokens(JSON.stringify(context));

    return {
      context,
      reasoning,
      tokensUsed
    };
  }

  // Private helper methods

  private extractDomains(text: string): string[] {
    const domains = [
      'api', 'ui', 'database', 'auth', 'admin', 'user', 'payment', 'notification',
      'search', 'analytics', 'logging', 'security', 'performance', 'testing',
      'deployment', 'monitoring', 'cache', 'queue', 'worker', 'service',
      'component', 'hook', 'util', 'helper', 'config', 'model', 'controller',
      'middleware', 'agent', 'tool', 'memory', 'stream', 'websocket'
    ];

    return domains.filter(domain => text.includes(domain));
  }

  private extractKeyTerms(text: string): string[] {
    // Extract meaningful terms, ignoring common words
    const words = text.toLowerCase().match(/\b\w{3,}\b/g) || [];
    const stopWords = new Set(['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use']);

    return [...new Set(words.filter(word => !stopWords.has(word) && word.length > 3))];
  }

  private calculateConfidenceScore(message: string, keyTerms: string[]): number {
    let score = 0.5; // Base confidence

    // Increase confidence for specific terms
    if (keyTerms.length > 0) score += 0.1;
    if (keyTerms.length > 3) score += 0.1;

    // Increase confidence for file mentions
    if (message.includes('.ts') || message.includes('.js') || message.includes('.tsx')) score += 0.2;

    // Increase confidence for specific patterns
    if (message.includes('src/') || message.includes('lib/')) score += 0.1;

    return Math.min(score, 1.0);
  }

  private async getAllProjectFiles(maxDepth: number): Promise<string[]> {
    const files: string[] = [];
    const self = this;

    async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
      if (currentDepth > maxDepth) return;

      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !entry.name.startsWith('.') &&
              !['node_modules', 'build', 'dist', '.next', 'coverage'].includes(entry.name)) {
            await scanDirectory(fullPath, currentDepth + 1);
          } else if (entry.isFile() && self.isRelevantFile(entry.name)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }

    await scanDirectory(this.projectRoot, 0);
    return files;
  }

  private isRelevantFile(filename: string): boolean {
    const relevantExtensions = ['.ts', '.tsx', '.js', '.jsx', '.json', '.md', '.yml', '.yaml', '.env'];
    const configFiles = ['package.json', 'tsconfig.json', 'CLAUDE.md', 'README.md'];

    return relevantExtensions.some(ext => filename.endsWith(ext)) ||
           configFiles.includes(filename);
  }

  private async scoreFileRelevance(
    files: string[],
    taskAnalysis: TaskAnalysis,
    strategy: ContextSelectionStrategy
  ): Promise<Array<{path: string, score: number}>> {
    const scoredFiles: Array<{path: string, score: number}> = [];

    for (const file of files) {
      let score = 0;

      // Base score for file type
      if (file.endsWith('.ts') || file.endsWith('.tsx')) score += 0.3;
      if (file.endsWith('.js') || file.endsWith('.jsx')) score += 0.2;
      if (file.endsWith('.json')) score += 0.1;
      if (file.endsWith('.md')) score += 0.1;

      // Priority patterns
      for (const pattern of strategy.prioritizePatterns) {
        if (file.includes(pattern)) score += 0.4;
      }

      // Key terms in filename
      const filename = path.basename(file).toLowerCase();
      for (const term of taskAnalysis.keyTerms) {
        if (filename.includes(term.toLowerCase())) score += 0.3;
      }

      // Domain relevance
      for (const domain of taskAnalysis.relevantDomains) {
        if (file.toLowerCase().includes(domain)) score += 0.2;
      }

      // Exclude patterns
      for (const pattern of strategy.excludePatterns) {
        if (file.includes(pattern)) score -= 0.5;
      }

      // Recently modified files get higher score
      try {
        const stats = await fs.stat(file);
        const daysSinceModified = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceModified < 7) score += 0.1;
      } catch (error) {
        // Ignore stat errors
      }

      // Boost for file-specific scope with exact filename matches
      if (taskAnalysis.scope === 'file' && taskAnalysis.keyTerms.some(term =>
        filename.startsWith(term.toLowerCase()) || term.toLowerCase().startsWith(filename.replace(/\.[^.]+$/, ''))
      )) {
        score += 10.0; // High boost to ensure file-specific files are prioritized
      }

      // De-prioritize core infrastructure files to avoid polluting context for generic user intents.
      // If the user explicitly mentions the core filename/term, allow it; otherwise apply a penalty.
      try {
        const corePatterns = ['orchestrator', 'server', 'feeder', 'recovery', 'mcp', 'memory', 'connector', 'auth', 'index', 'cli', 'config', 'projectfeeder'];
        const fileLower = file.toLowerCase();
        const matchesCore = corePatterns.some(p => fileLower.includes(p));
        const explicitlyRequestedCore = taskAnalysis.keyTerms.some(term => fileLower.includes(term.toLowerCase()));
        if (matchesCore && !explicitlyRequestedCore) {
          score -= 5.0; // Strong penalty for core infra files unless explicitly requested
        }
      } catch {}

      if (file.includes('DanteOrchestrator')) {
        console.log(`DEBUG: File ${file}, filename: ${filename}, score: ${score}, keyTerms: ${JSON.stringify(taskAnalysis.keyTerms)}, scope: ${taskAnalysis.scope}, domains: ${JSON.stringify(taskAnalysis.relevantDomains)}`);
      }

      scoredFiles.push({ path: file, score: Math.max(score, 0) });
    }

    return scoredFiles;
  }

  private async generateArchitectureSummary(): Promise<string> {
    try {
      const claudeMdPath = path.join(this.projectRoot, 'CLAUDE.md');
      const claudeContent = await fs.readFile(claudeMdPath, 'utf-8');

      // Extract architecture section
      const archMatch = claudeContent.match(/## Architecture([\s\S]*?)(?=##|$)/);
      if (archMatch) {
        return archMatch[1].trim();
      }
    } catch (error) {
      // No CLAUDE.md file
    }

    return 'Project architecture details not available in CLAUDE.md';
  }

  private async generateTaskSpecificContext(
    taskAnalysis: TaskAnalysis,
    selectedFiles: string[]
  ): Promise<string> {
    let context = `Task Type: ${taskAnalysis.type}\n`;
    context += `Scope: ${taskAnalysis.scope}\n`;
    context += `Relevant Files (${selectedFiles.length}):\n`;

    for (const file of selectedFiles.slice(0, 10)) { // Show first 10 files
      context += `- ${path.relative(this.projectRoot, file)}\n`;
    }

    if (taskAnalysis.keyTerms.length > 0) {
      context += `\nKey Terms: ${taskAnalysis.keyTerms.slice(0, 5).join(', ')}\n`;
    }

    return context;
  }

  private generateReasoningExplanation(
    taskAnalysis: TaskAnalysis,
    strategy: ContextSelectionStrategy,
    selectedFiles: string[]
  ): string {
    let reasoning = `Selected ${selectedFiles.length} files for ${taskAnalysis.type} task with ${taskAnalysis.scope} scope. ` +
                    `Token budget: ${strategy.tokenBudget}. Confidence: ${(taskAnalysis.confidenceScore * 100).toFixed(0)}%.`;
    if (taskAnalysis.relevantDomains.length > 0) {
      reasoning += ` Focused on domains: ${taskAnalysis.relevantDomains.join(', ')}.`;
    }
    return reasoning;
  }
}
