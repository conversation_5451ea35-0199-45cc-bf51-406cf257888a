import chalk from 'chalk';

// Type definition for streaming results - Vercel AI SDK compatible
export interface StreamedRunResult<T, U> {
  [Symbol.asyncIterator]: () => AsyncIterator<any>;
  completed?: Promise<any>;
  state?: any;
  toTextStream?: (options?: any) => AsyncIterable<string>;
}

export interface StreamOptions {
  onText?: (text: string) => void;
  onAgentSwitch?: (agentName: string) => void;
  onToolCall?: (toolName: string, args: any) => void;
  onReasoning?: (reasoning: string) => void;
  onComplete?: (result: any) => void;
  onError?: (error: Error) => void;
  onTraceEvent?: (event: any) => void;
  showProgress?: boolean;
  captureReasoning?: boolean;
}

export async function handleStream(
  stream: any,
  options: StreamOptions = {}
) {
  const {
    onText = (text) => process.stdout.write(text),
    onComplete,
    onError,
    showProgress = true,
  } = options;

  try {
    let buffer = '';
    let usage: any = undefined;
    
    // Handle Vercel AI SDK streams
    if (stream && typeof stream[Symbol.asyncIterator] === 'function') {
      for await (const chunk of stream) {
        if (typeof chunk === 'string') {
          buffer += chunk;
          onText(chunk);
        }
      }
    } else if (stream && stream.textStream) {
      for await (const chunk of stream.textStream) {
        buffer += chunk;
        onText(chunk);
      }
    } else if (stream && typeof stream === 'string') {
      buffer = stream;
      onText(stream);
    }

    // Best-effort usage extraction for simple streams
    try {
      const maybeUsage = (stream as any)?.usage;
      if (maybeUsage) {
        usage = typeof maybeUsage === 'function' ? await maybeUsage() : await maybeUsage;
      }
    } catch {}

    if (onComplete) {
      onComplete({
        finalOutput: buffer,
        fullText: buffer,
        usage,
      });
    }

    return {
      finalOutput: buffer,
      fullText: buffer,
      usage,
    };
  } catch (error) {
    if (onError) {
      onError(error as Error);
    }
    throw error;
  }
}

// Compatibility function for legacy code
export async function streamToText(stream: any): Promise<string> {
  let text = '';
  
  if (typeof stream === 'string') {
    return stream;
  }
  
  if (stream && stream.textStream) {
    for await (const chunk of stream.textStream) {
      text += chunk;
    }
  }
  
  return text;
}
