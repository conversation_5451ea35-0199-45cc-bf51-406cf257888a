import { EventEmitter } from 'events';
import { InterruptionEvent, InterruptionRecord, InterruptionStatus } from '../types/interruption';

/**
 * InterruptionBus manages pause/cancel/steer/replace-next events per session.
 * It also owns an AbortController per active session so model streams can be aborted fast.
 */
class InterruptionBus extends EventEmitter {
  private records = new Map<string, InterruptionRecord>(); // key: sessionId
  private controllers = new Map<string, AbortController>();
  private injectedContext = new Map<string, Array<{ message?: string; priority?: 'low'|'medium'|'high'; constraints?: Record<string, any> }>>();
  private replaceNext = new Map<string, { message?: string; priority?: 'low'|'medium'|'high'; constraints?: Record<string, any> }>();
  private lastEventKey = new Map<string, string>(); // idempotency guard

  /** Ensure an AbortController exists for this session and return it */
  ensureController(sessionId: string): AbortController {
    let c = this.controllers.get(sessionId);
    if (!c) { c = new AbortController(); this.controllers.set(sessionId, c); }
    return c;
  }

  getAbortSignal(sessionId: string): AbortSignal | undefined {
    const c = this.controllers.get(sessionId);
    return c?.signal;
  }

  clearController(sessionId: string) {
    this.controllers.delete(sessionId);
  }

  /** Record and broadcast an interruption. Returns the new status. */
  handle(event: InterruptionEvent): InterruptionStatus {
    const key = `${event.type}|${event.sessionId}|${event.stepId || ''}|${event.timestamp}`;
    if (this.lastEventKey.get(event.sessionId) === key) {
      // idempotent: ignore duplicate
      return this.records.get(event.sessionId)?.status || 'acknowledged';
    }
    this.lastEventKey.set(event.sessionId, key);

    let rec = this.records.get(event.sessionId);
    if (!rec) {
      rec = { lastEvent: event, status: 'pending', history: [{ status: 'pending', at: Date.now(), note: 'received' }] };
      this.records.set(event.sessionId, rec);
    } else {
      rec.lastEvent = event;
      rec.history.push({ status: 'pending', at: Date.now(), note: 'received' });
    }

    // Emit immediate acknowledgment for UI
    rec.status = 'acknowledged';
    rec.history.push({ status: 'acknowledged', at: Date.now(), note: 'ack' });
    this.emit('ack', { sessionId: event.sessionId, stepId: event.stepId, event });

    // Apply type-specific transitions
    switch (event.type) {
      case 'cancel': {
        // Abort running stream ASAP
        this.ensureController(event.sessionId).abort();
        rec.status = 'cancelled';
        rec.history.push({ status: 'cancelled', at: Date.now(), note: 'aborted' });
        this.emit('cancel', { sessionId: event.sessionId, stepId: event.stepId, event });
        break;
      }
      case 'pause': {
        try { this.ensureController(event.sessionId).abort(); } catch {}
        rec.status = 'paused';
        rec.history.push({ status: 'paused', at: Date.now(), note: 'aborted for pause' });
        this.emit('pause', { sessionId: event.sessionId, stepId: event.stepId, event });
        break;
      }
      case 'steer': {
        // Save injected context to be applied to next run
        const payload = event.payload || {};
        const arr = this.injectedContext.get(event.sessionId) || [];
        arr.push(payload);
        this.injectedContext.set(event.sessionId, arr);
        rec.lastAppliedPayload = payload;
        rec.status = 'applied';
        rec.history.push({ status: 'applied', at: Date.now(), note: 'steer context stored' });
        this.emit('steer', { sessionId: event.sessionId, stepId: event.stepId, event });
        break;
      }
      case 'replace-next': {
        const payload = event.payload || {};
        this.replaceNext.set(event.sessionId, payload);
        rec.lastAppliedPayload = payload;
        rec.status = 'applied';
        rec.history.push({ status: 'applied', at: Date.now(), note: 'replace-next stored' });
        this.emit('replace-next', { sessionId: event.sessionId, stepId: event.stepId, event });
        break;
      }
    }

    return rec.status;
  }

  getRecord(sessionId: string): InterruptionRecord | undefined {
    return this.records.get(sessionId);
  }

  consumeInjectedContext(sessionId: string): Array<{ message?: string; priority?: 'low'|'medium'|'high'; constraints?: Record<string, any> }> {
    const arr = this.injectedContext.get(sessionId) || [];
    this.injectedContext.delete(sessionId);
    return arr;
  }

  consumeReplaceNext(sessionId: string): { message?: string; priority?: 'low'|'medium'|'high'; constraints?: Record<string, any> } | undefined {
    const v = this.replaceNext.get(sessionId);
    this.replaceNext.delete(sessionId);
    return v;
  }

  resume(sessionId: string) {
    const rec = this.records.get(sessionId);
    if (!rec) return;
    // Create a fresh controller for next run
    this.controllers.set(sessionId, new AbortController());
    rec.status = 'resumed';
    rec.history.push({ status: 'resumed', at: Date.now(), note: 'new controller' });
    this.emit('resumed', { sessionId });
  }

  clear(sessionId: string) {
    this.records.delete(sessionId);
    this.controllers.delete(sessionId);
    this.injectedContext.delete(sessionId);
    this.replaceNext.delete(sessionId);
    this.lastEventKey.delete(sessionId);
  }
}

export const interruptBus = new InterruptionBus();
export default interruptBus;
