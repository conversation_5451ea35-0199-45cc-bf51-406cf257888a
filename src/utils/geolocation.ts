/*
 * Geolocation Utility
 *
 * Production-ready helpers around the browser's navigator.geolocation API
 * with permission handling, robust error mapping, and AbortSignal support.
 *
 * Usage:
 *   import { getCoordinates } from "./geolocation";
 *   const { coords } = await getCoordinates({ enableHighAccuracy: true });
 *   console.log(coords.latitude, coords.longitude);
 */

export type GeolocationPermissionStatus = "granted" | "denied" | "prompt" | "unsupported";

export interface GeoOptions {
  enableHighAccuracy?: boolean; // Default: false
  timeoutMs?: number; // Default: 10000
  maximumAgeMs?: number; // Default: 0
  // If true and current permission is "denied", throw immediately without prompting
  failFastOnDenied?: boolean; // Default: true
}

export interface Coordinates {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number | null;
  altitudeAccuracy?: number | null;
  heading?: number | null;
  speed?: number | null;
}

export interface GeoResult {
  coords: Coordinates;
  timestamp: number;
  permission: GeolocationPermissionStatus;
}

export enum GeoErrorCode {
  PERMISSION_DENIED = "PERMISSION_DENIED",
  POSITION_UNAVAILABLE = "POSITION_UNAVAILABLE",
  TIMEOUT = "TIMEOUT",
  UNSUPPORTED = "UNSUPPORTED",
  INSECURE_CONTEXT = "INSECURE_CONTEXT",
  NOT_BROWSER = "NOT_BROWSER",
  UNKNOWN = "UNKNOWN",
}

export class GeolocationError extends Error {
  readonly code: GeoErrorCode;
  readonly cause?: unknown;

  constructor(code: GeoErrorCode, message?: string, cause?: unknown) {
    super(message || code);
    this.name = "GeolocationError";
    this.code = code;
    this.cause = cause;
  }
}

/** Check if running in a browser environment. */
function isBrowser(): boolean {
  return typeof window !== "undefined" && typeof navigator !== "undefined";
}

/** Check if geolocation API is available. */
export function isGeolocationSupported(): boolean {
  return isBrowser() && !!navigator.geolocation;
}

/** Check if context is secure (required by many browsers for geolocation). */
function isSecureContext(): boolean {
  if (!isBrowser()) return false;
  // Some environments (e.g., Capacitor) may not set location.protocol cleanly; fall back
  try {
    return (window.isSecureContext ?? window.location.protocol === "https:") ||
      // Allow file protocol for development previews in some browsers
      window.location.protocol === "file:";
  } catch {
    return false;
  }
}

/** Query the current geolocation permission state using the Permissions API when available. */
export async function queryGeolocationPermission(): Promise<GeolocationPermissionStatus> {
  if (!isBrowser()) return "unsupported";
  const anyNav: any = navigator as any;
  if (!anyNav.permissions?.query) return "prompt"; // Permissions API not supported
  try {
    const status = await anyNav.permissions.query({ name: "geolocation" as PermissionName });
    // status.state can be: "granted" | "denied" | "prompt"
    if (status.state === "granted" || status.state === "denied" || status.state === "prompt") {
      return status.state;
    }
    return "prompt";
  } catch {
    return "prompt";
  }
}

/**
 * Get the user's current coordinates with robust permission handling and error mapping.
 * @param options GeoOptions
 * @param signal Optional AbortSignal to cancel the request
 */
export async function getCoordinates(
  options: GeoOptions = {},
  signal?: AbortSignal
): Promise<GeoResult> {
  const {
    enableHighAccuracy = false,
    timeoutMs = 10000,
    maximumAgeMs = 0,
    failFastOnDenied = true,
  } = options;

  if (!isBrowser()) {
    throw new GeolocationError(
      GeoErrorCode.NOT_BROWSER,
      "Geolocation can only be used in a browser environment"
    );
  }

  if (!isSecureContext()) {
    // Many browsers require HTTPS or localhost; provide actionable error
    throw new GeolocationError(
      GeoErrorCode.INSECURE_CONTEXT,
      "Geolocation requires a secure context (https:// or localhost)."
    );
  }

  if (!isGeolocationSupported()) {
    throw new GeolocationError(GeoErrorCode.UNSUPPORTED, "Geolocation is not supported");
  }

  const permission = await queryGeolocationPermission();
  if (permission === "denied" && failFastOnDenied) {
    throw new GeolocationError(
      GeoErrorCode.PERMISSION_DENIED,
      "Geolocation permission denied by the user/browser settings"
    );
  }

  // getCurrentPosition wrapper with AbortSignal and race timeout
  return new Promise<GeoResult>((resolve, reject) => {
    let settled = false;
    let timeoutId: number | undefined;

    const clear = () => {
      if (timeoutId !== undefined) {
        window.clearTimeout(timeoutId);
      }
      if (signal) {
        signal.removeEventListener("abort", onAbort);
      }
    };

    const onAbort = () => {
      if (settled) return;
      settled = true;
      clear();
      reject(new GeolocationError(GeoErrorCode.TIMEOUT, "Geolocation request aborted"));
    };

    if (signal?.aborted) {
      return onAbort();
    }

    if (signal) {
      signal.addEventListener("abort", onAbort);
    }

    // Enforce our own timeout in addition to the API timeout, for extra safety
    timeoutId = window.setTimeout(() => {
      if (settled) return;
      settled = true;
      clear();
      reject(new GeolocationError(GeoErrorCode.TIMEOUT, `Timed out after ${timeoutMs} ms`));
    }, timeoutMs + 100); // small buffer beyond API timeout

    try {
      navigator.geolocation.getCurrentPosition(
        (pos: GeolocationPosition) => {
          if (settled) return;
          settled = true;
          clear();
          const { coords, timestamp } = pos;
          resolve({
            coords: {
              latitude: coords.latitude,
              longitude: coords.longitude,
              accuracy: coords.accuracy,
              altitude: coords.altitude,
              altitudeAccuracy: coords.altitudeAccuracy,
              heading: coords.heading,
              speed: coords.speed,
            },
            timestamp,
            permission,
          });
        },
        (err: GeolocationPositionError | any) => {
          if (settled) return;
          settled = true;
          clear();
          // Map error codes per spec: 1 PERMISSION_DENIED, 2 POSITION_UNAVAILABLE, 3 TIMEOUT
          if (err && typeof err.code === "number") {
            switch (err.code) {
              case 1:
                reject(
                  new GeolocationError(
                    GeoErrorCode.PERMISSION_DENIED,
                    err.message || "User denied geolocation"
                  )
                );
                return;
              case 2:
                reject(
                  new GeolocationError(
                    GeoErrorCode.POSITION_UNAVAILABLE,
                    err.message || "Position unavailable"
                  )
                );
                return;
              case 3:
                reject(
                  new GeolocationError(
                    GeoErrorCode.TIMEOUT,
                    err.message || "Geolocation timed out"
                  )
                );
                return;
              default:
                // fall-through to unknown
                break;
            }
          }
          reject(new GeolocationError(GeoErrorCode.UNKNOWN, err?.message || "Unknown error", err));
        },
        {
          enableHighAccuracy,
          timeout: Math.max(0, timeoutMs),
          maximumAge: Math.max(0, maximumAgeMs),
        }
      );
    } catch (e: any) {
      if (settled) return;
      settled = true;
      clear();
      reject(new GeolocationError(GeoErrorCode.UNKNOWN, e?.message || "Unknown error", e));
    }
  });
}

/**
 * Watch the user's position changes. Returns an unsubscribe function.
 * The callback receives GeoResult-like payloads; errors are mapped to GeolocationError.
 */
export function watchPosition(
  onSuccess: (result: GeoResult) => void,
  onError: (error: GeolocationError) => void,
  options: GeoOptions = {}
): () => void {
  const { enableHighAccuracy = false, maximumAgeMs = 0, timeoutMs = 10000 } = options;

  if (!isBrowser()) {
    onError(
      new GeolocationError(
        GeoErrorCode.NOT_BROWSER,
        "Geolocation can only be used in a browser environment"
      )
    );
    return () => {};
  }
  if (!isSecureContext()) {
    onError(
      new GeolocationError(
        GeoErrorCode.INSECURE_CONTEXT,
        "Geolocation requires a secure context (https:// or localhost)."
      )
    );
    return () => {};
  }
  if (!isGeolocationSupported()) {
    onError(new GeolocationError(GeoErrorCode.UNSUPPORTED, "Geolocation is not supported"));
    return () => {};
  }

  let watchId = -1;
  queryGeolocationPermission()
    .then((permission) => {
      try {
        watchId = navigator.geolocation.watchPosition(
          (pos: GeolocationPosition) => {
            const { coords, timestamp } = pos;
            onSuccess({
              coords: {
                latitude: coords.latitude,
                longitude: coords.longitude,
                accuracy: coords.accuracy,
                altitude: coords.altitude,
                altitudeAccuracy: coords.altitudeAccuracy,
                heading: coords.heading,
                speed: coords.speed,
              },
              timestamp,
              permission,
            });
          },
          (err: GeolocationPositionError | any) => {
            if (err && typeof err.code === "number") {
              switch (err.code) {
                case 1:
                  onError(
                    new GeolocationError(
                      GeoErrorCode.PERMISSION_DENIED,
                      err.message || "User denied geolocation"
                    )
                  );
                  return;
                case 2:
                  onError(
                    new GeolocationError(
                      GeoErrorCode.POSITION_UNAVAILABLE,
                      err.message || "Position unavailable"
                    )
                  );
                  return;
                case 3:
                  onError(
                    new GeolocationError(
                      GeoErrorCode.TIMEOUT,
                      err.message || "Geolocation timed out"
                    )
                  );
                  return;
                default:
                  break;
              }
            }
            onError(new GeolocationError(GeoErrorCode.UNKNOWN, err?.message || "Unknown error", err));
          },
          {
            enableHighAccuracy,
            timeout: Math.max(0, timeoutMs),
            maximumAge: Math.max(0, maximumAgeMs),
          }
        );
      } catch (e: any) {
        onError(new GeolocationError(GeoErrorCode.UNKNOWN, e?.message || "Unknown error", e));
      }
    })
    .catch((e) => {
      onError(new GeolocationError(GeoErrorCode.UNKNOWN, (e as any)?.message || "Unknown error", e));
    });

  return () => {
    if (watchId !== -1) {
      try {
        navigator.geolocation.clearWatch(watchId);
      } catch {
        // ignore
      }
    }
  };
}

/** Convenience: get only latitude/longitude with a simple call. */
export async function getLatLng(
  options?: GeoOptions,
  signal?: AbortSignal
): Promise<{ latitude: number; longitude: number }> {
  const { coords } = await getCoordinates(options, signal);
  return { latitude: coords.latitude, longitude: coords.longitude };
}
