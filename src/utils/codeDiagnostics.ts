import * as fs from 'fs/promises';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface FileError {
  type: 'ts' | 'prettier' | 'json' | 'other';
  message: string;
  file: string;
  line: number;
  column?: number;
  code?: string;
  snippet?: string;
}

export interface FileDiagnostics {
  file: string;
  ok: boolean;
  errors: FileError[];
  formattingIssues?: boolean;
  notes?: string[];
}

export interface BuildCheckResult {
  ok: boolean;
  command: string;
  errors: FileError[];
  raw?: string;
}

async function readSnippet(filePath: string, line: number, radius = 2): Promise<string> {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    const lines = content.split('\n');
    const idx = Math.max(0, line - 1);
    const start = Math.max(0, idx - radius);
    const end = Math.min(lines.length - 1, idx + radius);
    const numbered = [] as string[];
    for (let i = start; i <= end; i++) {
      const ln = (i + 1).toString().padStart(4, ' ');
      const marker = i === idx ? '>' : ' ';
      numbered.push(`${marker} ${ln}: ${lines[i]}`);
    }
    return numbered.join('\n');
  } catch {
    return '';
  }
}

function isTSLike(filePath: string): boolean {
  const ext = path.extname(filePath).toLowerCase();
  return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);
}

function isJSON(filePath: string): boolean {
  return path.extname(filePath).toLowerCase() === '.json';
}

async function runCmd(cmd: string, opts: { cwd?: string } = {}) {
  try {
    const { stdout, stderr } = await execAsync(cmd, { cwd: opts.cwd, maxBuffer: 5 * 1024 * 1024 });
    return { ok: true, stdout, stderr };
  } catch (e: any) {
    return { ok: false, stdout: e.stdout || '', stderr: e.stderr || e.message || '' };
  }
}

function parseTscOutput(out: string): Array<{ file: string; line: number; column?: number; code?: string; message: string }> {
  const lines = (out || '').split(/\r?\n/);
  const results: Array<{ file: string; line: number; column?: number; code?: string; message: string }> = [];
  const r1 = /^(?<file>[^(:]+)\((?<line>\d+),(?<col>\d+)\):\s+error\s+(?<code>TS\d+):\s+(?<msg>.*)$/;
  const r2 = /^(?<file>[^:]+):(?<line>\d+):(?<col>\d+)\s+-\s+error\s+(?<code>TS\d+):\s+(?<msg>.*)$/;
  for (const l of lines) {
    const m = l.match(r1) || l.match(r2);
    if (m && m.groups) {
      const file = m.groups.file.trim();
      const line = parseInt(m.groups.line, 10);
      const column = parseInt(m.groups.col, 10);
      const code = m.groups.code;
      const message = (m.groups.msg || '').trim();
      results.push({ file, line, column, code, message });
    }
  }
  return results;
}

function parsePrettierOutput(out: string, err: string): Array<{ file: string; line: number; column?: number; message: string }> {
  const text = `${out}\n${err}`;
  const results: Array<{ file: string; line: number; column?: number; message: string }> = [];
  // Example: [error] src/index.ts: SyntaxError: Expression expected. (23:7)
  const rx = /\[error\]\s+(?<file>[^:]+):\s+SyntaxError:\s+(?<msg>.*)\s+\((?<line>\d+):(?<col>\d+)\)/g;
  let m: RegExpExecArray | null;
  while ((m = rx.exec(text)) !== null) {
    const file = m.groups?.file || '';
    const line = parseInt(m.groups?.line || '0', 10) || 0;
    const column = parseInt(m.groups?.col || '0', 10) || undefined;
    const message = (m.groups?.msg || '').trim();
    if (file && line > 0) results.push({ file, line, column, message });
  }
  return results;
}

export async function runTypeCheck(filterFiles?: string[], cwd?: string): Promise<FileError[]> {
  // Prefer project script if available
  let cmd = 'bun run typecheck';
  let res = await runCmd(cmd, { cwd });
  if (res.ok && !res.stderr) return [];
  // Fallback to tsc
  cmd = 'bunx tsc -p tsconfig.json --noEmit --pretty false';
  res = await runCmd(cmd, { cwd });
  if (!res.ok) {
    // If bunx not available, try npx
    const res2 = await runCmd('npx -y tsc -p tsconfig.json --noEmit --pretty false', { cwd });
    if (!res2.ok) {
      // Could be no tsc installed; in that case, no type info
      return [];
    }
    res = res2;
  }
  const parsed = parseTscOutput(`${res.stdout}\n${res.stderr}`);
  // Optionally filter by given files
  const filtered = Array.isArray(filterFiles) && filterFiles.length > 0
    ? parsed.filter(e => filterFiles.some(f => path.resolve(f) === path.resolve(e.file)))
    : parsed;
  return filtered.map((e) => ({
    type: 'ts',
    message: e.message,
    file: e.file,
    line: e.line,
    column: e.column,
    code: e.code,
  }));
}

export async function runPrettierCheck(files: string[], cwd?: string): Promise<FileError[]> {
  if (!files || files.length === 0) return [];
  const quoted = files.map(f => `"${f.replace(/"/g, '\\"')}"`).join(' ');
  let cmd = `bunx prettier --check ${quoted}`;
  let res = await runCmd(cmd, { cwd });
  if (res.ok) return [];
  // Fallback to npx
  res = await runCmd(`npx -y prettier --check ${quoted}`, { cwd });
  if (res.ok) return [];
  const parsed = parsePrettierOutput(res.stdout, res.stderr);
  return await Promise.all(parsed.map(async (p) => ({
    type: 'prettier' as const,
    message: p.message,
    file: p.file,
    line: p.line,
    column: p.column,
    snippet: await readSnippet(p.file, p.line)
  })));
}

export async function diagnoseFile(filePath: string, opts: { includeFormatting?: boolean } = {}): Promise<FileDiagnostics> {
  const absolute = path.resolve(filePath);
  const errors: FileError[] = [];
  const notes: string[] = [];
  // JSON syntax
  if (isJSON(absolute)) {
    try {
      const content = await fs.readFile(absolute, 'utf-8');
      JSON.parse(content);
    } catch (e: any) {
      const m = /position\s(\d+)/i.exec(e.message || '');
      let line = 0;
      if (m) {
        try {
          const pos = parseInt(m[1], 10);
          const text = await fs.readFile(absolute, 'utf-8');
          const before = text.slice(0, pos);
          line = before.split(/\n/).length;
        } catch {}
      }
      errors.push({ type: 'json', message: e.message || 'Invalid JSON', file: absolute, line: line || 1, snippet: await readSnippet(absolute, line || 1) });
    }
  }
  // TS/JS type/syntax
  if (isTSLike(absolute)) {
    const tsErrors = await runTypeCheck([absolute]);
    for (const e of tsErrors) {
      const snippet = await readSnippet(e.file, e.line);
      errors.push({ ...e, snippet });
    }
  }
  // Optional formatting check
  let formattingIssues = false;
  if (opts.includeFormatting && (isTSLike(absolute) || isJSON(absolute))) {
    const pErrs = await runPrettierCheck([absolute]);
    if (pErrs.length > 0) {
      formattingIssues = true;
      errors.push(...pErrs);
      notes.push('Prettier reported issues; run formatter or update code style.');
    }
  }

  return {
    file: absolute,
    ok: errors.length === 0,
    errors,
    formattingIssues,
    notes: notes.length ? notes : undefined
  };
}

export async function diagnoseFiles(files: string[], opts: { includeFormatting?: boolean } = {}): Promise<{ ok: boolean; results: FileDiagnostics[] }> {
  const unique = Array.from(new Set(files.map(f => path.resolve(f))));
  const results: FileDiagnostics[] = [];
  // Run a single tsc pass and then filter for performance
  const tsLike = unique.filter(isTSLike);
  const typeErrors = tsLike.length > 0 ? await runTypeCheck(tsLike) : [];
  const byFile: Record<string, FileError[]> = {};
  for (const err of typeErrors) {
    const key = path.resolve(err.file);
    (byFile[key] = byFile[key] || []).push(err);
  }
  for (const file of unique) {
    const diag: FileDiagnostics = { file, ok: true, errors: [] };
    // JSON validation
    if (isJSON(file)) {
      try { JSON.parse(await fs.readFile(file, 'utf-8')); } catch (e: any) {
        const m = /position\s(\d+)/i.exec(e.message || '');
        let line = 1;
        if (m) {
          const pos = parseInt(m[1], 10);
          const text = await fs.readFile(file, 'utf-8');
          const before = text.slice(0, pos);
          line = before.split(/\n/).length;
        }
        const snippet = await readSnippet(file, line);
        diag.errors.push({ type: 'json', message: e.message || 'Invalid JSON', file, line, snippet });
      }
    }
    // TS errors from batch run
    const errs = (byFile[file] || []);
    for (const e of errs) {
      const snippet = await readSnippet(e.file, e.line);
      diag.errors.push({ ...e, snippet });
    }
    // Formatting optionally
    if (opts.includeFormatting && (isTSLike(file) || isJSON(file))) {
      const pErrs = await runPrettierCheck([file]);
      if (pErrs.length > 0) {
        diag.errors.push(...pErrs);
        diag.formattingIssues = true;
      }
    }
    diag.ok = diag.errors.length === 0;
    results.push(diag);
  }
  return { ok: results.every(r => r.ok), results };
}

export async function getModifiedFiles(opts: { includeUntracked?: boolean; cwd?: string } = {}): Promise<string[]> {
  const { includeUntracked = true, cwd } = opts;
  const res = await runCmd('git status --porcelain', { cwd });
  if (!res.ok) return [];
  const files: string[] = [];
  const lines = (res.stdout || '').split(/\r?\n/);
  for (const l of lines) {
    if (!l.trim()) continue;
    const status = l.slice(0, 2);
    const file = l.slice(3).trim();
    if (!file) continue;
    if (status.includes('M') || status.includes('A') || status.includes('R') || (includeUntracked && status.includes('?'))) {
      files.push(path.resolve(cwd ?? process.cwd(), file));
    }
  }
  return files;
}

async function exists(p: string): Promise<boolean> {
  try { await fs.access(p as any); return true; } catch { return false; }
}

async function which(cmd: string, cwd?: string): Promise<boolean> {
  const r = await runCmd(process.platform === 'win32' ? `where ${cmd}` : `which ${cmd}`, { cwd });
  return r.ok && !!(r.stdout || '').trim();
}

function parseDartAnalyze(out: string): FileError[] {
  const lines = (out || '').split(/\r?\n/);
  const results: FileError[] = [];
  const rx = /^(?<file>[^:]+):(\s?)(?<line>\d+):(\s?)(?<col>\d+)\s+•\s+(?<msg>.*)$/;
  for (const l of lines) {
    const m = l.match(rx) as any;
    if (m && m.groups) {
      const file = m.groups.file.trim();
      const line = parseInt(m.groups.line, 10) || 1;
      const column = parseInt(m.groups.col, 10) || 1;
      const message = (m.groups.msg || '').trim();
      results.push({ type: 'other', file, line, column, message });
    }
  }
  return results;
}

function parseRuff(out: string): FileError[] {
  const lines = (out || '').split(/\r?\n/);
  const results: FileError[] = [];
  const rx = /^(?<file>.*?):(?<line>\d+):(?<col>\d+)\s+(?<code>[A-Z]\d{2,4})\s+(?<msg>.*)$/;
  for (const l of lines) {
    const m = l.match(rx) as any;
    if (m && m.groups) {
      const file = m.groups.file.trim();
      const line = parseInt(m.groups.line, 10) || 1;
      const column = parseInt(m.groups.col, 10) || 1;
      const message = `${m.groups.code}: ${(m.groups.msg || '').trim()}`;
      results.push({ type: 'other', file, line, column, message });
    }
  }
  return results;
}

function parseBlack(out: string, err: string): FileError[] {
  const text = `${out}\n${err}`;
  const results: FileError[] = [];
  const rx = /would reformat\s+(?<file>[^\s]+)/g;
  let m: RegExpExecArray | null;
  while ((m = rx.exec(text)) !== null) {
    const file = m.groups?.file || '';
    if (file) results.push({ type: 'other', file, line: 1, message: 'Black would reformat file' });
  }
  return results;
}

async function runNodeChecks(cwd?: string): Promise<BuildCheckResult> {
  // Try bun typecheck first
  let cmd = 'bun run typecheck';
  let res = await runCmd(cmd, { cwd });
  if (res.ok && !res.stderr) return { ok: true, command: cmd, errors: [] };
  // Try project build script
  cmd = 'bun run build';
  res = await runCmd(cmd, { cwd });
  if (res.ok) return { ok: true, command: cmd, errors: [] };
  // Fallback to bunx tsc
  cmd = 'bunx tsc -p tsconfig.json --noEmit --pretty false';
  res = await runCmd(cmd, { cwd });
  if (res.ok) {
    const parsed = parseTscOutput(`${res.stdout}\n${res.stderr}`);
    const errs: FileError[] = await Promise.all(parsed.map(async p => ({ type: 'ts', message: p.message, file: p.file, line: p.line, column: p.column, code: p.code, snippet: await readSnippet(p.file, p.line) })));
    return { ok: errs.length === 0, command: cmd, errors: errs, raw: `${res.stdout}\n${res.stderr}` };
  }
  // Fallback to npx tsc
  cmd = 'npx -y tsc -p tsconfig.json --noEmit --pretty false';
  res = await runCmd(cmd, { cwd });
  if (res.ok) {
    const parsed = parseTscOutput(`${res.stdout}\n${res.stderr}`);
    const errs: FileError[] = await Promise.all(parsed.map(async p => ({ type: 'ts', message: p.message, file: p.file, line: p.line, column: p.column, code: p.code, snippet: await readSnippet(p.file, p.line) })));
    return { ok: errs.length === 0, command: cmd, errors: errs, raw: `${res.stdout}\n${res.stderr}` };
  }
  return { ok: true, command: 'node_checks_skipped', errors: [] };
}

async function runDartChecks(cwd?: string): Promise<BuildCheckResult> {
  const hasFlutter = await which('flutter', cwd);
  const cmd = hasFlutter ? 'flutter analyze' : 'dart analyze';
  const res = await runCmd(cmd, { cwd });
  const text = `${res.stdout}\n${res.stderr}`;
  const errs = parseDartAnalyze(text);
  return { ok: res.ok && errs.length === 0, command: cmd, errors: errs, raw: text };
}

async function runPythonChecks(cwd?: string): Promise<BuildCheckResult> {
  const errors: FileError[] = [];
  let commands: string[] = [];
  if (await which('ruff', cwd)) { commands.push('ruff check .'); }
  if (await which('black', cwd)) { commands.push('black --check .'); }
  for (const c of commands) {
    const r = await runCmd(c, { cwd });
    if (c.startsWith('ruff')) errors.push(...parseRuff(`${r.stdout}\n${r.stderr}`));
    if (c.startsWith('black')) errors.push(...parseBlack(r.stdout, r.stderr));
  }
  return { ok: errors.length === 0, command: commands.join(' && ') || 'python_checks_skipped', errors };
}

async function detectLanguages(cwd?: string): Promise<{ node: boolean; dart: boolean; python: boolean; go: boolean; rust: boolean }> {
  const c = cwd || process.cwd();
  const node = await exists(path.join(c, 'package.json'));
  const dart = await exists(path.join(c, 'pubspec.yaml')) || (await hasAnyExt(c, '.dart'));
  const python = await exists(path.join(c, 'pyproject.toml')) || await exists(path.join(c, 'requirements.txt')) || (await hasAnyExt(c, '.py'));
  const go = await exists(path.join(c, 'go.mod')) || (await hasAnyExt(c, '.go'));
  const rust = await exists(path.join(c, 'Cargo.toml')) || (await hasAnyExt(c, '.rs'));
  return { node, dart, python, go, rust };
}

async function hasAnyExt(dir: string, ext: string): Promise<boolean> {
  try {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const e of entries) {
      if (e.isDirectory()) {
        if (['node_modules', '.git', 'dist', 'build', 'coverage', '.next', 'out', 'public', 'docker', '.cache'].includes(e.name)) continue;
        if (await hasAnyExt(path.join(dir, e.name), ext)) return true;
      } else if (e.isFile() && e.name.toLowerCase().endsWith(ext)) {
        return true;
      }
    }
  } catch {}
  return false;
}

export async function runBuildCheck(cwd?: string, opts: { thorough?: boolean } = {}): Promise<BuildCheckResult> {
  const langs = await detectLanguages(cwd);
  const results: BuildCheckResult[] = [];
  if (langs.node) results.push(await runNodeChecks(cwd));
  if (langs.dart) {
    results.push(await runDartChecks(cwd));
    if (opts.thorough && await which('dart', cwd)) {
      // Opportunistically run build_runner when configured in pubspec
      try {
        const pubspecPath = path.join(cwd || process.cwd(), 'pubspec.yaml');
        const hasPubspec = await exists(pubspecPath);
        if (hasPubspec) {
          const content = await fs.readFile(pubspecPath, 'utf-8');
          if (/build_runner\s*:/i.test(content)) {
            const r = await runCmd('dart run build_runner build -d', { cwd });
            if (!r.ok) {
              results.push({ ok: false, command: 'dart run build_runner build -d', errors: [{ type: 'other', file: pubspecPath, line: 1, message: (r.stderr || r.stdout || 'build_runner failed').slice(0, 4000) }] });
            } else {
              results.push({ ok: true, command: 'dart run build_runner build -d', errors: [] });
            }
          }
        }
      } catch {}
    }
  }
  if (langs.python) results.push(await runPythonChecks(cwd));
  if (langs.rust && await which('cargo', cwd)) {
    const r = await runCmd('cargo check', { cwd });
    const ok = r.ok;
    results.push({ ok, command: 'cargo check', errors: ok ? [] : [{ type: 'other', file: path.join(cwd || '.', ''), line: 1, message: (r.stderr || r.stdout || 'cargo check failed').slice(0, 4000) }] });
  }
  if (langs.go && await which('go', cwd)) {
    const r = await runCmd('go build ./...', { cwd });
    const ok = r.ok;
    results.push({ ok, command: 'go build ./...', errors: ok ? [] : [{ type: 'other', file: path.join(cwd || '.', ''), line: 1, message: (r.stderr || r.stdout || 'go build failed').slice(0, 4000) }] });
  }

  if (results.length === 0) {
    return { ok: true, command: 'no_checks_detected', errors: [] };
  }
  const okAll = results.every(r => r.ok);
  const errors = results.flatMap(r => r.errors);
  const command = results.map(r => r.command).join(' && ');
  return { ok: okAll, command, errors };
}
