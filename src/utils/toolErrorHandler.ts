/**
 * Tool Error Handler with Retry Logic
 * Provides intelligent error feedback and retry mechanisms for tool calls
 */

import { z, ZodIssue } from 'zod';

export interface ToolCallError {
  toolName: string;
  error: Error | string;
  parameters?: any;
  attemptNumber: number;
  timestamp: number;
}

export interface ToolRetryConfig {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
  enableIntelligentFeedback: boolean;
}

export interface ToolErrorFeedback {
  errorType: 'json_format' | 'missing_params' | 'invalid_type' | 'api_error' | 'system_error' | 'unknown';
  userMessage: string;
  correctiveInstructions: string;
  exampleCorrectCall?: string;
  shouldRetry: boolean;
  isSystemError?: boolean;
  requiresDiagnosticAgent?: boolean;
  recoveryStrategy?: 'restart' | 'reconnect' | 'configure' | 'install' | 'manual';
}

export interface DiagnosticResult {
  success: boolean;
  message: string;
  actionsTaken?: string[];
  errorDetails?: string;
}

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: ToolRetryConfig = {
  maxRetries: 3,
  retryDelayMs: 1000,
  backoffMultiplier: 1.5,
  enableIntelligentFeedback: true,
};

// Track error patterns per model
const modelErrorPatterns = new Map<string, Map<string, number>>();

/**
 * Analyze tool call error and generate corrective feedback
 */
export function analyzeToolError(
  error: Error | string,
  toolName: string,
  _parameters?: any,
  toolSchema?: z.ZodObject<any>
): ToolErrorFeedback {
  const errorMessage = typeof error === 'string' ? error : error.message;

  // First check if this is a system error that requires diagnostic attention
  if (isSystemError(errorMessage)) {
    const recovery = analyzeSystemError(errorMessage);
    return {
      errorType: 'system_error',
      userMessage: `System error detected: ${toolName} failed due to system issue`,
      correctiveInstructions: `I detected a system-level issue: ${recovery.description}. Let me attempt to fix this automatically.`,
      shouldRetry: true,
      isSystemError: true,
      requiresDiagnosticAgent: true,
      recoveryStrategy: recovery.strategy,
    };
  }

  // Parameter name errors (e.g., 'query' instead of 'pattern')
  if (errorMessage.includes('Invalid parameters') && errorMessage.includes('Did you mean')) {
    const suggestionMatch = errorMessage.match(/Did you mean: (.+)\?/);
    const corrections = suggestionMatch ? suggestionMatch[1] : '';
    return {
      errorType: 'invalid_type',
      userMessage: `Tool call failed: Incorrect parameter names for ${toolName}`,
      correctiveInstructions: `You used incorrect parameter names. ${corrections ? `Please make these corrections: ${corrections}` : 'Check the parameter names.'}`,
      exampleCorrectCall: generateExampleCall(toolName, toolSchema),
      shouldRetry: true,
    };
  }

  // Invalid parameters without suggestions
  if (errorMessage.includes('Invalid parameters')) {
    const validParamsMatch = errorMessage.match(/Valid parameters are: (.+)/);
    const validParams = validParamsMatch ? validParamsMatch[1] : '';
    return {
      errorType: 'invalid_type',
      userMessage: `Tool call failed: Invalid parameters for ${toolName}`,
      correctiveInstructions: `You used invalid parameter names. ${validParams ? `Valid parameters are: ${validParams}` : 'Please check the tool schema.'}`,
      exampleCorrectCall: generateExampleCall(toolName, toolSchema),
      shouldRetry: true,
    };
  }

  // JSON formatting errors
  if (errorMessage.includes('Invalid JSON') || errorMessage.includes('JSON')) {
    return {
      errorType: 'json_format',
      userMessage: `Tool call failed: Invalid JSON format for ${toolName}`,
      correctiveInstructions: `Please ensure all parameters are properly formatted as a valid JSON object. Do not include comments or trailing commas. Ensure all string values are properly quoted.`,
      exampleCorrectCall: generateExampleCall(toolName, toolSchema),
      shouldRetry: true,
    };
  }

  // Missing required parameters
  if (errorMessage.includes('required') || errorMessage.includes('missing')) {
    const missingParams = extractMissingParams(errorMessage);
    return {
      errorType: 'missing_params',
      userMessage: `Tool call failed: Missing required parameters for ${toolName}`,
      correctiveInstructions: `The following required parameters are missing: ${missingParams.join(', ')}. Please provide all required parameters.`,
      exampleCorrectCall: generateExampleCall(toolName, toolSchema),
      shouldRetry: true,
    };
  }

  // Type validation errors
  if (errorMessage.includes('type') || errorMessage.includes('Expected')) {
    return {
      errorType: 'invalid_type',
      userMessage: `Tool call failed: Invalid parameter types for ${toolName}`,
      correctiveInstructions: `One or more parameters have incorrect types. Please check that:\n- Strings are quoted\n- Numbers are not quoted\n- Booleans are true/false (not quoted)\n- Arrays use square brackets []`,
      exampleCorrectCall: generateExampleCall(toolName, toolSchema),
      shouldRetry: true,
    };
  }

  // API or network errors
  if (errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('API')) {
    return {
      errorType: 'api_error',
      userMessage: `Tool call failed: API/Network error for ${toolName}`,
      correctiveInstructions: `There was a network or API error. The system will retry automatically.`,
      shouldRetry: true,
    };
  }

  // Unknown errors
  return {
    errorType: 'unknown',
    userMessage: `Tool call failed for ${toolName}: ${errorMessage}`,
    correctiveInstructions: `An unexpected error occurred. Please review your parameters and try again with simpler inputs if possible.`,
    shouldRetry: errorMessage.length < 200, // Don't retry if error is very long (likely unrecoverable)
  };
}

/**
 * Generate an example correct tool call based on schema
 */
function generateExampleCall(toolName: string, schema?: z.ZodObject<any>): string {
  if (!schema) {
    // Provide generic examples for common tools
    const commonExamples: Record<string, string> = {
      'grep_code': '{"pattern": "function getName", "searchPath": "./src", "fileTypes": ["ts", "js"]}',
      'read_file': '{"path": "/path/to/file.ts"}',
      'list_directory': '{"path": "./src"}',
      'web_search': '{"query": "TypeScript best practices"}',
    };

    return commonExamples[toolName] || '{"param1": "value1", "param2": "value2"}';
  }

  // Generate example from schema
  try {
    const shape = schema.shape;
    const example: Record<string, any> = {};

    for (const [key, value] of Object.entries(shape)) {
      if (value instanceof z.ZodString) {
        example[key] = key === 'path' ? './example/path' : `example_${key}`;
      } else if (value instanceof z.ZodNumber) {
        example[key] = 10;
      } else if (value instanceof z.ZodBoolean) {
        example[key] = false;
      } else if (value instanceof z.ZodArray) {
        example[key] = ['item1', 'item2'];
      } else if (value instanceof z.ZodOptional) {
        // Skip optional fields in example
        continue;
      } else {
        example[key] = `<${key}>`;
      }
    }

    return JSON.stringify(example, null, 2);
  } catch {
    return '{"param": "value"}';
  }
}

/**
 * Extract missing parameter names from error message
 */
function extractMissingParams(errorMessage: string): string[] {
  const params: string[] = [];

  // Try to extract parameter names from common error formats
  const patterns = [
    /required.*?["'](\w+)["']/gi,
    /missing.*?["'](\w+)["']/gi,
    /(\w+).*?is required/gi,
  ];

  for (const pattern of patterns) {
    const matches = errorMessage.matchAll(pattern);
    for (const match of matches) {
      if (match[1]) {
        params.push(match[1]);
      }
    }
  }

  return params.length > 0 ? params : ['<parameters>'];
}

/**
 * Track error patterns for a specific model
 */
export function trackModelError(model: string, toolName: string, errorType: string): void {
  if (!modelErrorPatterns.has(model)) {
    modelErrorPatterns.set(model, new Map());
  }

  const modelErrors = modelErrorPatterns.get(model)!;
  const errorKey = `${toolName}:${errorType}`;
  modelErrors.set(errorKey, (modelErrors.get(errorKey) || 0) + 1);
}

/**
 * Get error statistics for a model
 */
export function getModelErrorStats(model: string): {
  totalErrors: number;
  topErrors: Array<{ pattern: string; count: number }>;
} {
  const modelErrors = modelErrorPatterns.get(model);
  if (!modelErrors) {
    return { totalErrors: 0, topErrors: [] };
  }

  const totalErrors = Array.from(modelErrors.values()).reduce((sum, count) => sum + count, 0);
  const topErrors = Array.from(modelErrors.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([pattern, count]) => ({ pattern, count }));

  return { totalErrors, topErrors };
}



/**
 * Validate tool parameters against schema before execution
 */
export function validateToolParameters(
  parameters: any,
  schema?: z.ZodObject<any>
): { valid: boolean; errors: string[] } {
  if (!schema) {
    // Basic validation without schema
    if (parameters === null || parameters === undefined) {
      return { valid: false, errors: ['Parameters are required'] };
    }

    if (typeof parameters !== 'object') {
      return { valid: false, errors: ['Parameters must be an object'] };
    }

    return { valid: true, errors: [] };
  }

  try {
    schema.parse(parameters);
    return { valid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.issues.map((e: ZodIssue) => `${e.path.join('.')}: ${e.message}`);
      return { valid: false, errors };
    }

    return { valid: false, errors: ['Validation failed'] };
  }
}

/**
 * Format tool error for display to user
 */
export function formatToolErrorForUser(
  error: ToolCallError,
  feedback: ToolErrorFeedback
): string {
  const parts = [
    `❌ Tool call failed: ${error.toolName}`,
    `Attempt: ${error.attemptNumber}`,
    `Error: ${feedback.userMessage}`,
  ];

  if (feedback.correctiveInstructions) {
    parts.push(`\n💡 Instructions: ${feedback.correctiveInstructions}`);
  }

  if (feedback.exampleCorrectCall) {
    parts.push(`\n📝 Example:\n${feedback.exampleCorrectCall}`);
  }

  return parts.join('\n');
}

/**
 * Create a tool wrapper that validates parameters and throws on error
 */
export function createValidatingToolWrapper(
  originalTool: any,
  toolName: string,
  expectedParameters?: Record<string, any>
) {
  // Import here to avoid circular dependency
  const { getToolSchema, suggestParameterCorrections } = require('./toolSchemas');

  // Use provided schema or look up from registry
  const schema = expectedParameters || getToolSchema(toolName);

  // If we don't have a registry schema but the tool already declares an inputSchema
  // (e.g., MCP-derived tools or Vercel AI tools), skip extra validation silently.
  if (!schema) {
    try {
      if (originalTool && (originalTool as any).inputSchema) {
        return originalTool;
      }
    } catch {}
    console.warn(`⚠️ No validation schema found for tool: ${toolName}`);
    return originalTool; // Return unwrapped tool if no schema
  }

  return {
    ...originalTool,
    invoke: async (context: any, argsJson: string) => {
      try {
        const args = JSON.parse(argsJson);

        // Apply parameter aliases to normalize keys (e.g., path -> filePath)
        const { getToolAliases } = require('./toolSchemas');
        const aliases = getToolAliases(toolName) || {};
        const normalizedArgs: Record<string, any> = {};
        for (const [key, val] of Object.entries(args)) {
          const canonical = (aliases as any)[key] || key;
          normalizedArgs[canonical] = val;
        }

        // Best-effort type coercion to reduce fragile failures from minor mismatches
        const coercedArgs: Record<string, any> = { ...normalizedArgs };
        for (const [param, config] of Object.entries(schema)) {
          if (config && Object.prototype.hasOwnProperty.call(coercedArgs, param)) {
            const expectedType = (config as any).type as string | undefined;
            if (expectedType) {
              const originalValue = coercedArgs[param];
              coercedArgs[param] = coerceParameterValue(coercedArgs[param], expectedType, param);
              // Debug logging for boolean coercion issues
              if (expectedType === 'boolean' && typeof originalValue === 'object') {
                console.log(`🔧 Coerced ${param} from object to: ${coercedArgs[param]} (type: ${typeof coercedArgs[param]})`);
              }
            }
          }
        }

        // Check for missing required parameters
        const missingParams: string[] = [];
        for (const [param, config] of Object.entries(schema)) {
          if (config && typeof config === 'object' && 'required' in config && config.required && !(param in coercedArgs)) {
            missingParams.push(param);
          }
        }

        if (missingParams.length > 0) {
          throw new Error(`Required parameters missing: ${missingParams.join(', ')}`);
        }

        // Check for invalid parameter names (e.g., 'query' instead of 'pattern')
        const validParams = Object.keys(schema);
        const invalidParams = Object.keys(coercedArgs).filter(p => !validParams.includes(p));

        if (invalidParams.length > 0) {
          // Use the enhanced suggestion system
          const suggestions = suggestParameterCorrections(toolName, invalidParams);

          const errorMsg = suggestions.length > 0
            ? `Invalid parameters: ${invalidParams.join(', ')}. Did you mean: ${suggestions.join(', ')}?`
            : `Invalid parameters: ${invalidParams.join(', ')}. Valid parameters are: ${validParams.join(', ')}`;

          throw new Error(errorMsg);
        }

        // Type validation (basic) - validate after coercion
        for (const [param, value] of Object.entries(coercedArgs)) {
          const paramConfig = schema[param];
          if (paramConfig && typeof paramConfig === 'object' && 'type' in paramConfig && paramConfig.type) {
            // After coercion, the value should match the expected type
            // Skip validation if null/undefined and it's optional
            if (value === null || value === undefined) {
              continue;
            }
            if (!validateParameterType(value, paramConfig.type)) {
              // Provide more helpful error message with actual value type
              const actualType = Array.isArray(value) ? 'array' : typeof value;
              console.error(`❌ Validation failed for ${param}:`, {
                expectedType: paramConfig.type,
                actualType,
                value: JSON.stringify(value).substring(0, 100),
                isBoolean: typeof value === 'boolean'
              });
              throw new Error(`Parameter '${param}' expected ${paramConfig.type} but got ${actualType} after coercion. Value: ${JSON.stringify(value).substring(0, 100)}`);
            }
          }
        }

        // Call the original tool
        return originalTool.invoke(context, JSON.stringify(coercedArgs));
      } catch (error) {
        // Re-throw with better error message
        if (error instanceof Error) {
          if (error.message.includes('JSON')) {
            throw new Error(`Invalid JSON format for ${toolName}: ${error.message}`);
          }
          throw error;
        }
        throw new Error(`Tool execution failed: ${error}`);
      }
    }
  };
}

/**
 * Validate parameter type
 */
function validateParameterType(value: any, expectedType: string): boolean {
  switch (expectedType) {
    case 'string':
      return typeof value === 'string';
    case 'number':
      return typeof value === 'number' && !isNaN(value);
    case 'boolean':
      return typeof value === 'boolean';
    case 'array':
      return Array.isArray(value);
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value);
    default:
      return true; // Unknown type, allow
  }
}

/**
 * Attempt to coerce a parameter value into the expected basic type.
 * This makes the system resilient to LLMs producing slightly mismatched shapes.
 */
function coerceParameterValue(value: any, expectedType: string, paramName?: string): any {
  try {
    if (value === null || value === undefined) return value;

    switch (expectedType) {
      case 'boolean': {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
          const v = value.trim().toLowerCase();
          if (['true', '1', 'yes', 'y', 'on', 'enabled'].includes(v)) return true;
          if (['false', '0', 'no', 'n', 'off', 'disabled'].includes(v)) return false;
        }
        if (typeof value === 'number') return value !== 0;
        if (typeof value === 'object') {
          // Common wrappers from various toolchains
          const candidateKeys = ['value', 'bool', 'boolean', 'enabled', 'set'];
          for (const k of candidateKeys) {
            if (Object.prototype.hasOwnProperty.call(value, k)) {
              return coerceParameterValue((value as any)[k], 'boolean', paramName);
            }
          }
          // As a last resort, treat presence of an object as true for flags like createDirs
          return true;
        }
        return value;
      }
      case 'array': {
        if (Array.isArray(value)) return value;
        if (typeof value === 'string') {
          // Support comma-separated list or single string -> array
          const trimmed = value.trim();
          if (trimmed.includes(',')) return trimmed.split(',').map(s => s.trim()).filter(Boolean);
          return [trimmed];
        }
        if (typeof value === 'object') {
          // Try common containers
          const containerKeys = ['values', 'items', 'list', 'array', 'value'];
          for (const k of containerKeys) {
            if (Array.isArray((value as any)[k])) return (value as any)[k];
          }
          // If object is array-like (numeric keys), convert to array
          const numericKeys = Object.keys(value).every(k => /^\d+$/.test(k));
          if (numericKeys) {
            const arr: any[] = [];
            Object.keys(value).sort((a, b) => Number(a) - Number(b)).forEach(k => arr.push((value as any)[k]));
            return arr;
          }
          // Fallback: attempt to use object values if they look primitive
          const vals = Object.values(value);
          if (vals.every(v => ['string', 'number', 'boolean'].includes(typeof v))) return vals;
        }
        return value;
      }
      case 'number': {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
          const n = Number(value);
          if (!Number.isNaN(n)) return n;
        }
        return value;
      }
      case 'string': {
        if (typeof value === 'string') return value;
        if (typeof value === 'number' || typeof value === 'boolean') return String(value);
        if (typeof value === 'object') {
          if (Object.prototype.hasOwnProperty.call(value, 'value')) return String((value as any).value);
        }
        return value;
      }
      case 'object':
      default:
        return value;
    }
  } catch (error) {
    console.warn(`⚠️ Coercion error for ${expectedType} parameter ${paramName}:`, error);
    return value;
  }
}

/**
 * Check if an error is a system-level error that requires diagnostic attention
 */
export function isSystemError(error: string): boolean {
  const lowerError = error.toLowerCase();

  const systemErrorPatterns = [
    // Tool execution errors
    'invalid json input for tool',
    'tool execution failed',
    'tool call failed',
    'tool not found',
    'tool returned error',

    // Tool server errors
    'tool server not running',
    'tool server',
    'server not running',
    'server unavailable',

    // MCP server errors
    'mcp server',
    'mcp',
    'model context protocol',

    // Connection errors
    'connection refused',
    'econnrefused',
    'connect econnrefused',
    'connection timeout',
    'connection lost',
    'disconnected',
    'failed to connect',
    'network error',
    'network unreachable',

    // Service errors
    'service unavailable',
    'service not found',
    'service not running',

    // API errors
    'api endpoint',
    'api not responding',
    'api server',
    '500 internal server error',
    '502 bad gateway',
    '503 service unavailable',
    '504 gateway timeout',

    // Process errors
    'process not found',
    'command not found',
    'no such file or directory',
    'permission denied',
    'eacces',
    'eaddrinuse',
    'port already in use',
    'address already in use',

    // Dependency errors
    'module not found',
    'cannot find module',
    'not installed',
    'missing dependency',
    'package not found',

    // Configuration errors
    'config',
    'configuration error',
    'invalid configuration',
    'missing configuration',
    'environment variable',
    'env var',
  ];

  return systemErrorPatterns.some(pattern => lowerError.includes(pattern));
}

/**
 * Error recovery strategies configuration
 */
type ErrorStrategy = {
  patterns: string[];
  requiresAll?: boolean;
  description: string;
  strategy: 'restart' | 'reconnect' | 'configure' | 'install' | 'manual';
  confidence: number;
};

const ERROR_STRATEGIES: ErrorStrategy[] = [
  {
    patterns: ['tool server not running'],
    description: 'Tool server is not running',
    strategy: 'restart',
    confidence: 0.9,
  },
  {
    patterns: ['mcp', 'disconnect'],
    requiresAll: true,
    description: 'MCP server connection lost',
    strategy: 'reconnect',
    confidence: 0.85,
  },
  {
    patterns: ['mcp', 'not connect'],
    requiresAll: true,
    description: 'MCP server connection lost',
    strategy: 'reconnect',
    confidence: 0.85,
  },
  {
    patterns: ['econnrefused', 'connection refused'],
    description: 'Service connection refused',
    strategy: 'restart',
    confidence: 0.8,
  },
  {
    patterns: ['eaddrinuse', 'port', 'address already in use'],
    description: 'Port conflict detected',
    strategy: 'restart',
    confidence: 0.8,
  },
  {
    patterns: ['not found', 'not installed', 'cannot find module'],
    description: 'Missing dependency',
    strategy: 'install',
    confidence: 0.75,
  },
  {
    patterns: ['config', 'environment variable', 'env var'],
    description: 'Configuration issue',
    strategy: 'configure',
    confidence: 0.7,
  },
  {
    patterns: ['permission', 'eacces'],
    description: 'Permission denied',
    strategy: 'manual',
    confidence: 0.6,
  },
  {
    patterns: ['service unavailable', '503'],
    description: 'Service temporarily unavailable',
    strategy: 'restart',
    confidence: 0.7,
  },
  {
    patterns: ['network', 'api', '50'],
    description: 'Network or API error',
    strategy: 'restart',
    confidence: 0.6,
  },
];

/**
 * Analyze system error and determine recovery strategy
 */
export function analyzeSystemError(error: string): {
  description: string;
  strategy: 'restart' | 'reconnect' | 'configure' | 'install' | 'manual';
  confidence: number;
} {
  const lowerError = error.toLowerCase();

  for (const errorStrategy of ERROR_STRATEGIES) {
    const hasMatch = errorStrategy.requiresAll
      ? errorStrategy.patterns.every(pattern => lowerError.includes(pattern))
      : errorStrategy.patterns.some(pattern => lowerError.includes(pattern));

    if (hasMatch) {
      return {
        description: errorStrategy.description,
        strategy: errorStrategy.strategy,
        confidence: errorStrategy.confidence,
      };
    }
  }

  // Default for unrecognized system errors
  return {
    description: 'System error detected',
    strategy: 'manual',
    confidence: 0.4,
  };
}
