import { readFile, realpath } from 'node:fs/promises';
import path from 'node:path';
import { createRequire } from 'node:module';

const require = createRequire(import.meta.url);
const pdfParse: (buffer: Buffer | Uint8Array) => Promise<any> = require('pdf-parse');

export interface PdfExtraction {
  file: string | null;
  numpages: number;
  info: unknown | null;
  metadata: unknown | null;
  text: string;
}

export interface SecureResolveOptions {
  baseDir?: string;
  allowOutsideBase?: boolean;
}

export interface ExtractFromFileOptions extends SecureResolveOptions {
  validateExtension?: boolean;
}

export class PathSecurityError extends Error {
  override name = 'PathSecurityError' as const;
  code = 'PATH_SECURITY_VIOLATION';

  constructor(message: string) {
    super(message);
  }
}

export class FileAccessError extends Error {
  override name = 'FileAccessError' as const;
  code = 'FILE_ACCESS_ERROR';
  override cause?: unknown;

  constructor(message: string, cause?: unknown) {
    super(message);
    if (cause !== undefined) {
      this.cause = cause;
    }
  }
}

export class PdfExtractionError extends Error {
  override name = 'PdfExtractionError' as const;
  code = 'PDF_EXTRACTION_FAILED';
  override cause?: unknown;

  constructor(message: string, cause?: unknown) {
    super(message);
    if (cause !== undefined) {
      this.cause = cause;
    }
  }
}

function toExtractionSchema(data: any, file: string | null): PdfExtraction {
  return {
    file,
    numpages:
      typeof data?.numpages === 'number'
        ? data.numpages
        : typeof data?.numrender === 'number'
          ? data.numrender
          : 0,
    info: data?.info ?? null,
    metadata: data?.metadata ? data.metadata._metadata ?? data.metadata : null,
    text: typeof data?.text === 'string' ? data.text : '',
  };
}

export async function secureResolvePath(
  inputPath: string,
  options: SecureResolveOptions = {},
): Promise<string> {
  if (!inputPath || typeof inputPath !== 'string') {
    throw new PathSecurityError('Invalid path provided');
  }

  const baseResolved = path.resolve(options.baseDir || process.cwd());
  let baseReal: string;
  try {
    baseReal = await realpath(baseResolved);
  } catch {
    // Fallback if baseDir does not exist or cannot be canonicalized
    baseReal = baseResolved;
  }
  const normalizedBase = path.normalize(baseReal + path.sep);

  const candidates: string[] = [];

  if (path.isAbsolute(inputPath)) {
    candidates.push(path.resolve(inputPath));
  } else {
    const fromCwd = path.resolve(process.cwd(), inputPath);
    candidates.push(fromCwd);

    const fromBase = path.resolve(baseResolved, inputPath);
    if (!candidates.includes(fromBase)) {
      candidates.push(fromBase);
    }
  }

  for (const candidate of candidates) {
    try {
      const real = await realpath(candidate);
      const normalizedReal = path.normalize(real + path.sep);
      if (options.allowOutsideBase || normalizedReal.startsWith(normalizedBase)) {
        return real;
      }
    } catch {
      // If the file doesn't exist yet or cannot be resolved, fall back to path-based check
      const normalizedResolved = path.normalize(candidate + path.sep);
      if (options.allowOutsideBase || normalizedResolved.startsWith(normalizedBase)) {
        return candidate;
      }
    }
  }

  if (options.allowOutsideBase && candidates.length > 0) {
    try {
      return await realpath(candidates[0]);
    } catch {
      return candidates[0];
    }
  }

  const attempted = candidates[0] ?? path.resolve(process.cwd(), inputPath);
  throw new PathSecurityError(`Resolved path escapes base directory: ${attempted}`);
}

export async function extractPdfFromBuffer(buffer: Buffer | Uint8Array): Promise<PdfExtraction> {
  try {
    const data = await pdfParse(buffer);
    return toExtractionSchema(data, null);
  } catch (err) {
    throw new PdfExtractionError('Failed to parse PDF buffer', err);
  }
}

export async function extractPdfFromFile(
  inputPath: string,
  options: ExtractFromFileOptions = {},
): Promise<PdfExtraction> {
  let absPath: string;

  try {
    absPath = await secureResolvePath(inputPath, {
      baseDir: options.baseDir,
      allowOutsideBase: !!options.allowOutsideBase,
    });
  } catch (error) {
    if (error instanceof PathSecurityError) {
      throw error;
    }
    throw new PathSecurityError('Invalid path provided');
  }

  if (options.validateExtension !== false) {
    const ext = path.extname(absPath).toLowerCase();
    if (ext !== '.pdf') {
      throw new PathSecurityError(`Refusing to read non-PDF file: ${absPath}`);
    }
  }

  let buffer: Buffer;
  try {
    buffer = await readFile(absPath);
  } catch (err) {
    throw new FileAccessError(`Unable to read file: ${absPath}`, err);
  }

  try {
    const data = await pdfParse(buffer);
    return toExtractionSchema(data, absPath);
  } catch (err) {
    throw new PdfExtractionError(`Failed to extract PDF text from file: ${absPath}`, err);
  }
}

export default {
  extractPdfFromBuffer,
  extractPdfFromFile,
  secureResolvePath,
  PathSecurityError,
  FileAccessError,
  PdfExtractionError,
};
