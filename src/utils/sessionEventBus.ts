import type { AgentEvent } from '../types/agentEvents';

type SessionEventListener = (event: AgentEvent) => void;

type StoredEvent = {
  event: AgentEvent;
  ts: number;
};

class SessionEventBus {
  private readonly buffers = new Map<string, StoredEvent[]>();
  private readonly listeners = new Map<string, Set<SessionEventListener>>();
  private readonly maxEvents = 500;
  private readonly ttlMs = 15 * 60 * 1000; // 15 minutes

  record(sessionId: string | undefined, event: AgentEvent) {
    if (!sessionId) return;
    const ts = this.eventTimestamp(event);
    const stored: StoredEvent = { event, ts };

    const buffer = this.buffers.get(sessionId) ?? [];
    buffer.push(stored);
    if (buffer.length > this.maxEvents) {
      buffer.splice(0, buffer.length - this.maxEvents);
    }
    this.buffers.set(sessionId, buffer);

    this.prune(sessionId, buffer);

    const listeners = this.listeners.get(sessionId);
    if (listeners && listeners.size > 0) {
      for (const listener of listeners) {
        try {
          listener(event);
        } catch (error) {
          console.warn('SessionEventBus listener error:', error);
        }
      }
    }
  }

  getEvents(sessionId: string, sinceTs?: number): AgentEvent[] {
    const buffer = this.buffers.get(sessionId);
    if (!buffer || buffer.length === 0) return [];
    if (typeof sinceTs !== 'number') {
      return buffer.map(entry => entry.event);
    }
    return buffer
      .filter(entry => entry.ts > sinceTs)
      .map(entry => entry.event);
  }

  subscribe(sessionId: string, listener: SessionEventListener): () => void {
    let listeners = this.listeners.get(sessionId);
    if (!listeners) {
      listeners = new Set();
      this.listeners.set(sessionId, listeners);
    }
    listeners.add(listener);
    return () => {
      const set = this.listeners.get(sessionId);
      if (!set) return;
      set.delete(listener);
      if (set.size === 0) {
        this.listeners.delete(sessionId);
      }
    };
  }

  reset(sessionId: string) {
    this.buffers.delete(sessionId);
  }

  private prune(sessionId: string, buffer: StoredEvent[]) {
    const cutoff = Date.now() - this.ttlMs;
    if (buffer.length === 0) return;
    if (buffer[0].ts >= cutoff) return;

    const idx = buffer.findIndex(entry => entry.ts >= cutoff);
    if (idx <= 0) {
      if (idx === -1) {
        // All events expired
        this.buffers.set(sessionId, []);
      }
      return;
    }
    buffer.splice(0, idx);
  }

  private eventTimestamp(event: AgentEvent): number {
    const parsed = Date.parse(event.timestamp);
    return Number.isFinite(parsed) ? parsed : Date.now();
  }
}

export const sessionEventBus = new SessionEventBus();
