/**
 * Cryptographic utilities for secure token storage
 * Implements AES-256-GCM encryption with PBKDF2 key derivation
 */

import { randomBytes, createCipheriv, createDecipheriv, pbkdf2Sync, pbkdf2, createHash, timingSafeEqual } from 'node:crypto';
import * as fs from 'node:fs';
import * as os from 'node:os';
import * as path from 'node:path';

// Encryption configuration constants
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const SALT_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits
// Use strong defaults in production, lighter in development to avoid blocking the event loop
const PBKDF2_ITERATIONS_DEFAULT = (() => {
  const envOverride = process.env.DANTE_PBKDF2_ITERATIONS ? parseInt(process.env.DANTE_PBKDF2_ITERATIONS, 10) : undefined;
  if (envOverride && Number.isFinite(envOverride) && envOverride > 0) return envOverride;
  return process.env.NODE_ENV === 'production' ? 100000 : 20000;
})();
const PBKDF2_DIGEST = 'sha256';

// Key rotation version for future-proofing
const CURRENT_VERSION = 1;

export interface EncryptedData {
  version: number;
  ciphertext: string;
  iv: string;
  tag: string;
  salt: string;
  timestamp: number;
}

export interface KeyDerivationParams {
  password: string;
  salt?: Buffer;
  iterations?: number;
  keyLength?: number;
  digest?: string;
}

export interface EncryptionResult {
  encrypted: EncryptedData;
  keyId?: string;
}

// Async key derivation using libuv thread pool
export async function deriveKeyAsync(params: KeyDerivationParams): Promise<{ key: Buffer; salt: Buffer }> {
  const {
    password,
    salt = randomBytes(SALT_LENGTH),
    iterations = PBKDF2_ITERATIONS_DEFAULT,
    keyLength = KEY_LENGTH,
    digest = PBKDF2_DIGEST
  } = params;

  if (!password || password.length < 12) {
    throw new Error('Password must be at least 12 characters long');
  }

  const key = await new Promise<Buffer>((resolve, reject) => {
    pbkdf2(password, salt, iterations, keyLength, digest, (err, derivedKey) => {
      if (err) return reject(err);
      resolve(derivedKey);
    });
  });

  return { key, salt };
}

/**
 * Derives an encryption key from a password using PBKDF2
 */
export function deriveKey(params: KeyDerivationParams): { key: Buffer; salt: Buffer } {
  const {
    password,
    salt = randomBytes(SALT_LENGTH),
    iterations = PBKDF2_ITERATIONS_DEFAULT,
    keyLength = KEY_LENGTH,
    digest = PBKDF2_DIGEST
  } = params;

  if (!password || password.length < 12) {
    throw new Error('Password must be at least 12 characters long');
  }

  const key = pbkdf2Sync(password, salt, iterations, keyLength, digest);
  
  return { key, salt };
}

/**
 * Encrypts data using AES-256-GCM
 */
export function encrypt(plaintext: string, password: string): EncryptedData {
  if (!plaintext) {
    throw new Error('Plaintext cannot be empty');
  }

  // Derive key from password
  const { key, salt } = deriveKey({ password });
  
  // Generate random IV
  const iv = randomBytes(IV_LENGTH);
  
  // Create cipher
  const cipher = createCipheriv(ALGORITHM, key, iv);
  
  // Encrypt data
  const encrypted = Buffer.concat([
    cipher.update(plaintext, 'utf8'),
    cipher.final()
  ]);
  
  // Get authentication tag
  const tag = cipher.getAuthTag();
  
  // Clear sensitive data from memory
  key.fill(0);
  
  return {
    version: CURRENT_VERSION,
    ciphertext: encrypted.toString('base64'),
    iv: iv.toString('base64'),
    tag: tag.toString('base64'),
    salt: salt.toString('base64'),
    timestamp: Date.now()
  };
}

// Async encrypt using async key derivation
export async function encryptAsync(plaintext: string, password: string): Promise<EncryptedData> {
  if (!plaintext) {
    throw new Error('Plaintext cannot be empty');
  }

  const { key, salt } = await deriveKeyAsync({ password });
  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv(ALGORITHM, key, iv);
  const encrypted = Buffer.concat([cipher.update(plaintext, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  key.fill(0);
  return {
    version: CURRENT_VERSION,
    ciphertext: encrypted.toString('base64'),
    iv: iv.toString('base64'),
    tag: tag.toString('base64'),
    salt: salt.toString('base64'),
    timestamp: Date.now()
  };
}

/**
 * Decrypts data encrypted with AES-256-GCM
 */
export function decrypt(encryptedData: EncryptedData, password: string): string {
  if (!encryptedData || !encryptedData.ciphertext) {
    throw new Error('Invalid encrypted data');
  }

  // Check version compatibility
  if (encryptedData.version > CURRENT_VERSION) {
    throw new Error(`Unsupported encryption version: ${encryptedData.version}`);
  }

  // Reconstruct buffers from base64
  const salt = Buffer.from(encryptedData.salt, 'base64');
  const iv = Buffer.from(encryptedData.iv, 'base64');
  const tag = Buffer.from(encryptedData.tag, 'base64');
  const ciphertext = Buffer.from(encryptedData.ciphertext, 'base64');
  
  // Derive key from password
  const { key } = deriveKey({ password, salt });
  
  // Create decipher
  const decipher = createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(tag);
  
  try {
    // Decrypt data
    const decrypted = Buffer.concat([
      decipher.update(ciphertext),
      decipher.final()
    ]);
    
    // Clear sensitive data from memory
    key.fill(0);
    
    return decrypted.toString('utf8');
  } catch (error) {
    // Clear sensitive data from memory
    key.fill(0);
    
    if (error instanceof Error && error.message.includes('Unsupported state or unable to authenticate data')) {
      throw new Error('Invalid password or corrupted data');
    }
    throw error;
  }
}

// Async decrypt using async key derivation
export async function decryptAsync(encryptedData: EncryptedData, password: string): Promise<string> {
  if (!encryptedData || !encryptedData.ciphertext) {
    throw new Error('Invalid encrypted data');
  }
  if (encryptedData.version > CURRENT_VERSION) {
    throw new Error(`Unsupported encryption version: ${encryptedData.version}`);
  }
  const salt = Buffer.from(encryptedData.salt, 'base64');
  const iv = Buffer.from(encryptedData.iv, 'base64');
  const tag = Buffer.from(encryptedData.tag, 'base64');
  const ciphertext = Buffer.from(encryptedData.ciphertext, 'base64');

  const { key } = await deriveKeyAsync({ password, salt });
  const decipher = createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(tag);
  try {
    const decrypted = Buffer.concat([decipher.update(ciphertext), decipher.final()]);
    key.fill(0);
    return decrypted.toString('utf8');
  } catch (error) {
    key.fill(0);
    if (error instanceof Error && error.message.includes('Unsupported state or unable to authenticate data')) {
      throw new Error('Invalid password or corrupted data');
    }
    throw error;
  }
}

/**
 * Generates a cryptographically secure random string
 */
export function generateSecureRandom(length: number = 32): string {
  return randomBytes(length).toString('base64url');
}

/**
 * Generates a secure nonce for OAuth state parameters
 */
export function generateNonce(): string {
  return generateSecureRandom(32);
}

/**
 * Hashes data using SHA-256
 */
export function hash(data: string): string {
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Performs timing-safe comparison of two strings
 */
export function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  const bufA = Buffer.from(a);
  const bufB = Buffer.from(b);
  
  return timingSafeEqual(bufA, bufB);
}

/**
 * Rotates encryption keys by re-encrypting data with a new password
 */
export function rotateKey(
  encryptedData: EncryptedData,
  oldPassword: string,
  newPassword: string
): EncryptedData {
  // Decrypt with old password
  const plaintext = decrypt(encryptedData, oldPassword);
  
  // Re-encrypt with new password
  return encrypt(plaintext, newPassword);
}

/**
 * Validates encryption data structure
 */
export function isValidEncryptedData(data: any): data is EncryptedData {
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  return (
    typeof data.version === 'number' &&
    typeof data.ciphertext === 'string' &&
    typeof data.iv === 'string' &&
    typeof data.tag === 'string' &&
    typeof data.salt === 'string' &&
    typeof data.timestamp === 'number'
  );
}

/**
 * Creates a key identifier for key management
 */
export function createKeyId(key: Buffer): string {
  return createHash('sha256').update(key).digest('hex').substring(0, 16);
}

/**
 * Derives a master key from environment variable or generates one
 */
export function getMasterKey(): string {
  // Cache the master key to avoid repeated filesystem reads and warning spam
  if ((getMasterKey as any)._cache) {
    return (getMasterKey as any)._cache as string;
  }

  const envKey = process.env.DANTE_MASTER_KEY;
  
  if (envKey && envKey.length >= 32) {
    return envKey;
  }
  
  // In production, this should throw an error
  if (process.env.NODE_ENV === 'production') {
    throw new Error('DANTE_MASTER_KEY environment variable must be set in production');
  }

  // Development fallback - generate and persist a random key
  const devKeyPath = getDevMasterKeyPath();
  let devKey: string;
  if (fs.existsSync(devKeyPath)) {
    devKey = fs.readFileSync(devKeyPath, 'utf8').trim();
  } else {
    devKey = randomBytes(32).toString('hex').slice(0, 32);
    fs.writeFileSync(devKeyPath, devKey, { mode: 0o600 });
  }
  if (devKey.length < 32) {
    throw new Error('Development master key file is invalid or corrupted.');
  }
  // Only warn once per process
  console.warn(`Using development master key from ${devKeyPath}. Set DANTE_MASTER_KEY for production.`);
  (getMasterKey as any)._cache = devKey;
  return (getMasterKey as any)._cache as string;
}

function getDevMasterKeyPath(): string {
  // Store in user home directory, hidden file
  const homeDir = os.homedir();
  return path.join(homeDir, '.dante_dev_master_key');
}
/**
 * Encrypts data for storage with automatic key management
 */
export function encryptForStorage(data: string): EncryptedData {
  const masterKey = getMasterKey();
  return encrypt(data, masterKey);
}

/**
 * Decrypts data from storage with automatic key management
 */
export function decryptFromStorage(encryptedData: EncryptedData): string {
  const masterKey = getMasterKey();
  return decrypt(encryptedData, masterKey);
}

// Async variants for storage helpers
export async function encryptForStorageAsync(data: string): Promise<EncryptedData> {
  const masterKey = getMasterKey();
  return encryptAsync(data, masterKey);
}

export async function decryptFromStorageAsync(encryptedData: EncryptedData): Promise<string> {
  const masterKey = getMasterKey();
  return decryptAsync(encryptedData, masterKey);
}

// Export utility types
export type { EncryptedData as EncryptedToken };
