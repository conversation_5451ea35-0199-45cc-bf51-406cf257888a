import { progressBus } from './progressBus';
import { getContextValue } from './requestContext';

export function reportProgress(stage: string, progress?: number, detail?: any) {
  const sessionId = getContextValue<string>('sessionId');
  const agent = getContextValue<string>('agent');
  const tool = getContextValue<string>('tool');
  progressBus.emitProgress({ sessionId, agent, tool, stage, progress, detail });
}

