import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';

describe('Config Validation', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
    mock.restore();
  });

  describe('validateProviderConfig', () => {
    it('should validate OpenAI config successfully when api<PERSON><PERSON> is present', async () => {
      process.env.OPENAI_API_KEY = 'test-openai-key';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('openai', getConfig().openai)).not.toThrow();
    });

    it('should throw error for missing OpenAI apiKey', async () => {
      delete process.env.OPENAI_API_KEY;
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('openai', getConfig().openai)).toThrow('Missing required configuration for provider "openai": apiKey. Please set the corresponding environment variables.');
    });

    it('should validate Azure config successfully when all required fields are present', async () => {
      process.env.AZURE_API_KEY = 'test-azure-key';
      process.env.AZURE_API_ENDPOINT = 'test-azure-endpoint';
      process.env.AZURE_DEPLOYMENT_NAME = 'test-azure-deployment';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('azure', getConfig().azure)).not.toThrow();
    });

    it('should throw error for missing Azure apiKey', async () => {
      delete process.env.AZURE_API_KEY;
      process.env.AZURE_API_ENDPOINT = 'test-azure-endpoint';
      process.env.AZURE_DEPLOYMENT_NAME = 'test-azure-deployment';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('azure', getConfig().azure)).toThrow('Missing required configuration for provider "azure": apiKey. Please set the corresponding environment variables.');
    });

    it('should throw error for missing Azure endpoint', async () => {
      process.env.AZURE_API_KEY = 'test-azure-key';
      delete process.env.AZURE_API_ENDPOINT;
      process.env.AZURE_DEPLOYMENT_NAME = 'test-azure-deployment';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('azure', getConfig().azure)).toThrow('Missing required configuration for provider "azure": endpoint. Please set the corresponding environment variables.');
    });

    it('should throw error for missing Azure deploymentName', async () => {
      process.env.AZURE_API_KEY = 'test-azure-key';
      process.env.AZURE_API_ENDPOINT = 'test-azure-endpoint';
      delete process.env.AZURE_DEPLOYMENT_NAME;
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('azure', getConfig().azure)).toThrow('Missing required configuration for provider "azure": deploymentName. Please set the corresponding environment variables.');
    });

    it('should validate Anthropic config successfully when apiKey is present', async () => {
      process.env.ANTHROPIC_API_KEY = 'test-anthropic-key';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('anthropic', getConfig().anthropic)).not.toThrow();
    });

    it('should throw error for missing Anthropic apiKey', async () => {
      delete process.env.ANTHROPIC_API_KEY;
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('anthropic', getConfig().anthropic)).toThrow('Missing required configuration for provider "anthropic": apiKey. Please set the corresponding environment variables.');
    });

    it('should validate Gemini config successfully when apiKey is present', async () => {
      process.env.GEMINI_API_KEY = 'test-gemini-key';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('gemini', getConfig().gemini)).not.toThrow();
    });

    it('should throw error for missing Gemini apiKey', async () => {
      delete process.env.GEMINI_API_KEY;
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('gemini', getConfig().gemini)).toThrow('Missing required configuration for provider "gemini": apiKey. Please set the corresponding environment variables.');
    });

    it('should validate Ollama config successfully when baseURL is present', async () => {
      process.env.OLLAMA_BASE_URL = 'http://localhost:11434';
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('ollama', getConfig().ollama)).not.toThrow();
    });

    it('should throw error for missing Ollama baseURL', async () => {
      delete process.env.OLLAMA_BASE_URL;
      const { validateProviderConfig, getConfig } = await import('./config');
      expect(() => validateProviderConfig('ollama', getConfig().ollama)).toThrow('Missing required configuration for provider "ollama": baseURL. Please set the corresponding environment variables.');
    });

    it('should not throw error for unknown provider', async () => {
      const { validateProviderConfig } = await import('./config');
      expect(() => validateProviderConfig('unknown', {})).not.toThrow();
    });

    it('should validate custom provider config successfully when endpoint is present', async () => {
      const { validateProviderConfig } = await import('./config');
      const customConfig = { endpoint: 'http://localhost:8080' };
      expect(() => validateProviderConfig('custom', customConfig)).not.toThrow();
    });

    it('should throw error for missing custom provider endpoint', async () => {
      const { validateProviderConfig } = await import('./config');
      const customConfig = {};
      expect(() => validateProviderConfig('custom', customConfig)).toThrow(
        'Missing required configuration for provider "custom": endpoint. Please set the corresponding environment variables.'
      );
    });
  });

  describe('validateConfig', () => {
    it('should validate AI provider config when AI_PROVIDER is set to openai', async () => {
      process.env.AI_PROVIDER = 'openai';
      process.env.OPENAI_API_KEY = 'test-openai-key';
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).not.toThrow();
    });

    it('should throw error if AI_PROVIDER is openai and OPENAI_API_KEY is missing', async () => {
      process.env.AI_PROVIDER = 'openai';
      delete process.env.OPENAI_API_KEY;
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).toThrow('Missing required configuration for provider "openai": apiKey. Please set the corresponding environment variables.');
    });

    it('should validate summarizer provider config when SUMMARIZER_ENABLED is true and SUMMARIZER_PROVIDER is set to ollama', async () => {
      process.env.SUMMARIZER_ENABLED = 'true';
      process.env.SUMMARIZER_PROVIDER = 'ollama';
      process.env.OLLAMA_BASE_URL = 'http://localhost:11434';
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).not.toThrow();
    });

    it('should throw error if SUMMARIZER_PROVIDER is ollama and OLLAMA_BASE_URL is missing', async () => {
      process.env.SUMMARIZER_ENABLED = 'true';
      process.env.SUMMARIZER_PROVIDER = 'ollama';
      delete process.env.OLLAMA_BASE_URL;
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).toThrow('Missing required configuration for provider "ollama": baseURL. Please set the corresponding environment variables.');
    });

    it('should not throw error if summarizer is disabled', async () => {
      process.env.SUMMARIZER_ENABLED = 'false';
      process.env.AI_PROVIDER = 'openai';
      process.env.OPENAI_API_KEY = 'test-openai-key';
      delete process.env.OLLAMA_BASE_URL; // Ollama config is missing, but summarizer is disabled
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).not.toThrow();
    });

    it('should not throw error if AI_PROVIDER is not set', async () => {
      delete process.env.AI_PROVIDER;
      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).not.toThrow();
    });

    it('should validate summarizer provider config when SUMMARIZER_PROVIDER is set to custom', async () => {
      process.env.SUMMARIZER_ENABLED = 'true';
      process.env.SUMMARIZER_PROVIDER = 'custom';

      const originalConfigModule = await import('./config');
      mock.module('./config', () => {
        return {
          ...originalConfigModule,
          getConfig: () => ({
            ...originalConfigModule.getConfig(),
            custom: { endpoint: 'http://localhost:8080' },
            summarizer: {
              ...originalConfigModule.getConfig().summarizer,
              provider: 'custom',
              enabled: true,
            },
          }),
        };
      });

      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).not.toThrow();
    });

    it('should throw error if SUMMARIZER_PROVIDER is custom and endpoint is missing', async () => {
      process.env.SUMMARIZER_ENABLED = 'true';
      process.env.SUMMARIZER_PROVIDER = 'custom';

      const originalConfigModule = await import('./config');
      mock.module('./config', () => {
        return {
          ...originalConfigModule,
          getConfig: () => ({
            ...originalConfigModule.getConfig(),
            custom: {}, // Missing endpoint
            summarizer: {
              ...originalConfigModule.getConfig().summarizer,
              provider: 'custom',
              enabled: true,
            },
          }),
        };
      });

      const { validateConfig } = await import('./config');
      expect(() => validateConfig()).toThrow(
        'Missing required configuration for provider "custom": endpoint. Please set the corresponding environment variables.'
      );
    });
  });
});


describe('initialContextBudgetRatio config', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
    mock.restore();
  });

  it('defaults to 0.5 when no env is set', async () => {
    delete process.env.INITIAL_CONTEXT_BUDGET_RATIO;
    const { getConfig } = await import('./config');
    expect(getConfig().initialContextBudgetRatio).toBe(0.5);
  });

  it('respects env override when valid (0 &lt; r ≤ 1)', async () => {
    process.env.INITIAL_CONTEXT_BUDGET_RATIO = '0.8';
    const { getConfig } = await import('./config');
    expect(getConfig().initialContextBudgetRatio).toBe(0.8);
  });

  it('clamps values greater than 1 to 1', async () => {
    process.env.INITIAL_CONTEXT_BUDGET_RATIO = '5';
    const { getConfig } = await import('./config');
    expect(getConfig().initialContextBudgetRatio).toBe(1);
  });

  it('clamps zero or negative values to a small positive number &lt;= 1', async () => {
    process.env.INITIAL_CONTEXT_BUDGET_RATIO = '0';
    const { getConfig } = await import('./config');
    const r0 = getConfig().initialContextBudgetRatio;
    expect(r0).toBeGreaterThan(0);
    expect(r0).toBeLessThanOrEqual(1);

    process.env.INITIAL_CONTEXT_BUDGET_RATIO = '-1';
    const rNeg = getConfig().initialContextBudgetRatio;
    expect(rNeg).toBeGreaterThan(0);
    expect(rNeg).toBeLessThanOrEqual(1);
  });

  it('handles non-numeric env by falling back to default 0.5', async () => {
    // @ts-ignore - intentional invalid value
    process.env.INITIAL_CONTEXT_BUDGET_RATIO = 'not-a-number';
    const { getConfig } = await import('./config');
    expect(getConfig().initialContextBudgetRatio).toBe(0.5);
  });
});
