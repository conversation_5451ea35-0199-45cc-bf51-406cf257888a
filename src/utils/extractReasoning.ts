/**
 * Utilities for extracting reasoning from agent responses
 */

export interface ReasoningData {
  reasoning: string;
  confidence?: number;
  assumptions?: string[] | null;
  alternatives?: string[] | null;
  data?: any;
}

/**
 * Extracts reasoning from an agent response
 * Works with both streaming and non-streaming responses
 */
export function extractReasoningFromResponse(response: any): ReasoningData | null {
  // Handle null/undefined
  if (!response) return null;

  // Direct structured output with reasoning
  if (typeof response === 'object' && response.reasoning) {
    return {
      reasoning: response.reasoning,
      confidence: response.confidence,
      assumptions: response.assumptions,
      alternatives: response.alternatives,
      data: response.data || response,
    };
  }

  // OpenAI Agents SDK response format
  if (response?.state?.modelResponses?.[0]?.output?.[0]?.content?.[0]?.text) {
    try {
      const parsed = JSON.parse(response.state.modelResponses[0].output[0].content[0].text);
      if (parsed && typeof parsed === 'object' && parsed.reasoning) {
        return {
          reasoning: parsed.reasoning,
          confidence: parsed.confidence,
          assumptions: parsed.assumptions,
          alternatives: parsed.alternatives,
          data: parsed.data || parsed,
        };
      }
    } catch (e) {
      // Not JSON or parsing failed
    }
  }

  // Check for reasoning in the response text (for models that include it inline)
  if (typeof response === 'string') {
    const reasoningMatch = response.match(/REASONING:\s*([\s\S]*?)(?:ANSWER:|CONFIDENCE:|$)/i);
    const confidenceMatch = response.match(/CONFIDENCE:\s*([\d.]+|low|medium|high)/i);
    
    if (reasoningMatch) {
      let confidence = 0.5;
      if (confidenceMatch) {
        const conf = confidenceMatch[1].toLowerCase();
        if (conf === 'low') confidence = 0.3;
        else if (conf === 'medium') confidence = 0.6;
        else if (conf === 'high') confidence = 0.9;
        else confidence = parseFloat(conf) || 0.5;
      }
      
      return {
        reasoning: reasoningMatch[1].trim(),
        confidence,
        data: response,
      };
    }
  }

  // Check in the actual output content
  const output = response?.state?.output?.[0]?.content;
  if (output && typeof output === 'object' && output.reasoning) {
    return {
      reasoning: output.reasoning,
      confidence: output.confidence,
      assumptions: output.assumptions,
      alternatives: output.alternatives,
      data: output.data || output,
    };
  }

  return null;
}

/**
 * Formats reasoning data for display
 */
export function formatReasoningForDisplay(reasoning: ReasoningData | null): string {
  if (!reasoning) return '';

  const lines: string[] = [];

  // Main reasoning
  if (reasoning.reasoning) {
    lines.push('🧠 **Reasoning Process:**');
    lines.push(reasoning.reasoning);
    lines.push('');
  }

  // Confidence
  if (reasoning.confidence !== undefined) {
    const percentage = (reasoning.confidence * 100).toFixed(0);
    const emoji = reasoning.confidence >= 0.8 ? '✅' : reasoning.confidence >= 0.5 ? '⚠️' : '❌';
    lines.push(`${emoji} **Confidence:** ${percentage}%`);
    lines.push('');
  }

  // Assumptions
  if (reasoning.assumptions && reasoning.assumptions.length > 0) {
    lines.push('📌 **Assumptions:**');
    reasoning.assumptions.forEach(assumption => {
      lines.push(`  • ${assumption}`);
    });
    lines.push('');
  }

  // Alternatives
  if (reasoning.alternatives && reasoning.alternatives.length > 0) {
    lines.push('🔄 **Alternatives Considered:**');
    reasoning.alternatives.forEach(alternative => {
      lines.push(`  • ${alternative}`);
    });
    lines.push('');
  }

  return lines.join('\n');
}

/**
 * Checks if a response contains reasoning
 */
export function hasReasoning(response: any): boolean {
  return extractReasoningFromResponse(response) !== null;
}

/**
 * Extract just the reasoning text from a response
 */
export function getReasoningText(response: any): string | null {
  const reasoning = extractReasoningFromResponse(response);
  return reasoning?.reasoning || null;
}

/**
 * Extract confidence level from a response
 */
export function getConfidence(response: any): number | null {
  const reasoning = extractReasoningFromResponse(response);
  return reasoning?.confidence ?? null;
}