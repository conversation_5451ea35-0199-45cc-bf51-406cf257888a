/**
 * Platform-agnostic utilities for system operations
 */

import * as net from 'net';
import * as os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Interfaces exported for external consumers
export interface PlatformInfo {
  platform: string;
  arch: string;
  version: string;
  type: string;
  isWindows: boolean;
  isMac: boolean;
  isLinux: boolean;
}

export type NormalizePathFn = (path: string) => string;

/**
 * Check if a port is in use (platform-agnostic)
 */
export async function isPortInUse(port: number, host: string = 'localhost'): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.once('error', (err: any) => {
      if (err.code === 'EADDRINUSE') {
        resolve(true);
      } else {
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      server.close();
      resolve(false);
    });
    
    server.listen(port, host);
  });
}

/**
 * Test if a TCP connection can be established to a port
 */
export async function canConnectToPort(port: number, host: string = 'localhost', timeout: number = 5000): Promise<boolean> {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    let connected = false;
    
    const timer = setTimeout(() => {
      socket.destroy();
      resolve(false);
    }, timeout);
    
    socket.connect(port, host, () => {
      connected = true;
      clearTimeout(timer);
      socket.end();
      resolve(true);
    });
    
    socket.on('error', () => {
      clearTimeout(timer);
      resolve(false);
    });
    
    socket.on('close', () => {
      clearTimeout(timer);
      if (!connected) {
        resolve(false);
      }
    });
  });
}

/**
 * Get process information (platform-agnostic)
 */
export async function getProcessVersion(command: string): Promise<string | null> {
  try {
    const { stdout } = await execAsync(`${command} --version`);
    return stdout.trim();
  } catch {
    try {
      // Some commands use -v instead of --version
      const { stdout } = await execAsync(`${command} -v`);
      return stdout.trim();
    } catch {
      return null;
    }
  }
}

/**
 * Check if a command exists in the system PATH
 */
export async function commandExists(command: string): Promise<boolean> {
  const isWindows = os.platform() === 'win32';
  const checkCommand = isWindows ? `where ${command}` : `which ${command}`;
  
  try {
    await execAsync(checkCommand);
    return true;
  } catch {
    return false;
  }
}

/**
 * Find process using a specific port (cross-platform)
 */
export async function findProcessUsingPort(port: number): Promise<{ pid?: number; name?: string } | null> {
  const platform = os.platform();
  
  try {
    if (platform === 'win32') {
      // Windows: Use netstat
      const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
      const lines = stdout.trim().split('\n');
      for (const line of lines) {
        if (line.includes('LISTENING')) {
          const parts = line.trim().split(/\s+/);
          const pid = parseInt(parts[parts.length - 1]);
          if (!isNaN(pid)) {
            try {
              const { stdout: taskList } = await execAsync(`tasklist /FI "PID eq ${pid}" /FO CSV`);
              const taskLines = taskList.trim().split('\n');
              if (taskLines.length > 1) {
                const processName = taskLines[1].split(',')[0].replace(/"/g, '');
                return { pid, name: processName };
              }
            } catch {
              return { pid };
            }
          }
        }
      }
    } else if (platform === 'darwin') {
      // macOS: Use lsof if available, fallback to netstat
      if (await commandExists('lsof')) {
        const { stdout } = await execAsync(`lsof -i :${port} -P -n | grep LISTEN | head -1`);
        if (stdout) {
          const parts = stdout.trim().split(/\s+/);
          const name = parts[0];
          const pid = parseInt(parts[1]);
          return { pid: isNaN(pid) ? undefined : pid, name };
        }
      } else {
        // Fallback to netstat
        const { stdout } = await execAsync(`netstat -anv | grep "\\.${port} " | grep LISTEN`);
        if (stdout) {
          return { name: 'Unknown Process' };
        }
      }
    } else {
      // Linux: Use ss or netstat
      if (await commandExists('ss')) {
        const { stdout } = await execAsync(`ss -tlnp | grep :${port}`);
        if (stdout) {
          const pidMatch = stdout.match(/pid=(\d+)/);
          const pid = pidMatch ? parseInt(pidMatch[1]) : undefined;
          const nameMatch = stdout.match(/\("([^"]+)"/);
          const name = nameMatch ? nameMatch[1] : undefined;
          return { pid, name };
        }
      } else if (await commandExists('netstat')) {
        const { stdout } = await execAsync(`netstat -tlnp 2>/dev/null | grep :${port}`);
        if (stdout) {
          const parts = stdout.trim().split(/\s+/);
          const processInfo = parts[parts.length - 1];
          if (processInfo && processInfo !== '-') {
            const [pid, name] = processInfo.split('/');
            return { pid: parseInt(pid), name };
          }
        }
      }
    }
  } catch {
    // Command failed, port might be free or we don't have permissions
  }
  
  return null;
}

/**
 * Kill a process by PID (cross-platform)
 */
export async function killProcess(pid: number, force: boolean = false): Promise<boolean> {
  const isWindows = os.platform() === 'win32';
  
  try {
    if (isWindows) {
      const flag = force ? '/F' : '';
      await execAsync(`taskkill /PID ${pid} ${flag}`);
    } else {
      const signal = force ? '-9' : '-15';
      await execAsync(`kill ${signal} ${pid}`);
    }
    return true;
  } catch {
    return false;
  }
}

/**
 * Get system platform info
 */
export function getPlatformInfo(): PlatformInfo {
  return {
    platform: os.platform(),
    arch: os.arch(),
    version: os.release(),
    type: os.type(),
    isWindows: os.platform() === 'win32',
    isMac: os.platform() === 'darwin',
    isLinux: os.platform() === 'linux',
  };
}

/**
 * Normalize path for the current platform
 */
export const normalizePath: NormalizePathFn = (path: string): string => {
  const isWindows = os.platform() === 'win32';
  if (isWindows) {
    return path.replace(/\//g, '\\');
  } else {
    return path.replace(/\\/g, '/');
  }
}