import { getContextValue } from './requestContext';
import { cancelBus } from './cancelBus';

export class CancelledError extends Error {
  constructor(message = 'Operation cancelled') {
    super(message);
    this.name = 'CancelledError';
  }
}

export function checkCancellation(throwIfCancelled = true): boolean {
  const sessionId = getContextValue<string>('sessionId');
  if (!sessionId) return false;
  const agent = getContextValue<string>('agent');
  const cancelled = cancelBus.isCancelled(sessionId, agent);
  if (cancelled && throwIfCancelled) {
    throw new CancelledError();
  }
  return cancelled;
}

