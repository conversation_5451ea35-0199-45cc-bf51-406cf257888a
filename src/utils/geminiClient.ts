import { config, validateGeminiConfig, isGeminiModel } from './config';
import { cleanParamsForModel, isReasoningModel } from './modelParamSanitizer';
import { google } from '@ai-sdk/google';
import { generateText, streamText, type CoreMessage } from 'ai';
import { rateLimiter } from '../../server/utils/rateLimiter';
import { withRetries } from '../../server/utils/retry';
import { output_tokens, input_tokens } from '../../server/observability/metrics';

export interface GeminiThinkingConfig {
  reasoning_effort?: 'none' | 'low' | 'medium' | 'high';
  thinking_budget?: number;
  include_thoughts?: boolean;
}

export interface GeminiRequestConfig {
  model: string;
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>;
  stream?: boolean;
  thinking?: GeminiThinkingConfig;
  temperature?: number;
  max_tokens?: number;
  tools?: any[];
  tool_choice?: string | { type: string; function?: { name: string } };
}

/**
 * GeminiClient provides OpenAI-compatible interface for Gemini models
 * Uses the OpenAI SDK with Gemini's endpoint for seamless integration
 */
export class GeminiClient {
  private isAvailable: boolean;

  constructor() {
    this.isAvailable = validateGeminiConfig();
  }

  /**
   * Check if Gemini client is available and configured
   */
  public isConfigured(): boolean {
    return this.isAvailable;
  }

  /**
   * Create a chat completion using Vercel AI SDK (generateText), returning
   * an OpenAI-compatible response shape for existing callers.
   */
  public async createChatCompletion(params: GeminiRequestConfig) {
    if (!this.isAvailable) {
      throw new Error('Gemini client is not configured. Please set GEMINI_API_KEY.');
    }
    const safe = cleanParamsForModel(params);

    // Map legacy messages to Vercel AI SDK shape using system + prompt
    const system = (params.messages || [])
      .filter((m) => m.role === 'system')
      .map((m) => m.content)
      .join('\n');

    // Prefer the last user content as prompt; fallback to concatenation
    const userMessages = (params.messages || []).filter((m) => m.role === 'user');
    const prompt = userMessages.length
      ? userMessages[userMessages.length - 1].content
      : (params.messages || []).map((m) => `${m.role}: ${m.content}`).join('\n');

    const providerOptions: any = {};
    if (params.thinking && params.thinking.thinking_budget) {
      providerOptions.google = {
        thinkingConfig: {
          thinkingBudget: params.thinking.thinking_budget,
          includeThoughts: params.thinking.include_thoughts,
        },
      };
    }

    // Ensure output budget is reasonably above thinking budget if both specified
    let maxOutputTokens = params.max_tokens;
    const tb = params.thinking?.thinking_budget;
    if (typeof maxOutputTokens === 'number' && typeof tb === 'number' && maxOutputTokens <= tb) {
      maxOutputTokens = tb + 1024; // provide headroom for final response
    }
    const limiterKey = `gemini:${params.model}:${config.gemini.apiKey}`;

    const result = await withRetries(async () => {
      await rateLimiter.consume(limiterKey);
      return generateText({
        model: google(params.model),
        system: system || undefined,
        prompt,
        maxOutputTokens,
        providerOptions,
      });
    }, params.model)();

    // Normalize usage if available
    const gm = (result.providerMetadata as any)?.google;
    const usageMeta = gm?.usageMetadata;
    const usage = (result as any).usage
      ? {
          prompt_tokens: (result as any).usage?.promptTokens,
          completion_tokens: (result as any).usage?.completionTokens,
          total_tokens: (result as any).usage?.totalTokens ?? usageMeta?.totalTokenCount,
        }
      : usageMeta
      ? {
          prompt_tokens: usageMeta?.promptTokenCount,
          completion_tokens: usageMeta?.candidatesTokenCount,
          total_tokens: usageMeta?.totalTokenCount,
        }
      : undefined;

    if (usage?.total_tokens) {
      rateLimiter.report(limiterKey, usage.total_tokens);
    }

    if (usage?.prompt_tokens) {
      input_tokens.observe(usage.prompt_tokens);
    }
    if (usage?.completion_tokens) {
      output_tokens.observe(usage.completion_tokens);
    }

    return {
      id: `gemini-${Date.now()}`,
      object: 'chat.completion',
      model: params.model,
      choices: [
        {
          index: 0,
          finish_reason: 'stop',
          message: {
            role: 'assistant',
            content: result.text,
            thinking: (result as any).reasoning,
            tool_calls: [],
          },
        },
      ],
      usage,
      providerMetadata: result.providerMetadata,
      raw: result,
    };
  }

  /**
   * Create a streaming chat completion
   */
  public async createStreamingCompletion(params: GeminiRequestConfig) {
    if (!this.isAvailable) {
      throw new Error('Gemini client is not configured. Please set GEMINI_API_KEY.');
    }
    const safe = cleanParamsForModel(params);

    const system = (params.messages || [])
      .filter((m) => m.role === 'system')
      .map((m) => m.content)
      .join('\n');
    const userMessages = (params.messages || []).filter((m) => m.role === 'user');
    const prompt = userMessages.length
      ? userMessages[userMessages.length - 1].content
      : (params.messages || []).map((m) => `${m.role}: ${m.content}`).join('\n');

    const providerOptions: any = {};
    if (params.thinking && params.thinking.thinking_budget) {
      providerOptions.google = {
        thinkingConfig: {
          thinkingBudget: params.thinking.thinking_budget,
        },
      };
    }

    // Return StreamTextResult; downstream expects .textStream
    // Ensure output budget is reasonably above thinking budget if both specified
    let maxOutputTokens = params.max_tokens;
    const tb = params.thinking?.thinking_budget;
    if (typeof maxOutputTokens === 'number' && typeof tb === 'number' && maxOutputTokens <= tb) {
      maxOutputTokens = tb + 1024;
    }
    const limiterKey = `gemini:${params.model}:${config.gemini.apiKey}`;

    return withRetries(async () => {
      await rateLimiter.consume(limiterKey);
      return streamText({
        model: google(params.model),
        system: system || undefined,
        prompt,
        maxOutputTokens,
        providerOptions,
      });
    }, params.model)();
  }

  /**
   * Get thinking budget based on complexity level
   * Optimized for cost and speed - starts low and increases only when needed
   */
  public static getThinkingBudget(level: 'minimal' | 'low' | 'medium' | 'high' | 'max'): number {
    switch (level) {
      // Gemini 2.5 models require a minimum thinking budget of 512
      case 'minimal': return 512;   // Minimum supported budget across 2.5 models
      case 'low': return 512;       // Simple questions and basic tasks
      case 'medium': return 2048;   // Moderate complexity analysis
      case 'high': return 8192;     // Complex problem solving
      case 'max': return 16384;     // Maximum for repeated errors or very complex tasks
      default: return 512;          // Conservative default
    }
  }

  /**
   * Create thinking configuration for different task complexities
   */
  public static createThinkingConfig(
    complexity: 'simple' | 'medium' | 'complex' | 'orchestration',
    includeThoughts: boolean = true,
    errorCount: number = 0
  ): GeminiThinkingConfig {
    let budgetLevel: 'minimal' | 'low' | 'medium' | 'high' | 'max';

    // Base budget based on complexity
    switch (complexity) {
      case 'simple':
        budgetLevel = 'minimal'; // 512 tokens - quick responses
        break;
      case 'medium':
        budgetLevel = 'low';     // 512 tokens - basic analysis
        break;
      case 'complex':
        budgetLevel = 'medium';  // 2048 tokens - deeper thinking
        break;
      case 'orchestration':
        budgetLevel = 'high';    // 8192 tokens - coordination tasks
        break;
      default:
        budgetLevel = 'low';     // Conservative default
    }

    // Gradually increase budget for repeated errors
    if (errorCount > 0) {
      if (errorCount >= 3) {
        budgetLevel = 'max';     // 16384 tokens - maximum for persistent issues
      } else if (errorCount >= 2) {
        budgetLevel = 'high';    // 8192 tokens - significant thinking needed
      } else if (errorCount >= 1) {
        budgetLevel = 'medium';  // 2048 tokens - more careful analysis
      }
    }

    const thinkingBudget = this.getThinkingBudget(budgetLevel);

    // Only return thinking_budget (omit include_thoughts for google provider)
    // reasoning_effort causes issues with some Gemini models
    return {
      thinking_budget: thinkingBudget,
      include_thoughts: includeThoughts,
    };
  }

  /**
   * Extract thinking/reasoning from Gemini response
   */
  public static extractThinking(response: any): string | null {
    if (!response) {
      return null;
    }

    // For OpenAI-format responses, check choices
    if (response.choices && response.choices[0]) {
      const choice = response.choices[0];

      // Check for thinking in message metadata or extra fields
      if (choice.message?.thinking) {
        return choice.message.thinking;
      }

      // Check for reasoning field
      if (choice.message?.reasoning) {
        return choice.message.reasoning;
      }

      // Check for thought content in message
      if (choice.message?.thought) {
        return choice.message.thought;
      }
    }

    // Fallback: check top-level fields
    if (response.thinking) {
      return response.thinking;
    }

    if (response.reasoning) {
      return response.reasoning;
    }

    return null;
  }

  /**
   * Get model-specific configuration
   */
  public static getModelConfig(modelId: string) {
    const configs = {
      'gemini-2.5-pro': {
        contextWindow: 1048576, // 1M+ tokens
        maxOutput: 65536,
        supportsThinking: true,
        canDisableThinking: false, // Pro cannot disable thinking
        bestFor: 'orchestration',
      },
      'gemini-2.5-flash': {
        contextWindow: 1048576,
        maxOutput: 65536,
        supportsThinking: true,
        canDisableThinking: true,
        bestFor: 'balanced-orchestration',
      },
      'gemini-2.5-flash-lite': {
        contextWindow: 1048576,
        maxOutput: 65536,
        supportsThinking: true,
        canDisableThinking: true,
        bestFor: 'high-throughput',
        defaultThinking: false, // Lite doesn't think by default
      },
    };

    return configs[modelId as keyof typeof configs] || null;
  }
}

// Create a singleton instance
export const geminiClient = new GeminiClient();

/**
 * Helper function to determine if we should use Gemini for a request
 */
export function shouldUseGemini(modelId: string): boolean {
  return isGeminiModel(modelId) && geminiClient.isConfigured();
}

/**
 * Get the appropriate thinking configuration for a task
 */
/**
 * Analyze request content to determine if it needs more thinking budget
 */
export function analyzeRequestComplexity(input: string | any[]): {
  complexity: 'simple' | 'medium' | 'complex';
  requiresMoreThinking: boolean;
  estimatedTokens: number;
} {
  const text = Array.isArray(input)
    ? input.map(item => typeof item === 'string' ? item : JSON.stringify(item)).join(' ')
    : input as string;

  const lowerText = text.toLowerCase();
  const estimatedTokens = Math.ceil(text.length / 4);

  // Complex indicators
  const complexKeywords = [
    'debug', 'error', 'bug', 'issue', 'problem', 'troubleshoot',
    'architecture', 'design pattern', 'refactor', 'optimize',
    'integration', 'database', 'api', 'security', 'performance',
    'algorithm', 'data structure', 'multi-step', 'comprehensive'
  ];

  // Simple indicators
  const simpleKeywords = [
    'hello', 'hi', 'what', 'who', 'how are you', 'tell me about',
    'explain', 'define', 'list', 'show', 'get', 'version'
  ];

  const complexMatches = complexKeywords.filter(keyword => lowerText.includes(keyword)).length;
  const simpleMatches = simpleKeywords.filter(keyword => lowerText.includes(keyword)).length;

  let complexity: 'simple' | 'medium' | 'complex' = 'simple';
  let requiresMoreThinking = false;

  if (estimatedTokens > 10000 || complexMatches >= 3) {
    complexity = 'complex';
    requiresMoreThinking = true;
  } else if (estimatedTokens > 2000 || complexMatches >= 1) {
    complexity = 'medium';
    requiresMoreThinking = complexMatches >= 2;
  } else if (simpleMatches >= 1 && complexMatches === 0) {
    complexity = 'simple';
    requiresMoreThinking = false;
  }

  return { complexity, requiresMoreThinking, estimatedTokens };
}

export function getThinkingConfigForTask(
  taskType: 'orchestration' | 'analysis' | 'coding' | 'simple',
  modelId: string,
  requestContent?: string | any[],
  errorCount: number = 0
): GeminiThinkingConfig {
  const modelConfig = GeminiClient.getModelConfig(modelId);

  if (!modelConfig?.supportsThinking) {
    return {}; // No thinking configuration
  }

  // Analyze request complexity if content is provided
  let adjustedComplexity = taskType;
  if (requestContent) {
    const analysis = analyzeRequestComplexity(requestContent);

    // Upgrade complexity if request analysis suggests it
    if (analysis.requiresMoreThinking) {
      if (taskType === 'simple' && analysis.complexity !== 'simple') {
        adjustedComplexity = analysis.complexity === 'complex' ? 'analysis' : 'coding';
      }
    }

    // Downgrade complexity for obviously simple requests
    if (analysis.complexity === 'simple' && taskType !== 'orchestration') {
      adjustedComplexity = 'simple';
    }
  }

  // Map task types to thinking config complexity
  let configComplexity: 'simple' | 'medium' | 'complex' | 'orchestration';
  switch (adjustedComplexity) {
    case 'orchestration':
      configComplexity = 'orchestration';
      break;
    case 'analysis':
      configComplexity = 'complex';
      break;
    case 'coding':
      configComplexity = 'medium';
      break;
    case 'simple':
    default:
      configComplexity = 'simple';
      break;
  }

  return GeminiClient.createThinkingConfig(configComplexity, true, errorCount);
}
