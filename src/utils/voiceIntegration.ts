/**
 * Voice Integration Utilities
 * Handles the connection between <PERSON>'s orchestration system and voice capabilities
 */

import { runDante } from '../index';
import { extractTextContent, prepareTextForVoice } from './textExtractor';

export interface VoiceIntegrationOptions {
  sessionId?: string;
  voice?: string;
  model?: string;
  stream?: boolean;
}

export interface VoiceIntegrationCallbacks {
  /**
   * Emit a structured event to downstream listeners (e.g. SSE voice stream).
   * The event type should conform to consumer expectations (voiceEventStream).
   */
  emitEvent?: (eventType: string, data: any) => void;
}

export interface VoiceProcessingResult {
  success: boolean;
  text: string;
  voiceOptimizedText: string;
  error?: string;
  metadata?: {
    model: string;
    processingTime: number;
    tokenCount: number;
  };
}

/**
 * Process a voice request through Dante's orchestration system
 * with automatic voice optimization and TTS preparation
 */
export async function processVoiceRequest(
  input: string,
  history: any[] = [],
  options: VoiceIntegrationOptions = {},
  callbacks: VoiceIntegrationCallbacks = {}
): Promise<VoiceProcessingResult> {
  const startTime = Date.now();
  const { emitEvent } = callbacks;
  
  try {
    // Build context from conversation history
    let contextualInput = input;
    let currentAgent = '<PERSON>';
    if (history.length > 0) {
      const turns = history
        .filter((h: any) => h?.type === 'message' && (h?.role === 'user' || h?.role === 'assistant'))
        .map((h: any) => {
          const text = typeof h?.content === 'string' ? h.content : (h?.content?.[0]?.text || h?.content?.text || '');
          return `${h.role === 'user' ? 'User' : 'Dante'}: ${text}`;
        })
        .slice(-12) // Keep recent context
        .join('\n');

      contextualInput = `Current conversation so far:\n${turns}\n\nUser request:\n${input}`;
    }

    let voiceOptimizedText = '';
    let streamedText = '';

    // Voice output callback to capture the optimized text
    const voiceOutputCallback = async (text: string, _voiceOptions: { voice?: string }) => {
      if (typeof text !== 'string' || !text.trim()) return;
      // Many providers resend the full response each time; prefer the longest prefix match
      if (voiceOptimizedText && text.startsWith(voiceOptimizedText)) {
        voiceOptimizedText = text;
        return;
      }
      if (!voiceOptimizedText) {
        voiceOptimizedText = text;
        return;
      }
      // Append only the new delta to avoid repeating previously spoken content
      const delta = text.replace(voiceOptimizedText, '').trim();
      voiceOptimizedText = delta ? `${voiceOptimizedText} ${delta}`.trim() : text;
    };

    const normalizeChunk = (chunk: any): string => {
      if (typeof chunk === 'string') return chunk;
      if (!chunk) return '';
      if (typeof chunk.text === 'string') return chunk.text;
      if (typeof chunk.delta === 'string') return chunk.delta;
      if (typeof chunk.content === 'string') return chunk.content;
      if (typeof chunk.message === 'string') return chunk.message;
      if (Array.isArray(chunk) && chunk.length > 0) {
        return normalizeChunk(chunk[0]);
      }
      return '';
    };

    const collectStreamText = async (streamLike: any) => {
      let aggregated = '';
      try {
        for await (const chunk of streamLike) {
          const textChunk = normalizeChunk(chunk);
          if (!textChunk) continue;
          aggregated += textChunk;
          emitEvent?.('message', { message: textChunk, agent: currentAgent, delta: true });
        }
      } catch (err) {
        console.warn('Voice stream collection failed:', err);
      }
      return aggregated;
    };

    // Run Dante with voice mode enabled
    const result = await runDante(contextualInput, {
      stream: options.stream || false,
      model: options.model,
      sessionId: options.sessionId,
      voiceMode: true,
      voiceOutputCallback,
      onAgentSelected: (payload) => {
        if (payload?.agent) {
          currentAgent = payload.agent;
        }
        emitEvent?.('agent_switch', {
          agent: payload?.agent,
          confidence: payload?.confidence,
          message: payload?.agent ? `Switched to ${payload.agent}` : undefined,
        });
      },
      onDelegationStart: (payload) => {
        const agentName = payload?.agent ?? currentAgent;
        emitEvent?.('delegation_start', {
          ...payload,
          agent: agentName,
        });
      },
      onDelegationEnd: (payload) => {
        const agentName = payload?.agent ?? currentAgent;
        emitEvent?.('delegation_end', {
          ...payload,
          agent: agentName,
        });
      },
      onPlanCreated: (payload) => emitEvent?.('plan_created', {
        ...payload,
        agent: currentAgent,
      }),
      onStepFinish: (payload) => {
        const agentName = payload?.agent || payload?.step?.agent || currentAgent;
        if (agentName) {
          currentAgent = agentName;
        }
        emitEvent?.('step_finish', { ...payload, agent: agentName });
        if (payload?.text && typeof payload.text === 'string') {
          streamedText = payload.text;
          emitEvent?.('message', { message: payload.text, agent: agentName });
        }
        try {
          const toolCalls = Array.isArray(payload?.toolCalls) ? payload.toolCalls : [];
          for (const tc of toolCalls) {
            const name = tc?.toolName || tc?.name || 'tool';
            const args = tc?.input ?? tc?.args ?? tc?.parameters ?? tc?.arguments ?? tc;
            emitEvent?.('tool_call', {
              tool: name,
              args,
              agent: agentName || currentAgent,
              message: `Using ${name}`,
            });
          }
          const toolResults = Array.isArray(payload?.toolResults) ? payload.toolResults : [];
          for (const tr of toolResults) {
            const name = tr?.toolName || tr?.name || 'tool';
            const resultData = tr?.output ?? tr?.result ?? tr?.content ?? tr;
            emitEvent?.('tool_result', {
              tool: name,
              result: typeof resultData === 'string' ? resultData : JSON.stringify(resultData),
              agent: agentName || currentAgent,
            });
          }
        } catch (err) {
          console.debug('Voice integration metadata relay failed:', err);
        }
      },
      onToolCall: (toolName, args) => emitEvent?.('tool_call', {
        tool: toolName,
        args,
        agent: currentAgent,
        message: `Using ${toolName}`,
      }),
      onAgentSwitch: (agentName) => {
        if (agentName) {
          currentAgent = agentName;
        }
        emitEvent?.('agent_switch', {
          agent: agentName,
          message: agentName ? `Switched to ${agentName}` : undefined,
        });
      },
      onText: (text) => {
        if (typeof text === 'string' && text.trim()) {
          streamedText = text;
          emitEvent?.('message', { message: text, agent: currentAgent });
        }
      },
    });

    // If the orchestrator returned a streaming result, consume it to build final text
    if (result && typeof result === 'object') {
      const streamCandidate = (result as any).textStream;
      if (streamCandidate && typeof streamCandidate[Symbol.asyncIterator] === 'function') {
        streamedText += await collectStreamText(streamCandidate);
      } else if (typeof (result as any)[Symbol.asyncIterator] === 'function') {
        streamedText += await collectStreamText(result);
      }
      try {
        await Promise.resolve((result as any).completed);
      } catch {}
    }

    // Extract text content from result using centralized utility
    let responseText = extractTextContent(result);

    if (!responseText && streamedText) {
      responseText = streamedText;
    }

    if (!voiceOptimizedText && streamedText) {
      voiceOptimizedText = prepareTextForVoice(streamedText);
    }

    // Use voice-optimized text if available, otherwise prepare the response text
    const finalVoiceText = voiceOptimizedText || prepareTextForVoice(responseText);

    const processingTime = Date.now() - startTime;
    const tokenCount = Math.ceil((contextualInput.length + responseText.length) / 4);

    return {
      success: true,
      text: responseText,
      voiceOptimizedText: finalVoiceText,
      metadata: {
        model: options.model || 'default',
        processingTime,
        tokenCount,
      }
    };

  } catch (error: any) {
    console.error('Voice request processing failed:', error);
    
    const processingTime = Date.now() - startTime;
    
    return {
      success: false,
      text: '',
      voiceOptimizedText: '',
      error: error?.message || 'Processing failed',
      metadata: {
        model: options.model || 'default',
        processingTime,
        tokenCount: 0,
      }
    };
  }
}

/**
 * Prepare text for voice output by cleaning formatting and optimizing for speech
 */

/**
 * Check if the current session supports voice capabilities
 */
export async function getVoiceCapabilities(_sessionId?: string): Promise<{
  hasVoiceSupport: boolean;
  availableVoices: string[];
  currentModel: string;
  supportsStreaming: boolean;
}> {
  try {

    return {
      hasVoiceSupport: true,
      availableVoices: ['alloy', 'ash', 'ballad', 'coral', 'echo', 'sage', 'shimmer', 'verse'],
      currentModel: 'default', // Session model info not available from current session type
      supportsStreaming: true,
    };
  } catch (error) {
    console.error('Failed to get voice capabilities:', error);
    return {
      hasVoiceSupport: false,
      availableVoices: [],
      currentModel: 'default',
      supportsStreaming: false,
    };
  }
}

/**
 * Validate voice input parameters
 */
export function validateVoiceInput(
  input: string,
  options: VoiceIntegrationOptions
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!input || typeof input !== 'string') {
    errors.push('Input must be a non-empty string');
  }

  if (input.length > 10000) {
    errors.push('Input text is too long (maximum 10,000 characters)');
  }

  if (options.voice && !['alloy', 'ash', 'ballad', 'coral', 'echo', 'sage', 'shimmer', 'verse'].includes(options.voice)) {
    errors.push('Invalid voice selection');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Format voice processing result for API response
 */
export function formatVoiceResponse(result: VoiceProcessingResult): any {
  return {
    success: result.success,
    text: result.text,
    voiceText: result.voiceOptimizedText,
    error: result.error,
    metadata: {
      model: result.metadata?.model,
      processingTime: result.metadata?.processingTime,
      estimatedTokens: result.metadata?.tokenCount,
    }
  };
}
