interface DeviceInfo {
  id: string;
  name: string;
  userAgent: string;
  lastSeen: number;
  ipAddress?: string;
}

interface SyncEvent {
  type: 'session_update' | 'message_added' | 'device_connected' | 'device_disconnected';
  sessionId: string;
  deviceId: string;
  timestamp: number;
  data?: any;
}

class DeviceSyncManager {
  private deviceId: string;
  private eventSource: EventSource | null = null;
  private listeners: Map<string, Set<(event: SyncEvent) => void>> = new Map();
  private connectedDevices: Map<string, DeviceInfo> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  constructor() {
    this.deviceId = this.generateDeviceId();
    this.setupBeforeUnload();
  }

  private generateDeviceId(): string {
    const stored = localStorage.getItem('dante_device_id');
    if (stored) return stored;
    
    const id = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('dante_device_id', id);
    return id;
  }

  private getDeviceName(): string {
    const stored = localStorage.getItem('dante_device_name');
    if (stored) return stored;
    
    // Generate a friendly device name based on user agent
    const ua = navigator.userAgent;
    let name = 'Unknown Device';
    
    if (ua.includes('Mobile') || ua.includes('Android')) {
      name = 'Mobile Device';
    } else if (ua.includes('iPad')) {
      name = 'iPad';
    } else if (ua.includes('iPhone')) {
      name = 'iPhone';
    } else if (ua.includes('Mac')) {
      name = 'Mac';
    } else if (ua.includes('Windows')) {
      name = 'Windows PC';
    } else if (ua.includes('Linux')) {
      name = 'Linux PC';
    }
    
    // Add timestamp to make it unique
    const uniqueName = `${name} (${new Date().toLocaleTimeString()})`;
    localStorage.setItem('dante_device_name', uniqueName);
    return uniqueName;
  }

  async connect(sessionId: string): Promise<void> {
    if (this.eventSource) {
      this.disconnect();
    }

    try {
      const deviceInfo = {
        id: this.deviceId,
        name: this.getDeviceName(),
        userAgent: navigator.userAgent,
        lastSeen: Date.now(),
      };

      // Register device with the server
      await fetch('/api/sync/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, device: deviceInfo }),
      });

      // Establish SSE connection for real-time sync
      this.eventSource = new EventSource(`/api/sync/events?sessionId=${encodeURIComponent(sessionId)}&deviceId=${encodeURIComponent(this.deviceId)}`);
      
      this.eventSource.onopen = () => {
        console.log('Device sync connected');
        this.reconnectAttempts = 0;
        if (this.reconnectTimeout) {
          clearTimeout(this.reconnectTimeout);
          this.reconnectTimeout = null;
        }
      };

      this.eventSource.onmessage = (event) => {
        try {
          const syncEvent: SyncEvent = JSON.parse(event.data);
          this.handleSyncEvent(syncEvent);
        } catch (error) {
          console.error('Failed to parse sync event:', error);
        }
      };

      this.eventSource.onerror = () => {
        console.error('Device sync connection error');
        this.handleReconnect(sessionId);
      };

    } catch (error) {
      console.error('Failed to connect to device sync:', error);
      this.handleReconnect(sessionId);
    }
  }

  private handleReconnect(sessionId: string): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
    this.reconnectAttempts++;

    this.reconnectTimeout = setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect(sessionId);
    }, delay);
  }

  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  private handleSyncEvent(event: SyncEvent): void {
    // Don't process events from our own device
    if (event.deviceId === this.deviceId) return;

    // Update connected devices list
    if (event.type === 'device_connected' && event.data?.device) {
      this.connectedDevices.set(event.deviceId, event.data.device);
    } else if (event.type === 'device_disconnected') {
      this.connectedDevices.delete(event.deviceId);
    }

    // Notify listeners
    const typeListeners = this.listeners.get(event.type);
    if (typeListeners) {
      typeListeners.forEach(listener => listener(event));
    }

    const allListeners = this.listeners.get('*');
    if (allListeners) {
      allListeners.forEach(listener => listener(event));
    }
  }

  addEventListener(eventType: string, listener: (event: SyncEvent) => void): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(listener);
  }

  removeEventListener(eventType: string, listener: (event: SyncEvent) => void): void {
    const typeListeners = this.listeners.get(eventType);
    if (typeListeners) {
      typeListeners.delete(listener);
    }
  }

  async broadcastEvent(sessionId: string, eventType: SyncEvent['type'], data?: any): Promise<void> {
    try {
      await fetch('/api/sync/broadcast', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          deviceId: this.deviceId,
          type: eventType,
          data,
          timestamp: Date.now(),
        }),
      });
    } catch (error) {
      console.error('Failed to broadcast sync event:', error);
    }
  }

  getConnectedDevices(): DeviceInfo[] {
    return Array.from(this.connectedDevices.values());
  }

  getCurrentDevice(): DeviceInfo {
    return {
      id: this.deviceId,
      name: this.getDeviceName(),
      userAgent: navigator.userAgent,
      lastSeen: Date.now(),
    };
  }

  private setupBeforeUnload(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        // Send disconnect event synchronously
        if (this.eventSource) {
          navigator.sendBeacon('/api/sync/disconnect', JSON.stringify({
            deviceId: this.deviceId,
          }));
        }
      });
    }
  }
}

export const deviceSyncManager = new DeviceSyncManager();
export type { DeviceInfo, SyncEvent };