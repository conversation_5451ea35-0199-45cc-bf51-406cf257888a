/**
 * Dynamic Context Consolidator
 * Intelligently consolidates context based on model capabilities
 */

import { getModelLimits, countTokens, truncateToTokenLimit } from './tokenLimiter';
import { sanitizeToolOutput } from '../mcp/utils';

export type ConsolidationLevel = 'minimal' | 'moderate' | 'aggressive';

export interface ConsolidationStrategy {
  model: string;
  contextLimit: number;
  toolResultLimit: number;
  consolidationLevel: ConsolidationLevel;
  preserveEssentialData: boolean;
}

export interface ConsolidationResult {
  consolidatedContent: string;
  originalTokens: number;
  consolidatedTokens: number;
  reductionPercentage: number;
  preservedElements: string[];
}

/**
 * Get consolidation strategy based on model
 */
export function getConsolidationStrategy(model: string): ConsolidationStrategy {
  const limits = getModelLimits(model);
  const contextLimit = limits.maxInputTokens;
  
  // ALL Gemini models have 1M context window regardless of variant
  // The only differences are speed, accuracy, and reasoning capabilities
  if (model.startsWith('gemini-')) {
    return {
      model,
      contextLimit: 900000, // 900K usable from 1M limit (leave buffer)
      toolResultLimit: 50000, // Very generous tool result limit for all Gemini models
      consolidationLevel: 'minimal',
      preserveEssentialData: true,
    };
  }
  
  // Long context models (200K+)
  if (model.includes('long-context') || contextLimit >= 150000) {
    return {
      model,
      contextLimit,
      toolResultLimit: 15000,
      consolidationLevel: 'moderate',
      preserveEssentialData: true,
    };
  }
  
  // Standard models (30K-128K)
  if (contextLimit >= 30000) {
    return {
      model,
      contextLimit,
      toolResultLimit: 5000,
      consolidationLevel: 'aggressive',
      preserveEssentialData: true,
    };
  }
  
  // Small context models (<30K)
  return {
    model,
    contextLimit,
    toolResultLimit: 2000,
    consolidationLevel: 'aggressive',
    preserveEssentialData: false,
  };
}

/**
 * Consolidate tool results based on model strategy
 */
export function consolidateToolResult(
  result: any,
  toolName: string,
  model: string
): string {
  const strategy = getConsolidationStrategy(model);
  
  // For minimal consolidation (Gemini), preserve more content
  if (strategy.consolidationLevel === 'minimal') {
    return sanitizeToolOutput(result);
  }
  
  // Check if result is already a string
  if (typeof result === 'string') {
    if (countTokens(result) > strategy.toolResultLimit) {
      return truncateToTokenLimit(result, strategy.toolResultLimit);
    }
    return result;
  }
  
  // Handle search results specially
  if (result.results && Array.isArray(result.results)) {
    return consolidateSearchResults(result, strategy);
  }
  
  // Handle file listings
  if (result.files && Array.isArray(result.files)) {
    return consolidateFileList(result, strategy);
  }
  
  // Handle directory trees
  if (result.tree || result.structure) {
    return consolidateDirectoryTree(result, strategy);
  }
  
  // Default consolidation
  try {
    const jsonString = JSON.stringify(result, null, 2);
    if (countTokens(jsonString) > strategy.toolResultLimit) {
      return consolidateGenericObject(result, strategy);
    }
    return jsonString;
  } catch {
    return String(result).slice(0, strategy.toolResultLimit * 4);
  }
}

/**
 * Consolidate search results intelligently
 */
function consolidateSearchResults(
  result: any,
  strategy: ConsolidationStrategy
): string {
  const maxResults = strategy.consolidationLevel === 'aggressive' ? 5 :
                     strategy.consolidationLevel === 'moderate' ? 15 : 30;
  
  const consolidated: any = {
    success: result.success,
    pattern: result.pattern,
    totalMatches: result.matches || result.results?.length || 0,
    showing: Math.min(maxResults, result.results?.length || 0),
    results: (result.results || []).slice(0, maxResults).map((r: any) => {
      if (strategy.consolidationLevel === 'aggressive') {
        // Only preserve essential fields
        return {
          file: r.file || r.path,
          line: r.line,
          match: r.match ? r.match.substring(0, 100) + (r.match.length > 100 ? '...' : '') : undefined,
        };
      }
      return r;
    }),
    truncated: (result.results?.length || 0) > maxResults,
  };
  
  if (strategy.consolidationLevel === 'aggressive' && consolidated.totalMatches > 20) {
    // Add summary for large result sets
    consolidated.summary = `Found ${consolidated.totalMatches} matches across multiple files. Showing first ${maxResults} results. Consider refining search criteria for better results.`;
  }
  
  return JSON.stringify(consolidated, null, 2);
}

/**
 * Consolidate file listings
 */
function consolidateFileList(
  result: any,
  strategy: ConsolidationStrategy
): string {
  const maxFiles = strategy.consolidationLevel === 'aggressive' ? 20 :
                   strategy.consolidationLevel === 'moderate' ? 50 : 100;
  
  const files = result.files || [];
  const consolidated: any = {
    success: result.success,
    path: result.path,
    totalFiles: files.length,
    showing: Math.min(maxFiles, files.length),
    files: files.slice(0, maxFiles),
    truncated: files.length > maxFiles,
  };
  
  if (strategy.consolidationLevel === 'aggressive' && files.length > maxFiles) {
    // Group by extension for summary
    const extensions = files.reduce((acc: any, file: string) => {
      const ext = file.split('.').pop() || 'no-ext';
      acc[ext] = (acc[ext] || 0) + 1;
      return acc;
    }, {});
    
    consolidated.summary = {
      byExtension: extensions,
      message: `Showing ${maxFiles} of ${files.length} files. Files grouped by type above.`,
    };
  }
  
  return JSON.stringify(consolidated, null, 2);
}

/**
 * Consolidate directory tree structures
 */
function consolidateDirectoryTree(
  result: any,
  strategy: ConsolidationStrategy
): string {
  const maxDepth = strategy.consolidationLevel === 'aggressive' ? 2 :
                   strategy.consolidationLevel === 'moderate' ? 3 : 5;
  
  const tree = result.tree || result.structure || result;
  
  // Recursively prune tree to max depth
  function pruneTree(node: any, currentDepth: number = 0): any {
    if (currentDepth >= maxDepth) {
      if (typeof node === 'object' && node !== null) {
        const childCount = Object.keys(node).length;
        if (childCount > 0) {
          return `[${childCount} items...]`;
        }
      }
      return node;
    }
    
    if (Array.isArray(node)) {
      return node.slice(0, 10).map(item => pruneTree(item, currentDepth + 1));
    }
    
    if (typeof node === 'object' && node !== null) {
      const pruned: any = {};
      let count = 0;
      for (const [key, value] of Object.entries(node)) {
        if (count >= 20 && strategy.consolidationLevel === 'aggressive') {
          pruned['...'] = `${Object.keys(node).length - count} more items`;
          break;
        }
        pruned[key] = pruneTree(value, currentDepth + 1);
        count++;
      }
      return pruned;
    }
    
    return node;
  }
  
  const consolidated = {
    type: 'directory_tree',
    maxDepth,
    structure: pruneTree(tree),
    note: strategy.consolidationLevel === 'aggressive' ? 
          'Tree pruned to reduce token usage. Use specific queries for detailed exploration.' : undefined,
  };
  
  return JSON.stringify(consolidated, null, 2);
}

/**
 * Consolidate generic objects
 */
function consolidateGenericObject(
  obj: any,
  strategy: ConsolidationStrategy
): string {
  const essential = ['success', 'error', 'message', 'status', 'result', 'data'];
  const consolidated: any = {};
  
  // Always preserve essential fields
  for (const field of essential) {
    if (field in obj) {
      consolidated[field] = obj[field];
    }
  }
  
  if (strategy.consolidationLevel === 'aggressive') {
    // Only keep essential fields for aggressive consolidation
    consolidated._truncated = true;
    consolidated._originalKeys = Object.keys(obj).length;
    return JSON.stringify(consolidated, null, 2);
  }
  
  // For moderate consolidation, include more fields but limit their size
  const maxFieldSize = strategy.consolidationLevel === 'moderate' ? 1000 : 5000;
  
  for (const [key, value] of Object.entries(obj)) {
    if (essential.includes(key)) continue; // Already added
    
    if (typeof value === 'string' && value.length > maxFieldSize) {
      consolidated[key] = value.substring(0, maxFieldSize) + '...[truncated]';
    } else if (Array.isArray(value) && value.length > 10) {
      consolidated[key] = [...value.slice(0, 10), `...${value.length - 10} more items`];
    } else if (typeof value === 'object' && value !== null) {
      const valueStr = JSON.stringify(value);
      if (valueStr.length > maxFieldSize) {
        consolidated[key] = '[object truncated]';
      } else {
        consolidated[key] = value;
      }
    } else {
      consolidated[key] = value;
    }
  }
  
  return JSON.stringify(consolidated, null, 2);
}

/**
 * Helper to safely compute percentage reduction without dividing by zero
 */
function computeReductionPercentage(originalTokens: number, consolidatedTokens: number): number {
  if (!originalTokens || originalTokens <= 0) return 0;
  return Math.round(((originalTokens - consolidatedTokens) / originalTokens) * 100);
}

/**
 * Consolidate conversation history based on model
 */
export function consolidateConversationHistory(
  messages: Array<{ role: string; content: string }>,
  model: string,
  targetTokens?: number
): ConsolidationResult {
  const strategy = getConsolidationStrategy(model);
  const maxTokens = targetTokens || strategy.contextLimit * 0.5; // Use 50% of context for history
  
  const originalTokens = messages.reduce((sum, msg) => sum + countTokens(msg.content), 0);
  
  if (originalTokens <= maxTokens) {
    return {
      consolidatedContent: JSON.stringify(messages),
      originalTokens,
      consolidatedTokens: originalTokens,
      reductionPercentage: 0,
      preservedElements: ['all_messages'],
    };
  }
  
  const preserved: string[] = [];
  const consolidatedMessages: Array<{ role: string; content: string }> = [];
  
  // Always preserve system messages
  const systemMessages = messages.filter(m => m.role === 'system');
  consolidatedMessages.push(...systemMessages);
  preserved.push('system_messages');
  
  // Get non-system messages
  const nonSystemMessages = messages.filter(m => m.role !== 'system');
  
  if (strategy.consolidationLevel === 'minimal') {
    // Keep most recent messages that fit
    const recent = [];
    let tokenCount = systemMessages.reduce((sum, msg) => sum + countTokens(msg.content), 0);
    
    for (let i = nonSystemMessages.length - 1; i >= 0; i--) {
      const msgTokens = countTokens(nonSystemMessages[i].content);
      if (tokenCount + msgTokens <= maxTokens) {
        recent.unshift(nonSystemMessages[i]);
        tokenCount += msgTokens;
      } else {
        break;
      }
    }
    
    consolidatedMessages.push(...recent);
    preserved.push(`recent_${recent.length}_messages`);
    
  } else if (strategy.consolidationLevel === 'moderate') {
    // Keep first and last messages, summarize middle
    const first = nonSystemMessages.slice(0, 2);
    const last = nonSystemMessages.slice(-5);
    const middle = nonSystemMessages.slice(2, -5);
    
    consolidatedMessages.push(...first);
    
    if (middle.length > 0) {
      consolidatedMessages.push({
        role: 'system',
        content: `[${middle.length} messages consolidated: Users discussed ${extractTopics(middle).join(', ')}]`,
      });
      preserved.push('middle_summary');
    }
    
    consolidatedMessages.push(...last);
    preserved.push('first_2_messages', 'last_5_messages');
    
  } else {
    // Aggressive: Only keep most critical messages
    const first = nonSystemMessages[0];
    const last = nonSystemMessages[nonSystemMessages.length - 1];
    
    if (first) consolidatedMessages.push(first);
    
    if (nonSystemMessages.length > 2) {
      consolidatedMessages.push({
        role: 'system',
        content: `[${nonSystemMessages.length - 2} messages omitted for context preservation]`,
      });
    }
    
    if (last && last !== first) consolidatedMessages.push(last);
    preserved.push('first_message', 'last_message');
  }
  
  const consolidatedTokens = consolidatedMessages.reduce((sum, msg) => sum + countTokens(msg.content), 0);
  
  return {
    consolidatedContent: JSON.stringify(consolidatedMessages),
    originalTokens,
    consolidatedTokens,
    reductionPercentage: computeReductionPercentage(originalTokens, consolidatedTokens),
    preservedElements: preserved,
  };
}

/**
 * Extract topics from messages for summarization
 */
function extractTopics(messages: Array<{ role: string; content: string }>): string[] {
  const topics = new Set<string>();
  
  for (const msg of messages) {
    // Look for common programming keywords
    if (msg.content.match(/\b(function|class|component|api|database|error|bug|feature)\b/i)) {
      topics.add('code implementation');
    }
    if (msg.content.match(/\b(fix|debug|solve|issue|problem)\b/i)) {
      topics.add('debugging');
    }
    if (msg.content.match(/\b(search|find|locate|query)\b/i)) {
      topics.add('searching');
    }
    if (msg.content.match(/\b(create|build|implement|add|new)\b/i)) {
      topics.add('feature development');
    }
  }
  
  return Array.from(topics).slice(0, 3);
}

/**
 * Create consolidation summary for user feedback
 */
export function createConsolidationSummary(
  model: string,
  originalTokens: number,
  consolidatedTokens: number
): string {
  const strategy = getConsolidationStrategy(model);
  const reduction = computeReductionPercentage(originalTokens, consolidatedTokens);
  
  if (strategy.consolidationLevel === 'minimal') {
    return `✅ Using ${model} with ${strategy.contextLimit / 1000}K context window. Minimal consolidation applied.`;
  }
  
  if (reduction > 50) {
    return `⚠️ Aggressive consolidation applied for ${model}: Reduced ${originalTokens} → ${consolidatedTokens} tokens (${reduction}% reduction)`;
  }
  
  if (reduction > 20) {
    return `📊 Moderate consolidation for ${model}: ${originalTokens} → ${consolidatedTokens} tokens (${reduction}% reduction)`;
  }
  
  return `✅ Content fits within ${model} limits: ${consolidatedTokens} tokens used`;
}