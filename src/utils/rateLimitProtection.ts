/**
 * Enhanced Rate Limit Protection
 * 
 * Protects against rate limits by intelligently managing context and
 * delegating to appropriate processing strategies.
 */

import { 
  countTokens, 
  getModelLimits, 
  validateAndPruneMessages,
  shouldDelegateToOrchestrator 
} from './tokenLimiter';
import { ContextualProjectFeeder } from './contextualProjectFeeder';

interface RateLimitProtectionConfig {
  emergencyTokenLimit: number;
  maxContextExpansions: number;
  progressiveReduction: boolean;
  enableQueueing: boolean;
}

interface ProtectionResult {
  shouldProceed: boolean;
  strategy: 'direct' | 'orchestrator' | 'contextual' | 'emergency' | 'queue';
  modifiedMessages?: Array<{ role: string; content: string; attachments?: any[] }>;
  reason: string;
  tokenEstimate: number;
  contextReduction?: string;
}

export class RateLimitProtection {
  private config: RateLimitProtectionConfig;
  private projectFeeder?: ContextualProjectFeeder;
  private rateLimitHistory: Map<string, number[]> = new Map();

  constructor(config: Partial<RateLimitProtectionConfig> = {}) {
    this.config = {
      emergencyTokenLimit: 10000,
      maxContextExpansions: 3,
      progressiveReduction: true,
      enableQueueing: true,
      ...config
    };
  }

  setProjectFeeder(feeder: ContextualProjectFeeder) {
    this.projectFeeder = feeder;
  }

  /**
   * Analyze request and determine protection strategy
   */
  async analyzeRequest(
    messages: Array<{ role: string; content: string; attachments?: any[] }>,
    model: string = 'gpt-5',
    userMessage?: string
  ): Promise<ProtectionResult> {
    const modelLimits = getModelLimits(model);
    const totalTokens = this.estimateTokens(messages);
    
    // Track rate limit patterns
    this.updateRateLimitHistory(model, totalTokens);
    
    // Check if we're approaching dangerous territory
    if (totalTokens > modelLimits.maxInputTokens * 2) {
      return {
        shouldProceed: false,
        strategy: 'emergency',
        reason: `Request too large (${totalTokens} tokens) - exceeds emergency limits`,
        tokenEstimate: totalTokens,
      };
    }

    // Check for patterns that suggest massive context loading
    if (this.detectMassiveContextPattern(messages)) {
      return await this.handleMassiveContext(messages, model, userMessage);
    }

    // Progressive reduction strategy
    if (totalTokens > modelLimits.maxInputTokens) {
      return await this.applyProgressiveReduction(messages, model, totalTokens);
    }

    // Check if we should delegate to orchestrator
    if (shouldDelegateToOrchestrator(messages, undefined, undefined, model)) {
      return {
        shouldProceed: true,
        strategy: 'orchestrator',
        reason: 'Large request - delegating to task orchestrator',
        tokenEstimate: totalTokens,
      };
    }

    // Request looks good to proceed
    return {
      shouldProceed: true,
      strategy: 'direct',
      reason: 'Request within safe limits',
      tokenEstimate: totalTokens,
    };
  }

  /**
   * Apply emergency token reduction
   */
  async applyEmergencyReduction(
    messages: Array<{ role: string; content: string; attachments?: any[] }>,
    model: string
  ): Promise<ProtectionResult> {
    const emergencyMessages = validateAndPruneMessages(
      messages, 
      model, 
      undefined, 
      this.config.emergencyTokenLimit
    );

    return {
      shouldProceed: true,
      strategy: 'emergency',
      modifiedMessages: emergencyMessages.messages,
      reason: `Applied emergency token reduction to ${this.config.emergencyTokenLimit} tokens`,
      tokenEstimate: this.estimateTokens(emergencyMessages.messages),
      contextReduction: 'Emergency context pruning applied'
    };
  }

  /**
   * Handle requests with massive context (like entire project dumps)
   */
  private async handleMassiveContext(
    messages: Array<{ role: string; content: string; attachments?: any[] }>,
    model: string,
    userMessage?: string
  ): Promise<ProtectionResult> {
    if (!this.projectFeeder || !userMessage) {
      // Fallback to emergency reduction
      return await this.applyEmergencyReduction(messages, model);
    }

    try {
      // Use contextual project feeder to get relevant context
      const contextResult = await this.projectFeeder.getContextualProjectInfo(
        userMessage,
        messages.slice(-3), // Last 3 messages for context
        model
      );

      // Create new messages with contextual information
      const contextualMessages = [
        ...messages.filter(m => m.role === 'system').slice(0, 1), // Keep system message
        {
          role: 'user',
          content: `${userMessage}\n\nRelevant Project Context:\n${JSON.stringify(contextResult.context, null, 2)}`
        }
      ];

      return {
        shouldProceed: true,
        strategy: 'contextual',
        modifiedMessages: contextualMessages,
        reason: `Replaced massive context with intelligent selection (${contextResult.tokensUsed} tokens)`,
        tokenEstimate: this.estimateTokens(contextualMessages),
        contextReduction: contextResult.reasoning
      };
    } catch (error) {
      console.warn('Contextual reduction failed, falling back to emergency reduction:', error);
      return await this.applyEmergencyReduction(messages, model);
    }
  }

  /**
   * Apply progressive reduction strategies
   */
  private async applyProgressiveReduction(
    messages: Array<{ role: string; content: string; attachments?: any[] }>,
    model: string,
    currentTokens: number
  ): Promise<ProtectionResult> {
    const modelLimits = getModelLimits(model);
    const targetTokens = Math.floor(modelLimits.maxInputTokens * 0.8); // 80% of limit

    // Strategy 1: Prune older messages
    const prunedResult = validateAndPruneMessages(messages, model, undefined, targetTokens);
    
    if (this.estimateTokens(prunedResult.messages) <= targetTokens) {
      return {
        shouldProceed: true,
        strategy: 'direct',
        modifiedMessages: prunedResult.messages,
        reason: `Pruned messages to fit within ${targetTokens} token limit`,
        tokenEstimate: this.estimateTokens(prunedResult.messages),
        contextReduction: 'Message history pruning applied'
      };
    }

    // Strategy 2: Delegate to orchestrator
    return {
      shouldProceed: true,
      strategy: 'orchestrator',
      reason: 'Context too large even after pruning - delegating to orchestrator',
      tokenEstimate: currentTokens,
    };
  }

  /**
   * Detect patterns that suggest massive context dumps
   */
  private detectMassiveContextPattern(messages: Array<{ role: string; content: string }>): boolean {
    for (const message of messages) {
      const content = message.content;
      
      // Check for file listing patterns
      if (this.countFileListings(content) > 100) return true;
      
      // Check for build output patterns
      if (content.includes('build/') && this.countLines(content) > 1000) return true;
      
      // Check for massive JSON dumps
      if (content.includes('"intermediates"') && content.length > 100000) return true;
      
      // Check for dependency listings
      if (content.includes('node_modules') && this.countLines(content) > 500) return true;
    }
    
    return false;
  }

  /**
   * Update rate limit history for pattern detection
   */
  private updateRateLimitHistory(model: string, tokens: number) {
    const now = Date.now();
    const history = this.rateLimitHistory.get(model) || [];
    
    // Keep only last 10 minutes of history
    const recentHistory = history.filter(timestamp => now - timestamp < 600000);
    recentHistory.push(now);
    
    this.rateLimitHistory.set(model, recentHistory);
  }

  /**
   * Estimate total tokens in messages
   */
  private estimateTokens(messages: Array<{ role: string; content: string }>): number {
    return messages.reduce((total, message) => {
      return total + countTokens(message.content) + 4; // 4 tokens for message overhead
    }, 0);
  }

  /**
   * Count file listings in content
   */
  private countFileListings(content: string): number {
    const filePatterns = [
      /\.[a-zA-Z]{1,4}$/gm, // File extensions
      /\/[^/\s]+\.[a-zA-Z]{1,4}/g, // File paths
    ];
    
    let count = 0;
    for (const pattern of filePatterns) {
      const matches = content.match(pattern);
      if (matches) count += matches.length;
    }
    
    return count;
  }

  /**
   * Count lines in content
   */
  private countLines(content: string): number {
    return content.split('\n').length;
  }

  /**
   * Get rate limit protection metrics
   */
  getMetrics(): {
    recentRequestCounts: Record<string, number>;
    averageTokenUsage: Record<string, number>;
    protectionActivations: number;
  } {
    const metrics = {
      recentRequestCounts: {} as Record<string, number>,
      averageTokenUsage: {} as Record<string, number>,
      protectionActivations: 0
    };

    // Calculate recent request counts
    for (const [model, history] of this.rateLimitHistory) {
      metrics.recentRequestCounts[model] = history.length;
    }

    return metrics;
  }
}

// Global instance
export const rateLimitProtection = new RateLimitProtection();