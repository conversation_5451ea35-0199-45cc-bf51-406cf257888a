import * as fs from 'fs/promises';
import * as path from 'path';
import { geminiContextService } from '../services/geminiContextService';
import { countTokens } from './tokenLimiter';

/**
 * Smart exploration strategy for efficiently understanding projects
 * without overwhelming token limits
 */
export class ExplorationStrategy {
  private readonly maxTokensPerRead = 5000;
  private readonly priorityFiles = [
    'README.md',
    'readme.md',
    'CLAUDE.md',
    'package.json',
    'tsconfig.json',
    '.env.example',
    'docker-compose.yml',
    'Makefile'
  ];

  /**
   * Explore a project intelligently without token overflow
   */
  async exploreProject(projectPath: string): Promise<{
    overview: string;
    structure: any;
    techStack: string[];
    keyFiles: string[];
    recommendations: string[];
  }> {
    const results = {
      overview: '',
      structure: {},
      techStack: [] as string[],
      keyFiles: [] as string[],
      recommendations: [] as string[]
    };

    try {
      // Step 1: Get project structure
      results.structure = await this.getProjectStructure(projectPath);

      // Step 2: Read documentation files
      const docs = await this.readDocumentation(projectPath);
      if (docs.readme) {
        results.overview = this.extractOverview(docs.readme);
      }

      // Step 3: Analyze configuration files
      const config = await this.analyzeConfiguration(projectPath);
      results.techStack = config.techStack;

      // Step 4: Identify key entry points
      results.keyFiles = await this.identifyKeyFiles(projectPath, results.structure);

      // Step 5: Generate smart recommendations
      results.recommendations = this.generateRecommendations(results);

      // If no README found, generate overview from structure
      if (!results.overview) {
        results.overview = this.generateOverviewFromStructure(results);
      }

      return results;
    } catch (error) {
      console.error('Error exploring project:', error);
      throw error;
    }
  }

  /**
   * Get project structure without reading file contents
   */
  private async getProjectStructure(projectPath: string, maxDepth: number = 3): Promise<any> {
    const self = this; // Capture this context

    async function explore(dir: string, depth: number): Promise<any> {
      if (depth > maxDepth) return null;

      const items = await fs.readdir(dir, { withFileTypes: true });
      const result: any = {};

      for (const item of items) {
        // Skip common ignore patterns
        if (self.shouldIgnore(item.name)) continue;

        const fullPath = path.join(dir, item.name);

        if (item.isDirectory()) {
          result[item.name] = await explore(fullPath, depth + 1);
        } else {
          const stats = await fs.stat(fullPath);
          result[item.name] = {
            size: stats.size,
            ext: path.extname(item.name)
          };
        }
      }

      return result;
    }

    return await explore(projectPath, 0);
  }

  /**
   * Read and consolidate documentation files
   */
  private async readDocumentation(projectPath: string): Promise<{
    readme?: string;
    claude?: string;
    contributing?: string;
  }> {
    const docs: any = {};

    for (const docFile of ['README.md', 'readme.md', 'CLAUDE.md', 'CONTRIBUTING.md']) {
      const filePath = path.join(projectPath, docFile);

      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const tokens = countTokens(content);

        if (tokens > this.maxTokensPerRead && geminiContextService.isAvailable()) {
          // Use Gemini to consolidate large documentation
          const consolidated = await geminiContextService.summarizeFile(filePath, {
            focusAreas: ['project purpose', 'features', 'architecture', 'setup'],
            maxOutputTokens: 2000
          });
          docs[docFile.toLowerCase().replace('.md', '')] = consolidated.summary;
        } else if (tokens <= this.maxTokensPerRead) {
          docs[docFile.toLowerCase().replace('.md', '')] = content;
        }
      } catch {
        // File doesn't exist, skip
      }
    }

    return docs;
  }

  /**
   * Analyze configuration files to understand tech stack
   */
  private async analyzeConfiguration(projectPath: string): Promise<{
    techStack: string[];
    dependencies: string[];
    scripts: Record<string, string>;
  }> {
    const result = {
      techStack: [] as string[],
      dependencies: [] as string[],
      scripts: {} as Record<string, string>
    };

    // Check package.json
    try {
      const packagePath = path.join(projectPath, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packagePath, 'utf-8'));

      // Extract tech stack from dependencies
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      // Identify major technologies
      if (allDeps.react) result.techStack.push('React');
      if (allDeps.vue) result.techStack.push('Vue');
      if (allDeps.angular) result.techStack.push('Angular');
      if (allDeps.express) result.techStack.push('Express');
      if (allDeps.next) result.techStack.push('Next.js');
      if (allDeps.typescript) result.techStack.push('TypeScript');
      if (allDeps['@openai/agents']) result.techStack.push('OpenAI Agents SDK (Legacy)');
      if (allDeps.vite) result.techStack.push('Vite');
      if (allDeps.webpack) result.techStack.push('Webpack');

      result.dependencies = Object.keys(allDeps).slice(0, 10);
      result.scripts = packageJson.scripts || {};
    } catch {
      // No package.json or error reading it
    }

    // Check for other config files
    try {
      await fs.access(path.join(projectPath, 'tsconfig.json'));
      if (!result.techStack.includes('TypeScript')) {
        result.techStack.push('TypeScript');
      }
    } catch {}

    try {
      await fs.access(path.join(projectPath, 'docker-compose.yml'));
      result.techStack.push('Docker');
    } catch {}

    try {
      await fs.access(path.join(projectPath, '.env.example'));
      result.techStack.push('Environment Configuration');
    } catch {}

    return result;
  }

  /**
   * Identify key files based on project structure
   */
  private async identifyKeyFiles(projectPath: string, structure: any): Promise<string[]> {
    const keyFiles: string[] = [];

    // Look for main entry points
    const entryPoints = [
      'src/index.ts',
      'src/index.js',
      'src/main.ts',
      'src/main.js',
      'src/app.ts',
      'src/app.js',
      'index.ts',
      'index.js'
    ];

    for (const entry of entryPoints) {
      try {
        await fs.access(path.join(projectPath, entry));
        keyFiles.push(entry);
      } catch {}
    }

    // Look for API/server files
    if (structure.src?.api) {
      keyFiles.push('src/api/');
    }
    if (structure.src?.server) {
      keyFiles.push('src/server/');
    }

    // Look for agent files (specific to this project)
    if (structure.src?.agents) {
      keyFiles.push('src/agents/DanteOrchestrator.ts');
    }

    // Look for configuration
    if (structure['.env.example']) {
      keyFiles.push('.env.example');
    }

    return keyFiles.slice(0, 10); // Limit to top 10 key files
  }

  /**
   * Extract overview from README content
   */
  private extractOverview(readme: string): string {
    // Try to extract the first few paragraphs
    const lines = readme.split('\n');
    const overview: string[] = [];
    let capturing = false;

    for (const line of lines) {
      // Skip headers
      if (line.startsWith('#')) {
        if (line.toLowerCase().includes('overview') ||
            line.toLowerCase().includes('about') ||
            line.toLowerCase().includes('introduction')) {
          capturing = true;
          continue;
        }
        if (capturing && !line.toLowerCase().includes('overview')) {
          break; // Stop at next section
        }
      }

      // Capture content
      if (capturing && line.trim()) {
        overview.push(line);
        if (overview.length >= 5) break; // Limit to 5 lines
      }

      // Also capture first paragraph even without header
      if (!line.startsWith('#') && line.trim() && overview.length === 0) {
        overview.push(line);
      }
    }

    return overview.join(' ').substring(0, 500);
  }

  /**
   * Generate overview from project structure if no README
   */
  private generateOverviewFromStructure(results: any): string {
    const parts: string[] = [];

    if (results.techStack.length > 0) {
      parts.push(`This is a ${results.techStack.join(', ')} project.`);
    }

    if (results.structure.src) {
      const srcDirs = Object.keys(results.structure.src).filter(d => typeof results.structure.src[d] === 'object');
      if (srcDirs.length > 0) {
        parts.push(`The source code is organized into: ${srcDirs.join(', ')}.`);
      }
    }

    if (results.keyFiles.length > 0) {
      parts.push(`Key entry points include: ${results.keyFiles.slice(0, 3).join(', ')}.`);
    }

    return parts.join(' ') || 'Unable to generate overview from structure.';
  }

  /**
   * Generate smart recommendations based on exploration
   */
  private generateRecommendations(results: any): string[] {
    const recommendations: string[] = [];

    if (!results.overview) {
      recommendations.push('Consider adding a README.md file to document the project');
    }

    if (results.techStack.includes('TypeScript') && !results.structure['tsconfig.json']) {
      recommendations.push('TypeScript is used but tsconfig.json is missing');
    }

    if (results.structure.src?.tests || results.structure.tests || results.structure.test) {
      recommendations.push('Test suite found - run tests to verify functionality');
    }

    if (results.structure['.env.example']) {
      recommendations.push('Environment configuration needed - copy .env.example to .env');
    }

    return recommendations;
  }

  /**
   * Check if a file/directory should be ignored
   */
  private shouldIgnore(name: string): boolean {
    const ignorePatterns = [
      'node_modules',
      '.git',
      'dist',
      'build',
      'coverage',
      '.next',
      '.nuxt',
      '.cache',
      '.vscode',
      '.idea',
      '*.log',
      '.DS_Store'
    ];

    return ignorePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(name);
      }
      return name === pattern;
    });
  }
}

// Export singleton instance
export const explorationStrategy = new ExplorationStrategy();
