import { encode } from 'gpt-tokenizer';

interface TokenLimitConfig {
  maxInputTokens: number;
  maxOutputTokens: number;
  model: string;
}

// Rate limit management
interface RateLimitState {
  requests: { timestamp: number; tokens: number }[];
  isBlocked: boolean;
  blockUntil?: number;
}

const rateLimitStates = new Map<string, RateLimitState>();

// Export for API access
// Return the internal Map so callers can use Map APIs (e.g., .get). This aligns with
// existing consumers (e.g., server rate-limit endpoint). If mutation safety becomes
// a concern, consider returning a shallow-cloned Map instead: new Map(rateLimitStates).
export function getRateLimitStates(): Map<string, RateLimitState> {
  return rateLimitStates;
}

// Token limits per model with rate limiting awareness
const modelLimits: Record<string, TokenLimitConfig> = {
  'gpt-5': {
    maxInputTokens: 480000,  // Very conservative to avoid rate limits (1M TPM / 2 requests)
    maxOutputTokens: 20000,
    model: 'gpt-5',
  },
  'gpt-5-mini': {
    maxInputTokens: 480000,  // Conservative limit to avoid rate limits
    maxOutputTokens: 10000,
    model: 'gpt-5-mini',
  },
  'gpt-5-nano': {
    maxInputTokens: 480000,  // Conservative limit to avoid rate limits
    maxOutputTokens: 4096,
    model: 'gpt-5-nano',
  },
  'gpt-4.1': {
    maxInputTokens: 480000,  // Conservative limit for 128K model
    maxOutputTokens: 4096,
    model: 'gpt-4.1',
  },
  'gpt-4.1-mini': {
    maxInputTokens: 50000,  // Conservative limit for 128K model
    maxOutputTokens: 4096,
    model: 'gpt-4.1-mini',
  },
  'gpt-4.1-nano': {
    maxInputTokens: 50000,  // Conservative limit for 128K model
    maxOutputTokens: 4096,
    model: 'gpt-4.1-nano',
  },
  // Long context variants with much higher limits
  'gpt-4.1-long-context': {
    maxInputTokens: 150000,  // Much higher for long context (200K TPM)
    maxOutputTokens: 8192,
    model: 'gpt-4.1',
  },
  'gpt-4.1-mini-long-context': {
    maxInputTokens: 300000,  // Very high for mini long context (400K TPM)
    maxOutputTokens: 8192,
    model: 'gpt-4.1-mini',
  },
  'gpt-4.1-nano-long-context': {
    maxInputTokens: 300000,  // Very high for nano long context (400K TPM)
    maxOutputTokens: 8192,
    model: 'gpt-4.1-nano',
  },
  'gpt-4o': {
    maxInputTokens: 15000,  // Very conservative to avoid rate limits (30k TPM / 2 requests)
    maxOutputTokens: 4096,
    model: 'gpt-4o',
  },
  'gpt-4o-mini': {
    maxInputTokens: 50000,  // Conservative limit for 128K model
    maxOutputTokens: 4096,
    model: 'gpt-4o-mini',
  },
  // Gemini models with massive context windows
  'gemini-2.5-pro': {
    maxInputTokens: 900000,  // 900K tokens (leave buffer from 1M limit)
    maxOutputTokens: 65536,  // Full 65K output limit
    model: 'gemini-2.5-pro',
  },
  'gemini-2.5-flash': {
    maxInputTokens: 900000,  // 900K tokens (leave buffer from 1M limit)
    maxOutputTokens: 65536,  // Full 65K output limit
    model: 'gemini-2.5-flash',
  },
  'gemini-2.5-flash-lite': {
    maxInputTokens: 900000,  // 900K tokens (leave buffer from 1M limit)
    maxOutputTokens: 65536,  // Full 65K output limit
    model: 'gemini-2.5-flash-lite',
  },
  'gpt-4': {
    maxInputTokens: 6000,
    maxOutputTokens: 4096,
    model: 'gpt-4',
  },
  'gpt-3.5-turbo': {
    maxInputTokens: 14000,
    maxOutputTokens: 4096,
    model: 'gpt-3.5-turbo',
  },
};

export function getModelLimits(model: string): TokenLimitConfig {
  return modelLimits[model] || modelLimits['gpt-4o'];
}

// Function to select the best model for large file operations
export function selectModelForFileOperation(estimatedTokens: number, defaultModel: string): string {
  // If estimated tokens are very high, use long context models
  if (estimatedTokens > 100000) {
    return 'gpt-4.1-nano-long-context'; // Highest capacity (400K TPM)
  } else if (estimatedTokens > 50000) {
    return 'gpt-4.1-mini-long-context'; // High capacity (400K TPM)
  } else if (estimatedTokens > 25000) {
    return 'gpt-4.1-long-context'; // Medium-high capacity (200K TPM)
  } else if (estimatedTokens > 15000) {
    return 'gpt-5-mini'; // Good capacity (200K TPM)
  }

  // For smaller operations, use the default model
  return defaultModel;
}

// Function to estimate tokens from file operation parameters
export function estimateFileOperationTokens(params: {
  fileCount?: number;
  recursive?: boolean;
  searchPattern?: string;
  directoryDepth?: number;
}): number {
  let estimate = 1000; // Base overhead

  if (params.recursive) {
    estimate += 10000; // Recursive operations are much larger
  }

  if (params.fileCount) {
    estimate += params.fileCount * 50; // ~50 tokens per file path
  }

  if (params.directoryDepth && params.directoryDepth > 3) {
    estimate += (params.directoryDepth - 3) * 5000; // Deep directories
  }

  return estimate;
}

export function countTokens(text: string): number {
  // Guard against undefined/null/non-string inputs
  if (typeof text !== 'string') {
    text = String(text ?? '');
  }
  try {
    return encode(text).length;
  } catch {
    // Fallback to character-based estimation (roughly 4 chars per token)
    return Math.ceil(text.length / 4);
  }
}

export function truncateToTokenLimit(text: string, maxTokens: number): string {
  const tokens = countTokens(text);

  if (tokens <= maxTokens) {
    return text;
  }

  // Binary search to find the right length
  let low = 0;
  let high = text.length;
  let result = '';

  while (low <= high) {
    const mid = Math.floor((low + high) / 2);
    const substr = text.substring(0, mid);
    const tokenCount = countTokens(substr);

    if (tokenCount <= maxTokens) {
      result = substr;
      low = mid + 1;
    } else {
      high = mid - 1;
    }
  }

  return result + '\n\n[Content truncated due to token limit]';
}

export function estimateMessageTokens(messages: Array<{ role: string; content: string; attachments?: any[] }>): number {
  let totalTokens = 0;

  for (const message of messages) {
    // Add tokens for message structure (role, etc.)
    totalTokens += 4;

    // Add content tokens
    if (typeof message.content === 'string') {
      totalTokens += countTokens(message.content);
    }
  }

  // Add tokens for response formatting
  totalTokens += 10;

  return totalTokens;
}

export function validateAndTruncateMessages(
  messages: Array<{ role: string; content: string; attachments?: any[] }>,
  model: string,
  maxTokensOverride?: number
): Array<{ role: string; content: string; attachments?: any[] }> {
  const limits = getModelLimits(model);
  // Use override if provided, otherwise use model limits
  const maxInputTokens = maxTokensOverride || limits.maxInputTokens;
  const totalTokens = estimateMessageTokens(messages);

  if (totalTokens <= maxInputTokens) {
    return messages;
  }

  console.warn(`Token limit exceeded: ${totalTokens} > ${maxInputTokens}. Truncating messages.`);

  // Keep the most recent messages that fit within the limit
  const truncatedMessages: Array<{ role: string; content: string; attachments?: any[] }> = [];
  let currentTokens = 0;

  // Always keep the first system message if it exists
  const firstMessage = messages[0];
  if (firstMessage?.role === 'system') {
    truncatedMessages.push(firstMessage);
    currentTokens += countTokens(firstMessage.content) + 4;
  }

  // Add messages from the end, working backwards
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i];
    if (message.role === 'system' && i === 0) continue; // Already added

    const messageTokens = countTokens(message.content) + 4;

    if (currentTokens + messageTokens <= maxInputTokens * 0.9) { // Leave 10% buffer
      // Preserve all message properties including attachments
      truncatedMessages.unshift({ ...message });
      currentTokens += messageTokens;
    } else {
      // If we can't fit the whole message, truncate it and stop
      const remainingTokens = Math.floor(maxInputTokens * 0.9) - currentTokens - 4;
      if (remainingTokens > 100) {
        const truncatedContent = truncateToTokenLimit(message.content, remainingTokens);
        truncatedMessages.unshift({ ...message, content: truncatedContent });
      }
      break;
    }
  }

  return truncatedMessages;
}

export function chunkLargeContent(content: string, maxTokensPerChunk: number = 5000): string[] {
  const totalTokens = countTokens(content);

  if (totalTokens <= maxTokensPerChunk) {
    return [content];
  }

  const chunks: string[] = [];
  const lines = content.split('\n');
  let currentChunk = '';
  let currentTokens = 0;

  for (const line of lines) {
    const lineTokens = countTokens(line);

    if (currentTokens + lineTokens > maxTokensPerChunk && currentChunk) {
      chunks.push(currentChunk.trim());
      currentChunk = '';
      currentTokens = 0;
    }

    currentChunk += line + '\n';
    currentTokens += lineTokens + 1;
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// Rate limit tracking functions
export function updateRateLimit(model: string, tokens: number): void {
  const now = Date.now();
  const state = rateLimitStates.get(model) || {
    requests: [],
    isBlocked: false,
  };

  // Remove requests older than 1 minute
  state.requests = state.requests.filter(req => now - req.timestamp < 60000);

  // Add current request
  state.requests.push({ timestamp: now, tokens });

  // Check if we're approaching rate limits (30k TPM)
  const totalTokensInLastMinute = state.requests.reduce((sum, req) => sum + req.tokens, 0);

  if (totalTokensInLastMinute > 25000) {
    state.isBlocked = true;
    state.blockUntil = now + 60000; // Block for 1 minute
    console.warn(`Rate limit protection activated for ${model}. Total tokens in last minute: ${totalTokensInLastMinute}`);
  }

  rateLimitStates.set(model, state);
}

export function isRateLimited(model: string): boolean {
  const state = rateLimitStates.get(model);
  if (!state) return false;

  const now = Date.now();

  // Check if block period has expired
  if (state.isBlocked && state.blockUntil && now > state.blockUntil) {
    state.isBlocked = false;
    state.blockUntil = undefined;
    rateLimitStates.set(model, state);
    return false;
  }

  return state.isBlocked;
}

export function getRateLimitDelay(model: string): number {
  const state = rateLimitStates.get(model);
  if (!state?.isBlocked || !state.blockUntil) return 0;

  return Math.max(0, state.blockUntil - Date.now());
}

// Memory-aware token management with model-specific limits
export function shouldDelegateToOrchestrator(
  messages: Array<{ role: string; content: string }>,
  memoryContext?: string,
  fileCount?: number,
  model?: string
): boolean {
  let totalTokens = estimateMessageTokens(messages);

  if (memoryContext) {
    totalTokens += countTokens(memoryContext);
  }

  // Get model-specific delegation threshold
  const isGeminiModel = model?.startsWith('gemini-') || false;

  if (isGeminiModel) {
    // For Gemini models with 1M+ context, use much higher thresholds
    return totalTokens > 500000 || (fileCount !== undefined && fileCount > 50);
  } else {
    // For OpenAI models, use conservative thresholds
    return totalTokens > 20000 || (fileCount !== undefined && fileCount > 5);
  }
}

// Enhanced message validation with memory pruning
export function validateAndPruneMessages(
  messages: Array<{ role: string; content: string; attachments?: any[] }>,
  model: string,
  memoryContext?: string,
  maxTokensOverride?: number
): { messages: typeof messages; prunedMemory?: string; delegateToOrchestrator: boolean } {
  const limits = getModelLimits(model);
  const maxInputTokens = maxTokensOverride || limits.maxInputTokens;

  let totalTokens = estimateMessageTokens(messages);
  let prunedMemory: string | undefined;

  // Add memory context tokens
  if (memoryContext) {
    const memoryTokens = countTokens(memoryContext);
    totalTokens += memoryTokens;

    // If memory is too large, prune it
    if (memoryTokens > maxInputTokens * 0.3) {
      prunedMemory = truncateToTokenLimit(memoryContext, Math.floor(maxInputTokens * 0.2));
      totalTokens = estimateMessageTokens(messages) + countTokens(prunedMemory);
    }
  }

  // Check if we should delegate to orchestrator
  const shouldDelegate = totalTokens > maxInputTokens * 1.5;

  if (shouldDelegate) {
    return {
      messages,
      prunedMemory,
      delegateToOrchestrator: true
    };
  }

  // If still too large, truncate messages
  if (totalTokens > maxInputTokens) {
    const truncatedMessages = validateAndTruncateMessages(messages, model, maxInputTokens);
    return {
      messages: truncatedMessages,
      prunedMemory,
      delegateToOrchestrator: false
    };
  }

  return {
    messages,
    prunedMemory,
    delegateToOrchestrator: false
  };
}
