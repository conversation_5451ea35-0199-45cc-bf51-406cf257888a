import { countTokens, getModelLimits } from './tokenLimiter';

export interface Task {
  id: string;
  type: 'analysis' | 'generation' | 'review' | 'search' | 'execution';
  description: string;
  files?: string[];
  content?: string;
  estimatedTokens: number;
  priority: number;
  dependencies?: string[];
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  output: string;
  tokensUsed: number;
  error?: string;
}

export interface WorkerAgent {
  id: string;
  type: string;
  currentTokens: number;
  maxTokens: number;
  busy: boolean;
  tasksCompleted: number;
}

export class TaskDelegationManager {
  private workers: Map<string, WorkerAgent> = new Map();
  private taskQueue: Task[] = [];
  private completedTasks: Map<string, TaskResult> = new Map();
  private model: string;
  
  constructor(model: string = 'gpt-5') {
    this.model = model;
    this.initializeWorkers();
  }
  
  private initializeWorkers() {
    const limits = getModelLimits(this.model);
    
    // Create specialized worker agents
    const workerTypes = [
      { id: 'file-analyst-1', type: 'analysis' },
      { id: 'file-analyst-2', type: 'analysis' },
      { id: 'file-analyst-3', type: 'analysis' },
      { id: 'code-generator-1', type: 'generation' },
      { id: 'code-reviewer-1', type: 'review' },
      { id: 'searcher-1', type: 'search' },
    ];
    
    workerTypes.forEach(worker => {
      this.workers.set(worker.id, {
        id: worker.id,
        type: worker.type,
        currentTokens: 0,
        maxTokens: limits.maxInputTokens * 0.8, // Use 80% of limit for safety
        busy: false,
        tasksCompleted: 0,
      });
    });
  }
  
  /**
   * Analyzes a large request and breaks it into manageable tasks
   */
  async decomposeLargeRequest(
    request: string,
    files?: string[],
    content?: string
  ): Promise<Task[]> {
    const tasks: Task[] = [];
    const requestTokens = countTokens(request);
    
    // Determine request type
    const isSecurityReview = /security|vulnerability|audit|scan/i.test(request);
    const isCodeGeneration = /create|generate|build|implement/i.test(request);
    const isAnalysis = /analyze|review|check|inspect/i.test(request);
    const isSearch = /search|find|locate|grep/i.test(request);
    
    if (files && files.length > 0) {
      // Break down file analysis into chunks
      const fileChunks = this.chunkFiles(files, isSecurityReview ? 3 : 5);
      
      fileChunks.forEach((chunk, index) => {
        tasks.push({
          id: `file-analysis-${index}`,
          type: 'analysis',
          description: `Analyze files: ${chunk.join(', ')}`,
          files: chunk,
          estimatedTokens: chunk.length * 2000, // Estimate 2000 tokens per file
          priority: isSecurityReview ? 1 : 2,
        });
      });
    }
    
    if (content && countTokens(content) > 10000) {
      // Break down large content into chunks
      const contentChunks = this.chunkContent(content, 8000);
      
      contentChunks.forEach((chunk, index) => {
        tasks.push({
          id: `content-analysis-${index}`,
          type: 'analysis',
          description: `Analyze content chunk ${index + 1}`,
          content: chunk,
          estimatedTokens: countTokens(chunk),
          priority: 2,
        });
      });
    }
    
    // Add synthesis task to combine results
    if (tasks.length > 1) {
      tasks.push({
        id: 'synthesis',
        type: 'analysis',
        description: 'Synthesize and combine all analysis results',
        estimatedTokens: 2000,
        priority: 3,
        dependencies: tasks.map(t => t.id),
      });
    }
    
    return tasks.sort((a, b) => a.priority - b.priority);
  }
  
  private chunkFiles(files: string[], chunkSize: number): string[][] {
    const chunks: string[][] = [];
    
    for (let i = 0; i < files.length; i += chunkSize) {
      chunks.push(files.slice(i, i + chunkSize));
    }
    
    return chunks;
  }
  
  private chunkContent(content: string, maxTokensPerChunk: number): string[] {
    const chunks: string[] = [];
    const lines = content.split('\n');
    let currentChunk = '';
    let currentTokens = 0;
    
    for (const line of lines) {
      const lineTokens = countTokens(line);
      
      if (currentTokens + lineTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
        currentTokens = 0;
      }
      
      currentChunk += line + '\n';
      currentTokens += lineTokens;
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }
  
  /**
   * Assigns tasks to available workers
   */
  assignTaskToWorker(task: Task): WorkerAgent | null {
    // Find available worker of the right type
    const availableWorkers = Array.from(this.workers.values()).filter(
      worker => !worker.busy && 
                worker.type === task.type &&
                worker.currentTokens + task.estimatedTokens <= worker.maxTokens
    );
    
    if (availableWorkers.length === 0) {
      // Try any available worker if no specialized one is available
      const anyWorker = Array.from(this.workers.values()).find(
        worker => !worker.busy && 
                  worker.currentTokens + task.estimatedTokens <= worker.maxTokens
      );
      return anyWorker || null;
    }
    
    // Choose worker with lowest token usage
    return availableWorkers.reduce((best, worker) => 
      worker.currentTokens < best.currentTokens ? worker : best
    );
  }
  
  /**
   * Executes tasks with worker agents
   */
  async executeTasks(tasks: Task[]): Promise<Map<string, TaskResult>> {
    const results = new Map<string, TaskResult>();
    const pending = [...tasks];
    const inProgress = new Map<string, WorkerAgent>();
    
    while (pending.length > 0 || inProgress.size > 0) {
      // Assign tasks to available workers
      for (let i = pending.length - 1; i >= 0; i--) {
        const task = pending[i];
        
        // Check if dependencies are complete
        if (task.dependencies) {
          const depsComplete = task.dependencies.every(dep => results.has(dep));
          if (!depsComplete) continue;
        }
        
        const worker = this.assignTaskToWorker(task);
        if (worker) {
          worker.busy = true;
          worker.currentTokens += task.estimatedTokens;
          inProgress.set(task.id, worker);
          pending.splice(i, 1);
          
          // Simulate task execution (in real implementation, this would call the agent)
          this.executeTask(task, worker).then(result => {
            results.set(task.id, result);
            worker.busy = false;
            worker.currentTokens -= task.estimatedTokens;
            worker.tasksCompleted++;
            inProgress.delete(task.id);
          });
        }
      }
      
      // Wait a bit before checking again
      if (inProgress.size > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }
  
  private async executeTask(task: Task, worker: WorkerAgent): Promise<TaskResult> {
    // This is where we would actually call the appropriate agent
    // For now, return a simulated result
    return {
      taskId: task.id,
      success: true,
      output: `Worker ${worker.id} completed ${task.type} task: ${task.description}`,
      tokensUsed: task.estimatedTokens,
    };
  }
  
  /**
   * Combines results from multiple workers into a coherent response
   */
  synthesizeResults(results: Map<string, TaskResult>): string {
    const sortedResults = Array.from(results.values()).sort((a, b) => {
      // Sort by task ID to maintain order
      return a.taskId.localeCompare(b.taskId);
    });
    
    let synthesis = '## Analysis Complete\n\n';
    
    // Group results by type
    const analysisResults = sortedResults.filter(r => r.taskId.includes('analysis'));
    const otherResults = sortedResults.filter(r => !r.taskId.includes('analysis'));
    
    if (analysisResults.length > 0) {
      synthesis += '### File Analysis Results:\n\n';
      analysisResults.forEach(result => {
        synthesis += `${result.output}\n\n`;
      });
    }
    
    if (otherResults.length > 0) {
      synthesis += '### Additional Results:\n\n';
      otherResults.forEach(result => {
        synthesis += `${result.output}\n\n`;
      });
    }
    
    // Add token usage summary
    const totalTokens = sortedResults.reduce((sum, r) => sum + r.tokensUsed, 0);
    synthesis += `\n---\n*Total tokens used: ${totalTokens}*\n`;
    synthesis += `*Tasks completed: ${sortedResults.length}*\n`;
    
    return synthesis;
  }
  
  /**
   * Gets worker status for monitoring
   */
  getWorkerStatus(): Array<{id: string, busy: boolean, usage: string}> {
    return Array.from(this.workers.values()).map(worker => ({
      id: worker.id,
      busy: worker.busy,
      usage: `${worker.currentTokens}/${worker.maxTokens} tokens (${Math.round(worker.currentTokens / worker.maxTokens * 100)}%)`,
    }));
  }
}