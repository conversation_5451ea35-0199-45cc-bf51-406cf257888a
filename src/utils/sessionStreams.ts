import { EnhancedStreamHandler } from './enhancedStreamHandler';

class SessionStreamRegistry {
  private map = new Map<string, EnhancedStreamHandler>();

  register(sessionId: string, handler: EnhancedStreamHandler) {
    this.map.set(sessionId, handler);
  }

  get(sessionId: string): EnhancedStreamHandler | undefined {
    return this.map.get(sessionId);
  }

  unregister(sessionId: string) {
    this.map.delete(sessionId);
  }
}

export const sessionStreams = new SessionStreamRegistry();
