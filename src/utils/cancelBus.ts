import { EventEmitter } from 'events';

type CancelKey = string; // sessionId[:agent]

class CancelBus extends EventEmitter {
  private cancelled = new Set<CancelKey>();

  private makeKey(sessionId: string, agent?: string) {
    return agent ? `${sessionId}:${agent}` : sessionId;
  }

  requestCancel(sessionId: string, agent?: string) {
    const key = this.makeKey(sessionId, agent);
    this.cancelled.add(key);
    this.emit('cancel', { sessionId, agent, key, timestamp: Date.now() });
  }

  clear(sessionId: string) {
    // Remove both session-level and any agent-scoped keys
    for (const key of Array.from(this.cancelled)) {
      if (key === sessionId || key.startsWith(`${sessionId}:`)) {
        this.cancelled.delete(key);
      }
    }
  }

  isCancelled(sessionId: string, agent?: string) {
    return this.cancelled.has(this.makeKey(sessionId, agent)) || this.cancelled.has(sessionId);
  }
}

export const cancelBus = new CancelBus();

