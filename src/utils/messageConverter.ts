/**
 * Message Converter - Vercel AI SDK Compatible
 */

import { AgentInputItem } from '../index';

/**
 * Convert simple chat messages to Vercel AI SDK format
 */
export function convertToAgentInputItems(
  messages: Array<{ role: string; content: string }>
): AgentInputItem[] {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

/**
 * Convert messages from UI format to Vercel AI SDK format
 */
export function convertUIMessagesToAgentFormat(
  messages: Array<{ 
    role: string; 
    content: string; 
    attachments?: { filename: string; dataUrl?: string; url?: string }[] 
  }>
): any[] {
  return messages.map(msg => {
    const baseMessage = {
      role: msg.role,
      content: msg.content
    };

    // Handle attachments if present
    if (msg.attachments && msg.attachments.length > 0) {
      return {
        ...baseMessage,
        attachments: msg.attachments
      };
    }

    return baseMessage;
  });
}

/**
 * Legacy compatibility function
 */
export function convertMessagesToAgentFormat(
  messages: Array<{ role: string; content: string }>
): any[] {
  return convertUIMessagesToAgentFormat(messages);
}