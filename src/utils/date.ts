// Centralized date utilities for consistent, deterministic prompts
// - Uses a single resolved date across the process lifetime
// - Can be overridden via env var `DANTE_TODAY_DATE` for testing/reproducibility

/**
 * Returns an ISO-8601 date string (YYYY-MM-DD) for a given Date.
 */
export function toISODate(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Resolve the "today" date used in prompts.
 * If `DANTE_TODAY_DATE` is set, that value is used.
 * Otherwise, it's computed once at module load for stability within a run.
 */
export const TODAY_DATE: string = (() => {
  const override = process.env.DANTE_TODAY_DATE;
  if (override && /^\d{4}-\d{2}-\d{2}$/.test(override)) return override;
  return toISODate(new Date());
})();


/**
 * Convert a Date to a UTC SQLite-compatible timestamp string "YYYY-MM-DD HH:mm:ss".
 */
export function toSQLiteTimestamp(date: Date): string {
  const pad = (n: number) => String(n).padStart(2, '0');
  const y = date.getUTCFullYear();
  const m = pad(date.getUTCMonth() + 1);
  const d = pad(date.getUTCDate());
  const hh = pad(date.getUTCHours());
  const mm = pad(date.getUTCMinutes());
  const ss = pad(date.getUTCSeconds());
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
}
