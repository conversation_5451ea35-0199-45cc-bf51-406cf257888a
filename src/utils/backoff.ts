/**
 * Exponential backoff with jitter utilities and Retry-After parsing
 * Pure functions; no side effects.
 */

export interface BackoffConfig {
  baseDelayMs: number;   // e.g., 500
  maxDelayMs: number;    // e.g., 8000
  jitter: number;        // 0..1 e.g., 0.2 (20%)
}

/**
 * Compute exponential backoff with optional jitter.
 * attempt: 1-based attempt number (1,2,3..)
 * Returns the delay in milliseconds (integer >= 0).
 */
export function computeBackoffDelay(attempt: number, cfg: BackoffConfig): number {
  const a = Math.max(1, Math.floor(attempt));
  const base = Math.max(0, cfg.baseDelayMs | 0);
  const cap = Math.max(base, cfg.maxDelayMs | 0);
  const j = Math.max(0, Math.min(1, Number(cfg.jitter) || 0));

  // Exponential (2^(attempt-1)) * base
  const raw = base * Math.pow(2, a - 1);
  // Cap
  const capped = Math.min(raw, cap);

  if (j === 0) return Math.round(capped);

  // Full jitter (randomized within ±j ratio of capped)
  const delta = capped * j;
  const min = Math.max(0, capped - delta);
  const max = capped + delta;
  const jittered = min + Math.random() * (max - min);
  return Math.round(jittered);
}

/**
 * Parse Retry-After header, supporting:
 *  - Integer seconds (e.g., "2")
 *  - HTTP date (e.g., "Wed, 21 Oct 2015 07:28:00 GMT")
 * Returns milliseconds or null when not parsable/absent.
 */
export function parseRetryAfter(retryAfterValue: string | number | Date | null | undefined): number | null {
  if (retryAfterValue == null) return null;

  if (typeof retryAfterValue === 'number' && isFinite(retryAfterValue)) {
    // Seconds - per HTTP spec
    return Math.max(0, Math.round(retryAfterValue)) * 1000;
  }

  if (retryAfterValue instanceof Date) {
    const diff = retryAfterValue.getTime() - Date.now();
    return diff > 0 ? diff : 0;
  }

  const str = String(retryAfterValue).trim();
  if (!str) return null;

  // Try integer seconds
  const asInt = parseInt(str, 10);
  if (!isNaN(asInt) && String(asInt) === str.replace(/\s+/g, '')) {
    return Math.max(0, asInt) * 1000;
  }

  // Try HTTP date
  const ts = Date.parse(str);
  if (!isNaN(ts)) {
    const diff = ts - Date.now();
    return diff > 0 ? diff : 0;
  }

  return null;
}

/**
 * Extract Retry-After from a headers-like object (case-insensitive).
 * Supports Node Fetch Headers, plain objects, and arrays of values.
 */
export function getRetryAfterFromHeaders(headers: any): number | null {
  try {
    if (!headers) return null;

    // Headers instance
    if (typeof headers?.get === 'function') {
      const v = headers.get('Retry-After') || headers.get('retry-after');
      return parseRetryAfter(v ?? undefined);
    }

    // Node-style raw headers object
    const keys = Object.keys(headers);
    const key = keys.find(k => k.toLowerCase() === 'retry-after');
    if (!key) return null;
    const value = (headers as any)[key];

    if (Array.isArray(value)) {
      if (value.length === 0) return null;
      return parseRetryAfter(value[value.length - 1]);
    }
    return parseRetryAfter(value);
  } catch {
    return null;
  }
}
