import { generateText } from "ai";
import { ollama } from "ai-sdk-ollama";
import { answer as simpleAnswer } from './SimpleAnswerAgent';
import { createPlan as createPlanningPlan, executePlan as executePlanning, buildAgentInput } from './PlanningAgent';
import { config } from '../utils/config';

 /**
 * TaskOrchestrator - lightweight entry point per architecture.md
 * - Exclusive entry point for incoming requests
 * - Uses offline/cheap model ONLY for complexity routing
 * - Delegates to SimpleAnswerAgent or PlanningAgent
 */
export class TaskOrchestrator {
  private async classifyComplexity(query: string): Promise<'simple' | 'complex'> {
    // Exclusively use Ollama for complexity classification (low latency/cost).
    // If Ollama is not available, fall back to heuristic rules only.
    const system = [
      'Classify the user request as simple or complex.',
      'Rules:',
      '- simple: direct Q&A, one-shot response, no planning, no tools, no multi-step dependencies.',
      '- complex: needs multi-step planning, tool use, code edits, web research, or orchestration.',
      'Return exactly: simple or complex.',
    ].join('\n');

    const ollamaAvailable = !!config.ollama?.baseURL;
    const offlineModel = (String((config as any)?.orchestrator?.offlineModel || '').trim() || 'llama3.2:3b-instruct');

    if (ollamaAvailable) {
      try {
        const { text } = await generateText({
          model: ollama(offlineModel),
          system,
          prompt: query,
        });
        const cls = String(text || '').trim().toLowerCase();
        if (cls.includes('complex')) return 'complex';
        if (cls.includes('simple')) return 'simple';
      } catch (e) {
        // Swallow and fall back to heuristic classification
      }
    }

    // Heuristic fallback if Ollama is not available or failed
    const s = query.toLowerCase();
    const complexHints = [
      'plan', 'steps', 'multi-step', 'decompose', 'research', 'investigate',
      'refactor', 'implement', 'patch', 'apply diff', 'execute', 'run command',
      'debug', 'fix', 'analyze repo', 'which file', 'architect', 'design',
      'integrate', 'deploy', 'migrate'
    ];
    const lengthHeavy = s.length > 320 || s.split(/\s+/).length > 60;
    if (lengthHeavy || complexHints.some(h => s.includes(h))) return 'complex';
    return 'simple';
  }

  public async handleRequest(query: string): Promise<string> {
    const complexity = await this.classifyComplexity(query);
    if (complexity === 'simple') {
      // Direct low-latency answer path (online only; no offline model here)
      return await simpleAnswer(query);
    }

    // Complex path: create plan, then execute (agents choose their own suitable models)
    const plan = await createPlanningPlan(query);
    const result = await executePlanning(plan);
    return typeof result === 'string' ? result : JSON.stringify(result);
  }
}
