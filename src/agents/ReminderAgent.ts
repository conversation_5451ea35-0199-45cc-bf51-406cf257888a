/**
 * Reminder Agent - Vercel AI SDK Implementation
 * Handles reminder creation, management, scheduling, and news monitoring
 */

import { generateText, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import {
  createReminderTool,
  listRemindersTool,
  updateReminderTool,
  deleteReminderTool,
  searchRemindersTool,
  pauseReminderTool,
  resumeReminderTool
} from '../tools/reminderTools';
import {
  createNewsMonitoringTool,
  listNewsMonitorsTool,
  updateNewsMonitorTool,
  deleteNewsMonitorTool
} from '../tools/unifiedReminderTools';
import { taskManagementTools } from '../tools/taskManagement';

export interface ReminderAgentOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  context?: any;
}

/**
 * Execute reminder operations with Vercel AI SDK
 */
export async function executeReminderAgent(
  input: string,
  options: ReminderAgentOptions = {}
): Promise<any> {
  const model = openai('gpt-5-mini'); // Good balance of capability and cost for reminders

  const commonConfig = {
    model,
    system: `You are <PERSON>'s Reminder Management Specialist. Your expertise is in creating, managing, and scheduling reminders, as well as monitoring news and events for users.

Your primary functions include:
1. **Reminder Management**: Create, update, delete, and schedule reminders with various types (one-time, recurring, event-based)
2. **News Monitoring**: Set up and manage news monitoring for specific topics, keywords, or sources
3. **Notification Delivery**: Ensure reminders and notifications are delivered through appropriate channels
4. **Schedule Optimization**: Help users optimize their reminder schedules and avoid conflicts
5. **Memory Integration**: Store and retrieve reminder-related information from user memory

Reminder Types Supported:
- One-time reminders with specific date/time
- Recurring reminders with cron expressions (daily, weekly, monthly, custom)
- News monitoring reminders that track specific topics
- Event-based reminders triggered by system events

Notification Channels:
- web_ui: Display in the web interface
- file_queue: Save to file system queue
- memory_store: Store in user memory system

Task Management:
- Check for assigned tasks using available task management tools
- Update your progress as you work on reminder operations
- Always provide clear feedback about reminder creation, updates, or issues

Best Practices:
- Always confirm reminder details before creation
- Use appropriate timezone information when scheduling
- Provide helpful scheduling suggestions (e.g., "daily at 9am" = "0 9 * * *")
- Explain cron expressions in plain language
- Test news monitoring queries before setting up long-term monitoring
- Respect user privacy and data retention preferences`,
    prompt: input,
    tools: {
      // Core reminder management tools
      create_reminder: createReminderTool,
      list_reminders: listRemindersTool,
      update_reminder: updateReminderTool,
      delete_reminder: deleteReminderTool,
      search_reminders: searchRemindersTool,
      pause_reminder: pauseReminderTool,
      resume_reminder: resumeReminderTool,

      // News monitoring tools
      create_news_monitor: createNewsMonitoringTool,
      list_news_monitors: listNewsMonitorsTool,
      update_news_monitor: updateNewsMonitorTool,
      delete_news_monitor: deleteNewsMonitorTool,

      // Task management integration
      ...taskManagementTools.reduce((acc, tool, index) => {
        const name = `task_mgmt_${index}`;
        acc[name] = tool;
        return acc;
      }, {} as Record<string, any>)
    },
    maxSteps: options.maxSteps ?? 8 // Allow multiple steps for complex reminder scenarios
  };

  if (options.stream) {
    return await streamText(commonConfig);
  } else {
    return await generateText(commonConfig);
  }
}

// Legacy compatibility wrapper
export const reminderAgent = {
  name: 'Reminder Agent',
  model: 'gpt-5-mini',

  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    return await executeReminderAgent(inputStr, options);
  }
};
