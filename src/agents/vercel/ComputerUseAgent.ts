/**
 * Computer Use Agent - Vercel AI SDK Implementation
 *
 * Specialized agent for computer automation tasks including browser control,
 * desktop automation, and visual interaction with applications.
 */

import { generateText, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { withRsRetryNonStreaming } from '../../services/responsesAPIClient';
import { computerUseDispatcher, runComputerUseDispatcher } from '../../tools/computerUseDispatcher';
import { computerUseTool } from '../../tools/computerUse/computerUseTool';
import { browserUseTools } from '../../tools/browserUseTools';
import { mapToolsByName } from '../../utils/toolUtils';
import { computerUseExecutor } from '../../tools/computerUse/computerUseToolExecutor';
import { config } from '../../utils/config';
import { fileOperationTools } from '../../tools/fileOperations';

export interface ComputerUseAgentOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  useGemini?: boolean;
  safetyLevel?: 'low' | 'medium' | 'high';
  context?: any;
}

/**
 * Execute computer use tasks with Vercel AI SDK
 */
export async function executeComputerUse(
  input: string,
  options: ComputerUseAgentOptions = {}
): Promise<any> {
  // Use Gemini for complex computer use tasks if available and requested
  const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5-mini');

  const commonConfig = {
    model,
    system: `You are Dante's Computer Use Specialist, an expert at automating computer tasks through visual interfaces.

Your capabilities include:
- Browser automation (clicking, typing, form filling, navigation)
- Desktop application control via Docker containers
- Screenshot analysis and visual element identification
- Multi-step workflow execution with safety checks
- Session management and task coordination

Computer Use Process:
1. Analyze the user's request and determine the best approach
2. Create or continue appropriate sessions (browser/desktop)
3. Use computer_use_dispatcher for task management
4. Execute actions safely with proper validation
5. Provide clear feedback on results and next steps

Safety Guidelines:
- Always validate URLs and actions before execution
- Use safety checks for sensitive operations
- Provide clear explanations of what actions will be taken
- Stop and ask for confirmation when encountering unexpected situations
- Maintain session isolation and proper cleanup

Tools Available:
- computer_use_dispatcher: Main tool for task management and coordination
- Direct computer use execution for simpler tasks

Focus on accuracy, safety, and clear communication about what you're doing and why.

Operational Conventions:
- For browser tasks (web navigation, clicking, typing), prefer the Python Browser-Use MCP tools (browser_use_*). Do NOT call computer_use start_session for browser tasks.
- Reserve computer_use for desktop/Ubuntu flows (Docker/X11) or when explicitly requested. Only then call computer_use with action="start_session" and environmentType="docker".
- If a browser task includes a URL, first call browser_use_open_and_wait with new_tab=false (unless specifically required) to navigate + wait for readiness with minimal screenshots. After ready, use browser_use_get_state to index elements and then click/type tools.
- If a result returns status="safety_check_required", ask the user to Approve & Continue. The UI provides an "Acknowledge Selected" button in the Computer Use Monitor; after approval, call action="acknowledge_safety" with the provided check IDs and continue.
- Before using read_file/read_files or file_edit on local paths, call get_working_directory to confirm alignment with the project path provided in context. If it differs, immediately call set_working_directory before proceeding.\n\nWhen the user provides attachments under /uploads, use read_uploaded_file/read_uploaded_files or read_file/read_files to open them. Uploaded attachments are renamed to normalized file IDs (e.g., /uploads/<fileId.ext>), so rely on the provided /uploads URL from the attachment metadata instead of the original filename. For PDFs, consider provider-native file input when available.`,
    // Prefer using a single stack per task to maintain context:
    // - computer_use for high-level, session-based automation, OR
    // - browser_use tools for granular navigation and DOM interactions.
    // Do not mix both in the same step.


    messages: [
      {
        role: 'user' as const,
        content: input
      }
    ],
  };

  try {
    const toolset = {
      // Always expose browser-use tools for web tasks
      ...mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
      // Only expose computer-use (Docker/X11) tools if explicitly enabled
      ...(config.computerUse.enabled ? { computerUseDispatcher, computerUseTool } : {}),
      // File operation tools for reading user uploads, etc.
      ...mapToolsByName(fileOperationTools as any, { fallbackPrefix: '' }),
    } as any;

    if (options.stream) {
      return await streamText({
        ...commonConfig,
        tools: toolset,
      });
    } else {
      return await withRsRetryNonStreaming({
        modelId: (commonConfig as any)?.model?.id ?? undefined,
        input,
        execute: () => generateText({
          ...commonConfig,
          tools: toolset,
        })
      });
    }
  } catch (error) {
    console.error('❌ Computer Use Agent execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: 'Computer use execution failed'
    };
  }
}

/**
 * Stream computer use execution with real-time updates
 */
export async function* streamComputerUse(
  input: string,
  options: ComputerUseAgentOptions = {}
): AsyncGenerator<any> {
  const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5-mini');

  try {
    const toolset = {
      ...mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
      ...(config.computerUse.enabled ? { computerUseDispatcher, computerUseTool } : {}),
      ...mapToolsByName(fileOperationTools as any, { fallbackPrefix: '' }),
    } as any;

    const result = streamText({
      model,
    system: `You are Dante's Computer Use Specialist. Execute computer automation tasks with streaming updates.

Provide real-time feedback on:
- Task analysis and planning
- Session creation and management
- Action execution progress
- Screenshot analysis results
- Safety check outcomes
- Task completion status

Use the computer_use_dispatcher tool for task management and coordination.
Always explain what you're doing and provide clear status updates.

Operational Conventions:
- For browser tasks (web navigation, clicking, typing), prefer Browser-Use MCP tools (browser_use_*). Do NOT start computer_use for these tasks.
- Use computer_use only for desktop/Ubuntu flows. When needed, call action="start_session" with environmentType="docker".
- For web tasks with a URL, first call browser_use_open_and_wait (new_tab=false) to reach readiness, then use browser_use_get_state and subsequent click/type tools.
- If a result returns status="safety_check_required", ask the user to Approve & Continue, then call action="acknowledge_safety" with the provided check IDs and continue.
- Before using read_file/read_files or file_edit on local paths, call get_working_directory to confirm alignment with the project path provided in context. If it differs, immediately call set_working_directory before proceeding.\n\nWhen the user provides attachments under /uploads, use read_uploaded_file/read_uploaded_files or read_file/read_files. Uploaded attachments are renamed to normalized file IDs (e.g., /uploads/<fileId.ext>); rely on the provided /uploads URL instead of the original filename when opening them.`,
      // Operational conventions reminder: include userPrompt in start_session, pass initialUrl when present,
      // and prompt the user to Approve & Continue when safety_check_required is returned.

      messages: [
        {
          role: 'user' as const,
          content: input
        }
      ],
      tools: toolset,
    });

    for await (const chunk of result.textStream) {
      yield {
        type: 'text',
        content: chunk,
        timestamp: new Date().toISOString()
      };
    }

    // Yield final result
    yield {
      type: 'complete',
      result: await result.text,
      toolResults: await result.toolResults,
      usage: await result.usage,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    yield {
      type: 'error',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Execute computer use with AI guidance and planning
 */
export async function executeComputerUseWithGuidance(
  userPrompt: string,
  options: ComputerUseAgentOptions = {}
): Promise<any> {
  console.log('🤖 Computer Use Agent: Executing with AI guidance');

  try {
    // Use the executor's AI guidance functionality
    const result = await computerUseExecutor.executeWithAIGuidance(
      userPrompt,
      { requestId: `agent_${Date.now()}` },
      {
        useGemini: options.useGemini,
        safetyLevel: options.safetyLevel,
        maxSteps: options.maxSteps
      }
    );

    return {
      success: result.success,
      sessionId: result.sessionId,
      result: result.result,
      screenshots: result.screenshots,
      actions: result.actions,
      safetyChecks: result.safetyChecks,
      metadata: {
        ...result.metadata,
        agent: 'ComputerUseAgent',
        aiGuided: true
      }
    };

  } catch (error) {
    console.error('❌ Computer Use Agent guidance execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      metadata: {
        agent: 'ComputerUseAgent',
        failed: true,
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Handle browser-specific computer use tasks
 */
export async function executeBrowserTask(
  task: string,
  initialUrl?: string,
  options: ComputerUseAgentOptions = {}
): Promise<any> {
  console.log('🌐 Computer Use Agent: Executing browser task');

  const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5');
  const userMessage = initialUrl
    ? `Navigate to ${initialUrl} and then ${task}`
    : task;

  try {
    const { mcpServerManager } = await import('../../mcp/MCPServerManager');
    const result = await withRsRetryNonStreaming({
      modelId: (model as any)?.id ?? undefined,
      input: userMessage,
      execute: () => generateText({
        model,
        system: `You are a browser automation specialist. Use the browser-use MCP tools efficiently.

    Guidelines:
    - If a URL is provided, first call browser_use_open_and_wait with new_tab=false, retries=5, max_wait_ms=120000, include_screenshot=true to navigate and wait for readiness (minimal screenshots). Then use browser_use_get_state to index elements and browser_use_click/type for interactions.
    - Avoid starting computer_use sessions for web tasks.
    - Prefer concise actions and verify context before clicking.`,
      messages: [
        {
          role: 'user' as const,
          content: userMessage,
        },
      ],
      tools: {
        ...mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
      },
      })
    });

    return {
      success: true,
      result,
    };
  } catch (error) {
    console.error('❌ Browser task execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      task: 'browser_automation',
    };
  }
}

/**
 * Handle desktop-specific computer use tasks
 */
export async function executeDesktopTask(
  task: string,
  options: ComputerUseAgentOptions = {}
): Promise<any> {
  console.log('🖥️ Computer Use Agent: Executing desktop task');

  try {
    // Create a desktop-specific task
    const taskResult = await runComputerUseDispatcher({
      operation: 'create_task',
      action: 'execute_task',
      environmentType: 'docker',
      userPrompt: task,
      displayWidth: 1024,
      displayHeight: 768
    });

    const parsedTask = (taskResult as any)?.result ?? taskResult;

    if (!parsedTask.success) {
      return parsedTask;
    }

    // Execute the task
    const executeResult = await runComputerUseDispatcher({
      operation: 'execute_task',
      taskId: parsedTask.taskId
    });

    return (executeResult as any)?.result ?? executeResult;

  } catch (error) {
    console.error('❌ Desktop task execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      task: 'desktop_automation'
    };
  }
}

/**
 * Get computer use task status
 */
export async function getTaskStatus(taskId: string): Promise<any> {
  try {
    const result = await runComputerUseDispatcher({
      operation: 'get_task_status',
      taskId
    });

    return (result as any)?.result ?? result;
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Cancel a computer use task
 */
export async function cancelTask(taskId: string): Promise<any> {
  try {
    const result = await runComputerUseDispatcher({
      operation: 'cancel_task',
      taskId
    });

    return (result as any)?.result ?? result;
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Cleanup computer use session
 */
export async function cleanupSession(sessionId: string): Promise<any> {
  try {
    const result = await runComputerUseDispatcher({
      operation: 'cleanup_session',
      sessionId
    });

    return (result as any)?.result ?? result;
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Main agent interface
export const computerUseAgent = {
  name: 'ComputerUseAgent',
  description: 'Specialized agent for computer automation, browser control, and desktop interaction',

  // Main execution method
  execute: executeComputerUse,

  // Streaming execution
  stream: streamComputerUse,

  // AI-guided execution
  executeWithGuidance: executeComputerUseWithGuidance,

  // Specialized methods
  browser: executeBrowserTask,
  desktop: executeDesktopTask,

  // Task management
  getTaskStatus,
  cancelTask,
  cleanupSession,

  // Expose underlying tools for test/inspection purposes
  tools: [
    computerUseTool
  ],

  // Agent metadata
  version: '2.0.0',
  sdk: 'vercel-ai',
  capabilities: [
    'browser_automation',
    'desktop_automation',
    'screenshot_analysis',
    'visual_interaction',
    'session_management',
    'task_coordination',
    'safety_validation'
  ]
};
