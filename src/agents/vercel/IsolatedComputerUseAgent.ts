/**
 * Isolated Computer Use Agent - Vercel AI SDK Implementation
 *
 * Provides isolated execution of computer use tasks with strict session
 * management, enhanced security, and complete context isolation.
 */

import { generateText, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import {
  computerUseExecutor,
  ComputerUseExecutionContext,
  ComputerUseExecutionOptions
} from '../../tools/computerUse/computerUseToolExecutor';
import { computerUseTool } from '../../tools/computerUse/computerUseTool';

// Enhanced task management for isolated execution
export interface ComputerUseTask {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'safety_check';
  sessionId?: string;
  request: any;
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  isolationLevel: 'strict' | 'standard' | 'relaxed';
  securityChecks?: any[];
}

export interface ComputerUseTaskManager {
  createTask: (task: any, isolationLevel?: 'strict' | 'standard' | 'relaxed') => string;
  getTask: (id: string) => ComputerUseTask | undefined;
  updateTask: (id: string, updates: Partial<ComputerUseTask>) => void;
  executeTask: (id: string) => Promise<any>;
  cancelTask: (id: string) => boolean;
  getAllTasks: () => ComputerUseTask[];
  cleanupTasks: () => void;
}

/**
 * Isolated Task Manager Implementation
 */
class IsolatedComputerUseTaskManager implements ComputerUseTaskManager {
  private tasks = new Map<string, ComputerUseTask>();
  private sessionIsolation = new Map<string, Set<string>>(); // sessionId -> taskIds

  createTask(request: any, isolationLevel: 'strict' | 'standard' | 'relaxed' = 'strict'): string {
    const taskId = `isolated_cu_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const task: ComputerUseTask = {
      id: taskId,
      status: 'pending',
      sessionId: request.sessionId,
      request,
      createdAt: new Date(),
      updatedAt: new Date(),
      isolationLevel,
      securityChecks: []
    };

    this.tasks.set(taskId, task);

    // Track session isolation
    if (request.sessionId) {
      if (!this.sessionIsolation.has(request.sessionId)) {
        this.sessionIsolation.set(request.sessionId, new Set());
      }
      this.sessionIsolation.get(request.sessionId)!.add(taskId);
    }

    console.log(`🔒 Created isolated computer use task: ${taskId} (isolation: ${isolationLevel})`);
    return taskId;
  }

  getTask(id: string): ComputerUseTask | undefined {
    return this.tasks.get(id);
  }

  updateTask(id: string, updates: Partial<ComputerUseTask>): void {
    const task = this.tasks.get(id);
    if (task) {
      Object.assign(task, updates);
      task.updatedAt = new Date();
      this.tasks.set(id, task);
      console.log(`🔒 Updated isolated task ${id}: status=${task.status}`);
    }
  }

  async executeTask(id: string): Promise<any> {
    const task = this.tasks.get(id);
    if (!task) {
      throw new Error(`Isolated task not found: ${id}`);
    }

    if (task.status !== 'pending') {
      throw new Error(`Task ${id} is not in pending status`);
    }

    this.updateTask(id, { status: 'running' });

    try {
      // Enhanced security checks for isolated execution
      const securityChecks = await this.performSecurityChecks(task);

      if (securityChecks.some(check => check.severity === 'critical')) {
        this.updateTask(id, {
          status: 'safety_check',
          securityChecks,
          error: 'Critical security checks failed'
        });
        return {
          success: false,
          status: 'safety_check_required',
          securityChecks,
          taskId: id
        };
      }

      // Execute with strict isolation
      const context: ComputerUseExecutionContext = {
        sessionId: task.sessionId,
        taskId: id,
        requestId: `isolated_${id}`
      };

      const options: ComputerUseExecutionOptions = {
        safetyLevel: task.isolationLevel === 'strict' ? 'high' :
                    task.isolationLevel === 'standard' ? 'medium' : 'low',
        timeout: task.isolationLevel === 'strict' ? 30000 : 60000
      };

      const result = await computerUseExecutor.executeComputerUseTask(
        task.request.action || 'execute_task',
        task.request,
        context,
        options
      );

      if (result.success) {
        this.updateTask(id, {
          status: 'completed',
          result: result.result
        });
      } else {
        this.updateTask(id, {
          status: 'failed',
          error: result.error,
          result: result.result
        });
      }

      return {
        ...result,
        taskId: id,
        isolationLevel: task.isolationLevel
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.updateTask(id, {
        status: 'failed',
        error: errorMessage,
        result: { success: false, error: errorMessage }
      });

      console.error(`❌ Isolated task ${id} failed:`, error);
      throw error;
    }
  }

  cancelTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (task && task.status === 'running') {
      this.updateTask(id, { status: 'cancelled' });

      // Cleanup session isolation if needed
      if (task.sessionId) {
        const sessionTasks = this.sessionIsolation.get(task.sessionId);
        if (sessionTasks) {
          sessionTasks.delete(id);
          if (sessionTasks.size === 0) {
            this.sessionIsolation.delete(task.sessionId);
          }
        }
      }

      console.log(`❌ Cancelled isolated task: ${id}`);
      return true;
    }
    return false;
  }

  getAllTasks(): ComputerUseTask[] {
    return Array.from(this.tasks.values());
  }

  cleanupTasks(): void {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    let cleaned = 0;

    for (const [taskId, task] of this.tasks.entries()) {
      if (task.updatedAt < oneDayAgo && task.status !== 'running') {
        this.tasks.delete(taskId);

        // Cleanup session isolation
        if (task.sessionId) {
          const sessionTasks = this.sessionIsolation.get(task.sessionId);
          if (sessionTasks) {
            sessionTasks.delete(taskId);
            if (sessionTasks.size === 0) {
              this.sessionIsolation.delete(task.sessionId);
            }
          }
        }

        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old isolated computer use tasks`);
    }
  }

  private async performSecurityChecks(task: ComputerUseTask): Promise<any[]> {
    const checks: any[] = [];

    // URL validation for browser tasks
    if (task.request.initialUrl || task.request.environmentType === 'browser') {
      const url = task.request.initialUrl;
      if (url) {
        // Check for suspicious domains
        const suspiciousDomains = ['malware.com', 'phishing.net']; // Example list
        const domain = new URL(url).hostname;

        if (suspiciousDomains.some(suspicious => domain.includes(suspicious))) {
          checks.push({
            id: `url_check_${Date.now()}`,
            code: 'SUSPICIOUS_DOMAIN',
            message: `Potentially dangerous domain detected: ${domain}`,
            severity: 'critical'
          });
        }
      }
    }

    // Action validation
    if (task.request.userPrompt) {
      const prompt = task.request.userPrompt.toLowerCase();
      const dangerousActions = ['delete', 'format', 'rm -rf', 'sudo', 'password'];

      for (const action of dangerousActions) {
        if (prompt.includes(action)) {
          checks.push({
            id: `action_check_${Date.now()}`,
            code: 'DANGEROUS_ACTION',
            message: `Potentially dangerous action detected: ${action}`,
            severity: task.isolationLevel === 'strict' ? 'critical' : 'high'
          });
        }
      }
    }

    return checks;
  }

  getTasksBySession(sessionId: string): ComputerUseTask[] {
    const taskIds = this.sessionIsolation.get(sessionId);
    if (!taskIds) return [];

    return Array.from(taskIds).map(id => this.tasks.get(id)!).filter(Boolean);
  }
}

// Global isolated task manager
const isolatedTaskManager = new IsolatedComputerUseTaskManager();

export interface IsolatedComputerUseOptions {
  isolationLevel?: 'strict' | 'standard' | 'relaxed';
  maxExecutionTime?: number;
  requireConfirmation?: boolean;
  securityLevel?: 'high' | 'medium' | 'low';
  stream?: boolean;
  useGemini?: boolean;
}

/**
 * Execute computer use tasks with strict isolation
 */
export async function executeIsolatedComputerUse(
  userPrompt: string,
  options: IsolatedComputerUseOptions = {}
): Promise<any> {
  const isolationLevel = options.isolationLevel || 'strict';
  console.log(`🔒 Executing isolated computer use task (level: ${isolationLevel})`);

  try {
    // Create isolated task
    const taskId = isolatedTaskManager.createTask({
      action: 'execute_task',
      userPrompt,
      environmentType: 'browser', // Default to browser for safety
      displayWidth: 1024,
      displayHeight: 768,
      requiresConfirmation: options.requireConfirmation !== false
    }, isolationLevel);

    // Execute task
    const result = await isolatedTaskManager.executeTask(taskId);

    return {
      ...result,
      isolation: {
        level: isolationLevel,
        taskId,
        executedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ Isolated computer use execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      isolation: {
        level: isolationLevel,
        failed: true,
        executedAt: new Date().toISOString()
      }
    };
  }
}

/**
 * Execute isolated computer use with AI agent coordination
 */
export async function executeIsolatedWithAgent(
  input: string,
  options: IsolatedComputerUseOptions = {}
): Promise<any> {
  const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5');

  try {
    const result = await generateText({
      model,
      system: `You are Dante's Isolated Computer Use Specialist. You execute computer automation tasks with the highest level of security and isolation.

ISOLATION REQUIREMENTS:
- All tasks run in completely isolated environments
- Enhanced security validation before execution
- Strict safety checks for all actions
- Complete session isolation and cleanup
- No persistent state between tasks

SECURITY PROTOCOL:
1. Validate all URLs and domains for safety
2. Scan user prompts for dangerous actions
3. Apply appropriate isolation level (strict/standard/relaxed)
4. Require confirmation for sensitive operations
5. Provide detailed security assessment

Your primary goal is safe execution with complete isolation. Always err on the side of caution.

Operational Conventions:
- When starting a session, include both environmentType and userPrompt, and set initialUrl if a target URL is present in the request.
- If a result returns status="safety_check_required", ask the user to Approve & Continue, then acknowledge by calling action="acknowledge_safety" with the provided check IDs before proceeding.`,

      messages: [
        {
          role: 'user' as const,
          content: input
        }
      ],
      tools: {
        computerUseTool
      },
    });

    // Execute the task through isolated manager
    const executionResult = await executeIsolatedComputerUse(input, options);

    return {
      agentResponse: result.text,
      toolCalls: result.toolCalls,
      usage: result.usage,
      executionResult,
      isolation: {
        agentControlled: true,
        level: options.isolationLevel || 'strict',
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ Isolated agent execution failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      isolation: {
        agentControlled: true,
        failed: true,
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Stream isolated computer use execution
 */
export async function* streamIsolatedComputerUse(
  input: string,
  options: IsolatedComputerUseOptions = {}
): AsyncGenerator<any> {
  const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5');

  try {
    yield {
      type: 'isolation_start',
      level: options.isolationLevel || 'strict',
      timestamp: new Date().toISOString()
    };

    const result = streamText({
      model,
      system: `You are Dante's Isolated Computer Use Specialist providing streaming execution updates.

STREAMING PROTOCOL:
- Provide real-time security validation updates
- Report isolation status at each step
- Stream safety check results
- Give detailed progress on task execution
- Report any security concerns immediately

Maintain the highest security standards while providing clear status updates.

Operational Conventions:
- Always include environmentType and userPrompt when starting sessions, and set initialUrl when applicable.
- On safety_check_required, request user approval (Approve & Continue) and then call action="acknowledge_safety" with the check IDs before continuing.`,

      messages: [
        {
          role: 'user' as const,
          content: input
        }
      ],
      tools: {
        computerUseTool
      },
    });

    for await (const chunk of result.textStream) {
      yield {
        type: 'agent_response',
        content: chunk,
        isolation: true,
        timestamp: new Date().toISOString()
      };
    }

    // Execute isolated task
    yield {
      type: 'execution_start',
      message: 'Starting isolated execution...',
      timestamp: new Date().toISOString()
    };

    const executionResult = await executeIsolatedComputerUse(input, options);

    yield {
      type: 'execution_complete',
      result: executionResult,
      isolation: {
        level: options.isolationLevel || 'strict',
        completed: true
      },
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    yield {
      type: 'error',
      error: error instanceof Error ? error.message : String(error),
      isolation: {
        failed: true,
        level: options.isolationLevel || 'strict'
      },
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get isolated task status
 */
export async function getIsolatedTaskStatus(taskId: string): Promise<any> {
  const task = isolatedTaskManager.getTask(taskId);

  if (!task) {
    return {
      success: false,
      error: `Isolated task not found: ${taskId}`
    };
  }

  return {
    success: true,
    task: {
      id: task.id,
      status: task.status,
      sessionId: task.sessionId,
      isolationLevel: task.isolationLevel,
      result: task.result,
      error: task.error,
      securityChecks: task.securityChecks,
      createdAt: task.createdAt.toISOString(),
      updatedAt: task.updatedAt.toISOString()
    }
  };
}

/**
 * Cancel isolated task
 */
export async function cancelIsolatedTask(taskId: string): Promise<any> {
  const cancelled = isolatedTaskManager.cancelTask(taskId);

  return {
    success: cancelled,
    taskId,
    message: cancelled ? 'Isolated task cancelled successfully' : 'Task could not be cancelled'
  };
}

// Periodic cleanup
setInterval(() => {
  isolatedTaskManager.cleanupTasks();
}, 60 * 60 * 1000); // Every hour

// Main isolated agent interface
export const isolatedComputerUseAgent = {
  name: 'Isolated Computer Use Agent',
  description: 'Computer use agent with strict isolation, enhanced security, and complete session management',

  // Main execution methods
  execute: executeIsolatedComputerUse,
  executeWithAgent: executeIsolatedWithAgent,
  stream: streamIsolatedComputerUse,

  // Task management
  taskManager: isolatedTaskManager,
  getTaskStatus: getIsolatedTaskStatus,
  cancelTask: cancelIsolatedTask,

  // Agent metadata
  version: '2.0.0',
  sdk: 'vercel-ai',
  isolationLevels: ['strict', 'standard', 'relaxed'],
  securityFeatures: [
    'url_validation',
    'action_scanning',
    'session_isolation',
    'enhanced_security_checks',
    'complete_cleanup'
  ]
};

// Export compatibility types
export { isolatedTaskManager as computerUseTaskManager };
