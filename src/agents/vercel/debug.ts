/**
 * Debug Agent - Vercel AI SDK Implementation
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { fileOperationTools } from '../../tools/fileOperations';
import { grepCodeTool } from '../../tools/grepCode';
import { google } from '@ai-sdk/google';
import { withRsRetryNonStreaming } from '../../services/responsesAPIClient';

export interface DebugOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  context?: any;
  onStepFinish?: (step: any) => void;
}

/**
 * Execute debugging task with Vercel AI SDK
 */
export async function executeDebug(
  input: string,
  options: DebugOptions = {}
): Promise<any> {
  const model = google('gemini-2.5-flash'); // Good model for debugging

  // Structured answer tool for typed debug output
  const answerTool = tool({
    description: 'Provide the final debugging result in a structured format.',
    inputSchema: z.object({
      summary: z.string().describe('Concise summary of the issue and fix'),
      rootCause: z.string().optional().describe('Identified root cause'),
      stepsToReproduce: z.array(z.string()).optional().describe('Steps to reproduce the issue'),
      fix: z.string().optional().describe('Applied fix or recommended solution'),
      impactedFiles: z.array(z.string()).optional().describe('Relevant file paths'),
      followUps: z.array(z.string()).optional().describe('Follow-up actions or tests'),
    }) as any,
    // no execute; tool call terminates with structured output
  }) as any;

  const commonConfig = {
    model,
    system: `You are Dante's Debug Specialist, an expert at identifying and fixing bugs, errors, and technical issues.

Your debugging expertise includes:
- Systematic problem analysis and root cause identification
- Error message interpretation and stack trace analysis
- Code debugging across multiple programming languages
- Performance optimization and bottleneck identification
- System diagnostics and troubleshooting

Debugging Methodology:
1. **Understand the Problem**: Analyze error messages, symptoms, and expected behavior
2. **Reproduce the Issue**: Identify steps to consistently reproduce the problem
3. **Isolate the Root Cause**: Use systematic debugging to narrow down the source
4. **Implement the Fix**: Apply targeted solutions with proper testing
5. **Prevent Recurrence**: Suggest improvements to avoid similar issues

Always approach debugging systematically and provide clear explanations of both the problem and solution.
When delivering the final response, use the answer tool to provide a structured summary.`,
    prompt: input,
    tools: {
      // File operation tools for reading and analyzing code
      ...fileOperationTools.reduce((acc, tool) => {
        const name = tool.name || 'unknown_tool';
        acc[name] = tool;
        return acc;
      }, {} as Record<string, any>),
      grep_code: grepCodeTool,
      // Structured answer
      answer: answerTool
    },
    stopWhen: stepCountIs(options.maxSteps ?? 10),
    toolChoice: 'required' as any,
    onStepFinish: options.onStepFinish,
    providerOptions: {
      google: {
        thinkingConfig: {
          includeThoughts: true,
          thinkingBudget: 8192,
        }
      },
      openai: {
        reasoningEffort: 'medium',
        reasoningSummary: 'auto'
      }
    },
    context: options.context
  };

  if (options.stream) {
    return await streamText(commonConfig);
  } else {
    const result = await withRsRetryNonStreaming({
      modelId: (commonConfig as any)?.model?.id ?? undefined,
      input,
      execute: () => generateText(commonConfig)
    });
    let structured: any = undefined;
    try {
      const call = (result as any).toolCalls?.find((c: any) => (c.name || c.toolName) === 'answer')
        || (result as any).toolCalls?.[0];
      structured = call?.args ?? call?.input ?? undefined;
    } catch {}
    const render = (s: any): string => {
      if (!s) return '';
      const out: string[] = [];
      if (s.summary) out.push(String(s.summary));
      if (s.rootCause) out.push(`Root Cause: ${s.rootCause}`);
      const list = (title: string, arr?: any[]) => {
        if (Array.isArray(arr) && arr.length) {
          out.push(`${title}:`);
          out.push(...arr.map((v: any) => `- ${typeof v === 'string' ? v : JSON.stringify(v)}`));
        }
      };
      list('Steps to Reproduce', s.stepsToReproduce);
      if (s.fix) out.push(`Fix: ${s.fix}`);
      list('Impacted Files', s.impactedFiles);
      list('Follow Ups', s.followUps);
      return out.join('\n');
    };
    const finalText = (result.text && String(result.text).trim().length > 0)
      ? result.text
      : render(structured);
    return {
      text: finalText,
      toolCalls: (result as any).toolCalls,
      usage: result.usage,
      steps: (result as any).steps,
      structuredDebug: structured,
    };
  }
}

// Legacy compatibility wrapper
export const vercelDebugAgent = {
  name: 'Debug Agent',
  model: 'gemini-2.5-flash',

  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    return await executeDebug(inputStr, options);
  }
};
