/**
 * Google Workspace Agent - Vercel AI SDK Implementation
 * Handles Gmail, Calendar, and Drive operations
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { withRsRetryNonStreaming } from '../../services/responsesAPIClient';
import {
  searchGmail,
  getRecentGmailEmails,
  readGmailEmail,
  batchReadGmailEmails,
  searchCalendarEvents,
  getTodayEvents,
  getWeekEvents,
  readCalendarEvent,
  getUpcomingMeetings,
  searchDrive,
  getRecentDocuments,
  fetchDriveFile,
  listSharedDrives,
  searchDriveByType
} from '../../tools/connectors';

export interface GoogleWorkspaceOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  useGemini?: boolean;
  context?: any;
  onStepFinish?: (step: any) => void;
}

// Public type for the legacy agent wrapper to improve typing in imports
export type VercelGoogleWorkspaceAgent = {
  name: string;
  model?: string;
  execute(input: string | any[], options?: any): Promise<any>;
};

/**
 * Execute Google Workspace task with Vercel AI SDK
 */
export async function executeGoogleWorkspaceTask(
  input: string,
  options: GoogleWorkspaceOptions = {}
): Promise<any> {
  // Use gpt-5 for Google Workspace integration for reliable OAuth handling
  const model = openai('gpt-5');

  // Structured answer tool for typed Workspace output
  const answerTool = tool({
    description: 'Provide the final Google Workspace action result in a structured format.',
    inputSchema: z.object({
      summary: z.string().describe('Concise summary of what was done or found'),
      items: z.array(z.object({
        type: z.string().optional(),
        id: z.string().optional(),
        title: z.string().optional(),
        url: z.string().optional(),
        when: z.string().optional(),
        note: z.string().optional(),
      })).optional(),
      authenticationRequired: z.boolean().optional(),
      nextSteps: z.array(z.string()).optional(),
    }) as any,
    // no execute; tool call terminates with structured output
  }) as any;

  const commonConfig = {
    model,
    system: `You are Dante's Google Workspace Specialist, expert at managing Gmail, Calendar, and Drive operations.

Your capabilities include:

📧 GMAIL OPERATIONS:
- Search emails with advanced queries
- Read specific emails or batch read multiple emails
- Get recent emails and manage inbox
- Support complex search filters (from:, subject:, date:, etc.)

📅 CALENDAR OPERATIONS:
- Search calendar events by time range or keywords
- Get today's schedule and weekly overview
- Read detailed event information
- Find upcoming meetings and availability

💾 DRIVE OPERATIONS:
- Search files and folders with advanced filters
- Access recent documents and shared drives
- Fetch and download file content
- Search by file type (docs, sheets, presentations, PDFs)

AUTHENTICATION:
- All operations require proper OAuth authentication
- Guide users through authentication flow when needed
- Maintain user privacy and security at all times
- Handle authentication errors gracefully

BEST PRACTICES:
- Always check authentication status before operations
- Provide clear error messages and next steps
- Format results in a user-friendly manner
- Respect user privacy and data security
- Use appropriate search queries for efficient results

When users need Google Workspace operations, guide them through the process and execute their requests efficiently.
When delivering the final response, use the answer tool to provide a structured summary.`,
    prompt: input,
    tools: {
      // Gmail tools
      search_gmail: searchGmail,
      get_recent_gmail_emails: getRecentGmailEmails,
      read_gmail_email: readGmailEmail,
      batch_read_gmail_emails: batchReadGmailEmails,

      // Calendar tools
      search_calendar_events: searchCalendarEvents,
      get_today_calendar_events: getTodayEvents,
      get_week_calendar_events: getWeekEvents,
      read_calendar_event: readCalendarEvent,
      get_upcoming_meetings: getUpcomingMeetings,

      // Drive tools
      search_google_drive: searchDrive,
      get_recent_drive_documents: getRecentDocuments,
      fetch_drive_file: fetchDriveFile,
      list_shared_drives: listSharedDrives,
      search_drive_by_type: searchDriveByType,
      // Structured answer
      answer: answerTool
    },
    stopWhen: stepCountIs(options.maxSteps ?? 10),
    toolChoice: 'required' as any,
    onStepFinish: options.onStepFinish
  };

  if (options.stream) {
    return await streamText(commonConfig);
  } else {
    const result = await withRsRetryNonStreaming({
      modelId: (commonConfig as any)?.model?.id ?? undefined,
      input,
      execute: () => generateText(commonConfig)
    });
    let structured: any = undefined;
    try {
      const call = (result as any).toolCalls?.find((c: any) => (c.name || c.toolName) === 'answer')
        || (result as any).toolCalls?.[0];
      structured = call?.args ?? call?.input ?? undefined;
    } catch {}
    const render = (s: any): string => {
      if (!s) return '';
      const out: string[] = [];
      if (s.summary) out.push(String(s.summary));
      if (Array.isArray(s.items) && s.items.length) {
        out.push('Items:');
        for (const it of s.items) {
          const attrs = [it.type, it.id, it.title, it.url, it.when].filter(Boolean).join(' — ');
          out.push(`- ${attrs || JSON.stringify(it)}`);
          if (it.note) out.push(`  note: ${it.note}`);
        }
      }
      if (s.authenticationRequired) out.push('Authentication Required: Yes');
      if (Array.isArray(s.nextSteps) && s.nextSteps.length) {
        out.push('Next Steps:');
        out.push(...s.nextSteps.map((n: any) => `- ${String(n)}`));
      }
      return out.join('\n');
    };
    const finalText = (result.text && String(result.text).trim().length > 0)
      ? result.text
      : render(structured);
    return {
      text: finalText,
      toolCalls: (result as any).toolCalls,
      usage: result.usage,
      steps: (result as any).steps,
      structuredWorkspace: structured,
    };
  }
}

// Legacy compatibility wrapper
export const vercelGoogleWorkspaceAgent = {
  name: 'Google Workspace Agent',
  model: 'gpt-5',

  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    return await executeGoogleWorkspaceTask(inputStr, options);
  }
};
