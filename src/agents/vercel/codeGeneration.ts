/**
 * Code Generation Agent - Vercel AI SDK Implementation
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { fileOperationTools, fileEditTool } from '../../tools/fileOperations';
import { taskManagementTools } from '../../tools/taskManagement';
import { agent_empty_final_total } from '../../../server/observability/metrics';
import { withRsRetryNonStreaming } from '../../services/responsesAPIClient';
import { RunJournal } from '../orchestration/journal/RunJournal';
import { Checkpoint, CheckpointDiagnostics, CheckpointState, NextAction, isoNow } from '../orchestration/journal/types';
import { PrepareMessage, PrepareResponse, CommitMessage, CommitResponse, AbortMessage, AbortResponse } from '../orchestration/types';
import { getConfig } from '../../utils/config';
import { parseUnifiedDiffText, stripABPrefix } from '../../utils/unifiedDiff';
import { getContextValue } from '../../utils/requestContext';
import { openOrUpdatePR } from '../../services/prService';
import { exec } from 'child_process';
import { promisify } from 'util';
const execAsync = promisify(exec);
const git = require('../../services/gitService') as any;

const CODEGEN_AGENT_ID = 'vercel-codegen';

function deriveFileScopesFromResourceKeys(resourceKeys?: string[]): string[] {
  if (!resourceKeys || resourceKeys.length === 0) return [];
  const scopes = new Set<string>();
  for (const key of resourceKeys) {
    if (!key) continue;
    const parts = key.split('|');
    for (const part of parts) {
      if (part.startsWith('path:')) {
        const value = part.slice('path:'.length).trim();
        if (value) scopes.add(value);
      }
    }
  }
  return Array.from(scopes);
}

/**
 * NOTE: Model selection is orchestrator-driven.
 * The orchestrator must provide the model here; this agent no longer selects a model internally.
 * Prior env-based selection has been centralized into the orchestrator.
 */
export interface CodeGenerationOptions {
  model: any; // Required: Vercel AI model instance provided by the orchestrator
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  context?: any;
  instructions?: string;
  abortSignal?: AbortSignal;
  onStepFinish?: (step: any) => void;
  traceId?: string;
}

enum AgentState {
  THINKING = 'THINKING',
  CALLING_TOOL = 'CALLING_TOOL',
  AWAITING_MODEL = 'AWAITING_MODEL',
  FINALIZING = 'FINALIZING',
  FINAL_EMITTED = 'FINAL_EMITTED',
}

class CodeGenerationAgent {
  private state: AgentState = AgentState.THINKING;
  private traceId: string;
  private partialResults: any[] = [];

  constructor(private input: string, private options: CodeGenerationOptions) {
    this.traceId = options.traceId || uuidv4();
  }

  private setState(newState: AgentState) {
    this.state = newState;
  }

  private ensureFinalEmission(error?: Error) {
    if (this.state === AgentState.FINAL_EMITTED) {
      return;
    }
    this.setState(AgentState.FINALIZING);

    if (error) {
      const salvagedResponse = {
        error: 'AGENT_EXECUTION_ERROR',
        message: error.message,
        traceId: this.traceId,
        partialResults: this.partialResults,
        nextSteps: 'Review the error and partial results to debug the issue. You may need to adjust the prompt or tools.',
        structuredCodegen: {
          summary: `Execution failed with error: ${error.message}`,
          notes: [
            `Trace ID: ${this.traceId}`,
            'This is a salvaged response due to an internal error.'
          ],
        },
      };
      console.error("Emitting salvaged final response:", salvagedResponse);
      agent_empty_final_total.inc();
    } else {
      console.log("Emitting successful final response");
    }

    this.setState(AgentState.FINAL_EMITTED);
  }

  public async execute(): Promise<any> {
    try {
      return await this.run();
    } catch (error: any) {
      this.ensureFinalEmission(error);
      throw error;
    } finally {
      this.ensureFinalEmission();
    }
  }

  private async run(): Promise<any> {
    const model = this.options.model;
    if (!model) {
      throw new Error('CodeGenerationAgent requires a model supplied by the orchestrator (no internal selection).');
    }

    const answerTool = tool({
      description: 'Provide the final code generation result in a structured format.',
      inputSchema: z.object({
        summary: z.string().describe('Concise summary of changes'),
        filesModified: z.array(
          z.object({
            path: z.string(),
            changeType: z.enum(['create', 'modify', 'delete']).optional(),
            rationale: z.string().optional(),
            diffHint: z.string().optional().describe('Short, human-readable summary of changes with line refs if known'),
            patchPreview: z.string().optional().describe('Unified diff preview (---/+++ headers) with essential hunks only'),
          })
        ).optional(),
        instructions: z.array(z.string()).optional().describe('Instructions to apply or verify changes'),
        commandsToRun: z.array(z.string()).optional().describe('Shell commands to validate/build/test'),
        notes: z.array(z.string()).optional(),
      }) as any,
    }) as any;

    const systemPrompt = this.options.instructions ?? `You are Dante's Code Generation Specialist...`;

    const commonConfig = {
      model,
      system: systemPrompt,
      prompt: this.input,
      tools: {
        ...fileOperationTools.reduce((acc, t) => ({ ...acc, [((t as any).name)]: t }), {}),
        ...taskManagementTools.reduce((acc, t) => ({ ...acc, [((t as any).name)]: t }), {}),
        answer: answerTool,
      },
      stopWhen: stepCountIs(this.options.maxSteps ?? 10),
      toolChoice: 'required' as any,
      onStepFinish: (step: any) => {
        this.partialResults.push(step);
        if (this.options.onStepFinish) {
          this.options.onStepFinish(step);
        }
      },
      context: this.options.context,
      traceId: this.traceId,
      abortSignal: this.options.abortSignal,
      providerOptions: {
        google: {
          thinkingConfig: { includeThoughts: true, thinkingBudget: 8192}
        },
        openai: {
          reasoningSummary: 'auto',
          reasoningEffort: 'high',
        }
      },
    };

    if (this.options.stream) {
      this.setState(AgentState.AWAITING_MODEL);
      return streamText(commonConfig);
    } else {
      this.setState(AgentState.AWAITING_MODEL);
      const result = await withRsRetryNonStreaming({
        modelId: (commonConfig as any)?.model?.id ?? undefined,
        input: this.input,
        execute: () => generateText(commonConfig)
      });
      this.setState(AgentState.THINKING);
      const normalizeStructured = (raw: any): any => {
        if (raw == null) return undefined;
        if (typeof raw === 'string') {
          const trimmed = raw.trim();
          if (!trimmed) return undefined;
          try {
            const parsed = JSON.parse(trimmed);
            return normalizeStructured(parsed);
          } catch {
            return { summary: trimmed };
          }
        }
        if (typeof raw === 'object') return raw;
        return { summary: String(raw) };
      };

      let structured: any = undefined;
      try {
        const toolCalls = Array.isArray((result as any).toolCalls) ? (result as any).toolCalls : [];
        const answerCall = toolCalls.find((c: any) => c?.toolName === 'answer');
        structured = normalizeStructured(answerCall?.args ?? answerCall?.arguments);

        if (!structured) {
          const steps = Array.isArray((result as any).steps) ? (result as any).steps : [];
          for (let i = steps.length - 1; i >= 0; i -= 1) {
            const step = steps[i];
            if ((step?.type === 'tool-call' || step?.type == null) && step?.toolName === 'answer') {
              structured = normalizeStructured(step?.args ?? step?.arguments);
              if (structured) break;
            }
          }
        }
      } catch (e) {
        console.error('Error extracting structured data:', e);
      }

      const render = (s: any): string => {
        if (!s) return '';
        if (typeof s === 'string') return s;
        if (typeof s !== 'object') return String(s ?? '');
        const out: string[] = [];
        if (s.summary) out.push(String(s.summary));
        if (Array.isArray(s.filesModified) && s.filesModified.length) {
          out.push('Files Modified:');
          for (const f of s.filesModified) out.push(`- ${f.path} [${f.changeType || 'modify'}]`);
        }
        if (Array.isArray(s.notes) && s.notes.length) {
          out.push('Notes:');
          for (const note of s.notes) out.push(`- ${note}`);
        }
        return out.join('\n');
      };

      const renderToolTrace = (): string => {
        const steps = Array.isArray((result as any).steps) ? (result as any).steps : [];
        const calls = steps
          .filter((step: any) => (step?.type === 'tool-call' || step?.type == null) && step?.toolName)
          .map((step: any) => {
            const toolName = step.toolName;
            const args = step.args ?? step.arguments;
            if (toolName === 'answer') return undefined;
            if (args && typeof args === 'object') {
              if ('path' in args && typeof args.path === 'string') return `- ${toolName}: ${args.path}`;
              if ('filePath' in args && typeof args.filePath === 'string') return `- ${toolName}: ${args.filePath}`;
            }
            return `- ${toolName}`;
          })
          .filter(Boolean) as string[];
        if (!calls.length) return '';
        return ['Tool activity:', ...calls].join('\n');
      };

      const pickFirstNonEmpty = (...values: Array<string | undefined>): string | undefined => {
        for (const value of values) {
          if (value && value.trim().length > 0) return value.trim();
        }
        return undefined;
      };

      const finalText = pickFirstNonEmpty(
        typeof result.text === 'string' ? result.text : undefined,
        render(structured),
        renderToolTrace(),
      ) ?? 'Code generation finished without a model-provided summary. Review the tool steps for details.';

      const structuredCodegen = structured && typeof structured === 'object' ? structured : undefined;

      return {
        text: finalText,
        toolCalls: (result as any).toolCalls,
        usage: result.usage,
        steps: (result as any).steps,
        structuredCodegen,
      };
    }
  }
}

export async function executeCodeGeneration(
  input: string,
  options: CodeGenerationOptions
): Promise<any> {
  const agent = new CodeGenerationAgent(input, options);
  return agent.execute();
}

/**
 * Two-Phase Commit (2PC) pipeline for implement/refactor/fix scopes.
 * Phase 1 (plan): file_edit(dryRun) -> previewDiff + patchPlanHash -> checks -> checkpoint(plan_ready)
 * Phase 2 (apply): idempotency by patchPlanHash -> file_edit(apply) -> checks -> commit (+optional PR) -> checkpoint(committed)
 */
export interface TwoPhaseCommitOptions {
  patchPlan: string | { ops?: any[]; diff?: string; [k: string]: any };
  intent?: 'implement' | 'refactor' | 'fix';
  workingBranch?: string;
  baseBranch?: string;
  runId?: string;
  taskId?: string;
}

type CheckDiag = { status: 'success' | 'failure'; logs?: string };

type VerificationCheckResult = { id: string; command: string; diag: CheckDiag };

type VerificationChecksSummary = {
  steps: VerificationCheckResult[];
  byName: Record<string, CheckDiag>;
  allGreen: boolean;
};

async function runCmd(cmd: string): Promise<{ ok: boolean; out: string }> {
  try {
    const { stdout, stderr } = await execAsync(cmd, { timeout: 120000 });
    return { ok: true, out: `${stdout || ''}${stderr || ''}` };
  } catch (e: any) {
    const out = `${e?.stdout || ''}${e?.stderr || e?.message || ''}`;
    if (/missing script|not found|ENOENT/i.test(out)) return { ok: true, out: `[skipped] ${out}` };
    return { ok: false, out };
  }
}

async function runVerificationChecks(): Promise<VerificationChecksSummary> {
  const cfg = getConfig();
  const configuredSteps = Array.isArray(cfg.orchestrator?.verification?.steps)
    ? cfg.orchestrator?.verification?.steps
    : [];

  const stepsToRun = (configuredSteps || [])
    .map(step => {
      if (!step) return null;
      const id = typeof (step as any).id === 'string' ? (step as any).id.trim() : '';
      const command = typeof (step as any).command === 'string' ? (step as any).command.trim() : '';
      if (!id || !command) return null;
      return { id, command };
    })
    .filter((step): step is { id: string; command: string } => Boolean(step));

  if (stepsToRun.length === 0) {
    return { steps: [], byName: {}, allGreen: true };
  }

  const steps: VerificationCheckResult[] = [];
  const byName: Record<string, CheckDiag> = {};
  let allGreen = true;

  for (const { id, command } of stepsToRun) {
    const result = await runCmd(command);
    const diag: CheckDiag = { status: result.ok ? 'success' : 'failure', logs: result.out };
    steps.push({ id, command, diag });
    byName[id] = diag;
    if (!result.ok) allGreen = false;
  }

  return { steps, byName, allGreen };
}

function extractFileScopesFromPlan(plan: any): string[] {
  try {
    if (typeof plan === 'string' || typeof plan?.diff === 'string') {
      const diffText = typeof plan === 'string' ? plan : plan.diff;
      const parsed = parseUnifiedDiffText(diffText);
      const paths = parsed.files.map(f => stripABPrefix(f.path));
      return Array.from(new Set(paths));
    }
    const ops = Array.isArray(plan?.ops) ? plan.ops : Array.isArray(plan?.operations) ? plan.operations : [];
    const files = ops.map((o: any) => o?.filePath).filter((p: any) => typeof p === 'string');
    return Array.from(new Set(files));
  } catch {
    return [];
  }
}

function buildDiagnosticsFromChecks(
  checks: VerificationChecksSummary,
  extras: Partial<CheckpointDiagnostics> = {}
): CheckpointDiagnostics {
  const base: CheckpointDiagnostics = {
    failureSummary: null,
    lastStackTrace: null,
    ...extras,
  };

  for (const [name, diag] of Object.entries(checks.byName)) {
    if (name === 'typecheck') base.typecheck = diag;
    else if (name === 'build') base.build = diag;
    else if (name === 'test') base.test = diag;
    else if (name === 'lint') base.lint = diag;
    else if (name === 'black') base.lint = diag;
    else {
      if (!base.checks) base.checks = {};
      base.checks[name] = diag;
    }
  }

  return base;
}

function buildCommitMessage(runId: string, taskId: string, stepId: string | undefined, intent: string | undefined, hash: string): string {
  const cfg = getConfig();
  const type = cfg.orchestrator?.resilience?.commit?.conventionalType || 'feat';
  const scope = intent || 'codegen';
  return `${type}(${scope}): apply patch ${hash.slice(0, 8)} [run:${runId} task:${taskId}]`;
}

export async function codeGenerationPrepare(payload: PrepareMessage): Promise<PrepareResponse> {
  const respondedAt = isoNow();
  const patchPlan = (payload as any)?.payload?.patchPlan ?? (payload.metadata as any)?.patchPlan;
  const intent = (payload as any)?.payload?.intent ?? (payload.metadata as any)?.intent ?? 'codegen';

  if (!patchPlan) {
    return { vote: 'no', reason: 'missing_patch_plan', respondedAt };
  }

  const journal = new RunJournal();
  const runId = payload.runId;
  const taskId = payload.taskId;
  const stepId = payload.stepId ?? `prepare_${Date.now()}`;

  const planRes = await (fileEditTool as any).execute({
    patchPlan,
    dryRun: true,
    diagnostics: false,
  });

  if (!planRes?.success) {
    const failureSummary = planRes?.error?.message ?? 'plan_dry_run_failed';
    const cpFailed: Checkpoint = {
      runId,
      taskId,
      stepId,
      agent: CODEGEN_AGENT_ID,
      timestamps: { createdAt: isoNow(), planReadyAt: isoNow(), failedAt: isoNow() },
      state: CheckpointState.Failed,
      nextAction: NextAction.Fail,
      scope: {
        fileScopes: extractFileScopesFromPlan(patchPlan),
        fileLocks: Array.isArray(payload.resourceKeys) ? [...payload.resourceKeys] : [],
        concurrencyGroupId: undefined,
      },
      plan: {
        patchPlan,
        previewDiff: planRes?.previewDiff,
        patchPlanHash: planRes?.patchPlanHash,
      },
      diagnostics: { failureSummary, lastStackTrace: null },
    } as any;
    try { await journal.upsertCheckpoint(cpFailed); } catch {}
    return { vote: 'no', reason: failureSummary, respondedAt };
  }

  const patchPlanHash: string | undefined = planRes.patchPlanHash;
  const fileScopes = extractFileScopesFromPlan(patchPlan);

  if (!fileScopes || fileScopes.length === 0) {
    return { vote: 'no', reason: 'missing_file_scopes', respondedAt };
  }

  const cpPlanReady: Checkpoint = {
    runId,
    taskId,
    stepId,
    agent: CODEGEN_AGENT_ID,
    timestamps: { createdAt: isoNow(), planReadyAt: isoNow() },
    state: CheckpointState.PlanReady,
    nextAction: NextAction.Apply,
    scope: {
      fileScopes,
      fileLocks: Array.isArray(payload.resourceKeys) ? [...payload.resourceKeys] : [],
      concurrencyGroupId: undefined,
    },
    plan: {
      patchPlan,
      previewDiff: planRes.previewDiff,
      patchPlanHash,
      intent,
    },
  } as any;
  try { await journal.upsertCheckpoint(cpPlanReady); } catch {}

  const checks = await runVerificationChecks();
  const diagnosticsAfterChecks = buildDiagnosticsFromChecks(checks, {
    failureSummary: checks.allGreen ? null : 'pre-apply checks failed',
    lastStackTrace: null,
  });
  const cpAfterChecks: Checkpoint = {
    ...cpPlanReady,
    diagnostics: diagnosticsAfterChecks,
    state: checks.allGreen ? CheckpointState.PlanReady : CheckpointState.Failed,
    nextAction: checks.allGreen ? NextAction.Apply : NextAction.Fail,
  };
  try { await journal.upsertCheckpoint(cpAfterChecks); } catch {}

  if (!checks.allGreen) {
    return {
      vote: 'no',
      reason: 'pre_apply_checks_failed',
      respondedAt: isoNow(),
      payload: {
        diagnostics: diagnosticsAfterChecks,
        verification: checks,
        patchPlanHash,
        previewDiff: planRes.previewDiff,
      },
    };
  }

  return {
    vote: 'yes',
    respondedAt: isoNow(),
    payload: {
      patchPlanHash,
      previewDiff: planRes.previewDiff,
      intent,
      verification: checks,
    },
  };
}

export async function codeGenerationCommit(payload: CommitMessage): Promise<CommitResponse> {
  const journal = new RunJournal();
  const respondedAt = isoNow();
  const checkpoint = await journal.getCurrent(payload.runId, payload.taskId);

  if (!checkpoint || !checkpoint.plan?.patchPlan) {
    return { ok: false, commitSha: null, respondedAt, payload: { reason: 'missing_plan_checkpoint' } };
  }

  const patchPlan = checkpoint.plan.patchPlan;
  const patchPlanHash = checkpoint.plan.patchPlanHash ?? payload.patchPlanHash ?? 'unknown';
  const intent = (checkpoint.plan as any)?.intent ?? (payload.metadata as any)?.intent ?? 'codegen';

  if (
    checkpoint.state === CheckpointState.Committed &&
    checkpoint.artifacts?.commitSha &&
    (!payload.patchPlanHash || payload.patchPlanHash === patchPlanHash)
  ) {
    return {
      ok: true,
      commitSha: checkpoint.artifacts.commitSha,
      respondedAt,
      payload: { idempotent: true },
    };
  }

  const fileScopes = checkpoint.scope?.fileScopes ?? extractFileScopesFromPlan(patchPlan);
  if (!fileScopes || fileScopes.length === 0) {
    return { ok: false, commitSha: null, respondedAt, payload: { reason: 'missing_file_scopes' } };
  }

  const stepId = payload.stepId ?? `commit_${Date.now()}`;
  const headBefore = await git.getHeadSha();

  const applyingCp: Checkpoint = {
    ...checkpoint,
    stepId,
    timestamps: {
      ...checkpoint.timestamps,
      applyReadyAt: isoNow(),
    },
    state: CheckpointState.Applying,
    nextAction: NextAction.Apply,
  };
  try { await journal.upsertCheckpoint(applyingCp); } catch {}

  try {
    const applyRes = await (fileEditTool as any).execute({
      patchPlan,
      dryRun: false,
      diagnostics: false,
    });

    if (!applyRes?.success) {
      if (headBefore?.ok && headBefore.sha) {
        try { await git.resetHard(headBefore.sha); } catch {}
      }
      const failureSummary = applyRes?.error?.message ?? 'apply_failed';
      const cpFailed: Checkpoint = {
        ...applyingCp,
        state: CheckpointState.Failed,
        nextAction: NextAction.Fail,
        timestamps: { ...applyingCp.timestamps, failedAt: isoNow() },
        diagnostics: { ...(applyingCp.diagnostics ?? {}), failureSummary, lastStackTrace: null },
      };
      try { await journal.upsertCheckpoint(cpFailed); } catch {}
      return { ok: false, commitSha: null, respondedAt: isoNow(), payload: { reason: failureSummary } };
    }

    const checks = await runVerificationChecks();
    if (!checks.allGreen) {
      if (headBefore?.ok && headBefore.sha) {
        try { await git.resetHard(headBefore.sha); } catch {}
      }
      const failureSummary = 'post_apply_checks_failed';
      const failedDiagnostics = buildDiagnosticsFromChecks(checks, {
        failureSummary,
        lastStackTrace: null,
      });
      const cpFailed: Checkpoint = {
        ...applyingCp,
        state: CheckpointState.Failed,
        nextAction: NextAction.Fail,
        timestamps: { ...applyingCp.timestamps, failedAt: isoNow() },
        diagnostics: failedDiagnostics,
      };
      try { await journal.upsertCheckpoint(cpFailed); } catch {}
      return {
        ok: false,
        commitSha: null,
        respondedAt: isoNow(),
        payload: { reason: failureSummary, diagnostics: failedDiagnostics, verification: checks },
      };
    }

    const commitCfg = getConfig().orchestrator?.resilience?.commit ?? {};
    let commitSha: string | null = null;
    let prUrl: string | null = null;
    let commitMessage: string | null = null;

    if (commitCfg.enabled) {
      const workingBranch = (payload.metadata as any)?.workingBranch || `${commitCfg.branchPrefix || 'orch/'}${payload.taskId}`;
      const baseBranch = (payload.metadata as any)?.baseBranch;
      try { await git.ensureBranch(workingBranch, baseBranch); } catch {}

      commitMessage = buildCommitMessage(payload.runId, payload.taskId, stepId, intent, patchPlanHash || 'nohash');
      const commit = await git.commitAll(commitMessage);
      if (commit?.ok) {
        commitSha = commit.sha ?? null;
        if (commitCfg.enablePR) {
          try {
            const pr = await openOrUpdatePR({ branch: workingBranch, title: commitMessage, body: 'Automated change by orchestrator' });
            if (pr.supported && pr.ok) prUrl = pr.url ?? null;
          } catch {}
        }
      }
    }

    const committedDiagnostics = buildDiagnosticsFromChecks(checks, {
      failureSummary: null,
      lastStackTrace: null,
    });

    const committedCp: Checkpoint = {
      ...applyingCp,
      state: CheckpointState.Committed,
      nextAction: NextAction.Handoff,
      timestamps: { ...applyingCp.timestamps, committedAt: isoNow() },
      diagnostics: committedDiagnostics,
      artifacts: {
        commitSha,
        commitMessage,
        prUrl,
        diffFiles: checkpoint.artifacts?.diffFiles,
      },
    };
    try { await journal.upsertCheckpoint(committedCp); } catch {}

    const respondedCommit = isoNow();
    return {
      ok: true,
      commitSha,
      respondedAt: respondedCommit,
      payload: {
        diagnostics: committedDiagnostics,
        verification: checks,
        patchPlanHash,
        commitMessage,
        prUrl,
      },
    };
  } catch (err: any) {
    if (headBefore?.ok && headBefore.sha) {
      try { await git.resetHard(headBefore.sha); } catch {}
    }
    const failureSummary = err?.message ?? 'commit_failed';
    const cpFailed: Checkpoint = {
      ...checkpoint,
      state: CheckpointState.Failed,
      nextAction: NextAction.Fail,
      timestamps: { ...checkpoint.timestamps, failedAt: isoNow() },
      diagnostics: { ...(checkpoint.diagnostics ?? {}), failureSummary, lastStackTrace: err?.stack ?? null },
    };
    try { await journal.upsertCheckpoint(cpFailed); } catch {}
    return {
      ok: false,
      commitSha: null,
      respondedAt: isoNow(),
      payload: { reason: failureSummary },
    };
  }
}

export async function codeGenerationAbort(payload: AbortMessage): Promise<AbortResponse> {
  const journal = new RunJournal();
  const respondedAt = isoNow();
  try {
    const checkpoint = await journal.getCurrent(payload.runId, payload.taskId);
    if (checkpoint) {
      const aborted: Checkpoint = {
        ...checkpoint,
        state: CheckpointState.Blocked,
        nextAction: NextAction.Retry,
        timestamps: { ...checkpoint.timestamps, failedAt: isoNow() },
        diagnostics: {
          ...(checkpoint.diagnostics ?? {}),
          failureSummary: 'transaction_aborted',
          lastStackTrace: null,
        },
      };
      await journal.upsertCheckpoint(aborted);
    }
  } catch {}

  return { ok: true, respondedAt };
}

export async function executeTwoPhaseCommit(options: TwoPhaseCommitOptions): Promise<{
  ok: boolean;
  state: 'plan_ready' | 'committed' | 'failed' | 'skipped';
  patchPlanHash?: string;
  previewDiff?: string;
  commitSha?: string | null;
  prUrl?: string | null;
  diagnostics?: CheckpointDiagnostics;
  verification?: VerificationChecksSummary;
}> {
  const cfg = getConfig();
  const runId = options.runId || (getContextValue<string>('runId') || `run_${Date.now()}`);
  const taskId = options.taskId || (getContextValue<string>('taskId') || `task_${Date.now()}`);
  const intent = options.intent || 'implement';

  const journal = new RunJournal();

  const planRes = await (fileEditTool as any).execute({
    patchPlan: options.patchPlan,
    dryRun: true,
    diagnostics: false
  });
  if (!planRes?.success) {
    const failureSummary = `plan_dry_run_failed: ${planRes?.error || ''}`;
   const cpPlan: Checkpoint = {
      runId, taskId,
      stepId: `plan_${Date.now()}`,
      agent: 'vercel-codegen',
      timestamps: { createdAt: isoNow(), planReadyAt: isoNow(), failedAt: isoNow() },
      state: CheckpointState.Failed,
      nextAction: NextAction.Fail,
      scope: { fileScopes: extractFileScopesFromPlan(options.patchPlan), fileLocks: [], concurrencyGroupId: undefined },
      plan: { patchPlan: options.patchPlan, previewDiff: planRes?.previewDiff, patchPlanHash: planRes?.patchPlanHash },
      diagnostics: { failureSummary },
    } as any;
    try { await journal.upsertCheckpoint(cpPlan); } catch {}
    return { ok: false, state: 'failed', patchPlanHash: planRes?.patchPlanHash, previewDiff: planRes?.previewDiff, diagnostics: { failureSummary } };
  }

  const patchPlanHash = planRes.patchPlanHash as string | undefined;
  const fileScopes = extractFileScopesFromPlan(options.patchPlan);
  const cpPlanReady: Checkpoint = {
    runId, taskId,
    stepId: `plan_${Date.now()}`,
    agent: 'vercel-codegen',
    timestamps: { createdAt: isoNow(), planReadyAt: isoNow() },
    state: CheckpointState.PlanReady,
    nextAction: NextAction.Apply,
    scope: { fileScopes, fileLocks: [], concurrencyGroupId: undefined },
    plan: { patchPlan: options.patchPlan, previewDiff: planRes.previewDiff, patchPlanHash },
  } as any;
  try { await journal.upsertCheckpoint(cpPlanReady); } catch {}

  const checks1 = await runVerificationChecks();
  const diagnosticsAfterChecks1 = buildDiagnosticsFromChecks(checks1, {
    failureSummary: checks1.allGreen ? null : 'pre-apply checks failed',
    lastStackTrace: null,
  });
  const cpAfterChecks1: Checkpoint = {
    ...cpPlanReady,
    timestamps: { ...cpPlanReady.timestamps },
    diagnostics: diagnosticsAfterChecks1,
    state: checks1.allGreen ? CheckpointState.PlanReady : CheckpointState.Failed,
    nextAction: checks1.allGreen ? NextAction.Apply : NextAction.Fail,
  };
  try { await journal.upsertCheckpoint(cpAfterChecks1); } catch {}
  if (!checks1.allGreen) {
    return {
      ok: false, state: 'failed', patchPlanHash, previewDiff: planRes.previewDiff,
      diagnostics: diagnosticsAfterChecks1,
      verification: checks1,
    };
  }

  const current = await journal.getCurrent(runId, taskId);
  if (current?.state === CheckpointState.Committed && current?.plan?.patchPlanHash && current.plan.patchPlanHash === patchPlanHash) {
    return { ok: true, state: 'skipped', patchPlanHash, previewDiff: planRes.previewDiff, commitSha: current.artifacts?.commitSha ?? null, prUrl: current.artifacts?.prUrl ?? null };
  }

  const headBefore = await git.getHeadSha();
  const cpApplyPending: Checkpoint = {
    runId, taskId,
    stepId: `apply_${Date.now()}`,
    agent: 'vercel-codegen',
    timestamps: { createdAt: isoNow(), applyReadyAt: isoNow() },
    state: CheckpointState.ApplyPending,
    nextAction: NextAction.Apply,
    scope: { fileScopes, fileLocks: [], concurrencyGroupId: undefined },
    plan: { patchPlan: options.patchPlan, previewDiff: planRes.previewDiff, patchPlanHash },
    vcs: { headCommit: headBefore.ok ? headBefore.sha : undefined },
  } as any;
  try { await journal.upsertCheckpoint(cpApplyPending); } catch {}

  const cpApplying: Checkpoint = { ...cpApplyPending, state: CheckpointState.Applying };
  try { await journal.upsertCheckpoint(cpApplying); } catch {}

  const applyRes = await (fileEditTool as any).execute({
    patchPlan: options.patchPlan,
    dryRun: false,
    diagnostics: false
  });
  if (!applyRes?.success) {
    if (headBefore.ok) { try { await git.resetHard(headBefore.sha!); } catch {} }
    const failureSummary = `apply_failed: ${applyRes?.error || ''}`;
    const cpFailed: Checkpoint = { ...cpApplying, state: CheckpointState.Failed, nextAction: NextAction.Fail, diagnostics: { ...cpApplying.diagnostics, failureSummary } };
    try { await journal.upsertCheckpoint(cpFailed); } catch {}
    return { ok: false, state: 'failed', patchPlanHash, diagnostics: { failureSummary } };
  }

  const checks2 = await runVerificationChecks();
  if (!checks2.allGreen) {
    if (headBefore.ok) { try { await git.resetHard(headBefore.sha!); } catch {} }
    const failureSummary = 'post-apply checks failed';
    const failedDiagnostics = buildDiagnosticsFromChecks(checks2, {
      failureSummary,
      lastStackTrace: null,
    });
    const cpFailed: Checkpoint = {
      ...cpApplying,
      state: CheckpointState.Failed,
      nextAction: NextAction.Fail,
      diagnostics: failedDiagnostics,
    };
    try { await journal.upsertCheckpoint(cpFailed); } catch {}
    return { ok: false, state: 'failed', patchPlanHash, diagnostics: failedDiagnostics, verification: checks2 };
  }

  let commitSha: string | undefined;
  let prUrl: string | null | undefined = null;
  const commitCfg = cfg.orchestrator?.resilience?.commit ?? {};
  if (commitCfg.enabled) {
    const branch = options.workingBranch || `${commitCfg.branchPrefix || 'orch/'}${taskId}`;
    await git.ensureBranch(branch, options.baseBranch);
    const message = buildCommitMessage(runId, taskId, cpApplying.stepId, options.intent, patchPlanHash || 'nohash');
    const commit = await git.commitAll(message);
    if (commit.ok) commitSha = commit.sha;
    if (commitCfg.enablePR) {
      const pr = await openOrUpdatePR({ branch, title: message, body: 'Automated change by orchestrator' });
      if (pr.supported && pr.ok) prUrl = pr.url ?? null;
    }
  }

  const committedDiagnostics = buildDiagnosticsFromChecks(checks2, {
    failureSummary: null,
    lastStackTrace: null,
  });
  const cpCommitted: Checkpoint = {
    ...cpApplying,
    state: CheckpointState.Committed,
    nextAction: NextAction.Handoff,
    timestamps: { ...cpApplying.timestamps, committedAt: isoNow() },
    diagnostics: committedDiagnostics,
    artifacts: { commitSha: commitSha ?? null, commitMessage: commitSha ? buildCommitMessage(runId, taskId, cpApplying.stepId, options.intent, patchPlanHash || 'nohash') : null, prUrl: prUrl ?? null },
  };
  try { await journal.upsertCheckpoint(cpCommitted); } catch {}

  return {
    ok: true,
    state: 'committed',
    patchPlanHash,
    previewDiff: planRes.previewDiff,
    commitSha: commitSha ?? null,
    prUrl: prUrl ?? null,
    diagnostics: committedDiagnostics,
    verification: checks2,
  };
}

export const vercelCodeGenerationAgent = {
  name: 'Code Generation Agent',
  model: 'gpt-5-codex',
  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => (typeof i === 'string' ? i : JSON.stringify(i))).join('\n')
      : input;
    if (!options?.model) {
      throw new Error('vercelCodeGenerationAgent requires a provided model. Use Dante orchestrator to obtain and pass the model explicitly.');
    }
    return executeCodeGeneration(inputStr, options as any);
  },
};
