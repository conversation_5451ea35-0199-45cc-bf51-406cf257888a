/**
 * Research Agent - Vercel AI SDK Implementation
 */

import { generateText, streamText, tool, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { config } from '../../utils/config';
import { webSearchTool } from '../../tools/webSearch';
import { enhancedWebSearchTool } from '../../tools/enhancedWebSearch';
import { webFetchTool } from '../../tools/webFetch';
import { grepCodeTool } from '../../tools/grepCode';
import { readFileTool, readFilesTool, setWorkingDirectoryTool, getWorkingDirectoryTool } from '../../tools/fileOperations';
import { createSearchPlanTool, summarizeSearchResultsTool } from '../../tools/searchSummarizationTools';
import { withRsRetryNonStreaming } from '../../services/responsesAPIClient';
import { pdfExtractTool } from '../../tools/pdfExtract';

export interface ResearchOptions {
  stream?: boolean;
  maxSteps?: number;
  temperature?: number;
  useGemini?: boolean;
  context?: any;
  onStepFinish?: (step: any) => void;
}

/**
 * Execute research task with Vercel AI SDK
 */
export async function executeResearch(
  input: string,
  options: ResearchOptions = {}
): Promise<any> {
  // Prefer Gemini if configured; allow explicit override via options.useGemini
  const preferGemini = !!(config.gemini?.apiKey || process.env.GOOGLE_GENERATIVE_AI_API_KEY);
  const useGemini = options.useGemini ?? preferGemini;
  const model = useGemini ? google('gemini-2.5-flash') : openai('gpt-5');

  // Structured answer tool for typed research output
  const answerTool = tool({
    description: 'Provide the final research output in a structured format.',
    inputSchema: z.object({
      summary: z.string().describe('Concise summary of findings'),
      keyFindings: z.array(z.string()).optional().describe('Important findings'),
      sources: z
        .array(
          z.object({
            title: z.string().optional(),
            url: z.string().url().optional(),
            note: z.string().optional(),
          })
        )
        .optional()
        .describe('Cited sources or references'),
      insights: z.array(z.string()).optional().describe('Notable insights or patterns'),
      nextSteps: z.array(z.string()).optional().describe('Recommended follow-up actions'),
    }) as any,
    // no execute; tool call terminates the agent with structured output
  }) as any;

  const commonConfig = {
    model,
    system: `You are Dante's Research Specialist, an expert at gathering, analyzing, and synthesizing information.

Your capabilities include:
- Comprehensive web research using multiple search engines
- Data analysis and pattern recognition
- Fact-checking and verification
- Information synthesis and report generation
- Source citation and credibility assessment

Research Process:
1. Understand the research question or topic
2. Plan your research strategy
3. If the question is about this repository or its code, use internal code tools (grep_code, read_file, read_files) to search the local codebase first. Prefer precise answers with file paths.
   - For questions like "which file manages memory?" search for keywords (e.g., MemoryManager, IMemoryManager) and return the most relevant file path(s) with a short justification.
   - After locating candidate files, read the file or specific regions to verify before answering.
   - Before using local tools, call get_working_directory to confirm you are within the project path provided in context. If not, call set_working_directory immediately to align it.
4. Otherwise, use web search tools to gather information from multiple sources
5. Analyze and cross-reference sources for credibility and relevance
6. **CRITICAL**: After gathering sufficient information (typically 3-6 high-quality sources), immediately call the 'answer' tool to provide your final structured response
7. Synthesize findings into clear, actionable insights
8. Provide citations and assess source credibility

**IMPORTANT**: You must call the 'answer' tool to terminate your research. Do not make endless tool calls without providing a final answer.

Guidelines for when to call the 'answer' tool:
- After 3-6 web_search calls with good results
- When you have enough information to provide a comprehensive answer
- When additional searches would provide diminishing returns
- Before reaching the maximum step limit

Always prioritize accuracy and provide well-researched information with proper context.
When delivering the final response, use the answer tool to provide a structured summary with sources.`,
    prompt: input,
    tools: {
      // Local codebase tools
      grep_code: grepCodeTool,
      read_file: readFileTool,
      read_files: readFilesTool,
      set_working_directory: setWorkingDirectoryTool,
      get_working_directory: getWorkingDirectoryTool,
      create_search_plan: createSearchPlanTool,
      summarize_search_results: summarizeSearchResultsTool,
      // Local PDF extraction tool
      pdf_extract: pdfExtractTool,
      // Web research tools
      web_search: webSearchTool,
      enhanced_web_search: enhancedWebSearchTool,
      web_fetch: webFetchTool,
      // Structured answer
      answer: answerTool,
      ...(useGemini ? {} : {  // Gemini has built-in web search
           url_context: google.tools.urlContext({}) as any,
      })
    },
    stopWhen: stepCountIs(options.maxSteps ?? 20),
    toolChoice: 'required' as any,
    onStepFinish: options.onStepFinish,
    context: options.context,
    providerOptions: {
      openai: {
        reasoningEffort: 'medium',
        reasoningSummary: 'auto'
      },
      google: {
        thinkingConfig: {
          thinkingBudget: 8192,
          includeThoughts: true,
        }
      }
    }
  };

  if (options.stream) {
    return await streamText(commonConfig);
  } else {
    const result = await withRsRetryNonStreaming({
      modelId: (commonConfig as any)?.model?.id ?? undefined,
      input,
      execute: () => generateText(commonConfig)
    });
    // Extract structured answer if present
    let structured: any = undefined;
    try {
      const call = (result as any).toolCalls?.find((c: any) => (c.name || c.toolName) === 'answer')
        || (result as any).toolCalls?.[0];
      structured = call?.args ?? call?.input ?? undefined;
    } catch {}
    const render = (s: any): string => {
      if (!s) return '';
      const out: string[] = [];
      if (s.summary) out.push(String(s.summary));
      const list = (title: string, arr?: any[]) => {
        if (Array.isArray(arr) && arr.length) {
          out.push(`${title}:`);
          out.push(...arr.map((v: any) => `- ${typeof v === 'string' ? v : JSON.stringify(v)}`));
        }
      };
      list('Key Findings', s.keyFindings);
      if (Array.isArray(s.sources) && s.sources.length) {
        out.push('Sources:');
        out.push(...s.sources.map((src: any) => `- ${src.title ? src.title + ' — ' : ''}${src.url ?? ''}${src.note ? ' (' + src.note + ')' : ''}`));
      }
      list('Insights', s.insights);
      list('Next Steps', s.nextSteps);
      return out.join('\n');
    };
    const finalText = (result.text && String(result.text).trim().length > 0)
      ? result.text
      : render(structured);
    return {
      text: finalText,
      toolCalls: (result as any).toolCalls,
      usage: result.usage,
      steps: (result as any).steps,
      structuredResearch: structured,
    };
  }
}

// Legacy compatibility wrapper
export const vercelResearchAgent = {
  name: 'Research Agent',
  model: 'gpt-5',
  description: 'Expert at gathering information, analyzing data, and conducting research',

  async execute(input: string | any[], options: any = {}) {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    return await executeResearch(inputStr, options);
  }
};
