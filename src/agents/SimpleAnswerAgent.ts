/**
 * SimpleAnswerAgent
 * Handles simple, stateless queries with low latency and no tool use.
 */

import { generateText } from 'ai';
import { google } from '@ai-sdk/google';
import { openai } from '@ai-sdk/openai';
import { config } from '../utils/config';
import { geminiClient, GeminiClient } from '../utils/geminiClient';

export class SimpleAnswerAgent {
  /**
   * Provide a direct, concise answer to a simple query.
   * - Prefers Gemini Flash for speed/cost.
   * - Falls back to OpenAI mini if Gemini isn't available.
   */
  public static async answer(query: string): Promise<string> {
    const system = 'You are a concise assistant. Answer directly and briefly. Do not use tools or multi-step reasoning.';
    const model = config.gemini.defaultModel || 'gemini-2.5-flash';

    // 1) Primary: use our Gemini client (handles env wiring, rate limits, retries)
    try {
      const res = await geminiClient.createChatCompletion({
        model,
        messages: [
          { role: 'system', content: system },
          { role: 'user', content: query },
        ],
          max_tokens: 256,
        thinking: GeminiClient.createThinkingConfig('simple', false),
      });

      const text = res.choices?.[0]?.message?.content?.trim();
      if (text) return text;
    } catch {
      // continue to fallback
    }

    // 2) Fallback A: call AI SDK Google provider directly (ensure key present)
    try {
      if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY && config.gemini.apiKey) {
        process.env.GOOGLE_GENERATIVE_AI_API_KEY = config.gemini.apiKey;
      }
      const { text } = await generateText({
        model: google(model),
        system,
        prompt: query,
          maxOutputTokens: 256,
      });
      if (text?.trim()) return text.trim();
    } catch {
      // continue to next fallback
    }

    // 3) Fallback B: OpenAI mini (ensure key present)
    try {
      if (!process.env.OPENAI_API_KEY && config.openai.apiKey) {
        process.env.OPENAI_API_KEY = config.openai.apiKey;
      }
      const { text } = await generateText({
        model: openai('gpt-5-mini'),
        system,
        prompt: query,
          maxOutputTokens: 256,
      });
      if (text?.trim()) return text.trim();
    } catch {
      // no-op
    }

    return 'Sorry, I cannot answer that right now.';
  }
}

/**
 * Backward-compatible function export matching architecture.md interface.
 */
export async function answer(query: string): Promise<string> {
  return SimpleAnswerAgent.answer(query);
}
