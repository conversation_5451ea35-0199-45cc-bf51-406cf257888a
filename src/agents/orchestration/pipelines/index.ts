/**
 * Pluggable per-category pipelines with lightweight validation hooks and optional
 * auto-generated ProgressEvents. Pure utilities with no side effects.
 *
 * These pipelines are intended to be used by the engine/scheduler.
 */

import {
  WorkUnit,
  WorkCategory,
  ProgressEvent,
  ProgressEventType,
  ArtifactRef,
  OrchestrationRequest,
  ArtifactType,
} from '../types';

/**
 * Context provided to pipeline handlers.
 */
export interface PipelineContext {
  /** Orchestration run identifier. */
  runId: string;
  /** Original orchestration request, if available. */
  request?: OrchestrationRequest;
}

/**
 * Pluggable pipeline for a given WorkCategory.
 * All hooks are optional except validations().
 */
export interface CategoryPipeline {
  /** Human-readable pipeline name. */
  name: string;

  /**
   * Returns a list of validation checks that should be applied to this work unit.
   * These are declarative; caller is responsible for executing checks.
   */
  validations(workUnit: WorkUnit): string[];

  /**
   * Hook invoked when execution for this unit begins.
   * May emit zero or more ProgressEvents (e.g., STARTED).
   */
  onStart?(workUnit: WorkUnit, ctx: PipelineContext): ProgressEvent[] | Promise<ProgressEvent[]>;

  /**
   * Hook invoked when an artifact is produced during execution.
   * May emit zero or more ProgressEvents (e.g., REVIEW_REQUEST).
   */
  onArtifact?(
    workUnit: WorkUnit,
    ctx: PipelineContext,
    artifact: ArtifactRef
  ): ProgressEvent[] | Promise<ProgressEvent[]>;

  /**
   * Hook invoked when execution completes for this unit.
   * May emit zero or more ProgressEvents (e.g., DONE).
   */
  onComplete?(workUnit: WorkUnit, ctx: PipelineContext): ProgressEvent[] | Promise<ProgressEvent[]>;
}

/** Mapping from WorkCategory -> CategoryPipeline. */
export type Pipelines = Record<WorkCategory, CategoryPipeline>;

/**
 * Helper to create a minimal ProgressEvent with deterministic structure.
 * Event ID format: "wu:{id}:{type}:{ts}"
 */
function evt(workUnit: WorkUnit, type: ProgressEventType, message?: string): ProgressEvent {
  const ts = Date.now();
  return {
    eventId: `wu:${workUnit.id}:${type}:${ts}`,
    type,
    workUnitId: workUnit.id,
    message,
    timestamp: ts,
  };
}

/**
 * Create default pipelines for coding, review, debug, and research categories.
 *
 * - Coding:
 *   - validations: diff/patch exists; tests exist/pass for scope; typecheck/build passes
 *   - onStart: emit planned-steps started
 *   - onArtifact: if patch/diff, emit review-request
 *   - onComplete: emit done
 * - Review:
 *   - validations: lint/type/security checks; tests pass for modified scope
 *   - onStart: emit started with checklist
 *   - onComplete: emit done
 * - Debug:
 *   - validations: reproduction exists; root cause identified; fix validated by tests
 *   - onStart/onComplete: started/done
 * - Research:
 *   - validations: concise notes with citations; answer feeds dependent units
 *   - onStart/onComplete: started/done
 */
export function createDefaultPipelines(): Pipelines {
  const coding: CategoryPipeline = {
    name: WorkCategory.Coding,
    validations: (_wu: WorkUnit) => [
      'Diff or patch exists for scoped changes',
      'Tests exist for scope and pass',
      'Typecheck/build or black (for python) passes',
    ],
    onStart: (wu, _ctx) => [
      evt(wu, ProgressEventType.Started, 'Coding started: plan-implement-validate'),
    ],
    onArtifact: (wu, _ctx, artifact) => {
      if (artifact?.type === ArtifactType.Patch || artifact?.type === ArtifactType.Diff) {
        return [evt(wu, ProgressEventType.ReviewRequest, 'Patch/diff ready for review')];
      }
      return [];
    },
    onComplete: (wu, _ctx) => [evt(wu, ProgressEventType.Done, 'Coding completed')],
  };

  const review: CategoryPipeline = {
    name: WorkCategory.Review,
    validations: (_wu: WorkUnit) => [
      'Lint/type/security checks pass',
      'Tests pass for modified scope',
    ],
    onStart: (wu, _ctx) => [
      evt(wu, ProgressEventType.Started, 'Review started: checklist applied'),
    ],
    onComplete: (wu, _ctx) => [evt(wu, ProgressEventType.Done, 'Review completed')],
  };

  const debug: CategoryPipeline = {
    name: WorkCategory.Debug,
    validations: (_wu: WorkUnit) => [
      'Reproduction exists',
      'Root cause identified',
      'Fix validated by tests',
    ],
    onStart: (wu, _ctx) => [
      evt(wu, ProgressEventType.Started, 'Debug started: reproduce-diagnose-fix-verify'),
    ],
    onComplete: (wu, _ctx) => [evt(wu, ProgressEventType.Done, 'Debug completed')],
  };

  const research: CategoryPipeline = {
    name: WorkCategory.Research,
    validations: (_wu: WorkUnit) => [
      'Concise notes with citations',
      'Answer feeds dependent units',
    ],
    onStart: (wu, _ctx) => [
      evt(wu, ProgressEventType.Started, 'Research started: gather-summarize-cite'),
    ],
    onComplete: (wu, _ctx) => [evt(wu, ProgressEventType.Done, 'Research completed')],
  };

  return {
    [WorkCategory.Coding]: coding,
    [WorkCategory.Review]: review,
    [WorkCategory.Debug]: debug,
    [WorkCategory.Research]: research,
  };
}
