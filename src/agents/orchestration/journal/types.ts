import { hash as sha256 } from '../../../utils/crypto';

export type ISO8601 = string;

export type RunId = string;
export type TaskId = string;
export type StepId = string;

/** Checkpoint lifecycle state */
export enum CheckpointState {
  Queued = 'queued',
  Running = 'running',
  PlanReady = 'plan_ready',
  ApplyPending = 'apply_pending',
  Applying = 'applying',
  Committed = 'committed',
  Failed = 'failed',
  Blocked = 'blocked',
  Review = 'review',
}

/** Next action instruction for receiver/resumer */
export enum NextAction {
  Plan = 'plan',
  Apply = 'apply',
  Review = 'review',
  Handoff = 'handoff',
  Retry = 'retry',
  Fail = 'fail',
}

export interface CheckpointTimestamps {
  createdAt: ISO8601;
  startedAt?: ISO8601 | null;
  planReadyAt?: ISO8601 | null;
  applyReadyAt?: ISO8601 | null;
  committedAt?: ISO8601 | null;
  failedAt?: ISO8601 | null;
}

export interface CheckpointVCS {
  workingBranch?: string;
  baseBranch?: string;
  baseCommit?: string;
  headCommit?: string;
  mergeBase?: string;
}

export interface CheckpointPlan {
  patchPlan?: any; // canonical JSON (we provide canonicalization helpers)
  previewDiff?: string;
  patchPlanHash?: string; // SHA-256 hex of canonicalized patchPlan
  idempotencyKeys?: string[]; // future use
  intent?: string;
}

export interface CheckpointScope {
  fileScopes?: string[]; // normalized paths
  fileLocks?: string[]; // placeholder for future file locks
  concurrencyGroupId?: string;
}

export interface CheckpointDiagnosticResult {
  status: 'success' | 'failure';
  logs?: string;
}

export interface CheckpointDiagnostics {
  build?: CheckpointDiagnosticResult;
  typecheck?: CheckpointDiagnosticResult;
  test?: CheckpointDiagnosticResult;
  lint?: CheckpointDiagnosticResult;
  checks?: Record<string, CheckpointDiagnosticResult>;
  failureSummary?: string | null;
  lastStackTrace?: string | null;
}

export interface CheckpointArtifacts {
  diffFiles?: string[];
  logFiles?: string[];
  prUrl?: string | null;
  commitSha?: string | null;
  commitMessage?: string | null;
}

export interface CheckpointRetryState {
  rsRetryCount?: number;
  backoff?: {
    strategy?: 'exponential';
    initialDelay?: number;
    maxRetries?: number;
  };
  lastRetryAt?: ISO8601 | null;
  /** Alias to support external specs/tests referring to lastAttemptTs */
  lastAttemptTs?: ISO8601 | null;
  /** Captured response headers from the last attempt (case-insensitive keys where possible) */
  lastResponseHeaders?: Record<string, string | null>;
  /** Stable idempotency key for the logical operation (model+promptHash+run/task/session) */
  operationKey?: string;
  /** Last error class string (e.g., APIError, NetworkError) */
  lastErrorClass?: string;
  /** Last error message (non-sensitive substring) */
  lastErrorMessage?: string;
  /** Last HTTP status encountered if available (e.g., 429) */
  lastStatus?: number;
}

export interface CheckpointLoopGuard {
  lastNTools?: string[];
  lastTransitions?: string[];
  pingPongSignatures?: string[];
}

export interface CheckpointHandoffDepth {
  current: number;
  max: number;
}

export interface CoolingWindows {
  duplicateContent?: number; // seconds
}

/**
 * Primary checkpoint record
 */
export interface Checkpoint {
  // Identity
  runId: RunId;
  taskId: TaskId;
  stepId: StepId;
  parentStepId?: StepId | null;
  agent: string;
  timestamps: CheckpointTimestamps;

  // Flow/state
  state: CheckpointState;
  lastCompletedStep?: string | null;
  nextAction: NextAction;
  acceptanceCriteria?: string[]; // from WorkUnit when applicable
  handoffDepth?: CheckpointHandoffDepth;
  /** @deprecated Use handoffDepth.max instead. Kept for compatibility. */
  maxDepth?: number; // redundancy with handoffDepth.max, kept for compatibility
  coolingWindows?: CoolingWindows;

  // Optional goal/objective for fingerprinting (from OrchestrationRequest)
  goal?: string;

  // VCS
  vcs?: CheckpointVCS;

  // Plan
  plan?: CheckpointPlan;

  // Scope and concurrency
  scope?: CheckpointScope;

  // Diagnostics, artifacts
  diagnostics?: CheckpointDiagnostics;
  artifacts?: CheckpointArtifacts;

  // Retry/loop guard
  retryState?: CheckpointRetryState;
  loopGuard?: CheckpointLoopGuard;

  // Content fingerprint (derived)
  contentFingerprint?: string;
}

/** Structured validation error for incomplete checkpoints */
export class CheckpointIncompleteError extends Error {
  code = 'CheckpointIncomplete';
  details: { missing: string[] };
  constructor(message: string, missing: string[]) {
    super(message);
    this.name = 'CheckpointIncompleteError';
    this.details = { missing };
  }
}

/** Minimal validation per spec: required fields to proceed on handoff/resume */
export function validateCheckpointRequired(cp: Partial<Checkpoint>): asserts cp is Checkpoint {
  const missing: string[] = [];
  const must = ['runId', 'taskId', 'stepId', 'agent', 'state', 'nextAction'] as const;
  for (const key of must) {
    if ((cp as any)[key] === undefined || (cp as any)[key] === null || (cp as any)[key] === '') {
      missing.push(key);
    }
  }
  if (!cp.timestamps?.createdAt) missing.push('timestamps.createdAt');
  if (!cp.scope?.fileScopes || cp.scope.fileScopes.length === 0) missing.push('scope.fileScopes');
  if (missing.length) {
    throw new CheckpointIncompleteError('Checkpoint is missing required fields', missing);
  }
}

/** Utility to stamp ISO8601 now */
export function isoNow(): ISO8601 {
  return new Date().toISOString();
}

/** Stable hash helper */
export function sha256Hex(input: string): string {
  return sha256(input);
}
// ===== 2PC Protocol Events =====

export type ProtocolDecision = 'commit' | 'abort';

export enum ProtocolEventType {
  PrepareSent = 'prepare_sent',
  PrepareAck = 'prepare_ack',
  DecisionPersisted = 'decision_persisted',
  CommitSent = 'commit_sent',
  CommitAck = 'commit_ack',
  AbortSent = 'abort_sent',
  AbortAck = 'abort_ack',
  RecoveryMarker = 'recovery_marker',
  LockSnapshot = 'lock_snapshot',

  // Join phase events for concurrency/merge
  JoinStarted = 'join_started',
  JoinMerged = 'join_merged',
  JoinConflict = 'join_conflict',
  JoinResolved = 'join_resolved',
  JoinAborted = 'join_aborted',
}

export interface ProtocolRetryState {
  attempt?: number;
  lastErrorClass?: string;
  lastErrorMessage?: string;
  lastStatus?: number;
}

export interface ProtocolEventBase {
  runId: RunId;
  taskId: TaskId;
  txnId: string;
  seqNo: number;
  /** ISO timestamp is preferred for readability in journal */
  timestamp: ISO8601;
  type: ProtocolEventType;
  retryState?: ProtocolRetryState;
}

export interface PrepareSentEvent extends ProtocolEventBase {
  type: ProtocolEventType.PrepareSent;
  participantId: string;
  resourceKeys?: string[];
  payload?: Record<string, any>;
}

export type PrepareVote = 'yes' | 'no' | 'unknown';

export interface PrepareAckEvent extends ProtocolEventBase {
  type: ProtocolEventType.PrepareAck;
  participantId: string;
  vote: PrepareVote;
  reason?: string;
  respondedAt?: ISO8601;
  payload?: Record<string, any>;
}

export interface DecisionPersistedEvent extends ProtocolEventBase {
  type: ProtocolEventType.DecisionPersisted;
  decision: ProtocolDecision;
  resourceKeys?: string[];
  participants?: string[];
  reason?: string;
  payload?: Record<string, any>;
}

export interface CommitSentEvent extends ProtocolEventBase {
  type: ProtocolEventType.CommitSent;
  participantId: string;
  payload?: Record<string, any>;
}

export interface CommitAckEvent extends ProtocolEventBase {
  type: ProtocolEventType.CommitAck;
  participantId: string;
  ok: boolean;
  commitSha?: string | null;
  respondedAt?: ISO8601;
  payload?: Record<string, any>;
}

export interface AbortSentEvent extends ProtocolEventBase {
  type: ProtocolEventType.AbortSent;
  participantId: string;
  payload?: Record<string, any>;
}

export interface AbortAckEvent extends ProtocolEventBase {
  type: ProtocolEventType.AbortAck;
  participantId: string;
  ok: boolean;
  reason?: string;
  respondedAt?: ISO8601;
  payload?: Record<string, any>;
}

export interface RecoveryMarkerEvent extends ProtocolEventBase {
  type: ProtocolEventType.RecoveryMarker;
  note?: string;
  state?: string;
  payload?: Record<string, any>;
}

/** Serialized snapshot of the lock table for observability/recovery assists */
export interface LockSnapshotEvent extends ProtocolEventBase {
  type: ProtocolEventType.LockSnapshot;
  snapshot: {
    held: Record<string, {
      txnId: string | null;
      expiresAt: number | null;
      priorityTs: number | null;
      leaseMs: number | null;
      queue: Array<{ txnId: string; priorityTs: number; enqueuedAt: number; seq: number }>;
    }>;
  };
}

export interface JoinStartedEvent extends ProtocolEventBase {
  type: ProtocolEventType.JoinStarted;
  joinId: string;
  partitionIds: string[];
  /** Optional: keys merged across partitions (for observability) */
  resourceKeys?: string[];
}

export interface JoinMergedEvent extends ProtocolEventBase {
  type: ProtocolEventType.JoinMerged;
  joinId: string;
  partitionIds: string[];
  mergedPatchPlanHash?: string;
  conflictCount?: number;
  payload?: Record<string, any>;
}

export interface JoinConflictEvent extends ProtocolEventBase {
  type: ProtocolEventType.JoinConflict;
  joinId: string;
  partitionIds: string[];
  conflictCount: number;
  payload?: {
    conflicts?: Array<{
      filePath: string;
      ranges?: Array<{ start: number; end: number }>;
      participants?: string[];
    }>;
  };
}

export interface JoinResolvedEvent extends ProtocolEventBase {
  type: ProtocolEventType.JoinResolved;
  joinId: string;
  partitionIds: string[];
  resolved: boolean;
  mergedPatchPlanHash?: string;
  payload?: Record<string, any>;
}

export interface JoinAbortedEvent extends ProtocolEventBase {
  type: ProtocolEventType.JoinAborted;
  joinId: string;
  partitionIds: string[];
  reason?: string;
  payload?: Record<string, any>;
}

export type ProtocolEvent =
  | PrepareSentEvent
  | PrepareAckEvent
  | DecisionPersistedEvent
  | CommitSentEvent
  | CommitAckEvent
  | AbortSentEvent
  | AbortAckEvent
  | RecoveryMarkerEvent
  | LockSnapshotEvent
  | JoinStartedEvent
  | JoinMergedEvent
  | JoinConflictEvent
  | JoinResolvedEvent
  | JoinAbortedEvent;
