import path from 'node:path';
import { hash as sha256 } from '../../../utils/crypto';
import { Checkpoint } from './types';

/**
 * Normalize a filesystem path:
 * - Convert backslashes to slashes
 * - Use posix normalization
 * - Trim whitespace
 */
export function normalizePath(p: string): string {
  if (!p) return '';
  const s = p.replace(/\\/g, '/').trim();
  // If s is absolute posix path, keep; otherwise normalize relative
  return path.posix.normalize(s);
}

/**
 * Recursively sort object keys and produce a deterministic, compact JSON string.
 * - Omits undefined values
 * - Preserves array order
 * - Sorts object keys lexicographically
 */
export function canonicalizeJSON(value: unknown): string {
  const seen = new WeakSet();

  function sortify(v: any): any {
    if (v === undefined) return undefined;
    if (v === null) return null;
    const t = typeof v;
    if (t === 'number' || t === 'boolean' || t === 'string') return v;
    if (t === 'bigint') return v.toString();
    if (t === 'symbol' || t === 'function') return undefined;

    if (Array.isArray(v)) {
      const arr = v.map((x) => sortify(x)).filter((x) => x !== undefined);
      return arr;
    }

    if (t === 'object') {
      if (seen.has(v)) {
        // Avoid cycles by stringifying to null
        return null;
      }
      seen.add(v);
      const keys = Object.keys(v as object).sort();
      const out: Record<string, any> = {};
      for (const k of keys) {
        const sv = sortify((v as any)[k]);
        if (sv !== undefined) out[k] = sv;
      }
      return out;
    }

    return undefined;
  }

  const normalized = sortify(value);
  return JSON.stringify(normalized);
}

/**
 * Canonicalize a patch plan. Apply deterministic JSON and normalize any file path arrays
 * when encountered under conventional keys.
 */
export function canonicalizePlan(plan: unknown): string {
  // Best-effort normalization for common path arrays inside plans
  function normalizePathsInObject(v: any): any {
    if (v === null || v === undefined) return v;
    if (Array.isArray(v)) return v.map(normalizePathsInObject);
    if (typeof v !== 'object') return v;

    const out: Record<string, any> = Array.isArray(v) ? [] : {};
    for (const k of Object.keys(v)) {
      const val = (v as any)[k];
      if (Array.isArray(val) && (k === 'files' || k === 'paths' || k === 'fileScopes')) {
        out[k] = [...val].map((p) => (typeof p === 'string' ? normalizePath(p) : p)).sort();
      } else {
        out[k] = normalizePathsInObject(val);
      }
    }
    return out;
  }

  const normalized = normalizePathsInObject(plan);
  return canonicalizeJSON(normalized);
}

export function hashPlan(plan: unknown): string {
  return sha256(canonicalizePlan(plan));
}

/**
 * Derive a content fingerprint from a checkpoint using:
 * - normalized goal (lowercased/trimmed)
 * - normalized and sorted fileScopes
 * - patchPlanHash (if present)
 */
export function deriveContentFingerprint(checkpoint: Partial<Checkpoint>): string {
  const goal = (checkpoint.goal ?? '').toString().trim().toLowerCase();
  const scopes = (checkpoint.scope?.fileScopes ?? [])
    .filter((p) => !!p)
    .map((p) => normalizePath(String(p)))
    .sort();
  const planHash =
    checkpoint.plan?.patchPlanHash ||
    (checkpoint.plan?.patchPlan !== undefined ? hashPlan(checkpoint.plan.patchPlan) : '');

  const payload = JSON.stringify({
    goal,
    scopes,
    planHash,
  });

  return sha256(payload);
}
