import { promises as fs } from 'node:fs';
import * as path from 'node:path';
import { getConfig } from '../../../utils/config';
import {
  Checkpoint,
  CheckpointIncompleteError,
  validateCheckpointRequired,
  ProtocolDecision,
  ProtocolEvent,
  ProtocolEventType,
  PrepareVote,
} from './types';
import { canonicalizePlan, hashPlan, deriveContentFingerprint } from './canonicalize';

type JournalRecordType = 'checkpoint' | 'protocol';

interface JournalRecord {
  runId: string;
  ts: number;
  type: JournalRecordType;
  data: any;
}

interface RunSnapshot {
  version: number;
  updatedAt: number;
  fileSize: number;
  checkpoints: Record<string, Checkpoint>;
}

class AsyncQueue {
  private tail: Promise<void> = Promise.resolve();
  runExclusive<T>(fn: () => Promise<T>): Promise<T> {
    const run = this.tail.then(fn, fn);
    this.tail = run.then(
      () => undefined,
      () => undefined,
    );
    return run;
  }
  waitForIdle(): Promise<void> {
    return this.tail;
  }
}

/**
 * Append-only JSONL-backed journal with per-(runId, taskId) "current checkpoint" index.
 * Files: <journalDir>/{runId}.jsonl
 */
export class RunJournal {
  private readonly dir: string;
  private readonly snapshotDir: string;
  private readonly fileMutexes = new Map<string, AsyncQueue>(); // filePath => mutex
  private readonly keyMutexes = new Map<string, AsyncQueue>(); // runId|taskId => mutex

  // lazy loaded indexes
  private loadedRuns = new Set<string>(); // runIds that have been scanned
  private currentIndex = new Map<string, Map<string, Checkpoint>>(); // runId => Map(taskId, cp)
  private logSizeCache = new Map<string, number>(); // runId => last known file size in bytes

  constructor(journalDir?: string) {
    const cfg = getConfig();
    this.dir = path.resolve(process.cwd(), journalDir ?? cfg.orchestrator.resilience.journalDir ?? 'data/run-journal');
    this.snapshotDir = path.join(this.dir, 'snapshots');
  }

  /** Upsert a checkpoint: validates schema, canonicalizes/derives hashes, appends to JSONL, updates current view. */
  async upsertCheckpoint(cp: Checkpoint): Promise<void> {
    // Validate presence of required fields (throws structured error)
    validateCheckpointRequired(cp);

    // Fill derived fields
    if (cp.plan?.patchPlan !== undefined && !cp.plan.patchPlanHash) {
      // Canonicalize and hash deterministically
      const _canon = canonicalizePlan(cp.plan.patchPlan);
      cp.plan.patchPlanHash = hashPlan(cp.plan.patchPlan);
    }

    // Derive content fingerprint
    try {
      cp.contentFingerprint = deriveContentFingerprint(cp);
    } catch {
      // ignore fingerprint errors; not fatal to persistence
    }

    const rec: JournalRecord = {
      runId: cp.runId,
      ts: Date.now(),
      type: 'checkpoint',
      data: cp,
    };

    await this.ensureDirs();
    const filePath = this.filePathFor(cp.runId);
    // Ensure file exists
    try {
      await fs.access(filePath);
    } catch {
      await fs.appendFile(filePath, '');
    }

    const fileMx = this.getFileMutex(filePath);
    await fileMx.runExclusive(async () => {
      const payload = JSON.stringify(rec) + '\n';
      await fs.appendFile(filePath, payload);
      // Update in-memory current view
      const index = this.getRunIndex(cp.runId);
      index.set(cp.taskId, deepClone(cp));
      try {
        const stats = await fs.stat(filePath);
        this.logSizeCache.set(cp.runId, stats.size);
        await this.writeSnapshot(cp.runId, stats.size);
      } catch {
        // ignore snapshot persistence failures; journal durability still guaranteed by append
      }
    });
  }

  /** Get the current checkpoint for (runId, taskId), or null if none. */
  async getCurrent(runId: string, taskId: string): Promise<Checkpoint | null> {
    await this.ensureLoaded(runId);
    const runIndex = this.currentIndex.get(runId);
    const cp = runIndex?.get(taskId) ?? null;
    return cp ? deepClone(cp) : null;
  }

  /** List all checkpoints for a runId (ordered by appearance in the file). */
  async listByRun(runId: string): Promise<Checkpoint[]> {
    const filePath = this.filePathFor(runId);
    const exists = await fileExists(filePath);
    if (!exists) return [];
    const raw = await fs.readFile(filePath, 'utf8');
    const out: Checkpoint[] = [];
    for (const line of raw.split('\n')) {
      const t = line.trim();
      if (!t) continue;
      try {
        const rec = JSON.parse(t) as JournalRecord;
        if (rec.runId !== runId) continue;
        if (rec.type !== 'checkpoint') continue;
        out.push(rec.data as Checkpoint);
      } catch {
        // skip malformed lines
      }
    }
    return out;
  }

  /** Append a 2PC protocol event to the journal (persist-before-notify helper). */
  async appendProtocolEvent(evt: ProtocolEvent): Promise<void> {
    const rec: { runId: string; ts: number; type: 'protocol'; data: ProtocolEvent } = {
      runId: evt.runId,
      ts: Date.now(),
      type: 'protocol',
      data: evt,
    };

    await this.ensureDirs();
    const filePath = this.filePathFor(evt.runId);
    try {
      await fs.access(filePath);
    } catch {
      await fs.appendFile(filePath, '');
    }

    const fileMx = this.getFileMutex(filePath);
    await fileMx.runExclusive(async () => {
      await fs.appendFile(filePath, JSON.stringify(rec) + '\n');
    });
  }

  async recordPrepareSent(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    resourceKeys?: string[];
    payload?: Record<string, any>;
    retryState?: ProtocolEvent['retryState'];
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.PrepareSent,
      participantId: params.participantId,
      resourceKeys: params.resourceKeys,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordPrepareAck(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    vote: PrepareVote;
    reason?: string;
    respondedAt?: string;
    payload?: Record<string, any>;
    retryState?: ProtocolEvent['retryState'];
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.respondedAt ?? new Date().toISOString(),
      type: ProtocolEventType.PrepareAck,
      participantId: params.participantId,
      vote: params.vote,
      reason: params.reason,
      respondedAt: params.respondedAt,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordDecisionPersisted(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    decision: ProtocolDecision;
    resourceKeys?: string[];
    participants?: string[];
    reason?: string;
    payload?: Record<string, any>;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.DecisionPersisted,
      decision: params.decision,
      resourceKeys: params.resourceKeys,
      participants: params.participants,
      reason: params.reason,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordCommitSent(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    payload?: Record<string, any>;
    timestamp?: string;
    retryState?: ProtocolEvent['retryState'];
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.CommitSent,
      participantId: params.participantId,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordCommitAck(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    ok: boolean;
    commitSha?: string | null;
    respondedAt?: string;
    payload?: Record<string, any>;
    retryState?: ProtocolEvent['retryState'];
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.respondedAt ?? new Date().toISOString(),
      type: ProtocolEventType.CommitAck,
      participantId: params.participantId,
      ok: params.ok,
      commitSha: params.commitSha,
      respondedAt: params.respondedAt,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordAbortSent(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    payload?: Record<string, any>;
    timestamp?: string;
    retryState?: ProtocolEvent['retryState'];
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.AbortSent,
      participantId: params.participantId,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordAbortAck(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    participantId: string;
    ok: boolean;
    reason?: string;
    respondedAt?: string;
    payload?: Record<string, any>;
    retryState?: ProtocolEvent['retryState'];
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.respondedAt ?? new Date().toISOString(),
      type: ProtocolEventType.AbortAck,
      participantId: params.participantId,
      ok: params.ok,
      reason: params.reason,
      respondedAt: params.respondedAt,
      payload: params.payload,
      retryState: params.retryState,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordJoinStarted(params: {
    runId: string;
    taskId: string;
    txnId: string;
    joinId: string;
    partitionIds: string[];
    seqNo: number;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinStarted,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordJoinMerged(params: {
    runId: string;
    taskId: string;
    txnId: string;
    joinId: string;
    partitionIds: string[];
    mergedPatchPlanHash?: string | null;
    conflictCount?: number;
    seqNo: number;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinMerged,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      mergedPatchPlanHash: params.mergedPatchPlanHash ?? undefined,
      conflictCount: params.conflictCount,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordJoinConflict(params: {
    runId: string;
    taskId: string;
    txnId: string;
    joinId: string;
    partitionIds: string[];
    conflictCount: number;
    hashes?: string[];
    seqNo: number;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinConflict,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      conflictCount: params.conflictCount,
      payload: params.hashes ? { hashes: params.hashes } : undefined,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordJoinResolved(params: {
    runId: string;
    taskId: string;
    txnId: string;
    joinId: string;
    partitionIds: string[];
    resolved: boolean;
    mergedPatchPlanHash?: string | null;
    notes?: string[];
    seqNo: number;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinResolved,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      resolved: params.resolved,
      mergedPatchPlanHash: params.mergedPatchPlanHash ?? undefined,
      payload: params.notes ? { notes: params.notes } : undefined,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordJoinAborted(params: {
    runId: string;
    taskId: string;
    txnId: string;
    joinId: string;
    partitionIds: string[];
    reason?: string;
    seqNo: number;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinAborted,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      reason: params.reason,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async recordRecoveryMarker(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    note?: string;
    state?: string;
    payload?: Record<string, any>;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.RecoveryMarker,
      note: params.note,
      state: params.state,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  // ===== Join-phase helpers =====
  async appendJoinStarted(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    joinId: string;
    partitionIds: string[];
    resourceKeys?: string[];
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinStarted,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      resourceKeys: params.resourceKeys,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async appendJoinMerged(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    joinId: string;
    partitionIds: string[];
    mergedPatchPlanHash?: string;
    conflictCount?: number;
    payload?: Record<string, any>;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinMerged,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      mergedPatchPlanHash: params.mergedPatchPlanHash,
      conflictCount: params.conflictCount,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async appendJoinConflict(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    joinId: string;
    partitionIds: string[];
    conflictCount: number;
    payload?: {
      conflicts?: Array<{
        filePath: string;
        ranges?: Array<{ start: number; end: number }>;
        participants?: string[];
      }>;
    };
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinConflict,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      conflictCount: params.conflictCount,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async appendJoinResolved(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    joinId: string;
    partitionIds: string[];
    resolved: boolean;
    mergedPatchPlanHash?: string;
    payload?: Record<string, any>;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinResolved,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      resolved: params.resolved,
      mergedPatchPlanHash: params.mergedPatchPlanHash,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  async appendJoinAborted(params: {
    runId: string;
    taskId: string;
    txnId: string;
    seqNo: number;
    joinId: string;
    partitionIds: string[];
    reason?: string;
    payload?: Record<string, any>;
    timestamp?: string;
  }): Promise<void> {
    const evt: ProtocolEvent = {
      runId: params.runId,
      taskId: params.taskId,
      txnId: params.txnId,
      seqNo: params.seqNo,
      timestamp: params.timestamp ?? new Date().toISOString(),
      type: ProtocolEventType.JoinAborted,
      joinId: params.joinId,
      partitionIds: params.partitionIds,
      reason: params.reason,
      payload: params.payload,
    } as any;
    await this.appendProtocolEvent(evt);
  }

   /** List protocol events for a run, optionally filtered by taskId and/or txnId. */
   async listProtocolEvents(runId: string, filter?: { taskId?: string; txnId?: string }): Promise<ProtocolEvent[]> {
    const filePath = this.filePathFor(runId);
    const exists = await fileExists(filePath);
    if (!exists) return [];
    const raw = await fs.readFile(filePath, 'utf8');
    const out: ProtocolEvent[] = [];
    for (const line of raw.split('\n')) {
      const t = line.trim();
      if (!t) continue;
      try {
        const rec = JSON.parse(t) as JournalRecord;
        if (!rec || rec.runId !== runId) continue;
        if (rec.type !== 'protocol') continue;
        const evt = rec.data as ProtocolEvent;
        if (filter?.taskId && evt.taskId !== filter.taskId) continue;
        if (filter?.txnId && (evt as any).txnId !== filter.txnId) continue;
        out.push(evt);
      } catch {
        // skip malformed lines
      }
    }
    // Stable ordering by seqNo then timestamp if present
    out.sort((a: any, b: any) => {
      const sa = (a.seqNo ?? 0) - (b.seqNo ?? 0);
      if (sa !== 0) return sa;
      const ta = new Date(a.timestamp || 0).getTime();
      const tb = new Date(b.timestamp || 0).getTime();
      return ta - tb;
    });
    return out;
  }

  /** Convenience: append a lock-table snapshot event */
  async appendLockSnapshot(
    runId: string,
    taskId: string,
    txnId: string,
    seqNo: number,
    snapshot: {
      held: Record<string, {
        txnId: string | null;
        expiresAt: number | null;
        priorityTs: number | null;
        leaseMs: number | null;
        queue: Array<{ txnId: string; priorityTs: number; enqueuedAt: number; seq: number }>;
      }>;
    },
  ): Promise<void> {
    const evt: ProtocolEvent = {
      runId,
      taskId,
      txnId,
      seqNo,
      timestamp: new Date().toISOString(),
      type: ProtocolEventType.LockSnapshot,
      snapshot,
    } as any;
    await this.appendProtocolEvent(evt);
  }

  /**
   * Ensure atomic write ordering for a (runId, taskId) by executing the callback under a per-key mutex.
   * Intended for higher-level orchestration that needs to perform read-modify-write safely.
   */
  async withTransaction<T>(runId: string, taskId: string, fn: () => Promise<T>): Promise<T> {
    const mx = this.getKeyMutex(this.key(runId, taskId));
    return mx.runExclusive(fn);
  }

  // ===== Internals =====

  private async ensureLoaded(runId: string): Promise<void> {
    if (this.loadedRuns.has(runId)) return;

    const filePath = this.filePathFor(runId);
    const exists = await fileExists(filePath);
    if (!exists) {
      // mark as loaded even if absent to avoid repeated IO
      this.loadedRuns.add(runId);
      return;
    }

    const index = this.getRunIndex(runId);
    index.clear();

    let snapshotFileSize = 0;
    const snapshot = await this.loadSnapshot(runId);
    if (snapshot) {
      snapshotFileSize = snapshot.fileSize;
      for (const [taskId, cp] of Object.entries(snapshot.checkpoints ?? {})) {
        index.set(taskId, deepClone(cp));
      }
    }

    const { content, fileSize } = await this.readFromOffset(filePath, snapshotFileSize);
    if (content) {
      for (const line of content.split('\n')) {
        const t = line.trim();
        if (!t) continue;
        let rec: JournalRecord | null = null;
        try {
          rec = JSON.parse(t) as JournalRecord;
        } catch {
          continue;
        }
        if (!rec || rec.runId !== runId) continue;
        if (rec.type !== 'checkpoint') continue;
        const cp = rec.data as Checkpoint;
        index.set(cp.taskId, deepClone(cp));
      }
    }

    this.loadedRuns.add(runId);
    this.logSizeCache.set(runId, fileSize);

    if (!snapshot || fileSize !== snapshot.fileSize) {
      try {
        await this.writeSnapshot(runId, fileSize);
      } catch {
        // best effort snapshot persistence; ignore failure to avoid impacting load
      }
    }
  }

  private async ensureDirs(): Promise<void> {
    await Promise.all([fs.mkdir(this.dir, { recursive: true }), fs.mkdir(this.snapshotDir, { recursive: true })]);
  }

  private key(runId: string, taskId: string): string {
    return `${runId}::${taskId}`;
  }

  private filePathFor(runId: string): string {
    return path.join(this.dir, `${runId}.jsonl`);
  }

  private snapshotPathFor(runId: string): string {
    return path.join(this.snapshotDir, `${runId}.snapshot.json`);
  }

  private getFileMutex(filePath: string): AsyncQueue {
    let m = this.fileMutexes.get(filePath);
    if (!m) {
      m = new AsyncQueue();
      this.fileMutexes.set(filePath, m);
    }
    return m;
  }

  private getKeyMutex(key: string): AsyncQueue {
    let m = this.keyMutexes.get(key);
    if (!m) {
      m = new AsyncQueue();
      this.keyMutexes.set(key, m);
    }
    return m;
  }

  private getRunIndex(runId: string): Map<string, Checkpoint> {
    let runMap = this.currentIndex.get(runId);
    if (!runMap) {
      runMap = new Map();
      this.currentIndex.set(runId, runMap);
    }
    return runMap;
  }

  private async loadSnapshot(runId: string): Promise<RunSnapshot | null> {
    const snapshotPath = this.snapshotPathFor(runId);
    try {
      const raw = await fs.readFile(snapshotPath, 'utf8');
      const parsed = JSON.parse(raw) as RunSnapshot;
      if (!parsed || typeof parsed !== 'object') return null;
      if (parsed.version !== 1 || typeof parsed.fileSize !== 'number') return null;
      return parsed;
    } catch {
      return null;
    }
  }

  private async writeSnapshot(runId: string, fileSize: number): Promise<void> {
    const snapshotPath = this.snapshotPathFor(runId);
    await fs.mkdir(this.snapshotDir, { recursive: true });
    const index = this.currentIndex.get(runId);
    const checkpoints: Record<string, Checkpoint> = {};
    if (index) {
      for (const [taskId, cp] of index.entries()) {
        checkpoints[taskId] = deepClone(cp);
      }
    }
    const snapshot: RunSnapshot = {
      version: 1,
      updatedAt: Date.now(),
      fileSize,
      checkpoints,
    };
    await fs.writeFile(snapshotPath, JSON.stringify(snapshot));
  }

  private async readFromOffset(filePath: string, offset: number): Promise<{ content: string; fileSize: number }> {
    const handle = await fs.open(filePath, 'r');
    try {
      const stats = await handle.stat();
      const fileSize = stats.size;
      const start = Math.max(0, Math.min(offset, fileSize));
      if (start >= fileSize) {
        return { content: '', fileSize };
      }
      const length = fileSize - start;
      const buffer = Buffer.alloc(length);
      await handle.read(buffer, 0, length, start);
      return { content: buffer.toString('utf8'), fileSize };
    } finally {
      await handle.close();
    }
  }
}

// ===== Utils =====

function deepClone<T>(obj: T): T {
  return obj == null ? obj : JSON.parse(JSON.stringify(obj));
}

async function fileExists(p: string): Promise<boolean> {
  try {
    await fs.access(p);
    return true;
  } catch {
    return false;
  }
}

export default RunJournal;
