import { promises as fs } from 'fs';
import * as path from 'path';
import { hash as sha256 } from '../../../utils/crypto';
import type { RunId, Ledger, PartitionPlan, Assignment, ProgressEvent, ArtifactRef, WorkUnitId, ProgressEventId } from '../types';

type RecordType = 'plan' | 'assignment' | 'event' | 'artifact' | 'summary' | 'rollup' | 'tracker';

/**
 * One JSONL record stored per line.
 * Record format:
 * { runId, ts, type: 'plan'|'assignment'|'event'|'artifact'|'summary'|'rollup', data }
 */
interface JSONLRecord {
  runId: RunId;
  ts: number;
  type: RecordType;
  // For 'artifact', data can be ArtifactRef or ArtifactRef[]
  data:
    | PartitionPlan
    | Assignment
    | ProgressEvent
    | ArtifactRef
    | ArtifactRef[]
    | { scope: string; summary: string }
    | { key: string; value: any }
    | Tracker;
}

class AsyncQueue {
  private tail: Promise<void> = Promise.resolve();
  runExclusive<T>(fn: () => Promise<T>): Promise<T> {
    const run = this.tail.then(fn, fn);
    this.tail = run.then(() => undefined, () => undefined);
    return run;
  }
  waitForIdle(): Promise<void> {
    return this.tail;
  }
}

const mutexes = new Map<string, AsyncQueue>();
function getMutex(key: string): AsyncQueue {
  let m = mutexes.get(key);
  if (!m) {
    m = new AsyncQueue();
    mutexes.set(key, m);
  }
  return m;
}
function deepClone<T>(obj: T): T {
  return obj == null ? obj : JSON.parse(JSON.stringify(obj));
}

/** Tracker model for deduplication keyed by (taskId, contentFingerprint). */
export interface Tracker {
  id: string;
  taskId: string;
  contentFingerprint: string;
  state: 'canonical' | 'superseded' | 'in_progress' | 'done' | 'failed';
  canonicalId?: string;
  createdAt: number;
  updatedAt: number;
  lastSeenAt: number;
}

async function fileExists(p: string): Promise<boolean> {
  try {
    await fs.access(p);
    return true;
  } catch {
    return false;
  }
}

/**
 * Durable JSONL-backed ledger for fine-grained orchestration.
 * Use JsonLedger.open(runId) to construct.
 */
export class JsonLedger {
  private readonly runId: RunId;
  private readonly filePath: string;
  private readonly dirPath: string;
  private readonly mutex: AsyncQueue;

  // In-memory state and indexes
  private ledger: Ledger;
  private seenEventKeys: Set<string>;
  private seenAssignmentIds: Set<string>;
  private artifactIndex: Map<string, ArtifactRef>;

  // Tracker indexes (not part of Ledger type yet)
  private trackers: Tracker[];
  private trackerByKey: Map<string, Tracker>; // key = taskId|contentFingerprint (canonical)

  private constructor(runId: RunId, filePath: string, dirPath: string) {
    this.runId = runId;
    this.filePath = filePath;
    this.dirPath = dirPath;
    this.mutex = getMutex(filePath);
    this.ledger = {
      runId,
      assignments: [],
      events: [],
      artifacts: [],
      lastUpdated: 0,
    };
    this.seenEventKeys = new Set();
    this.seenAssignmentIds = new Set();
    this.artifactIndex = new Map();

    this.trackers = [];
    this.trackerByKey = new Map();
  }

  /**
   * Open or create a ledger for the given runId. Loads any existing state.
   */
  static async open(runId: RunId): Promise<JsonLedger> {
    const dirPath = path.resolve(process.cwd(), 'data', 'ledger');
    await fs.mkdir(dirPath, { recursive: true });
    const filePath = path.join(dirPath, `${runId}.jsonl`);
    const instance = new JsonLedger(runId, filePath, dirPath);
    if (await fileExists(filePath)) {
      await instance.load();
    } else {
      // Ensure empty file exists for append-only behavior.
      await fs.appendFile(filePath, '');
    }
    return instance;
  }

  /**
   * Append a plan snapshot. Latest snapshot wins on load.
   */
  async recordPlan(plan: PartitionPlan): Promise<void> {
    if (!plan) throw new Error('recordPlan: plan must be provided');
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'plan',
      data: plan,
    };
    await this.mutex.runExclusive(async () => {
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      // In-memory update
      this.ledger.plan = plan;
      this.ledger.lastUpdated = rec.ts;
    });
  }

  /**
   * Append an assignment if not seen before (idempotent by assignment.id).
   * Returns { deduped: true } when an existing assignment id is seen.
   */
  async recordAssignment(a: Assignment): Promise<{ deduped: boolean }> {
    if (!a || !a.id) throw new Error('recordAssignment: assignment.id is required');
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'assignment',
      data: a,
    };
    let deduped = false;
    await this.mutex.runExclusive(async () => {
      if (this.seenAssignmentIds.has(a.id)) {
        deduped = true;
        return;
      }
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      this.seenAssignmentIds.add(a.id);
      this.ledger.assignments.push(a);
      this.ledger.lastUpdated = rec.ts;
    });
    return { deduped };
  }

  /**
   * Append an event if not seen before (idempotent by `${workUnitId}:${eventId}`).
   * Returns { deduped: true } when a duplicate is detected.
   */
  async recordEvent(e: ProgressEvent): Promise<{ deduped: boolean }> {
    if (!e) throw new Error('recordEvent: event is required');
    if (!e.workUnitId) throw new Error('recordEvent: workUnitId is required');
    if (!e.eventId) throw new Error('recordEvent: eventId is required');
    const key = JsonLedger.eventKey(e.workUnitId, e.eventId);
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'event',
      data: e,
    };
    let deduped = false;
    await this.mutex.runExclusive(async () => {
      if (this.seenEventKeys.has(key)) {
        deduped = true;
        return;
      }
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      this.seenEventKeys.add(key);
      this.ledger.events.push(e);
      this.ledger.lastUpdated = rec.ts;
    });
    return { deduped };
  }

  /**
   * Append artifact references. No on-disk dedupe; in-memory keeps last by id.
   */
  async recordArtifacts(list: ArtifactRef[]): Promise<void> {
    if (!Array.isArray(list)) throw new Error('recordArtifacts: list must be an array');
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'artifact',
      data: list,
    };
    await this.mutex.runExclusive(async () => {
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      for (const a of list) {
        if (!a || !a.id) continue;
        this.artifactIndex.set(a.id, a);
      }
      this.ledger.artifacts = Array.from(this.artifactIndex.values());
      this.ledger.lastUpdated = rec.ts;
    });
  }

  /**
   * Append a scoped summary.
   */
  async recordSummary(scope: string, summary: string): Promise<void> {
    if (!scope) throw new Error('recordSummary: scope is required');
    if (typeof summary !== 'string') throw new Error('recordSummary: summary must be a string');
    const payload = { scope, summary };
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'summary',
      data: payload,
    };
    await this.mutex.runExclusive(async () => {
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      if (!this.ledger.summaries) this.ledger.summaries = [];
      this.ledger.summaries.push(payload);
      this.ledger.lastUpdated = rec.ts;
    });
  }

  /**
   * Append an aggregated rollup entry.
   */
  async recordRollup(key: string, data: any): Promise<void> {
    if (!key) throw new Error('recordRollup: key is required');
    const payload = { key, value: data };
    const rec: JSONLRecord = {
      runId: this.runId,
      ts: Date.now(),
      type: 'rollup',
      data: payload,
    };
    await this.mutex.runExclusive(async () => {
      await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
      if (!this.ledger.rollups) this.ledger.rollups = {};
      this.ledger.rollups[key] = data;
      this.ledger.lastUpdated = rec.ts;
    });
  }

  /**
   * Load and reconstruct the latest Ledger state from disk (JSONL).
   * Latest plan wins; assignments/events are unique; artifacts accumulated.
   */
  async load(): Promise<Ledger> {
    const nextLedger: Ledger = {
      runId: this.runId,
      assignments: [],
      events: [],
      artifacts: [],
      lastUpdated: 0,
    };
    const assignmentIds = new Set<string>();
    const eventKeys = new Set<string>();
    const artifactIndex = new Map<string, ArtifactRef>();
    const trackerByKey = new Map<string, Tracker>();
    const trackers: Tracker[] = [];

    if (!(await fileExists(this.filePath))) {
      // Initialize in-memory if missing file
      this.ledger = nextLedger;
      this.seenAssignmentIds = assignmentIds;
      this.seenEventKeys = eventKeys;
      this.artifactIndex = artifactIndex;
      // Reset trackers cache too
      this.trackers = [];
      this.trackerByKey = new Map();
      return deepClone(this.ledger);
    }

    const raw = await fs.readFile(this.filePath, 'utf8');
    const lines = raw.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;
      let rec: JSONLRecord | null = null;
      try {
        rec = JSON.parse(trimmed) as JSONLRecord;
      } catch {
        // Skip malformed line
        continue;
      }
      if (!rec || rec.runId !== this.runId) continue;
      if (typeof rec.ts === 'number' && rec.ts > nextLedger.lastUpdated) {
        nextLedger.lastUpdated = rec.ts;
      }
      switch (rec.type) {
        case 'plan': {
          nextLedger.plan = rec.data as PartitionPlan;
          break;
        }
        case 'assignment': {
          const a = rec.data as Assignment;
          if (a && a.id && !assignmentIds.has(a.id)) {
            assignmentIds.add(a.id);
            nextLedger.assignments.push(a);
          }
          break;
        }
        case 'event': {
          const e = rec.data as ProgressEvent;
          if (e && e.workUnitId && e.eventId) {
            const key = JsonLedger.eventKey(e.workUnitId, e.eventId);
            if (!eventKeys.has(key)) {
              eventKeys.add(key);
              nextLedger.events.push(e);
            }
          }
          break;
        }
        case 'artifact': {
          const d = rec.data as ArtifactRef | ArtifactRef[];
          const list = Array.isArray(d) ? d : [d];
          for (const art of list) {
            if (art && art.id) {
              artifactIndex.set(art.id, art);
            }
          }
          break;
        }
        case 'summary': {
          const s = rec.data as { scope: string; summary: string };
          if (s && s.scope) {
            if (!nextLedger.summaries) nextLedger.summaries = [];
            nextLedger.summaries.push(s);
          }
          break;
        }
        case 'rollup': {
          const r = rec.data as { key: string; value: any };
          if (r && r.key) {
            if (!nextLedger.rollups) nextLedger.rollups = {};
            nextLedger.rollups[r.key] = r.value;
          }
          break;
        }
        case 'tracker': {
          const t = rec.data as Tracker;
          if (t && t.taskId && t.contentFingerprint) {
            const key = `${t.taskId}|${t.contentFingerprint}`;
            // Latest wins (by file order/ts)
            trackerByKey.set(key, t);
            trackers.push(t);
          }
          break;
        }
      }
    }
    nextLedger.artifacts = Array.from(artifactIndex.values());

    // Replace tracker caches
    this.trackers = trackers;
    this.trackerByKey = trackerByKey;

    // Replace in-memory state
    this.ledger = nextLedger;
    this.seenAssignmentIds = assignmentIds;
    this.seenEventKeys = eventKeys;
    this.artifactIndex = artifactIndex;
    return deepClone(this.ledger);
  }

  /**
   * Return a deep-cloned snapshot of the in-memory ledger (fast path).
   */
  getLedgerInMemory(): Ledger {
    return deepClone(this.ledger);
  }

  /**
   * Wait for all pending writes to finish.
   */
  async flush(): Promise<void> {
    await this.mutex.waitForIdle();
  }

  /**
   * Flush and release resources.
   */
  async close(): Promise<void> {
    await this.flush();
  }
   // ===== Tracker (deduplication) API =====

   /**
    * Record or deduplicate a tracker by (taskId, contentFingerprint).
    * - If canonical exists and within cooling window, returns { deduped:true, coolingRejected:true }.
    * - If canonical exists and outside cooling window, updates lastSeenAt and returns { deduped:true }.
    * - If none exists, creates canonical tracker and returns { deduped:false }.
    * - Also records a 'superseded' tracker when coolingRejected to provide an audit trail.
    */
   async recordOrDedupTracker(
     taskId: string,
     contentFingerprint: string,
     opts?: { coolingWindowMs?: number },
   ): Promise<{ deduped: boolean; coolingRejected?: boolean; canonical: Tracker; superseded?: Tracker }> {
     if (!taskId || !contentFingerprint) {
       throw new Error('recordOrDedupTracker: taskId and contentFingerprint are required');
     }
     const coolingWindowMs = Math.max(0, opts?.coolingWindowMs ?? 10_000);
     const nowTs = Date.now();
     const key = `${taskId}|${contentFingerprint}`;

     // Fast path: in-memory canonical
     const existing = this.trackerByKey.get(key);

     if (existing) {
       const delta = nowTs - (existing.lastSeenAt ?? existing.createdAt);
       if (delta < coolingWindowMs) {
         // Cooling window reject; append a superseded record for traceability
         const superseded: Tracker = {
           id: `trk_sup_${sha256(`${taskId}|${contentFingerprint}|${nowTs}`)}`,
           taskId,
           contentFingerprint,
           state: 'superseded',
           canonicalId: existing.id,
           createdAt: nowTs,
           updatedAt: nowTs,
           lastSeenAt: nowTs,
         };
         const rec: JSONLRecord = { runId: this.runId, ts: nowTs, type: 'tracker', data: superseded };
         await this.mutex.runExclusive(async () => {
           await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
         });
         return { deduped: true, coolingRejected: true, canonical: existing, superseded };
       }
       // Outside cooling window: update lastSeenAt on canonical
       const updated = { ...existing, updatedAt: nowTs, lastSeenAt: nowTs };
       const rec: JSONLRecord = { runId: this.runId, ts: nowTs, type: 'tracker', data: updated };
       await this.mutex.runExclusive(async () => {
         await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
         this.trackerByKey.set(key, updated);
         this.trackers.push(updated);
       });
       return { deduped: true, canonical: updated };
     }

     // Create new canonical tracker
     const canonical: Tracker = {
       id: `trk_${sha256(`${taskId}|${contentFingerprint}|${nowTs}`)}`,
       taskId,
       contentFingerprint,
       state: 'canonical',
       createdAt: nowTs,
       updatedAt: nowTs,
       lastSeenAt: nowTs,
     };
     const rec: JSONLRecord = { runId: this.runId, ts: nowTs, type: 'tracker', data: canonical };
     await this.mutex.runExclusive(async () => {
       await fs.appendFile(this.filePath, JSON.stringify(rec) + '\n');
       this.trackerByKey.set(key, canonical);
       this.trackers.push(canonical);
     });
     return { deduped: false, canonical };
   }

   /** Return a shallow copy of current canonical trackers. */
   listTrackers(): Tracker[] {
     return this.trackers.slice();
   }
  private static eventKey(workUnitId: WorkUnitId, eventId: ProgressEventId): string {
    return `${workUnitId}:${eventId}`;
  }
}
