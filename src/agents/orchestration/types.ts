/**
 * Fine-grained orchestration core data model types and enums.
 * This module is self-contained and exports constructs shared across planning, scheduling,
 * assignment, context packaging, events, ledger, and metrics.
 */

// Enums

/**
 * High-level category of work a unit represents.
 */
export enum WorkCategory {
  Coding = 'coding',
  Debug = 'debug',
  Research = 'research',
  Review = 'review',
}

/**
 * The primary intent of a work unit.
 */
export enum Intent {
  Implement = 'implement',
  Fix = 'fix',
  Refactor = 'refactor',
  Test = 'test',
  Research = 'research',
  Review = 'review',
}

/**
 * Event types emitted during work progress.
 */
export enum ProgressEventType {
  Started = 'started',
  Heartbeat = 'heartbeat',
  Blocker = 'blocker',
  NeedsInfo = 'needs-info',
  Artifact = 'artifact',
  ReviewRequest = 'review-request',
  Done = 'done',
  Failed = 'failed',
  Cancelled = 'cancelled',
}

/**
 * Types of artifacts produced during orchestration.
 */
export enum ArtifactType {
  Patch = 'patch',
  Diff = 'diff',
  File = 'file',
  Test = 'test',
  Log = 'log',
  Doc = 'doc',
}

/**
 * Lifecycle status of a WorkUnit.
 */
export enum WorkUnitStatus {
  Pending = 'pending',
  InProgress = 'in_progress',
  Blocked = 'blocked',
  Review = 'review',
  Done = 'done',
  Failed = 'failed',
}

/**
 * Runtime status of an Assignment.
 */
export enum AssignmentStatus {
  Queued = 'queued',
  Assigned = 'assigned',
  Running = 'running',
  Blocked = 'blocked',
  Done = 'done',
  Failed = 'failed',
  Cancelled = 'cancelled',
  Timeout = 'timeout',
}

// ID aliases

/** Unique identifier for an orchestration run. */
export type RunId = string;

/** Unique identifier for a work unit. */
export type WorkUnitId = string;

/** Unique identifier for an agent. */
export type AgentId = string;

/** Unique identifier for an assignment. */
export type AssignmentId = string;

/** Unique identifier for an artifact. */
export type ArtifactId = string;

/** Unique identifier for a progress event. */
export type ProgressEventId = string;

// Interfaces

/**
 * Top-level request to orchestrate work toward an objective.
 */
export interface OrchestrationRequest {
  /** Immutable run identifier. */
  readonly id: RunId;
  /** The high-level user or system objective. */
  objective: string;
  /** Optional scope hints to constrain search space. */
  scopeHints?: {
    files?: string[];
    modules?: string[];
    paths?: string[];
    includeTests?: boolean;
    exclude?: string[];
  };
  /** Optional category toggles to bias planner. */
  categoryMix?: {
    coding?: boolean;
    debug?: boolean;
    research?: boolean;
    review?: boolean;
  };
  /** Resource and policy constraints. */
  constraints?: {
    tokenBudget?: number;
    timeBudgetMs?: number;
    costBudgetUSD?: number;
    concurrency?: number;
    maxLOCPerUnit?: number;
    maxFilesPerUnit?: number;
  };
  /** Arbitrary metadata for traceability. */
  meta?: Record<string, any>;
  /** Feature flags controlling orchestration behavior. */
  flags?: {
    fineGrainedOrchestration?: boolean;
  };
}

/**
 * A discrete unit of work to be planned, scheduled, and executed.
 */
export interface WorkUnit {
  /** Stable identifier for this work unit. */
  readonly id: WorkUnitId;
  /** Category of work. */
  category: WorkCategory;
  /** Code or resource scope relevant to the unit. */
  scope: {
    files?: string[];
    modules?: string[];
    components?: string[];
    paths?: string[];
  };
  /** The intent of the change. */
  intent: Intent;
  /** Optional human-readable description. */
  description?: string;
  /** Testable acceptance criteria. */
  acceptanceCriteria: string[];
  /** Dependencies that must complete first. */
  dependencies: WorkUnitId[];
  /** Higher number = higher priority. */
  priority: number;
  /** Optional estimated effort (abstract units). */
  estimatedEffort?: number;
  /** Risk assessment. */
  risk?: 'low' | 'medium' | 'high';
  /** Current status. */
  status: WorkUnitStatus;
  /** Creation timestamp (ms since epoch). */
  createdAt: number;
  /** Last update timestamp (ms since epoch). */
  updatedAt?: number;
}

/**
 * Partitioned plan of work units and their relationships for a run.
 */
export interface PartitionPlan {
  /** Plan identifier. */
  readonly id: string;
  /** The orchestration run this plan belongs to. */
  requestId: RunId;
  /** The set of units in this partition. */
  units: WorkUnit[];
  /** Optional adjacency list describing dependencies or ordering. */
  adjacency?: Record<WorkUnitId, WorkUnitId[]>;
  /** Creation timestamp (ms since epoch). */
  createdAt: number;
  /** Optional snapshot of relevant config at plan time. */
  configSnapshot?: Record<string, any>;
}

// ===== Two-Phase Commit Coordinator Types =====

export enum CoordinatorState {
  New = 'new',
  Planning = 'planning',
  Locking = 'locking',
  Prepared = 'prepared',
  PrepareTimeout = 'prepare_timeout',
  DecisionPersistedCommit = 'decision_persisted_commit',
  DecisionPersistedAbort = 'decision_persisted_abort',
  Committing = 'committing',
  Committed = 'committed',
  Aborting = 'aborting',
  Aborted = 'aborted',
  Recovering = 'recovering',
  Failed = 'failed',
}

export interface CoordinatorEnvelope {
  runId: RunId;
  taskId: string;
  txnId: string;
  seqNo: number;
  stepId: string;
  resourceKeys: string[];
  patchPlanHash?: string;
  idempotencyKey?: string;
  dedupKey?: string;
  traceparent?: string;
  tracestate?: string;
  checkpoint?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface PrepareMessage extends CoordinatorEnvelope {
  type: 'Prepare';
  dryRun?: boolean;
  payload?: Record<string, any>;
}

export interface PrepareResponse {
  vote: 'yes' | 'no' | 'unknown';
  reason?: string;
  payload?: Record<string, any>;
  respondedAt?: string;
}

export interface CommitMessage extends CoordinatorEnvelope {
  type: 'Commit';
  payload?: Record<string, any>;
}

export interface CommitResponse {
  ok: boolean;
  commitSha?: string | null;
  payload?: Record<string, any>;
  respondedAt?: string;
}

export interface AbortMessage extends CoordinatorEnvelope {
  type: 'Abort';
  payload?: Record<string, any>;
}

export interface AbortResponse {
  ok: boolean;
  reason?: string;
  payload?: Record<string, any>;
  respondedAt?: string;
}

export interface TwoPhaseParticipant {
  id: string;
  agentId: string;
  prepare: (msg: PrepareMessage) => Promise<PrepareResponse>;
  commit: (msg: CommitMessage) => Promise<CommitResponse>;
  abort: (msg: AbortMessage) => Promise<AbortResponse>;
}

export interface CoordinatorRuntimeConfig {
  prepareTimeoutMs: number;
  commitAckTimeoutMs: number;
  lockLeaseMs: number;
  lockRenewIntervalMs: number;
}

export interface CoordinatorTransactionContext {
  runId: RunId;
  taskId: string;
  txnId: string;
  stepId: string;
  participants: TwoPhaseParticipant[];
  resourceKeys: string[];
  patchPlanHash?: string;
  idempotencyKey?: string;
  dedupKey?: string;
  traceparent?: string;
  tracestate?: string;
  checkpoint?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface CoordinatorResult {
  state: CoordinatorState;
  decision: 'commit' | 'abort';
  prepareAcks: Record<string, PrepareResponse>;
  commitAcks: Record<string, CommitResponse>;
  abortAcks: Record<string, AbortResponse>;
  locksReleased: string[];
}

/**
 * Capabilities and limits of an agent eligible for assignments.
 */
export interface AgentProfile {
  /** Agent identifier. */
  readonly id: AgentId;
  /** Display name. */
  name: string;
  /** Freeform capability tags, e.g., 'typescript', 'jest'. */
  capabilities: string[];
  /** Maximum token context size supported. */
  maxTokenContext: number;
  /** Optional underlying model identifier. */
  model?: string;
  /** Cost service-level agreement. */
  costSLA?: {
    maxUSDPer1kTokens?: number;
  };
  /** Time service-level agreement. */
  timeSLA?: {
    p50Ms?: number;
    p95Ms?: number;
  };
  /** Historical performance hints. */
  historical?: {
    successRate?: number;
    avgThroughput?: number;
  };
  /** Maximum concurrent assignments. */
  concurrencyLimit: number;
  /** Preferred work categories. */
  categoryAffinity?: WorkCategory[];
}

/**
 * Packaged context (artifacts, file slices, snippets) provided to an assignment.
 */
export interface ContextPackage {
  /** Package identifier. */
  readonly id: string;
  /** Referenced artifacts that form the context. */
  artifacts: ArtifactRef[];
  /** Optional file ranges to include. */
  files?: Array<{
    path: string;
    start?: number;
    end?: number;
    checksum?: string;
  }>;
  /** Optional interface or contract snippets. */
  interfaces?: Array<{
    name: string;
    snippet: string;
    path?: string;
  }>;
  /** Optional prior summaries to ground the work. */
  priorSummaries?: string[];
  /** Execution constraints applied to the assignment. */
  constraints: {
    tokenCap: number;
    timeLimitMs?: number;
  };
  /** Retrieval keys for fetching additional context. */
  retrievalKeys?: string[];
  /** Freeform notes. */
  notes?: string;
}

/**
 * Reference to a produced or input artifact.
 */
export interface ArtifactRef {
  /** Artifact identifier. */
  readonly id: ArtifactId;
  /** Artifact classification. */
  type: ArtifactType;
  /** Optional repository path. */
  path?: string;
  /** Optional semantic or content version. */
  version?: string;
  /** Optional content checksum. */
  checksum?: string;
  /** Optional size in bytes. */
  size?: number;
  /** Additional metadata. */
  metadata?: Record<string, any>;
}

/**
 * Binding of a work unit to an agent with context and runtime state.
 */
export interface Assignment {
  /** Assignment identifier. */
  readonly id: AssignmentId;
  /** Target work unit. */
  workUnitId: WorkUnitId;
  /** Assigned agent. */
  agentId: AgentId;
  /** Provided context package. */
  contextPackage: ContextPackage;
  /** Templated instructions for the agent. */
  instructions: string;
  /** Optional start time. */
  startAt?: number;
  /** Optional completion deadline. */
  deadline?: number;
  /** Current assignment status. */
  status: AssignmentStatus;
  /** Optional summary of results. */
  resultSummary?: string;
  /** Runtime metrics for the assignment. */
  metrics?: {
    tokenUsage?: number;
    durationMs?: number;
    costUSD?: number;
  };
  /** Number of attempts performed. */
  attempts?: number;
}

/**
 * Event emitted to reflect progress on a work unit.
 */
export interface ProgressEvent {
  /** Event identifier. */
  readonly eventId: ProgressEventId;
  /** Event type. */
  type: ProgressEventType;
  /** Related work unit. */
  workUnitId: WorkUnitId;
  /** Optional agent producing the event (for handoff/loop guard telemetry). */
  agentId?: AgentId;
  /** Optional completion percent [0..100]. */
  percentComplete?: number;
  /** Optional human-readable message. */
  message?: string;
  /** Optional artifacts attached to the event. */
  artifacts?: ArtifactRef[];
  /** Optional event-scoped metrics. */
  metrics?: {
    tokenUsage?: number;
    durationMs?: number;
  };
  /** Error details when type indicates failure or blocker. */
  error?: {
    code?: string;
    message: string;
    stack?: string;
  };
  /** Event timestamp (ms since epoch). */
  timestamp: number;
}

/**
 * Persistent record of a run, its plan, assignments, events, and artifacts.
 */
export interface Ledger {
  /** Run identifier this ledger tracks. */
  readonly runId: RunId;
  /** Planned partition (if any). */
  plan?: PartitionPlan;
  /** All assignments created during the run. */
  assignments: Assignment[];
  /** Ordered stream of progress events. */
  events: ProgressEvent[];
  /** All artifacts known to the system. */
  artifacts: ArtifactRef[];
  /** Optional scoped summaries produced during the run. */
  summaries?: Array<{
    scope: string;
    summary: string;
  }>;
  /** Optional aggregated rollups (costs, durations, etc.). */
  rollups?: Record<string, any>;
  /** Last update time (ms since epoch). */
  lastUpdated: number;
}

/**
 * Aggregate metrics about orchestration performance.
 */
export interface OrchestrationMetrics {
  /** Time from run start to first event, in ms. */
  timeToFirstUpdate: number;
  /** Units completed per hour (or comparable rate). */
  throughput: number;
  /** Fraction in [0,1] representing run success rate. */
  successRate: number;
  /** Total cost in USD. */
  cost: number;
  /** Total tokens consumed. */
  tokenUsage: number;
}
