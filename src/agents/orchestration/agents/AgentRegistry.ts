/**
 * In-memory registry for agent profiles, concurrency tracking, and agent selection.
 *
 * Storage
 * - Maintains a Map<AgentId, { profile, inFlight }> and a global inFlight counter.
 *
 * Concurrency
 * - acquire(agentId): increments per-agent and global counters if capacity allows (per-agent concurrencyLimit and optional maxGlobalConcurrency).
 * - release(agentId): decrements counters safely (clamped at 0).
 *
 * Selection
 * - selectFor(workUnit, candidates?, request?): Filters to agents with capacity > 0 (and respects global cap). Scores by:
 *   - Capability match via Jaccard similarity between profile.capabilities and inferred tags from files/intent/category.
 *   - Category affinity boost when agent prefers the unit category.
 *   - Historical successRate boost; penalize high in-flight utilization.
 *   - Lightly respect request constraints (tokenBudget): break ties by preferring lower maxTokenContext.
 * - Tie-breaking: higher successRate, then lower inFlight.
 *
 * History
 * - recordOutcome(): Updates profile.historical successRate (EMA, alpha=0.2) and avgThroughput (EMA on 1/durationMs).
 *
 * Notes
 * - Self-contained; only imports types from ../types.
 * - All methods are O(n) over agent count or better.
 */

import { AgentProfile, AgentId, WorkUnit, WorkCategory, OrchestrationRequest } from '../types';

export interface AgentRegistryOptions {
  /** Optionally seed registry with default agents. */
  defaultAgents?: AgentProfile[];
  /** Optional cap across all agents. */
  maxGlobalConcurrency?: number;
}

type AgentEntry = {
  profile: AgentProfile;
  inFlight: number;
};

const EMA_ALPHA = 0.2;

/** Centralized extension-to-language mapping */
const EXT_TO_LANG: ReadonlyMap<string, string> = new Map([
  ['ts', 'typescript'],
  ['tsx', 'react'],
  ['js', 'javascript'],
  ['jsx', 'react'],
  ['mjs', 'javascript'],
  ['cjs', 'javascript'],
  ['py', 'python'],
  ['rb', 'ruby'],
  ['go', 'go'],
  ['rs', 'rust'],
  ['java', 'java'],
  ['cs', 'csharp'],
  ['php', 'php'],
  ['kt', 'kotlin'],
  ['swift', 'swift'],
  ['cpp', 'cpp'],
  ['cc', 'cpp'],
  ['cxx', 'cpp'],
  ['c', 'c'],
  ['h', 'c'],
  ['mm', 'objective-c++'],
  ['m', 'objective-c'],
  ['scala', 'scala'],
  ['sh', 'bash'],
  ['bash', 'bash'],
  ['zsh', 'bash'],
  ['md', 'markdown'],
  ['json', 'json'],
  ['yml', 'yaml'],
  ['yaml', 'yaml'],
  ['sql', 'sql'],
  ['css', 'css'],
  ['scss', 'scss'],
  ['html', 'html'],
]);
export class AgentRegistry {
  private readonly agents: Map<AgentId, AgentEntry> = new Map();
  private globalInFlight = 0;
  private readonly maxGlobalConcurrency?: number;

  /**
   * Create a new AgentRegistry.
   * - Seeds with defaultAgents (in-flight starts at 0).
   * - Applies an optional global concurrency cap.
   */
  constructor(opts?: AgentRegistryOptions) {
    this.maxGlobalConcurrency = opts?.maxGlobalConcurrency;
    if (opts?.defaultAgents) {
      for (const a of opts.defaultAgents) {
        // Defensive shallow copy to avoid external mutation of stored reference
        const copy: AgentProfile = { ...a, capabilities: [...(a.capabilities ?? [])] };
        this.agents.set(copy.id, { profile: copy, inFlight: 0 });
      }
    }
  }

  /**
   * List shallow copies of agent profiles currently registered.
   * Does not include runtime in-flight counters in the return values.
   */
  list(): AgentProfile[] {
    const out: AgentProfile[] = [];
    for (const { profile } of this.agents.values()) {
      out.push({ ...profile, capabilities: [...(profile.capabilities ?? [])] });
    }
    return out;
  }

  /**
   * Get the current profile object for an agent.
   * Returns undefined if not found.
   */
  get(id: AgentId): AgentProfile | undefined {
    return this.agents.get(id)?.profile;
  }

  /**
   * Insert or update an agent profile.
   * - Preserves existing in-flight counter for that agent.
   * - Replaces the stored profile reference with the provided one.
   */
  upsert(profile: AgentProfile): void {
    const existing = this.agents.get(profile.id);
    const inFlight = existing?.inFlight ?? 0;
    // Ensure capabilities array is defined defensively
    if (!profile.capabilities) profile.capabilities = [];
    this.agents.set(profile.id, { profile, inFlight });
  }

  /**
   * Get current in-flight count.
   * - If agentId omitted, returns global in-flight.
   * - If agentId provided, returns that agent's in-flight (0 if unknown).
   */
  inFlight(agentId?: AgentId): number {
    if (!agentId) return this.globalInFlight;
    return this.agents.get(agentId)?.inFlight ?? 0;
  }

  /**
   * Remaining per-agent capacity (>= 0).
   * Returns 0 for unknown agents.
   */
  capacity(agentId: AgentId): number {
    const entry = this.agents.get(agentId);
    if (!entry) return 0;
    const limit = Math.max(0, entry.profile.concurrencyLimit ?? 0);
    return Math.max(0, limit - entry.inFlight);
  }

  /**
   * Attempt to acquire a concurrency slot for an agent.
   * - Respects per-agent concurrencyLimit and optional global cap.
   * - Returns true on success; false otherwise.
   */
  acquire(agentId: AgentId): boolean {
    const entry = this.agents.get(agentId);
    if (!entry) return false;
    if (this.capacity(agentId) <= 0) return false;
    if (this.maxGlobalConcurrency !== undefined && this.globalInFlight >= this.maxGlobalConcurrency) {
      return false;
    }
    entry.inFlight += 1;
    this.globalInFlight += 1;
    return true;
  }

  /**
   * Release a previously acquired slot for an agent.
   * - Safely clamps counters at 0.
   * - No-op if agent is unknown.
   */
  release(agentId: AgentId): void {
    const entry = this.agents.get(agentId);
    if (!entry) return;
    if (entry.inFlight > 0) entry.inFlight -= 1;
    if (this.globalInFlight > 0) this.globalInFlight -= 1;
  }

  /**
   * Select the best available agent for a WorkUnit.
   * - Filters to agents with capacity > 0. If global cap is saturated, returns undefined.
   * - Scores based on capability match (Jaccard), category affinity, historical success rate,
   *   and utilization penalty. Lightly respects request constraints (tokenBudget) as a tie-breaker
   *   by preferring lower maxTokenContext on near-equal scores.
   *
   * Returns the selected AgentProfile, or undefined if none have capacity.
   */
  selectFor(
    workUnit: WorkUnit,
    candidates?: AgentProfile[],
    request?: OrchestrationRequest,
  ): AgentProfile | undefined {
    // If the global cap is fully utilized, nothing is available.
    if (this.maxGlobalConcurrency !== undefined && this.globalInFlight >= this.maxGlobalConcurrency) {
      return undefined;
    }

    // Build candidate list from registry entries
    const pool: AgentEntry[] = [];
    if (candidates && candidates.length > 0) {
      for (const c of candidates) {
        const e = this.agents.get(c.id);
        if (e) pool.push(e);
      }
    } else {
      for (const e of this.agents.values()) pool.push(e);
    }

    // Filter to those with available per-agent capacity
    const available = pool.filter((e) => this.capacity(e.profile.id) > 0);
    if (available.length === 0) return undefined;

    const inferredTags = this.inferTagsFromWorkUnit(workUnit);
    const tokenBudget = request?.constraints?.tokenBudget;
    const EPS = 1e-3;

    let best: AgentEntry | undefined;
    let bestScore = -Infinity;

    for (const entry of available) {
      const score = this.scoreAgent(entry, workUnit, inferredTags);

      if (score > bestScore + EPS) {
        best = entry;
        bestScore = score;
        continue;
      }
      if (Math.abs(score - bestScore) <= EPS) {
        // Tie-breakers:
        // 1) Higher historical success rate
        const srA = entry.profile.historical?.successRate ?? 0;
        const srB = best?.profile.historical?.successRate ?? 0;
        if (srA !== srB) {
          if (srA > srB) {
            best = entry;
            bestScore = score;
          }
          continue;
        }

        // 2) Lower current inFlight
        const inA = entry.inFlight;
        const inB = best?.inFlight ?? 0;
        if (inA !== inB) {
          if (inA < inB) {
            best = entry;
            bestScore = score;
          }
          continue;
        }

        // 3) Lightly respect tokenBudget by preferring lower maxTokenContext
        if (tokenBudget !== undefined) {
          const mA = entry.profile.maxTokenContext ?? Number.POSITIVE_INFINITY;
          const mB = best?.profile.maxTokenContext ?? Number.POSITIVE_INFINITY;
          if (mA !== mB) {
            if (mA < mB) {
              best = entry;
              bestScore = score;
            }
            continue;
          }
        }
      }
    }

    return best?.profile;
  }

  /**
   * Record an assignment outcome for an agent.
   * - Updates historical.successRate via EMA (alpha=0.2).
   * - Updates historical.avgThroughput via EMA on (1 / durationMs).
   * - Persists only in-memory on the stored AgentProfile.
   */
  recordOutcome(
    agentId: AgentId,
    success: boolean,
    metrics?: { durationMs?: number; tokenUsage?: number },
  ): void {
    const entry = this.agents.get(agentId);
    if (!entry) return;

    const hist = (entry.profile.historical = entry.profile.historical ?? {});
    const priorSR = hist.successRate ?? (success ? 1 : 0);
    const obsSR = success ? 1 : 0;
    hist.successRate = (1 - EMA_ALPHA) * priorSR + EMA_ALPHA * obsSR;

    // Throughput as 1/durationMs (units per ms). If duration is missing/0, treat as 0 observation.
    const priorTP = hist.avgThroughput ?? 0;
    const obsTP =
      metrics?.durationMs && metrics.durationMs > 0 ? 1 / metrics.durationMs : 0;
    hist.avgThroughput = (1 - EMA_ALPHA) * priorTP + EMA_ALPHA * obsTP;
  }

  // ===== Private helpers =====

  private scoreAgent(entry: AgentEntry, workUnit: WorkUnit, inferredTags: Set<string>): number {
    const caps = new Set((entry.profile.capabilities ?? []).map((s) => s.toLowerCase()));
    const jacc = this.jaccard(caps, inferredTags); // [0,1]

    const hasAffinity =
      (entry.profile.categoryAffinity ?? []).includes(workUnit.category as WorkCategory);
    const affinityBoost = hasAffinity ? 0.15 : 0;

    const successRate = entry.profile.historical?.successRate ?? 0.5; // neutral default
    const utilization =
      entry.profile.concurrencyLimit > 0
        ? entry.inFlight / entry.profile.concurrencyLimit
        : 1; // fully penalize if limit is 0 (shouldn't happen)

    // Weighted score components
    const score =
      0.5 * jacc + // capability match
      0.3 * successRate + // historical performance
      affinityBoost - // category preference boost
      0.25 * Math.min(1, Math.max(0, utilization)); // utilization penalty

    return score;
  }

  private jaccard(a: Set<string>, b: Set<string>): number {
    if (a.size === 0 && b.size === 0) return 0;
    let inter = 0;
    for (const v of a) if (b.has(v)) inter++;
    const union = a.size + b.size - inter;
    return union > 0 ? inter / union : 0;
  }

  private inferTagsFromWorkUnit(workUnit: WorkUnit): Set<string> {
    const tags = new Set<string>();

    // Category and intent
    if (workUnit.category) tags.add(String(workUnit.category).toLowerCase());
    // Intent is an enum; stringify defensively
    if ((workUnit as any).intent) tags.add(String((workUnit as any).intent).toLowerCase());

    // File-based inference
    const files = workUnit.scope?.files ?? [];
    for (const f of files) {
      const lower = f.toLowerCase();
      const ext = this.extname(lower);
      const lang = this.languageTagFromExt(ext);
      if (lang) tags.add(lang);

      if (lower.includes('test') || lower.includes('spec')) {
        tags.add('test');
        tags.add('jest'); // heuristic for JS/TS projects
      }
      if (lower.includes('e2e')) tags.add('e2e');
      if (lower.includes('dockerfile') || lower.endsWith('dockerfile')) tags.add('docker');
      if (lower.endsWith('package.json')) tags.add('node');
      if (lower.endsWith('requirements.txt')) tags.add('python');
    }

    // Modules/components can hint domains (lightweight)
    const modules = workUnit.scope?.modules ?? [];
    for (const m of modules) {
      const lm = m.toLowerCase();
      if (lm.includes('auth')) tags.add('auth');
      if (lm.includes('db') || lm.includes('database')) tags.add('database');
      if (lm.includes('ui') || lm.includes('component')) tags.add('frontend');
      if (lm.includes('api') || lm.includes('server')) tags.add('backend');
    }

    // Components/paths heuristics
    const comps = workUnit.scope?.components ?? [];
    for (const c of comps) {
      const lc = c.toLowerCase();
      if (lc.includes('react')) tags.add('react');
      if (lc.includes('vue')) tags.add('vue');
      if (lc.includes('svelte')) tags.add('svelte');
    }

    const paths = workUnit.scope?.paths ?? [];
    for (const p of paths) {
      const lp = p.toLowerCase();
      if (lp.includes('/src/agents')) tags.add('agents');
      if (lp.includes('/src/tests') || lp.includes('__tests__')) {
        tags.add('test');
        tags.add('jest');
      }
      if (lp.includes('/api/')) tags.add('api');
    }

    return tags;
  }

  private extname(filename: string): string {
    const i = filename.lastIndexOf('.');
    if (i < 0) return '';
    return filename.slice(i + 1);
  }

  private languageTagFromExt(ext: string): string | undefined {
    const key = (ext || '').replace(/^\./, '').toLowerCase();
    return EXT_TO_LANG.get(key);
  }
}
