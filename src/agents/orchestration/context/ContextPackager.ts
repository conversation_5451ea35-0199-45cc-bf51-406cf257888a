/**
 * ContextPackager builds a compact, strict minimal-context package for a WorkUnit.
 *
 * Heuristics:
 * - Use workUnit.scope.files as authoritative list; resolve against baseDir.
 * - Per-file byte cap: include full file if under cap; else include header/footer slices.
 * - Optional exported interface/class/type/function/const snippets (±15 lines) up to 3 per file.
 * - Global token cap: approximate tokens as ceil(chars/4). Never exceed.
 *   - If mandatory slices would exceed, truncate last file’s footer first, then header.
 * - Robustness: missing files are skipped with notes; do not throw for individual errors.
 *
 * Outputs ContextPackage with:
 * - id, artifacts (empty), files slices (path,start,end,checksum), optional interfaces, optional priorSummaries,
 *   constraints (tokenCap, timeLimitMs), retrievalKeys (paths, group keys, unit identifiers), and concise notes.
 */

import { promises as fs } from 'fs';
import * as nodePath from 'path';
import { createHash } from 'crypto';
import type { WorkUnit, OrchestrationRequest, ContextPackage } from '../types';

export interface ContextPackagerOptions {
  tokenCap?: number; // default 6000
  perFileByteCap?: number; // default 16000
  perFileHeaderLines?: number; // default 120
  perFileFooterLines?: number; // default 60
  includeInterfaceSnippets?: boolean; // default true
  baseDir?: string; // default process.cwd()
  timeLimitMs?: number; // optional
}

/** Approximate token count using a simple heuristic (1 token ~ 4 chars). */
function approxTokens(s: string): number {
  if (!s) return 0;
  return Math.ceil(s.length / 4);
}

/** Normalize to path relative to baseDir if within it; otherwise absolute. */
function relPath(p: string, baseDir: string): string {
  const resolvedBase = nodePath.resolve(baseDir);
  const resolved = nodePath.resolve(p);
  if (resolved.startsWith(resolvedBase + nodePath.sep) || resolved === resolvedBase) {
    return nodePath.relative(resolvedBase, resolved) || '.';
  }
  return resolved;
}

/** Join inclusive [start..end] (1-based) lines to a single string. */
function sliceLines(lines: string[], start: number, end: number): string {
  const s = Math.max(1, start);
  const e = Math.max(s, Math.min(end, lines.length));
  return lines.slice(s - 1, e).join('\n');
}

/** SHA1 checksum (hex) of provided string. */
function sha1(s: string): string {
  return createHash('sha1').update(s).digest('hex');
}

type ExportSnippet = { name: string; snippet: string; start: number; end: number; path: string };

/**
 * Extract up to `limit` exported declarations snippets with ±15 lines context.
 * Matches: /^export\s+(interface|type|class|function|const|let|var)\b/
 */
function extractExportSnippets(
  lines: string[],
  filePath: string,
  limit: number
): Array<{ name: string; snippet: string; start: number; end: number }> {
  const results: Array<{ name: string; snippet: string; start: number; end: number }> = [];
  const exp = /^export\s+(interface|type|class|function|const|let|var)\b/;
  for (let i = 0; i < lines.length && results.length < limit; i++) {
    const line = lines[i];
    if (!exp.test(line)) continue;
    // Try to extract a name after the keyword
    let name = 'export';
    const nameMatch =
      line.match(/^export\s+(interface|type|class|function)\s+([A-Za-z0-9_$]+)/) ||
      line.match(/^export\s+(const|let|var)\s+([A-Za-z0-9_$]+)/);
    if (nameMatch && nameMatch[2]) {
      name = nameMatch[2];
    }
    const ctx = 15;
    const start = Math.max(1, i + 1 - ctx);
    const end = Math.min(lines.length, i + 1 + ctx);
    const snippet = sliceLines(lines, start, end);
    results.push({ name, snippet, start, end });
  }
  return results;
}

type FileSlice = { path: string; start: number; end: number; checksum: string };

export class ContextPackager {
  private opts: Required<Omit<ContextPackagerOptions, 'timeLimitMs'>> & { timeLimitMs?: number };

  constructor(opts?: ContextPackagerOptions) {
    this.opts = {
      tokenCap: opts?.tokenCap ?? 6000,
      perFileByteCap: opts?.perFileByteCap ?? 16000,
      perFileHeaderLines: opts?.perFileHeaderLines ?? 120,
      perFileFooterLines: opts?.perFileFooterLines ?? 60,
      includeInterfaceSnippets: opts?.includeInterfaceSnippets ?? true,
      baseDir: opts?.baseDir ?? process.cwd(),
      timeLimitMs: opts?.timeLimitMs,
    };
  }

  /**
   * Build a minimal ContextPackage for the given WorkUnit, enforcing hard token caps.
   * - Mandatory content: per-file full content if under byte cap, else header/footer slices.
   * - Optional content: interface export snippets (±15 lines), prior summary (short).
   * - Token gating: never exceed tokenCap; if necessary, truncate the last file’s footer first, then header.
   */
  async package(workUnit: WorkUnit, _request?: OrchestrationRequest): Promise<ContextPackage> {
    const {
      tokenCap,
      perFileByteCap,
      perFileHeaderLines,
      perFileFooterLines,
      includeInterfaceSnippets,
      baseDir,
      timeLimitMs,
    } = this.opts;

    const pkgId = `ctx_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;
    const filesInScope = (workUnit?.scope?.files ?? []).filter(Boolean);
    const retrievalKeys = new Set<string>();
    const notesParts: string[] = [];
    let missingFiles = 0;
    let truncatedFiles = 0;
    let totalSlices = 0;

    // Seed retrieval keys with identifiers and scope hints
    if (workUnit?.id) retrievalKeys.add(String(workUnit.id));
    if (workUnit?.intent) retrievalKeys.add(String(workUnit.intent));
    if (workUnit?.category) retrievalKeys.add(String(workUnit.category));
    for (const comp of workUnit?.scope?.components ?? []) retrievalKeys.add(comp);
    for (const mod of workUnit?.scope?.modules ?? []) retrievalKeys.add(mod);
    for (const p of workUnit?.scope?.paths ?? []) retrievalKeys.add(p);

    // Early return if no files in scope
    if (!filesInScope.length) {
      const minimal: ContextPackage = {
        id: pkgId,
        artifacts: [],
        constraints: { tokenCap, timeLimitMs },
        retrievalKeys: Array.from(retrievalKeys),
        notes: 'No files in scope; built retrieval keys from unit identifiers and scope components.',
      };
      // Optional prior summary (short)
      if (workUnit?.description && workUnit.description.length <= 500) {
        minimal.priorSummaries = [workUnit.description];
      }
      return minimal;
    }

    // Add path-based retrieval keys and group keys from directory segments
    for (const f of filesInScope) {
      const abs = nodePath.resolve(baseDir, f);
      const r = relPath(abs, baseDir);
      retrievalKeys.add(r);
      const dir = nodePath.dirname(r);
      if (dir && dir !== '.') {
        const parts = dir.split(nodePath.sep).filter(Boolean);
        for (const seg of parts) {
          retrievalKeys.add(`group:${seg}`);
        }
      }
    }

    const outFileSlices: FileSlice[] = [];
    const outInterfaces: Array<{ name: string; snippet: string; path: string }> = [];
    let accTokens = 0;

    // Helper to attempt to add text under cap
    const tryAddText = (text: string): boolean => {
      const t = approxTokens(text);
      if (accTokens + t > tokenCap) return false;
      accTokens += t;
      return true;
    };

    // Truncate helpers: shrink footer first (move start forward), then header (move end backward)
    const shrinkFooterToTokens = (
      lines: string[],
      start: number,
      end: number,
      budgetTokens: number
    ): { start: number; end: number; text: string } | null => {
      if (start > end) return null;
      let s = start;
      let e = end;
      let text = sliceLines(lines, s, e);
      let t = approxTokens(text);
      if (t <= budgetTokens) return { start: s, end: e, text };
      // Move start forward until fits or empty
      while (s <= e) {
        s++;
        text = sliceLines(lines, s, e);
        t = approxTokens(text);
        if (t <= budgetTokens) return { start: s, end: e, text };
      }
      return null;
    };

    const shrinkHeaderToTokens = (
      lines: string[],
      start: number,
      end: number,
      budgetTokens: number
    ): { start: number; end: number; text: string } | null => {
      if (start > end) return null;
      let s = start;
      let e = end;
      let text = sliceLines(lines, s, e);
      let t = approxTokens(text);
      if (t <= budgetTokens) return { start: s, end: e, text };
      // Move end backward until fits or empty
      while (s <= e) {
        e--;
        text = sliceLines(lines, s, e);
        t = approxTokens(text);
        if (t <= budgetTokens) return { start: s, end: e, text };
      }
      return null;
    };

    // Process each file in scope
    for (const f of filesInScope) {
      // If we already hit the cap, stop adding more
      if (accTokens >= tokenCap) break;

      const abs = nodePath.resolve(baseDir, f);
      const rel = relPath(abs, baseDir);

      let content: string | null = null;
      try {
        content = await fs.readFile(abs, 'utf8');
      } catch {
        missingFiles++;
        notesParts.push(`Missing file: ${rel}`);
        continue;
      }
      if (content == null) continue;

      const bytes = Buffer.byteLength(content, 'utf8');
      const lines = content.split('\n');
      const lineCount = lines.length;

      // Compute mandatory slices
      type SlicePlan =
        | { kind: 'full'; start: number; end: number; text: string }
        | {
            kind: 'split';
            header?: { start: number; end: number; text: string };
            footer?: { start: number; end: number; text: string };
          };

      let plan: SlicePlan;
      if (bytes <= perFileByteCap) {
        // Whole file is below byte cap - include as a single slice
        plan = { kind: 'full', start: 1, end: lineCount, text: content };
      } else {
        // Over byte cap - header and footer slices
        const headerStart = 1;
        const headerEnd = Math.min(perFileHeaderLines, lineCount);
        // Avoid overlap by ensuring footer starts after header
        const rawFooterStart = Math.max(lineCount - perFileFooterLines + 1, 1);
        const footerStart = Math.max(rawFooterStart, headerEnd + 1);
        const footerEnd = lineCount;

        const headerText =
          headerEnd >= headerStart ? sliceLines(lines, headerStart, headerEnd) : '';
        const footerText =
          footerEnd >= footerStart ? sliceLines(lines, footerStart, footerEnd) : '';

        plan = {
          kind: 'split',
          header:
            headerText
              ? { start: headerStart, end: headerEnd, text: headerText }
              : undefined,
          footer:
            footerText
              ? { start: footerStart, end: footerEnd, text: footerText }
              : undefined,
        };
      }

      // Attempt to add mandatory slices under token cap. If over, truncate this file's slices.
      const beforeTokens = accTokens;

      if (plan.kind === 'full') {
        const fullTokens = approxTokens(plan.text);
        if (accTokens + fullTokens <= tokenCap) {
          // Add as-is
          outFileSlices.push({
            path: rel,
            start: plan.start,
            end: plan.end,
            checksum: sha1(plan.text),
          });
          accTokens += fullTokens;
          totalSlices += 1;
        } else {
          // Fallback to header/footer split to preserve most critical parts
          const headerEnd = Math.min(perFileHeaderLines, lineCount);
          const footerStart = Math.max(lineCount - perFileFooterLines + 1, headerEnd + 1);
          const headerText =
            headerEnd >= 1 ? sliceLines(lines, 1, headerEnd) : '';
          const footerText =
            lineCount >= footerStart ? sliceLines(lines, footerStart, lineCount) : '';

          let header = headerText ? { start: 1, end: headerEnd, text: headerText } : undefined;
          let footer = footerText ? { start: footerStart, end: lineCount, text: footerText } : undefined;

          const headerTokens = header ? approxTokens(header.text) : 0;
          const footerTokens = footer ? approxTokens(footer.text) : 0;

          let allowed = tokenCap - accTokens;

          // Add header fully if possible
          if (header && headerTokens <= allowed) {
            outFileSlices.push({
              path: rel,
              start: header.start,
              end: header.end,
              checksum: sha1(header.text),
            });
            accTokens += headerTokens;
            totalSlices += 1;
            allowed = tokenCap - accTokens;
          }

          // Try to add footer with remaining budget, shrinking footer first
          if (footer && allowed > 0) {
            const shrunkFooter = shrinkFooterToTokens(lines, footer.start, footer.end, allowed);
            if (shrunkFooter) {
              outFileSlices.push({
                path: rel,
                start: shrunkFooter.start,
                end: shrunkFooter.end,
                checksum: sha1(shrunkFooter.text),
              });
              accTokens += approxTokens(shrunkFooter.text);
              totalSlices += 1;
            }
          }

          if (accTokens === beforeTokens) {
            // Nothing could be added for this file
            truncatedFiles += 1;
            notesParts.push(`Truncated entire file due to cap: ${rel}`);
          } else {
            truncatedFiles += 1;
            notesParts.push(`Truncated file (split from full) due to cap: ${rel}`);
          }
        }
      } else {
        const headerTokens = plan.header ? approxTokens(plan.header.text) : 0;
        const footerTokens = plan.footer ? approxTokens(plan.footer.text) : 0;
        const totalTokensNeeded = headerTokens + footerTokens;

        if (accTokens + totalTokensNeeded <= tokenCap) {
          // Add both as-is
          if (plan.header) {
            outFileSlices.push({
              path: rel,
              start: plan.header.start,
              end: plan.header.end,
              checksum: sha1(plan.header.text),
            });
            accTokens += headerTokens;
            totalSlices += 1;
          }
          if (plan.footer) {
            outFileSlices.push({
              path: rel,
              start: plan.footer.start,
              end: plan.footer.end,
              checksum: sha1(plan.footer.text),
            });
            accTokens += footerTokens;
            totalSlices += 1;
          }
        } else {
          // Truncate footer first
          let allowed = tokenCap - accTokens;
          let addedSomething = false;

          if (plan.header && plan.header.text && allowed > 0) {
            // Prefer keeping header intact if we can
            if (headerTokens <= allowed) {
              outFileSlices.push({
                path: rel,
                start: plan.header.start,
                end: plan.header.end,
                checksum: sha1(plan.header.text),
              });
              accTokens += headerTokens;
              totalSlices += 1;
              addedSomething = true;
              allowed = tokenCap - accTokens;
            } else {
              // If header alone doesn't fit, we'll try it after footer attempt (rule: shrink footer first)
            }
          }

          if (plan.footer && plan.footer.text && allowed > 0) {
            const shrunk = shrinkFooterToTokens(lines, plan.footer.start, plan.footer.end, allowed);
            if (shrunk) {
              outFileSlices.push({
                path: rel,
                start: shrunk.start,
                end: shrunk.end,
                checksum: sha1(shrunk.text),
              });
              accTokens += approxTokens(shrunk.text);
              totalSlices += 1;
              addedSomething = true;
              allowed = tokenCap - accTokens;
            }
          }

          // If header still not added and there is remaining budget, try shrinking header now
          if (plan.header && plan.header.text && headerTokens > (tokenCap - beforeTokens)) {
            const rem = tokenCap - accTokens;
            if (rem > 0) {
              const shrunkHeader = shrinkHeaderToTokens(lines, plan.header.start, plan.header.end, rem);
              if (shrunkHeader) {
                outFileSlices.push({
                  path: rel,
                  start: shrunkHeader.start,
                  end: shrunkHeader.end,
                  checksum: sha1(shrunkHeader.text),
                });
                accTokens += approxTokens(shrunkHeader.text);
                totalSlices += 1;
                addedSomething = true;
              }
            }
          }

          if (!addedSomething) {
            truncatedFiles += 1;
            notesParts.push(`Unable to include header/footer for: ${rel} due to hard cap`);
          } else {
            truncatedFiles += 1;
            notesParts.push(`Truncated slices for: ${rel} due to cap`);
          }
        }
      }

      // Optional interface snippets (up to 3 per file) if enabled and budget remains
      if (includeInterfaceSnippets && accTokens < tokenCap) {
        const snippets = extractExportSnippets(lines, rel, 3);
        for (const sn of snippets) {
          const t = approxTokens(sn.snippet);
          if (accTokens + t > tokenCap) {
            // skip optional snippet if it would exceed cap
            continue;
          }
          outInterfaces.push({ name: sn.name, snippet: sn.snippet, path: rel });
          accTokens += t;
        }
      }
    }

    // Optional prior summary from description (short) if space remains
    const priorSummaries: string[] = [];
    if (workUnit?.description && workUnit.description.length <= 500) {
      const t = approxTokens(workUnit.description);
      if (accTokens + t <= tokenCap) {
        priorSummaries.push(workUnit.description);
        accTokens += t;
      } else {
        notesParts.push('Omitted prior summary due to token cap.');
      }
    }

    // Build concise notes
    const includedFiles = new Set(outFileSlices.map(s => s.path)).size;
    const interfacesCount = outInterfaces.length;
    const noteHead = `Included ${includedFiles} file(s), ${totalSlices} slice(s), ${interfacesCount} interface snippet(s).`;
    const noteTail = [
      missingFiles ? `Skipped ${missingFiles} missing.` : '',
      truncatedFiles ? `Truncated ${truncatedFiles}.` : '',
      `TokenCap=${tokenCap}.`,
    ]
      .filter(Boolean)
      .join(' ');
    const notes = [noteHead, ...notesParts, noteTail].filter(Boolean).join(' ');

    const pkg: ContextPackage = {
      id: pkgId,
      artifacts: [],
      files: outFileSlices.length ? outFileSlices : undefined,
      interfaces: outInterfaces.length ? outInterfaces : undefined,
      priorSummaries: priorSummaries.length ? priorSummaries : undefined,
      constraints: { tokenCap, timeLimitMs },
      retrievalKeys: Array.from(retrievalKeys),
      notes,
    };

    return pkg;
  }
}

export { approxTokens, relPath, sliceLines, sha1, extractExportSnippets };
