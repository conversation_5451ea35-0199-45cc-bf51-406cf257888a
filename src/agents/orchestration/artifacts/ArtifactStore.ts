import { promises as fs } from 'fs';
import * as path from 'path';
import { createHash } from 'crypto';
import { ArtifactId, ArtifactRef, ArtifactType, RunId } from '../types';

const META_FILENAME = 'artifact.meta.json';

/**
 * Filesystem-backed store for artifacts (diffs/patches/files/logs/tests/docs) with versioning and checksums.
 * Layout: baseDir/{runId}/{artifactId}/{version}/{filename}
 * - Default baseDir: <repo>/data/artifacts
 * - No separate JSON indexes are persisted; ArtifactRef returned is the metadata carrier.
 */

/** Options to configure the ArtifactStore. */
export interface ArtifactStoreOptions {
  /** Storage root directory. Defaults to <repo>/data/artifacts */
  baseDir?: string;
}

/** Input to put() for storing a new artifact or new version of an artifact. */
export type PutArtifactInput = {
  runId: RunId;
  type: ArtifactType;
  /** Suggested file name; used in the stored path. If omitted and sourcePath is provided, basename(sourcePath) is used; otherwise a default by type. */
  filename?: string;
  /** Content to write. If provided, takes precedence over sourcePath. */
  content?: string | Buffer;
  /** If provided (and content is not), file is copied from this path. */
  sourcePath?: string;
  /** Arbitrary metadata echoed back in the ArtifactRef (not persisted separately). */
  metadata?: Record<string, any>;
  /** Optional explicit version (e.g., 'v3'). If omitted, auto-incremented as v1, v2, ... */
  version?: string;
};

/** Helper: sha1 hash as hex string. */
function sha1(bufOrStr: Buffer | string): string {
  const h = createHash('sha1');
  h.update(bufOrStr);
  return h.digest('hex');
}

/** Helper: ensure directory exists (recursive). */
async function ensureDir(p: string): Promise<void> {
  await fs.mkdir(p, { recursive: true });
}

/** Helper: safe readdir; returns [] if ENOENT. */
async function readDirSafe(p: string): Promise<string[]> {
  try {
    return await fs.readdir(p);
  } catch (err: any) {
    if (err && (err as NodeJS.ErrnoException).code === 'ENOENT') return [];
    throw err;
  }
}

/** Helper: find latest version folder name (e.g., 'v3') by numeric max. */
async function latestVersion(dir: string): Promise<string | undefined> {
  const names = await readDirSafe(dir);
  let max = 0;
  for (const name of names) {
    const m = /^v(\d+)$/.exec(name);
    if (m) {
      const n = parseInt(m[1], 10);
      if (n > max) max = n;
    }
  }
  return max > 0 ? `v${max}` : undefined;
}

/** Helper: path relative to base (repo root). */
function toRel(p: string, base: string): string {
  return path.relative(base, p);
}

/** Infer ArtifactType from filename extension as a best-effort reconstruction. */
function inferTypeFromFilename(file: string): ArtifactType {
  const ext = path.extname(file).toLowerCase();
  if (ext === '.patch') return ArtifactType.Patch;
  if (ext === '.diff') return ArtifactType.Diff;
  if (ext === '.log') return ArtifactType.Log;
  if (ext === '.md' || ext === '.rst' || ext === '.txt') return ArtifactType.Doc;
  if (ext === '.test' || ext === '.spec' || file.toLowerCase().includes('.test.')) return ArtifactType.Test;
  return ArtifactType.File;
}

/** Default filename if none provided. */
function defaultFilenameByType(t: ArtifactType): string {
  switch (t) {
    case ArtifactType.Patch:
      return 'artifact.patch';
    case ArtifactType.Diff:
      return 'artifact.diff';
    case ArtifactType.Log:
      return 'artifact.log';
    case ArtifactType.Doc:
      return 'artifact.txt';
    case ArtifactType.Test:
      return 'artifact.test';
    case ArtifactType.File:
    default:
      return 'artifact.bin';
  }
}

/** Compute an artifact id. Chosen to be stable across versions: sha1(runId|type|filename). */
function computeArtifactId(runId: RunId, type: ArtifactType, filename: string): ArtifactId {
  return sha1(`${runId}|${type}|${filename}`);
}

/**
 * Filesystem-backed ArtifactStore.
 *
 * - put(): stores content or copies from source, versions as v1..vN, computes checksum+size, returns ArtifactRef
 * - get(): resolves latest version for (runId, artifactId), returns ref and absolute contentPath
 * - list(): lists latest refs for all artifact ids under a run
 * - resolvePath(): resolves absolute path from an ArtifactRef.path
 */
export class ArtifactStore {
  readonly baseDir: string;
  private readonly repoRoot: string;

  constructor(opts?: ArtifactStoreOptions) {
    this.repoRoot = process.cwd();
    this.baseDir = opts?.baseDir ?? path.resolve(this.repoRoot, 'data', 'artifacts');
  }

  /**
   * Store an artifact (or a new version of an existing artifact).
   * Ensures directories exist, writes bytes, computes checksum and size.
   */
  async put(input: PutArtifactInput): Promise<ArtifactRef> {
    const { runId, type, metadata } = input;

    if (!input.content && !input.sourcePath) {
      throw new Error('ArtifactStore.put: either content or sourcePath must be provided');
    }

    const filename =
      input.filename ??
      (input.sourcePath ? path.basename(input.sourcePath) : defaultFilenameByType(type));

    // Prepare bytes (prefer explicit content over sourcePath)
    let bytes: Buffer;
    if (typeof input.content === 'string') {
      bytes = Buffer.from(input.content, 'utf8');
    } else if (Buffer.isBuffer(input.content)) {
      bytes = input.content;
    } else {
      // sourcePath must exist if we got here
      bytes = await fs.readFile(input.sourcePath as string);
    }

    const checksum = sha1(bytes);
    const size = bytes.byteLength;

    const artifactId = computeArtifactId(runId, type, filename);
    const artifactDir = path.join(this.baseDir, runId, artifactId);

    // Determine version
    let version = input.version;
    if (!version) {
      const latest = await latestVersion(artifactDir);
      if (!latest) {
        version = 'v1';
      } else {
        const n = parseInt(latest.slice(1), 10);
        version = `v${n + 1}`;
      }
    }

    const versionDir = path.join(artifactDir, version);
    await ensureDir(versionDir);

    const filePath = path.join(versionDir, filename);
    await fs.writeFile(filePath, bytes);

    const relPath = toRel(filePath, this.repoRoot);

    // Persist lightweight metadata for fast enumeration
    const metaRecord = {
      id: artifactId,
      type,
      filename,
      version,
      checksum,
      size,
      path: relPath,
      metadata,
      createdAt: Date.now(),
    };
    await fs.writeFile(path.join(versionDir, META_FILENAME), JSON.stringify(metaRecord, null, 2), 'utf8');

    const ref: ArtifactRef = {
      id: artifactId,
      type,
      path: relPath,
      version,
      checksum,
      size,
      metadata,
    };

    return ref;
  }

  /**
   * Resolve the latest version of an artifact for a run and return its ref and content path.
   * Reconstructs ref fields derivable from the filesystem (path, version, checksum, size).
   */
  async get(runId: RunId, artifactId: ArtifactId): Promise<{ ref: ArtifactRef; contentPath: string }> {
    const artifactDir = path.join(this.baseDir, runId, artifactId);
    const ver = (await latestVersion(artifactDir)) ?? 'v1';
    const versionDir = path.join(artifactDir, ver);
// Prefer reading lightweight metadata to avoid full file reads
try {
  const metaRaw = await fs.readFile(path.join(versionDir, META_FILENAME), 'utf8');
  const meta = JSON.parse(metaRaw) as any;

  let filename: string | undefined = typeof meta.filename === 'string' ? meta.filename : undefined;
  if (!filename) {
    const files = await readDirSafe(versionDir);
    if (files.length === 0) {
      throw new Error(`ArtifactStore.get: no files found for ${runId}/${artifactId}/${ver}`);
    }
    files.sort();
    filename = files[0];
  }
  const contentPath = path.join(versionDir, filename);
  const type: ArtifactType = (meta.type as ArtifactType) ?? inferTypeFromFilename(filename);
  const ref: ArtifactRef = {
    id: artifactId,
    type,
    path: typeof meta.path === 'string' ? meta.path : toRel(contentPath, this.repoRoot),
    version: ver,
    checksum: meta.checksum,
    size: meta.size,
    metadata: meta.metadata,
  };
  return { ref, contentPath };
} catch (err: any) {
  // Fallback for backward compatibility (no metadata or parse failure)
  if (err && (err as NodeJS.ErrnoException).code !== 'ENOENT') {
    // continue to legacy reconstruction
  }
}

// Legacy behavior: reconstruct by reading the artifact file
// Pick the (first) file in the version directory
const files = await readDirSafe(versionDir);
if (files.length === 0) {
  throw new Error(`ArtifactStore.get: no files found for ${runId}/${artifactId}/${ver}`);
}
// Deterministic order
files.sort();
const filename = files[0];
const contentPath = path.join(versionDir, filename);

const bytes = await fs.readFile(contentPath);
const checksum = sha1(bytes);
const stat = await fs.stat(contentPath);
const size = stat.size;

const type = inferTypeFromFilename(filename);

const ref: ArtifactRef = {
  id: artifactId,
  type,
  path: toRel(contentPath, this.repoRoot),
  version: ver,
  checksum,
  size,
};

return { ref, contentPath };
  }

  /**
   * List all artifact ids for a run and return their latest version refs.
   */
  async list(runId: RunId): Promise<ArtifactRef[]> {
    const runDir = path.join(this.baseDir, runId);
    const entries = await readDirSafe(runDir);
    const refs: ArtifactRef[] = [];

    for (const id of entries) {
      const artifactDir = path.join(runDir, id);
      // Ensure directory (skip files that might appear)
      let stats: import('fs').Stats;
      try {
        stats = await fs.stat(artifactDir);
      } catch {
        continue;
      }
      if (!stats.isDirectory()) continue;

      const ver = (await latestVersion(artifactDir)) ?? 'v1';
      const versionDir = path.join(artifactDir, ver);

      // Prefer metadata for fast listing
      let pushed = false;
      try {
        const metaRaw = await fs.readFile(path.join(versionDir, META_FILENAME), 'utf8');
        const meta = JSON.parse(metaRaw) as any;

        let filename: string | undefined = typeof meta.filename === 'string' ? meta.filename : undefined;
        if (!filename) {
          const files = await readDirSafe(versionDir);
          if (files.length === 0) continue;
          files.sort();
          filename = files[0];
        }
        const contentPath = path.join(versionDir, filename);

        refs.push({
          id,
          type: ((meta.type as ArtifactType) ?? inferTypeFromFilename(filename)),
          path: typeof meta.path === 'string' ? meta.path : toRel(contentPath, this.repoRoot),
          version: ver,
          checksum: meta.checksum,
          size: meta.size,
          metadata: meta.metadata,
        });
        pushed = true;
      } catch (err: any) {
        // no metadata or parse error -> fallback
        if (err && (err as NodeJS.ErrnoException).code !== 'ENOENT') {
          // ignore and fallback
        }
      }

      if (!pushed) {
        const files = await readDirSafe(versionDir);
        if (files.length === 0) continue;
        files.sort();
        const filename = files[0];
        const contentPath = path.join(versionDir, filename);

        const bytes = await fs.readFile(contentPath);
        const checksum = sha1(bytes);
        const st = await fs.stat(contentPath);

        refs.push({
          id,
          type: inferTypeFromFilename(filename),
          path: toRel(contentPath, this.repoRoot),
          version: ver,
          checksum,
          size: st.size,
        });
      }
    }

    // Deterministic order by id then version
    refs.sort((a, b) => (a.id === b.id ? String(a.version).localeCompare(String(b.version)) : a.id.localeCompare(b.id)));

    return refs;
  }

  /**
   * Resolve an absolute path to the artifact file based on ref.path and the store's baseDir.
   * If ref.path is relative, it's considered relative to the repo root.
   */
  resolvePath(ref: ArtifactRef): string {
    if (!ref.path) {
      throw new Error('ArtifactStore.resolvePath: ref.path is required');
    }
    return path.isAbsolute(ref.path) ? ref.path : path.resolve(this.repoRoot, ref.path);
  }
}
