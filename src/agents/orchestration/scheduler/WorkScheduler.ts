import {
  Assignment,
  AssignmentStatus,
  OrchestrationRequest,
  PartitionPlan,
  ProgressEvent,
  ProgressEventId,
  ProgressEventType,
  WorkCategory,
  WorkUnit,
  WorkUnitId,
  Intent,
  RunId,
} from '../types';
import { AgentRegistry } from '../agents/AgentRegistry';
import { ContextPackager } from '../context/ContextPackager';
import { AssignmentManager } from '../assign/AssignmentManager';
import { deriveContentFingerprint } from '../journal/canonicalize';
import { getConfig } from '../../../utils/config';
import { JsonLedger } from '../ledger/JsonLedger';
import ConcurrencyManager, {
  ParticipantSpec as ConcurrencyParticipantSpec,
  PartitionDescriptor as ConcurrencyPartitionDescriptor,
  PartitionPlan as ConcurrencyPartitionPlan,
  PartitionResult as ConcurrencyPartitionResult,
} from '../concurrency/ConcurrencyManager';
import Diff<PERSON><PERSON><PERSON>, { ConflictEntry } from '../concurrency/DiffJoiner';
import ConflictResolver, { ConflictResolutionContext, ResolvedConflict } from '../concurrency/ConflictResolver';
import { RunJournal } from '../journal/RunJournal';

/**
 * A function that executes a concrete Assignment and yields ProgressEvents.
 * Implementations should continuously yield updates until completion or failure.
 */
export type AssignmentExecutor = (assignment: Assignment) => AsyncGenerator<ProgressEvent> | Promise<AsyncGenerator<ProgressEvent>>;

/**
 * Scheduler runtime configuration.
 */
export interface WorkSchedulerOptions {
  /** Global cap on concurrent in-flight assignments (default: Infinity). */
  maxInFlight?: number;
  /**
   * Per-category concurrency caps.
   * Defaults: coding:3, review:2, debug:2, research:2
   */
  perCategoryConcurrency?: Partial<Record<WorkCategory, number>>;
  /**
   * Retry policy:
   * - maxRetries: number of retries after the initial attempt
   * - baseDelayMs: initial backoff delay in ms
   * - backoffFactor: exponential multiplier
   * - maxDelayMs: maximum backoff delay
   *
   * Defaults: { maxRetries: 2, baseDelayMs: 1000, backoffFactor: 2, maxDelayMs: 15000 }
   */
  retry?: {
    maxRetries: number;
    baseDelayMs: number;
    backoffFactor: number;
    maxDelayMs: number;
  };
  /** Heartbeat cadence while a unit is running (default 2000ms). */
  heartbeatIntervalMs?: number;
  /**
   * Per-unit inactivity timeout: if no executor events arrive within this window,
   * the attempt fails with timeout (default 300000ms = 5 minutes).
   */
  timeoutMsPerUnit?: number;

  /** Required collaborators */
  agentRegistry: AgentRegistry;
  contextPackager: ContextPackager;
  executor: AssignmentExecutor;

  /** Optional convenience hook to observe each emitted ProgressEvent (e.g., write Ledger). */
  onProgress?: (evt: ProgressEvent) => void | Promise<void>;

  /** Two-phase commit + concurrency orchestration hooks. */
  twoPhase?: WorkSchedulerTwoPhaseOptions;
}

export interface TwoPhasePartitionContext {
  runId: RunId;
  parentTxnId: string;
  taskId: string;
  workUnit: WorkUnit;
  assignment: Assignment;
}

export interface WorkSchedulerTwoPhaseOptions {
  enabled: boolean;
  parallelEnabled: boolean;
  maxParallel: number;
  conflictPolicy: 'resolve-prefer-older' | 'abort';
  journal: RunJournal;
  deriveParticipants: (ctx: { workUnit: WorkUnit; assignment: Assignment }) => Promise<ConcurrencyParticipantSpec[]> | ConcurrencyParticipantSpec[];
  executePartition: (
    partition: ConcurrencyPartitionDescriptor,
    ctx: TwoPhasePartitionContext,
  ) => Promise<ConcurrencyPartitionResult>;
  resolveConflictsWithAgent?: (conflict: ConflictEntry, context: ConflictResolutionContext) => Promise<ResolvedConflict | null>;
}

const CODE_CHANGE_INTENTS = new Set<Intent>([
  Intent.Implement,
  Intent.Refactor,
  Intent.Fix,
]);

/**
 * WorkScheduler
 *
 * Executes a PartitionPlan as a dependency-aware DAG using topological scheduling with:
 * - Global and per-category concurrency gating
 * - Agent selection and capacity acquisition via AgentRegistry
 * - Context packaging per assignment via ContextPackager
 * - Retries with exponential backoff and reassignment
 * - Periodic heartbeats and inactivity timeouts per running unit
 * - Cooperative cancellation (cancel() marks running units cancelled and halts new scheduling)
 *
 * Lifecycle and events:
 * - For each attempt: emit Started(type=started, message='attempt X/Y'), then forward executor events.
 * - While running: emit Heartbeat(type=heartbeat) every heartbeatIntervalMs (resets heartbeat timer on any incoming executor event).
 * - Timeout: if no executor event within timeoutMsPerUnit, emit Failed(type=failed, error.code='timeout').
 * - Success: when executor stream completes without failure, emit ReviewRequest(type=review-request) if category=coding, then emit Done(type=done).
 *
 * Topology:
 * - indegree computed from plan.adjacency (absent keys => no deps). Only successful units unblock dependents.
 *
 * Cancellation:
 * - cancel() sets flag, immediately marks any running unit as Cancelled(type=cancelled) and stops scheduling new ones.
 */
export class WorkScheduler {
  private readonly agentRegistry: AgentRegistry;
  private readonly contextPackager: ContextPackager;
  private readonly executor: AssignmentExecutor;
  private readonly onProgress?: (evt: ProgressEvent) => void | Promise<void>;

  private readonly maxInFlight: number;
  private readonly perCategoryConcurrency: Record<WorkCategory, number>;
  private readonly retry: { maxRetries: number; baseDelayMs: number; backoffFactor: number; maxDelayMs: number };
  private readonly heartbeatIntervalMs: number;
  private readonly timeoutMsPerUnit: number;

  // Resilience controls
  private readonly maxHandoffDepth: number;
  private readonly loopGuardEnabled: boolean;
  private readonly fingerprintAgentHistory = new Map<string, string[]>();

  // Two-phase commit orchestration runtime
  private readonly twoPhase?: WorkSchedulerTwoPhaseOptions;
  private currentRunId?: RunId;

  private cancelled = false;

  constructor(opts: WorkSchedulerOptions) {
    const DEFAULT_PER_CAT: Record<WorkCategory, number> = {
      [WorkCategory.Coding]: 3,
      [WorkCategory.Review]: 2,
      [WorkCategory.Debug]: 2,
      [WorkCategory.Research]: 2,
    };
    const retryDefaults = { maxRetries: 2, baseDelayMs: 1000, backoffFactor: 2, maxDelayMs: 15000 };

    this.agentRegistry = opts.agentRegistry;
    this.contextPackager = opts.contextPackager;
    this.executor = opts.executor;
    this.onProgress = opts.onProgress;

    this.maxInFlight = opts.maxInFlight ?? Number.POSITIVE_INFINITY;
    this.perCategoryConcurrency = {
      ...DEFAULT_PER_CAT,
      ...(opts.perCategoryConcurrency ?? {}),
    };
    this.retry = { ...retryDefaults, ...(opts.retry ?? {}) };
    this.heartbeatIntervalMs = opts.heartbeatIntervalMs ?? 2000;
    this.timeoutMsPerUnit = opts.timeoutMsPerUnit ?? 300000;

    // Resilience config (static defaults if not provided)
    const r = getConfig().orchestrator?.resilience ?? { maxHandoffDepth: 3, enableLoopGuard: false };
    this.maxHandoffDepth = r.maxHandoffDepth ?? 3;
    this.loopGuardEnabled = r.enableLoopGuard ?? false;

    this.twoPhase = opts.twoPhase && opts.twoPhase.enabled ? opts.twoPhase : undefined;
    this.currentRunId = undefined;
  }

  /** Cooperative cancellation: prevents further scheduling and marks any running attempts as cancelled. */
  cancel(): void {
    this.cancelled = true;
    // Active run() loop will observe this flag and cancel all running units.
  }

  /**
   * Execute a plan as a DAG with dependency-aware topological scheduling.
   * Concurrency caps are applied globally and per category.
   * Yields ProgressEvents for all units including heartbeats, retries, timeouts, cancellation, and completion.
   */
  async *run(plan: PartitionPlan, request: OrchestrationRequest): AsyncGenerator<ProgressEvent> {
    this.currentRunId = plan.requestId ?? request?.id ?? this.currentRunId ?? `run-${Date.now()}`;
    const queue = new AsyncQueue<ProgressEvent>();
    const safeEmit = async (evt: ProgressEvent) => {
      queue.push(evt);
      try {
        if (this.onProgress) await this.onProgress(evt);
      } catch {
        // Swallow observer errors to avoid stalling the scheduler.
      }
    };

    const { indegree, children } = topoInit(plan.adjacency, plan.units);
    const unitsById = new Map<WorkUnitId, WorkUnit>(plan.units.map((u) => [u.id, u]));
    const ready: WorkUnitId[] = [];
    const done = new Set<WorkUnitId>();
    const failed = new Set<WorkUnitId>();

    // Initialize ready queue with indegree 0
    for (const u of plan.units) {
      if ((indegree.get(u.id) ?? 0) === 0) ready.push(u.id);
    }

    // Runtime state
    let globalInFlight = 0;
    const catRunning = new Map<WorkCategory, number>();
    const attempts = new Map<WorkUnitId, number>();
    const maxAttemptsAllowed = this.retry.maxRetries + 1; // initial + retries

    // Running attempts management
    type RunningInfo = {
      cancel: () => void;
      promise: Promise<AttemptOutcome>;
      agentId: string;
      category: WorkCategory;
    };
    const running = new Map<WorkUnitId, RunningInfo>();

    // Track scheduled retries (to avoid double scheduling)
    const retryTimers = new Map<WorkUnitId, NodeJS.Timeout>();

    // Core scheduler loop
    const schedulerLoop = (async () => {
      while (true) {
        // Cancellation handling: cancel all running attempts and drain
        if (this.cancelled) {
          for (const [, info] of running.entries()) {
            // Invoke per-attempt cancel routine (emits cancelled event)
            info.cancel();
          }
          // Stop scheduling: clear pending retries and ready queue to allow loop exit
          for (const t of retryTimers.values()) clearTimeout(t);
          retryTimers.clear();
          ready.length = 0;
        }

        // Attempt to schedule from ready while respecting caps
        let madeProgress = false;

        if (!this.cancelled) {
          let guard = 0;
          while (guard++ < plan.units.length) {
            if (ready.length === 0) break;
            if (!canSchedule(globalInFlight, this.maxInFlight)) break;

            const wuId = ready.shift()!;
            if (done.has(wuId) || failed.has(wuId) || running.has(wuId)) {
              continue;
            }
            const wu = unitsById.get(wuId);
            if (!wu) continue;

            const catCap = decideCategoryCap(wu.category, this.perCategoryConcurrency);
            const currCat = catRunning.get(wu.category) ?? 0;
            if (currCat >= catCap) {
              // Defer due to category cap; push to back
              ready.push(wuId);
              break;
            }

            // Agent selection
            const agent = this.agentRegistry.selectFor(wu, undefined, request);
            if (!agent) {
              // No agent currently available; defer
              ready.push(wuId);
              break;
            }
            // Acquire capacity
            const acquired = this.agentRegistry.acquire(agent.id);
            if (!acquired) {
              // Could not acquire concurrently; defer
              ready.push(wuId);
              break;
            }

            // Schedule attempt
            const attemptNo = (attempts.get(wuId) ?? 0) + 1;
            attempts.set(wuId, attemptNo);

            // Loop guard and handoff depth check based on content fingerprint (requires file scopes)
            const fp = computeFingerprint(request, wu);

            // Tracker dedup cooling-window enforcement via Ledger
            if (fp) {
              try {
                const rconf = getConfig().orchestrator?.resilience ?? {};
                const ledger = await JsonLedger.open(request.id);
                const ded = await ledger.recordOrDedupTracker(wu.id, fp, { coolingWindowMs: rconf.coolingWindowMs ?? 10_000 });
                if (ded.coolingRejected) {
                  const errEvt = normalizeEvent(
                    {
                      type: ProgressEventType.Failed,
                      workUnitId: wuId,
                      message: `cooling window active for content fingerprint (task:${wuId})`,
                      error: { code: 'cooling_window', message: 'cooling_window' },
                    },
                    wuId,
                    attemptNo,
                    agent.id,
                  );
                  await safeEmit(errEvt);
                  failed.add(wuId);
                  madeProgress = true;
                  continue;
                }
              } catch {
                // ignore ledger failures for scheduler robustness
              }
            }

            if (this.loopGuardEnabled && fp) {
              const hist = this.fingerprintAgentHistory.get(fp) ?? [];
              const last2 = hist.slice(-2);
              const nextDepth = hist.length + 1;
              const loopDetected = last2.length === 2 && last2[0] !== last2[1] && last2[0] === agent.id;
              const depthExceeded = nextDepth > this.maxHandoffDepth;
              if (loopDetected || depthExceeded) {
                const code = loopDetected ? 'loop_detected' : 'handoff_depth_exceeded';
                const errEvt = normalizeEvent(
                  {
                    type: ProgressEventType.Failed,
                    workUnitId: wuId,
                    message: loopDetected
                      ? 'A→B→A loop detected on content fingerprint'
                      : `handoff depth exceeded (max=${this.maxHandoffDepth})`,
                    error: { code, message: code },
                  },
                  wuId,
                  attemptNo,
                  agent.id,
                );
                await safeEmit(errEvt);
                failed.add(wuId);
                madeProgress = true;
                continue;
              }
            }

            // Build context
            let contextPkg: import('../types').ContextPackage;
            try {
              contextPkg = await this.contextPackager.package(wu, request);
            } catch (e: any) {
              // Treat packaging failure as attempt failure, release capacity and schedule retry
              this.agentRegistry.release(agent.id);
              const errEvt = normalizeEvent(
                {
                  type: ProgressEventType.Failed,
                  workUnitId: wuId,
                  message: 'context packaging failed',
                  error: { code: 'context_packaging_error', message: String(e?.message ?? e) },
                },
                wuId,
                attemptNo,
                agent.id,
              );
              await safeEmit(errEvt);

              scheduleRetryOrFail(
                wu,
                attemptNo,
                attempts,
                maxAttemptsAllowed,
                retryTimers,
                ready,
                failed,
                this.retry,
              );
              madeProgress = true;
              continue;
            }

            // Create assignment and kick executor
            const instructionBuilder = new AssignmentManager();
            const instructions = instructionBuilder.buildInstruction(wu, agent, contextPkg, request);
            const assignment: Assignment = {
              id: `asgn_${wuId}_${attemptNo}_${Math.random().toString(36).slice(2, 8)}`,
              workUnitId: wuId,
              agentId: agent.id,
              contextPackage: contextPkg,
              instructions,
              startAt: now(),
              status: AssignmentStatus.Running, // transitions: queued -> assigned -> running
              attempts: attemptNo,
            };

            // Emit started before consuming executor stream
            const startedEvt = normalizeEvent(
              {
                type: ProgressEventType.Started,
                workUnitId: wuId,
                message: `attempt ${attemptNo}/${maxAttemptsAllowed}`,
                percentComplete: attemptNo === 1 ? 0 : undefined,
              },
              wuId,
              attemptNo,
              assignment.agentId,
            );
            await safeEmit(startedEvt);

            // Update counters and track running
            globalInFlight += 1;
            catRunning.set(wu.category, currCat + 1);

            const { promise, cancel } = this.runAttempt(wu, assignment, attemptNo, safeEmit);
            running.set(wuId, { promise, cancel, agentId: agent.id, category: wu.category });
            madeProgress = true;

            // Track fingerprint agent sequence for loop guard
            if (fp) {
              const hist = this.fingerprintAgentHistory.get(fp) ?? [];
              hist.push(agent.id);
              const maxKeep = this.maxHandoffDepth + 1;
              if (hist.length > maxKeep) hist.splice(0, hist.length - maxKeep);
              this.fingerprintAgentHistory.set(fp, hist);
            }

            // When attempt finishes, finalize and update topology
            (async () => {
              const outcome = await promise.catch(() => ({ outcome: 'failed' as AttemptResult }));
              // Release agent capacity
              this.agentRegistry.release(agent.id);

              // Update counters
              globalInFlight = Math.max(0, globalInFlight - 1);
              catRunning.set(wu.category, Math.max(0, (catRunning.get(wu.category) ?? 1) - 1));
              running.delete(wuId);

              if (this.cancelled) {
                // On global cancel, we do not schedule retries or unlock dependents
                return;
              }

              if (outcome.outcome === 'success') {
                done.add(wuId);
                // Unblock dependents
                const kids = children.get(wuId);
                if (kids) {
                  for (const child of kids) {
                    const cur = indegree.get(child) ?? 0;
                    const next = Math.max(0, cur - 1);
                    indegree.set(child, next);
                    if (next === 0 && !done.has(child) && !failed.has(child)) {
                      ready.push(child);
                    }
                  }
                }
              } else if (outcome.outcome === 'cancelled') {
                failed.add(wuId); // treated as terminal; do not unlock dependents
              } else if (outcome.outcome === 'timeout' || outcome.outcome === 'failed') {
                // Retry if attempts remain
                const currentAttempt = attempts.get(wuId) ?? attemptNo;
                scheduleRetryOrFail(
                  wu,
                  currentAttempt,
                  attempts,
                  maxAttemptsAllowed,
                  retryTimers,
                  ready,
                  failed,
                  this.retry,
                );
              }
            })().catch(() => {
              // Swallow finalize errors
            });
          }
        }

        // Exit condition: no ready work, nothing running, no scheduled retries, or cancelled drained
        if (
          running.size === 0 &&
          ready.length === 0 &&
          Array.from(retryTimers.values()).length === 0
        ) {
          break;
        }

        // Yield control briefly if no immediate scheduling progress
        if (!madeProgress) {
          await sleep(50);
        }
      }

      // Ensure all timers cleared
      for (const t of retryTimers.values()) {
        clearTimeout(t);
      }
      retryTimers.clear();

      // If cancelled and there were running units, they were asked to cancel; wait a moment to allow cancelled events to flush
      if (this.cancelled) {
        await sleep(25);
      }

      // End the event queue
      queue.end();
    })();

    // Drain queue for caller
    for await (const evt of queue) {
      yield evt;
    }

    // Ensure scheduler loop settled
    await schedulerLoop;

    this.currentRunId = undefined;
  }

  // ===== Attempt Execution =====

  private runAttempt(
    wu: WorkUnit,
    assignment: Assignment,
    attemptNo: number,
    safeEmit: (evt: ProgressEvent) => Promise<void>,
  ): { promise: Promise<AttemptOutcome>; cancel: () => void } {
    let ended = false;
    let lastPercent: number | undefined = assignment.attempts && assignment.attempts > 1 ? undefined : 0;
    let inactivityTimer: NodeJS.Timeout | undefined;
    let heartbeatTimer: NodeJS.Timeout | undefined;
    let currentGen: AsyncGenerator<ProgressEvent> | undefined;

    const clearTimers = () => {
      if (inactivityTimer) clearTimeout(inactivityTimer);
      if (heartbeatTimer) clearInterval(heartbeatTimer);
      inactivityTimer = undefined;
      heartbeatTimer = undefined;
    };

    const endOnce = async (res: AttemptOutcome): Promise<AttemptOutcome> => {
      if (ended) return res;
      ended = true;
      clearTimers();
      return res;
    };

    const resetInactivity = () => {
      if (inactivityTimer) clearTimeout(inactivityTimer);
      inactivityTimer = setTimeout(async () => {
        if (ended) return;
        // Timeout
        const evt = normalizeEvent(
          {
            type: ProgressEventType.Failed,
            workUnitId: wu.id,
            message: 'assignment timed out (no progress)',
            error: { code: 'timeout', message: `No progress for ${this.timeoutMsPerUnit}ms` },
          },
          wu.id,
          attemptNo,
          assignment.agentId,
        );
        await safeEmit(evt);
        // Emit terminal 'timeout' status (as failed event above already indicates failure)
        await endOnce({ outcome: 'timeout' });
        try {
          await currentGen?.return?.(undefined as any);
        } catch {
          // ignore
        }
      }, this.timeoutMsPerUnit);
    };

    const startHeartbeat = () => {
      if (heartbeatTimer) clearInterval(heartbeatTimer);
      heartbeatTimer = setInterval(async () => {
        if (ended) return;
        const hb = normalizeEvent(
          {
            type: ProgressEventType.Heartbeat,
            workUnitId: wu.id,
            percentComplete: lastPercent,
          },
          wu.id,
          attemptNo,
          assignment.agentId,
        );
        await safeEmit(hb);
      }, this.heartbeatIntervalMs);
    };

    let cancelRequested = false;
    const cancel = () => {
      if (ended || cancelRequested) return;
      cancelRequested = true;
      // Emit cancelled and end
      const evt = normalizeEvent(
        {
          type: ProgressEventType.Cancelled,
          workUnitId: wu.id,
          message: 'cancelled',
        },
        wu.id,
        attemptNo,
        assignment.agentId,
      );
      // Fire-and-forget to avoid deadlock; serialization is preserved by queue
      void safeEmit(evt)
        .then(() => endOnce({ outcome: 'cancelled' }))
        .catch(() => endOnce({ outcome: 'cancelled' }))
        .finally(() => {
          void currentGen?.return?.(undefined as any);
        });
    };

    const promise = (async (): Promise<AttemptOutcome> => {
      try {
        // Executor start
        let gen: AsyncGenerator<ProgressEvent>;
        try {
          const twoPhaseGen = await this.maybeCreateTwoPhaseGenerator(wu, assignment, attemptNo);
          if (twoPhaseGen) {
            gen = twoPhaseGen;
          } else {
            const maybe = this.executor(assignment);
            gen = (await Promise.resolve(maybe)) as AsyncGenerator<ProgressEvent>;
          }
          currentGen = gen;
        } catch (e: any) {
          const evt = normalizeEvent(
            {
              type: ProgressEventType.Failed,
              workUnitId: wu.id,
              message: 'executor threw',
              error: { code: 'executor_error', message: String(e?.message ?? e), stack: e?.stack },
            },
            wu.id,
            attemptNo,
            assignment.agentId,
          );
          await safeEmit(evt);
          return endOnce({ outcome: 'failed' });
        }

        // Start timers
        resetInactivity();
        startHeartbeat();

        let failed = false;
        let cancelled = false;

        for await (const raw of gen) {
          if (ended) break;
          if (cancelRequested) {
            cancelled = true;
            break;
          }

          // Normalize and forward
          const forwarded = normalizeEvent(raw, wu.id, attemptNo, assignment.agentId);
          if (forwarded.percentComplete !== undefined) lastPercent = forwarded.percentComplete;

          // Reset heartbeat cadence on any incoming event
          startHeartbeat();

          // Reset inactivity on executor-originated events only
          resetInactivity();

          // Detect terminal signals within stream
          if (forwarded.type === ProgressEventType.Failed) {
            failed = true;
          }
          if (forwarded.type === ProgressEventType.Cancelled) {
            cancelled = true;
          }

          await safeEmit(forwarded);

          if (failed || cancelled) {
            break;
          }
        }

        if (ended) {
          return { outcome: 'failed' }; // already handled by timeout/cancel
        }
        if (cancelRequested || cancelled) {
          return endOnce({ outcome: 'cancelled' });
        }
        if (failed) {
          return endOnce({ outcome: 'failed' });
        }

        // Successful completion: emit review-request for coding, then done
        if (wu.category === WorkCategory.Coding) {
          const rr = normalizeEvent(
            {
              type: ProgressEventType.ReviewRequest,
              workUnitId: wu.id,
              message: 'ready for review',
              percentComplete: 100,
            },
            wu.id,
            attemptNo,
            assignment.agentId,
          );
          await safeEmit(rr);
        }
        const doneEvt = normalizeEvent(
          {
            type: ProgressEventType.Done,
            workUnitId: wu.id,
            percentComplete: 100,
            message: 'completed',
          },
          wu.id,
          attemptNo,
          assignment.agentId,
        );
        await safeEmit(doneEvt);
        return endOnce({ outcome: 'success' });
      } finally {
        clearTimers();
      }
    })();

    return { promise, cancel };
  }

  private shouldUseTwoPhase(wu: WorkUnit): boolean {
    if (!this.twoPhase || !this.twoPhase.enabled) return false;
    if (!wu || wu.category !== WorkCategory.Coding) return false;
    if (!wu.intent) return false;
    return CODE_CHANGE_INTENTS.has(wu.intent as Intent);
  }

  private async maybeCreateTwoPhaseGenerator(
    wu: WorkUnit,
    assignment: Assignment,
    attemptNo: number,
  ): Promise<AsyncGenerator<ProgressEvent> | null> {
    if (!this.shouldUseTwoPhase(wu)) return null;
    const runId = this.currentRunId ?? `run-${Date.now()}`;
    const twoPhase = this.twoPhase;
    if (!twoPhase) return null;

    const participants = await Promise.resolve(twoPhase.deriveParticipants({ workUnit: wu, assignment }));
    if (!participants || participants.length === 0) return null;

    const txnId = `${wu.id}:txn:${Date.now()}`;
    const diffJoiner = new DiffJoiner();
    const conflictResolver = new ConflictResolver({
      policy: twoPhase.conflictPolicy,
      resolveWithAgent: twoPhase.resolveConflictsWithAgent,
    });
    const manager = new ConcurrencyManager({
      txnId,
      maxParallel: twoPhase.parallelEnabled ? Math.max(1, twoPhase.maxParallel) : 1,
      diffJoiner,
      conflictResolver,
    });

    const partitionPlan: ConcurrencyPartitionPlan = manager.planPartitions(participants);
    if (partitionPlan.partitions.length === 0) return null;

    const journal = twoPhase.journal;
    const joinId = `${txnId}:join`;

    const runPartition = async (partition: ConcurrencyPartitionDescriptor): Promise<ConcurrencyPartitionResult> => {
      const ctx: TwoPhasePartitionContext = {
        runId,
        parentTxnId: txnId,
        taskId: wu.id,
        workUnit: wu,
        assignment,
      };
      return twoPhase.executePartition(partition, ctx);
    };

    const joinSeq = (() => {
      let value = 1;
      return () => value++;
    })();

    const agentId = assignment.agentId;
    const workUnitId = wu.id;

    const self = this;

    return (async function* twoPhaseGenerator(): AsyncGenerator<ProgressEvent> {
      yield normalizeEvent(
        {
          type: ProgressEventType.Started,
          workUnitId,
          message: 'two-phase commit initiated',
          percentComplete: 5,
        },
        workUnitId,
        attemptNo,
        agentId,
      );

      try {
        const partitionResults = await manager.executePartitionsInParallel(partitionPlan, {
          runPartition,
        });

        const aborted = partitionResults.find((res) => res.decision !== 'commit');
        if (aborted) {
          await journal.recordJoinAborted({
            runId,
            taskId: wu.id,
            txnId,
            joinId,
            partitionIds: partitionPlan.partitions.map((p) => p.id),
            reason: 'partition_aborted',
            seqNo: joinSeq(),
          });
          throw new Error('partition_aborted');
        }

        await journal.recordJoinStarted({
          runId,
          taskId: wu.id,
          txnId,
          joinId,
          partitionIds: partitionPlan.partitions.map((p) => p.id),
          seqNo: joinSeq(),
        });

        const joinResult = await manager.join(partitionPlan, partitionResults);

        await journal.recordJoinMerged({
          runId,
          taskId: wu.id,
          txnId,
          joinId,
          partitionIds: partitionPlan.partitions.map((p) => p.id),
          mergedPatchPlanHash: joinResult.joinOutcome.mergedPatchPlanHash ?? undefined,
          conflictCount: joinResult.joinOutcome.conflicts.length,
          seqNo: joinSeq(),
        });

        if (joinResult.joinOutcome.conflicts.length > 0) {
          await journal.recordJoinConflict({
            runId,
            taskId: wu.id,
            txnId,
            joinId,
            partitionIds: partitionPlan.partitions.map((p) => p.id),
            conflictCount: joinResult.joinOutcome.conflicts.length,
            hashes: joinResult.joinOutcome.conflicts.map((c) => c.hash),
            seqNo: joinSeq(),
          });

          const resolution = joinResult.resolution;
          if (resolution && resolution.unresolvedConflicts.length === 0) {
            await journal.recordJoinResolved({
              runId,
              taskId: wu.id,
              txnId,
              joinId,
              partitionIds: partitionPlan.partitions.map((p) => p.id),
              resolved: true,
              mergedPatchPlanHash: resolution.mergedPatchPlanHash ?? undefined,
              notes: resolution.notes,
              seqNo: joinSeq(),
            });
          } else {
            await journal.recordJoinAborted({
              runId,
              taskId: wu.id,
              txnId,
              joinId,
              partitionIds: partitionPlan.partitions.map((p) => p.id),
              reason: 'conflict_unresolved',
              seqNo: joinSeq(),
            });
            throw new Error('join_conflict_unresolved');
          }
        }

        yield normalizeEvent(
          {
            type: ProgressEventType.ReviewRequest,
            workUnitId,
            message: 'changes ready for review',
            percentComplete: 90,
          },
          workUnitId,
          attemptNo,
          agentId,
        );

        yield normalizeEvent(
          {
            type: ProgressEventType.Done,
            workUnitId,
            message: 'two-phase commit complete',
            percentComplete: 100,
          },
          workUnitId,
          attemptNo,
          agentId,
        );
      } catch (error: any) {
        const message = error?.message ?? 'two_phase_error';
        yield normalizeEvent(
          {
            type: ProgressEventType.Failed,
            workUnitId,
            message,
            error: { code: 'two_phase_error', message },
          },
          workUnitId,
          attemptNo,
          agentId,
        );
        throw error;
      }
    })();
  }
}

/** ===== Helpers ===== */

type AttemptResult = 'success' | 'failed' | 'timeout' | 'cancelled';
type AttemptOutcome = { outcome: AttemptResult };

function now(): number {
  return Date.now();
}

function sleep(ms: number): Promise<void> {
  return new Promise((res) => setTimeout(res, ms));
}

function canSchedule(globalInFlight: number, maxInFlight: number): boolean {
  return globalInFlight < maxInFlight;
}

function decideCategoryCap(cat: WorkCategory, caps: Record<WorkCategory, number>): number {
  return caps[cat] ?? Number.POSITIVE_INFINITY;
}

function generateEventId(wuId: WorkUnitId, attemptNo: number, type: ProgressEventType, ts: number): ProgressEventId {
  return `wu:${wuId}:att:${attemptNo}:${type}:${ts}`;
}

function normalizeEvent(
  base: Partial<ProgressEvent> & { type: ProgressEventType; workUnitId: WorkUnitId },
  wuId: WorkUnitId,
  attemptNo: number,
  agentId?: string,
): ProgressEvent {
  const ts = base.timestamp ?? now();
  const evt: ProgressEvent = {
    eventId: base.eventId ?? generateEventId(wuId, attemptNo, base.type, ts),
    type: base.type,
    workUnitId: base.workUnitId ?? wuId,
    agentId,
    percentComplete: base.percentComplete,
    message: base.message,
    artifacts: base.artifacts,
    metrics: base.metrics,
    error: base.error,
    timestamp: ts,
  };
  return evt;
}

function topoInit(
  adjacency: PartitionPlan['adjacency'] | undefined,
  units: WorkUnit[],
): { indegree: Map<WorkUnitId, number>; children: Map<WorkUnitId, Set<WorkUnitId>> } {
  const indegree = new Map<WorkUnitId, number>();
  const children = new Map<WorkUnitId, Set<WorkUnitId>>();
  const ids = new Set(units.map((u) => u.id));
  for (const id of ids) {
    indegree.set(id, 0);
    children.set(id, new Set());
  }
  if (adjacency) {
    for (const [from, outs] of Object.entries(adjacency)) {
      if (!ids.has(from)) continue;
      for (const to of outs ?? []) {
        if (!ids.has(to)) continue;
        children.get(from)!.add(to);
        indegree.set(to, (indegree.get(to) ?? 0) + 1);
      }
    }
  }
  return { indegree, children };
}

function scheduleRetryOrFail(
  wu: WorkUnit,
  currentAttempt: number,
  attempts: Map<WorkUnitId, number>,
  maxAttemptsAllowed: number,
  retryTimers: Map<WorkUnitId, NodeJS.Timeout>,
  ready: WorkUnitId[],
  failed: Set<WorkUnitId>,
  retryCfg: { maxRetries: number; baseDelayMs: number; backoffFactor: number; maxDelayMs: number },
): void {
  if (currentAttempt < maxAttemptsAllowed) {
    const retryIndex = currentAttempt; // 1-based current attempt; exponent = attempt-1
    const rawDelay = retryCfg.baseDelayMs * Math.pow(retryCfg.backoffFactor, retryIndex - 1);
    const delay = Math.min(retryCfg.maxDelayMs, Math.max(0, Math.floor(rawDelay)));
    const t = setTimeout(() => {
      attempts.set(wu.id, currentAttempt); // keep last attempt count; next schedule increments
      ready.push(wu.id);
      retryTimers.delete(wu.id);
    }, delay);
    retryTimers.set(wu.id, t);
  } else {
    failed.add(wu.id);
  }
}

function computeFingerprint(request: OrchestrationRequest, wu: WorkUnit): string | null {
  const files = wu.scope?.files ?? [];
  if (!files || files.length === 0) return null;
  const cp: any = {
    goal: request?.objective ?? '',
    scope: { fileScopes: files },
  };
  try {
    return deriveContentFingerprint(cp);
  } catch {
    return null;
  }
}

/** Minimal async queue used to multiplex per-unit events to the caller. */
class AsyncQueue<T> implements AsyncIterable<T> {
  private items: T[] = [];
  private resolvers: Array<(value: IteratorResult<T>) => void> = [];
  private done = false;

  push(item: T) {
    if (this.done) return;
    if (this.resolvers.length > 0) {
      const resolve = this.resolvers.shift()!;
      resolve({ value: item, done: false });
    } else {
      this.items.push(item);
    }
  }

  end() {
    if (this.done) return;
    this.done = true;
    while (this.resolvers.length > 0) {
      const resolve = this.resolvers.shift()!;
      resolve({ value: undefined as any, done: true });
    }
  }

  async next(): Promise<IteratorResult<T>> {
    if (this.items.length > 0) {
      const value = this.items.shift()!;
      return { value, done: false };
    }
    if (this.done) return { value: undefined as any, done: true };
    return new Promise<IteratorResult<T>>((resolve) => {
      this.resolvers.push(resolve);
    });
  }

  [Symbol.asyncIterator](): AsyncIterator<T> {
    return {
      next: () => this.next(),
    };
  }
}
