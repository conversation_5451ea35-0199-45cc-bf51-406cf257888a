import { parseUnifiedDiffText, stripABPrefix, serializeUnifiedDiffFromMap, UnifiedHunk } from '../../../utils/unifiedDiff';
import { canonicalizePlan, hashPlan } from '../journal/canonicalize';
import { sha256Hex } from '../journal/types';

export interface DiffJoinerOptions {
  omitEmptyMerges?: boolean;
}

export interface PartitionJoinArtifact {
  partitionId: string;
  participantIds: string[];
  previewDiff?: string | null;
}

export interface ConflictDiffEntry {
  participantId: string;
  partitionId: string;
  hunks: UnifiedHunk[];
  diffText: string;
}

export interface ConflictEntry {
  filePath: string;
  participants: string[];
  partitionIds: string[];
  diffs: ConflictDiffEntry[];
  hash: string;
}

export interface DiffJoinOutcome {
  mergedPatchPlan: any | null;
  mergedPreviewDiff: string | null;
  mergedPatchPlanHash: string | null;
  conflicts: ConflictEntry[];
}

export class DiffJoiner {
  private readonly omitEmpty: boolean;

  constructor(opts: DiffJoinerOptions = {}) {
    this.omitEmpty = opts.omitEmptyMerges ?? true;
  }

  join(artifacts: PartitionJoinArtifact[]): DiffJoinOutcome {
    if (artifacts.length === 0) {
      return { mergedPatchPlan: null, mergedPreviewDiff: null, mergedPatchPlanHash: null, conflicts: [] };
    }

    const fileMap = new Map<string, ConflictDiffEntry[]>();

    for (const artifact of artifacts) {
      if (!artifact.previewDiff) continue;
      const parsed = parseUnifiedDiffText(artifact.previewDiff);
      for (const file of parsed.files) {
        const normalized = stripABPrefix(file.path);
        const entry = fileMap.get(normalized) ?? [];
        const diffText = serializeUnifiedDiffFromMap(new Map([[normalized, { hunks: file.hunks }]]));
        entry.push({
          participantId: artifact.participantIds[0] ?? artifact.partitionId,
          partitionId: artifact.partitionId,
          hunks: file.hunks,
          diffText,
        });
        fileMap.set(normalized, entry);
      }
    }

    const mergedDiffMap = new Map<string, { hunks: UnifiedHunk[] }>();
    const mergedFiles: Array<{ path: string; diff: string }> = [];
    const conflicts: ConflictEntry[] = [];

    const sortedFiles = Array.from(fileMap.entries()).sort(([a], [b]) => (a < b ? -1 : a > b ? 1 : 0));

    for (const [filePath, entries] of sortedFiles) {
      if (entries.length <= 1) {
        const chosen = entries[0];
        if (!chosen) continue;
        if (this.omitEmpty && !this.hasMeaningfulLines(chosen.hunks)) continue;
        mergedDiffMap.set(filePath, { hunks: chosen.hunks });
        mergedFiles.push({ path: filePath, diff: chosen.diffText });
        continue;
      }

      const hashSource = entries.map((e) => ({
        participantId: e.participantId,
        partitionId: e.partitionId,
        filePath,
        diff: e.diffText,
      }));
      const hash = sha256Hex(canonicalizePlan(hashSource));
      conflicts.push({
        filePath,
        participants: entries.map((e) => e.participantId),
        partitionIds: entries.map((e) => e.partitionId),
        diffs: entries,
        hash,
      });
    }

    const mergedPreviewDiff = mergedDiffMap.size > 0 ? serializeUnifiedDiffFromMap(mergedDiffMap) : null;
    const mergedPatchPlan = mergedFiles.length > 0 ? { files: mergedFiles } : null;
    const mergedPatchPlanHash = mergedPatchPlan ? hashPlan(mergedPatchPlan) : null;

    return {
      mergedPatchPlan,
      mergedPreviewDiff,
      mergedPatchPlanHash,
      conflicts,
    };
  }

  private hasMeaningfulLines(hunks: UnifiedHunk[]): boolean {
    for (const h of hunks) {
      if (!h.lines) continue;
      for (const line of h.lines) {
        if (line.startsWith('+') || line.startsWith('-')) {
          if (line.trim().length > 1) return true;
        }
      }
    }
    return false;
  }
}

export default DiffJoiner;
