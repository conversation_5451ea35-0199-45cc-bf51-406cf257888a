import { Diff<PERSON><PERSON><PERSON>, DiffJoinOutcome, PartitionJoinArtifact, ConflictEntry } from './DiffJoiner';
import { ConflictResolver, ConflictResolutionOutcome } from './ConflictResolver';
import { TwoPhaseParticipant, CoordinatorResult } from '../types';

export interface ParticipantSpec {
  id: string;
  participant: TwoPhaseParticipant;
  resourceKeys: string[];
  priorityTs?: number;
  metadata?: Record<string, any>;
}

export interface PartitionDescriptor {
  id: string;
  resourceKeys: string[];
  participants: ParticipantSpec[];
}

export interface PartitionPlan {
  txnId: string;
  partitions: PartitionDescriptor[];
}

export interface PartitionResult {
  partitionId: string;
  decision: 'commit' | 'abort';
  coordinator: CoordinatorResult;
  previewDiff?: string | null;
  patchPlanHash?: string | null;
  participantIds: string[];
}

export interface ParallelExecutionOptions {
  runPartition: (partition: PartitionDescriptor) => Promise<PartitionResult>;
}

export interface JoinResult {
  joinOutcome: DiffJoinOutcome;
  resolution?: ConflictResolutionOutcome;
}

export interface ConcurrencyManagerOptions {
  txnId: string;
  maxParallel?: number;
  diffJoiner?: DiffJoiner;
  conflictResolver?: ConflictResolver;
}

export class ConcurrencyManager {
  private readonly txnId: string;
  private readonly maxParallel: number;
  private readonly diffJoiner: DiffJoiner;
  private readonly conflictResolver: ConflictResolver | null;

  constructor(opts: ConcurrencyManagerOptions) {
    this.txnId = opts.txnId;
    this.maxParallel = Math.max(1, opts.maxParallel ?? 2);
    this.diffJoiner = opts.diffJoiner ?? new DiffJoiner();
    this.conflictResolver = opts.conflictResolver ?? null;
  }

  planPartitions(participants: ParticipantSpec[]): PartitionPlan {
    const sorted = [...participants].sort((a, b) => (a.id < b.id ? -1 : a.id > b.id ? 1 : 0));
    const partitions: PartitionDescriptor[] = [];

    for (const spec of sorted) {
      const normalizedKeys = Array.from(new Set(spec.resourceKeys)).sort();
      spec.resourceKeys = normalizedKeys;

      let target: PartitionDescriptor | null = null;
      for (const candidate of partitions) {
        if (this.isDisjoint(candidate.resourceKeys, normalizedKeys)) {
          target = candidate;
          break;
        }
      }

      if (!target) {
        target = {
          id: `${this.txnId}:partition:${partitions.length + 1}`,
          resourceKeys: [...normalizedKeys],
          participants: [],
        };
        partitions.push(target);
      } else {
        target.resourceKeys = Array.from(new Set([...target.resourceKeys, ...normalizedKeys])).sort();
      }
      target.participants.push(spec);
    }

    return { txnId: this.txnId, partitions };
  }

  async executePartitionsInParallel(plan: PartitionPlan, opts: ParallelExecutionOptions): Promise<PartitionResult[]> {
    const queue = [...plan.partitions];
    const results: PartitionResult[] = [];
    if (queue.length === 0) return results;

    const runNext = async (): Promise<void> => {
      const partition = queue.shift();
      if (!partition) return;
      const result = await opts.runPartition(partition);
      results.push(result);
      if (queue.length > 0) {
        await runNext();
      }
    };

    const concurrency = Math.min(this.maxParallel, Math.max(1, queue.length));
    await Promise.all(Array.from({ length: concurrency }, () => runNext()));
    return results.sort((a, b) => (a.partitionId < b.partitionId ? -1 : a.partitionId > b.partitionId ? 1 : 0));
  }

  async join(plan: PartitionPlan, results: PartitionResult[]): Promise<JoinResult> {
    const artifacts: PartitionJoinArtifact[] = results.map((r) => ({
      partitionId: r.partitionId,
      participantIds: r.participantIds,
      previewDiff: r.previewDiff,
    }));

    const joinOutcome = this.diffJoiner.join(artifacts);
    if (joinOutcome.conflicts.length === 0 || !this.conflictResolver) {
      return { joinOutcome };
    }

    const partitionOrder = plan.partitions.map((p) => p.id);
    const resolution = await this.conflictResolver.resolveConflicts(joinOutcome.conflicts, { partitionOrder });
    return { joinOutcome, resolution };
  }

  private isDisjoint(a: string[], b: string[]): boolean {
    const set = new Set(a);
    for (const key of b) {
      if (set.has(key)) return false;
    }
    return true;
  }
}

export default ConcurrencyManager;
