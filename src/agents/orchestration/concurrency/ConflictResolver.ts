import { hashPlan } from '../journal/canonicalize';
import { sha256Hex } from '../journal/types';
import type { ConflictEntry } from './DiffJoiner';

export interface ConflictResolverOptions {
  policy: 'resolve-prefer-older' | 'abort';
  resolveWithAgent?: (conflict: ConflictEntry, context: ConflictResolutionContext) => Promise<ResolvedConflict | null>;
}

export interface ConflictResolutionContext {
  partitionOrder: string[];
  attemptId?: string;
  metadata?: Record<string, any>;
}

export interface ResolvedConflict {
  filePath: string;
  diff: string;
  notes?: string;
}

export interface ConflictResolutionOutcome {
  resolvedConflicts: ResolvedConflict[];
  unresolvedConflicts: ConflictEntry[];
  mergedPatchPlan: any | null;
  mergedPreviewDiff: string | null;
  mergedPatchPlanHash: string | null;
  notes: string[];
}

export class ConflictResolver {
  private readonly policy: 'resolve-prefer-older' | 'abort';
  private readonly resolveWithAgent?: (conflict: ConflictEntry, context: ConflictResolutionContext) => Promise<ResolvedConflict | null>;
  private readonly cache = new Map<string, ResolvedConflict | null>();

  constructor(opts: ConflictResolverOptions) {
    this.policy = opts.policy;
    this.resolveWithAgent = opts.resolveWithAgent;
  }

  async resolveConflicts(conflicts: ConflictEntry[], ctx: ConflictResolutionContext): Promise<ConflictResolutionOutcome> {
    if (!conflicts.length) {
      return { resolvedConflicts: [], unresolvedConflicts: [], mergedPatchPlan: null, mergedPreviewDiff: null, mergedPatchPlanHash: null, notes: [] };
    }

    const resolved: ResolvedConflict[] = [];
    const unresolved: ConflictEntry[] = [];
    const notes: string[] = [];

    for (const conflict of conflicts) {
      const cacheKey = conflict.hash;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (cached) resolved.push(cached);
        else unresolved.push(conflict);
        continue;
      }

      const overlaps = this.hasOverlappingChanges(conflict);

      if (this.policy === 'resolve-prefer-older' && (overlaps || !this.resolveWithAgent)) {
        const preferred = this.pickPreferred(conflict, ctx.partitionOrder);
        if (preferred) {
          resolved.push(preferred);
          this.cache.set(cacheKey, preferred);
          notes.push(`conflict:${conflict.filePath}:selected ${preferred.notes ?? 'preferred participant'}`);
          continue;
        }
      }

      if (this.resolveWithAgent) {
        const agentResolved = await this.resolveWithAgent(conflict, ctx);
        if (agentResolved) {
          resolved.push(agentResolved);
          this.cache.set(cacheKey, agentResolved);
          notes.push(`conflict:${conflict.filePath}:resolved_by_agent`);
          continue;
        }
      }

      this.cache.set(cacheKey, null);
      unresolved.push(conflict);
    }

    const mergedPatchPlan = resolved.length > 0 ? { files: resolved.map((r) => ({ path: r.filePath, diff: r.diff })) } : null;
    const mergedPreviewDiff = mergedPatchPlan ? this.buildMergedPreview(resolved) : null;
    const mergedPatchPlanHash = mergedPatchPlan ? hashPlan(mergedPatchPlan) : null;

    return {
      resolvedConflicts: resolved,
      unresolvedConflicts: unresolved,
      mergedPatchPlan,
      mergedPreviewDiff,
      mergedPatchPlanHash,
      notes,
    };
  }

  private pickPreferred(conflict: ConflictEntry, order: string[]): ResolvedConflict | null {
    for (const partitionId of order) {
      const entry = conflict.diffs.find((d) => d.partitionId === partitionId);
      if (!entry) continue;
      const diff = entry.diffText;
      const note = `partition:${partitionId}`;
      return { filePath: conflict.filePath, diff, notes: note };
    }
    const fallback = conflict.diffs[0];
    if (!fallback) return null;
    return { filePath: conflict.filePath, diff: fallback.diffText, notes: `partition:${fallback.partitionId}` };
  }

  private hasOverlappingChanges(conflict: ConflictEntry): boolean {
    const ranges: Array<{ start: number; end: number }> = [];
    for (const diff of conflict.diffs) {
      for (const h of diff.hunks) {
        const start = h.newStart;
        const span = Math.max(1, h.newLines || 0);
        const end = start + span;
        for (const existing of ranges) {
          if (start < existing.end && end > existing.start) {
            return true;
          }
        }
        ranges.push({ start, end });
      }
    }
    return false;
  }

  private buildMergedPreview(resolved: ResolvedConflict[]): string {
    const parts: string[] = [];
    for (const r of resolved) {
      parts.push(r.diff.trimEnd());
    }
    const joined = parts.join('\n');
    const normalized = joined.endsWith('\n') ? joined : joined + '\n';
    return normalized;
  }
}

export default ConflictResolver;
