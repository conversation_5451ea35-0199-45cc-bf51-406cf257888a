import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';
import {
  OrchestrationRequest,
  PartitionPlan,
  WorkUnit,
  WorkCategory,
  Intent,
  WorkUnitId,
  WorkUnitStatus,
} from '../types';

/**
 * Options for HeuristicPartitioner behavior.
 */
export interface PartitionerOptions {
  /** Max files per WorkUnit (greedy pack). Default: 4 */
  maxFilesPerUnit?: number;
  /** Max lines-of-code per WorkUnit (approximate, greedy). Default: 400 */
  maxLOCPerUnit?: number;
  /** Create per-unit review WorkUnits. Default: true */
  createReviewUnits?: boolean;
  /** Whether to include tests from scope hints. Default: true */
  includeTestsFromScope?: boolean;
  /** Base directory to resolve scope hints against. Default: process.cwd() */
  baseDir?: string;
  /** LOC thresholds mapping to risk levels. Default: { low: 120, medium: 300 } */
  riskThresholds?: { low: number; medium: number };
}

type FileEntry = {
  relPath: string; // posix-style relative to baseDir
  absPath: string;
  loc: number;
};

const DEFAULTS: Required<Omit<PartitionerOptions, 'riskThresholds'>> & { riskThresholds: { low: number; medium: number } } = {
  maxFilesPerUnit: 4,
  maxLOCPerUnit: 400,
  createReviewUnits: true,
  includeTestsFromScope: true,
  baseDir: process.cwd(),
  riskThresholds: { low: 120, medium: 300 },
};

const ALLOWED_EXTS = new Set([
  '.ts',
  '.tsx',
  '.js',
  '.jsx',
  '.mjs',
  '.cjs',
  '.json',
  '.md',
  '.css',
  '.scss',
]);

const TEST_FILE_REGEX = /\.(test|spec)\.[tj]sx?$/i;

/**
 * Dependency-aware heuristic partitioner.
 *
 * Replaces coarse category-level delegation with fine-grained WorkUnit partitions
 * built from request scope hints and simple codebase heuristics. Produces a DAG
 * PartitionPlan with explicit dependencies, per-unit acceptance criteria, priorities,
 * and effort estimates.
 */
export class HeuristicPartitioner {
  private readonly opts: Required<PartitionerOptions>;

  constructor(opts?: PartitionerOptions) {
    const merged = { ...DEFAULTS, ...(opts ?? {}) };
    // riskThresholds may be partially provided; normalize
    const risk = opts?.riskThresholds ?? DEFAULTS.riskThresholds;
    this.opts = {
      ...merged,
      riskThresholds: {
        low: risk.low ?? DEFAULTS.riskThresholds.low,
        medium: risk.medium ?? DEFAULTS.riskThresholds.medium,
      },
    };
  }

  /**
   * Build a dependency-aware partition plan for the provided request.
   * - Extracts scope using files/modules/paths hints.
   * - Groups by component/folder.
   * - Greedily chunks by max files and LOC.
   * - Creates coding WorkUnits and dependent review units.
   * - Adds a final integration review depending on all per-unit reviews.
   * - Validates DAG acyclicity before returning.
   */
  async build(request: OrchestrationRequest): Promise<PartitionPlan> {
    const now = Date.now();

    // Blend constraints from request into options
    const effOpts = {
      ...this.opts,
      maxFilesPerUnit:
        request.constraints?.maxFilesPerUnit ?? this.opts.maxFilesPerUnit,
      maxLOCPerUnit:
        request.constraints?.maxLOCPerUnit ?? this.opts.maxLOCPerUnit,
    };

    const baseDir = effOpts.baseDir;

    // 1) Assemble candidate file list from scope hints
    const { files, modules, paths, includeTests, exclude } =
      request.scopeHints ?? {};
    const candidateAbs = new Set<string>();

    // From files/paths: resolve and include (dir recursion or single file)
    const pathHints = [...(files ?? []), ...(paths ?? [])];
    for (const hint of pathHints) {
      const abs = path.isAbsolute(hint) ? hint : path.join(baseDir, hint);
      const st = await safeStat(abs);
      if (!st) continue;
      if (st.isDirectory()) {
        const listed = await listFilesRecursive(abs);
        for (const f of listed) candidateAbs.add(f);
      } else if (st.isFile()) {
        if (isAllowedExtension(abs)) candidateAbs.add(abs);
      }
    }

    // From modules: substring match under src/ (fallback to baseDir)
    if (modules && modules.length > 0) {
      const srcRoot = path.join(baseDir, 'src');
      const srcExists = await safeStat(srcRoot);
      const scanRoot = srcExists?.isDirectory() ? srcRoot : baseDir;
      const all = await listFilesRecursive(scanRoot);
      for (const mod of modules) {
        for (const f of all) {
          if (f.includes(mod) && isAllowedExtension(f)) {
            candidateAbs.add(f);
          }
        }
      }
    }

    // Normalize to relative posix paths and apply excludes/tests filters
    let relCandidates = Array.from(candidateAbs).map((abs) =>
      toRelPosix(abs, baseDir),
    );

    // Apply exclude via minimal substring match
    if (exclude && exclude.length > 0) {
      relCandidates = relCandidates.filter(
        (p) => !exclude.some((ex) => p.includes(ex)),
      );
    }

    // Decide test inclusion: include only if both (global option) and (scope flag not false)
    const allowTests =
      effOpts.includeTestsFromScope && includeTests !== false;
    if (!allowTests) {
      relCandidates = relCandidates.filter((p) => !TEST_FILE_REGEX.test(p));
    }

    // De-dup and stable sort
    relCandidates = Array.from(new Set(relCandidates)).sort();

    // 2) Load LOC per file (fallback 0 on error)
    const fileEntries: FileEntry[] = [];
    for (const rel of relCandidates) {
      const abs = path.join(baseDir, fromPosix(rel));
      const loc = await estimateLOC(abs);
      fileEntries.push({ relPath: rel, absPath: abs, loc });
    }

    // 3) Group by component/folder
    const groups = groupByComponent(fileEntries);

    // 4) Chunk each group by limits
    const codingUnits: WorkUnit[] = [];
    const adjacency: Record<WorkUnitId, WorkUnitId[]> = {};
    const createCoding = request.categoryMix?.coding !== false;

    if (createCoding) {
      for (const [groupKey, entries] of groups.entries()) {
        const chunks = chunkByLimits(entries, effOpts.maxFilesPerUnit, effOpts.maxLOCPerUnit);
        chunks.forEach((chunk, idx) => {
          const filesInChunk = chunk.map((e) => e.relPath);
          const totalLOC = sumLOC(chunk);
          const priority = Math.max(1, 1000 - Math.floor(totalLOC));
          const risk = classifyRisk(totalLOC, effOpts.riskThresholds);
          const id = makeCodingId(groupKey, idx);

          const wu: WorkUnit = {
            id,
            category: WorkCategory.Coding,
            scope: { files: filesInChunk },
            intent: Intent.Implement,
            acceptanceCriteria: [
              'Only files in scope are modified or newly added.',
              'Build/typecheck or black (for python) succeeds for affected modules.',
              'Targeted tests for scope exist and pass.',
            ],
            dependencies: [],
            priority,
            estimatedEffort: totalLOC,
            risk,
            status: WorkUnitStatus.Pending,
            createdAt: now,
          };

          codingUnits.push(wu);
          adjacency[wu.id] = adjacency[wu.id] ?? [];
        });
      }
    }

    // 5) Create review units and integration review
    const createReviewCategory = request.categoryMix?.review !== false;
    const reviewEnabled = effOpts.createReviewUnits && createReviewCategory;

    const units: WorkUnit[] = [...codingUnits];
    const reviewIds: WorkUnitId[] = [];

    if (reviewEnabled) {
      // Per-unit reviews
      for (const coding of codingUnits) {
        const reviewId = `${coding.id}_review`;
        const reviewWU: WorkUnit = {
          id: reviewId,
          category: WorkCategory.Review,
          scope: { files: coding.scope.files ?? [] },
          intent: Intent.Review,
          acceptanceCriteria: [
            'Static checks (lint/type) pass on modified files.',
            'Unit/integration tests pass for modified scope.',
            'Security-sensitive patterns flagged or cleared.',
          ],
          dependencies: [coding.id],
          priority: Math.max(1, 1000 - Math.floor(coding.estimatedEffort ?? 0)),
          estimatedEffort: Math.max(10, Math.floor((coding.estimatedEffort ?? 0) * 0.25)),
          risk: coding.risk,
          status: WorkUnitStatus.Pending,
          createdAt: now,
        };
        units.push(reviewWU);
        reviewIds.push(reviewWU.id);
        adjacency[reviewWU.id] = [coding.id];
        // ensure coding node is present in adjacency
        adjacency[coding.id] = adjacency[coding.id] ?? [];
      }

      // Final integration review (only if we have per-unit reviews)
      if (reviewIds.length > 0) {
        const totalLOC = codingUnits.reduce((acc, u) => acc + (u.estimatedEffort ?? 0), 0);
        const integrationId = `wu_integration_review_${hashId(request.id)}`;
        const integrationWU: WorkUnit = {
          id: integrationId,
          category: WorkCategory.Review,
          scope: { files: [] }, // integration scope
          intent: Intent.Review,
          acceptanceCriteria: ['End-to-end build/tests pass; integration summary produced.'],
          dependencies: [...reviewIds],
          priority: Math.max(1, 1000 - Math.floor(totalLOC)),
          estimatedEffort: Math.max(20, Math.floor(totalLOC * 0.15)),
          risk: classifyRisk(totalLOC, effOpts.riskThresholds),
          status: WorkUnitStatus.Pending,
          createdAt: now,
        };
        units.push(integrationWU);
        adjacency[integrationWU.id] = [...reviewIds];
        // ensure nodes exist in adjacency
        for (const rid of reviewIds) {
          adjacency[rid] = adjacency[rid] ?? [];
        }
      }
    }

    // 6) Validate DAG (acyclic)
    validateAcyclic(units.map((u) => u.id), adjacency);

    // 7) Build plan
    const plan: PartitionPlan = {
      id: `plan_${hashId(`${request.id}:${now}`)}`,
      requestId: request.id,
      units,
      adjacency,
      createdAt: now,
      configSnapshot: {
        resolved: {
          maxFilesPerUnit: effOpts.maxFilesPerUnit,
          maxLOCPerUnit: effOpts.maxLOCPerUnit,
          createReviewUnits: effOpts.createReviewUnits,
          includeTestsFromScope: effOpts.includeTestsFromScope,
          baseDir: effOpts.baseDir,
          riskThresholds: effOpts.riskThresholds,
        },
        requestCategoryMix: request.categoryMix ?? {},
        requestConstraints: request.constraints ?? {},
        scopeHintsPresent: !!request.scopeHints,
      },
    };

    return plan;
  }
}

/* ------------------------- utility helpers (local) ------------------------ */

function hashId(input: string): string {
  return crypto.createHash('sha1').update(input).digest('hex').slice(0, 8);
}

async function safeStat(p: string): Promise<import('fs').Stats | null> {
  try {
    return await fs.stat(p);
  } catch {
    return null;
  }
}

function isAllowedExtension(absPath: string): boolean {
  const ext = path.extname(absPath).toLowerCase();
  return ALLOWED_EXTS.has(ext);
}

async function listFilesRecursive(rootAbs: string): Promise<string[]> {
  const entries: string[] = [];
  async function walk(dir: string) {
    let listing: string[];
    try {
      listing = await fs.readdir(dir);
    } catch {
      return;
    }
    for (const name of listing) {
      const abs = path.join(dir, name);
      let st: import('fs').Stats | null = null;
      try {
        st = await fs.stat(abs);
      } catch {
        continue;
      }
      if (st.isDirectory()) {
        await walk(abs);
      } else if (st.isFile()) {
        if (isAllowedExtension(abs)) {
          entries.push(abs);
        }
      }
    }
  }
  await walk(rootAbs);
  return entries;
}

async function estimateLOC(absPath: string): Promise<number> {
  try {
    const buf = await fs.readFile(absPath, 'utf8');
    if (buf.length === 0) return 0;
    // Count lines by splitting on \n; normalize CRLF
    return buf.replace(/\r\n/g, '\n').split('\n').length;
  } catch {
    return 0;
  }
}

function toRelPosix(absPath: string, baseDir: string): string {
  const rel = path.relative(baseDir, absPath);
  return rel.split(path.sep).join('/');
}

function fromPosix(relPosix: string): string {
  return relPosix.split('/').join(path.sep);
}

function groupByComponent(files: FileEntry[]): Map<string, FileEntry[]> {
  const groups = new Map<string, FileEntry[]>();
  for (const fe of files) {
    const key = computeGroupKey(fe.relPath);
    const arr = groups.get(key);
    if (arr) arr.push(fe);
    else groups.set(key, [fe]);
  }
  return groups;
}

function computeGroupKey(relPosixPath: string): string {
  const p = relPosixPath; // already posix
  const componentsPrefix = 'src/components/';
  if (p.startsWith(componentsPrefix)) {
    const after = p.slice(componentsPrefix.length);
    const first = after.split('/')[0] || 'components';
    return `components/${first}`;
  }
  const srcPrefix = 'src/';
  if (p.startsWith(srcPrefix)) {
    const after = p.slice(srcPrefix.length);
    const first = after.split('/')[0];
    if (first && after.includes('/')) {
      return `src/${first}`;
    }
    // file directly under src/ or malformed - fallback to parent directory
    const parent = posixDirname(p);
    return parent || 'src';
  }
  // Fallback to parent directory name
  return posixDirname(p) || '.';
}

function posixDirname(p: string): string {
  const parts = p.split('/');
  parts.pop();
  return parts.join('/');
}

function chunkByLimits(
  entries: FileEntry[],
  maxFiles: number,
  maxLOC: number,
): FileEntry[][] {
  // Greedy: largest files first; pack until adding would exceed limits
  const sorted = [...entries].sort((a, b) => b.loc - a.loc);
  const chunks: FileEntry[][] = [];
  let current: FileEntry[] = [];
  let currentFiles = 0;
  let currentLOC = 0;

  function flush() {
    if (current.length > 0) {
      chunks.push(current);
      current = [];
      currentFiles = 0;
      currentLOC = 0;
    }
  }

  for (const e of sorted) {
    const wouldFiles = currentFiles + 1;
    const wouldLOC = currentLOC + e.loc;

    const exceedFiles = wouldFiles > maxFiles;
    const exceedLOC = wouldLOC > maxLOC;

    if ((exceedFiles || exceedLOC) && current.length > 0) {
      flush();
    }

    // After flush (or if empty), if single file exceeds LOC, place alone
    if (current.length === 0 && e.loc > maxLOC) {
      chunks.push([e]);
      continue;
    }

    current.push(e);
    currentFiles += 1;
    currentLOC += e.loc;
  }
  flush();
  return chunks;
}

function sumLOC(chunk: FileEntry[]): number {
  return chunk.reduce((acc, e) => acc + e.loc, 0);
}

function classifyRisk(
  loc: number,
  thresholds: { low: number; medium: number },
): 'low' | 'medium' | 'high' {
  if (loc <= thresholds.low) return 'low';
  if (loc <= thresholds.medium) return 'medium';
  return 'high';
}

function makeCodingId(groupKey: string, index: number): WorkUnitId {
  return `wu_${hashId(groupKey)}_${index}`;
}

function validateAcyclic(
  nodeIds: WorkUnitId[],
  adjacency: Record<WorkUnitId, WorkUnitId[]>,
): void {
  // Kahn's algorithm
  const nodes = new Set(nodeIds);
  // Ensure all nodes exist in adjacency
  for (const id of nodes) {
    if (!adjacency[id]) adjacency[id] = [];
  }

  const indeg = new Map<WorkUnitId, number>();
  for (const id of nodes) indeg.set(id, 0);

  for (const [from, tos] of Object.entries(adjacency)) {
    if (!nodes.has(from as WorkUnitId)) continue;
    for (const to of tos) {
      if (!nodes.has(to)) continue;
      indeg.set(to, (indeg.get(to) ?? 0) + 1);
    }
  }

  const queue: WorkUnitId[] = [];
  for (const [id, d] of indeg.entries()) {
    if (d === 0) queue.push(id);
  }

  let visited = 0;
  while (queue.length > 0) {
    const u = queue.shift()!;
    visited++;
    const outs = adjacency[u] ?? [];
    for (const v of outs) {
      if (!nodes.has(v)) continue;
      const newDeg = (indeg.get(v) ?? 0) - 1;
      indeg.set(v, newDeg);
      if (newDeg === 0) queue.push(v);
    }
  }

  if (visited !== nodes.size) {
    throw new Error('Cycle detected in partition adjacency');
  }
}
