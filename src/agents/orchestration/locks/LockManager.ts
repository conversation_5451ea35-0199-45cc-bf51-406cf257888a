import { promises as fs } from 'node:fs';
import * as path from 'node:path';

export type AcquireResult = {
  ok: boolean;
  waitReason?: string;
  granted: string[];
  pending: string[];
  wounded?: string[];
};

interface Holder {
  txnId: string;
  expiresAt: number;
  priorityTs: number;
  leaseMs: number;
}

interface QueueEntry {
  txnId: string;
  priorityTs: number;
  enqueuedAt: number;
  seq: number;
}

interface ResourceLock {
  holder?: Holder;
  queue: QueueEntry[];
}

type Snapshot = {
  held: Record<string, {
    txnId: string | null;
    expiresAt: number | null;
    priorityTs: number | null;
    leaseMs: number | null;
    queue: Array<{ txnId: string; priorityTs: number; enqueuedAt: number; seq: number }>;
  }>;
};

class AsyncMutex {
  private tail = Promise.resolve();

  runExclusive<T>(fn: () => Promise<T>): Promise<T> {
    const run = this.tail.then(fn, fn);
    this.tail = run.then(
      () => undefined,
      () => undefined,
    );
    return run;
  }
}

export interface LockManagerOptions {
  persistDir?: string;
  clock?: () => number;
  agingHalfLifeMs?: number;
  agingWeight?: number;
}

function defaultClock(): number {
  return Date.now();
}

/**
 * LockManager implements strict two-phase locking (2PL) with leases and wound-wait deadlock
 * prevention. It maintains an in-memory lock table and persists periodic snapshots so that
 * the coordinator can rebuild state during recovery.
 */
export class LockManager {
  private readonly table = new Map<string, ResourceLock>();
  private readonly txnHeld = new Map<string, Set<string>>();
  private readonly mutex = new AsyncMutex();
  private readonly persistFile: string;
  private readonly clock: () => number;
  private readonly agingWeight: number;

  private seqCounter = 0;

  constructor(opts: LockManagerOptions = {}) {
    const dir = path.resolve(process.cwd(), opts.persistDir ?? 'data');
    this.persistFile = path.join(dir, 'lock-table.jsonl');
    this.clock = opts.clock ?? defaultClock;
    if (opts.agingWeight !== undefined) {
      this.agingWeight = Math.max(0, opts.agingWeight);
    } else if (opts.agingHalfLifeMs && opts.agingHalfLifeMs > 0) {
      this.agingWeight = 1_000 / opts.agingHalfLifeMs;
    } else {
      this.agingWeight = 1;
    }
  }

  async init(): Promise<void> {
    await this.ensurePersistDir();
    await this.restoreLatestSnapshot();
  }

  async acquire(txnId: string, keys: string[], leaseMs: number, priorityTs: number): Promise<AcquireResult> {
    if (!txnId) throw new Error('txnId required');
    if (!Array.isArray(keys) || keys.length === 0) {
      return { ok: true, granted: [], pending: [] };
    }

    return this.mutex.runExclusive(async () => {
      const now = this.clock();
      const sorted = [...new Set(keys)].sort();
      const granted: string[] = [];
      const pending: string[] = [];
      const wounded = new Set<string>();

      const changedByGc = this.gcExpired(now);

      let changed = changedByGc;
      let waitReason: string | undefined;

      for (const key of sorted) {
        const lock = this.getResource(key);

        // Already holding the key -> renew lease inline
        if (lock.holder?.txnId === txnId) {
          lock.holder.expiresAt = now + Math.max(1, leaseMs);
          lock.holder.leaseMs = Math.max(1, leaseMs);
          granted.push(key);
          changed = true;
          continue;
        }

        if (!lock.holder) {
          this.grantLock(lock, key, txnId, priorityTs, leaseMs, now);
          granted.push(key);
          changed = true;
          continue;
        }

        const holder = lock.holder;
        // Wound-wait: if requester is older (smaller priorityTs), wound the younger holder
        if (priorityTs < holder.priorityTs) {
          wounded.add(holder.txnId);
          waitReason = 'wound-wait';
        } else if (!waitReason) {
          waitReason = 'waiting';
        }

        this.enqueue(lock, txnId, priorityTs, now);
        pending.push(key);
      }

      if (changed) {
        await this.persistSnapshotLocked();
      }

      return {
        ok: pending.length === 0,
        waitReason,
        granted,
        pending,
        wounded: wounded.size > 0 ? Array.from(wounded) : undefined,
      };
    });
  }

  async renew(txnId: string, keys: string[], leaseMs: number): Promise<boolean> {
    if (!txnId || keys.length === 0) return false;
    return this.mutex.runExclusive(async () => {
      const now = this.clock();
      let allOk = true;
      let changed = false;
      for (const key of keys) {
        const lock = this.table.get(key);
        if (!lock || !lock.holder || lock.holder.txnId !== txnId) {
          allOk = false;
          continue;
        }
        lock.holder.expiresAt = now + Math.max(1, leaseMs);
        lock.holder.leaseMs = Math.max(1, leaseMs);
        changed = true;
      }

      if (changed) {
        await this.persistSnapshotLocked();
      }
      return allOk;
    });
  }

  async release(txnId: string, keys?: string[]): Promise<void> {
    await this.mutex.runExclusive(async () => {
      const now = this.clock();
      const targetKeys = keys && keys.length > 0 ? [...new Set(keys)] : Array.from(this.txnHeld.get(txnId) ?? []);
      if (targetKeys.length === 0) return;

      let changed = false;

      for (const key of targetKeys) {
        const lock = this.table.get(key);
        if (!lock || !lock.holder || lock.holder.txnId !== txnId) continue;

        const leaseMs = lock.holder.leaseMs;
        lock.holder = undefined;
        this.unmarkTxnHold(txnId, key);
        changed = true;

        this.promoteNext(lock, key, now, leaseMs);

        if (!lock.holder && lock.queue.length === 0) {
          this.table.delete(key);
        }
      }

      if (this.gcExpired(now)) {
        changed = true;
      }

      if (changed) {
        await this.persistSnapshotLocked();
      }
    });
  }

  async snapshot(): Promise<Snapshot> {
    return this.mutex.runExclusive(async () => this.snapshotUnlocked());
  }

  private snapshotUnlocked(): Snapshot {
    const held: Snapshot['held'] = {};
    for (const [key, lock] of this.table.entries()) {
      held[key] = {
        txnId: lock.holder?.txnId ?? null,
        expiresAt: lock.holder?.expiresAt ?? null,
        priorityTs: lock.holder?.priorityTs ?? null,
        leaseMs: lock.holder?.leaseMs ?? null,
        queue: lock.queue.map((entry) => ({
          txnId: entry.txnId,
          priorityTs: entry.priorityTs,
          enqueuedAt: entry.enqueuedAt,
          seq: entry.seq,
        })),
      };
    }
    return { held };
  }

  private grantLock(lock: ResourceLock, key: string, txnId: string, priorityTs: number, leaseMs: number, now: number) {
    lock.holder = {
      txnId,
      expiresAt: now + Math.max(1, leaseMs),
      priorityTs,
      leaseMs: Math.max(1, leaseMs),
    };
    this.markTxnHold(txnId, key);
  }

  private enqueue(lock: ResourceLock, txnId: string, priorityTs: number, now: number): void {
    if (lock.queue.some((q) => q.txnId === txnId)) return;
    this.seqCounter += 1;
    lock.queue.push({ txnId, priorityTs, enqueuedAt: now, seq: this.seqCounter });
  }

  private promoteNext(lock: ResourceLock, key: string, now: number, leaseMs?: number) {
    while (lock.queue.length > 0) {
      const nextIdx = this.pickNext(lock.queue, now);
      if (nextIdx < 0) break;
      const next = lock.queue.splice(nextIdx, 1)[0];
      // Skip if txn already holds key (may happen if transaction re-enqueued while waiting)
      const existing = this.txnHeld.get(next.txnId);
      if (existing && existing.has(key)) {
        continue;
      }
      this.grantLock(lock, key, next.txnId, next.priorityTs, leaseMs ?? 1000, now);
      break;
    }
  }

  private pickNext(queue: QueueEntry[], now: number): number {
    let bestIdx = -1;
    let bestScore = Number.POSITIVE_INFINITY;
    for (let i = 0; i < queue.length; i += 1) {
      const entry = queue[i];
      const age = Math.max(0, now - entry.enqueuedAt);
      const score = entry.priorityTs - age * this.agingWeight;
      if (score < bestScore || (score === bestScore && entry.seq < queue[bestIdx]?.seq)) {
        bestScore = score;
        bestIdx = i;
      }
    }
    return bestIdx;
  }

  private markTxnHold(txnId: string, key: string) {
    let set = this.txnHeld.get(txnId);
    if (!set) {
      set = new Set<string>();
      this.txnHeld.set(txnId, set);
    }
    set.add(key);
  }

  private unmarkTxnHold(txnId: string, key: string) {
    const set = this.txnHeld.get(txnId);
    if (!set) return;
    set.delete(key);
    if (set.size === 0) {
      this.txnHeld.delete(txnId);
    }
  }

  private getResource(key: string): ResourceLock {
    let lock = this.table.get(key);
    if (!lock) {
      lock = { holder: undefined, queue: [] };
      this.table.set(key, lock);
    }
    return lock;
  }

  private gcExpired(now: number): boolean {
    let changed = false;
    for (const [key, lock] of this.table.entries()) {
      if (lock.holder && lock.holder.expiresAt <= now) {
        const oldTxn = lock.holder.txnId;
        const leaseMs = lock.holder.leaseMs;
        lock.holder = undefined;
        this.unmarkTxnHold(oldTxn, key);
        changed = true;
        this.promoteNext(lock, key, now, leaseMs);
      }
      if (!lock.holder && lock.queue.length === 0) {
        this.table.delete(key);
      }
    }
    return changed;
  }

  private async ensurePersistDir(): Promise<void> {
    try {
      await fs.mkdir(path.dirname(this.persistFile), { recursive: true });
    } catch {
      // ignore
    }
  }

  private async persistSnapshotLocked(): Promise<void> {
    try {
      const line = JSON.stringify({ ts: this.clock(), type: 'lock_snapshot', data: this.snapshotUnlocked() }) + '\n';
      await fs.appendFile(this.persistFile, line, 'utf8');
    } catch {
      // non-fatal
    }
  }

  private async restoreLatestSnapshot(): Promise<void> {
    let raw = '';
    try {
      raw = await fs.readFile(this.persistFile, 'utf8');
    } catch {
      return;
    }

    const lines = raw.split('\n').map((s) => s.trim()).filter(Boolean);
    for (let i = lines.length - 1; i >= 0; i -= 1) {
      try {
        const rec = JSON.parse(lines[i]);
        if (rec && rec.type === 'lock_snapshot' && rec.data) {
          this.loadSnapshot(rec.data as Snapshot);
          this.gcExpired(this.clock());
          return;
        }
      } catch {
        // ignore malformed
      }
    }
  }

  private loadSnapshot(snapshot: Snapshot): void {
    this.table.clear();
    this.txnHeld.clear();
    this.seqCounter = 0;

    for (const [key, entry] of Object.entries(snapshot.held ?? {})) {
      const lock: ResourceLock = { holder: undefined, queue: [] };
      if (entry.txnId && entry.expiresAt) {
        lock.holder = {
          txnId: entry.txnId,
          expiresAt: entry.expiresAt,
          priorityTs: entry.priorityTs ?? this.clock(),
          leaseMs: entry.leaseMs ?? 1000,
        };
        this.markTxnHold(entry.txnId, key);
      }
      for (const q of entry.queue ?? []) {
        this.seqCounter = Math.max(this.seqCounter, q.seq ?? 0);
        lock.queue.push({
          txnId: q.txnId,
          priorityTs: q.priorityTs,
          enqueuedAt: q.enqueuedAt ?? this.clock(),
          seq: q.seq ?? ++this.seqCounter,
        });
      }
      this.table.set(key, lock);
    }
  }
}

export default LockManager;
