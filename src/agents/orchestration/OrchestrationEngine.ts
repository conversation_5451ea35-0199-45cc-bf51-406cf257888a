import {
  RunId,
  OrchestrationRequest,
  PartitionPlan,
  WorkUnit,
  AgentProfile,
  Assignment,
  ContextPackage,
  WorkCategory,
  Ledger,
  OrchestrationMetrics,
  ProgressEventType,
  CoordinatorState,
  CoordinatorRuntimeConfig,
  CoordinatorTransactionContext,
  CoordinatorResult,
  TwoPhaseParticipant,
} from './types';
import { HeuristicPartitioner } from './partitioning/HeuristicPartitioner';
import { AgentRegistry } from './agents/AgentRegistry';
import { ContextPackager } from './context/ContextPackager';
import { AssignmentManager } from './assign/AssignmentManager';
import {
  WorkScheduler,
  WorkSchedulerOptions,
  AssignmentExecutor,
  WorkSchedulerTwoPhaseOptions,
  TwoPhasePartitionContext,
} from './scheduler/WorkScheduler';
import { JsonLedger } from './ledger/JsonLedger';
import { RunJournal } from './journal/RunJournal';
import { Checkpoint, CheckpointState, NextAction, isoNow, CheckpointIncompleteError, validateCheckpointRequired } from './journal/types';
import { LockManager } from './locks/LockManager';
import { TwoPhaseCommitCoordinator } from './TwoPhaseCommitCoordinator';
import { getConfig } from '../../utils/config';
import type {
  ParticipantSpec as ConcurrencyParticipantSpec,
  PartitionDescriptor as ConcurrencyPartitionDescriptor,
  PartitionResult as ConcurrencyPartitionResult,
} from './concurrency/ConcurrencyManager';
import { codeGenerationPrepare, codeGenerationCommit, codeGenerationAbort } from '../vercel/codeGeneration';
import type { ProgressEvent } from './types';

/**
 * Options for configuring collaborators of OrchestrationEngine (in case consumers add a factory later).
 * Note: The current engine constructor does not accept options; defaults are used.
 */
export interface OrchestrationEngineOptions {
  partitioner?: HeuristicPartitioner;
  agentRegistry?: AgentRegistry;
  contextPackager?: ContextPackager;
  assignmentManager?: AssignmentManager;
  schedulerOptions?: Partial<WorkSchedulerOptions>;
  executor?: AssignmentExecutor;
}

/**
 * OrchestrationEngine
 *
 * Facade that wires partitioning, agent selection, context packaging, assignment creation,
 * scheduling/execution, progress handling, ledger persistence, and metrics exposure.
 */
export class OrchestrationEngine {
   private runId: RunId;
   private ledgerPromise: Promise<JsonLedger> | null = null;

   private registry: AgentRegistry;
   private partitioner: HeuristicPartitioner;
   private packager: ContextPackager;
   private assigner: AssignmentManager;
   private schedulerOptions: Partial<WorkSchedulerOptions>;
  private executor: AssignmentExecutor;

  private journal: RunJournal;
  private lockManager: LockManager;
  private lockManagerReady: Promise<void>;
  private coordinatorConfig: CoordinatorRuntimeConfig;
  private coordinatorEnabled: boolean;
  private concurrencyConfig: {
    enabled: boolean;
    parallelEnabled: boolean;
    maxParallel: number;
    conflictPolicy: string;
  };

  // Metrics state
  private runStartAt: number;
   private firstEventAt?: number;
   private eventsCount = 0;
   private successCount = 0;
   private failCount = 0;
   private tokenUsage = 0;
   private cost = 0;

   // Local caches to support summarize() without awaiting ledger
   private lastPlan?: PartitionPlan;
   private unitLastEventType = new Map<string, ProgressEventType>();
   private unitEventLog = new Map<string, ProgressEvent[]>();

   constructor(runId: RunId) {
     this.runId = runId;

     // Defaults
     this.partitioner = new HeuristicPartitioner();
     this.registry = new AgentRegistry();
     this.packager = new ContextPackager();
     this.assigner = new AssignmentManager();
     this.schedulerOptions = {};
    this.executor = OrchestrationEngine.noopExecutor;

    this.journal = new RunJournal();

    const cfg = getConfig();
    const resilience = cfg.orchestrator?.resilience ?? {};
    this.coordinatorEnabled = resilience.coordinator?.enabled !== false;
    const timeouts = resilience.timeouts ?? {};
    this.coordinatorConfig = {
      prepareTimeoutMs: timeouts.prepareMs ?? 5000,
      commitAckTimeoutMs: timeouts.commitAckMs ?? 3000,
      lockLeaseMs: timeouts.lockLeaseMs ?? 6000,
      lockRenewIntervalMs: timeouts.lockRenewIntervalMs ?? 3000,
    };
    const concurrency = resilience.concurrency ?? {};
    this.concurrencyConfig = {
      enabled: concurrency.enabled !== false,
      parallelEnabled: concurrency.parallelEnabled !== false,
      maxParallel: concurrency.maxParallel ?? 4,
      conflictPolicy: concurrency.conflictPolicy ?? 'resolve-prefer-older',
    };
    this.lockManager = new LockManager();
    this.lockManagerReady = this.lockManager.init().catch((err) => {
      console.error('[OrchestrationEngine] Failed to initialize LockManager', err);
      throw err;
    });

    this.runStartAt = Date.now();
  }
   /**
    * Inject a custom assignment executor to enable real delegation.
    */
  public setExecutor(executor: AssignmentExecutor): void {
    this.executor = executor;
  }

  /**
   * Replace the internal agent registry (e.g., to provide default agents).
   */
  public setAgentRegistry(registry: AgentRegistry): void {
    this.registry = registry;
  }

  /**
   * Build a partition plan for the provided request and record it in the ledger.
   * Blends maxFilesPerUnit/maxLOCPerUnit constraints into the partitioner.
   */
  async plan(request: OrchestrationRequest): Promise<PartitionPlan> {
    // Blend constraints for this invocation (use a temporary partitioner if specific constraints provided)
    const hasBlend =
      request.constraints?.maxFilesPerUnit !== undefined ||
      request.constraints?.maxLOCPerUnit !== undefined;

    const partitioner = hasBlend
      ? new HeuristicPartitioner({
          maxFilesPerUnit: request.constraints?.maxFilesPerUnit,
          maxLOCPerUnit: request.constraints?.maxLOCPerUnit,
        })
      : this.partitioner;

    const plan = await partitioner.build(request);
    this.lastPlan = plan;

    const ledger = await this.getLedger();
    await ledger.recordPlan(plan);
    return plan;
  }

  /**
   * Execute a plan through the WorkScheduler and yield progress events.
   * The wrapped executor records assignments in the ledger before delegation.
   */
  async *execute(plan: PartitionPlan, request?: OrchestrationRequest): AsyncGenerator<ProgressEvent> {
    // Cache plan for checkpoint scope/acceptance mapping
    this.lastPlan = plan;

    // Wrapped executor: record assignment then delegate (or no-op stream if no custom executor)
    const wrappedExecutor: AssignmentExecutor = async (assignment: Assignment) => {
      const ledger = await this.getLedger();
      await ledger.recordAssignment(assignment);

      // If custom executor is not provided (we are using the noop), return an empty generator
      if (this.executor === OrchestrationEngine.noopExecutor) {
        return OrchestrationEngine.emptyStream();
      }

      const gen = await Promise.resolve(this.executor(assignment));
      return gen;
    };

    const schedulerOptions: WorkSchedulerOptions = {
      ...(this.schedulerOptions ?? {}),
      agentRegistry: this.registry,
      contextPackager: this.packager,
      executor: wrappedExecutor,
      onProgress: async (evt) => {
        await this.onProgress(evt);
        try {
          await this.writeCheckpointForEvent(evt, request);
        } catch {
          // Swallow checkpoint errors to avoid affecting scheduling; validation failures are handled at handoff boundaries
        }
      },
      twoPhase: this.schedulerOptions?.twoPhase ?? this.buildTwoPhaseSchedulerOptions(),
    };

    const scheduler = new WorkScheduler(schedulerOptions);

    for await (const evt of scheduler.run(plan, request as OrchestrationRequest)) {
      if (this.firstEventAt === undefined) this.firstEventAt = Date.now();
      yield evt;
    }
  }

  /** Provide a 2PC coordinator instance bound to this engine's run and persistence. */
  public async createCoordinator(opts?: Partial<CoordinatorRuntimeConfig>): Promise<TwoPhaseCommitCoordinator> {
    const lockManager = await this.getLockManager();
    const cfg: CoordinatorRuntimeConfig = {
      ...this.coordinatorConfig,
      ...(opts ?? {}),
    };
    return new TwoPhaseCommitCoordinator({
      runId: this.runId,
      journal: this.journal,
      lockManager,
      config: cfg,
    });
  }

  private buildTwoPhaseSchedulerOptions(): WorkSchedulerTwoPhaseOptions | undefined {
    if (!this.coordinatorEnabled) return undefined;
    return {
      enabled: true,
      parallelEnabled: this.concurrencyConfig.enabled && this.concurrencyConfig.parallelEnabled,
      maxParallel: this.concurrencyConfig.maxParallel,
      conflictPolicy: (this.concurrencyConfig.conflictPolicy as 'resolve-prefer-older' | 'abort'),
      journal: this.journal,
      deriveParticipants: (ctx) => this.deriveParticipantsForTwoPhase(ctx),
      executePartition: (partition, ctx) => this.executeTwoPhasePartition(partition, ctx),
    };
  }

  public deriveResourceKeysFromScope(scope?: { fileScopes?: string[] | undefined }): string[] {
    const files = scope?.fileScopes ?? [];
    return this.computeResourceKeys(files);
  }

  private async deriveParticipantsForTwoPhase(ctx: { workUnit: WorkUnit; assignment: Assignment }): Promise<ConcurrencyParticipantSpec[]> {
    if (ctx.assignment.agentId !== 'vercel-codegen') return [];
    const files = ctx.workUnit.scope?.files ?? [];
    const resourceGroups = this.partitionResourceKeys(files);
    if (resourceGroups.length === 0) return [];

    const specs: ConcurrencyParticipantSpec[] = [];
    resourceGroups.forEach((group, index) => {
      const participantId = group.length > 1 ? `${ctx.assignment.agentId}#${index + 1}` : ctx.assignment.agentId;
      const participant = this.createCodeGenerationParticipant(participantId);
      const resourceKeys = this.computeResourceKeys(group);
      if (resourceKeys.length === 0) return;
      specs.push({
        id: participantId,
        participant,
        resourceKeys,
        priorityTs: Date.now(),
        metadata: { workUnitId: ctx.workUnit.id },
      });
    });
    return specs;
  }

  private partitionResourceKeys(files: string[]): string[][] {
    if (!this.concurrencyConfig.enabled || !this.concurrencyConfig.parallelEnabled) {
      return files.length ? [files] : [];
    }
    if (files.length === 0) return [];
    if (files.length === 1) return [files];
    const maxGroups = Math.max(1, this.concurrencyConfig.maxParallel);
    const chunkSize = Math.max(1, Math.ceil(files.length / maxGroups));
    const groups: string[][] = [];
    for (let i = 0; i < files.length; i += chunkSize) {
      groups.push(files.slice(i, i + chunkSize));
    }
    return groups;
  }

  private computeResourceKeys(files: string[]): string[] {
    return files
      .map((file) => file?.trim())
      .filter((file): file is string => Boolean(file && file.length > 0))
      .map((file) => this.toResourceKey(file));
  }

  private toResourceKey(filePath: string): string {
    const normalized = filePath.replace(/\\/g, '/');
    return `repo:workspace|branch:main|path:${normalized}`;
  }

  private createCodeGenerationParticipant(id: string): TwoPhaseParticipant {
    return {
      id,
      agentId: 'vercel-codegen',
      prepare: codeGenerationPrepare,
      commit: codeGenerationCommit,
      abort: codeGenerationAbort,
    };
  }

  private async executeTwoPhasePartition(
    partition: ConcurrencyPartitionDescriptor,
    ctx: TwoPhasePartitionContext,
  ): Promise<ConcurrencyPartitionResult> {
    const coordinatorResult = await this.createTwoPhaseSubTxn({
      parentTxnId: ctx.parentTxnId,
      partitionId: partition.id,
      resourceKeys: partition.resourceKeys,
      participants: partition.participants.map((p) => p.participant),
      taskId: ctx.taskId,
      stepId: partition.id,
      metadata: { workUnitId: ctx.workUnit.id },
    });

    const primaryId = partition.participants[0]?.id;
    const prepareAck = primaryId ? coordinatorResult.prepareAcks[primaryId] : undefined;

    return {
      partitionId: partition.id,
      decision: coordinatorResult.decision,
      coordinator: coordinatorResult,
      previewDiff: prepareAck?.payload?.previewDiff ?? null,
      patchPlanHash: prepareAck?.payload?.patchPlanHash ?? null,
      participantIds: partition.participants.map((p) => p.id),
    };
  }

  public async createTwoPhaseSubTxn(params: {
    parentTxnId: string;
    partitionId: string;
    resourceKeys: string[];
    participants: TwoPhaseParticipant[];
    taskId: string;
    stepId?: string;
    metadata?: Record<string, any>;
  }): Promise<CoordinatorResult> {
    const coordinator = await this.createCoordinator();
    const txnId = `${params.parentTxnId}:${params.partitionId.split(':').pop() ?? params.partitionId}`;
    const ctx: CoordinatorTransactionContext = {
      runId: this.runId,
      taskId: params.taskId,
      txnId,
      stepId: params.stepId ?? params.partitionId,
      participants: params.participants,
      resourceKeys: params.resourceKeys,
      metadata: { parentTxnId: params.parentTxnId, ...(params.metadata ?? {}) },
    };
    return coordinator.runTransaction(ctx);
  }

  /**
   * Select an agent (from provided candidates or registry), package context, build instructions,
   * create an Assignment, record it, and return it.
   */
  async assign(
    workUnit: WorkUnit,
    agents?: AgentProfile[],
    request?: OrchestrationRequest,
  ): Promise<Assignment> {
    const chosen =
      this.registry.selectFor(workUnit, agents, request) ??
      this.registry.selectFor(workUnit, undefined, request);
    if (!chosen) {
      throw new Error('No available agent for the provided work unit');
    }

    const context = await this.packager.package(workUnit, request);

    const assignment = this.assigner.createAssignment(workUnit, chosen, context, request);
    const ledger = await this.getLedger();
    await ledger.recordAssignment(assignment);
    return assignment;
  }

  /**
   * Build a minimal context package for a work unit.
   */
  async packageContext(workUnit: WorkUnit, request?: OrchestrationRequest): Promise<ContextPackage> {
    return this.packager.package(workUnit, request);
  }

  /**
   * Handle a progress event:
   * - Append to ledger
   * - Record artifacts if present
   * - Update internal metrics
   */
  async onProgress(evt: ProgressEvent): Promise<void> {
    const ledger = await this.getLedger();
    await ledger.recordEvent(evt);
    if (evt.artifacts && evt.artifacts.length > 0) {
      await ledger.recordArtifacts(evt.artifacts);
    }

    // Metrics and caches
    if (this.firstEventAt === undefined) this.firstEventAt = Date.now();
    this.eventsCount += 1;

    if (evt.metrics?.tokenUsage) this.tokenUsage += evt.metrics.tokenUsage;

    if (evt.type === ProgressEventType.Done) this.successCount += 1;
    if (evt.type === ProgressEventType.Failed) this.failCount += 1;

    // Cache last few events per unit for summarize()
    const list = this.unitEventLog.get(evt.workUnitId) ?? [];
    list.push(evt);
    // Keep last 5
    while (list.length > 5) list.shift();
    this.unitEventLog.set(evt.workUnitId, list);
    this.unitLastEventType.set(evt.workUnitId, evt.type);
  }

  /**
   * Summarize either a specific WorkUnit by last few events or aggregate by category.
   */
  summarize(target: WorkUnit | { category: WorkCategory }): string {
    // WorkUnit summary: last events for that id
    if ((target as WorkUnit).id) {
      const wu = target as WorkUnit;
      const events = this.unitEventLog.get(wu.id) ?? [];
      if (events.length === 0) return `No events for unit ${wu.id}`;
      const lines = events.map((e) => {
        const pct = e.percentComplete !== undefined ? ` ${e.percentComplete}%` : '';
        const msg = e.message ? ` - ${e.message}` : '';
        return `[${new Date(e.timestamp).toISOString()}] ${e.type}${pct}${msg}`;
      });
      return `Unit ${wu.id} summary:\n` + lines.join('\n');
    }

    // Category summary: count latest states of units in that category
    const category = (target as { category: WorkCategory }).category;
    const plan = this.lastPlan;
    if (!plan) return `No plan available to summarize category ${category}`;

    const ids = plan.units.filter((u) => u.category === category).map((u) => u.id);
    let done = 0;
    let failed = 0;
    let blocked = 0;

    for (const id of ids) {
      const last = this.unitLastEventType.get(id);
      if (last === ProgressEventType.Done) done += 1;
      else if (last === ProgressEventType.Failed) failed += 1;
      else if (
        last === ProgressEventType.Blocker ||
        last === ProgressEventType.NeedsInfo ||
        last === ProgressEventType.Cancelled
      ) {
        blocked += 1;
      }
    }

    return `Category ${category}: done=${done}, failed=${failed}, blocked=${blocked}, total=${ids.length}`;
  }

  /**
   * Flush the ledger; if a new runId is provided, reopen the ledger for that id.
   */
  async persistLedger(runId?: RunId): Promise<void> {
    const ledger = await this.getLedger();
    await ledger.flush();

    if (runId && runId !== this.runId) {
      this.runId = runId;
      this.ledgerPromise = JsonLedger.open(this.runId);
    }
  }

  /**
   * Load the ledger state (optionally for a different runId).
   */
  async loadLedger(runId?: RunId): Promise<Ledger> {
    if (runId && runId !== this.runId) {
      this.runId = runId;
      this.ledgerPromise = JsonLedger.open(this.runId);
    }
    const ledger = await this.getLedger();
    return ledger.load();
  }

  /**
   * Compute and return current orchestration metrics.
   * timeToFirstUpdate is 0 if no events observed yet.
   */
  metrics(): OrchestrationMetrics {
    const elapsedMs = Math.max(1, Date.now() - this.runStartAt);
    const hours = elapsedMs / (1000 * 60 * 60);
    const throughput = hours > 0 ? this.successCount / hours : 0;

    const attempts = this.successCount + this.failCount;
    const successRate = attempts > 0 ? this.successCount / attempts : 0;

    const timeToFirstUpdate =
      this.firstEventAt !== undefined ? Math.max(0, this.firstEventAt - this.runStartAt) : 0;

    return {
      timeToFirstUpdate,
      throughput,
      successRate,
      cost: this.cost,
      tokenUsage: this.tokenUsage,
    };
  }

  /**
   * Build a handoff payload for a task by fetching its current checkpoint; throws if incomplete.
   */
  public async buildHandoffPayload(taskId: string): Promise<Checkpoint> {
    const cp = await this.journal.getCurrent(this.runId, taskId);
    if (!cp) {
      throw new CheckpointIncompleteError(`No checkpoint found for task ${taskId}`, ['checkpoint']);
    }
    // Validate required fields for handoff protocol
    validateCheckpointRequired(cp);
    return cp;
  }

  /**
   * Resume a task strictly at the nextAction specified by its latest checkpoint.
   * Returns the nextAction directive; callers must honor it (no regeneration of prior steps).
   */
  public async resumeFromCheckpoint(taskId: string): Promise<NextAction> {
    const cp = await this.journal.getCurrent(this.runId, taskId);
    if (!cp) {
      throw new CheckpointIncompleteError(`No checkpoint found for task ${taskId}`, ['checkpoint']);
    }
    validateCheckpointRequired(cp);
    return cp.nextAction;
  }

  // ===== Internals =====

  private async getLedger(): Promise<JsonLedger> {
    if (!this.ledgerPromise) {
      this.ledgerPromise = JsonLedger.open(this.runId);
    }
    return this.ledgerPromise;
  }

  private async getLockManager(): Promise<LockManager> {
    await this.lockManagerReady;
    return this.lockManager;
  }

  /** Derive and persist a checkpoint snapshot for a progress event. */
  private async writeCheckpointForEvent(evt: ProgressEvent, request?: OrchestrationRequest): Promise<void> {
    // Map event to state/nextAction and timestamps
    const { state, next } = OrchestrationEngine.mapEvent(evt.type);
    const unit = this.lastPlan?.units.find((u) => u.id === evt.workUnitId);
    const acceptance = unit?.acceptanceCriteria ?? [];
    const files = unit?.scope?.files ?? [];

    const cp: Checkpoint = {
      runId: this.runId,
      taskId: evt.workUnitId,
      stepId: evt.eventId,
      parentStepId: undefined,
      agent: evt.agentId ?? 'unknown',
      timestamps: {
        createdAt: isoNow(),
        startedAt: evt.type === ProgressEventType.Started ? isoNow() : undefined,
        planReadyAt: evt.type === ProgressEventType.ReviewRequest ? isoNow() : undefined,
        committedAt: evt.type === ProgressEventType.Done ? isoNow() : undefined,
        failedAt: evt.type === ProgressEventType.Failed ? isoNow() : undefined,
      },
      state,
      lastCompletedStep: evt.type === ProgressEventType.Done ? evt.eventId : undefined,
      nextAction: next,
      acceptanceCriteria: acceptance,
      handoffDepth: { current: 0, max: 3 },
      coolingWindows: { duplicateContent: 10 },
      goal: request?.objective,
      vcs: {},
      plan: {},
      scope: { fileScopes: files, fileLocks: [], concurrencyGroupId: undefined },
      diagnostics:
        evt.type === ProgressEventType.Failed
          ? { failureSummary: evt.message ?? evt.error?.message ?? 'failed', lastStackTrace: evt.error?.stack ?? null }
          : undefined,
      artifacts:
        evt.artifacts && evt.artifacts.length
          ? { diffFiles: evt.artifacts.filter(a => a.type === 'diff').map(a => a.path!).filter(Boolean) }
          : undefined,
      retryState: undefined,
      loopGuard: undefined,
    };

    try {
      // Only persist when minimal required fields are present (validate will throw otherwise)
      await this.journal.upsertCheckpoint(cp);
    } catch (e) {
      // Skip incomplete checkpoints (e.g., missing fileScopes) to avoid disrupting runtime
      if (!(e instanceof CheckpointIncompleteError)) {
        // Re-throw unexpected errors
        throw e;
      }
    }
  }

  private static mapEvent(t: ProgressEventType): { state: CheckpointState; next: NextAction } {
    switch (t) {
      case ProgressEventType.Started:
        return { state: CheckpointState.Running, next: NextAction.Plan };
      case ProgressEventType.ReviewRequest:
        return { state: CheckpointState.Review, next: NextAction.Review };
      case ProgressEventType.Done:
        return { state: CheckpointState.Committed, next: NextAction.Handoff };
      case ProgressEventType.Blocker:
      case ProgressEventType.NeedsInfo:
        return { state: CheckpointState.Blocked, next: NextAction.Handoff };
      case ProgressEventType.Failed:
        return { state: CheckpointState.Failed, next: NextAction.Fail };
      case ProgressEventType.Cancelled:
        return { state: CheckpointState.Blocked, next: NextAction.Retry };
      case ProgressEventType.Artifact:
      case ProgressEventType.Heartbeat:
      default:
        return { state: CheckpointState.Running, next: NextAction.Plan };
    }
  }

  /** Default empty executor stream (immediate completion). */
  private static async *noopExecutor(_assignment: Assignment): AsyncGenerator<ProgressEvent> {
    return;
  }

  private static async *emptyStream(): AsyncGenerator<ProgressEvent> {
    return;
  }
}

export { TwoPhaseCommitCoordinator } from './TwoPhaseCommitCoordinator';
