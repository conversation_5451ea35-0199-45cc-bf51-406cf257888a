import {
  <PERSON><PERSON><PERSON>,
  Coordinator<PERSON><PERSON><PERSON><PERSON>onfig,
  Coordinator<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TwoPhaseParticipant,
  PrepareMessage,
  PrepareResponse,
  CommitMessage,
  CommitResponse,
  AbortMessage,
  AbortResponse,
  CoordinatorState,
} from './types';
import { RunJournal } from './journal/RunJournal';
import { ProtocolEventType } from './journal/types';
import { LockManager } from './locks/LockManager';
import { computeBackoffDelay } from '../../utils/backoff';
import { getConfig } from '../../utils/config';

type CoordinatorDeps = {
  runId: RunId;
  journal: RunJournal;
  lockManager: LockManager;
  config: CoordinatorRuntimeConfig;
};

type CallOutcome<T> =
  | { ok: true; value: T; respondedAt: string }
  | { ok: false; error?: Error; respondedAt: string; timedOut?: boolean };

const DEFAULT_RETRY_CFG = {
  baseDelayMs: 500,
  maxDelayMs: 8000,
  jitter: 0.2,
  maxAttempts: 5,
};

function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, Math.max(0, ms)));
}

/**
 * Coordinator implementing orchestrator-level two-phase commit with persisted decisions.
 */
export class TwoPhaseCommitCoordinator {
  private readonly runId: RunId;
  private readonly journal: RunJournal;
  private readonly lockManager: LockManager;
  private readonly config: CoordinatorRuntimeConfig;
  private readonly retryCfg: { baseDelayMs: number; maxDelayMs: number; jitter: number; maxAttempts: number };

  private seqNo = 0;
  private leaseTimer: NodeJS.Timeout | null = null;
  private locksHeld: string[] = [];

  constructor(deps: CoordinatorDeps) {
    this.runId = deps.runId;
    this.journal = deps.journal;
    this.lockManager = deps.lockManager;
    this.config = deps.config;

    const cfg = getConfig();
    const retry = cfg.orchestrator?.resilience?.retry ?? {};
    this.retryCfg = {
      baseDelayMs: retry.baseDelayMs ?? DEFAULT_RETRY_CFG.baseDelayMs,
      maxDelayMs: retry.maxDelayMs ?? DEFAULT_RETRY_CFG.maxDelayMs,
      jitter: retry.jitter ?? DEFAULT_RETRY_CFG.jitter,
      maxAttempts: retry.maxAttempts ?? DEFAULT_RETRY_CFG.maxAttempts,
    };
  }

  /** Execute a fresh 2PC transaction end-to-end. */
  public async runTransaction(ctx: CoordinatorTransactionContext): Promise<CoordinatorResult> {
    const prepareAcks: Record<string, PrepareResponse> = {};
    const commitAcks: Record<string, CommitResponse> = {};
    const abortAcks: Record<string, AbortResponse> = {};

    let decision: 'commit' | 'abort' = 'abort';
    let state: CoordinatorState = CoordinatorState.New;
    let released: string[] = [];

    try {
      state = CoordinatorState.Planning;
      await this.acquireLocks(ctx);
      state = CoordinatorState.Locking;

      const prep = await this.runPreparePhase(ctx);
      Object.assign(prepareAcks, prep);
      const allYes = this.allPreparedYes(prep);
      decision = allYes ? 'commit' : 'abort';

      if (!allYes) {
        state = CoordinatorState.PrepareTimeout;
      } else {
        state = CoordinatorState.Prepared;
      }

      await this.journal.recordDecisionPersisted({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        decision,
        resourceKeys: ctx.resourceKeys,
        participants: ctx.participants.map((p) => p.id),
        reason: allYes ? undefined : 'prepare_vote_rejected',
      });

      if (decision === 'commit') {
        state = CoordinatorState.DecisionPersistedCommit;
        const commit = await this.runCommitPhase(ctx, prep);
        Object.assign(commitAcks, commit);
        state = CoordinatorState.Committed;
      } else {
        state = CoordinatorState.DecisionPersistedAbort;
        const abort = await this.runAbortPhase(ctx, undefined, prep);
        Object.assign(abortAcks, abort);
        state = CoordinatorState.Aborted;
      }

      released = await this.releaseLocks(ctx);

      return {
        state,
        decision,
        prepareAcks,
        commitAcks,
        abortAcks,
        locksReleased: released,
      };
    } catch (err) {
      state = CoordinatorState.Failed;
      released = await this.releaseLocks(ctx);
      await this.journal.recordRecoveryMarker({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        note: 'transaction_failed',
        state: CoordinatorState.Failed,
        payload: { error: err instanceof Error ? err.message : String(err) },
      });
      throw err;
    }
  }

  /** Resume a transaction from persisted journal state. */
  public async recover(ctx: CoordinatorTransactionContext): Promise<CoordinatorResult> {
    const events = await this.journal.listProtocolEvents(this.runId, {
      taskId: ctx.taskId,
      txnId: ctx.txnId,
    });

    let maxSeq = 0;
    const prepareAcks: Record<string, PrepareResponse> = {};
    const commitAcks: Record<string, CommitResponse> = {};
    const abortAcks: Record<string, AbortResponse> = {};
    let decision: 'commit' | 'abort' | null = null;

    for (const evt of events) {
      if (evt.seqNo && evt.seqNo > maxSeq) maxSeq = evt.seqNo;
      switch (evt.type) {
        case ProtocolEventType.PrepareAck:
          prepareAcks[evt.participantId] = {
            vote: evt.vote,
            reason: evt.reason,
            respondedAt: evt.respondedAt,
            payload: evt.payload,
          };
          break;
        case ProtocolEventType.CommitAck:
          commitAcks[evt.participantId] = {
            ok: evt.ok,
            commitSha: evt.commitSha,
            respondedAt: evt.respondedAt,
            payload: evt.payload,
          };
          break;
        case ProtocolEventType.AbortAck:
          abortAcks[evt.participantId] = {
            ok: evt.ok,
            reason: evt.reason,
            respondedAt: evt.respondedAt,
            payload: evt.payload,
          };
          break;
        case ProtocolEventType.DecisionPersisted:
          decision = evt.decision;
          break;
        default:
          break;
      }
    }

    this.seqNo = maxSeq;

    if (!decision) {
      // No recorded decision, rerun from scratch (safe abort strategy)
      return this.runTransaction(ctx);
    }

    let finalState = CoordinatorState.Recovering;
    try {
      await this.acquireLocks(ctx);
      await this.journal.recordRecoveryMarker({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        state: CoordinatorState.Recovering,
        note: 'recovery_start',
      });

      if (decision === 'commit') {
        const outstanding = ctx.participants.filter((p) => !commitAcks[p.id]?.ok);
        if (outstanding.length > 0) {
          const supplemental = await this.runCommitPhase(ctx, prepareAcks, outstanding);
          Object.assign(commitAcks, supplemental);
        }
        finalState = CoordinatorState.Committed;
      } else {
        const outstanding = ctx.participants.filter((p) => !abortAcks[p.id]?.ok);
        if (outstanding.length > 0) {
        const supplemental = await this.runAbortPhase(ctx, outstanding, prepareAcks);
          Object.assign(abortAcks, supplemental);
        }
        finalState = CoordinatorState.Aborted;
      }

      const released = await this.releaseLocks(ctx);

      await this.journal.recordRecoveryMarker({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        state: finalState,
        note: 'recovery_complete',
        payload: { locksReleased: released },
      });

      return {
        state: finalState,
        decision,
        prepareAcks,
        commitAcks,
        abortAcks,
        locksReleased: released,
      };
    } catch (err) {
      await this.releaseLocks(ctx);
      await this.journal.recordRecoveryMarker({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        note: 'recovery_failed',
        state: CoordinatorState.Failed,
        payload: { error: err instanceof Error ? err.message : String(err) },
      });
      throw err;
    }
  }

  private nextSeq(): number {
    this.seqNo += 1;
    return this.seqNo;
  }

  private async acquireLocks(ctx: CoordinatorTransactionContext): Promise<void> {
    const keys = Array.from(new Set(ctx.resourceKeys)).sort();
    if (keys.length === 0) {
      this.locksHeld = [];
      return;
    }

    const start = Date.now();
    let attempt = 1;
    const priorityTs = Date.now();

    while (true) {
      const res = await this.lockManager.acquire(ctx.txnId, keys, this.config.lockLeaseMs, priorityTs);
      if (res.ok) {
        this.locksHeld = keys;
        this.startLeaseRenewal(ctx.txnId);
        const snapshot = await this.lockManager.snapshot();
        await this.journal.appendLockSnapshot(this.runId, ctx.taskId, ctx.txnId, this.nextSeq(), snapshot);
        return;
      }

      if (Date.now() - start >= this.config.prepareTimeoutMs) {
        throw new Error('lock_acquire_timeout');
      }

      const delay = computeBackoffDelay(attempt, this.retryCfg);
      await sleep(delay);
      if (this.retryCfg.maxAttempts <= 0 || attempt < this.retryCfg.maxAttempts) {
        attempt += 1;
      }
    }
  }

  private async releaseLocks(ctx: CoordinatorTransactionContext): Promise<string[]> {
    if (this.locksHeld.length === 0) {
      this.stopLeaseRenewal();
      return [];
    }
    const released = [...this.locksHeld];
    try {
      await this.lockManager.release(ctx.txnId);
      const snapshot = await this.lockManager.snapshot();
      await this.journal.appendLockSnapshot(this.runId, ctx.taskId, ctx.txnId, this.nextSeq(), snapshot);
    } finally {
      this.stopLeaseRenewal();
      this.locksHeld = [];
    }
    return released;
  }

  private startLeaseRenewal(txnId: string): void {
    this.stopLeaseRenewal();
    if (this.locksHeld.length === 0) return;

    this.leaseTimer = setInterval(() => {
      this.lockManager
        .renew(txnId, this.locksHeld, this.config.lockLeaseMs)
        .catch(() => {
          // best-effort, coordinator will retry on next tick
        });
    }, Math.max(100, this.config.lockRenewIntervalMs));
    if (typeof this.leaseTimer.unref === 'function') {
      this.leaseTimer.unref();
    }
  }

  private stopLeaseRenewal(): void {
    if (this.leaseTimer) {
      clearInterval(this.leaseTimer);
      this.leaseTimer = null;
    }
  }

  private allPreparedYes(acks: Record<string, PrepareResponse>): boolean {
    return Object.values(acks).every((ack) => ack.vote === 'yes');
  }

  private buildPrepareMessage(ctx: CoordinatorTransactionContext, seqNo: number): PrepareMessage {
    return {
      type: 'Prepare',
      runId: this.runId,
      taskId: ctx.taskId,
      txnId: ctx.txnId,
      seqNo,
      stepId: ctx.stepId,
      resourceKeys: ctx.resourceKeys,
      patchPlanHash: ctx.patchPlanHash,
      idempotencyKey: ctx.idempotencyKey,
      dedupKey: ctx.dedupKey,
      traceparent: ctx.traceparent,
      tracestate: ctx.tracestate,
      checkpoint: ctx.checkpoint,
      metadata: ctx.metadata,
      payload: ctx.metadata?.preparePayload,
    };
  }

  private buildCommitMessage(ctx: CoordinatorTransactionContext, seqNo: number, prepareAck?: PrepareResponse): CommitMessage {
    return {
      type: 'Commit',
      runId: this.runId,
      taskId: ctx.taskId,
      txnId: ctx.txnId,
      seqNo,
      stepId: ctx.stepId,
      resourceKeys: ctx.resourceKeys,
      patchPlanHash: ctx.patchPlanHash,
      idempotencyKey: ctx.idempotencyKey,
      dedupKey: ctx.dedupKey,
      traceparent: ctx.traceparent,
      tracestate: ctx.tracestate,
      checkpoint: ctx.checkpoint,
      metadata: { ...ctx.metadata, prepareAck },
      payload: ctx.metadata?.commitPayload,
    };
  }

  private buildAbortMessage(ctx: CoordinatorTransactionContext, seqNo: number, prepareAck?: PrepareResponse): AbortMessage {
    return {
      type: 'Abort',
      runId: this.runId,
      taskId: ctx.taskId,
      txnId: ctx.txnId,
      seqNo,
      stepId: ctx.stepId,
      resourceKeys: ctx.resourceKeys,
      patchPlanHash: ctx.patchPlanHash,
      idempotencyKey: ctx.idempotencyKey,
      dedupKey: ctx.dedupKey,
      traceparent: ctx.traceparent,
      tracestate: ctx.tracestate,
      checkpoint: ctx.checkpoint,
      metadata: { ...ctx.metadata, prepareAck },
      payload: ctx.metadata?.abortPayload,
    };
  }

  private async runPreparePhase(ctx: CoordinatorTransactionContext): Promise<Record<string, PrepareResponse>> {
    const responses: Record<string, PrepareResponse> = {};
    for (const participant of ctx.participants) {
      const seq = this.nextSeq();
      const message = this.buildPrepareMessage(ctx, seq);
      await this.journal.recordPrepareSent({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: seq,
        participantId: participant.id,
        resourceKeys: ctx.resourceKeys,
        payload: message.payload,
      });

      const outcome = await this.callParticipant(() => participant.prepare(message), this.config.prepareTimeoutMs);
      let response: PrepareResponse;
      if (outcome.ok) {
        response = outcome.value;
        response.vote = response.vote ?? 'unknown';
        response.respondedAt = outcome.respondedAt;
      } else if (outcome.timedOut) {
        response = { vote: 'unknown', reason: 'timeout', respondedAt: outcome.respondedAt };
      } else {
        response = { vote: 'no', reason: outcome.error?.message ?? 'prepare_failed', respondedAt: outcome.respondedAt };
      }
      responses[participant.id] = response;
      await this.journal.recordPrepareAck({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        participantId: participant.id,
        vote: response.vote,
        reason: response.reason,
        respondedAt: response.respondedAt,
        payload: response.payload,
      });
    }
    return responses;
  }

  private async runCommitPhase(
    ctx: CoordinatorTransactionContext,
    prepareAcks: Record<string, PrepareResponse>,
    subset?: TwoPhaseParticipant[],
  ): Promise<Record<string, CommitResponse>> {
    const responses: Record<string, CommitResponse> = {};
    const participants = subset ?? ctx.participants;
    for (const participant of participants) {
      const seq = this.nextSeq();
      const message = this.buildCommitMessage(ctx, seq, prepareAcks[participant.id]);
      await this.journal.recordCommitSent({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: seq,
        participantId: participant.id,
        payload: message.payload,
      });

      const outcome = await this.callParticipant(() => participant.commit(message), this.config.commitAckTimeoutMs);
      let response: CommitResponse;
      if (outcome.ok) {
        response = outcome.value;
        response.ok = response.ok ?? true;
        response.respondedAt = outcome.respondedAt;
      } else if (outcome.timedOut) {
        response = { ok: false, commitSha: null, respondedAt: outcome.respondedAt, payload: { timeout: true } };
      } else {
        response = { ok: false, commitSha: null, respondedAt: outcome.respondedAt, payload: { error: outcome.error?.message } };
      }
      responses[participant.id] = response;
      await this.journal.recordCommitAck({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        participantId: participant.id,
        ok: response.ok,
        commitSha: response.commitSha,
        respondedAt: response.respondedAt,
        payload: response.payload,
      });
    }
    return responses;
  }

  private async runAbortPhase(
    ctx: CoordinatorTransactionContext,
    subset?: TwoPhaseParticipant[],
    prepareAcks?: Record<string, PrepareResponse>,
  ): Promise<Record<string, AbortResponse>> {
    const responses: Record<string, AbortResponse> = {};
    const participants = subset ?? ctx.participants;
    for (const participant of participants) {
      const seq = this.nextSeq();
      const message = this.buildAbortMessage(ctx, seq, prepareAcks?.[participant.id]);
      await this.journal.recordAbortSent({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: seq,
        participantId: participant.id,
        payload: message.payload,
      });

      const outcome = await this.callParticipant(() => participant.abort(message), this.config.commitAckTimeoutMs);
      let response: AbortResponse;
      if (outcome.ok) {
        response = outcome.value;
        response.ok = response.ok ?? true;
        response.respondedAt = outcome.respondedAt;
      } else if (outcome.timedOut) {
        response = { ok: false, reason: 'timeout', respondedAt: outcome.respondedAt, payload: { timeout: true } };
      } else {
        response = { ok: false, reason: outcome.error?.message ?? 'abort_failed', respondedAt: outcome.respondedAt, payload: { error: outcome.error?.message } };
      }
      responses[participant.id] = response;
      await this.journal.recordAbortAck({
        runId: this.runId,
        taskId: ctx.taskId,
        txnId: ctx.txnId,
        seqNo: this.nextSeq(),
        participantId: participant.id,
        ok: response.ok,
        reason: response.reason,
        respondedAt: response.respondedAt,
        payload: response.payload,
      });
    }
    return responses;
  }

  private async callParticipant<T>(fn: () => Promise<T>, timeoutMs: number): Promise<CallOutcome<T>> {
    const start = Date.now();
    return new Promise((resolve) => {
      let settled = false;
      const timer = setTimeout(() => {
        if (settled) return;
        settled = true;
        resolve({ ok: false, timedOut: true, respondedAt: new Date().toISOString() });
      }, Math.max(1, timeoutMs));

      fn()
        .then((value) => {
          if (settled) return;
          settled = true;
          clearTimeout(timer);
          resolve({ ok: true, value, respondedAt: new Date().toISOString() });
        })
        .catch((error: Error) => {
          if (settled) return;
          settled = true;
          clearTimeout(timer);
          resolve({ ok: false, error, respondedAt: new Date().toISOString() });
        });
    });
  }
}
