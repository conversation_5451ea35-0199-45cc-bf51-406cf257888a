import { createHash } from 'crypto';
import {
  Assignment,
  AssignmentStatus,
  ContextPackage,
  OrchestrationRequest,
  WorkUnit,
  AgentProfile,
  WorkCategory,
} from '../types';

/**
 * AssignmentManager options controlling budgets, attempts, and instruction templating.
 */
export interface AssignmentManagerOptions {
  /** Default time budget in ms when no request/agent SLA provides one. Default: 15 minutes. */
  timeBudgetMsDefault?: number;
  /** Default token cap for instruction limits if not present in context. Default: 6000. */
  tokenCapDefault?: number;
  /** Maximum attempts the scheduler should allow per unit. Used for instruction limits. Default: 3. */
  maxAttemptsPerUnit?: number;
  /** Optional category-specific checklists appended to acceptance criteria. */
  checklists?: {
    coding?: string[];
    review?: string[];
    debug?: string[];
    research?: string[];
  };
  /** Optional overrides for sections in the instruction template. */
  instructionTemplateOverrides?: Partial<{
    objective: string;
    steps: string[];
    deliverables: string[];
    limits: string[];
  }>;
}

/**
 * Builds deterministic, self-contained assignment instructions and wraps them into an Assignment.
 * - IDs are deterministic-unique per (workUnit, agent, timestamp)
 * - Deadlines are computed per request/agent/default SLAs
 * - Instructions are minimal and explicit with ordered sections
 */
export class AssignmentManager {
  private readonly defaults: Required<Pick<AssignmentManagerOptions,
    'timeBudgetMsDefault' | 'tokenCapDefault' | 'maxAttemptsPerUnit'>>;
  private readonly checklists?: AssignmentManagerOptions['checklists'];
  private readonly templateOverrides?: AssignmentManagerOptions['instructionTemplateOverrides'];

  constructor(opts?: AssignmentManagerOptions) {
    this.defaults = {
      timeBudgetMsDefault: opts?.timeBudgetMsDefault ?? (15 * 60 * 1000), // 15m
      tokenCapDefault: opts?.tokenCapDefault ?? 6000,
      maxAttemptsPerUnit: opts?.maxAttemptsPerUnit ?? 3,
    };
    this.checklists = opts?.checklists;
    this.templateOverrides = opts?.instructionTemplateOverrides;
  }

  /**
   * Build the instruction text for a downstream agent, using the standardized template.
   *
   * Sections (exact order):
   * 1) Objective
   * 2) Scope
   * 3) Context
   * 4) Steps
   * 5) Deliverables
   * 6) Limits
   * 7) Acceptance Criteria
   */
  public buildInstruction(
    workUnit: WorkUnit,
    agent: AgentProfile,
    context: ContextPackage,
    request?: OrchestrationRequest
  ): string {
    // Deterministic content only. No timestamps or randomness here.
    const lines: string[] = [];

    // Objective
    const objective = this.templateOverrides?.objective ?? this.composeObjective(workUnit);
    lines.push('Objective:');
    lines.push(objective.trim());
    lines.push('');

    // Scope
    const scopeLines = this.composeScope(workUnit, context);
    lines.push('Scope:');
    lines.push(...scopeLines);
    lines.push('- Do not modify files outside scope.');
    lines.push('');

    // Context
    const contextLines = this.composeContext(context);
    lines.push('Context:');
    lines.push(...contextLines);
    lines.push('');

    // Steps
    const steps = this.composeSteps(workUnit, agent);
    lines.push('Steps:');
    for (const s of steps) lines.push(`- ${s}`);
    lines.push('');

    // Deliverables
    const deliverables = this.composeDeliverables();
    lines.push('Deliverables:');
    for (const d of deliverables) lines.push(`- ${d}`);
    lines.push('');

    // Limits
    const limits = this.composeLimits(agent, context, request);
    lines.push('Limits:');
    for (const l of limits) lines.push(`- ${l}`);
    lines.push('');

    // Acceptance Criteria
    const acceptance = this.composeAcceptance(workUnit);
    lines.push('Acceptance Criteria:');
    for (const a of acceptance) lines.push(`- ${a}`);

    // Truncate to keep under ~3–5k chars (soft cap).
    const maxChars = 4500;
    const text = lines.join('\n');
    if (text.length > maxChars) {
      // Soft truncation strategy: drop end while marking truncation.
      const keep = Math.max(0, maxChars - 15);
      return text.slice(0, keep).trimEnd() + '\n... (truncated)';
    }
    return text;
  }

  /**
   * Create an Assignment object for the provided work unit and agent, computing deterministic ID
   * and deadline. Attempts initialized to 0 and status set to 'queued'.
   */
  public createAssignment(
    workUnit: WorkUnit,
    agent: AgentProfile,
    context: ContextPackage,
    request?: OrchestrationRequest
  ): Assignment {
    const id = `as_${sha1(`${workUnit.id}|${agent.id}|${now()}`)}`;
    const deadline = this.computeDeadline(agent, request);
    const instructions = this.buildInstruction(workUnit, agent, context, request);
    const assignment: Assignment = {
      id,
      workUnitId: workUnit.id,
      agentId: agent.id,
      contextPackage: context,
      instructions,
      deadline,
      status: AssignmentStatus.Queued,
      attempts: 0,
    };
    return assignment;
  }

  /**
   * Compute the absolute deadline (epoch millis) based on:
   * 1) request.constraints.timeBudgetMs divided by concurrent lanes estimate (min 1), else
   * 2) agent.timeSLA.p95Ms, else
   * 3) default timeBudgetMsDefault.
   */
  public computeDeadline(agent: AgentProfile, request?: OrchestrationRequest): number | undefined {
    const timeBudgetMs = request?.constraints?.timeBudgetMs;
    const concurrencyHint = request?.constraints?.concurrency;
    const lanesEstimate = Math.max(
      1,
      Number.isFinite(concurrencyHint as number) && (concurrencyHint as number)! > 0
        ? (concurrencyHint as number)
        : (agent.concurrencyLimit && agent.concurrencyLimit > 0 ? Math.min(agent.concurrencyLimit, 8) : 1)
    );

    let chosenMs: number | undefined;
    if (typeof timeBudgetMs === 'number' && timeBudgetMs > 0) {
      chosenMs = Math.max(1, Math.floor(timeBudgetMs / lanesEstimate));
    } else if (typeof agent.timeSLA?.p95Ms === 'number' && agent.timeSLA.p95Ms > 0) {
      chosenMs = agent.timeSLA.p95Ms;
    } else {
      chosenMs = this.defaults.timeBudgetMsDefault;
    }

    return typeof chosenMs === 'number' ? now() + chosenMs : undefined;
  }

  // ===== Template composition helpers =====

  private composeObjective(workUnit: WorkUnit): string {
    const desc = (workUnit.description ?? '').trim();
    const headline = `${capitalize(workUnit.intent)} ${capitalize(workUnit.category)} task`;
    if (desc) {
      return `${headline}: ${desc}`;
    }
    return headline;
  }

  private composeScope(workUnit: WorkUnit, context: ContextPackage): string[] {
    const out: string[] = [];
    const files = [...(workUnit.scope.files ?? [])];
    files.sort((a, b) => a.localeCompare(b));
    const { list: formattedFiles, truncated } = fmtFiles(context, 20, files);

    if (formattedFiles.length) {
      out.push('Files:');
      for (const f of formattedFiles) out.push(`- ${f}`);
      if (truncated) out.push('... (truncated)');
    } else {
      out.push('Files: (none specified)');
    }

    // Also show components/modules/paths briefly if present
    const comp = truncateList([...(workUnit.scope.components ?? [])].sort(), 10);
    const mods = truncateList([...(workUnit.scope.modules ?? [])].sort(), 10);
    const paths = truncateList([...(workUnit.scope.paths ?? [])].sort(), 10);

    if (comp.items.length) {
      out.push('Components:');
      comp.items.forEach((c) => out.push(`- ${c}`));
      if (comp.truncated) out.push('... (truncated)');
    }
    if (mods.items.length) {
      out.push('Modules:');
      mods.items.forEach((m) => out.push(`- ${m}`));
      if (mods.truncated) out.push('... (truncated)');
    }
    if (paths.items.length) {
      out.push('Paths:');
      paths.items.forEach((p) => out.push(`- ${p}`));
      if (paths.truncated) out.push('... (truncated)');
    }

    return out;
  }

  private composeContext(context: ContextPackage): string[] {
    const out: string[] = [];

    const ifaceNames = truncateList(
      (context.interfaces ?? []).map((i) => i.name).sort(),
      20
    );
    out.push('Interfaces:');
    if (ifaceNames.items.length) {
      ifaceNames.items.forEach((n) => out.push(`- ${n}`));
      if (ifaceNames.truncated) out.push('... (truncated)');
    } else {
      out.push('- (none)');
    }

    const summaries = truncateList((context.priorSummaries ?? []).map((s) => truncateStr(s, 250)), 5);
    out.push('Prior Summaries:');
    if (summaries.items.length) {
      summaries.items.forEach((s) => out.push(`- ${s}`));
      if (summaries.truncated) out.push('... (truncated)');
    } else {
      out.push('- (none)');
    }

    const keys = truncateList([...(context.retrievalKeys ?? [])].sort(), 20);
    out.push('Retrieval Keys:');
    if (keys.items.length) {
      keys.items.forEach((k) => out.push(`- ${k}`));
      if (keys.truncated) out.push('... (truncated)');
    } else {
      out.push('- (none)');
    }

    return out;
  }

  private composeSteps(workUnit: WorkUnit, agent: AgentProfile): string[] {
    const base: string[] = [
      'Review scope and context; inspect relevant files before changes.',
      'Plan minimal changes aligned with Acceptance Criteria; avoid unrelated refactors.',
      'Implement the change and prepare an atomic patch/diff.',
      'Run or describe targeted tests/logs to validate behavior.',
      'Emit structured ProgressEvents (started, heartbeat, artifact, done; blocker/needs-info as necessary).',
      'Request review when done and provide a brief summary.',
    ];

    // Allow overrides but ensure boilerplate ("ProgressEvents" and "request review") are present.
    const steps = (this.templateOverrides?.steps?.length ? [...this.templateOverrides.steps] : base).slice(0, 6);

    const ensureContains = (needle: RegExp, fallback: string) => {
      if (!steps.some((s) => needle.test(s))) steps.push(fallback);
    };
    ensureContains(/ProgressEvents/i, 'Emit structured ProgressEvents (started, heartbeat, artifact, done; blocker/needs-info as necessary).');
    ensureContains(/request review/i, 'Request review when done and provide a brief summary.');

    // Keep between 3 and 6 steps
    return steps.slice(0, Math.max(3, Math.min(6, steps.length)));
  }

  private composeDeliverables(): string[] {
    const base: string[] = [
      'Atomic diff/patch applying only the necessary changes.',
      'Targeted tests or logs demonstrating the behavior change.',
      'Short implementation notes and any follow-ups.',
      'ProgressEvents capturing artifacts and completion.',
    ];
    return this.templateOverrides?.deliverables?.length ? this.templateOverrides.deliverables : base;
  }

  private composeLimits(agent: AgentProfile, context: ContextPackage, request?: OrchestrationRequest): string[] {
    const tokenCap = context.constraints?.tokenCap ?? this.defaults.tokenCapDefault;
    const timeLimitMs = context.constraints?.timeLimitMs ?? (this.deriveDeadlineDelta(agent, request) ?? this.defaults.timeBudgetMsDefault);
    const base: string[] = [
      `Token budget: ≤ ${tokenCap} tokens.`,
      `Time budget: ≤ ${Math.max(1, Math.floor(timeLimitMs))} ms.`,
      `Max attempts: ≤ ${this.defaults.maxAttemptsPerUnit}.`,
      'If blocked or missing info, emit blocker/needs-info and pause.',
      'Avoid non-essential changes outside the Acceptance Criteria.',
    ];
    return this.templateOverrides?.limits?.length ? this.templateOverrides.limits : base;
  }

  private deriveDeadlineDelta(agent: AgentProfile, request?: OrchestrationRequest): number | undefined {
    // Mirrors computeDeadline but returns the duration rather than absolute epoch.
    const timeBudgetMs = request?.constraints?.timeBudgetMs;
    const concurrencyHint = request?.constraints?.concurrency;
    const lanesEstimate = Math.max(
      1,
      Number.isFinite(concurrencyHint as number) && (concurrencyHint as number)! > 0
        ? (concurrencyHint as number)
        : (agent.concurrencyLimit && agent.concurrencyLimit > 0 ? Math.min(agent.concurrencyLimit, 8) : 1)
    );

    if (typeof timeBudgetMs === 'number' && timeBudgetMs > 0) {
      return Math.max(1, Math.floor(timeBudgetMs / lanesEstimate));
    }
    if (typeof agent.timeSLA?.p95Ms === 'number' && agent.timeSLA.p95Ms > 0) {
      return agent.timeSLA.p95Ms;
    }
    return this.defaults.timeBudgetMsDefault;
  }

  private composeAcceptance(workUnit: WorkUnit): string[] {
    const items: string[] = [];
    for (const c of workUnit.acceptanceCriteria ?? []) items.push(c);

    const cat = workUnit.category;
    const catKey: keyof NonNullable<AssignmentManagerOptions['checklists']> =
      cat === WorkCategory.Coding ? 'coding'
        : cat === WorkCategory.Review ? 'review'
        : cat === WorkCategory.Debug ? 'debug'
        : 'research';

    const checklist = this.checklists?.[catKey];
    if (checklist?.length) {
      const truncated = truncateList(checklist, 15);
      for (const x of truncated.items) items.push(x);
      if (truncated.truncated) items.push('... (truncated)');
    }

    return items;
  }
}

// ===== Utilities (not exported) =====

function sha1(s: string): string {
  return createHash('sha1').update(s).digest('hex');
}

function truncateList<T>(arr: T[], max: number): { items: T[]; truncated: boolean } {
  if (!Array.isArray(arr)) return { items: [], truncated: false };
  if (arr.length <= max) return { items: arr, truncated: false };
  return { items: arr.slice(0, max), truncated: true };
}

function truncateStr(s: string, max: number): string {
  if (s.length <= max) return s;
  return s.slice(0, Math.max(0, max - 3)).trimEnd() + '...';
}

function fmtFiles(
  context: ContextPackage,
  maxCount: number,
  files: string[]
): { list: string[]; truncated: boolean } {
  const ranges = new Map<string, { start?: number; end?: number }>();
  for (const f of context.files ?? []) {
    if (f?.path) ranges.set(f.path, { start: f.start, end: f.end });
  }
  const ordered = [...files];
  const formatted = ordered.map((p) => {
    const r = ranges.get(p);
    if (r && (typeof r.start === 'number' || typeof r.end === 'number')) {
      const start = typeof r.start === 'number' ? r.start : '';
      const end = typeof r.end === 'number' ? r.end : '';
      return `${p} [${start}-${end}]`;
    }
    return p;
  });
  const t = truncateList(formatted, maxCount);
  return { list: t.items, truncated: t.truncated };
}

function capitalize(s: string | WorkCategory): string {
  const v = String(s);
  return v.charAt(0).toUpperCase() + v.slice(1);
}

function now(): number {
  return Date.now();
}
