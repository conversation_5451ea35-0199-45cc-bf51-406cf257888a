import { researchAgent as defsResearchAgent } from "./BuiltInAgents";
const defsTools = (defsResearchAgent as any).tools;
export const researchAgent = {
  ...defsResearchAgent,
  // Preserve tools from definitions: convert tool map -> array
  tools: Array.isArray(defsTools)
    ? defsTools
    : (defsTools && typeof defsTools === 'object')
      ? Object.values(defsTools)
      : [],
} as typeof defsResearchAgent & { tools: any[] };

export * from "./vercel/research";
export { vercelResearchAgent as ResearchAgent } from "./vercel/research";
