import { memoryManager } from "@/memory/MemoryManager";
import { MemoryType } from "@/memory/types";

  export const shouldThrottleMemory = (toolName: string, recentMemoryNotes: Map<string, number>, windowMs = 60000): boolean => {
    const last = recentMemoryNotes.get(toolName) || 0;
    const now = Date.now();
    if (now - last < windowMs) return true;
    recentMemoryNotes.set(toolName, now);
    if (recentMemoryNotes.size > 50) {
      const oldest = Array.from(recentMemoryNotes.entries()).sort((a, b) => a[1] - b[1])[0]?.[0];
      if (oldest) recentMemoryNotes.delete(oldest);
    }
    return false;
  }

  export const maybeRememberToolOutcome = async (toolName: string, args: any, result: any, agentName: string, recentMemoryNotes: Map<string, number>) => {
    try {
      if (shouldThrottleMemory(toolName, recentMemoryNotes)) return;
      const hasUsefulInfo = () => {
        if (result == null) return false;
        if (typeof result === 'string') return result.length > 40;
        if (Array.isArray(result)) return result.length > 0;
        if (typeof result === 'object') return Object.keys(result).length > 0;
        return false;
      };
      if (!hasUsefulInfo()) return;
      const preview = (val: any) => {
        try { const s = typeof val === 'string' ? val : JSON.stringify(val); return s.length > 400 ? s.slice(0, 400) + '…' : s; } catch { return '[unserializable]'; }
      };
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        { type: 'tool_heuristic', tool: toolName, agent: agentName, argsPreview: preview(args), resultPreview: preview(result), timestamp: new Date().toISOString() },
        { tags: ['tool', 'heuristic', 'searchable', toolName], source: 'auto_memory', confidence: 0.8 }
      );
      console.log(`🧠 Remembered useful ${toolName} outcome for future recall`);
    } catch {}
  }
