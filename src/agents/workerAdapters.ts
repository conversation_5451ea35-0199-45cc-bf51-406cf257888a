/**
 * Worker Adapters
 *
 * Thin wrappers that route ModelOrchestrator worker delegations to the
 * Vercel AI Orchestrator's registered agents. This removes the need for
 * duplicate legacy agent implementations and ensures a single source of truth.
 */

function normalizeInput(input: string | any[]): string {
  return Array.isArray(input)
    ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
    : input;
}

function adapterFor(agentName: string, displayName?: string) {
  return {
    name: displayName || agentName,
    async execute(input: string | any[], options: any = {}) {
      const inputStr = normalizeInput(input);
      const { orchestrator } = await import('./DanteOrchestrator');
      // Map legacy options to orchestrator options
      return orchestrator.executeWithAgent(agentName, inputStr, {
        stream: options.stream || false,
        maxSteps: options.maxTurns || options.maxSteps || 10,
        onStepFinish: options.onStepFinish,
        onAgentSelected: options.onAgentSelected,
      } as any);
    }
  };
}

// Diagnostic agent retains its dedicated implementation for now
import { default as diagnostic } from './DiagnosticAgent';

export const workerAgents = {
  research: adapterFor('ResearchAgent', 'Research Agent'),
  'code-generation': adapterFor('CodeGenerationAgent', 'Code Generation Agent'),
  // Map code-refactor tasks to Code Generation Agent until a specialized refactor agent is added
  'code-refactor': adapterFor('CodeGenerationAgent', 'Code Generation Agent'),
  debug: adapterFor('DebugAgent', 'Debug Agent'),
  security: adapterFor('SecurityAnalysisAgent', 'Security Analysis Agent'),
  'code-review': adapterFor('CodeReviewAgent', 'Code Review Agent'),
  weather: adapterFor('WeatherAgent', 'Weather Agent'),
  orchestrator: adapterFor('TaskOrchestrator', 'Task Orchestrator'),
  planning: adapterFor('PlanningAgent', 'Planning Agent'),
  reminder: adapterFor('ReminderAgent', 'Reminder Agent'),
  diagnostic: diagnostic, // Keep dedicated implementation
  'computer-use': adapterFor('ComputerUseAgent', 'Computer Use Agent'),
  };

export type WorkerAgentsMap = typeof workerAgents;
