import { stepCountIs } from 'ai';
import { interruptBus } from '../utils/interruptBus';
import { maybeRememberToolOutcome } from './helpers/memory';
import * as fsSync2 from 'fs';
import * as crypto from 'crypto';

/**
 * Common configuration factory for AI SDK models
 * Provides standardized configuration with tool tracking, verification, and error handling
 */

// Helper functions
export const safeParseArgs = (raw: any): any => {
  if (!raw) return undefined;
  if (typeof raw === 'string') {
    try { return JSON.parse(raw); } catch { return undefined; }
  }
  return raw;
};

export const recordPathHit = (map: Map<string, number>, value: unknown): number | undefined => {
  if (typeof value !== 'string' || value.length === 0) return undefined;
  const prev = map.get(value) ?? 0;
  const next = prev + 1;
  map.set(value, next);
  return next;
};

export const summarize = (val: any, depth = 0): any => {
  try {
    if (val == null) return val;
    if (typeof val === 'string') return val.length > 200 ? val.slice(0, 200) + '…' : val;
    if (typeof val === 'number' || typeof val === 'boolean') return val;
    if (Array.isArray(val)) return { length: val.length, sample: val.slice(0, 3).map(v => summarize(v, depth + 1)) };
    if (typeof val === 'object') {
      const out: Record<string, any> = {};
      for (const [k, v] of Object.entries(val).slice(0, 8)) out[k] = summarize(v, depth + 1);
      return out;
    }
    return String(val);
  } catch { return '[unserializable]'; }
};

export interface CommonConfigOptions {
  model: any;
  agent: any;
  input: string;
  steeringPreamble?: string;
  options: any;
  finalTools: Record<string, any>;
  forceToolChoice?: any;
  agentName: string;
  runToolStats: any;
  orchestratorInstance: any; // The DanteOrchestrator instance for accessing methods and properties
}

export function createCommonConfig({
  model,
  agent,
  input,
  steeringPreamble = '',
  options,
  finalTools,
  forceToolChoice,
  agentName,
  runToolStats,
  orchestratorInstance
}: CommonConfigOptions) {
  return {
    model,
    system: agent.instructions,
    prompt: `${input}${steeringPreamble}`,
    abortSignal: options.sessionId ? interruptBus.ensureController(options.sessionId).signal : undefined,
    tools: Object.keys(finalTools).length > 0 ? finalTools : undefined,
    toolChoice: forceToolChoice,
    providerOptions: {
      openai: {
        reasoningEffort: 'medium',
        reasoningSummary: 'auto'
      }
    },
    stopWhen: stepCountIs(options.maxSteps ?? agent.maxSteps ?? 10),
    onStepFinish: (step: any) => {
      if (options.onStepFinish) options.onStepFinish(step);

      if (step.toolCalls?.length > 0) {
        for (const toolCall of step.toolCalls) {
          const tName = (toolCall.name || toolCall.toolName || toolCall.tool_name || toolCall.function?.name || 'unknown_tool');
          const tArgs = (toolCall.input || toolCall.args || toolCall.function?.arguments || toolCall.parameters);
          runToolStats.totalCalls += 1;
          const parsedArgs = safeParseArgs(tArgs);
          const updateMaxRepeat = (category: 'readFile' | 'listDirectory', pathCandidate: unknown) => {
            const hit = recordPathHit(runToolStats[category].paths, pathCandidate);
            if (typeof hit === 'number') {
              runToolStats[category].maxRepeat = Math.max(runToolStats[category].maxRepeat, hit);
            }
          };
          if (tName === 'read_file') {
            runToolStats.readFile.total += 1;
            updateMaxRepeat('readFile', parsedArgs?.filePath ?? parsedArgs?.path);
          } else if (tName === 'read_files') {
            const paths = Array.isArray(parsedArgs?.filePaths) ? parsedArgs.filePaths : [];
            if (paths.length === 0) {
              runToolStats.readFile.total += 1;
              updateMaxRepeat('readFile', parsedArgs?.filePath ?? parsedArgs?.path);
            } else {
              runToolStats.readFile.total += paths.length;
              for (const p of paths) updateMaxRepeat('readFile', p);
            }
          } else if (tName === 'list_directory') {
            runToolStats.listDirectory.total += 1;
            updateMaxRepeat('listDirectory', parsedArgs?.dirPath ?? parsedArgs?.path ?? parsedArgs?.directory);
          } else if (tName === 'file_edit') {
            runToolStats.fileEdit += 1;
          }
          orchestratorInstance.emit('toolCall', { agent: agentName, tool: tName, args: tArgs });
          try { console.log(`🛠️ Tool call | Agent: ${agentName} | Tool: ${tName} | Args: ${JSON.stringify(summarize(tArgs))}`); } catch {}
          try {
            orchestratorInstance.recentToolArgs.push({ name: tName, args: tArgs, t: Date.now() });
            if (orchestratorInstance.recentToolArgs.length > 20) orchestratorInstance.recentToolArgs.shift();
            // Capture projectId on set_working_directory for durable task snapshots
            if (tName === 'set_working_directory') {
              try {
                const argObj = typeof tArgs === 'string' ? JSON.parse(tArgs) : (tArgs || {});
                const dirPath = argObj?.dirPath || argObj?.path || argObj?.cwd;
                if (typeof dirPath === 'string' && dirPath.length > 0) {
                  orchestratorInstance.currentProjectId = dirPath;
                }
              } catch (e) {
                console.warn('Failed to extract dirPath from set_working_directory args:', e);
              }
            }
          } catch (e) {
            console.warn('Failed to record recent tool call:', e);
          }
          if (options.onToolCall) options.onToolCall(tName, tArgs);
        }
      }

      if (step.toolResults?.length > 0) {
        for (const toolResult of step.toolResults) {
          try {
            const rName = (toolResult.name || toolResult.toolName || toolResult.tool_name || 'tool');
            console.log(`🧩 Tool result | Agent: ${agentName} | Tool: ${rName} | Result: ${JSON.stringify(summarize(toolResult.result ?? toolResult))}`);
            let pairedArgs: any = undefined;
            const now = Date.now();
            for (let i = orchestratorInstance.recentToolArgs.length - 1; i >= 0; i--) {
              const entry = orchestratorInstance.recentToolArgs[i];
              if (entry.name === rName && now - entry.t < 5000) { pairedArgs = entry.args; break; }
            }
            // Auto-retry guard for unified_diff with mergeStrategy escalation
            try {
              const raw = pairedArgs;
              const argsObj = typeof raw === 'string' ? (JSON.parse(raw) as any) : (raw || {});
              const val0: any = (toolResult.result ?? toolResult);
              const isFileEdit = rName === 'file_edit';
              const failed = !!(val0 && val0.success === false);
              const isUnifiedDiff = argsObj && argsObj.operation === 'patch' && ((argsObj.patchFormat === 'unified_diff') || typeof argsObj.diff === 'string');
              if (isFileEdit && failed && isUnifiedDiff) {
                void orchestratorInstance._attemptUnifiedDiffAutoRetry(finalTools, agentName, argsObj, toolResult, summarize)
                  .catch((err: any) => console.warn('Unified diff auto-retry async error:', err));
              }
            } catch (autoRetryErr) {
              console.warn('Unified diff auto-retry guard failed:', autoRetryErr);
            }
            void maybeRememberToolOutcome(rName, pairedArgs, toolResult.result ?? toolResult, agentName, orchestratorInstance.recentMemoryNotes);
            // Record a compact preview for handoff summaries
            const preview = (() => {
              try {
                const val = toolResult.result ?? toolResult;
                const s = typeof val === 'string' ? val : JSON.stringify(val);
                return s.length > 160 ? s.slice(0, 160) + '…' : s;
              } catch { return '[unserializable]'; }
            })();
            const buf = orchestratorInstance.recentToolResultsByAgent.get(agentName) || [];
            buf.push({ name: rName, preview, t: Date.now() });
            // Keep last 10
            while (buf.length > 10) buf.shift();
            orchestratorInstance.recentToolResultsByAgent.set(agentName, buf);

            // Record file_edit success status for guards
            try {
              if (rName === 'file_edit') {
                const val = (toolResult.result ?? toolResult);
                const success = !!(val && (val.success === true));
                const diagOk = typeof val?.diagnostics?.ok === 'boolean' ? !!val.diagnostics.ok : true;

                // Enhanced write verification with better success detection
                const modifiedFlag = (typeof val?.modified === 'boolean') ? !!val.modified : true;
                const replacements = typeof val?.replacements === 'number' ? val.replacements : undefined;
                const notNoOp = modifiedFlag && (replacements == null || replacements > 0);

                // Improved hash verification with fallback strategies
                let persistedOk = true;
                let hashVerified = false;
                try {
                  const p = typeof (val?.path) === 'string' ? val.path : undefined;
                  const afterHash: string | undefined = val?.hashes?.after;
                  if (p && afterHash) {
                    try {
                      const data = fsSync2.readFileSync(p, 'utf-8');
                      const h = crypto.createHash('sha256').update(data).digest('hex');
                      if (h !== afterHash) {
                        persistedOk = false;
                        console.warn(`Hash verification failed for ${p}: expected ${afterHash}, got ${h}`);
                      }
                      hashVerified = true;
                    } catch (readErr) {
                      console.warn(`Failed to read file for hash verification ${p}:`, readErr);
                      persistedOk = false;
                    }
                  }
                } catch (hashErr) {
                  console.warn('Hash verification error:', hashErr);
                  persistedOk = false;
                }

                // If hash verification wasn't available, use alternative success indicators
                if (!hashVerified) {
                  // Consider it successful if we have success=true and either diagnostics are OK or we have modification indicators
                  if (success && (diagOk || notNoOp)) {
                    persistedOk = true;
                    console.log(`No hash available for ${val?.path || 'unknown file'}, using alternative verification`);
                  }
                }

                const ok = success && diagOk && notNoOp && persistedOk;
                const path = typeof val?.path === 'string' ? val.path : undefined;

                // Enhanced logging for better debugging
                if (ok) {
                  console.log(`✅ File edit verified successfully: ${path || 'unknown'} (success=${success}, diag=${diagOk}, modified=${modifiedFlag}, persisted=${persistedOk})`);
                } else {
                  console.warn(`❌ File edit verification failed: ${path || 'unknown'} (success=${success}, diag=${diagOk}, modified=${modifiedFlag}, persisted=${persistedOk})`);
                }

                const wb = orchestratorInstance.recentWriteStatusByAgent.get(agentName) || [];
                wb.push({ name: rName, ok, path, t: Date.now() });
                while (wb.length > 20) wb.shift();
                orchestratorInstance.recentWriteStatusByAgent.set(agentName, wb);
              }
              // Track diagnostics outcomes for stronger no-op detection
              if (rName === 'diagnose_file_syntax') {
                try {
                  const valRaw: any = (toolResult.result ?? toolResult);
                  let ok: boolean | undefined;
                  let diagPath: string | undefined;
                  if (typeof valRaw === 'string') {
                    try {
                      const parsed = JSON.parse(valRaw);
                      ok = !!(parsed?.diagnostics?.ok ?? parsed?.ok ?? parsed?.success);
                      diagPath = parsed?.diagnostics?.file || parsed?.file || parsed?.path;
                    } catch (e) {
                      console.warn('Failed to parse diagnose_file_syntax result as JSON:', e);
                    }
                  } else if (typeof valRaw === 'object' && valRaw) {
                    ok = !!(valRaw?.diagnostics?.ok ?? valRaw?.ok ?? valRaw?.success);
                    diagPath = valRaw?.diagnostics?.file || valRaw?.file || valRaw?.path;
                  }
                  const dbuf = orchestratorInstance.recentDiagnosticsOkByAgent.get(agentName) || [];
                  dbuf.push({ path: typeof diagPath === 'string' ? diagPath : undefined, ok: !!ok, t: Date.now() });
                  while (dbuf.length > 20) dbuf.shift();
                  orchestratorInstance.recentDiagnosticsOkByAgent.set(agentName, dbuf);
                } catch (e) {
                  console.warn('diagnose_file_syntax result processing failed', { agent: agentName, error: e });
                }
              }
              // Track assertion outcomes to allow verification-based acceptance
              if (rName === 'assert_file_contains') {
                const val: any = (toolResult.result ?? toolResult);
                const ok = !!(val && (val.success === true));
                const path = typeof val?.path === 'string' ? val.path : undefined;
                const abuf = orchestratorInstance.recentAssertStatusByAgent.get(agentName) || [];
                abuf.push({ ok, path, t: Date.now() });
                while (abuf.length > 20) abuf.shift();
                orchestratorInstance.recentAssertStatusByAgent.set(agentName, abuf);
              }
            } catch (e) {
              console.warn('Error during verification-based success check (assert/diagnose loop):', e);
            }
          } catch (e) {
            console.warn('Error during verification-based success check (assert/diagnose loop):', e);
          }
        }
      }
    }
  } as any;
}
