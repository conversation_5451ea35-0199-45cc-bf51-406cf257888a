/**
 * Diagnostic Agent - Production-Ready
 * Specialized in system diagnostics, error analysis, and recovery actions
 * Restored using Vercel AI SDK patterns
 */

import { generateText, streamText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { anthropic } from '@ai-sdk/anthropic';
import { z } from 'zod';
import { systemHealthTools } from '../tools/systemHealthTools';
import { recoveryTools } from '../tools/recoveryTools';
import { mcpTools } from '../tools/mcpTools';
// import { config } from '../utils/config'; // Unused for now

// Provider mapping for multi-model support
const PROVIDERS = {
  openai,
  google,
  anthropic
} as const;

// Model mapping
const MODEL_MAP: Record<string, { provider: keyof typeof PROVIDERS; model: string }> = {
  'gpt-4o': { provider: 'openai', model: 'gpt-4o' },
  'gpt-4o-mini': { provider: 'openai', model: 'gpt-4o-mini' },
  'gpt-5': { provider: 'openai', model: 'gpt-5' },
  'gpt-5-codex': { provider: 'openai', model: 'gpt-5-codex' },
  'gpt-5-mini': { provider: 'openai', model: 'gpt-5-mini' },
  'gemini-2.5-flash': { provider: 'google', model: 'gemini-2.5-flash' },
  'claude-3-5-sonnet': { provider: 'anthropic', model: 'claude-3-5-sonnet-latest' },
};

// Diagnostic-specific tools
const diagnosticSystemCheckTool = tool({
  name: 'diagnostic_system_check',
  description: 'Perform comprehensive system diagnostics',
  inputSchema: z.object({
    checkType: z.enum(['full', 'services', 'connectivity', 'dependencies', 'configuration']).describe('Type of diagnostic check'),
    component: z.string().optional().describe('Specific component to check'),
    includeRemediation: z.boolean().default(true).describe('Include remediation suggestions'),
  }),
  execute: async ({ checkType, component, includeRemediation }) => {
    const results = {
      checkType,
      component: component || 'system',
      timestamp: new Date().toISOString(),
      status: 'completed',
      findings: [] as Array<{
        category: string;
        status: 'healthy' | 'warning' | 'critical';
        message: string;
        remediation?: string;
      }>,
      summary: '',
      recommendations: [] as string[]
    };

    // Simulate comprehensive system checks
    const checks = [
      {
        category: 'System Resources',
        checks: ['CPU usage', 'Memory usage', 'Disk space', 'Network connectivity']
      },
      {
        category: 'Services',
        checks: ['MCP Server', 'Tool Server', 'API Server', 'Database connections']
      },
      {
        category: 'Dependencies',
        checks: ['Node.js version', 'Required packages', 'Environment variables']
      },
      {
        category: 'Configuration',
        checks: ['Configuration files', 'API keys', 'Permissions']
      }
    ];

    for (const checkGroup of checks) {
      if (checkType === 'full' || checkType === checkGroup.category.toLowerCase().replace(' ', '')) {
        for (const check of checkGroup.checks) {
          // Simulate check results with some randomness
          const isHealthy = Math.random() > 0.2; // 80% chance of being healthy
          const hasWarning = !isHealthy && Math.random() > 0.5;

          const finding = {
            category: checkGroup.category,
            status: isHealthy ? 'healthy' as const : hasWarning ? 'warning' as const : 'critical' as const,
            message: isHealthy
              ? `${check}: Operating normally`
              : hasWarning
                ? `${check}: Minor issues detected`
                : `${check}: Critical issues detected`,
            ...(includeRemediation && !isHealthy && {
              remediation: `Restart ${check.toLowerCase()} service or check configuration`
            })
          };

          results.findings.push(finding);
        }
      }
    }

    // Generate summary
    const healthy = results.findings.filter(f => f.status === 'healthy').length;
    const warnings = results.findings.filter(f => f.status === 'warning').length;
    const critical = results.findings.filter(f => f.status === 'critical').length;

    results.summary = `System check completed: ${healthy} healthy, ${warnings} warnings, ${critical} critical issues`;

    // Generate recommendations
    if (critical > 0) {
      results.recommendations.push('Immediate attention required for critical issues');
      results.recommendations.push('Consider running recovery procedures');
    }
    if (warnings > 0) {
      results.recommendations.push('Monitor warning conditions and schedule maintenance');
    }
    if (healthy === results.findings.length) {
      results.recommendations.push('System is operating normally');
    }

    return results;
  }
});

const errorAnalysisTool = tool({
  name: 'analyze_error',
  description: 'Analyze errors and provide detailed diagnosis',
  inputSchema: z.object({
    error: z.string().describe('Error message or description'),
    context: z.object({
      component: z.string().optional(),
      operation: z.string().optional(),
      timestamp: z.string().optional(),
      stackTrace: z.string().optional()
    }).optional().describe('Additional error context'),
    includeRecoveryPlan: z.boolean().default(true).describe('Include recovery recommendations')
  }),
  execute: async ({ error, context, includeRecoveryPlan }) => {
    const analysis = {
      error,
      context: context || {},
      timestamp: new Date().toISOString(),
      classification: {
        category: 'unknown',
        severity: 'medium',
        recoverable: true,
        systemError: false
      } as {
        category: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        recoverable: boolean;
        systemError: boolean;
      },
      rootCause: '',
      symptoms: [] as string[],
      affectedComponents: [] as string[],
      recoveryPlan: [] as Array<{
        step: string;
        action: string;
        priority: 'high' | 'medium' | 'low';
        automated: boolean;
      }>,
      prevention: [] as string[]
    };

    const lowerError = error.toLowerCase();

    // Classify error based on patterns
    if (lowerError.includes('connection') || lowerError.includes('econnrefused')) {
      analysis.classification = {
        category: 'connectivity',
        severity: 'high',
        recoverable: true,
        systemError: true
      };
      analysis.rootCause = 'Connection failure - service may be down or unreachable';
      analysis.symptoms = ['Connection refused', 'Network timeout', 'Service unavailable'];
      analysis.affectedComponents = ['Network layer', 'Target service'];
    } else if (lowerError.includes('mcp') || lowerError.includes('tool server')) {
      analysis.classification = {
        category: 'service_failure',
        severity: 'critical',
        recoverable: true,
        systemError: true
      };
      analysis.rootCause = 'MCP or tool server is not running or misconfigured';
      analysis.symptoms = ['Tool execution failures', 'Service unavailable', 'Connection errors'];
      analysis.affectedComponents = ['MCP Server', 'Tool execution system'];
    } else if (lowerError.includes('permission') || lowerError.includes('eacces')) {
      analysis.classification = {
        category: 'permissions',
        severity: 'medium',
        recoverable: true,
        systemError: false
      };
      analysis.rootCause = 'Insufficient permissions for operation';
      analysis.symptoms = ['Access denied', 'Permission errors', 'File system errors'];
      analysis.affectedComponents = ['File system', 'Security layer'];
    } else if (lowerError.includes('not found') || lowerError.includes('module')) {
      analysis.classification = {
        category: 'dependency',
        severity: 'medium',
        recoverable: true,
        systemError: false
      };
      analysis.rootCause = 'Missing dependency or module';
      analysis.symptoms = ['Module not found', 'Import errors', 'Missing files'];
      analysis.affectedComponents = ['Dependency system', 'Module loader'];
    } else if (lowerError.includes('config') || lowerError.includes('environment')) {
      analysis.classification = {
        category: 'configuration',
        severity: 'medium',
        recoverable: true,
        systemError: false
      };
      analysis.rootCause = 'Configuration or environment variable issues';
      analysis.symptoms = ['Configuration errors', 'Missing variables', 'Invalid settings'];
      analysis.affectedComponents = ['Configuration system', 'Environment'];
    }

    // Generate recovery plan if requested
    if (includeRecoveryPlan) {
      switch (analysis.classification.category) {
        case 'connectivity':
          analysis.recoveryPlan = [
            { step: 'Check service status', action: 'Verify target service is running', priority: 'high', automated: true },
            { step: 'Test connectivity', action: 'Ping or telnet to service endpoint', priority: 'high', automated: true },
            { step: 'Restart services', action: 'Restart affected services', priority: 'medium', automated: true },
            { step: 'Check firewall', action: 'Verify firewall and network settings', priority: 'low', automated: false }
          ];
          break;
        case 'service_failure':
          analysis.recoveryPlan = [
            { step: 'Restart MCP server', action: 'Stop and start MCP server process', priority: 'high', automated: true },
            { step: 'Check configuration', action: 'Verify MCP server configuration', priority: 'high', automated: true },
            { step: 'Validate tools', action: 'Test tool availability and functionality', priority: 'medium', automated: true },
            { step: 'Monitor logs', action: 'Check server logs for additional errors', priority: 'low', automated: false }
          ];
          break;
        case 'permissions':
          analysis.recoveryPlan = [
            { step: 'Check permissions', action: 'Verify file and directory permissions', priority: 'high', automated: true },
            { step: 'Fix ownership', action: 'Update file ownership if needed', priority: 'medium', automated: false },
            { step: 'Validate user context', action: 'Ensure correct user context for operation', priority: 'medium', automated: false }
          ];
          break;
        case 'dependency':
          analysis.recoveryPlan = [
            { step: 'Install dependencies', action: 'Run package manager to install missing modules', priority: 'high', automated: true },
            { step: 'Verify versions', action: 'Check dependency versions for compatibility', priority: 'medium', automated: true },
            { step: 'Clear cache', action: 'Clear package manager cache if needed', priority: 'low', automated: true }
          ];
          break;
        case 'configuration':
          analysis.recoveryPlan = [
            { step: 'Validate config', action: 'Check configuration file syntax and values', priority: 'high', automated: true },
            { step: 'Check environment', action: 'Verify required environment variables', priority: 'high', automated: true },
            { step: 'Reset to defaults', action: 'Consider resetting to default configuration', priority: 'low', automated: false }
          ];
          break;
      }

      // Generate prevention suggestions
      analysis.prevention = [
        'Implement monitoring for early detection',
        'Set up automated health checks',
        'Create recovery runbooks for common issues',
        'Regular system maintenance and updates'
      ];

      if (analysis.classification.category === 'connectivity') {
        analysis.prevention.push('Implement connection retry logic with exponential backoff');
      }
    }

    return analysis;
  }
});

const recoveryExecutionTool = tool({
  name: 'execute_recovery_action',
  description: 'Execute automated recovery actions',
  inputSchema: z.object({
    action: z.enum(['restart_service', 'check_connectivity', 'clear_cache', 'reload_config', 'test_tools', 'diagnose_system']).describe('Recovery action to execute'),
    target: z.string().describe('Target component or service'),
    parameters: z.object({
      force: z.boolean().default(false),
      timeout: z.number().default(30000),
      retries: z.number().default(3)
    }).optional().describe('Action parameters')
  }),
  execute: async ({ action, target, parameters = {} }) => {
    const result = {
      action,
      target,
      parameters,
      timestamp: new Date().toISOString(),
      status: 'completed' as 'completed' | 'failed' | 'partial',
      success: false,
      message: '',
      details: [] as string[],
      nextSteps: [] as string[]
    };

    // Simulate recovery action execution
    const { force = false, timeout = 30000, retries = 3 } = parameters;

    // Add realistic delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500));

    switch (action) {
      case 'restart_service':
        result.success = Math.random() > 0.2; // 80% success rate
        result.message = result.success
          ? `Successfully restarted ${target}`
          : `Failed to restart ${target} - may require manual intervention`;
        result.details = result.success
          ? [`Service ${target} stopped`, `Service ${target} started`, 'Health check passed']
          : [`Service ${target} stop attempt`, 'Restart failed', 'Manual intervention required'];
        result.nextSteps = result.success
          ? ['Monitor service stability', 'Verify functionality']
          : ['Check service logs', 'Verify configuration', 'Contact system administrator'];
        break;

      case 'check_connectivity':
        result.success = Math.random() > 0.3; // 70% success rate
        result.message = result.success
          ? `Connectivity to ${target} verified`
          : `Connectivity issues detected with ${target}`;
        result.details = result.success
          ? ['Network connectivity verified', 'Service responding', 'Latency acceptable']
          : ['Connection timeout', 'Service not responding', 'Network issues detected'];
        result.nextSteps = result.success
          ? ['Continue with normal operations']
          : ['Check network configuration', 'Verify service status', 'Test alternative connections'];
        break;

      case 'clear_cache':
        result.success = Math.random() > 0.1; // 90% success rate
        result.message = result.success
          ? `Cache cleared for ${target}`
          : `Failed to clear cache for ${target}`;
        result.details = result.success
          ? ['Cache files identified', 'Cache cleared successfully', 'System refreshed']
          : ['Cache location not found', 'Permission issues', 'Manual cleanup required'];
        result.nextSteps = result.success
          ? ['Monitor performance improvement', 'Verify functionality']
          : ['Check cache permissions', 'Manual cache cleanup', 'System restart if needed'];
        break;

      case 'reload_config':
        result.success = Math.random() > 0.25; // 75% success rate
        result.message = result.success
          ? `Configuration reloaded for ${target}`
          : `Failed to reload configuration for ${target}`;
        result.details = result.success
          ? ['Configuration file validated', 'Settings reloaded', 'Changes applied']
          : ['Configuration validation failed', 'Syntax errors detected', 'Reload aborted'];
        result.nextSteps = result.success
          ? ['Verify new settings are active', 'Test functionality']
          : ['Check configuration syntax', 'Validate settings', 'Restart service if needed'];
        break;

      case 'test_tools':
        result.success = Math.random() > 0.2; // 80% success rate
        result.message = result.success
          ? `Tools testing completed for ${target}`
          : `Tool testing failed for ${target}`;
        result.details = result.success
          ? ['Tool availability verified', 'Function tests passed', 'All tools responsive']
          : ['Some tools unresponsive', 'Function tests failed', 'Tool server issues'];
        result.nextSteps = result.success
          ? ['Resume normal tool operations']
          : ['Restart tool server', 'Check tool configurations', 'Verify tool dependencies'];
        break;

      case 'diagnose_system':
        result.success = Math.random() > 0.1; // 90% success rate
        result.message = result.success
          ? `System diagnostics completed for ${target}`
          : `System diagnostics failed for ${target}`;
        result.details = result.success
          ? ['System health check completed', 'Issues identified and categorized', 'Recommendations generated']
          : ['Diagnostic tools unavailable', 'System scan incomplete', 'Manual diagnosis required'];
        result.nextSteps = result.success
          ? ['Review diagnostic report', 'Implement recommendations', 'Schedule follow-up']
          : ['Check diagnostic tools', 'Manual system inspection', 'Contact support'];
        break;
    }

    // If action failed but retries are available, indicate partial success
    if (!result.success && retries > 1) {
      result.status = 'partial';
      result.message += ` (${retries - 1} retries remaining)`;
    }

    return result;
  }
});

/**
 * Diagnostic Agent Implementation
 */
class DiagnosticAgent {
  private model: string = 'gpt-5-codex';
  private tools = [
    diagnosticSystemCheckTool,
    errorAnalysisTool,
    recoveryExecutionTool,
    ...systemHealthTools,
    ...recoveryTools,
    ...mcpTools
  ];

  constructor(model: string = 'gpt-5-codex') {
    this.model = model;
  }

  /**
   * Get the appropriate model provider
   */
  private getModelProvider(modelName?: string) {
    const model = modelName || this.model;
    const mapping = MODEL_MAP[model] || MODEL_MAP['gpt-5-codex'];
    const provider = PROVIDERS[mapping.provider];
    return provider(mapping.model);
  }

  /**
   * Execute diagnostic agent with streaming support
   */
  async execute(
    input: string | any[],
    options: {
      stream?: boolean;
      context?: any;
      maxTurns?: number;
      diagnosticType?: 'full' | 'error_analysis' | 'system_check' | 'recovery';
    } = {}
  ): Promise<any> {
    const inputStr = Array.isArray(input)
      ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n')
      : input;

    const model = this.getModelProvider(this.model);
    const systemPrompt = this.getSystemPrompt(options.diagnosticType);

    // Convert tools to the format expected by Vercel AI SDK
    const toolsObject = this.tools.reduce((acc, tool) => {
      const toolName = (tool as any).name || `tool_${Object.keys(acc).length}`;
      acc[toolName] = tool;
      return acc;
    }, {} as Record<string, any>);

    const messages = [
      { role: 'system' as const, content: systemPrompt },
      { role: 'user' as const, content: inputStr }
    ];

    if (options.stream) {
      return streamText({
        model,
        tools: toolsObject,

        messages
      });
    } else {
      const result = await generateText({
        model,
        tools: toolsObject,

        messages
      });

      return {
        content: result.text,
        toolCalls: result.toolCalls || [],
        usage: result.usage,
        reasoning: result.reasoning
      };
    }
  }

  /**
   * Get system prompt based on diagnostic type
   */
  private getSystemPrompt(diagnosticType?: string): string {
    const basePrompt = `You are a highly skilled diagnostic agent specialized in system troubleshooting, error analysis, and automated recovery. Your expertise includes:

CORE CAPABILITIES:
- System health monitoring and diagnostics
- Error pattern recognition and root cause analysis
- Automated recovery action execution
- Service and connectivity troubleshooting
- Configuration validation and correction
- Performance analysis and optimization
- Preventive maintenance recommendations

DIAGNOSTIC TOOLS AVAILABLE:
- diagnostic_system_check: Comprehensive system diagnostics
- analyze_error: Deep error analysis with recovery recommendations
- execute_recovery_action: Automated recovery actions
- System health monitoring tools
- Recovery and remediation tools
- MCP server management tools

APPROACH:
1. Analyze the issue systematically and thoroughly
2. Use diagnostic tools to gather comprehensive information
3. Identify root causes and contributing factors
4. Provide clear, actionable recovery recommendations
5. Execute automated recovery where appropriate
6. Offer preventive measures to avoid recurrence

Always be precise, methodical, and focused on practical solutions. Use tools proactively to gather information and execute recovery actions.`;

    switch (diagnosticType) {
      case 'error_analysis':
        return `${basePrompt}

SPECIALIZED FOCUS: ERROR ANALYSIS
Your primary task is to analyze errors in detail, identify root causes, and provide comprehensive recovery plans. Focus on:
- Error classification and severity assessment
- Root cause identification
- Impact analysis on system components
- Step-by-step recovery procedures
- Prevention strategies`;

      case 'system_check':
        return `${basePrompt}

SPECIALIZED FOCUS: SYSTEM HEALTH CHECK
Your primary task is to perform comprehensive system diagnostics. Focus on:
- System resource monitoring (CPU, memory, disk, network)
- Service availability and health
- Configuration validation
- Dependency verification
- Performance baseline assessment`;

      case 'recovery':
        return `${basePrompt}

SPECIALIZED FOCUS: SYSTEM RECOVERY
Your primary task is to execute recovery actions and restore system functionality. Focus on:
- Immediate stabilization actions
- Service restart and recovery procedures
- Configuration correction
- Connectivity restoration
- Validation of recovery success`;

      case 'full':
      default:
        return `${basePrompt}

FULL DIAGNOSTIC MODE:
Perform comprehensive analysis covering all aspects - error analysis, system health, and recovery actions as needed.`;
    }
  }

  /**
   * Get agent information
   */
  getName(): string {
    return 'Diagnostic Agent';
  }

  getDescription(): string {
    return 'Specialized diagnostic agent for system troubleshooting, error analysis, and automated recovery';
  }

  getCapabilities(): string[] {
    return [
      'System diagnostics and health monitoring',
      'Error analysis and root cause identification',
      'Automated recovery action execution',
      'Service troubleshooting and management',
      'Configuration validation and correction',
      'Performance monitoring and optimization',
      'Preventive maintenance recommendations'
    ];
  }

  /**
   * Switch model for the agent
   */
  setModel(model: string): void {
    this.model = model;
  }
}

// Create and export the diagnostic agent instance
export const diagnosticAgent = new DiagnosticAgent();

export default diagnosticAgent;
