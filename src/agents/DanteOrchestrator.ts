/**
 * DanteOrchestrator - Unified orchestrator using Vercel AI SDK
 * This file now contains the full orchestrator implementation to avoid forking.
 * Use absolute paths when using file tools (e.g., read_file, file_edit).
 */

import { generateText, streamText, stepCountIs, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { anthropic } from '@ai-sdk/anthropic';
import { createOllama } from 'ai-sdk-ollama';
import { z } from 'zod';
import { config } from '../utils/config';
import { memoryManager } from '../memory/MemoryManager';
import { getContextValue, setContextValue } from '../utils/requestContext';
import * as fsSync2 from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { MemoryType, MemoryPriority } from '../memory/types';
import { EventEmitter } from 'events';
import { registerAllAgents } from './BuiltInAgents';
import { executeCodeGeneration, type CodeGenerationOptions } from './vercel/codeGeneration';
import { appendStep, newRunId } from '../utils/stepAggregator';
import { diagnoseFile /*, runBuildCheck */ } from '../utils/codeDiagnostics';
import { analyzeToolError, DEFAULT_RETRY_CONFIG, formatToolErrorForUser } from '../utils/toolErrorHandler';
import { errorRecoveryMiddleware, type RecoveryContext } from '../utils/errorRecoveryMiddleware';
import { consolidateToolResult } from '../utils/contextConsolidator';
import { summarizeAgentResult } from '../utils/summarizeAgentResult';
import {
  rememberTool,
  recallTool,
  forgetTool,
  getMemoryStatsTool,
  contextualMemorySearchTool,
  learnFromErrorTool,
  initializeMemory
} from '../tools/memoryTools';
import { pdfExtractTool } from '../tools/pdfExtract';
import { interruptBus } from '../utils/interruptBus';

import { answer as simpleAnswer } from './SimpleAnswerAgent';
import { createPlan as createPlanningPlan, executePlan as executePlanning, buildAgentInput } from './PlanningAgent';
import { AgentDefinition, AnalysisTurnState, OrchestratorOptions, ToolUsageStatsSnapshot, StreamStepAggregation, MODEL_MAP } from '@/types/orchestrator-types';
import { maybeRememberToolOutcome } from './helpers/memory';
// Provider mapping
const ollama = createOllama({ baseURL: config.ollama.baseURL });
const PROVIDERS = { openai, google, anthropic, ollama } as const;

const MEMORY_TOOL_NAMES = new Set([
  'remember',
  'recall',
  'forget_memories',
  'get_memory_stats',
  'contextual_memory_search',
  'learn_from_error'
]);

const STRUCTURED_ANSWER_FIELD_BY_AGENT: Record<string, string> = {
  ResearchAgent: 'structuredResearch',
  DebugAgent: 'structuredDebug',
  GoogleWorkspaceAgent: 'structuredWorkspace',
  CodeGenerationAgent: 'structuredCodegen',
};

export class DanteAIOrchestrator extends EventEmitter {
  private agents = new Map<string, AgentDefinition>();
  public currentModel: string;
  private globalTools: Record<string, any> = {};
  private recentToolArgs: Array<{ name: string; args: any; t: number }> = [];
  private recentMemoryNotes: Map<string, number> = new Map();
  // Track recent tool result previews per agent to enrich handoff summaries
  private recentToolResultsByAgent: Map<string, Array<{ name: string; preview: string; t: number }>> = new Map();
  // Track recent write/edit outcomes with basic success + diagnostics signal
  private recentWriteStatusByAgent: Map<string, Array<{ name: string; ok: boolean; path?: string; t: number }>> = new Map();
  // Track recent assertion outcomes (e.g., assert_file_contains) to allow verification-based acceptance
  private recentAssertStatusByAgent: Map<string, Array<{ ok: boolean; path?: string; t: number }>> = new Map();
  // Track recent diagnostics outcomes (e.g., diagnose_file_syntax ok flags) per agent
  private recentDiagnosticsOkByAgent: Map<string, Array<{ path?: string; ok: boolean; t: number }>> = new Map();
  // Track last known assistant final text per agent (non-stream runs)
  private lastAgentTextByAgent: Map<string, string> = new Map();
  // Track current projectId (working directory) when set_working_directory is called
  private currentProjectId?: string;

  // Budget repeated auto-repair attempts across nested retries/fallbacks within the same request
  private noWriteBudgetByKey: Map<string, { count: number; firstT: number; lastPath?: string }> = new Map();

  // Permit bounded read-only turns that demonstrably gather new context before enforcing write guards
  private analysisTurnAllowance: Map<string, AnalysisTurnState> = new Map();

  // Lightweight metrics and concurrency controls (from ModelOrchestrator)
  private activeRequests = new Map<string, { startTime: number; model: string; type: string }>();
  private requestQueue: Array<{ id: string; priority: number; processor: () => Promise<any> }> = [];
  private isProcessingQueue = false;
  private maxConcurrentRequests = 5;
  private modelMetrics = new Map<string, {
    requestCount: number;
    successCount: number;
    failureCount: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    lastUsed: Date | null;
    errorRate: number;
  }>();
  private sessionContext: Map<string, { lastPlan?: any; completedSteps?: string[]; lastStrategy?: string; lastUpdated: number }> = new Map();

  constructor(defaultModel: string = 'gpt-5') {
    super();
    // Normalize possible Google-style ids coming from the UI (e.g. 'models/gemini-2.5-pro')
    this.currentModel = DanteAIOrchestrator.normalizeModelId(defaultModel);
  }

  // Accept UI ids like 'models/gemini-2.5-pro' and normalize to our internal map keys
  static normalizeModelId(modelId: string | undefined): string {
    if (!modelId) return 'gpt-5';
    return modelId.startsWith('models/') ? modelId.slice('models/'.length) : modelId;
  }

  registerAgent(agent: AgentDefinition): void {
    this.agents.set(agent.name, agent);
    console.log(`✅ Registered agent: ${agent.name}`);
  }

  registerTools(tools: Record<string, any>): void {
    this.globalTools = { ...this.globalTools, ...tools };
    console.log(`🔧 Registered ${Object.keys(tools).length} global tools`);
  }

  private _structuredFieldForAgent(agentName: string): string | undefined {
    if (!agentName) return undefined;
    if (Object.prototype.hasOwnProperty.call(STRUCTURED_ANSWER_FIELD_BY_AGENT, agentName)) {
      return STRUCTURED_ANSWER_FIELD_BY_AGENT[agentName as keyof typeof STRUCTURED_ANSWER_FIELD_BY_AGENT];
    }
    return undefined;
  }

  private _coerceAnswerPayload(raw: any): { original: any; parsed?: any } {
    if (raw == null) return { original: raw };
    if (typeof raw === 'string') {
      const trimmed = raw.trim();
      if (!trimmed) return { original: raw };
      try {
        const parsed = JSON.parse(trimmed);
        return { original: raw, parsed };
      } catch {
        return { original: raw };
      }
    }
    if (typeof raw === 'object') return { original: raw, parsed: raw };
    return { original: raw };
  }

  private _extractAnswerPayload(res: any): { original: any; parsed?: any; call?: any } | undefined {
    if (!res || typeof res !== 'object') return undefined;

    try {
      const toolCalls = Array.isArray((res as any).toolCalls) ? (res as any).toolCalls : [];
      for (let i = toolCalls.length - 1; i >= 0; i -= 1) {
        const call = toolCalls[i];
        const name = call?.toolName || call?.name;
        if (typeof name === 'string' && name.toLowerCase() === 'answer') {
          const raw = call?.args ?? call?.arguments ?? call?.input;
          const payload = this._coerceAnswerPayload(raw);
          if (payload.parsed && typeof raw === 'string') {
            try { call.args = payload.parsed; } catch {}
          }
          return { ...payload, call };
        }
      }

      const steps = Array.isArray((res as any).steps) ? (res as any).steps : [];
      for (let i = steps.length - 1; i >= 0; i -= 1) {
        const step = steps[i];
        if (!step || typeof step !== 'object') continue;
        const stepName = step?.toolName || step?.name;
        if (typeof stepName !== 'string' || stepName.toLowerCase() !== 'answer') continue;
        const stepType = String(step?.type ?? '').toLowerCase();
        if (stepType && stepType !== 'tool-call' && stepType !== 'tool_call' && stepType !== 'toolcall') continue;
        const raw = step?.args ?? step?.arguments ?? step?.input ?? step?.result ?? step?.output;
        const payload = this._coerceAnswerPayload(raw);
        const argsValue = payload.parsed ?? payload.original;
        if (argsValue === undefined) continue;
        const call: any = {
          toolName: 'answer',
          name: 'answer',
          args: argsValue,
          input: argsValue,
        };
        const callId = step?.toolCallId ?? step?.tool_call_id ?? step?.callId ?? step?.call_id ?? step?.id;
        if (callId != null) {
          const normalizedId = String(callId);
          call.toolCallId = normalizedId;
          call.id = normalizedId;
        }
        return { ...payload, call };
      }
    } catch (err) {
      try { console.warn('Failed to extract answer payload from result:', err); } catch {}
    }

    return undefined;
  }

  private _ensureAnswerResult(res: any, agentName: string): void {
    if (!res || typeof res !== 'object') return;

    const payload = this._extractAnswerPayload(res);
    if (!payload) return;

    const { call, parsed, original } = payload;
    if (call) {
      const existing = Array.isArray((res as any).toolCalls) ? (res as any).toolCalls : [];
      const hasAnswer = existing.some((c: any) => {
        const name = c?.toolName || c?.name;
        return typeof name === 'string' && name.toLowerCase() === 'answer';
      });
      if (!hasAnswer) {
        try { (res as any).toolCalls = [...existing, call]; } catch {}
      } else if (!Array.isArray((res as any).toolCalls)) {
        try { (res as any).toolCalls = existing; } catch {}
      }
    }

    const structured = parsed && typeof parsed === 'object' ? parsed : undefined;
    const field = this._structuredFieldForAgent(agentName);
    if (structured) {
      if (field) {
        if (!(res as any)[field]) {
          try { (res as any)[field] = structured; } catch {}
        }
      } else if (!(res as any).structuredAnswer) {
        try { (res as any).structuredAnswer = structured; } catch {}
      }
    }

    const currentText = typeof (res as any).text === 'string' ? (res as any).text.trim() : '';
    if (!currentText) {
      if (structured && typeof structured.summary === 'string' && structured.summary.trim().length > 0) {
        try { (res as any).text = structured.summary.trim(); } catch {}
      } else if (typeof original === 'string' && original.trim().length > 0) {
        try { (res as any).text = original.trim(); } catch {}
      }
    }
  }

  private _normalizeCodegenPayload(raw: any): any {
    if (raw == null) return undefined;
    if (typeof raw === 'string') {
      const trimmed = raw.trim();
      if (!trimmed) return undefined;
      try {
        const parsed = JSON.parse(trimmed);
        return this._normalizeCodegenPayload(parsed);
      } catch {
        return { summary: trimmed };
      }
    }
    if (typeof raw === 'object') return raw;
    return { summary: String(raw) };
  }

  private _renderCodegenSummary(structured: any): string {
    if (!structured) return '';
    if (typeof structured === 'string') return structured;
    if (typeof structured !== 'object') return String(structured ?? '');
    const out: string[] = [];
    if (structured.summary) out.push(String(structured.summary));
    if (Array.isArray(structured.filesModified) && structured.filesModified.length) {
      out.push('Files Modified:');
      for (const f of structured.filesModified) {
        if (!f) continue;
        const path = typeof f.path === 'string' ? f.path : '[unknown path]';
        const changeType = typeof f.changeType === 'string' ? f.changeType : 'modify';
        out.push(`- ${path} [${changeType}]`);
      }
    }
    if (Array.isArray(structured.notes) && structured.notes.length) {
      out.push('Notes:');
      for (const note of structured.notes) out.push(`- ${note}`);
    }
    return out.join('\n');
  }

  private _extractCodegenAnswer(res: any): { structured?: any; rendered?: string } {
    try {
      const calls = Array.isArray(res?.toolCalls) ? res.toolCalls : [];
      const steps = Array.isArray(res?.steps) ? res.steps : [];
      const answerCall = calls.find((c: any) => (c?.toolName || c?.name) === 'answer');
      const raw = answerCall?.args ?? answerCall?.arguments ?? answerCall?.input;
      let structured = this._normalizeCodegenPayload(raw);
      if (!structured) {
        for (let i = steps.length - 1; i >= 0; i -= 1) {
          const step = steps[i];
          const stepName = step?.toolName || step?.name;
          const stepType = step?.type;
          if (stepName === 'answer' && (stepType === 'tool-call' || stepType == null)) {
            const candidate = step?.args ?? step?.arguments ?? step?.input;
            structured = this._normalizeCodegenPayload(candidate);
            if (structured) break;
          }
        }
      }
      const rendered = this._renderCodegenSummary(structured);
      return {
        structured,
        rendered: rendered && rendered.trim().length > 0 ? rendered : undefined,
      };
    } catch {
      return { structured: undefined, rendered: undefined };
    }
  }

  private getModelProvider(modelName?: string) {
    const modelInput = DanteAIOrchestrator.normalizeModelId(modelName || this.currentModel);
    // Support inline provider prefix for offline usage, e.g. 'ollama:gemma3:12b'
    if (modelInput.startsWith('ollama:')) {
      const m = modelInput.slice('ollama:'.length);
      const prov = (PROVIDERS as any)['ollama'];
      if (!prov) throw new Error(`Provider ollama not found`);
      return prov(m);
    }
    const mapping = (MODEL_MAP as any)[modelInput] || (MODEL_MAP as any)['gpt-5'];
    const provider = (PROVIDERS as any)[mapping.provider];
    if (!provider) throw new Error(`Provider ${mapping.provider} not found`);
    return provider(mapping.model);
  }
  // Provider/model availability gate. Skips models whose provider is not configured.
  private isModelAvailable(modelKey: string): boolean {
    try {
      const normalized = DanteAIOrchestrator.normalizeModelId(modelKey);
      // Inline Ollama prefix support
      if (normalized.startsWith('ollama:')) {
        return (String(config.orchestrator?.offlineProvider || '').toLowerCase() === 'ollama') && !!config.ollama?.baseURL;
      }
      const mapping = (MODEL_MAP as any)[normalized];
      if (!mapping) return false;
      const provider = mapping.provider as keyof typeof PROVIDERS;
      if (provider === 'openai') {
        return !!(config.openai?.apiKey || process.env.OPENAI_API_KEY);
      }
      if (provider === 'google') {
        // Support both env/config variants
        return !!(config.gemini?.apiKey || process.env.GEMINI_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY);
      }
      if (provider === 'anthropic') {
        return !!(process.env.ANTHROPIC_API_KEY || (config as any)?.anthropic?.apiKey);
      }
      if (provider === 'ollama') {
        return !!config.ollama?.baseURL;
      }
      return false;
    } catch {
      return false;
    }
  }

  // Internal generate wrapper so tests can stub and record attempt order without hitting providers.
  private async doGenerate(modelKey: string, cfg: any, stream: boolean): Promise<any> {
    if (stream) {
      return await streamText(cfg);
    }
    return await generateText(cfg);
  }

  // Attempt auto-retry for file_edit with unified_diff by escalating mergeStrategy.
  // Extracted from onStepFinish to improve readability and testability.
  private async _attemptUnifiedDiffAutoRetry(
    finalTools: Record<string, any>,
    agentName: string,
    argsObj: any,
    toolResult: any,
    summarize: (val: any, depth?: number) => any,
  ): Promise<void> {
    const baseArgs = { ...argsObj };
    const current = (baseArgs.mergeStrategy ? String(baseArgs.mergeStrategy) : (baseArgs.fuzzy ? 'fuzzy' : 'strict')) as 'strict'|'fuzzy'|'git_3way'|'conflict_markers';
    const order: Array<'strict'|'fuzzy'|'git_3way'|'conflict_markers'> = ['strict','fuzzy','git_3way','conflict_markers'];
    const startIdx = Math.min(order.indexOf(current) + 1, order.length - 1);
    for (let idx = startIdx; idx < order.length; idx++) {
      const strategy = order[idx];
      const retryArgs = { ...baseArgs, mergeStrategy: strategy } as any;
      if (strategy === 'fuzzy') retryArgs.fuzzy = true;
      if (strategy === 'strict') retryArgs.fuzzy = false;
      console.warn(`🔁 Auto-retrying file_edit unified_diff with mergeStrategy='${strategy}'`);
      // Find the tool implementation (validated tool has invoke; raw tool has execute)
      const t = (finalTools as any)['file_edit'];
      let retryRes: any;
      try {
        if (t?.invoke) {
          retryRes = await t.invoke({}, JSON.stringify(retryArgs));
        } else if (t?.execute) {
          retryRes = await t.execute(retryArgs);
        }
      } catch {
        retryRes = undefined;
      }
      if (retryRes && retryRes.success === true) {
        console.log(`✅ Unified diff auto-retry succeeded with mergeStrategy='${strategy}'`);
        // Record as a successful tool result in buffers
        try {
          const preview = JSON.stringify(summarize(retryRes));
          const buf2 = this.recentToolResultsByAgent.get(agentName) || [];
          buf2.push({ name: 'file_edit', preview: preview.length > 160 ? preview.slice(0,160)+'…' : preview, t: Date.now() });
          while (buf2.length > 10) buf2.shift();
          this.recentToolResultsByAgent.set(agentName, buf2);
        } catch {}
        // Write status bookkeeping
        try {
          const success = true;
          const diagOk = typeof retryRes?.diagnostics?.ok === 'boolean' ? !!retryRes.diagnostics.ok : true;
          const modifiedFlag = (typeof retryRes?.modified === 'boolean') ? !!retryRes.modified : true;
          const replacements = typeof retryRes?.replacements === 'number' ? retryRes.replacements : undefined;
          const notNoOp = modifiedFlag && (replacements == null || replacements > 0);
          let persistedOk = true;
          try {
            const p = typeof (retryRes?.path) === 'string' ? retryRes.path : undefined;
            const afterHash: string | undefined = retryRes?.hashes?.after;
            if (p && afterHash) {
              const data = fsSync2.readFileSync(p, 'utf-8');
              const h = crypto.createHash('sha256').update(data).digest('hex');
              if (h !== afterHash) persistedOk = false;
            }
          } catch { persistedOk = false; }
          const ok = success && diagOk && notNoOp && persistedOk;
          const path2 = typeof retryRes?.path === 'string' ? retryRes.path : undefined;
          const wb2 = this.recentWriteStatusByAgent.get(agentName) || [];
          wb2.push({ name: 'file_edit', ok, path: path2, t: Date.now() });
          while (wb2.length > 20) wb2.shift();
          this.recentWriteStatusByAgent.set(agentName, wb2);
        } catch {}
        // Replace the failed display value with the successful retry for downstream guards
        (toolResult as any).result = retryRes;
        break;
      }
    }
  }

  // --- Concurrency queue ---
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue) return;
    this.isProcessingQueue = true;
    try {
      // Sort high priority first
      this.requestQueue.sort((a, b) => (b.priority || 1) - (a.priority || 1));
      const availableSlots = Math.max(0, this.maxConcurrentRequests - this.activeRequests.size);
      if (availableSlots > 0 && this.requestQueue.length > 0) {
        const toProcess = this.requestQueue.splice(0, availableSlots);
        for (const next of toProcess) {
          // Start processor without awaiting; it will update activeRequests internally
          Promise.resolve(next.processor())
            .catch((err) => console.error('Error in queued processor:', err))
            .finally(() => {
              // When a queued task completes, try to drain the queue
              this.processQueue().catch((err) => console.error('Error processing queue in finally block:', err));
            });
        }
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private enqueue<T>(priority: number, processor: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const id = this.generateRequestId();
      this.requestQueue.push({ id, priority: priority || 1, processor: async () => {
        try { const v = await processor(); resolve(v); } catch (e) { reject(e as any); }
      }});
      this.processQueue().catch(() => {});
    });
  }

  // --- Metrics and status ---
  getStatus(): {
    modelMetrics: Map<string, any>;
    activeRequests: number;
    queueLength: number;
    totalRequestsProcessed: number;
    averageResponseTime: number;
    systemHealth: 'healthy' | 'degraded' | 'critical';
  } {
    const totalRequests = Array.from(this.modelMetrics.values()).reduce((s, m) => s + m.requestCount, 0);
    const totalResp = Array.from(this.modelMetrics.values()).reduce((s, m) => s + m.averageResponseTime * m.requestCount, 0);
    const avg = totalRequests > 0 ? totalResp / totalRequests : 0;
    const maxErr = Math.max(0, ...Array.from(this.modelMetrics.values()).map(m => m.errorRate || 0));
    const systemHealth: 'healthy' | 'degraded' | 'critical' = maxErr > 0.6 ? 'critical' : maxErr > 0.25 ? 'degraded' : 'healthy';
    return {
      modelMetrics: new Map(this.modelMetrics),
      activeRequests: this.activeRequests.size,
      queueLength: this.requestQueue.length,
      totalRequestsProcessed: totalRequests,
      averageResponseTime: avg,
      systemHealth,
    };
  }

  // Centralized initializer for model metrics to avoid duplication
  private _getOrCreateModelMetrics(model: string): {
    requestCount: number;
    successCount: number;
    failureCount: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    lastUsed: Date | null;
    errorRate: number;
  } {
    const existing = this.modelMetrics.get(model);
    if (existing) return existing;
    const m = {
      requestCount: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      lastUsed: null as Date | null,
      errorRate: 0,
    };
    this.modelMetrics.set(model, m);
    return m;
  }

  private recordRequestSuccess(model: string, processingTime: number, tokens: number = 0): void {
    const m = this._getOrCreateModelMetrics(model);
    m.requestCount++;
    m.successCount++;
    m.totalTokensUsed += Math.max(0, tokens || 0);
    m.averageResponseTime = m.averageResponseTime + (processingTime - m.averageResponseTime) / m.requestCount;
    m.lastUsed = new Date();
    m.errorRate = m.requestCount > 0 ? m.failureCount / m.requestCount : 0;
    this.modelMetrics.set(model, m);
  }

  private recordRequestFailure(model: string, _error: Error, processingTime: number): void {
    const m = this._getOrCreateModelMetrics(model);
    m.requestCount++;
    m.failureCount++;
    m.averageResponseTime = m.averageResponseTime + (processingTime - m.averageResponseTime) / m.requestCount;
    m.lastUsed = new Date();
    m.errorRate = m.requestCount > 0 ? m.failureCount / m.requestCount : 0;
    this.modelMetrics.set(model, m);
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;
  }

  async routeToAgent(
    input: string,
  ): Promise<{ agent: string; confidence: number }> {
    // Prefer configured offline model for routing; fall back to cost‑effective online model
    const offlineProv = String(config.orchestrator?.offlineProvider || '').toLowerCase();
    const offlineModel = String(config.orchestrator?.offlineModel || '');

    const systemPrompt = `You are an agent router. Analyze the user's request and choose the best specialized agent.

Routing rules:
- Prefer specialized agents; do not pick generic coordinator agents.
- Choose ResearchAgent for web/code research and information gathering.
- Choose CodeGenerationAgent for code changes, refactors, or adding features.
- Choose DebugAgent for bug reproduction and fixes.
- Choose SecurityAnalysisAgent for security reviews.
- Choose PlanningAgent to produce plans only.
- Choose TaskOrchestrator for large, multi-step tasks, the task is unclear or when the user asks to install/setup/integrate/configure/deploy new tech.
- Choose ComputerUseAgent for visible, user-facing browser/computer automation.
- Choose GoogleWorkspaceAgent for Gmail/Calendar/Drive tasks.
- Do not invent names. If uncertain, output TaskOrchestrator.

Available agents:
${Array.from(this.agents.entries()).map(([name, agent]) => `- ${name}: ${agent.description}`).join('\n')}
`;

    const runRouter = async (modelKey: string) => {
      const { text } = await generateText({
        model: this.getModelProvider(modelKey),
        system: systemPrompt,
        prompt: input,
      });
      return text;
    };

    let text: string | undefined;

    // 1) Try offline model when configured (e.g., Ollama)
    if (offlineProv === 'ollama' && offlineModel && config.ollama?.baseURL) {
      const offlineKey = `ollama:${offlineModel}`;
      try {
        text = await runRouter(offlineKey);
      } catch (e: any) {
        console.warn(`Offline routing model failed (${offlineKey}); falling back to online: ${e?.message || String(e)}`);
      }
    }

    // 2) Fallback to a low‑cost online model (prefer gpt-5-nano; check availability)
    if (!text) {
      const candidates = ['gpt-5-nano','gpt-5-mini', 'gemini-2.5-flash', 'gpt-5'];
      const chosen = candidates.find((m) => this.isModelAvailable(m)) || 'gpt-5-mini';
      text = await runRouter(chosen);
    }

    const selectedAgent = text.trim();
    if (this.agents.has(selectedAgent)) {
      return { agent: selectedAgent, confidence: 0.9 };
    }

    // Fallback: prefer TaskOrchestrator for unknown outputs, otherwise first registered agent
    const fallbackAgent = this.agents.has('TaskOrchestrator')
      ? 'TaskOrchestrator'
      : (this.agents.has('ResearchAgent') ? 'ResearchAgent' : Array.from(this.agents.keys())[0]);
    if (!fallbackAgent) {
      throw new Error('No agents registered with orchestrator');
    }
    return {
      agent: fallbackAgent,
      confidence: 0.3,
    };
  }

  async executeWithAgent(
    agentName: string,
    input: string,
    options: OrchestratorOptions = {}
  ): Promise<any> {
    const agent = this.agents.get(agentName);
    if (!agent) throw new Error(`Agent ${agentName} not found`);

    const callStartTime = Date.now();
    console.log(`🤖 Executing with agent: ${agentName}`);

    // Bind request-scoped working directory to orchestrator projectId for proper snapshot tagging
    let contextCwd: string | undefined;
    try {
      contextCwd = getContextValue<string>('cwd');
    } catch (e) {
      console.warn('Failed to read current request cwd from context:', e);
    }
    if (options.initialWorkingDirectory) {
      try {
        const absInitial = path.resolve(options.initialWorkingDirectory);
        if (contextCwd !== absInitial) {
          try { setContextValue('cwd', absInitial); contextCwd = absInitial; } catch (setErr) { console.warn('Failed to update context cwd:', setErr); }
        }
        if (process.cwd() !== absInitial) {
          try { process.chdir(absInitial); } catch (chdirErr) { console.warn('Failed to set process cwd:', chdirErr); }
        }
        this.currentProjectId = absInitial;
      } catch (e) {
        console.warn('Failed to apply initial working directory:', e);
      }
    }
    if (contextCwd && typeof contextCwd === 'string' && contextCwd.length > 0) {
      this.currentProjectId = contextCwd;
    }
    // Auto-initialize working directory for agents that need file operations
    const needsFileTools = ['CodeGenerationAgent', 'DebugAgent', 'SecurityAnalysisAgent', 'ComputerUseAgent', 'ResearchAgent'].includes(agentName);
    if (needsFileTools) {
      try {
        const projectRoot = this.currentProjectId || process.cwd();
        const currentDir = getContextValue<string>('cwd') || process.cwd();

        // Only set working directory if it's different from the project root
        if (currentDir !== projectRoot) {
          console.log(`🔧 Auto-initializing working directory for ${agentName}: ${projectRoot}`);
          try { setContextValue('cwd', projectRoot); } catch (e) { console.warn('Failed to set context cwd:', e); }
          try { process.chdir(projectRoot); } catch (e) { console.warn('Failed to set process cwd:', e); }
        }
      } catch (e) {
        console.warn(`Failed to auto-initialize working directory for ${agentName}:`, e);
      }
    }

    const runToolStats: ToolUsageStatsSnapshot = {
      totalCalls: 0,
      readFile: {
        total: 0,
        paths: new Map<string, number>(),
        maxRepeat: 0,
      },
      listDirectory: {
        total: 0,
        paths: new Map<string, number>(),
        maxRepeat: 0,
      },
      fileEdit: 0,
    };

    const safeParseArgs = (raw: any): any => {
      if (!raw) return undefined;
      if (typeof raw === 'string') {
        try { return JSON.parse(raw); } catch { return undefined; }
      }
      return raw;
    };

    const recordPathHit = (map: Map<string, number>, value: unknown): number | undefined => {
      if (typeof value !== 'string' || value.length === 0) return undefined;
      const prev = map.get(value) ?? 0;
      const next = prev + 1;
      map.set(value, next);
      return next;
    };
    // Orchestrator-driven model selection:
    // Centralized for code generation. Explicit fallback order is:
    // gpt-5 → gemini-2.5-flash → gemini-2.5-pro → gpt-5-mini.
    // Gemini gating is based solely on provider availability (API key/config), not an env flag.
    const agentModelKey = DanteAIOrchestrator.normalizeModelId(agent.model || this.currentModel);
    const codegenChain = ['gpt-5', 'gemini-2.5-flash', 'gemini-2.5-pro', 'gpt-5-mini'];
    const availableCodegenChain = codegenChain.filter((m) => this.isModelAvailable(m));
    const selectedModelKey = agentName === 'CodeGenerationAgent'
      ? (availableCodegenChain[0] || codegenChain[0])
      : agentModelKey;
    const model = this.getModelProvider(selectedModelKey);

    // Special handling for agents that need file operation tools
    let agentTools = agent.tools || {};

    if (needsFileTools) {
      // Dynamically import file, diagnostics, and task tools to avoid circular dependencies
      try {
        const { fileOperationTools } = await import('../tools/fileOperations');
        const { codeDiagnosticsTools } = await import('../tools/codeDiagnostics');
        const { taskManagementTools } = await import('../tools/taskManagement');

        // Convert array tools to object format
        const fileTools = fileOperationTools.reduce((acc: any, tool: any) => {
          acc[tool.name] = tool;
          return acc;
        }, {});

        const diagTools = codeDiagnosticsTools.reduce((acc: any, tool: any) => {
          acc[tool.name] = tool;
          return acc;
        }, {});

        const taskTools = taskManagementTools.reduce((acc: any, tool: any) => {
          acc[tool.name] = tool;
          return acc;
        }, {});

        agentTools = { ...agentTools, ...fileTools, ...diagTools, ...taskTools };
        console.log(`📁 Added ${Object.keys(fileTools).length} file tools, ${Object.keys(diagTools).length} diagnostics tools and ${Object.keys(taskTools).length} task tools to ${agentName}`);
      } catch (e) {
        console.warn(`Failed to load tools for ${agentName}:`, e);
      }
    }

    const mergedTools = { ...this.globalTools, ...agentTools };
    const tools = Object.fromEntries(
      Object.entries(mergedTools).filter(([name]) => !MEMORY_TOOL_NAMES.has(name))
    );

    // Inject finalization tool for CodeGenerationAgent to guarantee a final result
    let finalTools = tools;
    let forceToolChoice: any = undefined;
    if (agentName === 'CodeGenerationAgent') {
      const answerTool = tool({
        description: 'Provide the final code generation result in a structured format.',
        inputSchema: z.object({
          summary: z.string().describe('Concise summary of changes'),
          filesModified: z.array(
            z.object({
              path: z.string(),
              changeType: z.enum(['create', 'modify', 'delete']).optional(),
              rationale: z.string().optional(),
              diffHint: z.string().optional().describe('Short, human-readable summary of changes with line refs if known'),
              patchPreview: z.string().optional().describe('Unified diff preview (---/+++ headers) with essential hunks only'),
            })
          ).optional(),
          instructions: z.array(z.string()).optional().describe('Instructions to apply or verify changes'),
          commandsToRun: z.array(z.string()).optional().describe('Shell commands to validate/build/test'),
          notes: z.array(z.string()).optional(),
        }) as any,
      }) as any;
      finalTools = { ...tools, answer: answerTool };
      forceToolChoice = 'required' as any;
    }

    // Guard tuning flags (env-configurable)
    const writeGuardEnabled = String(process.env.DISABLE_NO_WRITE_GUARD || '').toLowerCase() !== 'true';
    // Prevent repeated auto-repair attempts across retries/fallbacks within the same executeWithAgent call
    let autoRepairTriggered = false;

    const summarize = (val: any, depth = 0): any => {
      try {
        if (val == null) return val;
        if (typeof val === 'string') return val.length > 200 ? val.slice(0, 200) + '…' : val;
        if (typeof val === 'number' || typeof val === 'boolean') return val;
        if (Array.isArray(val)) return { length: val.length, sample: val.slice(0, 3).map(v => summarize(v, depth + 1)) };
        if (typeof val === 'object') {
          const out: Record<string, any> = {};
          for (const [k, v] of Object.entries(val).slice(0, 8)) out[k] = summarize(v, depth + 1);
          return out;
        }
        return String(val);
      } catch { return '[unserializable]'; }
    };

    // Compose any injected steering context from interruptBus for this session
    let __steeringPreamble = '';
    try {
      if (options.sessionId) {
        const updates = interruptBus.consumeInjectedContext(options.sessionId) || [];
        const repl = interruptBus.consumeReplaceNext(options.sessionId);
        const lines: string[] = [];
        if (updates.length > 0) {
          lines.push('[User Steering Update]');
          for (const u of updates) {
            const parts: string[] = [];
            if (u.priority) parts.push(`priority: ${u.priority}`);
            if (u.message) parts.push(`message: ${u.message}`);
            if (u.constraints && Object.keys(u.constraints).length > 0) {
              try { parts.push(`constraints: ${JSON.stringify(u.constraints)}`); } catch (e) { console.warn('Failed to stringify steering constraints', e); }
            }
            if (parts.length > 0) lines.push(`- ${parts.join(' | ')}`);
          }
          lines.push('Apply these updates before continuing.');
        }
        if (repl) {
          lines.push('[Override Next Step]');
          const parts: string[] = [];
          if (repl.priority) parts.push(`priority: ${repl.priority}`);
          if (repl.message) parts.push(`next: ${repl.message}`);
          if (repl.constraints && Object.keys(repl.constraints).length > 0) {
            try { parts.push(`constraints: ${JSON.stringify(repl.constraints)}`); } catch (e) { console.warn('Failed to stringify replacement constraints', e); }
          }
          if (parts.length > 0) lines.push(`- ${parts.join(' | ')}`);
          lines.push('Discard any previously planned immediate next action and perform the override instruction instead.');
        }
        if (lines.length > 0) {
          __steeringPreamble = `\n\n${lines.join('\n')}`;
          try { this.emit('steeringApplied', { sessionId: options.sessionId, override: !!repl, updates: updates.length }); } catch (e) { console.warn('Failed to emit steeringApplied event', e); }
        }
      }
    } catch (e) {
      console.warn('Failed to process steering context from interruptBus:', e);
    }


    const commonConfig = {
      model,
      system: agent.instructions,
      prompt: `${input}${__steeringPreamble}`,
      abortSignal: options.sessionId ? interruptBus.ensureController(options.sessionId).signal : undefined,
      tools: Object.keys(finalTools).length > 0 ? finalTools : undefined,
      toolChoice: forceToolChoice,
      providerOptions: {
        openai: {
          reasoningEffort: 'medium',
          reasoningSummary: 'auto'
        }
      },
      stopWhen: stepCountIs(options.maxSteps ?? agent.maxSteps ?? 10),
      onStepFinish: (step: any) => {
        if (options.onStepFinish) options.onStepFinish(step);

      if (step.toolCalls?.length > 0) {
          for (const toolCall of step.toolCalls) {
            const tName = (toolCall.name || toolCall.toolName || toolCall.tool_name || toolCall.function?.name || 'unknown_tool');
            const tArgs = (toolCall.input || toolCall.args || toolCall.function?.arguments || toolCall.parameters);
            runToolStats.totalCalls += 1;
            const parsedArgs = safeParseArgs(tArgs);
            const updateMaxRepeat = (category: 'readFile' | 'listDirectory', pathCandidate: unknown) => {
              const hit = recordPathHit(runToolStats[category].paths, pathCandidate);
              if (typeof hit === 'number') {
                runToolStats[category].maxRepeat = Math.max(runToolStats[category].maxRepeat, hit);
              }
            };
            if (tName === 'read_file') {
              runToolStats.readFile.total += 1;
              updateMaxRepeat('readFile', parsedArgs?.filePath ?? parsedArgs?.path);
            } else if (tName === 'read_files') {
              const paths = Array.isArray(parsedArgs?.filePaths) ? parsedArgs.filePaths : [];
              if (paths.length === 0) {
                runToolStats.readFile.total += 1;
                updateMaxRepeat('readFile', parsedArgs?.filePath ?? parsedArgs?.path);
              } else {
                runToolStats.readFile.total += paths.length;
                for (const p of paths) updateMaxRepeat('readFile', p);
              }
            } else if (tName === 'list_directory') {
              runToolStats.listDirectory.total += 1;
              updateMaxRepeat('listDirectory', parsedArgs?.dirPath ?? parsedArgs?.path ?? parsedArgs?.directory);
            } else if (tName === 'file_edit') {
              runToolStats.fileEdit += 1;
            }
            this.emit('toolCall', { agent: agentName, tool: tName, args: tArgs });
            try { console.log(`🛠️ Tool call | Agent: ${agentName} | Tool: ${tName} | Args: ${JSON.stringify(summarize(tArgs))}`); } catch {}
            try {
              this.recentToolArgs.push({ name: tName, args: tArgs, t: Date.now() });
              if (this.recentToolArgs.length > 20) this.recentToolArgs.shift();
              // Capture projectId on set_working_directory for durable task snapshots
              if (tName === 'set_working_directory') {
                try {
                  const argObj = typeof tArgs === 'string' ? JSON.parse(tArgs) : (tArgs || {});
                  const dirPath = argObj?.dirPath || argObj?.path || argObj?.cwd;
                  if (typeof dirPath === 'string' && dirPath.length > 0) {
                    this.currentProjectId = dirPath;
                  }
                } catch (e) {
                  console.warn('Failed to extract dirPath from set_working_directory args:', e);
                }
              }
            } catch (e) {
              console.warn('Failed to record recent tool call:', e);
            }
            if (options.onToolCall) options.onToolCall(tName, tArgs);
          }
        }

        if (step.toolResults?.length > 0) {
          for (const toolResult of step.toolResults) {
            try {
              const rName = (toolResult.name || toolResult.toolName || toolResult.tool_name || 'tool');
              console.log(`🧩 Tool result | Agent: ${agentName} | Tool: ${rName} | Result: ${JSON.stringify(summarize(toolResult.result ?? toolResult))}`);
              let pairedArgs: any = undefined;
              const now = Date.now();
              for (let i = this.recentToolArgs.length - 1; i >= 0; i--) {
                const entry = this.recentToolArgs[i];
                if (entry.name === rName && now - entry.t < 5000) { pairedArgs = entry.args; break; }
              }
              // Auto-retry guard for unified_diff with mergeStrategy escalation
              try {
                const raw = pairedArgs;
                const argsObj = typeof raw === 'string' ? (JSON.parse(raw) as any) : (raw || {});
                const val0: any = (toolResult.result ?? toolResult);
                const isFileEdit = rName === 'file_edit';
                const failed = !!(val0 && val0.success === false);
                const isUnifiedDiff = argsObj && argsObj.operation === 'patch' && ((argsObj.patchFormat === 'unified_diff') || typeof argsObj.diff === 'string');
                if (isFileEdit && failed && isUnifiedDiff) {
                  void this._attemptUnifiedDiffAutoRetry(finalTools, agentName, argsObj, toolResult, summarize)
                    .catch(err => console.warn('Unified diff auto-retry async error:', err));
                }
              } catch (autoRetryErr) {
                console.warn('Unified diff auto-retry guard failed:', autoRetryErr);
              }
              void maybeRememberToolOutcome(rName, pairedArgs, toolResult.result ?? toolResult, agentName, this.recentMemoryNotes);
              // Record a compact preview for handoff summaries
              const preview = (() => {
                try {
                  const val = toolResult.result ?? toolResult;
                  const s = typeof val === 'string' ? val : JSON.stringify(val);
                  return s.length > 160 ? s.slice(0, 160) + '…' : s;
                } catch { return '[unserializable]'; }
              })();
              const buf = this.recentToolResultsByAgent.get(agentName) || [];
              buf.push({ name: rName, preview, t: Date.now() });
              // Keep last 10
              while (buf.length > 10) buf.shift();
              this.recentToolResultsByAgent.set(agentName, buf);

              // Record file_edit success status for guards
              try {
                if (rName === 'file_edit') {
                  const val = (toolResult.result ?? toolResult);
                  const success = !!(val && (val.success === true));
                  const diagOk = typeof val?.diagnostics?.ok === 'boolean' ? !!val.diagnostics.ok : true;

                  // Enhanced write verification with better success detection
                  const modifiedFlag = (typeof val?.modified === 'boolean') ? !!val.modified : true;
                  const replacements = typeof val?.replacements === 'number' ? val.replacements : undefined;
                  const notNoOp = modifiedFlag && (replacements == null || replacements > 0);

                  // Improved hash verification with fallback strategies
                  let persistedOk = true;
                  let hashVerified = false;
                  try {
                    const p = typeof (val?.path) === 'string' ? val.path : undefined;
                    const afterHash: string | undefined = val?.hashes?.after;
                    if (p && afterHash) {
                      try {
                        const data = fsSync2.readFileSync(p, 'utf-8');
                        const h = crypto.createHash('sha256').update(data).digest('hex');
                        if (h !== afterHash) {
                          persistedOk = false;
                          console.warn(`Hash verification failed for ${p}: expected ${afterHash}, got ${h}`);
                        }
                        hashVerified = true;
                      } catch (readErr) {
                        console.warn(`Failed to read file for hash verification ${p}:`, readErr);
                        persistedOk = false;
                      }
                    }
                  } catch (hashErr) {
                    console.warn('Hash verification error:', hashErr);
                    persistedOk = false;
                  }

                  // If hash verification wasn't available, use alternative success indicators
                  if (!hashVerified) {
                    // Consider it successful if we have success=true and either diagnostics are OK or we have modification indicators
                    if (success && (diagOk || notNoOp)) {
                      persistedOk = true;
                      console.log(`No hash available for ${val?.path || 'unknown file'}, using alternative verification`);
                    }
                  }

                  const ok = success && diagOk && notNoOp && persistedOk;
                  const path = typeof val?.path === 'string' ? val.path : undefined;

                  // Enhanced logging for better debugging
                  if (ok) {
                    console.log(`✅ File edit verified successfully: ${path || 'unknown'} (success=${success}, diag=${diagOk}, modified=${modifiedFlag}, persisted=${persistedOk})`);
                  } else {
                    console.warn(`❌ File edit verification failed: ${path || 'unknown'} (success=${success}, diag=${diagOk}, modified=${modifiedFlag}, persisted=${persistedOk})`);
                  }

                  const wb = this.recentWriteStatusByAgent.get(agentName) || [];
                  wb.push({ name: rName, ok, path, t: Date.now() });
                  while (wb.length > 20) wb.shift();
                  this.recentWriteStatusByAgent.set(agentName, wb);
                }
                // Track diagnostics outcomes for stronger no-op detection
                if (rName === 'diagnose_file_syntax') {
                  try {
                    const valRaw: any = (toolResult.result ?? toolResult);
                    let ok: boolean | undefined;
                    let diagPath: string | undefined;
                    if (typeof valRaw === 'string') {
                      try {
                        const parsed = JSON.parse(valRaw);
                        ok = !!(parsed?.diagnostics?.ok ?? parsed?.ok ?? parsed?.success);
                        diagPath = parsed?.diagnostics?.file || parsed?.file || parsed?.path;
                      } catch (e) {
                        console.warn('Failed to parse diagnose_file_syntax result as JSON:', e);
                      }
                    } else if (typeof valRaw === 'object' && valRaw) {
                      ok = !!(valRaw?.diagnostics?.ok ?? valRaw?.ok ?? valRaw?.success);
                      diagPath = valRaw?.diagnostics?.file || valRaw?.file || valRaw?.path;
                    }
                    const dbuf = this.recentDiagnosticsOkByAgent.get(agentName) || [];
                    dbuf.push({ path: typeof diagPath === 'string' ? diagPath : undefined, ok: !!ok, t: Date.now() });
                    while (dbuf.length > 20) dbuf.shift();
                    this.recentDiagnosticsOkByAgent.set(agentName, dbuf);
                  } catch (e) {
                    console.warn('diagnose_file_syntax result processing failed', { agent: agentName, error: e });
                  }
                }
                // Track assertion outcomes to allow verification-based acceptance
                if (rName === 'assert_file_contains') {
                  const val: any = (toolResult.result ?? toolResult);
                  const ok = !!(val && (val.success === true));
                  const path = typeof val?.path === 'string' ? val.path : undefined;
                  const abuf = this.recentAssertStatusByAgent.get(agentName) || [];
                  abuf.push({ ok, path, t: Date.now() });
                  while (abuf.length > 20) abuf.shift();
                  this.recentAssertStatusByAgent.set(agentName, abuf);
                }
              } catch (e) {
                console.warn('Error during verification-based success check (assert/diagnose loop):', e);
              }
            } catch (e) {
              console.warn('Error during verification-based success check (assert/diagnose loop):', e);
            }
          }
        }
      }
    } as any;

    // Helper: build a short handoff preamble describing recent tool activity
    const buildHandoffPreamble = () => {
      console.log(`[LOG] Building handoff preamble for agent ${agentName}, recentToolArgs length: ${this.recentToolArgs.length}, recentToolResults length: ${(this.recentToolResultsByAgent.get(agentName) || []).length}`);
      try {
        const now = Date.now();
        const recent = this.recentToolArgs
          .filter((e) => now - e.t < 60_000)
          .slice(-10);
        if (recent.length === 0) {
          console.log(`[LOG] No recent tools for preamble, returning empty`);
          return '';
        }
        const summary = Array.from(new Set(recent.map((e) => e.name))).join(', ');
        // Recent tool result previews (last 3)
        const resultsBuf = (this.recentToolResultsByAgent.get(agentName) || []).slice(-3);
        const resultsLines = resultsBuf.map((r) => `- ${r.name}: ${r.preview}`).join('\n');
        // Last assistant text, if available
        const lastText = this.lastAgentTextByAgent.get(agentName);
        const lastTextLine = lastText ? `\nLast assistant note: ${lastText.slice(0, 220)}${lastText.length > 220 ? '…' : ''}` : '';
        // Objective (trim original input to a single‑line compact form)
        const objective = (() => {
          const oneLine = String(input).replace(/\s+/g, ' ').trim();
          return oneLine.length > 220 ? oneLine.slice(0, 220) + '…' : oneLine;
        })();

        const nextHint = (() => {
          const lastRes = resultsBuf[resultsBuf.length - 1]?.name || recent[recent.length - 1]?.name;
          return lastRes ? `Avoid redoing '${lastRes}'. Continue with the next logical subtask.` : 'Continue with the next logical subtask.';
        })();

        // Check recent write status to guide auto-recovery
        let writeGuidance = '';
        try {
          const wb = (this.recentWriteStatusByAgent.get(agentName) || []).slice(-3);
          const lastWrite = wb.reverse().find(w => ['file_edit'].includes(w.name));
          if (lastWrite) {
            if (!lastWrite.ok) {
              const p = lastWrite.path ? ` (${lastWrite.path})` : '';
              writeGuidance = `\nDetected a recent ${lastWrite.name}${p} with failing diagnostics or formatting. Immediately run diagnose_file_syntax on the affected file, apply fixes (format/syntax), then verify with read_file + assert_file_contains before concluding.`;
            } else if (lastWrite.ok && lastWrite.path) {
              writeGuidance = `\nLast write ${lastWrite.name} succeeded with clean diagnostics for ${lastWrite.path}. Continue from there.`;
            }
          }
        } catch {}

        const preamble = `\n\n[System Handoff Note]\nTask Summary:\n- Objective: ${objective}\n- Recent tools: ${summary}\n${resultsLines ? `- Recent results:\n${resultsLines}\n` : ''}${lastTextLine}${writeGuidance}\nGuidance: Previous step did not finalize cleanly. ${nextHint} Use the current filesystem state and working directory.`;
        console.log(`[LOG] Handoff preamble built, length: ${preamble.length}, includes tools: ${recent.length > 0}`);
        return preamble;
      } catch (e) {
        console.log(`[LOG] Error building preamble: ${e}`);
        return '';
      }
    };

    // Helper: recall prior task snapshots from memory to avoid duplicate work
    const buildRecallPreamble = async () => {
      try {
        const preferences = await memoryManager.recallUserPreferences();
        if (!preferences || preferences.length === 0) return '';
        const lines: string[] = [];
        for (const pref of preferences.slice(0, 3)) {
          try {
            const category = String(pref?.content?.category || 'general');
            const preferenceSummary = (() => {
              const prefVal = pref?.content?.preference;
              if (typeof prefVal === 'string') return prefVal;
              if (prefVal && typeof prefVal === 'object') {
                const entries = Object.entries(prefVal).map(([k, v]) => `${k}: ${typeof v === 'string' ? v : JSON.stringify(v)}`);
                return entries.slice(0, 4).join('; ');
              }
              return JSON.stringify(prefVal ?? {});
            })();
            lines.push(`- ${category}: ${preferenceSummary}`);
          } catch {}
        }
        if (lines.length === 0) return '';
        return `\n\n[User Preferences]\n${lines.join('\n')}\nHonor these preferences while executing the task.`;
      } catch (err) {
        try { console.warn('Failed to recall user preferences for preamble:', err); } catch {}
        return '';
      }
    };

    // Execute with optional fallbacks for rate limits
    const tryExecute = async (modelKey: string, label?: string, extraPreamble?: string, opts?: { disableTools?: boolean }): Promise<any> => {
      const providerModel = this.getModelProvider(modelKey);
      // Prepend recall preamble if available (prevents duplicate work across sessions)
      const recall = await buildRecallPreamble();
      const promptWithRecall = label
        ? `${input}${recall}${buildHandoffPreamble()}${extraPreamble || ''}`
        : `${input}${recall}`;
      const cfg: any = {
        ...commonConfig,
        model: providerModel,
        prompt: promptWithRecall,
      };
      const disableTools = !!opts?.disableTools;
      if (disableTools) {
        cfg.tools = undefined;
        cfg.toolChoice = undefined;
      }

      const baseCodegenOptions: CodeGenerationOptions = {
        model: providerModel,
        maxSteps: options.maxSteps ?? agent.maxSteps ?? 10,
        onStepFinish: commonConfig.onStepFinish,
        context: commonConfig.context,
        instructions: agent.instructions,
        abortSignal: (commonConfig as any)?.abortSignal,
      };

      if (options.stream) {
        console.log(`📡 [Orchestrator] Streaming response for agent ${agentName}${label ? ` (${label})` : ''}`);
        this.emit('modelAttempt', { agent: agentName, model: modelKey });
        if (agentName === 'CodeGenerationAgent' && !disableTools) {
          return await executeCodeGeneration(promptWithRecall, { ...baseCodegenOptions, stream: true });
        }
        return await this.doGenerate(modelKey, cfg, true);
      }
      this.emit('modelAttempt', { agent: agentName, model: modelKey });
      let res: any;
      let codegenStructuredAnswer: any | undefined;
      if (agentName === 'CodeGenerationAgent' && !disableTools) {
        res = await executeCodeGeneration(promptWithRecall, { ...baseCodegenOptions });
        const extraction = this._extractCodegenAnswer(res);
        codegenStructuredAnswer = extraction.structured;
        if ((typeof ((res as any)?.text) !== 'string' || String((res as any)?.text).trim().length === 0) && extraction.rendered) {
          (res as any).text = extraction.rendered;
        }
        if (codegenStructuredAnswer && typeof codegenStructuredAnswer === 'object') {
          (res as any).structuredCodegen = codegenStructuredAnswer;
        }
      } else {
        res = await this.doGenerate(modelKey, cfg, false);
      }

      // Synthesize a final text from the 'answer' tool call for CodeGenerationAgent when needed
      try {
        if (agentName === 'CodeGenerationAgent' && !disableTools) {
          if (!codegenStructuredAnswer) {
            const extraction = this._extractCodegenAnswer(res);
            codegenStructuredAnswer = extraction.structured;
            if ((typeof ((res as any)?.text) !== 'string' || String((res as any)?.text).trim().length === 0) && extraction.rendered) {
              (res as any).text = extraction.rendered;
            }
            if (codegenStructuredAnswer && typeof codegenStructuredAnswer === 'object') {
              (res as any).structuredCodegen = codegenStructuredAnswer;
            }
          }
        }
      } catch {}

      this._ensureAnswerResult(res, agentName);

      // Guard: some agents must produce a final text answer. If we only have toolCalls
      // and no text, treat as transient failure so upstream can retry or fall back.
      try {
        const txt = (res as any)?.text;
        const tc = (res as any)?.toolCalls;
        const mustFinalize = ['CodeGenerationAgent', 'DebugAgent', 'SecurityAnalysisAgent'].includes(agentName);
        if (!options.stream && mustFinalize) {
          const emptyText = typeof txt !== 'string' || txt.trim().length === 0;
          if (emptyText && Array.isArray(tc) && tc.length > 0) {
            const err: any = new Error(`AGENT_EMPTY_FINAL: ${agentName} produced no final text after ${tc.length} tool calls on ${modelKey}. This likely indicates a partial or interrupted run. Retry is recommended.`);
            err.code = 'AGENT_EMPTY_FINAL';
            throw err;
          }
        }
      } catch (guardErr) {
        // Re-throw to outer handler for retry/fallback logic
        throw guardErr;
      }

      // Guard: require a confirmed write/edit success with clean diagnostics
      // Apply ONLY to CodeGenerationAgent. Research/Planning/etc. should never trigger this.
      let analysisKey: string | null = null;
      try {
        analysisKey = this._analysisTurnKey(agentName, options, input);
        if (!writeGuardEnabled) {
          // Guard disabled via env; accept result
          this._resetAnalysisProgress(analysisKey);
          return res;
        }
        if (agentName !== 'CodeGenerationAgent') {
          this._resetAnalysisProgress(analysisKey);
          return res;
        }

        if (!codegenStructuredAnswer) {
          const extraction = this._extractCodegenAnswer(res);
          codegenStructuredAnswer = extraction.structured;
        }
        const answerArgs: any = codegenStructuredAnswer;
        const summaryText = typeof answerArgs?.summary === 'string' ? answerArgs.summary : '';
        const declaredFiles = Array.isArray(answerArgs?.filesModified) ? answerArgs.filesModified : [];
        const declaredPaths = declaredFiles
          .map((f: any) => (typeof f?.path === 'string' ? f.path.trim() : ''))
          .filter((p: string) => p.length > 0);
        const finalText = typeof (res as any)?.text === 'string' ? (res as any).text : '';

        const wb = (this.recentWriteStatusByAgent.get(agentName) || []).slice(-10);
        const callWrites = wb.filter(w => w.t >= callStartTime);
        const hadFileEditAttempt = callWrites.some(w => w.name === 'file_edit');

        const skipWriteGuard = this._shouldSkipWriteGuardForAnalysis({
          input: String(input ?? ''),
          routeText: options.routeText,
          summary: summaryText,
          finalText,
          declaredPaths,
          hadFileEditAttempt,
          readFileCount: runToolStats.readFile.total,
          uniqueReadPaths: runToolStats.readFile.paths.size,
          readFileMaxRepeat: runToolStats.readFile.maxRepeat,
          listDirCount: runToolStats.listDirectory.total,
          totalToolCalls: runToolStats.totalCalls,
        });

        if (skipWriteGuard) {
          this._resetAnalysisProgress(analysisKey);
          console.log('ℹ️ Skipping NO_WRITE_CONFIRMED guard: analysis/discovery heuristics satisfied.');
          return res;
        }

        let wroteOk = callWrites.some(w => w.ok === true && (w.name === 'file_edit') || (w.name === 'file_write'));
          // Heuristic bailout: if diagnostics were run and are already clean (likely no-op request),
          // avoid looping. We tolerate a clean diagnose without a write once per run.
          if (!wroteOk) {
            try {
              const dBuf = (this.recentDiagnosticsOkByAgent.get(agentName) || []).slice(-5);
              const now3 = Date.now();
              const hasDiagOk = dBuf.reverse().some(d => (now3 - d.t) < 5 * 60_000 && d.ok === true);
              if (hasDiagOk) {
                console.log('ℹ️ Skipping NO_WRITE_CONFIRMED guard: recent diagnostics show ok=true — no write necessary.');
                return res;
              }
            } catch {}
          }
          // If no confirmed write yet, accept verification-based success:
          // recent assert_file_contains OK + clean diagnostics on asserted path.
          if (!wroteOk) {
            try {
              const asserts = (this.recentAssertStatusByAgent.get(agentName) || []).slice(-5);
              const now2 = Date.now();
              for (const a of asserts.reverse()) {
                if (now2 - a.t > 2 * 60_000) continue; // last 2 minutes
                if (!a.ok) continue;
                if (a.path) {
                  try {
                    const diag = await diagnoseFile(a.path, { includeFormatting: true });
                    if (diag.ok) {
                      // Treat as confirmed success; synthesize a write confirmation so downstream flows proceed
                      const wb = this.recentWriteStatusByAgent.get(agentName) || [];
                      wb.push({ name: 'synthetic_write_confirmation', ok: true, path: a.path, t: Date.now() });
                      while (wb.length > 20) wb.shift();
                      this.recentWriteStatusByAgent.set(agentName, wb);
                      wroteOk = true;
                      break;
                    }
                  } catch (e) {
                    console.warn('diagnoseFile failed during verification-based success check', { agent: agentName, path: a.path, error: e });
                  }
                }
              }
            } catch (e) {
              console.warn('Error during verification-based success check (assert/diagnose loop):', e);
            }
          }
          if (!wroteOk) {
            const allowed = this._maybeAllowContextGatheringTurn(analysisKey, runToolStats, agentName);
            if (allowed) {
              return res;
            }
          }
          if (!wroteOk) {
            // Attempt a single auto-repair pass by prompting the agent with precise diagnostics
            // Run-wide throttle: only one auto-repair across nested retries/fallbacks for this task+agent+path
            // Compute a stable budget key
            let budgetKey = '';
            try {
              const inferredPath = this._inferLastAffectedPath(this.recentToolArgs, callWrites);
              const projectRoot = this.currentProjectId || process.cwd();
              const h = crypto.createHash('sha256').update(`${agentName}|${projectRoot}|${inferredPath || ''}|${String(input).slice(0,512)}`).digest('hex');
              budgetKey = h;
            } catch {}
            if (budgetKey) {
              const b = this.noWriteBudgetByKey.get(budgetKey) || { count: 0, firstT: Date.now() };
              // Increased retry budget from 1 to 3 attempts per task
              if (b.count >= 3) {
                const err: any = new Error('NO_WRITE_CONFIRMED: Terminal — auto-repair budget exhausted for this task.');
                err.code = 'NO_WRITE_CONFIRMED';
                err.terminal = true;
                throw err;
              }
              b.count += 1;
              this.noWriteBudgetByKey.set(budgetKey, b);
            }

            if (autoRepairTriggered) {
              // Already attempted; avoid repeating log spam in fallbacks/retries
              const contextHint = (() => {
                try {
                  const last = callWrites.slice().reverse().find(w => ['file_edit'].includes(w.name));
                  return last?.path ? ` Affected file: ${last.path}` : '';
                } catch { return ''; }
              })();
              const err: any = new Error(`NO_WRITE_CONFIRMED: CodeGenerationAgent produced no confirmed change.${contextHint} Auto-repair already attempted.`);
              err.code = 'NO_WRITE_CONFIRMED';
              err.terminal = true;
              throw err;
            }
            try {
              const lastPath = this._inferLastAffectedPath(this.recentToolArgs, callWrites);
              let diagSummary = '';
              if (lastPath) {
                try {
                  const diag = await diagnoseFile(lastPath, { includeFormatting: true });
                  const parts: string[] = [];
                  parts.push(`File: ${diag.file}`);
                  parts.push(`OK: ${diag.ok}`);
                  if (diag.errors.length) {
                    parts.push('Errors:');
                    for (const e of diag.errors.slice(0, 5)) {
                      parts.push(`- [${e.type}] ${e.file}:${e.line}${e.column ? ':'+e.column : ''} ${e.message}`);
                    }
                  }
                  if (diag.formattingIssues) parts.push('Formatting issues detected by Prettier/Dart formatter.');
                  diagSummary = parts.join('\n');
                } catch {}
              }
              // External‑path awareness: if affected file is outside current project, guide the agent to set working directory
              const projectRoot = this.currentProjectId || process.cwd();
              const outsideWD = ((): boolean => {
                try {
                  if (!lastPath) return false;
                  const rel = path.relative(projectRoot, lastPath);
                  return !(rel === '' || (!rel.startsWith('..') && !path.isAbsolute(rel)));
                } catch { return false; }
              })();
              const wdHint = (lastPath && outsideWD)
                ? `\n0) Working directory mismatch detected. Current working directory is \`${projectRoot}\`, but the affected file is outside that tree. Call set_working_directory with dirPath: \`${path.dirname(lastPath)}\` before continuing.\n`
                : '';
              const autoRepairPreamble = `\n\n[Auto-Repair Guidance]\nA recent file_edit did not confirm clean diagnostics. ${lastPath ? `Focus file: ${lastPath}.` : ''}\nDiagnostics summary:\n${diagSummary || 'No diagnostics available.'}\n\nActions you must take now:\n${wdHint}1) Run diagnose_file_syntax on the affected file${lastPath ? ` (${lastPath})` : ''} with includeFormatting=true.\n2) If you wrapped a widget (e.g., InkWell), ensure it is properly closed (balance parentheses/braces).\n3) Apply precise fixes (syntax, imports, types, formatting) using file_edit:\n   - For targeted changes, use operation='replace' or operation='patch' with patchFormat='anchored'.\n   - For unified diffs, use operation='patch' and patchFormat='unified_diff'. Start with strict (default). If it fails with invalid_patch/context mismatch, retry with mergeStrategy='fuzzy'. If in a Git repo, consider mergeStrategy='git_3way'. As a last resort, use mergeStrategy='conflict_markers' and immediately resolve the markers with follow-up anchored patches.\n4) Re-run diagnose_file_syntax to verify it is clean.\n5) Perform read-after-write verification (read_file, assert_file_contains).\n6) If the project has a build/test pipeline, run run_build_check (thorough=true when available) and fix reported issues.\nOnly conclude once diagnostics/build are clean.`;

              console.warn('⚠️ NO_WRITE_CONFIRMED detected; attempting auto-repair pass with explicit diagnostics.');
              autoRepairTriggered = true;
              // If the affected file is outside the working directory, stop auto-repair and surface a terminal hint
              if (lastPath) {
                try {
                  const projectRoot2 = this.currentProjectId || process.cwd();
                  const rel2 = path.relative(projectRoot2, lastPath);
                  const outside2 = !(rel2 === '' || (!rel2.startsWith('..') && !path.isAbsolute(rel2)));
                  if (outside2) {
                    const err: any = new Error(`WORKDIR_OUTSIDE: Affected file is outside current working dir (${projectRoot2}). Call set_working_directory to ${path.dirname(lastPath)} and retry.`);
                    err.code = 'NO_WRITE_CONFIRMED';
                    err.terminal = true;
                    throw err;
                  }
                } catch {}
              }
              // Prefer a dedicated code generation handoff to ensure write tools + behavior
              if (agentName !== 'CodeGenerationAgent') {
                try {
                  console.warn('🔁 Handing off to CodeGenerationAgent for write/diagnostic recovery…');
                  const inputWithRepair = `${input}${autoRepairPreamble}`;
                  this._resetAnalysisProgress(analysisKey);
                  return await this.executeWithAgent('CodeGenerationAgent', inputWithRepair, {
                    ...options,
                    maxSteps: Math.max(12, options.maxSteps || 10),
                    onAgentSelected: options.onAgentSelected,
                  });
                } catch (handoffErr) {
                  console.warn('⚠️ CodeGenerationAgent handoff failed, retrying auto-repair on current agent…', handoffErr);
                }
              }

              // Fallback: retry current agent with explicit auto-repair preamble
              const repaired = await tryExecute(modelKey, 'auto-repair-1', autoRepairPreamble);
              this._resetAnalysisProgress(analysisKey);
              return repaired;
            } catch (autoRepairErr) {
              const contextHint = (() => {
                try {
                  const lp = this._inferLastAffectedPath(this.recentToolArgs, callWrites);
                  if (!lp) return '';
                  const projectRoot = this.currentProjectId || process.cwd();
                  const rel = path.relative(projectRoot, lp);
                  const outside = !(rel === '' || (!rel.startsWith('..') && !path.isAbsolute(rel)));
                  const wdMsg = outside ? ` (outside current working dir: ${projectRoot}; call set_working_directory to ${path.dirname(lp)})` : '';
                  return ` Affected file: ${lp}${wdMsg}`;
                } catch { return ''; }
              })();
              const err: any = new Error(`NO_WRITE_CONFIRMED: CodeGenerationAgent produced no confirmed file_edit with clean diagnostics for an apply/edit request.${contextHint}. Please run diagnose_file_syntax (includeFormatting=true) and fix reported issues; if still failing, delegate to DebugAgent.`);
              err.code = 'NO_WRITE_CONFIRMED';
              err.terminal = true;
              throw err;
          }
        }
        if (wroteOk) {
          this._resetAnalysisProgress(analysisKey);
        }
      } catch (guardErr2) {
        this._resetAnalysisProgress(analysisKey);
        throw guardErr2;
      }
      try {
        const steps = (res as any).steps?.length ?? 0;
        const totalTokens = res.usage?.totalTokens ?? 0;
        const finish = (res as any).finishReason ?? 'unknown';
        console.log(`✅ [Orchestrator] Completed agent ${agentName}${label ? ` (${label})` : ''} | steps=${steps} | tokens=${totalTokens} | finish=${finish}`);
        // Capture last text for future handoffs
        try { if ((res as any).text) this.lastAgentTextByAgent.set(agentName, (res as any).text); } catch {}
        // Persist a snapshot of progress for future sessions
        try { await this.persistTaskSnapshot(agentName, input, finish === 'stop' ? 'completed' : 'checkpoint'); } catch {}
      } catch {}
      return res;
    };

    const isOpenAIRateLimit = (err: any) => {
      try {
        const msg = (err?.message || String(err || '')).toLowerCase();
        const status = (err?.status ?? err?.code ?? err?.httpStatus ?? '').toString();
        return (
          msg.includes('rate limit') ||
          msg.includes('rate_limit_exceeded') ||
          msg.includes('too many requests') ||
          msg.includes('quota') ||
          msg.includes('requests per') ||
          msg.includes('tokens per') ||
          status === '429' ||
          msg.includes('429')
        );
      } catch { return false; }
    };

    const isEmptyFinalGuard = (err: any) => {
      try { return (err?.code === 'AGENT_EMPTY_FINAL') || String(err?.message || '').includes('AGENT_EMPTY_FINAL'); } catch { return false; }
    };

    const isNoWriteConfirmed = (err: any) => {
      try {
        if (err?.terminal === true) return false; // treat terminal no-write as non-retryable
        return (err?.code === 'NO_WRITE_CONFIRMED') || String(err?.message || '').includes('NO_WRITE_CONFIRMED');
      } catch { return false; }
    };

    const fallbackOrderFor = (primaryModel: string, agentNameParam: string): string[] => {
      console.log(`[LOG] Determining fallback order for agent: ${agentNameParam}, primary model: ${primaryModel}`);

      // Explicit, centralized chain for CodeGeneration:
      // gpt-5 → gpt-5-mini  → gemini-2.5-flash → gemini-2.5-pro  (skip unavailable)
      if (agentNameParam === 'CodeGenerationAgent') {
        const full = ['gpt-5', 'gpt-5-mini', 'gemini-2.5-flash', 'gemini-2.5-pro'];
        const chain = full
          .filter(m => m !== primaryModel)
          .filter(m => this.isModelAvailable(m));
        console.log(`[LOG] CodeGeneration fallback chain: ${chain.join(' -> ')}`);
        return chain;
      }

      // General fallbacks remain unchanged for other agents
      if (primaryModel === 'gpt-5') {
        const chain = ['gpt-5-mini', 'gemini-2.5-flash', 'gemini-2.5-pro'];
        console.log(`[LOG] GPT-5 fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }

      if (primaryModel === 'gemini-2.5-flash') {
        const chain = ['gemini-2.5-pro', 'gpt-5-mini'];
        console.log(`[LOG] Gemini fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }

      if (primaryModel === 'gemini-2.5-pro') {
        const chain = ['gpt-5-mini'];
        console.log(`[LOG] Gemini Pro fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }
      if (primaryModel === 'gpt-5-mini') {
        const chain = ['gpt-5', 'gpt-4o'];
        console.log(`[LOG] gpt-5-mini fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }
      if (primaryModel === 'gpt-4o') {
        const chain = ['gpt-4.1'];
        console.log(`[LOG] GPT-4o fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }

      if (primaryModel === 'gpt-4.1' && !['code-generation', 'CodeGenerationAgent'].includes(agentNameParam)) {
        const chain = ['gpt-5', 'gemini-2.5-flash', 'gemini-2.5-pro'];
        console.log(`[LOG] GPT-4.1 fallback chain activated for ${agentNameParam}: ${chain.join(' -> ')}`);
        return chain.filter(m => this.isModelAvailable(m));
      }

      console.log(`[LOG] No fallback chain for this agent/model combination`);
      return [];
    };

    // Offline models are used only as a fallback after frontier models are exhausted (see below).

    // First attempt with the selected model (centralized selection)
    try {
      if (!this.isModelAvailable(selectedModelKey)) {
        const chain0 = fallbackOrderFor(selectedModelKey, agentName);
        for (const fb of chain0) {
          try {
            console.log(`[LOG] Primary model unavailable; attempting ${fb}`);
            return await tryExecute(fb, `fallback -> ${fb}`);
          } catch (e) {
            console.warn(`❌ Pre-attempt fallback to ${fb} failed: ${(e as any)?.message || e}`);
          }
        }
        // As last resort, try offline model without tools
        const offProv = String(config.orchestrator?.offlineProvider || '').toLowerCase();
        const offModel = String(config.orchestrator?.offlineModel || '');
        if (offProv === 'ollama' && offModel && config.ollama?.baseURL) {
          const offlineKey = `ollama:${offModel}`;
          try {
            console.log(`🧰 [Orchestrator] Attempting offline fallback via ${offlineKey}`);
            return await tryExecute(offlineKey, 'offline-fallback', undefined, { disableTools: true });
          } catch (offErr) {
            console.warn(`❌ Offline fallback failed: ${(offErr as any)?.message || offErr}`);
          }
        }
        throw new Error(`No available frontier models for ${agentName}`);
      }
      return await tryExecute(selectedModelKey);
    } catch (err: any) {
      // Handle aborts as pause/cancel checkpoints
      try {
        const emsg = String(err?.message || '');
        const isAbort = (err?.name === 'AbortError') || /abort/i.test(emsg);
        if (isAbort) {
          try { await this.persistTaskSnapshot(agentName, input, 'checkpoint'); } catch {}
          throw err; // bubble up; server will end stream quickly
        }
      } catch {}
      console.log(`[LOG] Error in executeWithAgent for ${agentName} with model ${agent.model}: ${err.message}`);
      console.log(`[LOG] Is OpenAI rate limit? ${isOpenAIRateLimit(err)}`);
      // Retry once for empty-final or no-write-confirmed guard errors
      if (isEmptyFinalGuard(err) || isNoWriteConfirmed(err)) {
        console.warn(`⚠️ Guard triggered (${isEmptyFinalGuard(err) ? 'empty-final' : 'no-write-confirmed'}) for ${agentName}. Retrying once on same model…`);
        try {
          const res = await tryExecute(selectedModelKey, 'retry-1');
          return res;
        } catch (retryErr: any) {
          console.warn(`❌ Retry after empty-final also failed: ${retryErr?.message || retryErr}`);
          // Fall through to fallback chain
        }
      }
      // If we hit an OpenAI rate limit (or retryable guards) and have a fallback chain, hand off
      if (isOpenAIRateLimit(err) || isEmptyFinalGuard(err) || isNoWriteConfirmed(err)) {
        const chain = fallbackOrderFor(selectedModelKey, agentName);
        console.log(`[LOG] Rate limit confirmed, fallback chain: ${chain.length > 0 ? chain.join(', ') : 'empty'}`);
        if (chain.length > 0) {
          const reason = isOpenAIRateLimit(err) ? 'rate limit' : (isEmptyFinalGuard(err) ? 'empty-final' : 'no-write-confirmed');
          console.warn(`⚠️ Issue on ${agent.model} for ${agentName} (${reason}). Switching to fallback…`);
          // Persist a handoff snapshot so recovery has durable context
          try { await this.persistTaskSnapshot(agentName, input, 'handoff'); } catch {}
          for (const fb of chain) {
            try {
              console.log(`[LOG] Attempting fallback to model: ${fb} for rate limit recovery`);
              // Notify UI that the active agent context is switching models
              try { options.onAgentSelected?.({ agent: `${agentName} (${fb})`, confidence: 0.9 }); } catch {}
              const res = await tryExecute(fb, `fallback -> ${fb}`);
              console.log(`[LOG] Fallback to ${fb} succeeded - model switch logged`);
              // Emit event for logging model switch
              this.emit('modelSwitched', { from: agent.model, to: fb, agent: agentName, reason: isOpenAIRateLimit(err) ? 'rate-limit' : (isEmptyFinalGuard(err) ? 'empty-final' : 'no-write-confirmed') });
              return res;
            } catch (err2: any) {
              console.warn(`❌ Fallback to ${fb} failed: ${err2?.message || err2}`);
              continue;
            }
          }
          console.log(`[LOG] All fallbacks exhausted for ${agentName}`);
        } else {
          console.log(`[LOG] No fallback chain available for ${agentName}`);
        }
      }
      // Final fallback: attempt offline model (ollama) without tools if configured
      try {
        const offProv = String(config.orchestrator?.offlineProvider || '').toLowerCase();
        const offModel = String(config.orchestrator?.offlineModel || '');
        if (offProv === 'ollama' && offModel && config.ollama?.baseURL) {
          const offlineKey = `ollama:${offModel}`;
          console.warn(`🧰 [Orchestrator] Attempting final offline fallback via ${offlineKey}`);
          const res = await tryExecute(offlineKey, 'offline-fallback', undefined, { disableTools: true });
          return res;
        }
      } catch (offErr: any) {
        console.warn(`❌ Final offline fallback failed: ${offErr?.message || String(offErr)}`);
      }
      // If no fallback succeeded or error was not a rate limit, rethrow
      throw err;
    }
  // Persist a durable task snapshot to memory so future sessions can resume or avoid duplication
  }

  private async persistTaskSnapshot(agentName: string, input: string, status: 'checkpoint' | 'handoff' | 'completed') {
    try {
      const now = new Date();
      const recentTools = Array.from(new Set(this.recentToolArgs.slice(-10).map(e => e.name)));
      const recentResults = (this.recentToolResultsByAgent.get(agentName) || []).slice(-3);
      const lastNote = this.lastAgentTextByAgent.get(agentName) || '';
      const objective = String(input).replace(/\s+/g, ' ').trim();
      const content = {
        kind: 'task_summary',
        objective,
        agent: agentName,
        status,
        recentTools,
        recentResults,
        lastAssistantNote: lastNote,
        timestamp: now.toISOString(),
      };

      await memoryManager.create(
        MemoryType.SEMANTIC,
        content,
        {
          tags: ['task', 'summary', 'task-summary', 'searchable', agentName],
          source: `orchestrator_${status}`,
          confidence: 0.9,
          priority:  MemoryPriority.MEDIUM,
          projectId: this.currentProjectId,
          ttl: 90 * 24 * 60 * 60 * 1000 // 90 days
        } as any
      );
      console.log(`🧠 Persisted ${status} task snapshot (${agentName})${this.currentProjectId ? ` for project ${this.currentProjectId}` : ''}`);
    } catch (e) {
      console.warn('Failed to persist task snapshot:', e);
    }
  }

  // --- Lightweight planning + delegation (classic path) ---
  private formatAgentDisplayName(internalName: string): string {
    return internalName.replace(/Agent$/g, ' Agent').replace(/([a-z])([A-Z])/g, '$1 $2').replace(/\s+/g, ' ').trim();
  }

  private mapPlannedAgentToRegistered(agentLabel: string | undefined): string {
    if (!agentLabel || typeof agentLabel !== 'string') return 'ResearchAgent';
    const trimmed = agentLabel.trim();
    if (this.agents.has(trimmed)) return trimmed;
    const direct = Array.from(this.agents.keys()).find((name) => name.toLowerCase() === trimmed.toLowerCase());
    if (direct) return direct;
    const a = trimmed.toLowerCase();
    if (['research', 'researcher', 'analysis', 'analyze'].includes(a)) return 'ResearchAgent';
    if (['code', 'coding', 'implementation', 'build', 'code-generation', 'codegen'].includes(a)) return 'CodeGenerationAgent';
    if (['debug', 'fix', 'bugfix', 'troubleshoot'].includes(a)) return 'DebugAgent';
    if (['security', 'security-analysis', 'pentest', 'vuln'].includes(a)) return 'SecurityAnalysisAgent';
    if (['review', 'code-review', 'qa'].includes(a)) return 'CodeReviewAgent';
    if (['file-analysis', 'file analysis', 'analysis-worker', 'file risk', 'file verification'].includes(a)) return 'FileAnalysisAgent';
    if (['planning', 'plan'].includes(a)) return 'PlanningAgent';
    if (['workspace', 'google', 'google-workspace', 'gmail', 'calendar', 'drive', 'email', 'inbox'].includes(a)) return 'GoogleWorkspaceAgent';
    if (['computer', 'computer-use', 'browser', 'automation', 'web-automation'].includes(a)) return 'ComputerUseAgent';
    if (['isolated-computer-use', 'headless', 'background', 'monitoring'].includes(a)) return 'IsolatedComputerUseAgent';
    if (['diagnostic', 'healthcheck', 'diagnostics'].includes(a)) return 'DebugAgent';
    return 'ResearchAgent';
  }

  private buildStepRoutingBasis(
    step: {
      id?: string;
      title?: string;
      description?: string;
      input?: string;
      acceptanceCriteria?: string[];
      dependsOn?: string[];
    },
    planSummary?: string,
  ): string {
    const lines: string[] = [];
    if (planSummary && planSummary.trim()) lines.push(`Plan summary: ${planSummary.trim()}`);
    const id = step.id ? ` (${step.id})` : '';
    if (step.title && step.title.trim()) lines.push(`Step title${id}: ${step.title.trim()}`);
    if (step.description && step.description.trim()) lines.push(`Step description: ${step.description.trim()}`);
    if (step.input && step.input.trim()) lines.push(`Provided input: ${step.input.trim()}`);
    if (Array.isArray(step?.dependsOn) && step.dependsOn.length > 0) {
      lines.push(`Depends on steps: ${step.dependsOn.join(', ')}`);
    }
    if (Array.isArray(step?.acceptanceCriteria) && step.acceptanceCriteria.length > 0) {
      lines.push('Acceptance criteria:');
      for (const crit of step.acceptanceCriteria) {
        if (typeof crit === 'string' && crit.trim()) {
          lines.push(`- ${crit.trim()}`);
        }
      }
    }
    lines.push('Choose the single best execution agent for this step. Prefer specialized workers and avoid TaskOrchestrator or PlanningAgent for execution.');
    return lines.join('\n');
  }

  private normalizeAgentChoice(step: { title?: string; description?: string; input?: string }, mapped: string): string {
    const desc = `${step.title || ''} ${step.description || ''} ${step.input || ''}`.toLowerCase();
    const analysisKeywords = ['analyze', 'investigate', 'search', 'scan', 'list', 'identify', 'which file', 'find file', 'read file', 'grep', 'explore'];
    const codeChangeKeywords = ['write code', 'implement', 'refactor', 'apply patch', 'update file', 'run command', 'execute command', 'edit file', 'generate code', 'modify', 'change', 'update', 'patch', 'fix', 'create file', 'replace'];
    const computerUseKeywords = ['open', 'navigate', 'go to', 'browser', 'click', 'type', 'play', 'scroll', 'form', 'submit', 'select'];
    const backgroundKeywords = ['monitor', 'watch for', 'when available', 'auto-add', 'headless', 'in the background', 'periodically'];
    const workspaceKeywords = ['email', 'emails', 'gmail', 'inbox', 'unread', 'calendar', 'drive', 'google drive', 'gdrive'];
    const websiteSearchHints = ['on youtube', 'on youtube music', 'on music.youtube.com', 'in the browser', 'search bar', 'site search'];
    const fileAnalysisHints = ['run_file_analysis', 'file analysis', 'file risk', 'post-change analysis', 'modified files overview'];
    if (fileAnalysisHints.some(k => desc.includes(k))) return 'FileAnalysisAgent';
    if (analysisKeywords.some(k => desc.includes(k)) && websiteSearchHints.some(k => desc.includes(k))) return 'ComputerUseAgent';
    if (workspaceKeywords.some(k => desc.includes(k))) return 'GoogleWorkspaceAgent';
    if (analysisKeywords.some(k => desc.includes(k))) return 'ResearchAgent';
    if (codeChangeKeywords.some(k => desc.includes(k))) return 'CodeGenerationAgent';
    if (computerUseKeywords.some(k => desc.includes(k))) return 'ComputerUseAgent';
    if (backgroundKeywords.some(k => desc.includes(k))) return 'IsolatedComputerUseAgent';
    return mapped;
  }

  private async resolveStepAgent(
    step: { title?: string; description?: string; input?: string; agent?: string; acceptanceCriteria?: string[]; dependsOn?: string[] },
    planSummary?: string,
  ): Promise<string> {
    const direct = this.mapPlannedAgentToRegistered(step.agent);
    if (!['TaskOrchestrator', 'PlanningAgent'].includes(direct)) {
      return direct;
    }

    const basis = this.buildStepRoutingBasis(step, planSummary);
    if (basis.trim().length > 0) {
      try {
        const routed = await this.routeToAgent(basis);
        const mapped = this.mapPlannedAgentToRegistered(routed.agent);
        if (!['TaskOrchestrator', 'PlanningAgent'].includes(mapped)) {
          return mapped;
        }
      } catch (e) {
        console.warn('Failed to resolve step agent via routeToAgent:', e);
      }
    }

    const heuristic = this.normalizeAgentChoice(step, direct);
    const mappedHeuristic = this.mapPlannedAgentToRegistered(heuristic);
    if (!['TaskOrchestrator', 'PlanningAgent'].includes(mappedHeuristic)) {
      return mappedHeuristic;
    }

    return 'ResearchAgent';
  }

  private _createStreamAggregator(agent: string): StreamStepAggregation {
    return {
      agent,
      textChunks: [],
      toolCalls: [],
      writeConfirmed: false,
      errors: [],
    };
  }

  private _observeStreamEventForAggregation(agg: StreamStepAggregation, event: any): void {
    if (!agg || event == null) return;

    const recordToolResult = (id: string | undefined, name: string, raw: any) => {
      const normalizedResult = raw;
      let matching = id
        ? [...agg.toolCalls].reverse().find(call => call.id === id)
        : undefined;

      if (!matching) {
        matching = [...agg.toolCalls].reverse().find(call => call.name === name && typeof call.result === 'undefined');
      }

      if (matching) {
        matching.result = normalizedResult;
        if (id && !matching.id) matching.id = id;
      } else {
        agg.toolCalls.push({ id, name, result: normalizedResult });
      }

      if (!agg.structuredAnswer && typeof name === 'string' && name.toLowerCase() === 'answer' && normalizedResult) {
        if (typeof normalizedResult === 'object') {
          agg.structuredAnswer = normalizedResult;
        } else if (typeof normalizedResult === 'string') {
          const parsed = this._safeJsonParse(normalizedResult);
          if (parsed && typeof parsed === 'object') {
            agg.structuredAnswer = parsed;
          }
        }
      }

      if (typeof name === 'string' && name === 'file_edit' && normalizedResult && typeof normalizedResult === 'object') {
        const success = typeof normalizedResult.success === 'boolean' ? normalizedResult.success : true;
        const modified = typeof normalizedResult.modified === 'boolean' ? normalizedResult.modified : true;
        const replacements = typeof normalizedResult.replacements === 'number' ? normalizedResult.replacements : undefined;
        if (success && modified && (replacements == null || replacements > 0)) {
          agg.writeConfirmed = true;
        }
        const errMsg = typeof normalizedResult.error === 'string' ? normalizedResult.error : undefined;
        if (errMsg) agg.errors.push(errMsg);
        const msg = typeof normalizedResult.message === 'string' ? normalizedResult.message : undefined;
        if (msg && msg !== errMsg) agg.errors.push(msg);
      }

      if (normalizedResult && typeof normalizedResult === 'object' && typeof normalizedResult.error === 'string') {
        agg.errors.push(normalizedResult.error);
      }
    };

    if (typeof event === 'string') {
      agg.textChunks.push(event);
      return;
    }

    if (typeof event !== 'object') return;

    const type = String(event.type || '').toLowerCase();

    if (type === 'raw_model_stream_event') {
      const data = event.data || {};
      const delta = typeof data?.delta === 'string' ? data.delta : undefined;
      const text = typeof data?.text === 'string' ? data.text : undefined;
      const payload = delta ?? text;
      if (typeof payload === 'string' && payload.length > 0) {
        agg.textChunks.push(payload);
      }
      return;
    }

    if (type === 'tool_call') {
      const data = event.data || {};
      const name = typeof data?.name === 'string' ? data.name : 'tool';
      const callId = this._extractToolCallId(data);
      const argsRaw = data?.args;
      const parsedArgs = typeof argsRaw === 'string' ? this._safeJsonParse(argsRaw) ?? argsRaw : argsRaw;
      if (callId) {
        const existing = agg.toolCalls.find(call => call.id === callId);
        if (existing) {
          existing.name = name;
          existing.args = parsedArgs;
          existing.id = callId;
        } else {
          agg.toolCalls.push({ id: callId, name, args: parsedArgs });
        }
      } else {
        agg.toolCalls.push({ name, args: parsedArgs });
      }
      return;
    }

    if (type === 'tool_result') {
      const data = event.data || {};
      const name = typeof data?.name === 'string' ? data.name : 'tool';
      const callId = this._extractToolCallId(data);
      const resultRaw = data?.result;
      const parsedResult = typeof resultRaw === 'string' ? this._safeJsonParse(resultRaw) ?? resultRaw : resultRaw;
      recordToolResult(callId, name, parsedResult);
      return;
    }

    if (type === 'message' && typeof event.data?.message === 'string') {
      agg.textChunks.push(event.data.message);
      return;
    }

    const maybeText = typeof event?.text === 'string' ? event.text : undefined;
    if (maybeText) {
      agg.textChunks.push(maybeText);
    }
  }

  private _extractToolCallId(data: any): string | undefined {
    if (!data || typeof data !== 'object') return undefined;

    const normalize = (value: unknown): string | undefined => {
      if (typeof value === 'string') {
        const trimmed = value.trim();
        return trimmed.length > 0 ? trimmed : undefined;
      }
      if (typeof value === 'number' && Number.isFinite(value)) return String(value);
      if (typeof value === 'bigint') return value.toString();
      return undefined;
    };

    const candidates: unknown[] = [
      (data as any).id,
      (data as any).toolCallId,
      (data as any).tool_call_id,
      (data as any).callId,
      (data as any).call_id,
      (data as any).toolId,
      (data as any).tool_id,
      (data as any).itemId,
      (data as any).item_id,
      (data as any).tool?.id,
      (data as any).rawItem?.id,
      (data as any).rawItem?.toolCallId,
      (data as any).rawItem?.tool_call_id,
      (data as any).rawItem?.callId,
      (data as any).rawItem?.call_id,
      (data as any).raw?.id,
    ];

    for (const candidate of candidates) {
      const normalized = normalize(candidate);
      if (normalized) return normalized;
    }

    return undefined;
  }

  private _finalizeStreamAggregator(agg: StreamStepAggregation): { result: any; meta: { writeConfirmed: boolean; errors: string[] } } {
    const text = agg.textChunks.join('');
    const result: any = {};
    if (text.trim().length > 0) {
      result.text = text;
    }
    if (agg.toolCalls.length > 0) {
      result.toolCalls = agg.toolCalls.map(call => ({
        toolName: call.name,
        args: call.args,
        result: call.result,
        toolCallId: call.id,
      }));
    }
    if (agg.structuredAnswer) {
      result.structuredCodegen = agg.structuredAnswer;
    }
    return {
      result,
      meta: {
        writeConfirmed: agg.writeConfirmed,
        errors: agg.errors,
      },
    };
  }

  private _normalizeStreamCompletion(stream: any): Promise<void> | undefined {
    try {
      const completed = stream?.completed;
      if (completed && typeof completed.then === 'function') {
        return completed as Promise<void>;
      }
    } catch {
      /* ignore completion inspection errors */
    }
    return undefined;
  }

  private _safeJsonParse<T = any>(value: unknown): T | undefined {
    if (typeof value !== 'string') return undefined;
    try {
      return JSON.parse(value) as T;
    } catch {
      return undefined;
    }
  }

  private async getStructuredPlan(input: string, context?: any): Promise<{ summary?: string; steps: Array<any> }> {
    const planningPrompt = `You are the PlanningAgent. Create a concise, structured execution plan in strict JSON.\n\nInput:\n${input}\n\nOutput JSON schema:\n{\n  "summary": string,\n  "steps": [\n    {\n      "id": string,\n      "title": string,\n      "description": string,\n      "agent": string,\n      "input": string,\n      "dependsOn": string[],\n      "acceptanceCriteria": string[]\n    }\n  ]\n}\n\nRules:\n- Return ONLY valid JSON. No prose, no markdown.\n- Keep steps minimal and actionable.`;
    const planningTimeoutMs = Number(context?.planningTimeoutMs ?? 60000);
    let plan: any;
    try {
      const mod = await import('./PlanningAgent');
      if (typeof (mod as any).createStructuredExecutionPlan === 'function') {
        plan = await Promise.race([
          (mod as any).createStructuredExecutionPlan(input, { planningDepth: 'detailed', context }),
          new Promise((_, rej) => setTimeout(() => rej(new Error('planning_timeout')), planningTimeoutMs))
        ]);
      } else {
        throw new Error('structured_plan_tool_unavailable');
      }
    } catch {
      const mod = await import('./PlanningAgent');
      const res = await Promise.race([
        (mod as any).planningAgent.execute(planningPrompt, { stream: false, planningDepth: 'detailed', context }),
        new Promise((_, rej) => setTimeout(() => rej(new Error('planning_timeout')), planningTimeoutMs))
      ]);
      const text = (res as any)?.text ?? String(res ?? '');
      const fence = text.match(/```[a-zA-Z]*\n([\s\S]*?)```/);
      const raw = fence ? fence[1] : text;
      plan = JSON.parse(raw);
    }
    if (!plan || !Array.isArray(plan.steps) || plan.steps.length === 0) throw new Error('PlanningAgent returned an empty or invalid plan');
    // Normalize and sanitize steps
    const normalizedSteps = plan.steps.map((s: any, i: number) => ({
      id: String(s.id ?? `step_${i + 1}`),
      title: s.title ? String(s.title) : undefined,
      description: String(s.description ?? s.task ?? ''),
      agent: String(s.agent ?? s.owner ?? 'research').toLowerCase(),
      input: s.input != null ? String(s.input) : undefined,
      dependsOn: Array.isArray(s.dependsOn) ? s.dependsOn.map((d: any) => String(d)) : [],
      focus: s.focus ? String(s.focus) : undefined,
      instructions: Array.isArray(s.instructions) ? s.instructions.map((instr: any) => String(instr)) : undefined,
      expectedOutputs: Array.isArray(s.expectedOutputs) ? s.expectedOutputs.map((o: any) => String(o)) : undefined,
      acceptanceCriteria: Array.isArray(s.acceptanceCriteria) ? s.acceptanceCriteria.map((a: any) => String(a)) : undefined,
      handoffNotes: s.handoffNotes ? String(s.handoffNotes) : undefined,
      references: Array.isArray(s.references) ? s.references.map((r: any) => String(r)) : undefined,
    }));

    // Drop planning-only steps to avoid recursive re-planning feedback loops
    const filtered = normalizedSteps.filter((s: any) => {
      const mapped = this.mapPlannedAgentToRegistered(s.agent);
      return mapped !== 'PlanningAgent';
    });

    // Deduplicate identical steps (same mapped agent + description + input)
    const seen = new Set<string>();
    const deduped: any[] = [];
    for (const s of filtered) {
      const mapped = this.mapPlannedAgentToRegistered(s.agent);
      const key = `${mapped}|${(s.title || '').trim()}|${(s.description || '').trim()}|${(s.input || '').trim()}`.toLowerCase();
      if (seen.has(key)) continue;
      seen.add(key);
      deduped.push(s);
    }
    const existingIds = new Set(deduped.map((s: any) => s.id));
    const codeReviewStepIds = deduped
      .filter((s: any) => this.mapPlannedAgentToRegistered(s.agent) === 'CodeReviewAgent')
      .map((s: any) => s.id);
    const hasFileAnalysisStep = deduped.some((s: any) => this.mapPlannedAgentToRegistered(s.agent) === 'FileAnalysisAgent');

    if (codeReviewStepIds.length > 0 && !hasFileAnalysisStep) {
      let counter = 1;
      let generatedId = `file_analysis_${codeReviewStepIds[codeReviewStepIds.length - 1] || 'summary'}`;
      while (existingIds.has(generatedId)) {
        generatedId = `file_analysis_${counter++}`;
      }
      deduped.push({
        id: generatedId,
        title: 'Post-change File Analysis',
        description: 'After code review completes, inspect each modified file to confirm no security, build, or regression risks remain. Use list_modified_files and run_file_analysis, incorporate code review findings, and recommend follow-up actions if issues remain.',
        agent: 'file-analysis',
        input: undefined,
        dependsOn: Array.from(new Set(codeReviewStepIds)),
        acceptanceCriteria: [
          'Summarize run_file_analysis findings for every modified file with explicit risk level.',
          'Call out any gaps still present after the code review and recommend remediation steps.',
          'Confirm whether the build/test state is safe or specify additional verification required.',
        ],
      });
    }

    plan.steps = deduped;
    return plan;
  }

  async planAndDelegate(input: string, options: OrchestratorOptions & { maxWorkers?: number; context?: any } = {}): Promise<any> {
    const runId = newRunId('task');
    const plan = await this.getStructuredPlan(input, options.context);
    try { appendStep(runId, { t: Date.now(), agent: 'TaskOrchestrator', step: { type: 'plan_created', summary: plan.summary, steps: plan.steps.map((s: any) => ({ id: s.id, agent: s.agent, dependsOn: s.dependsOn || [] })) } }); } catch {}

    if (options.stream) {
      const self = this;
      const completedStepSummaries = new Map<string, string>();
      const planStepMeta = new Map<string, any>(plan.steps.map((s: any) => [s.id, { ...s }]));
      const streamingStepResults: Array<{ stepId: string; agent: string; success: boolean; summary?: string; output?: any; error?: string; meta?: any }> = [];
      const iterator = {
        async *[Symbol.asyncIterator]() {
          yield { type: 'plan_created', data: { summary: plan.summary, steps: plan.steps } };
          // Analyze plan for cycles and missing dependencies, and pre-mark blocked steps
          const stepsById = new Map<string, any>();
          for (const s of plan.steps) stepsById.set(s.id, s);
          const dependents = new Map<string, Set<string>>();
          const indegree = new Map<string, number>();
          for (const s of plan.steps) { dependents.set(s.id, new Set()); indegree.set(s.id, 0); }
          const unknownDeps = new Map<string, string[]>();
          for (const s of plan.steps) {
            const deps = Array.isArray(s.dependsOn) ? s.dependsOn : [];
            for (const d of deps) {
              if (!stepsById.has(d)) {
                const arr = unknownDeps.get(s.id) || [];
                arr.push(d);
                unknownDeps.set(s.id, arr);
              } else {
                indegree.set(s.id, (indegree.get(s.id) || 0) + 1);
                (dependents.get(d) as Set<string>).add(s.id);
              }
            }
          }
          const blocked = new Map<string, string>();
          for (const [sid, arr] of unknownDeps) blocked.set(sid, `missing_dependency: ${arr.join(', ')}`);
          const queue0: string[] = [];
          const visited0 = new Set<string>();
          for (const [sid, deg] of indegree) if ((deg || 0) === 0 && !blocked.has(sid)) queue0.push(sid);
          while (queue0.length) {
            const cur = queue0.shift() as string;
            visited0.add(cur);
            for (const dep of dependents.get(cur) || []) {
              if (blocked.has(dep)) continue;
              const nd = (indegree.get(dep) || 0) - 1;
              indegree.set(dep, nd);
              if (nd === 0) queue0.push(dep);
            }
          }
          for (const sid of stepsById.keys()) if (!visited0.has(sid) && !blocked.has(sid)) blocked.set(sid, 'cycle_detected');
          const cascadeQ: string[] = Array.from(blocked.keys());
          while (cascadeQ.length) {
            const b = cascadeQ.shift() as string;
            for (const d of dependents.get(b) || []) {
              if (!blocked.has(d)) { blocked.set(d, 'blocked_by_dependency'); cascadeQ.push(d); }
            }
          }
          type Active = {
            step: any;
            iterator: AsyncIterator<any>;
            agentInternal: string;
            agentDisplay: string;
            aggregator: StreamStepAggregation;
            next?: Promise<{ index: number; r: IteratorResult<any> }>;
            index: number;
            failedOnStart?: boolean;
            completion?: Promise<void>;
            completionHandled?: boolean;
            failureReason?: string;
          };
          const depsLeft = new Map<string, Set<string>>();
          for (const s of plan.steps) depsLeft.set(s.id, new Set(s.dependsOn || []));
          const done = new Set<string>();
          const active: Active[] = [];
          // Concurrency heuristic: if the plan has no explicit dependencies and includes
          // multiple code-generation steps, run sequentially to avoid file-edit collisions.
          const hasNoDeps = plan.steps.every((s: any) => !s.dependsOn || s.dependsOn.length === 0);
          const codegenCount = plan.steps.filter((s: any) => self.normalizeAgentChoice(s, self.mapPlannedAgentToRegistered(s.agent)) === 'CodeGenerationAgent').length;
          const unsafeParallel = hasNoDeps && codegenCount > 1;
          const requestedWorkers = Math.max(1, (options as any).maxWorkers ?? 2);
          const maxWorkers = unsafeParallel ? 1 : requestedWorkers;

          // Emit skipped markers for blocked steps so UI shows why they won't run
          for (const [sid, reason] of blocked.entries()) {
            const st = stepsById.get(sid);
            const agentInternal = self.normalizeAgentChoice(st, self.mapPlannedAgentToRegistered(st.agent));
            const agentDisplay = self.formatAgentDisplayName(agentInternal);
            yield { type: 'delegation_skipped', data: { stepId: sid, agent: agentDisplay, title: st.title || sid, description: st.description, reason } } as any;
          }

          const startStep = async (step: any): Promise<Active> => {
            const agentInternal = await self.resolveStepAgent(step, plan.summary);
            const agentDisplay = self.formatAgentDisplayName(agentInternal);
            let res: any; let failedOnStart = false;
            const aggregator = self._createStreamAggregator(agentInternal);
            try {
              const dependencySummaries = Array.isArray(step?.dependsOn)
                ? step.dependsOn.map((dep: any) => ({
                    id: String(dep),
                    summary: completedStepSummaries.get(String(dep)),
                    handoffNotes: planStepMeta.get(String(dep))?.handoffNotes,
                  }))
                : [];
              const inputForAgent = buildAgentInput(step, {
                title: step.title || step.id,
                dependencySummaries,
                planSummary: plan.summary,
              });
              res = await self.executeWithAgent(agentInternal, inputForAgent, {
                stream: true,
                maxSteps: Math.min(options.maxSteps ?? 8, 12),
                onStepFinish: (s: any) => {
                  try { appendStep(runId, { t: Date.now(), agent: agentInternal, step: s }); } catch {}
                  try { (options as any)?.onStepFinish?.(s); } catch {}
                },
              } as any);
            } catch (e) { failedOnStart = true; res = { async *[Symbol.asyncIterator]() {} } as any; }
            const it = (res as any)[Symbol.asyncIterator] ? (res as any)[Symbol.asyncIterator]() : (res as any).textStream?.[Symbol.asyncIterator]?.();
            return {
              step,
              iterator: it,
              agentInternal,
              agentDisplay,
              aggregator,
              index: -1,
              failedOnStart,
              completion: self._normalizeStreamCompletion(res),
            };
          };

          const schedule = async (): Promise<any[]> => {
            const startEvents: any[] = [];
            while (active.length < maxWorkers) {
              const next = plan.steps.find(s => !done.has(s.id) && !active.find(a => a.step.id === s.id) && !blocked.has(s.id) && (depsLeft.get(s.id)?.size || 0) === 0);
              if (!next) break;
              const a = await startStep(next);
              a.index = active.length;
              active.push(a);
              startEvents.push({ type: 'delegation_start', data: { stepId: next.id, agent: a.agentDisplay, title: next.title || `${a.agentDisplay} Started`, description: `Processing delegated task` } } as any);
              a.next = a.iterator.next().then(r => ({ index: a.index, r }));
            }
            return startEvents;
          };

          for (const ev of await schedule()) { yield ev as any; }
          while (active.length) {
            const settled = await Promise.race(active.filter(a => a.next).map(a => a!.next!));
            if (!settled) break;
            const { index, r } = settled;
            const a = active[index];
            if (!a) continue;
            if (r.done) {
              // step done
              done.add(a.step.id);
              if (a.completion && !a.completionHandled) {
                try {
                  await a.completion;
                } catch (err: any) {
                  a.failureReason = err instanceof Error ? err.message : String(err ?? 'unknown stream error');
                } finally {
                  a.completionHandled = true;
                }
              }
              const aggregated = self._finalizeStreamAggregator(a.aggregator);
              const summary = summarizeAgentResult(aggregated.result, a.step);
              if (summary) {
                completedStepSummaries.set(a.step.id, summary);
                const metaEntry = planStepMeta.get(a.step.id);
                if (metaEntry) metaEntry.handoffNotes = summary;
              }
              const stepError = a.failureReason || aggregated.meta.errors[0];
              const success = !a.failedOnStart && !stepError;
              streamingStepResults.push({
                stepId: a.step.id,
                agent: a.agentDisplay,
                success,
                summary,
                output: aggregated.result,
                error: stepError,
                meta: aggregated.meta,
              });
              yield {
                type: 'delegation_end',
                data: { stepId: a.step.id, agent: a.agentDisplay, success, summary, error: stepError },
              } as any;
              // update dependents
              for (const s of plan.steps) { const deps = depsLeft.get(s.id); if (deps && deps.has(a.step.id)) deps.delete(a.step.id); }
              // remove
              active.splice(index, 1);
              for (let i = index; i < active.length; i++) active[i].index = i;
              for (const ev of await schedule()) { yield ev as any; }
            } else {
              self._observeStreamEventForAggregation(a.aggregator, r.value);
              yield r.value as any;
              a.next = a.iterator.next().then(rr => ({ index: a.index, r: rr }));
            }
          }
          yield { type: 'raw_model_stream_event', data: { type: 'output_text', text: `\n— Orchestration complete —` } } as any;
        }
      } as AsyncIterable<any>;
      (iterator as any).stepResults = streamingStepResults;
      (iterator as any).completedSummaries = completedStepSummaries;
      return iterator as any;
    }

    // Non-stream: run steps sequentially and synthesize a concise result
    const results: Array<{ stepId: string; agent: string; success: boolean; output?: any; summary?: string; error?: string; startedAt: number; completedAt: number; }>
      = [];
    // Compute blocked steps (cycles/missing deps) to skip gracefully
    const stepsByIdNS = new Map(plan.steps.map((s: any) => [s.id, s] as const));
    const dependentsNS = new Map<string, Set<string>>();
    const indegreeNS = new Map<string, number>();
    for (const s of plan.steps) { dependentsNS.set(s.id, new Set()); indegreeNS.set(s.id, 0); }
    const unknownDepsNS = new Map<string, string[]>();
    for (const s of plan.steps) {
      for (const d of (Array.isArray(s.dependsOn) ? s.dependsOn : [])) {
        if (!stepsByIdNS.has(d)) {
          const arr = unknownDepsNS.get(s.id) || [];
          arr.push(d);
          unknownDepsNS.set(s.id, arr);
        } else {
          indegreeNS.set(s.id, (indegreeNS.get(s.id) || 0) + 1);
          (dependentsNS.get(d) as Set<string>).add(s.id);
        }
      }
    }
    const blockedNS = new Map<string, string>();
    for (const [sid, arr] of unknownDepsNS) blockedNS.set(sid, `missing_dependency: ${arr.join(', ')}`);
    const qNS: string[] = [];
    const visitedNodes = new Set<string>();
    for (const [sid, deg] of indegreeNS) if ((deg || 0) === 0 && !blockedNS.has(sid)) qNS.push(sid);
    while (qNS.length) {
      const cur = qNS.shift() as string;
      visitedNodes.add(cur);
      for (const dep of dependentsNS.get(cur) || []) {
        if (blockedNS.has(dep)) continue;
        const nd = (indegreeNS.get(dep) || 0) - 1;
        indegreeNS.set(dep, nd);
        if (nd === 0) qNS.push(dep);
      }
    }
    for (const sid of stepsByIdNS.keys()) if (!visitedNodes.has(sid) && !blockedNS.has(sid)) blockedNS.set(sid, 'cycle_detected');
    const cas: string[] = Array.from(blockedNS.keys());
    while (cas.length) {
      const b = cas.shift() as string;
      for (const d of dependentsNS.get(b) || []) { if (!blockedNS.has(d)) { blockedNS.set(d, 'blocked_by_dependency'); cas.push(d); } }
    }

    const completedStepSummaries = new Map<string, string>();
    const planStepMeta = new Map<string, any>(plan.steps.map((s: any) => [s.id, s] as const));

    for (const step of plan.steps) {
      const agentInternal = await this.resolveStepAgent(step, plan.summary);
      const agentDisplay = this.formatAgentDisplayName(agentInternal);
      const startedAt = Date.now();
      try {
        if (blockedNS.has(step.id)) {
          results.push({ stepId: step.id, agent: agentDisplay, success: false, error: blockedNS.get(step.id) || 'blocked', startedAt, completedAt: Date.now() });
          continue;
        }
        const dependencySummaries = Array.isArray((step as any)?.dependsOn)
          ? (step as any).dependsOn.map((dep: any) => ({
              id: String(dep),
              summary: completedStepSummaries.get(String(dep)),
              handoffNotes: planStepMeta.get(String(dep))?.handoffNotes,
            }))
          : [];
        const inputForAgent = buildAgentInput(step as any, {
          title: step.title || step.id,
          dependencySummaries,
          planSummary: plan.summary,
        });
        // Forward onStepFinish so the API layer can surface tool events during delegated steps
        const res = await this.executeWithAgent(
          agentInternal,
          inputForAgent,
          {
            stream: false,
            maxSteps: Math.min(options.maxSteps ?? 8, 12),
            onStepFinish: (s: any) => {
              try { appendStep(runId, { t: Date.now(), agent: agentInternal, step: s }); } catch {}
              try { (options as any)?.onStepFinish?.(s); } catch {}
            },
          } as any
        );
        const summary = summarizeAgentResult(res, step);
        completedStepSummaries.set(step.id, summary);
        const metaEntry = planStepMeta.get(step.id);
        if (metaEntry) metaEntry.handoffNotes = summary;
        results.push({ stepId: step.id, agent: agentDisplay, success: true, output: res, summary, startedAt, completedAt: Date.now() });
      } catch (e: any) {
        results.push({ stepId: step.id, agent: agentDisplay, success: false, error: e?.message || String(e), startedAt, completedAt: Date.now() });
      }
    }
    // Optional synthesis
    let synthesizedText = '';
    try {
      const { synthesisWorker } = await import('./workers/SynthesisWorker');
      const workerResultsPayload = results.map(({ output, ...rest }) => ({ ...rest, result: output }));
      const synthesizedResult = await synthesisWorker.execute(
        JSON.stringify({ originalTask: input, workerResults: workerResultsPayload }),
        { context: { taskType: 'synthesis', originalTask: input }, synthesisType: 'detailed', prioritizeInsights: true }
      );
      synthesizedText = typeof synthesizedResult?.synthesizedResponse === 'string' ? synthesizedResult.synthesizedResponse : JSON.stringify(synthesizedResult);
    } catch {
      synthesizedText = results.map(r => `- [${r.success ? 'ok' : 'fail'}] ${r.agent} (${r.stepId})`).join('\n');
    }
    return { type: 'delegated_orchestration', originalTask: input, text: synthesizedText, finalMessage: synthesizedText, runId, plan, stepResults: results };
  }

  // --- Session plan helpers (for follow-ups like "proceed") ---
  public setSessionPlan(sessionId: string, plan: any): void { this.upsertSessionPlan(sessionId, plan); }
  public getSessionPlan(sessionId: string): any | undefined { return this.sessionContext.get(sessionId)?.lastPlan; }
  public markStepCompleted(sessionId: string, stepId: string): void { this.markStepComplete(sessionId, stepId); }
  private async upsertSessionPlan(sessId: string, plan: any): Promise<void> {
    try {
      if (sessId && plan) {
        const ctx = this.sessionContext.get(sessId) || ({ lastUpdated: Date.now() } as any);
        ctx.lastPlan = plan; ctx.lastStrategy = 'guided_pipeline'; ctx.lastUpdated = Date.now();
        if (!Array.isArray(ctx.completedSteps)) ctx.completedSteps = [];
        this.sessionContext.set(sessId, ctx);
      }
    } catch (e) { console.error(`Failed to upsert session plan for session ${sessId}:`, e); }
  }
  private async markStepComplete(sessId: string, stepId: string): Promise<void> {
    try {
      const ctx = this.sessionContext.get(sessId) || ({ lastUpdated: Date.now() } as any);
      if (!Array.isArray(ctx.completedSteps)) ctx.completedSteps = [];
      if (!ctx.completedSteps.includes(stepId)) ctx.completedSteps.push(stepId);
      ctx.lastUpdated = Date.now();
      this.sessionContext.set(sessId, ctx);
    } catch (e) { console.error(`Failed to mark step ${stepId} as complete for session ${sessId}:`, e); }
  }

  // --- Direct tool execution with retry/backoff and recovery ---
  async executeToolCall(
    functionCall: { name: string; args: any },
    context?: any,
    model: string = this.currentModel
  ): Promise<string> {
    const startTime = Date.now();
    // Find tool in global or agent-specific registries
    let tool: any = (this.globalTools || {})[functionCall.name];
    if (!tool) {
      for (const agent of this.agents.values()) {
        const ts = agent.tools || {};
        if (functionCall.name in ts) { tool = (ts as any)[functionCall.name]; break; }
      }
    }
    if (!tool) {
      const available = [
        ...Object.keys(this.globalTools || {}),
        ...Array.from(this.agents.values()).flatMap(a => Object.keys(a.tools || {}))
      ];
      throw new Error(`Tool '${functionCall.name}' not found. Available tools: ${available.join(', ') || 'none'}`);
    }
    const retryConfig = { ...DEFAULT_RETRY_CONFIG, maxRetries: 3, enableIntelligentFeedback: true } as any;
    let lastError: Error | undefined; let attemptCount = 0;
    while (attemptCount < retryConfig.maxRetries) {
      attemptCount++;
      try {
        const timeoutPromise = new Promise<never>((_, reject) => setTimeout(() => reject(new Error(`Tool execution timeout after 30 seconds for ${functionCall.name}`)), 30000));
        const consolidated = await this._executeToolOnce(tool, functionCall, context, model, timeoutPromise);
        const execMs = Date.now() - startTime;
        try { console.log(`✅ Tool ${functionCall.name} completed on attempt ${attemptCount} in ${execMs}ms`); } catch {}
        return consolidated;
      } catch (error: any) {
        lastError = error instanceof Error ? error : new Error(String(error));
        const errorFeedback = analyzeToolError(lastError, functionCall.name, functionCall.args);
        console.error(`❌ Tool execution failed for ${functionCall.name} (attempt ${attemptCount}/${retryConfig.maxRetries}):`, lastError.message);
        if (errorFeedback.isSystemError && errorFeedback.requiresDiagnosticAgent) {
          try {
            const recoveryContext: RecoveryContext = {
              sessionId: context?.sessionId || 'unknown',
              agentName: 'DanteOrchestrator',
              toolName: functionCall.name,
              originalInput: JSON.stringify(functionCall.args),
              errorHistory: [{ error: lastError, timestamp: Date.now(), recoveryAttempted: false }],
              metadata: { attemptNumber: attemptCount, model, totalRetries: retryConfig.maxRetries }
            };
            const recoveryResult = await errorRecoveryMiddleware.recover(lastError, recoveryContext, {
              useAdvancedRecovery: true,
              diagnosticModel: 'gpt-5-mini',
              recoveryModel: 'gpt-5-mini',
            });
            if (recoveryResult.success && recoveryResult.recovered) {
              try {
                const retryTimeout = new Promise<never>((_, reject) => setTimeout(() => reject(new Error(`Tool execution timeout after 30 seconds for ${functionCall.name}`)), 30000));
                const consolidated = await this._executeToolOnce(tool, functionCall, context, model, retryTimeout);
                return consolidated;
              } catch { /* fall through */ }
            }
          } catch { /* continue */ }
        }
        if (!errorFeedback.shouldRetry || attemptCount >= retryConfig.maxRetries) {
          const userError = formatToolErrorForUser({ toolName: functionCall.name, error: lastError, parameters: functionCall.args, attemptNumber: attemptCount, timestamp: Date.now() }, errorFeedback);
          throw new Error(userError);
        }
        const delay = retryConfig.retryDelayMs * Math.pow(retryConfig.backoffMultiplier, attemptCount - 1);
        await new Promise(res => setTimeout(res, delay));
      }
    }
    throw lastError || new Error(`Tool ${functionCall.name} failed after ${attemptCount} attempts`);
  }

  private async _executeToolOnce(
    tool: any,
    functionCall: { name: string; args: any },
    context: any,
    model: string,
    timeoutPromise: Promise<never>
  ): Promise<string> {
    let executionPromise: Promise<any>;
    if (typeof tool?.invoke === 'function') {
      const argsJson = JSON.stringify(functionCall.args);
      const runContext = (context || {}) as any;
      executionPromise = tool.invoke(runContext, argsJson);
    } else if (typeof tool?.execute === 'function') {
      executionPromise = tool.execute(functionCall.args, context);
    } else if (typeof tool === 'function') {
      executionPromise = tool(functionCall.args);
    } else {
      throw new Error(`Tool '${functionCall.name}' is not callable.`);
    }
    const result = await Promise.race([executionPromise, timeoutPromise]);
    const validated = this.validateToolResult(result, functionCall.name);
    const consolidated = consolidateToolResult(validated, functionCall.name, model);
    return typeof consolidated === 'string' ? consolidated : JSON.stringify(consolidated);
  }

  private validateToolResult(result: any, _toolName: string): any {
    if (typeof result === 'string' && result.includes('An error occurred while running the tool')) {
      const errorMatch = result.match(/Error: Error: (.+)/);
      const errorMessage = (errorMatch && errorMatch[1]) ? errorMatch[1] : result;
      throw new Error(errorMessage);
    }
    if (typeof result === 'object' && result !== null) {
      if ((result as any).error || ((result as any).success === false && (result as any).message)) {
        const errorMessage = (result as any).error || (result as any).message || 'Tool execution failed';
        throw new Error(errorMessage);
      }
    }
    return result;
  }

  private async _processInternal(input: string, options: OrchestratorOptions = {}): Promise<any> {
    const runId = newRunId('orchestrator');
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    try { this.activeRequests.set(requestId, { startTime, model: this.currentModel, type: 'unknown' }); } catch {}
    // Route using provided routeText (typically last user message) when available
    const routeBasis = options.routeText && typeof options.routeText === 'string' && options.routeText.trim().length > 0
      ? options.routeText
      : input;
    const { agent: agentName, confidence } = await this.routeToAgent(routeBasis);
    console.log(`📊 Routing to ${agentName} (confidence: ${(confidence * 100).toFixed(0)}%)`);
    this.emit('agentSelected', { agent: agentName, confidence, input });
    try { options.onAgentSelected?.({ agent: agentName, confidence }); } catch (e) { console.warn('onAgentSelected callback error:', e); }
    // Wrap onStepFinish to also record steps for UI
    const wrappedOptions = {
      ...options,
      background: (options as any).background === true || this.isBackgroundTask(routeBasis, agentName),
      onStepFinish: (step: any) => {
        try { appendStep(runId, { t: Date.now(), agent: agentName, step }); } catch {}
        try { options.onStepFinish?.(step); } catch {}
      }
    };
    let result: any;
    try {
      result = await this.executeWithAgent(agentName, input, wrappedOptions);
      const processingTime = Date.now() - startTime;
      this.recordRequestSuccess(this.currentModel, processingTime, 0);
      this.emit('requestCompleted', { requestId, model: this.currentModel, processingTime, success: true });
    } catch (e) {
      const processingTime = Date.now() - startTime;
      this.recordRequestFailure(this.currentModel, e as Error, processingTime);
      this.emit('requestFailed', { requestId, model: this.currentModel, error: e instanceof Error ? e.message : String(e), processingTime });
      throw e;
    } finally {
      try { this.activeRequests.delete(requestId); } catch {}
      // In case there are queued requests waiting for capacity
      try { this.processQueue().catch(() => {}); } catch {}
    }
    const asAny = result as any;
    if (asAny && asAny.textStream && typeof asAny.textStream[Symbol.asyncIterator] === 'function') return result;
    if (asAny && typeof asAny[Symbol.asyncIterator] === 'function') return result;
    if (asAny && 'text' in asAny) {
      // If text is empty but we have toolCalls (e.g., answer tool only), surface a helpful log
      try {
        if ((typeof asAny.text !== 'string' || asAny.text.trim().length === 0) && Array.isArray(asAny.toolCalls) && asAny.toolCalls.length > 0) {
          console.log(`ℹ️ [Orchestrator] Empty text with ${asAny.toolCalls.length} toolCalls — UI should read structured fields/toolCalls.`);
        }
      } catch {}
      return { text: asAny.text, agent: agentName, toolCalls: asAny.toolCalls, usage: asAny.usage, steps: asAny.steps, runId };
    }
    return { ...result, runId };
  }

  async process(input: string, options: OrchestratorOptions = {}): Promise<any> {
    // If we're at capacity, enqueue the request
    if (this.activeRequests.size >= this.maxConcurrentRequests) {
      // Basic priority heuristic: shorter maxSteps gets slightly higher priority
      const priority = Math.max(1, Math.min(3, (options.maxSteps && options.maxSteps <= 6) ? 2 : 1));
      return this.enqueue(priority, () => this._processInternal(input, options));
    }
    return this._processInternal(input, options);
  }

  switchModel(model: string): void {
    const normalized = DanteAIOrchestrator.normalizeModelId(model);
    if (!MODEL_MAP[normalized]) throw new Error(`Model ${model} not supported`);
    this.currentModel = normalized;
    console.log(`🔄 Switched to model: ${model}`);
  }

  async routeToSpecificAgent(agentName: string, input: string, options: OrchestratorOptions = {}): Promise<any> {
    console.log(`🎯 Routing directly to agent: ${agentName}`);
    return await this.executeWithAgent(agentName, input, options);
  }

  getAgents(): AgentDefinition[] { return Array.from(this.agents.values()); }


  private _shouldSkipWriteGuardForAnalysis(args: {
    input: string;
    routeText?: string;
    summary: string;
    finalText: string;
    declaredPaths: string[];
    hadFileEditAttempt: boolean;
    readFileCount: number;
    uniqueReadPaths: number;
    readFileMaxRepeat: number;
    listDirCount: number;
    totalToolCalls: number;
  }): boolean {
    /**
     * Purpose:
     * Decide whether to skip the NO_WRITE_CONFIRMED guard for CodeGenerationAgent.
     * We skip only when the run appears to be analysis/planning-only (no declared files, no edit attempts)
     * and the output language explicitly indicates "no changes", or clearly reflects analysis/planning intent.
     *
     * Inputs considered:
     * - args.declaredPaths: assistant-declared modified files (from 'answer' tool). If present, do NOT skip.
     * - args.hadFileEditAttempt: whether a file_edit tool was attempted. If yes, do NOT skip.
     * - args.summary/finalText: assistant's final content, scanned for explicit "no change" and analysis/planning cues.
     * - args.input/routeText: original intent text; used as a final fallback to detect prep-only requests.
     * - args.readFileCount/uniqueReadPaths/readFileMaxRepeat: volume + repetition of read_file usage (detects read loops).
     * - args.listDirCount/totalToolCalls: ensures guard is only skipped for light, read-focused analysis sessions.
     *
     * Return:
     * - true  => treat as analysis-only; skip NO_WRITE_CONFIRMED enforcement
     * - false => require confirmed write (or verification-based success) for apply/edit-type requests
     */
    try {
      // Short-circuit: if assistant declared any changed paths or actually attempted edits, enforce the guard.
      if (args.declaredPaths.length > 0) return false;
      if (args.hadFileEditAttempt) return false;

      // Relaxed read loop detection - increased thresholds to reduce false positives
      const readLoopSuspected = (() => {
        const heavyLoop = args.readFileCount >= 20 && (args.uniqueReadPaths <= 8 || args.readFileMaxRepeat >= 5);
        const dominantReads = args.totalToolCalls >= 15 && args.readFileCount / Math.max(1, args.totalToolCalls) >= 0.8;
        const directorySpinner = args.listDirCount >= 10 && (args.totalToolCalls - args.listDirCount) <= 3;
        return heavyLoop || dominantReads || directorySpinner;
      })();
      if (readLoopSuspected) return false;

      // Normalize the assistant's output we will scan for cues.
      const output = `${args.summary || ''}\n${args.finalText || ''}`.toLowerCase();
      const matchesAny = (patterns: RegExp[]) => patterns.some((re) => re.test(output));

      // Bucket 1 — Explicit "no-change" signals: direct statements that no edits/changes were made.
      const explicitNoChangePatterns: RegExp[] = [
        /\bno (?:code|file|files) changes?\b/i,
        /\bno edits?\b/i,
        /\bno modifications?\b/i,
        /\bdid(?:\s+not|n't) modify\b/i,
        /\bdid(?:\s+not|n't) change\b/i,
        /\bnothing changed\b/i,
        /\banalysis only\b/i,
        /\bread[-\s]?only\b/i,
        /\bjust reviewed\b/i,
        /\bonly reviewed\b/i,
        /\bcontext (?:gather|collection)\b/i,
        /\bdiscovery findings?\b/i,
        /\binvestigation summary\b/i,
        /\bno action needed\b/i,
        /\bno changes required\b/i,
        /\bexamination complete\b/i,
        /\bassessment complete\b/i,
        /\bmonitoring\b/i,
        /\bobservation\b/i,
      ];
      // If any explicit no-change phrase appears, skip enforcement.
      if (matchesAny(explicitNoChangePatterns)) return true;

      // Bucket 2 — Analysis stems: morphological roots to catch variants (e.g., analyze/analyzing/analysis).
      const analysisRoots = [
        'investigat', 'analyz', 'analysis', 'inspect', 'explor', 'assess', 'review', 'understand',
        'gather', 'identify', 'discover', 'context', 'survey', 'audit', 'inventory', 'map',
        'document', 'observe', 'scan', 'examin', 'catalog', 'outline', 'check', 'verify',
        'validate', 'test', 'debug', 'trace', 'monitor', 'log', 'search', 'find', 'locate'
      ];
      const analysisPatterns = analysisRoots.map((root) => new RegExp(`\\b${root}\\w*\\b`, 'i'));

      // Bucket 3 — Planning terms: forward-looking language that implies next steps rather than changes made now.
      const planningPatterns: RegExp[] = [
        /\bnext steps?\b/i,
        /\bup next\b/i,
        /\bfollow[-\s]?up\b/i,
        /\bplan\b/i,
        /\bplanned\b/i,
        /\broadmap\b/i,
        /\bto[-\s]?do\b/i,
        /\bpending\b/i,
        /\bprepar(?:e|ing)\b/i,
        /\bprep\b/i,
        /\bbefore (?:writing|editing|making changes)\b/i,
        /\binitial review\b/i,
        /\bdiscovery phase\b/i,
        /\banalysis phase\b/i,
        /\bcontext (?:gather|collection)\b/i,
        /\bstep\s*1\b/i,
        /\bphase\s*1\b/i,
        /\brecommendations?\b/i,
        /\bsuggestions?\b/i,
        /\bconsiderations?\b/i,
        /\boptions?\b/i,
        /\balternatives?\b/i,
      ];

      // Bucket 4 — Past-change terms: strong evidence the agent actually changed or produced modifications.
      // If any of these are present, we DO NOT skip enforcement.
      const pastChangePatterns: RegExp[] = [
        /\bupdated\b/i,
        /\bmodified\b/i,
        /\bimplemented\b/i,
        /\bapplied\b/i,
        /\badded\b/i,
        /\bcreated\b/i,
        /\brefactored\b/i,
        /\bfixed\b/i,
        /\bdeleted\b/i,
        /\brenamed\b/i,
        /\bpatched\b/i,
        /\breplaced\b/i,
        /\bwrote\b/i,
        /\bconverted\b/i,
        /\badjusted\b/i,
        /\bcommitted\b/i,
        /\bchanged\b/i,
        /\bintroduced\b/i,
        /\bedited\b/i,
        /\binserted\b/i,
        /\bremoved\b/i,
      ];

      // Heuristic decision:
      // If we detect analysis/planning cues and do NOT detect past-change cues, treat as analysis-only.
      const analysisCue = matchesAny(analysisPatterns);
      const planCue = matchesAny(planningPatterns);
      const changeCue = matchesAny(pastChangePatterns);
      if ((analysisCue || planCue) && !changeCue) return true;

      // Intent-level fallback: even if the output is ambiguous, if the original request clearly asked
      // for discovery/prep-only work, skip enforcement.
      const combinedIntent = `${args.input || ''} ${args.routeText || ''}`.toLowerCase();
      if (this._looksLikePrepOnlyIntent(combinedIntent)) {
        // Relaxed thresholds for legitimate analysis tasks
        if (args.totalToolCalls <= 15 && args.readFileCount <= 15) {
          return true;
        }
      }

      // Default: require write confirmation.
      return false;
    } catch (err) {
      // Defensive: on any evaluation error, log and fall back to requiring confirmation.
      try { console.warn('Failed to evaluate analysis-only guard skip:', err); } catch {}
      return false;
    }
  }

  private _looksLikePrepOnlyIntent(text: string): boolean {
    try {
      const lower = String(text || '').toLowerCase();
      if (!lower || lower.trim().length === 0) return false;
      const infoPhrases = ['retrieve', 'gather', 'collect', 'find', 'identify', 'inspect', 'review', 'understand', 'analyze', 'investigate', 'research', 'list', 'catalog', 'inventory', 'map', 'explore', 'scan', 'locate', 'determine', 'document', 'summarize', 'read'];
      const stageHints = ['step 1', 'step one', 'phase 1', 'analysis step', 'discovery step', 'initial research', 'pre-work', 'context gathering', 'analysis phase', 'prep step', 'discovery task'];
      const actionPhrases = ['apply', 'write', 'update', 'fix', 'modify', 'patch', 'change', 'implement', 'refactor', 'create', 'delete', 'replace', 'rename', 'convert', 'adjust', 'introduce'];
      const infoCue = infoPhrases.some((p) => lower.includes(p));
      const stageCue = stageHints.some((p) => lower.includes(p));
      const actionCue = actionPhrases.some((p) => lower.includes(p));
      if (lower.includes('do not modify') || lower.includes('do not edit') || lower.includes('read-only') || lower.includes('analysis only') || lower.includes('discovery only')) {
        return true;
      }
      if ((infoCue && stageCue) || (infoCue && !actionCue)) {
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  private _analysisTurnKey(agentName: string, options: OrchestratorOptions, input: string): string | null {
    try {
      const root = this.currentProjectId || process.cwd();
      const route = (options.routeText || '').slice(0, 160);
      const session = options.sessionId ? `|session:${options.sessionId}` : '';
      const payload = `${agentName}|${root}${session}|${route}|${String(input || '').slice(0, 256)}`;
      return crypto.createHash('sha256').update(payload).digest('hex');
    } catch {
      return null;
    }
  }

  private _resetAnalysisProgress(key: string | null): void {
    if (!key) return;
    this.analysisTurnAllowance.delete(key);
  }

  private _maybeAllowContextGatheringTurn(
    key: string | null,
    stats: ToolUsageStatsSnapshot,
    agentName: string,
  ): boolean {
    if (!key) return false;

    const parsedLimit = Number.parseInt(process.env.NO_WRITE_ANALYSIS_GRACE ?? '5', 10);
    const limit = Number.isFinite(parsedLimit) && parsedLimit >= 0 ? parsedLimit : 5;
    if (limit === 0) {
      this.analysisTurnAllowance.delete(key);
      return false;
    }

    const now = Date.now();
    let state = this.analysisTurnAllowance.get(key);
    if (state && now - state.lastUpdated > 10 * 60_000) {
      this.analysisTurnAllowance.delete(key);
      state = undefined;
    }
    if (!state) {
      state = {
        count: 0,
        readPaths: new Set<string>(),
        listDirs: new Set<string>(),
        totalToolCalls: 0,
        lastUpdated: now,
      };
    }

    const readPaths = Array.from(stats.readFile.paths.keys()).filter((p): p is string => typeof p === 'string' && p.length > 0);
    const dirPaths = Array.from(stats.listDirectory.paths.keys()).filter((p): p is string => typeof p === 'string' && p.length > 0);
    const introducedRead = readPaths.some((p) => !state.readPaths.has(p));
    const introducedDir = dirPaths.some((p) => !state.listDirs.has(p));
    const toolDelta = stats.totalCalls > state.totalToolCalls;

    state.lastUpdated = now;
    state.totalToolCalls = Math.max(state.totalToolCalls, stats.totalCalls);
    readPaths.forEach((p) => state.readPaths.add(p));
    dirPaths.forEach((p) => state.listDirs.add(p));

    const hasContextActivity = stats.readFile.total > 0 || stats.listDirectory.total > 0 || stats.totalCalls > 0;
    const qualifies = stats.fileEdit === 0 && hasContextActivity && (introducedRead || introducedDir || toolDelta);

    if (!qualifies) {
      this.analysisTurnAllowance.set(key, state);
      return false;
    }

    if (state.count >= limit) {
      this.analysisTurnAllowance.delete(key);
      return false;
    }

    state.count += 1;
    this.analysisTurnAllowance.set(key, state);
    try {
      console.log(`ℹ️ Skipping NO_WRITE_CONFIRMED guard: read-only progress turn ${state.count}/${limit} for ${agentName}.`);
    } catch {}
    return true;
  }

  private isBackgroundTask(text?: string, agentName?: string): boolean {
    try {
      if (agentName === 'IsolatedComputerUseAgent') return true;
      const s = String(text || '').toLowerCase();
      const hints = [
        'summarize','summary','summarization',
        'classify','classification','categorize',
        'tag','label','cluster','batch',
        'non-interactive','non interactive','background','headless',
        'periodically','cron','schedule',
        'embed','embeddings','embedding','vectorize',
        'extract','sentiment','digest','index'
      ];
      return hints.some(h => s.includes(h));
    } catch {
      return false;
    }
  }

  // Helper to infer the last affected file path based on recent writes and tool args
  private _inferLastAffectedPath(
    recentToolArgs: Array<{ name: string; args: any; t: number }>,
    recentWrites: Array<{ name: string; ok: boolean; path?: string; t: number }>
  ): string | undefined {
    try {
      // Prefer most recent write/edit/patch that references a path
      const lastWrite = recentWrites
        .slice()
        .reverse()
        .find((w) => ['file_edit'].includes(w.name));
      if (lastWrite?.path && typeof lastWrite.path === 'string' && lastWrite.path.length > 0) {
        return lastWrite.path;
      }
      // Fallback: infer from recent tool arguments likely to contain a path
      const candNames = ['diagnose_file_syntax', 'read_file', 'file_edit'];
      for (const e of recentToolArgs.slice().reverse()) {
        if (!candNames.includes(e.name)) continue;
        try {
          const argObj = typeof e.args === 'string' ? JSON.parse(e.args) : (e.args || {});
          const p = argObj?.filePath || argObj?.path;
          if (typeof p === 'string' && p.length > 0) return p;
        } catch {}
      }
    } catch (e) {
      try { console.warn('Failed to infer path from tool args/writes:', e); } catch {}
    }
    return undefined;
  }

  private async learnFromError(error: string, solution: string, agentName: string) {
    try {
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        {
          type: 'error_solution',
          error,
          solution,
          agent: agentName,
          timestamp: new Date().toISOString(),
        },
        {
          tags: ['error', 'solution', agentName],
          source: 'auto_memory',
          confidence: 0.9,
        }
      );
    } catch (e) {
      console.warn('Failed to learn from error:', e);
    }
  }

  private async salvageResults(
    agentName: string,
    input: string,
    runId: string,
    error?: any
  ): Promise<any> {
    console.warn(`⚠️ Salvaging results for ${agentName} due to timeout or error.`);

    const recentToolCalls = this.recentToolArgs.slice(-5);
    const recentToolResults = (this.recentToolResultsByAgent.get(agentName) || []).slice(-3);

    const salvagedContent = {
      error: error ? { message: error.message, code: error.code } : 'Timeout',
      message: `The agent '${agentName}' timed out or encountered an error.`,
      nextSteps: [
        'Retry the request.',
        'If the problem persists, try a different agent.',
        'Check the logs for more details.',
      ],
      partialResults: {
        recentToolCalls,
        recentToolResults,
      },
      runId,
    };

    // Remember the failure
    await this.learnFromError(
      `Agent ${agentName} failed`,
      JSON.stringify(salvagedContent),
      agentName
    );

    return {
      text: `The agent '${agentName}' timed out or encountered an error. Please try again.`,
      finalMessage: JSON.stringify(salvagedContent, null, 2),
      agent: agentName,
      runId,
      salvaged: true,
    };
  }
}

// Create the main orchestrator instance
const orchestrator = new DanteAIOrchestrator(DanteAIOrchestrator.normalizeModelId(config.openai?.defaultModel || 'gpt-5'));

// Register all agents
registerAllAgents(orchestrator);

// Register global memory tools so all agents can access them
orchestrator.registerTools({
  remember: rememberTool,
  recall: recallTool,
  forget_memories: forgetTool,
  get_memory_stats: getMemoryStatsTool,
  contextual_memory_search: contextualMemorySearchTool,
  learn_from_error: learnFromErrorTool,
  // PDF extraction tool available to all agents
  pdf_extract: pdfExtractTool,
});

// Initialize memory system (non-blocking)
void initializeMemory().catch((e) => console.warn('Memory init failed:', e));

// Compatibility wrapper for legacy callers
const DanteOrchestrator = {
  name: 'DanteOrchestrator',
  model: orchestrator.currentModel,
  tools: [] as any[],
  async execute(input: string | any[], options?: any) {
    const inputStr = Array.isArray(input) ? input.map(i => typeof i === 'string' ? i : JSON.stringify(i)).join('\n') : input;
    // Prefer routing STRICTLY on the latest user message to avoid pollution from prior assistant/system content
    let routeText: string | undefined = undefined;
    if (Array.isArray(input)) {
      try {
        const messages = input as any[];
        const userMsgs = messages.filter(
          (m) => typeof m === 'object' && m && (m as any).role === 'user'
        );
        const lastUser = userMsgs[userMsgs.length - 1];
        if (lastUser) {
          const userContent = typeof (lastUser as any).content === 'string'
            ? ((lastUser as any).content as string)
            : undefined;
          // Route ONLY on the user's last message
          if (userContent) {
            routeText = userContent;
          }
        }
      } catch {}
    }
    return await orchestrator.process(inputStr, {
      stream: options?.stream,
      maxSteps: options?.maxTurns,
      onStepFinish: options?.onStepFinish,
      onAgentSelected: options?.onAgentSelected,
      routeText
    });
  },
  setModel(model: string) { orchestrator.switchModel(model); (this as any).model = model; },
  getAgents() { return orchestrator.getAgents(); }
};

export default DanteOrchestrator;
export { orchestrator };
