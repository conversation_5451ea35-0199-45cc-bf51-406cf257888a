/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Diff-focused code review utility used by the definitions agent layer.
 * Produces a structured result that supports "no action needed" early stop.
 *
 * No default export. Named export only: CodeReviewAgent.
 */

export type Severity = "none" | "low" | "medium" | "high" | "critical";

export interface CodeReviewIssue {
  file?: string;
  line?: number;
  severity: Exclude<Severity, "none">;
  message: string;
  suggestion?: string;
  rule?: string;
}

export interface CodeReviewResult {
  criticalIssues: CodeReviewIssue[];
  highIssues: CodeReviewIssue[];
  mediumIssues: CodeReviewIssue[];
  lowIssues: CodeReviewIssue[];
  // Redundant flags for broader compatibility with consumers
  actionRequired?: boolean;
  requiresAction?: boolean;
  actionNeeded?: boolean;
  reportOnly?: boolean;
  status: "ok" | "no_action_needed" | "action_required" | "report_only";
  severity: Severity;
  files?: string[];
  findings?: string[];
  suggestions?: string[];
  metadata?: Record<string, unknown>;
  summary?: string;
}

export interface ExecuteArgs {
  diffs: string | string[];
  context?: unknown;
}

type MutableResult = Omit<CodeReviewResult, "criticalIssues" | "highIssues" | "mediumIssues" | "lowIssues"> & {
  criticalIssues: CodeReviewIssue[];
  highIssues: CodeReviewIssue[];
  mediumIssues: CodeReviewIssue[];
  lowIssues: CodeReviewIssue[];
};

function severityRank(s: Severity): number {
  switch (s) {
    case "critical": return 4;
    case "high": return 3;
    case "medium": return 2;
    case "low": return 1;
    default: return 0;
  }
}

function rankToSeverity(rank: number): Severity {
  if (rank >= 4) return "critical";
  if (rank === 3) return "high";
  if (rank === 2) return "medium";
  if (rank === 1) return "low";
  return "none";
}

function normalizeDiffs(diffs: string | string[]): string[] {
  if (Array.isArray(diffs)) return diffs.filter(Boolean);
  if (typeof diffs === "string") return diffs ? [diffs] : [];
  return [];
}

function pushIssue(bucket: CodeReviewIssue[], issue: CodeReviewIssue) {
  bucket.push(issue);
}

function analyzeAddedLine(line: string, currentFile: string | undefined, lineNumber: number, result: MutableResult) {
  const trimmed = line.trim();

  // Simple heuristics
  if (/\bany\b/.test(trimmed)) {
    pushIssue(result.mediumIssues, {
      file: currentFile,
      line: lineNumber,
      message: "Usage of 'any' type detected.",
      suggestion: "Replace 'any' with a specific type.",
      severity: "medium",
      rule: "ts-no-explicit-any",
    });
  }

  if (/\/\/\s*ts-ignore/.test(trimmed) || /@ts-ignore/.test(trimmed)) {
    pushIssue(result.highIssues, {
      file: currentFile,
      line: lineNumber,
      message: "TypeScript ignore directive added.",
      suggestion: "Remove ts-ignore and address the underlying typing issue.",
      severity: "high",
      rule: "ts-no-ts-ignore",
    });
  }

  if (/console\.(log|debug|trace)\s*\(/.test(trimmed)) {
    pushIssue(result.lowIssues, {
      file: currentFile,
      line: lineNumber,
      message: "Debug logging added.",
      suggestion: "Remove or guard console logging.",
      severity: "low",
      rule: "no-debug-logging",
    });
  }

  if (/\bFIXME\b/.test(trimmed)) {
    pushIssue(result.highIssues, {
      file: currentFile,
      line: lineNumber,
      message: "FIXME present in new code.",
      suggestion: "Resolve the FIXME before merging.",
      severity: "high",
      rule: "no-fixme",
    });
  }

  if (/\bTODO\b/.test(trimmed)) {
    pushIssue(result.lowIssues, {
      file: currentFile,
      line: lineNumber,
      message: "TODO present in new code.",
      suggestion: "Track TODO in issue tracker or address it.",
      severity: "low",
      rule: "no-todo",
    });
  }

  if (/process\.env\./.test(trimmed)) {
    pushIssue(result.mediumIssues, {
      file: currentFile,
      line: lineNumber,
      message: "Direct environment variable usage.",
      suggestion: "Ensure secrets are not logged and access is centralized.",
      severity: "medium",
      rule: "env-access",
    });
  }
}

function parseUnifiedDiff(diff: string, result: MutableResult) {
  const lines = diff.split(/\r?\n/);
  let currentFile: string | undefined;
  let newLine: number | undefined;

  for (const raw of lines) {
    if (raw.startsWith("+++ ")) {
      // e.g., "+++ b/src/file.ts"
      const m = raw.match(/^\+\+\+\s+[ab]\/(.+)$/);
      currentFile = m ? m[1] : undefined;
      if (currentFile) {
        if (!result.files) result.files = [];
        if (!result.files.includes(currentFile)) result.files.push(currentFile);
      }
      continue;
    }

    // Hunk header: @@ -oldStart[,oldLen] +newStart[,newLen] @@ ...
    if (raw.startsWith("@@ ")) {
      const hm = raw.match(/^@@\s+-\d+(?:,\d+)?\s+\+(\d+)(?:,\d+)?\s+@@/);
      if (hm) {
        newLine = parseInt(hm[1], 10);
      } else {
        newLine = undefined;
      }
      continue;
    }

    // Added lines in unified diffs start with '+', ignore the file header '+++'
    if (raw.startsWith("+") && !raw.startsWith("+++")) {
      const ln = typeof newLine === 'number' ? newLine : 0;
      analyzeAddedLine(raw.slice(1), currentFile, ln, result);
      if (typeof newLine === 'number') newLine++;
      continue;
    }

    // Context lines (' ') advance newLine; removed lines ('-') do not.
    if (raw.startsWith(" ")) {
      if (typeof newLine === 'number') newLine++;
      continue;
    }
  }
}

function summarize(result: MutableResult) {
  const counts = {
    critical: result.criticalIssues.length,
    high: result.highIssues.length,
    medium: result.mediumIssues.length,
    low: result.lowIssues.length,
  };
  const highestRank = Math.max(
    counts.critical ? 4 : 0,
    counts.high ? 3 : 0,
    counts.medium ? 2 : 0,
    counts.low ? 1 : 0
  );

  const hasIssues = highestRank > 0;

  result.severity = rankToSeverity(highestRank);
  if (!hasIssues) {
    result.status = "no_action_needed";
    result.actionRequired = false;
    result.requiresAction = false;
    result.actionNeeded = false;
    result.reportOnly = false;
    result.summary = "No actionable issues detected in provided diffs.";
  } else {
    result.status = highestRank >= 2 ? "action_required" : "report_only";
    const needAction = highestRank >= 2; // medium or above => action required
    result.actionRequired = needAction;
    result.requiresAction = needAction;
    result.actionNeeded = needAction;
    result.reportOnly = !needAction;
    result.summary = `Issues detected — critical:${counts.critical}, high:${counts.high}, medium:${counts.medium}, low:${counts.low}.`;
  }

  // Optional, simple findings/suggestions aggregation
  result.findings = [
    ...(result.criticalIssues.map(i => i.message)),
    ...(result.highIssues.map(i => i.message)),
    ...(result.mediumIssues.map(i => i.message)),
    ...(result.lowIssues.map(i => i.message)),
  ];
  result.suggestions = [
    ...(result.criticalIssues.flatMap(i => i.suggestion ? [i.suggestion] : [])),
    ...(result.highIssues.flatMap(i => i.suggestion ? [i.suggestion] : [])),
    ...(result.mediumIssues.flatMap(i => i.suggestion ? [i.suggestion] : [])),
    ...(result.lowIssues.flatMap(i => i.suggestion ? [i.suggestion] : [])),
  ];
}

export const CodeReviewAgent = {
  async execute(args: ExecuteArgs): Promise<CodeReviewResult> {
    const diffs = normalizeDiffs(args.diffs);

    const base: MutableResult = {
      criticalIssues: [],
      highIssues: [],
      mediumIssues: [],
      lowIssues: [],
      status: "ok",
      severity: "none",
      files: [],
      findings: [],
      suggestions: [],
      metadata: { source: "CodeReviewAgent", version: 1 },
      summary: undefined,
    };

    if (diffs.length === 0 || diffs.every(d => !d || d.trim().length === 0)) {
      // No diffs provided — explicitly return "no_action_needed"
      base.status = "no_action_needed";
      base.actionRequired = false;
      base.requiresAction = false;
      base.actionNeeded = false;
      base.reportOnly = false;
      base.summary = "No diffs provided.";
      return base;
    }

    for (const diff of diffs) {
      parseUnifiedDiff(diff, base);
    }

    summarize(base);
    return base;
  },
} as const;
