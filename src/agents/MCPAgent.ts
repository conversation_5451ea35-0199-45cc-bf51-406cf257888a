import { generateText, streamText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { tool } from 'ai';
import { z } from 'zod';
import { config } from '../utils/config';
import {
  getAllMCPTools,
  getMCPToolsByTags,
  getMCPStatus,
  initializeMCPServers,
  connectMCPServer,
  disconnectMCPServer,
  performMCPHealthCheck,
  invalidateMCPToolCache
} from '../mcp';

// Initialize OpenAI client
const openai = createOpenAI({
  apiKey: config.openai.apiKey,
});

/**
 * MCP management tools for the agent
 */
const mcpStatusTool = tool({
  name: 'get_mcp_status',
  description: 'Get the current status of MCP servers and integration',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const status = getMCPStatus();
      return { success: true, status };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

const mcpToolsTool = tool({
  name: 'get_mcp_tools',
  description: 'Get all available MCP tools or tools filtered by tags',
  inputSchema: z.object({
    tags: z.array(z.string()).nullable().optional().describe('Optional tags to filter tools by')
  }),
  execute: async ({ tags }) => {
    try {
      const result = tags && tags.length > 0
        ? await getMCPToolsByTags(tags)
        : await getAllMCPTools();

      const toolsList = result.metadata.map(meta => ({
        name: meta.toolName,
        description: meta.description,
        server: meta.serverName,
        serverId: meta.serverId,
        tags: meta.tags
      }));

      const payload: any = {
        success: true,
        totalTools: result.tools.length,
        tools: toolsList,
      };
      if (tags && tags.length > 0) payload.filteredByTags = tags;
      return payload;
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

const mcpConnectServerTool = tool({
  name: 'connect_mcp_server',
  description: 'Connect to a specific MCP server by ID',
  inputSchema: z.object({
    serverId: z.string().describe('The ID of the MCP server to connect to')
  }),
  execute: async ({ serverId }) => {
    try {
      await connectMCPServer(serverId);
      return { success: true, serverId };
    } catch (error) {
      return { success: false, serverId, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

const mcpDisconnectServerTool = tool({
  name: 'disconnect_mcp_server',
  description: 'Disconnect from a specific MCP server by ID',
  inputSchema: z.object({
    serverId: z.string().describe('The ID of the MCP server to disconnect from')
  }),
  execute: async ({ serverId }) => {
    try {
      await disconnectMCPServer(serverId);
      return { success: true, serverId };
    } catch (error) {
      return { success: false, serverId, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

const mcpHealthCheckTool = tool({
  name: 'mcp_health_check',
  description: 'Perform a health check on all connected MCP servers',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const results = await performMCPHealthCheck();
      const healthSummary = Object.entries(results).map(([serverId, isHealthy]) => ({
        serverId,
        status: isHealthy ? 'healthy' : 'unhealthy'
      }));

      return {
        success: true,
        healthCheck: 'completed',
        results: healthSummary,
        totalServers: healthSummary.length,
        healthyServers: healthSummary.filter(s => s.status === 'healthy').length
      };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

const mcpInvalidateCacheTool = tool({
  name: 'invalidate_mcp_cache',
  description: 'Invalidate the MCP tool cache for better tool discovery',
  inputSchema: z.object({
    serverId: z.string().optional().describe('Optional server ID to invalidate cache for specific server only')
  }),
  execute: async ({ serverId }) => {
    try {
      invalidateMCPToolCache(serverId);
      return { success: true, scope: serverId ? 'server' : 'all', serverId };
    } catch (error) {
      return { success: false, serverId, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
});

/**
 * MCP Agent - Specialized agent for managing MCP servers and utilizing MCP tools
 */
class MCPAgent {
  private tools = [
    mcpStatusTool,
    mcpToolsTool,
    mcpConnectServerTool,
    mcpDisconnectServerTool,
    mcpHealthCheckTool,
    mcpInvalidateCacheTool
  ];

  private dynamicMCPTools: any[] = [];
  private lastToolsUpdate = 0;
  private toolsUpdateInterval = 30000; // 30 seconds

  constructor() {
    this.refreshDynamicTools();
  }

  /**
   * Get system instructions for the MCP agent
   */
  private getSystemInstructions(): string {
    return `You are the MCP (Model Context Protocol) Agent, specialized in managing MCP server connections and utilizing MCP tools.

**Your primary responsibilities:**

1. **MCP Server Management**
   - Monitor and manage MCP server connections
   - Help users connect/disconnect from MCP servers
   - Perform health checks and diagnostics
   - Provide status reports on MCP integration

2. **Tool Discovery and Usage**
   - Discover available MCP tools from connected servers
   - Help users understand what tools are available
   - Utilize MCP tools to accomplish user requests
   - Filter tools by tags and categories

3. **Problem Resolution**
   - Diagnose MCP connection issues
   - Cache invalidation when tools aren't updating
   - Server reconnection when needed
   - Provide clear error explanations

**Available MCP Management Tools:**
- get_mcp_status: Get current MCP integration status
- get_mcp_tools: List all available MCP tools or filter by tags
- connect_mcp_server: Connect to specific MCP servers
- disconnect_mcp_server: Disconnect from MCP servers
- mcp_health_check: Check health of all connected servers
- invalidate_mcp_cache: Refresh tool cache for better discovery

**Best Practices:**
- Always check MCP status first when troubleshooting
- Use health checks to diagnose connection issues
- Invalidate cache if tools seem outdated
- Provide clear explanations of MCP capabilities
- Be proactive about server management

**Communication Style:**
- Be informative and helpful about MCP capabilities
- Explain technical concepts clearly
- Provide step-by-step guidance for MCP setup
- Use appropriate technical terminology
- Always acknowledge limitations and suggest alternatives`;
  }

  /**
   * Refresh dynamic MCP tools from connected servers
   */
  private async refreshDynamicTools(): Promise<void> {
    const now = Date.now();
    if (now - this.lastToolsUpdate < this.toolsUpdateInterval) {
      return; // Don't update too frequently
    }

    try {
      const result = await getAllMCPTools();
      this.dynamicMCPTools = result.tools;
      this.lastToolsUpdate = now;
      console.log(`🔄 Refreshed ${this.dynamicMCPTools.length} dynamic MCP tools`);
    } catch (error) {
      console.warn('⚠️ Failed to refresh dynamic MCP tools:', error);
    }
  }

  /**
   * Get all available tools (management + dynamic MCP tools)
   */
  private async getAllTools(): Promise<any[]> {
    await this.refreshDynamicTools();
    return [...this.tools, ...this.dynamicMCPTools];
  }

  /**
   * Execute MCP agent with non-streaming response
   */
  async execute(messages: any[], model: string = config.openai.defaultModel): Promise<string> {
    try {
      // Initialize MCP servers if needed
      const status = getMCPStatus();
      if (!status.initialized) {
        console.log('🔧 Initializing MCP servers for agent execution...');
        await initializeMCPServers();
      }

      // Get all available tools
      const allTools = await this.getAllTools();

      console.log(`🤖 MCP Agent executing with ${allTools.length} tools (${this.tools.length} management + ${this.dynamicMCPTools.length} MCP tools)`);

      const result = await generateText({
        model: openai(model),
        system: this.getSystemInstructions(),
        messages,
        tools: allTools.reduce((acc, tool) => {
          acc[tool.name || tool.toolName] = tool;
          return acc;
        }, {}),
      });

      return result.text;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ MCP Agent execution failed:', error);
      return `MCP Agent error: ${errorMessage}`;
    }
  }

  /**
   * Execute MCP agent with streaming response
   */
  async executeStream(
    messages: any[],
    model: string = config.openai.defaultModel,
    onChunk: (chunk: any) => void
  ): Promise<void> {
    try {
      // Initialize MCP servers if needed
      const status = getMCPStatus();
      if (!status.initialized) {
        console.log('🔧 Initializing MCP servers for agent execution...');
        await initializeMCPServers();
      }

      // Get all available tools
      const allTools = await this.getAllTools();

      console.log(`🤖 MCP Agent streaming with ${allTools.length} tools (${this.tools.length} management + ${this.dynamicMCPTools.length} MCP tools)`);

      const result = await streamText({
        model: openai(model),
        system: this.getSystemInstructions(),
        messages,
        tools: allTools.reduce((acc, tool) => {
          acc[tool.name || tool.toolName] = tool;
          return acc;
        }, {}),
      });

      // Stream the response
      for await (const chunk of result.textStream) {
        onChunk({
          type: 'text',
          content: chunk,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ MCP Agent streaming execution failed:', error);
      onChunk({
        type: 'error',
        content: `MCP Agent error: ${errorMessage}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get agent information
   */
  getInfo() {
    return {
      name: 'MCP Agent',
      description: 'Specialized agent for managing MCP servers and utilizing MCP tools',
      capabilities: [
        'MCP server management',
        'Tool discovery and usage',
        'Connection diagnostics',
        'Health monitoring',
        'Cache management'
      ],
      managementTools: this.tools.length,
      dynamicTools: this.dynamicMCPTools.length
    };
  }
}

// Create singleton instance
export const mcpAgent = new MCPAgent();

// Export for backward compatibility
export default mcpAgent;
