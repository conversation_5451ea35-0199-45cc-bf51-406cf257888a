/**
 * File Analysis Worker - Vercel AI SDK Implementation
 * Specializes in analyzing individual files as part of larger code analysis tasks
 */

import { generateText, stepCountIs } from 'ai';
import { openai } from '@ai-sdk/openai';
import { fileOperationTools } from '../../tools/fileOperations';

export interface FileAnalysisOptions {
  stream?: boolean;
  temperature?: number;
  context?: any;
  analysisDepth?: 'surface' | 'detailed' | 'comprehensive';
  focusAreas?: string[];
}

/**
 * Execute file analysis with Vercel AI SDK
 */
export async function executeFileAnalysisWorker(
  fileContent: string,
  options: FileAnalysisOptions = {}
): Promise<any> {
  const model = openai('gpt-5'); // Use full model for thorough file analysis

  const analysisDepth = options.analysisDepth || 'detailed';
  const focusAreas = options.focusAreas || [];

  // Parse file content if it's JSON with file info
  let content = fileContent;
  let instruction = '';
  let filename = 'unknown';
  let fileType = 'text';

  try {
    const parsed = JSON.parse(fileContent);
    if (parsed.file && parsed.instruction) {
      instruction = parsed.instruction;
      filename = parsed.file.path || parsed.file.name || 'unknown';
      fileType = parsed.file.type || 'text';
      content = parsed.file.content || JSON.stringify(parsed.file);
    }
  } catch {
    // Content is plain text file content
  }

  const systemPrompt = `You are a specialized file analysis worker in Dante's task orchestration system.

Your expertise includes:
1. **Code Analysis**: Structure, patterns, dependencies, quality assessment
2. **Documentation Analysis**: Completeness, accuracy, organization
3. **Configuration Analysis**: Settings, security, best practices
4. **Data File Analysis**: Format, structure, integrity, patterns
5. **Text Analysis**: Content, organization, relationships

Always begin by calling get_working_directory to confirm you are operating inside the project path provided in context. If it differs, call set_working_directory before reading or referencing related files.

Analysis Depth: ${analysisDepth}
- **Surface**: High-level overview, main components, obvious issues
- **Detailed**: Thorough analysis with specific findings and recommendations
- **Comprehensive**: Deep dive with security, performance, and architecture insights

File Information:
- Filename: ${filename}
- Type: ${fileType}
- Size: ${content.length} characters

${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}

Provide structured analysis with:
1. File overview and purpose
2. Key findings and observations
3. Code quality and best practices (for code files)
4. Potential issues or improvements
5. Relationships to other files/components
6. Security considerations (if applicable)
7. Performance implications (if applicable)

Format your response as structured markdown for easy synthesis with other file analyses.`;

  const prompt = instruction ?
    `${instruction}\n\n--- FILE CONTENT ---\nFile: ${filename}\n${content}` :
    `Analyze this file:\n\nFile: ${filename}\n${content}`;

  const config = {
    model,
    system: systemPrompt,
    prompt,
    tools: {
      // Include file operation tools for reading related files if needed
      read_file: fileOperationTools.find(tool => tool.name === 'read_file'),
      list_directory: fileOperationTools.find(tool => tool.name === 'list_directory'),
      get_working_directory: fileOperationTools.find(tool => tool.name === 'get_working_directory'),
      set_working_directory: fileOperationTools.find(tool => tool.name === 'set_working_directory'),
    },
    stopWhen: stepCountIs(5) // Allow some tool usage for comprehensive analysis
  };

  try {
    const result = await generateText(config);

    // Extract structured information from the analysis
    const analysis = result.text;

    return {
      success: true,
      analysis,
      fileInfo: {
        filename,
        type: fileType,
        size: content.length,
        analysisDepth
      },
      metadata: {
        workerType: 'file_analysis',
        model: 'gpt-5',
        tokensUsed: result.usage?.totalTokens || 0,
        processingTime: Date.now(),
        focusAreas,
        structured: true
      },
      keyFindings: extractKeyFindings(analysis),
      recommendations: extractRecommendations(analysis)
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown analysis error',
      fileInfo: {
        filename,
        type: fileType,
        size: content.length,
        analysisDepth
      },
      metadata: {
        workerType: 'file_analysis',
        model: 'gpt-5',
        processingTime: Date.now(),
        focusAreas
      }
    };
  }
}

/**
 * Extract key findings from analysis text
 */
function extractKeyFindings(analysis: string): string[] {
  const findings: string[] = [];

  // Look for common patterns in analysis text
  const lines = analysis.split('\n');
  let inFindingsSection = false;

  for (const line of lines) {
    const trimmed = line.trim();

    // Detect findings sections
    if (trimmed.toLowerCase().includes('key finding') ||
        trimmed.toLowerCase().includes('observation') ||
        trimmed.toLowerCase().includes('important')) {
      inFindingsSection = true;
      continue;
    }

    // Extract bullet points or numbered items in findings sections
    if (inFindingsSection && (trimmed.startsWith('- ') || trimmed.startsWith('* ') || /^\d+\./.test(trimmed))) {
      findings.push(trimmed.replace(/^[-*\d.\s]+/, '').trim());
    }

    // Stop when we hit a new major section
    if (trimmed.startsWith('##') && inFindingsSection) {
      inFindingsSection = false;
    }
  }

  return findings.slice(0, 5); // Limit to top 5 findings
}

/**
 * Extract recommendations from analysis text
 */
function extractRecommendations(analysis: string): string[] {
  const recommendations: string[] = [];

  const lines = analysis.split('\n');
  let inRecommendationsSection = false;

  for (const line of lines) {
    const trimmed = line.trim();

    // Detect recommendations sections
    if (trimmed.toLowerCase().includes('recommend') ||
        trimmed.toLowerCase().includes('suggest') ||
        trimmed.toLowerCase().includes('improve')) {
      inRecommendationsSection = true;
      continue;
    }

    // Extract bullet points or numbered items in recommendations sections
    if (inRecommendationsSection && (trimmed.startsWith('- ') || trimmed.startsWith('* ') || /^\d+\./.test(trimmed))) {
      recommendations.push(trimmed.replace(/^[-*\d.\s]+/, '').trim());
    }

    // Stop when we hit a new major section
    if (trimmed.startsWith('##') && inRecommendationsSection) {
      inRecommendationsSection = false;
    }
  }

  return recommendations.slice(0, 5); // Limit to top 5 recommendations
}

// Legacy compatibility wrapper
export const fileAnalysisWorker = {
  name: 'File Analysis Worker',
  model: 'gpt-5',

  async execute(fileContent: string, options: FileAnalysisOptions = {}) {
    return await executeFileAnalysisWorker(fileContent, options);
  }
};
