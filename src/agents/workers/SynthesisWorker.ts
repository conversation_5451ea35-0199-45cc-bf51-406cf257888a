/**
 * Synthesis Worker - Vercel AI SDK Implementation
 * Aggregates and synthesizes results from multiple workers into coherent responses
 */

import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { getThinkingConfigForTask } from '../../utils/geminiClient';
import { z } from 'zod';

export interface SynthesisOptions {
  stream?: boolean;
  temperature?: number;
  context?: any;
  synthesisType?: 'summary' | 'detailed' | 'comprehensive' | 'executive';
  prioritizeInsights?: boolean;
  includeMetadata?: boolean;
}

export interface WorkerResultForSynthesis {
  chunkId: string;
  result: any;
  tokensUsed: number;
  processingTime: number;
  workerType?: string;
  success?: boolean;
}

/**
 * Execute result synthesis with Vercel AI SDK
 */
export async function executeSynthesisWorker(
  synthesisInput: string,
  options: SynthesisOptions = {}
): Promise<any> {
  // Use Gemini Pro for synthesis due to its superior summarization capabilities
  const model = google('models/gemini-2.5-pro');
  const think = getThinkingConfigForTask('analysis', 'gemini-2.5-pro', synthesisInput);
  const tb = (think as any)?.thinking_budget || 2048;

  const synthesisType = options.synthesisType || 'detailed';
  const prioritizeInsights = options.prioritizeInsights ?? true;
  const includeMetadata = options.includeMetadata ?? true;

  // Parse synthesis input
  let parsedInput: any;
  try {
    parsedInput = JSON.parse(synthesisInput);
  } catch {
    // If input is not JSON, treat as plain text synthesis task
    parsedInput = { originalTask: synthesisInput, workerResults: [] };
  }

  const { originalTask, workerResults, errors, metadata } = parsedInput;

  const systemPrompt = `You are Dante's Synthesis Specialist, responsible for combining multiple worker results into coherent, comprehensive responses.

Your expertise includes:
1. **Information Integration**: Combining related findings from multiple sources
2. **Pattern Recognition**: Identifying common themes, conflicts, and relationships
3. **Insight Generation**: Deriving higher-level insights from detailed analyses
4. **Quality Synthesis**: Creating coherent narratives from fragmented information
5. **Executive Summarization**: Distilling key information for decision-making

Synthesis Type: ${synthesisType}
- **Summary**: Concise overview of main findings and conclusions
- **Detailed**: Comprehensive synthesis with supporting evidence and analysis
- **Comprehensive**: Full synthesis including methodology, findings, insights, and recommendations
- **Executive**: High-level strategic overview focused on decisions and actions

Synthesis Guidelines:
${prioritizeInsights ? '- Prioritize unique insights and non-obvious patterns over basic information summary' : ''}
- Identify and resolve conflicts or inconsistencies between worker results
- Maintain traceability to source worker results when important
- Structure information logically with clear headings and sections
- Provide actionable conclusions and next steps
- Highlight critical findings that require attention
${includeMetadata ? '- Include relevant metadata about the synthesis process' : ''}

Original Task: ${originalTask || 'Multi-part analysis task'}
Worker Results Available: ${workerResults?.length || 0}
Errors Encountered: ${errors?.length || 0}

Create a unified, professional response that fully addresses the original task using insights from all worker results.`;

  // Prepare synthesis content
  const synthesisContent = prepareSynthesisContent(parsedInput, synthesisType);

  // Helper: render a readable text response from structured synthesis
  const renderStructured = (s: any): string => {
    if (!s || typeof s !== 'object') return '';
    const lines: string[] = [];
    if (s.summary) {
      lines.push(String(s.summary).trim());
      lines.push('');
    }
    const renderList = (title: string, arr?: any[]) => {
      if (Array.isArray(arr) && arr.length > 0) {
        lines.push(`${title}:`);
        for (const item of arr) {
          if (typeof item === 'string') lines.push(`- ${item}`);
          else if (item && typeof item === 'object') {
            if (item.action) {
              const pri = item.priority ? ` [${item.priority}]` : '';
              const own = item.owner ? ` (owner: ${item.owner})` : '';
              lines.push(`- ${item.action}${pri}${own}`);
            } else if (item.title && item.content) {
              lines.push(`\n## ${item.title}`);
              lines.push(String(item.content));
            } else {
              lines.push(`- ${JSON.stringify(item)}`);
            }
          }
        }
        lines.push('');
      }
    };
    renderList('Key Findings', s.keyFindings);
    renderList('Insights', s.insights);
    renderList('Recommendations', s.recommendations);
    renderList('Next Actions', s.nextActions);
    return lines.join('\n').trim();
  };

  // Structured output tool to produce consistent, typed synthesis
  const answerTool = tool({
    description: 'Provide the final structured synthesis output.',
    inputSchema: z.object({
      summary: z.string().describe('One-paragraph overall summary'),
      keyFindings: z.array(z.string()).optional().describe('Top concrete findings'),
      insights: z.array(z.string()).optional().describe('Notable non-obvious insights'),
      recommendations: z.array(z.string()).optional().describe('Actionable recommendations'),
      nextActions: z
        .array(
          z.object({
            action: z.string(),
            priority: z.enum(['low', 'medium', 'high']).optional(),
            owner: z.string().optional(),
          })
        )
        .optional()
        .describe('Clear next steps with optional priority and owner'),
      sections: z
        .array(z.object({ title: z.string(), content: z.string() }))
        .optional()
        .describe('Optional structured sections of the synthesis'),
    }) as any,
    // no execute: invoking the tool will terminate generation with structured output
  } as any);

  const config = {
    model,
    system: systemPrompt +
      '\n\nINSTRUCTION: Use the answer tool to output the final synthesis in a structured format.',
    prompt: synthesisContent,
    tools: { answer: answerTool },
    toolChoice: 'required' as any,
    providerOptions: {
      google: {
        thinkingConfig: { thinkingBudget: tb, includeThoughts: true }
      },
      openai: {
        maxTokens: tb,
        reasoningSummary: 'detailed',
        reasoningEffort: 'low',
      }
    },
  };

  try {
    const result = await generateText(config);

    // Extract structured synthesis from tool call
    let structured: any = undefined;
    try {
      const call = (result as any).toolCalls?.find((c: any) => (c.name || c.toolName) === 'answer')
        || (result as any).toolCalls?.[0];
      structured = call?.args ?? call?.input ?? undefined;
    } catch {}

    const rendered = (result.text && String(result.text).trim().length > 0)
      ? result.text
      : renderStructured(structured);
    try {
      const rlen = (rendered || '').length;
      const usedStructured = (!result.text || String(result.text).trim().length === 0) && structured;
      console.log(`🧪 [SynthesisWorker] Rendered synthesis | fromStructured=${usedStructured} | length=${rlen}`);
    } catch {}

    const synthesisResult = {
      success: true,
      synthesizedResponse: rendered,
      steps: (result as any).steps,
      structuredSynthesis: structured,
      rawToolCalls: (result as any).toolCalls,
      synthesisMetadata: {
        type: synthesisType,
        model: 'gemini-2.5-pro',
        tokensUsed: result.usage?.totalTokens || 0,
        processingTime: Date.now(),
        workerResultsProcessed: workerResults?.length || 0,
        errorsHandled: errors?.length || 0,
        originalTaskComplexity: estimateTaskComplexity(originalTask),
        synthesisQuality: assessSynthesisQuality(
          (structured && typeof structured.summary === 'string' ? structured.summary : result.text) || '',
          workerResults
        ),
        ...(includeMetadata && metadata ? { originalMetadata: metadata } : {})
      },
      workerSummary: {
        totalWorkers: workerResults?.length || 0,
        successfulWorkers: workerResults?.filter((r: any) => r.result && !r.error).length || 0,
        failedWorkers: errors?.length || 0,
        totalTokensUsed: metadata?.totalTokensUsed || 0,
        totalProcessingTime: metadata?.totalProcessingTime || 0
      }
    };

    return synthesisResult;

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown synthesis error',
      synthesisMetadata: {
        type: synthesisType,
        model: 'gemini-2.5-pro',
        processingTime: Date.now(),
        workerResultsProcessed: workerResults?.length || 0,
        errorsHandled: errors?.length || 0
      },
      fallbackResponse: createFallbackSynthesis(parsedInput),
      originalTask
    };
  }
}

/**
 * Prepare synthesis content from parsed input
 */
function prepareSynthesisContent(parsedInput: any, synthesisType: string): string {
  const { originalTask, workerResults, errors } = parsedInput;

  let content = `SYNTHESIS REQUEST\n\nOriginal Task: ${originalTask || 'Multi-part analysis'}\n\n`;

  // Add worker results
  if (workerResults && workerResults.length > 0) {
    content += `WORKER RESULTS (${workerResults.length} total):\n\n`;

    workerResults.forEach((result: any, index: number) => {
      content += `--- Worker Result ${index + 1} (${result.chunkId || `chunk_${index}`}) ---\n`;

      if (result.result) {
        if (typeof result.result === 'object') {
          // Handle structured results from file analysis worker
          if (result.result.analysis) {
            content += result.result.analysis;
          } else if (result.result.content) {
            content += result.result.content;
          } else {
            content += JSON.stringify(result.result, null, 2);
          }
        } else {
          content += result.result;
        }
      } else {
        content += '[No result content available]';
      }

      content += `\n\nMetadata: ${result.tokensUsed || 0} tokens, ${result.processingTime || 0}ms\n\n`;
    });
  }

  // Add error information if any
  if (errors && errors.length > 0) {
    content += `ERRORS ENCOUNTERED (${errors.length} total):\n\n`;
    errors.forEach((error: any, index: number) => {
      content += `Error ${index + 1} (${error.chunkId}): ${error.error}\n`;
    });
    content += '\n';
  }

  // Add synthesis instructions based on type
  switch (synthesisType) {
    case 'summary':
      content += 'SYNTHESIS INSTRUCTION: Create a concise summary highlighting the most important findings and conclusions.';
      break;
    case 'executive':
      content += 'SYNTHESIS INSTRUCTION: Create an executive summary focused on strategic insights, decisions needed, and recommended actions.';
      break;
    case 'comprehensive':
      content += 'SYNTHESIS INSTRUCTION: Create a comprehensive synthesis including detailed findings, analysis, insights, and actionable recommendations.';
      break;
    default: // detailed
      content += 'SYNTHESIS INSTRUCTION: Create a detailed synthesis that thoroughly addresses the original task using all available information.';
  }

  return content;
}

/**
 * Estimate task complexity for metadata
 */
function estimateTaskComplexity(originalTask: string): 'low' | 'medium' | 'high' | 'very_high' {
  if (!originalTask) return 'medium';

  const taskLength = originalTask.length;
  const complexityIndicators = [
    'comprehensive', 'analyze entire', 'full audit', 'complete review',
    'architecture', 'security', 'performance', 'refactor', 'migration'
  ];

  const hasComplexityIndicators = complexityIndicators.some(indicator =>
    originalTask.toLowerCase().includes(indicator)
  );

  if (taskLength > 1000 || hasComplexityIndicators) return 'very_high';
  if (taskLength > 500) return 'high';
  if (taskLength > 200) return 'medium';
  return 'low';
}

/**
 * Assess synthesis quality for metadata
 */
function assessSynthesisQuality(synthesisText: string, workerResults: any[]): {
  length: number;
  structure: 'poor' | 'adequate' | 'good' | 'excellent';
  completeness: number; // 0-1 score
} {
  const length = synthesisText.length;

  // Assess structure based on headings, sections, formatting
  const hasHeadings = /#{1,6}\s/.test(synthesisText);
  const hasBulletPoints = /^[-*+]\s/m.test(synthesisText);
  const hasNumberedLists = /^\d+\.\s/m.test(synthesisText);
  const hasConclusion = /conclusion|summary|recommendation/i.test(synthesisText);

  let structure: 'poor' | 'adequate' | 'good' | 'excellent' = 'poor';
  const structureScore = (hasHeadings ? 1 : 0) + (hasBulletPoints ? 1 : 0) +
                        (hasNumberedLists ? 1 : 0) + (hasConclusion ? 1 : 0);

  if (structureScore >= 3) structure = 'excellent';
  else if (structureScore >= 2) structure = 'good';
  else if (structureScore >= 1) structure = 'adequate';

  // Estimate completeness based on worker result coverage
  const completeness = Math.min(1.0, length / (workerResults.length * 200)); // Rough heuristic

  return { length, structure, completeness };
}

/**
 * Create fallback synthesis when main synthesis fails
 */
function createFallbackSynthesis(parsedInput: any): string {
  const { originalTask, workerResults, errors } = parsedInput;

  let fallback = `# Task Completion Summary\n\n`;
  fallback += `**Original Task**: ${originalTask || 'Multi-part analysis'}\n\n`;

  if (workerResults && workerResults.length > 0) {
    fallback += `## Results Processed\n\n`;
    fallback += `Successfully processed ${workerResults.filter((r: any) => r.result).length} out of ${workerResults.length} work chunks.\n\n`;

    // Include key results if available
    workerResults.slice(0, 3).forEach((result: any, index: number) => {
      if (result.result) {
        fallback += `### Result ${index + 1}\n`;
        const content = typeof result.result === 'object' ?
          JSON.stringify(result.result).substring(0, 300) + '...' :
          result.result.substring(0, 300) + '...';
        fallback += content + '\n\n';
      }
    });
  }

  if (errors && errors.length > 0) {
    fallback += `## Issues Encountered\n\n`;
    errors.forEach((error: any, index: number) => {
      fallback += `${index + 1}. ${error.error}\n`;
    });
  }

  fallback += `\n*Note: This is a fallback summary due to synthesis processing issues. Individual worker results may contain more detailed information.*`;

  return fallback;
}

// Legacy compatibility wrapper
export const synthesisWorker = {
  name: 'Synthesis Worker',
  model: 'gemini-2.5-pro',

  async execute(synthesisInput: string, options: SynthesisOptions = {}) {
    return await executeSynthesisWorker(synthesisInput, options);
  }
};
