// Workers migrated to Vercel AI SDK
export { fileAnalysisWorker } from './FileAnalysisWorker';
export { synthesisWorker } from './SynthesisWorker';
export { chunkProcessorWorker } from './ChunkProcessorWorker';

// Legacy type definitions for compatibility
export interface FileAnalysisInput {
  files: string[];
  analysisType: string;
}

export interface SynthesisInput {
  data: any[];
  format: string;
}

export interface ChunkProcessorInput {
  chunks: any[];
  operation: string;
}