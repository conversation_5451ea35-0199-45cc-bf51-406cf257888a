/**
 * Chunk Processor Worker - Vercel AI SDK Implementation
 * Handles processing of individual chunks in parallel task orchestration
 */

import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';

export interface ChunkProcessorOptions {
  stream?: boolean;
  temperature?: number;
  context?: any;
}

/**
 * Execute chunk processing with Vercel AI SDK
 */
export async function executeChunkProcessorWorker(
  chunkContent: string,
  options: ChunkProcessorOptions = {}
): Promise<any> {
  const model = openai('gpt-5-mini'); // Use mini for efficiency in parallel processing

  // Parse chunk content if it's JSON
  let content = chunkContent;
  let instruction = '';
  let metadata = options.context;

  try {
    const parsed = JSON.parse(chunkContent);
    if (parsed.instruction && parsed.content) {
      instruction = parsed.instruction;
      content = parsed.content;
    } else if (parsed.file && parsed.instruction) {
      instruction = parsed.instruction;
      content = JSON.stringify(parsed.file);
      metadata = { ...metadata, filename: parsed.file.path || parsed.file.name };
    }
  } catch {
    // Content is plain text, use as-is
  }

  const systemPrompt = `You are a specialized chunk processor worker in Dante's task orchestration system.

Your role is to:
1. Process individual chunks of larger tasks efficiently and accurately
2. Maintain consistency with the overall task objective
3. Focus on your specific chunk while being aware of the larger context
4. Provide detailed, structured output for synthesis
5. Handle various content types (text, code, data, files)

Chunk Processing Guidelines:
- Process thoroughly but efficiently
- Maintain original context and meaning
- Structure output for easy synthesis
- Include relevant metadata and insights
- Focus on accuracy over speed
- Identify key findings and important details

Content Type: ${metadata?.fileType || metadata?.chunkType || 'general'}
${metadata?.filename ? `Filename: ${metadata.filename}` : ''}

Provide structured, actionable output that contributes to the overall task completion.`;

  const prompt = instruction ?
    `${instruction}\n\n--- CHUNK CONTENT ---\n${content}` :
    content;

  const config = {
    model,
    system: systemPrompt,
    prompt,
  };

  try {
    const result = await generateText(config);

    return {
      success: true,
      content: result.text,
      metadata: {
        chunkType: metadata?.chunkType || 'processed',
        filename: metadata?.filename,
        model: 'gpt-5-mini',
        tokensUsed: result.usage?.totalTokens || 0,
        processingTime: Date.now(),
        structured: true
      },
      originalChunk: {
        size: content.length,
        type: typeof content,
        hasInstruction: !!instruction
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown processing error',
      metadata: {
        chunkType: 'failed',
        filename: metadata?.filename,
        model: 'gpt-5-mini',
        processingTime: Date.now()
      },
      originalChunk: {
        size: content.length,
        type: typeof content,
        hasInstruction: !!instruction
      }
    };
  }
}

// Legacy compatibility wrapper
export const chunkProcessorWorker = {
  name: 'Chunk Processor Worker',
  model: 'gpt-5-mini',

  async execute(chunkContent: string, options: ChunkProcessorOptions = {}) {
    return await executeChunkProcessorWorker(chunkContent, options);
  }
};
