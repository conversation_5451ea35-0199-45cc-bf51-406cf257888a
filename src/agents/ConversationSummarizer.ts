import { generateText } from 'ai';
import { getAIProvider } from '../ai/providers';
import { FALLBACK_AGENT_ERROR_SUMMARY } from '../constants/errors';
import { rateLimiter } from '../../server/utils/rateLimiter';
import { withRetries } from '../../server/utils/retry';
export class ConversationSummarizer {
  private ollamaProvider: ReturnType<typeof getAIProvider>;

  constructor() {
    this.ollamaProvider = getAIProvider('ollama');
  }

  async summarize(conversationHistory: string[], modelName?: string): Promise<string> {
    const effectiveModelName = modelName || process.env.SUMMARIZER_MODEL || 'gpt-oss:20b';
    if (!conversationHistory || conversationHistory.length === 0) {
      return '';
    }
    const prompt = `Summarize the following conversation into a single, concise sentence. Focus on the main topic and key outcome:

${conversationHistory.join('\n')}

Summary:`;

    try {
      const limiterKey = `ollama:${effectiveModelName}`;

      const { text, usage } = await withRetries(async () => {
        await rateLimiter.consume(limiterKey);
        return generateText({
          model: this.ollamaProvider(effectiveModelName),
          prompt: prompt,
        });
      }, effectiveModelName)();

      if (usage.totalTokens) {
        rateLimiter.report(limiterKey, usage.totalTokens);
      }

      return text.trim();
    } catch (error: any) {
      console.error('Error summarizing conversation with Ollama:', error);
      if (error.message && error.message.includes('model not found')) {
        console.warn(
          `Warning: Model '${modelName}' not found in your Ollama instance. Please run 'ollama pull ${modelName}'`,
        );
        return FALLBACK_AGENT_ERROR_SUMMARY;
      }
      return FALLBACK_AGENT_ERROR_SUMMARY;
    }
  }
}
