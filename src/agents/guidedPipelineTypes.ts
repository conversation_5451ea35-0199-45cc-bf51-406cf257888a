export interface PlanStep {
  id: string;
  title: string;
  description: string;
  agent: string;
  input: string;
  dependsOn: string[];
  acceptanceCriteria: string;
}

export interface Plan {
  summary: string;
  steps: PlanStep[];
}

export interface CodegenStepResult {
  stepId: string;
  result: any;
  error: string | null;
  duration: number;
}

export interface GuidedPipelineResult {
  type: 'guided_pipeline_result';
  originalTask: string;
  plan: Plan;
  stepResults: CodegenStepResult[];
  synthesizedResult: any;
  processingTime: number;
  text: string;
  finalMessage: string;
  completed: boolean;
  remainingSteps: string[];
}
