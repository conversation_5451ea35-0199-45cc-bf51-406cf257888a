#!/usr/bin/env bun
/**
 * Database migration script for TokenVault
 * Handles both SQLite and PostgreSQL migrations
 */

import { existsSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { Database as BunDatabase } from 'bun:sqlite';
import { Pool } from 'pg';
import { config } from 'dotenv';
import { SCHEMA, INDEXES } from './schema';

// Load environment variables
config();

interface MigrationConfig {
  type: 'sqlite' | 'postgresql';
  connectionString?: string;
  filename?: string;
}

class TokenVaultMigration {
  private config: MigrationConfig;
  private db!: BunDatabase | Pool;

  constructor() {
    this.config = this.parseConfig();
    this.initializeDatabase();
  }

  private parseConfig(): MigrationConfig {
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction && process.env.DATABASE_URL) {
      return {
        type: 'postgresql',
        connectionString: process.env.DATABASE_URL
      };
    }

    return {
      type: 'sqlite',
      filename: process.env.TOKEN_DB_PATH || join(process.cwd(), 'data', 'tokens.db')
    };
  }

  private initializeDatabase(): void {
    if (this.config.type === 'sqlite') {
      const filename = this.config.filename!;

      // Ensure directory exists
      const dir = dirname(filename);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      }

      this.db = new BunDatabase(filename);
      console.log(`📦 Connected to SQLite database: ${filename}`);

      // Enable foreign keys and WAL mode for better performance
      this.db.exec('PRAGMA journal_mode = WAL');
      this.db.exec('PRAGMA foreign_keys = ON');
    } else {
      this.db = new Pool({
        connectionString: this.config.connectionString,
        max: 5
      });
      console.log('🐘 Connected to PostgreSQL database');
    }
  }

  async migrate(): Promise<void> {
    console.log('🚀 Starting database migration...\n');

    try {
      // Get statements from TypeScript schema
      const statements = this.getStatements();

      console.log(`📝 Found ${statements.length} SQL statements to execute\n`);

      for (let i = 0; i < statements.length; i++) {
        console.log(`\n[${i + 1}/${statements.length}] Processing statement...`);
        await this.executeStatement(statements[i]);
      }

      // Create initial provider configurations
      await this.seedProviders();

      console.log('\n✅ Migration completed successfully!');

      // Show statistics
      await this.showStatistics();
    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    } finally {
      await this.close();
    }
  }

  private getStatements(): string[] {
    const statements: string[] = [];

    // Add table creation statements
    const tableOrder = ['users', 'oauth_sessions', 'audit_log', 'conversation_logs', 'token_rotation_history', 'oauth_providers', 'failed_auth_attempts'] as const;

    for (const tableName of tableOrder) {
      const tableSchema = SCHEMA[tableName];
      if (tableSchema) {
        statements.push(tableSchema);
      }
    }

    // Add indexes
    statements.push(...INDEXES);

    console.log(`📊 Statement breakdown: ${tableOrder.length} tables, ${INDEXES.length} indexes`);

    return statements;
  }

  private async executeStatement(statement: string): Promise<void> {
    const tableName = this.extractTableName(statement);

    // Debug output
    console.log(`🔍 Statement preview: ${statement.substring(0, 100)}...`);
    if (tableName) {
      console.log(`🔄 Executing: CREATE TABLE ${tableName}...`);
    }

    try {
      if (this.db instanceof BunDatabase) {
        this.db.exec(statement);
      } else {
        await this.db.query(statement);
      }

      if (tableName) {
        console.log(`✅ ${this.getOperationType(statement)} table: ${tableName}`);
      } else if (statement.toUpperCase().includes('INDEX')) {
        const indexName = this.extractIndexName(statement);
        console.log(`✅ Created index: ${indexName}`);
      }
    } catch (error: any) {
      // Ignore "already exists" errors
      if (error.message?.includes('already exists') ||
          error.message?.includes('duplicate')) {
        if (tableName) {
          console.log(`⏭️  Table already exists: ${tableName}`);
        }
      } else {
        console.error(`❌ Failed to execute statement:`, error.message);
        throw error;
      }
    }
  }

  private extractTableName(statement: string): string | null {
    const match = statement.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
    return match ? match[1] : null;
  }

  private extractIndexName(statement: string): string | null {
    const match = statement.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
    return match ? match[1] : null;
  }

  private getOperationType(statement: string): string {
    if (statement.toUpperCase().includes('CREATE TABLE')) return 'Created';
    if (statement.toUpperCase().includes('ALTER TABLE')) return 'Altered';
    if (statement.toUpperCase().includes('DROP TABLE')) return 'Dropped';
    return 'Modified';
  }

  private async seedProviders(): Promise<void> {
    console.log('\n📝 Seeding OAuth providers...');

    const providers = [
      {
        name: 'google',
        clientId: process.env.GOOGLE_CLIENT_ID || 'configure-in-env',
        authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenUrl: 'https://oauth2.googleapis.com/token',
        userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
        revokeUrl: 'https://oauth2.googleapis.com/revoke',
        scopes: 'openid email profile',
        supportsPkce: true,
        supportsRefresh: true,
        tokenExpiry: 3600,
        refreshExpiry: 2592000 // 30 days
      },
      {
        name: 'microsoft',
        clientId: process.env.MICROSOFT_CLIENT_ID || 'configure-in-env',
        authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
        userInfoUrl: 'https://graph.microsoft.com/v1.0/me',
        revokeUrl: null,
        scopes: 'openid email profile offline_access',
        supportsPkce: true,
        supportsRefresh: true,
        tokenExpiry: 3600,
        refreshExpiry: null
      },
      {
        name: 'dropbox',
        clientId: process.env.DROPBOX_CLIENT_ID || 'configure-in-env',
        authUrl: 'https://www.dropbox.com/oauth2/authorize',
        tokenUrl: 'https://api.dropboxapi.com/oauth2/token',
        userInfoUrl: null,
        revokeUrl: 'https://api.dropboxapi.com/2/auth/token/revoke',
        scopes: 'account_info.read files.metadata.read',
        supportsPkce: true,
        supportsRefresh: true,
        tokenExpiry: 14400, // 4 hours
        refreshExpiry: null
      }
    ];

    for (const provider of providers) {
      try {
        if (this.db instanceof BunDatabase) {
          const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO oauth_providers (
              provider_name, client_id, auth_url, token_url, user_info_url,
              revoke_url, scopes, supports_pkce, supports_refresh,
              token_expiry_seconds, refresh_expiry_seconds
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);

          stmt.run(
            provider.name,
            provider.clientId,
            provider.authUrl,
            provider.tokenUrl,
            provider.userInfoUrl,
            provider.revokeUrl,
            provider.scopes,
            provider.supportsPkce ? 1 : 0,
            provider.supportsRefresh ? 1 : 0,
            provider.tokenExpiry,
            provider.refreshExpiry
          );
        } else {
          await this.db.query(`
            INSERT INTO oauth_providers (
              provider_name, client_id, auth_url, token_url, user_info_url,
              revoke_url, scopes, supports_pkce, supports_refresh,
              token_expiry_seconds, refresh_expiry_seconds
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (provider_name) DO UPDATE SET
              client_id = EXCLUDED.client_id,
              auth_url = EXCLUDED.auth_url,
              token_url = EXCLUDED.token_url,
              user_info_url = EXCLUDED.user_info_url,
              revoke_url = EXCLUDED.revoke_url,
              scopes = EXCLUDED.scopes,
              supports_pkce = EXCLUDED.supports_pkce,
              supports_refresh = EXCLUDED.supports_refresh,
              token_expiry_seconds = EXCLUDED.token_expiry_seconds,
              refresh_expiry_seconds = EXCLUDED.refresh_expiry_seconds,
              updated_at = CURRENT_TIMESTAMP
          `, [
            provider.name,
            provider.clientId,
            provider.authUrl,
            provider.tokenUrl,
            provider.userInfoUrl,
            provider.revokeUrl,
            provider.scopes,
            provider.supportsPkce,
            provider.supportsRefresh,
            provider.tokenExpiry,
            provider.refreshExpiry
          ]);
        }

        console.log(`✅ Configured provider: ${provider.name}`);
      } catch (error: any) {
        if (error.message?.includes('UNIQUE constraint failed')) {
          console.log(`⏭️  Provider already configured: ${provider.name}`);
        } else {
          console.error(`❌ Failed to seed provider ${provider.name}:`, error);
        }
      }
    }
  }

  private async showStatistics(): Promise<void> {
    console.log('\n📊 Database Statistics:');

    try {
      const tables = [
        'users',
        'oauth_sessions',
        'audit_log',
        'token_rotation_history',
        'oauth_providers',
        'failed_auth_attempts'
      ];

      for (const table of tables) {
        let count: number;

        if (this.db instanceof BunDatabase) {
          const result = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get() as any;
          count = result.count;
        } else {
          const result = await this.db.query(`SELECT COUNT(*) FROM ${table}`);
          count = parseInt(result.rows[0].count);
        }

        console.log(`  ${table}: ${count} records`);
      }
    } catch (error) {
      console.log('  (Statistics unavailable)');
    }
  }

  private async close(): Promise<void> {
    if (this.db instanceof BunDatabase) {
      this.db.close();
    } else {
      await this.db.end();
    }
  }

  async rollback(): Promise<void> {
    console.log('⚠️  Rolling back migration...\n');

    const tables = [
      'failed_auth_attempts',
      'oauth_providers',
      'token_rotation_history',
      'audit_log',
      'oauth_sessions',
      'users'
    ];

    for (const table of tables) {
      try {
        if (this.db instanceof BunDatabase) {
          this.db.exec(`DROP TABLE IF EXISTS ${table}`);
        } else {
          await this.db.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
        }
        console.log(`✅ Dropped table: ${table}`);
      } catch (error) {
        console.error(`❌ Failed to drop table ${table}:`, error);
      }
    }

    console.log('\n✅ Rollback completed');
    await this.close();
  }

  async verify(): Promise<void> {
    console.log('🔍 Verifying migration...\n');

    const requiredTables = [
      'users',
      'oauth_sessions',
      'audit_log',
      'token_rotation_history',
      'oauth_providers',
      'failed_auth_attempts'
    ];

    let allValid = true;

    for (const table of requiredTables) {
      try {
        if (this.db instanceof BunDatabase) {
          this.db.prepare(`SELECT 1 FROM ${table} LIMIT 1`).get();
        } else {
          await this.db.query(`SELECT 1 FROM ${table} LIMIT 1`);
        }
        console.log(`✅ Table verified: ${table}`);
      } catch (error) {
        console.log(`❌ Table missing or invalid: ${table}`);
        allValid = false;
      }
    }

    if (allValid) {
      console.log('\n✅ All tables verified successfully!');
    } else {
      console.log('\n❌ Some tables are missing or invalid');
      process.exit(1);
    }

    await this.close();
  }
}
/**
* Centralized, minimal migration runner used by the app at startup.
* Ensures conversations/messages schema exists before repositories are used.
* Idempotent and concise.
*/
export async function runMigrations(): Promise<void> {
 // Determine DB type the same way repositories do
 const envType = (process.env.DATABASE_TYPE || '').toLowerCase();
 const isPostgres =
   envType === 'postgres' ||
   envType === 'postgresql' ||
   (process.env.DATABASE_URL || '').startsWith('postgres');

 if (!isPostgres) {
   // SQLite path resolution matches repositories
   const filename = process.env.SQLITE_DB_FILE || join(process.cwd(), 'data', 'tokens.db');
   const dir = dirname(filename);
   if (!existsSync(dir)) {
     mkdirSync(dir, { recursive: true });
   }

   const db = new BunDatabase(filename);
   try {
     db.exec('PRAGMA journal_mode = WAL');
     db.exec('PRAGMA foreign_keys = ON');

     // Create only the schemas needed for conversations/messages
     const tableOrder = ['conversations', 'messages'] as const;
     for (const name of tableOrder) {
       const stmt = (SCHEMA as any)[name];
       if (typeof stmt === 'string' && stmt.trim().length > 0) {
         db.exec(stmt);
       }
     }

     // Create only the relevant indexes
     for (const idx of INDEXES) {
       if (
         idx.includes('conversations(') ||
         idx.includes('messages(')
       ) {
         db.exec(idx);
       }
     }
   } finally {
     db.close();
   }
 } else {
   // PostgreSQL
   const connectionString =
     process.env.DATABASE_URL ||
     process.env.PG_CONNECTION_STRING ||
     'postgres://localhost:5432/postgres';

   const pool = new Pool({ connectionString, max: 5 });
   try {
     // Adapt TIMESTAMP to TIMESTAMPTZ for Postgres for better semantics
     const toPgTime = (sql: string) => sql.replace(/\bTIMESTAMP\b/gi, 'TIMESTAMPTZ');

     const conversations = (SCHEMA as any)['conversations'];
     const messages = (SCHEMA as any)['messages'];

     if (typeof conversations === 'string') {
       await pool.query(toPgTime(conversations));
     }
     if (typeof messages === 'string') {
       await pool.query(toPgTime(messages));
     }

     for (const idx of INDEXES) {
       if (
         idx.includes('conversations(') ||
         idx.includes('messages(')
       ) {
         await pool.query(idx);
       }
     }
   } finally {
     await (pool as any).end();
   }
 }

 console.log('✅ DB migrations applied (conversations/messages)');
}

// CLI interface
async function main() {
 const args = process.argv.slice(2);
 const command = args[0] || 'migrate';

 const migration = new TokenVaultMigration();

 switch (command) {
   case 'migrate':
   case 'up':
     await migration.migrate();
     break;

   case 'rollback':
   case 'down':
     await migration.rollback();
     break;

   case 'verify':
   case 'check':
     await migration.verify();
     break;

   default:
     console.log(`
Token Vault Database Migration Tool

Usage:
  bun run src/db/migrate.ts [command]

Commands:
  migrate, up     - Run migrations (default)
  rollback, down  - Rollback all migrations
  verify, check   - Verify database schema

Environment Variables:
  NODE_ENV        - Set to 'production' for PostgreSQL
  DATABASE_URL    - PostgreSQL connection string (production)
  TOKEN_DB_PATH   - SQLite database path (development)
     `);
     process.exit(0);
 }
}

// Run if executed directly
if (import.meta.main) {
 main().catch(console.error);
}

export { TokenVaultMigration };
