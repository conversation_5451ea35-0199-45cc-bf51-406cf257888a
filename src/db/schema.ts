/**
 * Database schema definitions for TokenVault
 */

export const SCHEMA = {
  users: `
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      external_id VARCHAR(255) UNIQUE NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      name VA<PERSON>HA<PERSON>(255),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_login TIMESTAMP,
      is_active INTEGER DEFAULT 1,
      metadata TEXT
    )`,

  oauth_sessions: `
    CREATE TABLE IF NOT EXISTS oauth_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      provider VARCHAR(50) NOT NULL,
      access_token_encrypted TEXT NOT NULL,
      refresh_token_encrypted TEXT,
      token_type VARCHAR(50) DEFAULT 'Bearer',
      expires_at TIMESTAMP NOT NULL,
      refresh_expires_at TIMESTAMP,
      scope TEXT,
      id_token_encrypted TEXT,
      state_hash VARCHAR(64) NOT NULL,
      pkce_verifier_hash VARCHAR(64),
      session_id VARCHAR(255) UNIQUE NOT NULL,
      ip_address VARCHAR(45),
      user_agent TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_refreshed TIMESTAMP,
      refresh_count INTEGER DEFAULT 0,
      revoked_at TIMESTAMP,
      revoke_reason VARCHAR(255),
      encryption_version INTEGER DEFAULT 1,
      metadata TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      UNIQUE(user_id, provider)
    )`,

  audit_log: `
    CREATE TABLE IF NOT EXISTS audit_log (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      event_type VARCHAR(50) NOT NULL,
      user_id INTEGER,
      session_id VARCHAR(255),
      provider VARCHAR(50),
      ip_address VARCHAR(45),
      user_agent TEXT,
      success INTEGER NOT NULL,
      error_message TEXT,
      metadata TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )`,

  token_rotation_history: `
    CREATE TABLE IF NOT EXISTS token_rotation_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      session_id VARCHAR(255) NOT NULL,
      old_token_hash VARCHAR(64) NOT NULL,
      new_token_hash VARCHAR(64) NOT NULL,
      rotation_reason VARCHAR(100),
      rotated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      rotated_by VARCHAR(255)
    )`,

  oauth_providers: `
    CREATE TABLE IF NOT EXISTS oauth_providers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      provider_name VARCHAR(50) UNIQUE NOT NULL,
      client_id VARCHAR(255) NOT NULL,
      auth_url VARCHAR(500) NOT NULL,
      token_url VARCHAR(500) NOT NULL,
      user_info_url VARCHAR(500),
      revoke_url VARCHAR(500),
      scopes TEXT,
      supports_pkce INTEGER DEFAULT 1,
      supports_refresh INTEGER DEFAULT 1,
      token_expiry_seconds INTEGER DEFAULT 3600,
      refresh_expiry_seconds INTEGER,
      metadata TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

  failed_auth_attempts: `
    CREATE TABLE IF NOT EXISTS failed_auth_attempts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      identifier VARCHAR(255) NOT NULL,
      attempt_type VARCHAR(50) NOT NULL,
      ip_address VARCHAR(45),
      user_agent TEXT,
      error_type VARCHAR(100),
      attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

  conversation_logs: `
    CREATE TABLE IF NOT EXISTS conversation_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      one_line_summary VARCHAR(280),
      full_conversation_json TEXT,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )`,

  conversations: `
    CREATE TABLE IF NOT EXISTS conversations (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      summary TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,

  messages: `
    CREATE TABLE IF NOT EXISTS messages (
      id TEXT PRIMARY KEY,
      conversation_id TEXT NOT NULL,
      role TEXT NOT NULL,
      content TEXT NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
    )`
};

export const INDEXES = [
  'CREATE INDEX IF NOT EXISTS idx_conversation_logs_user_id ON conversation_logs(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_conversation_logs_timestamp ON conversation_logs(timestamp)',
  'CREATE INDEX IF NOT EXISTS idx_conversation_logs_one_line_summary ON conversation_logs(one_line_summary)',
  'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
  'CREATE INDEX IF NOT EXISTS idx_users_external_id ON users(external_id)',
  'CREATE INDEX IF NOT EXISTS idx_oauth_sessions_user_id ON oauth_sessions(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_oauth_sessions_provider ON oauth_sessions(provider)',
  'CREATE INDEX IF NOT EXISTS idx_oauth_sessions_session_id ON oauth_sessions(session_id)',
  'CREATE INDEX IF NOT EXISTS idx_oauth_sessions_expires_at ON oauth_sessions(expires_at)',
  'CREATE INDEX IF NOT EXISTS idx_oauth_sessions_revoked_at ON oauth_sessions(revoked_at)',
  'CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id)',
  'CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON audit_log(event_type)',
  'CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at)',
  'CREATE INDEX IF NOT EXISTS idx_failed_auth_attempts_identifier ON failed_auth_attempts(identifier)',
  'CREATE INDEX IF NOT EXISTS idx_failed_auth_attempts_attempted_at ON failed_auth_attempts(attempted_at)',
  'CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at)',
  'CREATE INDEX IF NOT EXISTS idx_messages_conversation_id_created_at ON messages(conversation_id, created_at)'
];
