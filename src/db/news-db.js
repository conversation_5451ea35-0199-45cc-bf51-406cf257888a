import fs from 'fs/promises';
import path from 'path';

// Lightweight news DB wrapper. Prefers SQLite (better-sqlite3) if available,
// otherwise falls back to a JSON file in ./data/news-summaries.json

const SQLITE_DB_PATH = process.env.DB_PATH || path.join(process.cwd(), 'data', 'dante.db');

async function ensureDataDir() {
  const dir = path.join(process.cwd(), 'data');
  await fs.mkdir(dir, { recursive: true });
  return dir;
}

async function saveToJson(articleId, summary) {
  try {
    const dataDir = await ensureDataDir();
    const filePath = path.join(dataDir, 'news-summaries.json');
    let existing = {};
    try {
      const raw = await fs.readFile(filePath, 'utf-8');
      existing = JSON.parse(raw);
    } catch (e) {
      existing = {};
    }
    existing[articleId] = { ...summary, savedAt: new Date().toISOString() };
    await fs.writeFile(filePath, JSON.stringify(existing, null, 2), 'utf-8');
    return true;
  } catch (err) {
    console.error('news-db: Failed to save JSON summary', err);
    return false;
  }
}

export async function saveArticleSummary(articleId, summary) {
  if (!articleId) {
    throw new Error('saveArticleSummary requires articleId');
  }
  // Try SQLite first
  let db;
  let result = false;
  try {
    const sqlite = await import('better-sqlite3');
    // Initialize DB and table
    db = sqlite.default(SQLITE_DB_PATH);
    db.exec(`CREATE TABLE IF NOT EXISTS news_summaries (
      id TEXT PRIMARY KEY,
      article_id TEXT,
      summary_json TEXT,
      why_it_matters TEXT,
      model TEXT,
      generated_at TEXT,
      saved_at TEXT
    )`);

    const stmt = db.prepare(`INSERT OR REPLACE INTO news_summaries
      (id, article_id, summary_json, why_it_matters, model, generated_at, saved_at)
      VALUES (@id, @article_id, @summary_json, @why_it_matters, @model, @generated_at, @saved_at)`);

    const now = new Date().toISOString();
    const payload = {
      id: `${articleId}`,
      article_id: articleId,
      summary_json: JSON.stringify(summary.bullets || []),
      why_it_matters: summary.why_it_matters || '',
      model: summary.model || '',
      generated_at: summary.generatedAt || now,
      saved_at: now
    };

    stmt.run(payload);
    result = true;
  } catch (err) {
    // If sqlite not available or fails, fallback to JSON file
    console.warn('news-db: SQLite not available or failed, falling back to JSON store:', err && err.message);
    result = await saveToJson(articleId, summary);
  } finally {
    if (db) {
      try { db.close(); } catch (e) {}
    }
  }
  return result;
}

export default { saveArticleSummary };
