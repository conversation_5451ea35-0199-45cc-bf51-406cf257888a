import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { authenticate } from '../middleware/authenticate';
import { storeFile, getFileRecord, generateSignedUrl } from '../../services/fileStorageService';

const router = Router();
const upload = multer({ storage: multer.memoryStorage(), limits: { fileSize: 10 * 1024 * 1024 } }); // 10MB limit

// POST /files - Upload a new file (multipart/form-data, field name: "file")
router.post('/', authenticate, upload.single('file'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user?.id) {
      return res.status(401).json({ message: 'Unauthorized' });
    }
    const f = req.file as Express.Multer.File | undefined;
    if (!f) return res.status(400).json({ message: 'No file uploaded' });

    const result = await storeFile(f.buffer, f.originalname, f.mimetype, req.user.id);
    const url = await generateSignedUrl(result.id);

    res.status(201).json({
      message: 'File uploaded successfully',
      fileId: result.id,
      url,
      metadata: result.metadata,
    });
  } catch (error) {
    next(error);
  }
});

// GET /files/:id - Get file metadata and a signed URL for download
router.get('/:id', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user?.id) return res.status(401).json({ message: 'Unauthorized' });
    const fileId = req.params.id;

    const record = await getFileRecord(fileId, req.user.id);
    const url = await generateSignedUrl(fileId);

    res.status(200).json({ metadata: record.metadata, url });
  } catch (error) {
    next(error);
  }
});

export default router;
