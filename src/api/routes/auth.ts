import { Router } from 'express';
import passport from '../../config/passport';
import { connectorService } from '../../services/connectorService';

const router = Router();

// Initiates the Google OAuth 2.0 authentication flow
router.get('/google', (req, res, next) => {
  const connector = req.query.connector as string;
  console.log('OAuth initiate - connector param:', connector);
  
  if (req.session) {
    req.session.connector = connector;
    // Save session before redirecting
    req.session.save((err) => {
      if (err) {
        console.error('Failed to save session:', err);
      } else {
        console.log('Session saved with connector:', connector);
      }
    });
  }
  
  // Determine scopes based on connector type
  const scopeMap: Record<string, string[]> = {
    calendar: ['profile', 'email', 'https://www.googleapis.com/auth/calendar.readonly'],
    drive: ['profile', 'email', 'https://www.googleapis.com/auth/drive.readonly'],
    email: ['profile', 'email', 'https://www.googleapis.com/auth/gmail.readonly'],
    gmail: ['profile', 'email', 'https://www.googleapis.com/auth/gmail.readonly']
  };
  
  const scopes = scopeMap[connector] || ['profile', 'email'];
  
  // Pass connector through OAuth using the state parameter to avoid
  // relying solely on server-side session persistence across redirects
  passport.authenticate('google', { 
    scope: scopes,
    accessType: 'offline',
    prompt: 'consent',
    state: connector || undefined
  })(req, res, next);
});

// Handles the callback from Google after authentication
router.get(
  '/google/callback',
  passport.authenticate('google', { failureRedirect: '/login' }),
  async (req, res) => {
    // Successful authentication, send success message to popup opener
    // Prefer OAuth state param; fall back to session if needed
    const connector = (req.query.state as string) || req.session?.connector;
    const user: any = req.user;
    const authInfo: any = req.authInfo;
    const tokenBundle = authInfo?.tokens || {};
    const accessToken = tokenBundle.accessToken || user?.accessToken;
    const refreshToken = tokenBundle.refreshToken || user?.refreshToken;

    console.log('OAuth callback - session:', req.session);
    console.log('OAuth callback - connector:', connector);
    console.log('OAuth callback - user exists:', !!user);
    console.log('OAuth callback - accessToken exists:', !!accessToken);

    if (connector && user) {
      // Map connector type to connector ID
      const connectorIdMap: Record<string, string> = {
        calendar: 'connector_googlecalendar',
        drive: 'connector_googledrive',
        email: 'connector_gmail',
        gmail: 'connector_gmail'
      };
      
      const connectorId = connectorIdMap[connector];
      
      if (connectorId && accessToken) {
        // Save the OAuth tokens for this connector
        try {
          const userEmail = user.emails?.[0]?.value || user.email;
          await connectorService.storeOAuthToken(
            {
              id: user.id,
              email: userEmail
            },
            connectorId as any, // Type assertion needed due to connector ID type
            {
              access_token: accessToken,
              refresh_token: refreshToken,
              expires_at: tokenBundle.expires_in
                ? Date.now() + Number(tokenBundle.expires_in) * 1000
                : Date.now() + 3600000,
              token_type: tokenBundle.token_type || 'Bearer',
              scope: typeof tokenBundle.scope === 'string' ? tokenBundle.scope : connector,
              id_token: tokenBundle.id_token
            }
          );
          console.log(`OAuth tokens saved for ${connectorId} with access token: ${String(accessToken)?.substring(0, 20)}...`);
        } catch (error) {
          console.error(`Failed to save OAuth tokens for ${connectorId}:`, error);
        }
      }
      
      res.send(`
        <!DOCTYPE html>
        <html>
        <head><title>Authentication Success</title></head>
        <body>
          <h3>Authentication successful! Closing window...</h3>
          <script>
            console.log('OAuth callback: Sending success message for ${connectorId}');
            if (window.opener) {
              window.opener.postMessage({ type: 'oauth_success', connectorId: '${connectorId}' }, '*');
              console.log('OAuth callback: Message sent to opener');
              setTimeout(() => {
                window.close();
              }, 1000);
            } else {
              console.error('OAuth callback: No window.opener available');
              document.body.innerHTML = '<p>Authentication successful! You can close this window.</p>';
            }
          </script>
        </body>
        </html>
      `);
    } else {
      res.send(`
        <script>
          window.opener.postMessage({ type: 'oauth_error', error: 'Missing connector or user data' }, '*');
          window.close();
        </script>
      `);
    }
  }
);

// Route to check authentication status
router.get('/status', (req, res) => {
  if (req.isAuthenticated()) {
    res.json({
      authenticated: true,
      user: req.user,
    });
  } else {
    res.json({
      authenticated: false,
    });
  }
});

// Route to log out
router.get('/logout', (req, res, next) => {
  req.logout((err) => {
    if (err) {
      return next(err);
    }
    res.redirect('/');
  });
});

export default router;

// Additional utility routes used by the UI hook
// Revoke tokens for a given connectorId
router.post('/revoke', async (req, res) => {
  try {
    const { connectorId } = req.body as { connectorId?: string };
    if (!connectorId) {
      return res.status(400).json({ error: 'connectorId is required' });
    }

    let user: any = req.user;
    let userEmail = user?.emails?.[0]?.value || user?.email;
    // Require authentication for token revocation
    if (!userEmail) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // connectorService.revokeTokens expects userId and connectorId; use email as identifier
    await connectorService.revokeTokens(userEmail, connectorId as any, 'user_requested');
    return res.json({ success: true });
  } catch (error: any) {
    console.error('Token revoke failed:', error);
    return res.status(500).json({ error: error.message || 'Failed to revoke tokens' });
  }
});

// Refresh tokens for a given connectorId (best-effort placeholder)
router.post('/refresh', async (req, res) => {
  try {
    const { connectorId } = req.body as { connectorId?: string };
    if (!connectorId) {
      return res.status(400).json({ error: 'connectorId is required' });
    }

    let user: any = req.user;
    let userEmail = user?.emails?.[0]?.value || user?.email;
    let userId = user?.id;
    // Require authentication for token refresh
    if (!userEmail) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const refreshed = await connectorService.refreshOAuthToken({ id: userId, email: userEmail }, connectorId as any);
    if (!refreshed) {
      return res.status(500).json({ error: 'Refresh failed' });
    }
    return res.json({ success: true, expiresAt: new Date(refreshed.expires_at || Date.now()).toISOString() });
  } catch (error: any) {
    console.error('Token refresh failed:', error);
    return res.status(500).json({ error: error.message || 'Failed to refresh token' });
  }
});
