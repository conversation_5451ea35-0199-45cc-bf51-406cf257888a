import express from 'express';

interface DeviceInfo {
  id: string;
  name: string;
  userAgent: string;
  lastSeen: number;
  ipAddress?: string;
}

interface SyncEvent {
  type: 'session_update' | 'message_added' | 'device_connected' | 'device_disconnected';
  sessionId: string;
  deviceId: string;
  timestamp: number;
  data?: any;
}

// In-memory storage for connected devices and SSE connections
const sessionDevices = new Map<string, Map<string, DeviceInfo>>();
const sseConnections = new Map<string, Map<string, express.Response>>();

const router = express.Router();

// Register a device for a session
router.post('/register', (req, res) => {
  try {
    const { sessionId, device } = req.body;
    
    if (!sessionId || !device || !device.id) {
      return res.status(400).json({ error: 'Invalid request data' });
    }

    // Add IP address to device info
    const deviceWithIP: DeviceInfo = {
      ...device,
      ipAddress: req.ip || req.connection.remoteAddress,
      lastSeen: Date.now(),
    };

    // Initialize session devices map if needed
    if (!sessionDevices.has(sessionId)) {
      sessionDevices.set(sessionId, new Map());
    }

    // Register the device
    sessionDevices.get(sessionId)!.set(device.id, deviceWithIP);

    // Broadcast device connected event to other devices
    broadcastToSession(sessionId, {
      type: 'device_connected',
      sessionId,
      deviceId: device.id,
      timestamp: Date.now(),
      data: { device: deviceWithIP },
    }, device.id);

    res.json({ success: true, device: deviceWithIP });
  } catch (error) {
    console.error('Device registration error:', error);
    res.status(500).json({ error: 'Failed to register device' });
  }
});

// SSE endpoint for real-time synchronization
router.get('/events', (req, res) => {
  const { sessionId, deviceId } = req.query;
  
  if (!sessionId || !deviceId) {
    return res.status(400).json({ error: 'Missing sessionId or deviceId' });
  }

  // Set up SSE headers
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');
  // Prevent proxy buffering (e.g., nginx) which can break SSE
  res.setHeader('X-Accel-Buffering', 'no');
  // Disable server and socket timeouts for this long-lived connection
  try { (req.socket as any)?.setTimeout?.(0); } catch {}
  try { (req.socket as any)?.setKeepAlive?.(true); } catch {}
  // Encourage client reconnection delay if disconnected
  res.write(`retry: 5000\n\n`);
  // Flush headers explicitly so the client treats this as a stream
  if (typeof (res as any).flushHeaders === 'function') {
    (res as any).flushHeaders();
  }

  // Initialize session connections map if needed
  if (!sseConnections.has(sessionId as string)) {
    sseConnections.set(sessionId as string, new Map());
  }

  // Store this connection
  sseConnections.get(sessionId as string)!.set(deviceId as string, res);

  // Send initial connection confirmation
  res.write(`data: ${JSON.stringify({
    type: 'connected',
    sessionId,
    deviceId,
    timestamp: Date.now(),
  })}\n\n`);

  // Send current connected devices list
  const sessionDeviceMap = sessionDevices.get(sessionId as string);
  if (sessionDeviceMap) {
    const devices = Array.from(sessionDeviceMap.values());
    res.write(`data: ${JSON.stringify({
      type: 'devices_list',
      sessionId,
      deviceId,
      timestamp: Date.now(),
      data: { devices },
    })}\n\n`);
  }

  // Heartbeat to keep connection alive across proxies and intermediaries
  const heartbeat = setInterval(() => {
    try {
      // Comment line per SSE spec to avoid triggering onmessage handlers
      res.write(`: ping ${Date.now()}\n\n`);
    } catch {
      // Best-effort; cleanup will occur on close
    }
  }, 25000);

  // Handle client disconnect
  req.on('close', () => {
    clearInterval(heartbeat);
    // Remove this connection
    const sessionConnections = sseConnections.get(sessionId as string);
    if (sessionConnections) {
      sessionConnections.delete(deviceId as string);
      if (sessionConnections.size === 0) {
        sseConnections.delete(sessionId as string);
      }
    }

    // Remove device from session
    const sessionDeviceMap = sessionDevices.get(sessionId as string);
    if (sessionDeviceMap) {
      sessionDeviceMap.delete(deviceId as string);
      if (sessionDeviceMap.size === 0) {
        sessionDevices.delete(sessionId as string);
      }
    }

    // Broadcast device disconnected event
    broadcastToSession(sessionId as string, {
      type: 'device_disconnected',
      sessionId: sessionId as string,
      deviceId: deviceId as string,
      timestamp: Date.now(),
    }, deviceId as string);
    try { res.end(); } catch {}
  });
});

// Broadcast an event to a session
router.post('/broadcast', (req, res) => {
  try {
    const { sessionId, deviceId, type, data, timestamp } = req.body;
    
    if (!sessionId || !deviceId || !type) {
      return res.status(400).json({ error: 'Invalid request data' });
    }

    const event: SyncEvent = {
      type,
      sessionId,
      deviceId,
      timestamp: timestamp || Date.now(),
      data,
    };

    broadcastToSession(sessionId, event, deviceId);
    res.json({ success: true });
  } catch (error) {
    console.error('Broadcast error:', error);
    res.status(500).json({ error: 'Failed to broadcast event' });
  }
});

// Handle device disconnect (beacon)
router.post('/disconnect', (req, res) => {
  try {
    const { deviceId } = req.body;
    
    if (!deviceId) {
      return res.status(400).json({ error: 'Missing deviceId' });
    }

    // Find and remove device from all sessions
    for (const [sessionId, deviceMap] of sessionDevices.entries()) {
      if (deviceMap.has(deviceId)) {
        deviceMap.delete(deviceId);
        
        // Broadcast disconnect event
        broadcastToSession(sessionId, {
          type: 'device_disconnected',
          sessionId,
          deviceId,
          timestamp: Date.now(),
        }, deviceId);
        
        if (deviceMap.size === 0) {
          sessionDevices.delete(sessionId);
        }
        break;
      }
    }

    // Remove SSE connections
    for (const [sessionId, connectionMap] of sseConnections.entries()) {
      if (connectionMap.has(deviceId)) {
        const connection = connectionMap.get(deviceId);
        try {
          const ended = connection && ((connection as any).writableEnded || (connection as any).finished);
          if (connection && !ended) connection.end();
        } catch {}
        connectionMap.delete(deviceId);
        
        if (connectionMap.size === 0) {
          sseConnections.delete(sessionId);
        }
        break;
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Disconnect error:', error);
    res.status(500).json({ error: 'Failed to disconnect device' });
  }
});

// Get connected devices for a session
router.get('/devices/:sessionId', (req, res) => {
  try {
    const { sessionId } = req.params;
    const deviceMap = sessionDevices.get(sessionId);
    
    if (!deviceMap) {
      return res.json({ devices: [] });
    }

    const devices = Array.from(deviceMap.values());
    res.json({ devices });
  } catch (error) {
    console.error('Get devices error:', error);
    res.status(500).json({ error: 'Failed to get devices' });
  }
});

// Helper function to broadcast events to all devices in a session except sender
function broadcastToSession(sessionId: string, event: SyncEvent, excludeDeviceId?: string): void {
  const sessionConnections = sseConnections.get(sessionId);
  if (!sessionConnections) return;

  const eventData = `data: ${JSON.stringify(event)}\n\n`;

  for (const [deviceId, connection] of sessionConnections.entries()) {
    // Skip the sender device
    if (excludeDeviceId && deviceId === excludeDeviceId) continue;
    
    try {
      const ended = (connection as any).writableEnded || (connection as any).finished;
      if (!ended) connection.write(eventData);
    } catch (error) {
      console.error('Failed to send event to device:', deviceId, error);
      // Remove broken connection
      sessionConnections.delete(deviceId);
    }
  }
}

// Cleanup old devices periodically (every 5 minutes)
setInterval(() => {
  const now = Date.now();
  const fiveMinutes = 5 * 60 * 1000;

  for (const [sessionId, deviceMap] of sessionDevices.entries()) {
    for (const [deviceId, device] of deviceMap.entries()) {
      if (now - device.lastSeen > fiveMinutes) {
        deviceMap.delete(deviceId);
        
        // Broadcast disconnect event
        broadcastToSession(sessionId, {
          type: 'device_disconnected',
          sessionId,
          deviceId,
          timestamp: now,
        }, deviceId);
      }
    }
    
    if (deviceMap.size === 0) {
      sessionDevices.delete(sessionId);
    }
  }
}, 5 * 60 * 1000);

export default router;
