import express from 'express';
import { reminderManager } from '../reminder/ReminderManager';
import { newsPollingService } from '../reminder/services/NewsPollingService';
import { notificationDelivery } from '../reminder/notification/NotificationDelivery';
import { ReminderType, ReminderStatus, NotificationChannel } from '../reminder/types';

const router = express.Router();

// Create a new reminder
router.post('/', async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      schedule,
      action,
      priority = 'medium',
      tags = [],
      maxExecutions,
      maxRetries = 3,
      sessionId,
      createdBy,
    } = req.body;

    if (!title || !description || !type) {
      return res.status(400).json({
        error: 'Missing required fields: title, description, type',
      });
    }

    const reminder = await reminderManager.createReminder({
      title,
      description,
      type: type as ReminderType,
      schedule,
      action,
      priority,
      tags,
      maxExecutions,
      maxRetries,
      sessionId,
      createdBy: createdBy || 'API',
    });

    res.status(201).json({
      success: true,
      reminder: {
        id: reminder.id,
        title: reminder.title,
        type: reminder.type,
        status: reminder.status,
        nextExecutionAt: reminder.nextExecutionAt,
        priority: reminder.priority,
        createdAt: reminder.createdAt,
      },
      message: `Successfully created ${type} reminder: "${title}"`,
    });
  } catch (error) {
    console.error('Failed to create reminder:', error);
    res.status(500).json({
      error: 'Failed to create reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Create news monitoring reminder
router.post('/news-monitoring', async (req, res) => {
  try {
    const {
      title,
      topic,
      eventDate,
      eventStartTime,
      eventEndTime,
      summaryType = 'live_updates',
      pollingInterval = 30,
      searchQueries,
      keywordsToTrack,
      maxUpdatesPerHour = 10,
      notificationChannels = ['web_ui'],
      sessionId,
      createdBy,
    } = req.body;

    if (!title || !topic) {
      return res.status(400).json({
        error: 'Missing required fields: title, topic',
      });
    }

    // Parse dates
    let parsedEventDate: Date | undefined;
    let parsedStartTime: Date | undefined;
    let parsedEndTime: Date | undefined;

    if (eventDate) parsedEventDate = new Date(eventDate);
    if (eventStartTime) parsedStartTime = new Date(eventStartTime);
    if (eventEndTime) parsedEndTime = new Date(eventEndTime);

    // Map notification channels
    const channels = notificationChannels.map((ch: string) => {
      switch (ch) {
        case 'web_ui': return NotificationChannel.WEB_UI;
        case 'file_queue': return NotificationChannel.FILE_QUEUE;
        case 'memory_store': return NotificationChannel.MEMORY_STORE;
        default: return NotificationChannel.WEB_UI;
      }
    });

    const reminder = await reminderManager.createNewsMonitoringReminder({
      title,
      topic,
      eventDate: parsedEventDate,
      eventStartTime: parsedStartTime,
      eventEndTime: parsedEndTime,
      summaryType,
      pollingInterval,
      searchQueries,
      keywordsToTrack,
      notificationChannels: channels,
      sessionId,
      createdBy: createdBy || 'API',
    });

    res.status(201).json({
      success: true,
      reminder: {
        id: reminder.id,
        title: reminder.title,
        topic,
        pollingInterval,
        summaryType,
        nextCheck: reminder.nextExecutionAt,
        status: reminder.status,
      },
      message: `Successfully set up news monitoring for "${topic}"`,
    });
  } catch (error) {
    console.error('Failed to create news monitoring:', error);
    res.status(500).json({
      error: 'Failed to create news monitoring',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// List reminders
router.get('/', async (req, res) => {
  try {
    const {
      status,
      type,
      sessionId,
      tags,
      limit = 20,
      includeCompleted = false,
    } = req.query;

    const filters: any = {};
    if (status) filters.status = status as ReminderStatus;
    if (type) filters.type = type as ReminderType;
    if (sessionId) filters.sessionId = sessionId as string;
    if (tags) {
      filters.tags = Array.isArray(tags) ? tags as string[] : [tags as string];
    }

    const allReminders = await reminderManager.listReminders(filters);

    // Filter out completed unless specifically requested
    const filteredReminders = includeCompleted === 'true' ?
      allReminders :
      allReminders.filter(r => r.status !== ReminderStatus.COMPLETED);

    const limitNum = parseInt(limit as string, 10);
    const limitedReminders = filteredReminders.slice(0, limitNum);

    const reminderSummaries = limitedReminders.map(r => ({
      id: r.id,
      title: r.title,
      type: r.type,
      status: r.status,
      priority: r.priority,
      nextExecutionAt: r.nextExecutionAt,
      lastExecutedAt: r.lastExecutedAt,
      executionCount: r.executionCount,
      tags: r.tags,
      createdAt: r.createdAt,
    }));

    res.json({
      success: true,
      reminders: reminderSummaries,
      totalFound: filteredReminders.length,
      totalShown: limitedReminders.length,
      filters,
    });
  } catch (error) {
    console.error('Failed to list reminders:', error);
    res.status(500).json({
      error: 'Failed to list reminders',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get specific reminder
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const reminder = await reminderManager.getReminder(id);

    if (!reminder) {
      return res.status(404).json({
        error: 'Reminder not found',
        message: `Reminder with ID "${id}" not found`,
      });
    }

    // Get additional status for news monitoring reminders
    let additionalInfo: any = {};

    if (reminder.type === ReminderType.NEWS_MONITORING) {
      const session = newsPollingService.getActiveSession(id);
      if (session) {
        additionalInfo.newsMonitoring = {
          topic: session.config.topic,
          updateCount: session.updateCount,
          lastUpdate: session.lastUpdate,
          seenUrlCount: session.seenUrls.size,
          keyDevelopmentCount: session.keyDevelopments.length,
          isActive: session.isActive,
        };
      }
    }

    res.json({
      success: true,
      reminder: {
        ...reminder,
        ...additionalInfo,
      },
    });
  } catch (error) {
    console.error('Failed to get reminder:', error);
    res.status(500).json({
      error: 'Failed to get reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Update reminder
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Parse dates if provided
    if (updates.schedule?.executeAt) {
      updates.schedule.executeAt = new Date(updates.schedule.executeAt);
    }

    const updatedReminder = await reminderManager.updateReminder(id, updates);

    if (!updatedReminder) {
      return res.status(404).json({
        error: 'Reminder not found',
        message: `Reminder with ID "${id}" not found`,
      });
    }

    res.json({
      success: true,
      reminder: {
        id: updatedReminder.id,
        title: updatedReminder.title,
        type: updatedReminder.type,
        status: updatedReminder.status,
        priority: updatedReminder.priority,
        nextExecutionAt: updatedReminder.nextExecutionAt,
        updatedAt: updatedReminder.updatedAt,
      },
      message: `Successfully updated reminder: "${updatedReminder.title}"`,
    });
  } catch (error) {
    console.error('Failed to update reminder:', error);
    res.status(500).json({
      error: 'Failed to update reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Cancel reminder
router.post('/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const reminder = await reminderManager.getReminder(id);
    if (!reminder) {
      return res.status(404).json({
        error: 'Reminder not found',
        message: `Reminder with ID "${id}" not found`,
      });
    }

    const cancelled = await reminderManager.cancelReminder(id);

    if (cancelled) {
      // If it's a news monitoring reminder, stop the polling
      if (reminder.type === ReminderType.NEWS_MONITORING) {
        await newsPollingService.stopPolling(id);
      }

      // Send cancellation notification if requested
      if (reason) {
        await notificationDelivery.sendSystemNotification(
          'Reminder Cancelled',
          `Reminder "${reminder.title}" has been cancelled. Reason: ${reason}`,
          reminder.sessionId
        );
      }

      res.json({
        success: true,
        message: `Successfully cancelled reminder: "${reminder.title}"`,
      });
    } else {
      res.status(400).json({
        error: 'Failed to cancel reminder',
        message: 'Reminder may not be in a cancellable state',
      });
    }
  } catch (error) {
    console.error('Failed to cancel reminder:', error);
    res.status(500).json({
      error: 'Failed to cancel reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Pause reminder
router.post('/:id/pause', async (req, res) => {
  try {
    const { id } = req.params;

    const paused = await reminderManager.pauseReminder(id);

    if (paused) {
      res.json({
        success: true,
        message: 'Reminder paused successfully',
      });
    } else {
      res.status(400).json({
        error: 'Failed to pause reminder',
        message: 'Reminder may not exist or may not be active',
      });
    }
  } catch (error) {
    console.error('Failed to pause reminder:', error);
    res.status(500).json({
      error: 'Failed to pause reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Resume reminder
router.post('/:id/resume', async (req, res) => {
  try {
    const { id } = req.params;

    const resumed = await reminderManager.resumeReminder(id);

    if (resumed) {
      res.json({
        success: true,
        message: 'Reminder resumed successfully',
      });
    } else {
      res.status(400).json({
        error: 'Failed to resume reminder',
        message: 'Reminder may not exist or may not be paused',
      });
    }
  } catch (error) {
    console.error('Failed to resume reminder:', error);
    res.status(500).json({
      error: 'Failed to resume reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Delete reminder
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deleted = await reminderManager.deleteReminder(id);

    if (deleted) {
      res.json({
        success: true,
        message: 'Reminder deleted successfully',
      });
    } else {
      res.status(404).json({
        error: 'Reminder not found',
        message: `Reminder with ID "${id}" not found`,
      });
    }
  } catch (error) {
    console.error('Failed to delete reminder:', error);
    res.status(500).json({
      error: 'Failed to delete reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get upcoming reminders
router.get('/upcoming/:limit?', async (req, res) => {
  try {
    const limit = parseInt(req.params.limit || '10', 10);

    const upcomingReminders = await reminderManager.getUpcomingReminders(limit);

    res.json({
      success: true,
      reminders: upcomingReminders.map(r => ({
        id: r.id,
        title: r.title,
        type: r.type,
        nextExecutionAt: r.nextExecutionAt,
        priority: r.priority,
      })),
      count: upcomingReminders.length,
    });
  } catch (error) {
    console.error('Failed to get upcoming reminders:', error);
    res.status(500).json({
      error: 'Failed to get upcoming reminders',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Manual news poll for testing
router.post('/:id/poll-news', async (req, res) => {
  try {
    const { id } = req.params;

    const reminder = await reminderManager.getReminder(id);
    if (!reminder) {
      return res.status(404).json({
        error: 'Reminder not found',
        message: `Reminder with ID "${id}" not found`,
      });
    }

    if (reminder.type !== ReminderType.NEWS_MONITORING) {
      return res.status(400).json({
        error: 'Invalid reminder type',
        message: 'This endpoint is only for news monitoring reminders',
      });
    }

    const updates = await newsPollingService.performManualPoll(id);

    res.json({
      success: true,
      updates: updates.map(update => ({
        id: update.id,
        title: update.title,
        summary: update.summary,
        changeType: update.changeType,
        timestamp: update.timestamp,
        sourceCount: update.sources.length,
        keyPointCount: update.keyPoints.length,
      })),
      updateCount: updates.length,
      message: `Found ${updates.length} news updates`,
    });
  } catch (error) {
    console.error('Failed to perform manual news poll:', error);
    res.status(500).json({
      error: 'Failed to perform manual news poll',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get system status
router.get('/system/status', async (req, res) => {
  try {
    const systemStatus = reminderManager.getSystemStatus();
    const queueStatus = notificationDelivery.getQueueStatus();
    const deliveryStats = notificationDelivery.getStats();
    const activeSessions = newsPollingService.getAllActiveSessions();

    const status = {
      system: {
        initialized: systemStatus.initialized,
        enabled: systemStatus.config.enabled,
        maxActiveReminders: systemStatus.config.maxActiveReminders,
        scheduler: systemStatus.scheduler,
      },
      notifications: {
        queueLength: queueStatus.queueLength,
        activeConnections: queueStatus.activeConnections,
        isProcessing: queueStatus.isProcessing,
        stats: deliveryStats,
      },
      newsPolling: {
        activeSessions: activeSessions.length,
        maxConcurrentSessions: 10,
        sessions: activeSessions.map(session => ({
          reminderId: session.reminderId,
          topic: session.config.topic,
          updateCount: session.updateCount,
          lastUpdate: session.lastUpdate,
          isActive: session.isActive,
        })),
      },
    };

    res.json({
      success: true,
      status,
      message: `Reminder system is ${systemStatus.initialized ? 'active' : 'inactive'}`,
    });
  } catch (error) {
    console.error('Failed to get system status:', error);
    res.status(500).json({
      error: 'Failed to get system status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// SSE endpoint for real-time notifications
router.get('/notifications/stream/:sessionId?', (req, res) => {
  try {
    const { sessionId } = req.params;

    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');
    res.setHeader('X-Accel-Buffering', 'no');
    
    try { (req.socket as any)?.setTimeout?.(0); } catch {}
    try { (req.socket as any)?.setKeepAlive?.(true); } catch {}
    try { res.write(`retry: 5000\n\n`); } catch {}
    try { (res as any).flushHeaders?.(); } catch {}

    // Register the connection
    const connectionId = notificationDelivery.registerSSEConnection(res, sessionId || 'default');

    // Send initial connection confirmation
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      connectionId,
      timestamp: new Date().toISOString(),
    })}\n\n`);

    // Setup heartbeat
    const heartbeat = setInterval(() => {
      try {
        res.write(`data: ${JSON.stringify({
          type: 'heartbeat',
          timestamp: new Date().toISOString(),
        })}\n\n`);
      } catch (error) {
        clearInterval(heartbeat);
      }
    }, 30000); // Every 30 seconds

    // Cleanup on disconnect
    req.on('close', () => {
      clearInterval(heartbeat);
      console.log(`📡 SSE connection closed: ${connectionId}`);
    });

  } catch (error) {
    console.error('Failed to setup SSE connection:', error);
    res.status(500).json({
      error: 'Failed to setup notification stream',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get session notifications
router.get('/notifications/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { limit = 50, markAsRead = false } = req.query;

    const notifications = await notificationDelivery.getSessionNotifications(
      sessionId,
      parseInt(limit as string, 10)
    );

    if (markAsRead === 'true') {
      // Mark all notifications as read
      for (const notification of notifications) {
        await notificationDelivery.markAsRead(notification.id, sessionId);
      }
    }

    res.json({
      success: true,
      notifications,
      count: notifications.length,
    });
  } catch (error) {
    console.error('Failed to get session notifications:', error);
    res.status(500).json({
      error: 'Failed to get session notifications',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Mark notification as read
router.post('/notifications/:sessionId/:notificationId/read', async (req, res) => {
  try {
    const { sessionId, notificationId } = req.params;

    const marked = await notificationDelivery.markAsRead(notificationId, sessionId);

    if (marked) {
      res.json({
        success: true,
        message: 'Notification marked as read',
      });
    } else {
      res.status(404).json({
        error: 'Notification not found',
        message: 'Notification may not exist or may not belong to this session',
      });
    }
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
    res.status(500).json({
      error: 'Failed to mark notification as read',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;