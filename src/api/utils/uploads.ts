import path from 'path';
import { URL } from 'url';

const DEFAULT_UPLOADS_DIR = path.join(process.cwd(), 'uploads');
const DEFAULT_PUBLIC_BASE = '/uploads';

export function getUploadsDir(): string {
  return DEFAULT_UPLOADS_DIR;
}

// Maps a public /uploads URL or absolute URL ending with /uploads/* to a safe absolute path under uploadsDir
export function resolveUploadPublicUrlToPath(input: string, uploadsDir = DEFAULT_UPLOADS_DIR, publicBase = DEFAULT_PUBLIC_BASE): string {
  if (!input) throw new Error('No URL provided');

  const normalizedBase = publicBase.endsWith('/') ? publicBase.slice(0, -1) : publicBase;
  const normalizedUploadsDir = path.resolve(uploadsDir);
  let relativePath = '';

  const trimmed = input.trim();

  // Absolute URL case
  if (/^https?:\/\//i.test(trimmed)) {
    const u = new URL(trimmed);
    if (!u.pathname.startsWith(normalizedBase + '/')) {
      throw new Error('URL is not under uploads public base');
    }
    relativePath = u.pathname.slice(normalizedBase.length); // keep leading '/'
  } else {
    const endIdxCandidates = [trimmed.indexOf('?'), trimmed.indexOf('#')]
      .filter(idx => idx >= 0);
    const endIdx = endIdxCandidates.length > 0 ? Math.min(...endIdxCandidates) : trimmed.length;
    const pathOnly = trimmed.slice(0, endIdx);

    if (pathOnly === normalizedBase) {
      relativePath = '';
    } else if (pathOnly.startsWith(normalizedBase + '/')) {
      relativePath = pathOnly.slice(normalizedBase.length); // keep leading '/'
    } else {
      throw new Error('Path is not under uploads public base');
    }
  }

  let decodedRelative = relativePath;
  if (relativePath) {
    try {
      decodedRelative = decodeURIComponent(relativePath);
    } catch {
      decodedRelative = relativePath;
    }
  }
  const target = path.resolve(normalizedUploadsDir, '.' + decodedRelative);
  if (!target.startsWith(normalizedUploadsDir + path.sep) && target !== normalizedUploadsDir) {
    throw new Error('Path traversal detected');
  }
  return target;
}
