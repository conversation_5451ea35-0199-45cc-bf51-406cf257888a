/**
 * Computer Use API Routes - Vercel AI SDK Implementation
 * 
 * Provides REST API endpoints for computer use functionality including
 * task management, session control, and streaming execution.
 */

import { Router, Request, Response } from 'express';
import { computerUseTaskManager, runComputerUseDispatcher } from '../tools/computerUseDispatcher';
import {
  computerUseExecutor,
  ComputerUseExecutionContext,
  ComputerUseExecutionOptions
} from '../tools/computerUse/computerUseToolExecutor';

const router = Router();

// Middleware for request logging
router.use((req: Request, res: Response, next) => {
  console.log(`🖥️ Computer Use API: ${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')?.substring(0, 100),
    timestamp: new Date().toISOString()
  });
  next();
});

// Error handler wrapper
const asyncHandler = (fn: (req: Request, res: Response, next: any) => Promise<any>) => 
  (req: Request, res: Response, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };

// Helper: safely parse dispatcher results which might already be objects
const parseDispatcherResult = (result: unknown) => {
  try {
    if (typeof result === 'string') {
      return JSON.parse(result);
    }
    return result as any;
  } catch (err) {
    // If parsing fails, return an error-shaped object for consistency
    return {
      success: false,
      error: err instanceof Error ? err.message : 'Failed to parse dispatcher result',
      raw: result
    };
  }
};

/**
 * Create a new computer use task
 * POST /api/computer-use/tasks
 */
router.post('/tasks', asyncHandler(async (req: Request, res: Response) => {
  const { action, sessionId, environmentType, userPrompt, ...otherParams } = req.body;

  if (!action) {
    return res.status(400).json({
      success: false,
      error: 'action parameter is required'
    });
  }

  try {
    const result = await runComputerUseDispatcher({
      operation: 'create_task',
      action,
      sessionId,
      environmentType,
      userPrompt,
      ...otherParams
    });

    const parsedResult = parseDispatcherResult(result);
    
    if (parsedResult.success) {
      res.status(201).json(parsedResult);
    } else {
      res.status(400).json(parsedResult);
    }
  } catch (error) {
    console.error('❌ Error creating computer use task:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Get task status
 * GET /api/computer-use/tasks/:taskId
 */
router.get('/tasks/:taskId', asyncHandler(async (req: Request, res: Response) => {
  const { taskId } = req.params;

  try {
    const result = await runComputerUseDispatcher({
      operation: 'get_task_status',
      taskId
    });

    const parsedResult = parseDispatcherResult(result);
    
    if (parsedResult.success) {
      res.json(parsedResult);
    } else {
      res.status(404).json(parsedResult);
    }
  } catch (error) {
    console.error(`❌ Error getting task status for ${taskId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Execute a computer use task
 * POST /api/computer-use/tasks/:taskId/execute
 */
router.post('/tasks/:taskId/execute', asyncHandler(async (req: Request, res: Response) => {
  const { taskId } = req.params;

  try {
    const result = await runComputerUseDispatcher({
      operation: 'execute_task',
      taskId
    });

    const parsedResult = parseDispatcherResult(result);
    res.json(parsedResult);
  } catch (error) {
    console.error(`❌ Error executing task ${taskId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Cancel a computer use task
 * POST /api/computer-use/tasks/:taskId/cancel
 */
router.post('/tasks/:taskId/cancel', asyncHandler(async (req: Request, res: Response) => {
  const { taskId } = req.params;

  try {
    const result = await runComputerUseDispatcher({
      operation: 'cancel_task',
      taskId
    });

    const parsedResult = parseDispatcherResult(result);
    res.json(parsedResult);
  } catch (error) {
    console.error(`❌ Error cancelling task ${taskId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Get all tasks
 * GET /api/computer-use/tasks
 */
router.get('/tasks', asyncHandler(async (req: Request, res: Response) => {
  try {
    const result = await runComputerUseDispatcher({
      operation: 'get_all_tasks'
    });

    const parsedResult = parseDispatcherResult(result);
    res.json(parsedResult);
  } catch (error) {
    console.error('❌ Error getting all tasks:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Direct computer use execution with AI guidance
 * POST /api/computer-use/execute
 */
router.post('/execute', asyncHandler(async (req: Request, res: Response) => {
  const { userPrompt, sessionId, useGemini, safetyLevel, ...options } = req.body;

  if (!userPrompt) {
    return res.status(400).json({
      success: false,
      error: 'userPrompt is required'
    });
  }

  const context: ComputerUseExecutionContext = {
    sessionId,
    requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  };

  const executionOptions: ComputerUseExecutionOptions = {
    useGemini,
    safetyLevel,
    ...options
  };

  try {
    const result = await computerUseExecutor.executeWithAIGuidance(
      userPrompt,
      context,
      executionOptions
    );

    res.json(result);
  } catch (error) {
    console.error('❌ Error in direct computer use execution:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Streaming computer use execution
 * POST /api/computer-use/execute/stream
 */
router.post('/execute/stream', asyncHandler(async (req: Request, res: Response) => {
  const { userPrompt, sessionId, useGemini, ...options } = req.body;

  if (!userPrompt) {
    return res.status(400).json({
      success: false,
      error: 'userPrompt is required'
    });
  }

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');

  const context: ComputerUseExecutionContext = {
    sessionId,
    requestId: `stream_req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  };

  const executionOptions: ComputerUseExecutionOptions = {
    stream: true,
    useGemini,
    ...options
  };

  try {
    console.log('🌊 Starting streaming computer use execution');
    
    for await (const chunk of computerUseExecutor.executeWithStreaming(
      userPrompt,
      context,
      executionOptions
    )) {
      const data = JSON.stringify(chunk);
      res.write(`data: ${data}\n\n`);
      
      // End stream on completion or error
      if (chunk.type === 'complete' || chunk.type === 'error') {
        break;
      }
    }
    
    res.write(`data: ${JSON.stringify({ type: 'end' })}\n\n`);
    res.end();
    
  } catch (error) {
    console.error('❌ Error in streaming computer use execution:', error);
    const errorData = JSON.stringify({
      type: 'error',
      error: error instanceof Error ? error.message : 'Stream execution failed',
      timestamp: new Date().toISOString()
    });
    res.write(`data: ${errorData}\n\n`);
    res.end();
  }
}));

/**
 * Acknowledge safety checks and continue
 * POST /api/computer-use/sessions/:sessionId/acknowledge-safety
 */
router.post('/sessions/:sessionId/acknowledge-safety', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const { safetyChecks } = req.body;

  if (!safetyChecks || !Array.isArray(safetyChecks)) {
    return res.status(400).json({
      success: false,
      error: 'safetyChecks array is required'
    });
  }

  try {
    const result = await computerUseExecutor.acknowledgeSafetyAndContinue(
      sessionId,
      safetyChecks
    );

    res.json(result);
  } catch (error) {
    console.error(`❌ Error acknowledging safety for session ${sessionId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Continue session with screenshot
 * POST /api/computer-use/sessions/:sessionId/continue
 */
router.post('/sessions/:sessionId/continue', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const { callId } = req.body;

  if (!callId) {
    return res.status(400).json({
      success: false,
      error: 'callId is required'
    });
  }

  try {
    const result = await computerUseExecutor.continueWithScreenshot(
      sessionId,
      callId
    );

    res.json(result);
  } catch (error) {
    console.error(`❌ Error continuing session ${sessionId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Close and cleanup a session
 * DELETE /api/computer-use/sessions/:sessionId
 */
router.delete('/sessions/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;

  try {
    const result = await runComputerUseDispatcher({
      operation: 'cleanup_session',
      sessionId
    });

    const parsedResult = parseDispatcherResult(result);
    res.json(parsedResult);
  } catch (error) {
    console.error(`❌ Error closing session ${sessionId}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Get all active sessions
 * GET /api/computer-use/sessions
 */
router.get('/sessions', asyncHandler(async (req: Request, res: Response) => {
  try {
    const activeSessions = computerUseExecutor.getActiveSessions();
    
    res.json({
      success: true,
      sessions: activeSessions,
      totalSessions: activeSessions.length
    });
  } catch (error) {
    console.error('❌ Error getting active sessions:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

/**
 * Health check endpoint
 * GET /api/computer-use/health
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  try {
    const health = await computerUseExecutor.healthCheck();
    
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 206 : 503;
    
    res.status(statusCode).json({
      success: health.status !== 'unhealthy',
      status: health.status,
      details: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error in computer use health check:', error);
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Get computer use statistics
 * GET /api/computer-use/stats
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const allTasks = computerUseTaskManager.getAllTasks();
    const activeSessions = computerUseExecutor.getActiveSessions();
    
    const stats = {
      tasks: {
        total: allTasks.length,
        pending: allTasks.filter(t => t.status === 'pending').length,
        running: allTasks.filter(t => t.status === 'running').length,
        completed: allTasks.filter(t => t.status === 'completed').length,
        failed: allTasks.filter(t => t.status === 'failed').length,
        cancelled: allTasks.filter(t => t.status === 'cancelled').length,
        safetyCheck: allTasks.filter(t => t.status === 'safety_check').length
      },
      sessions: {
        active: activeSessions.length,
        oldestSession: activeSessions.length > 0 
          ? Math.min(...activeSessions.map(s => s.startedAt.getTime()))
          : null
      },
      system: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    };

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Error getting computer use stats:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    });
  }
}));

// Global error handler for computer use routes
router.use((error: any, req: Request, res: Response, next: Function) => {
  console.error('❌ Computer Use Route Error:', {
    path: req.path,
    method: req.method,
    error: error.message,
    stack: error.stack
  });

  res.status(500).json({
    success: false,
    error: 'Computer use service error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

export { router as computerUseRoutes };
