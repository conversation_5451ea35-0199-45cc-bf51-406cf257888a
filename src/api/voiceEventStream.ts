import { Response } from 'express';

type Client = {
  id: number;
  res: Response;
  heartbeat?: NodeJS.Timeout;
};

class VoiceEventStream {
  private clients: Map<string, Set<Client>> = new Map();
  private nextId = 1;

  addClient(sessionId: string, res: Response): () => void {
    const client: Client = { id: this.nextId++, res };
    if (!this.clients.has(sessionId)) this.clients.set(sessionId, new Set());
    this.clients.get(sessionId)!.add(client);

    // Initial headers for SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no');
    res.setHeader('Access-Control-Allow-Origin', '*');
    try { (res.socket as any)?.setTimeout?.(0); } catch {}
    try { (res.socket as any)?.setKeepAlive?.(true); } catch {}
    // Advise client on reconnect delay and flush headers
    try { res.write(`retry: 5000\n\n`); } catch {}
    try { (res as any).flushHeaders?.(); } catch {}
    const connectedPayload = { type: 'trace_event', timestamp: new Date().toISOString(), data: { message: 'connected' } };
    res.write(`event: message\n`);
    res.write(`data: ${JSON.stringify(connectedPayload)}\n\n`);

    // Heartbeat to keep connection alive
    client.heartbeat = setInterval(() => {
      try {
        res.write(`: ping ${Date.now()}\n\n`);
      } catch {
        // Cleanup handled in removeClient
      }
    }, 25000);

    return () => this.removeClient(sessionId, client.id);
  }

  removeClient(sessionId: string, clientId: number) {
    const set = this.clients.get(sessionId);
    if (!set) return;
    for (const c of set) {
      if (c.id === clientId) {
        if (c.heartbeat) {
          try { clearInterval(c.heartbeat); } catch {}
        }
        try { c.res.end(); } catch {}
        set.delete(c);
        break;
      }
    }
    if (set.size === 0) this.clients.delete(sessionId);
  }

  broadcast(sessionId: string, eventType: string, data: any) {
    const set = this.clients.get(sessionId);
    if (!set || set.size === 0) return;
    const payload = {
      type: eventType,
      timestamp: new Date().toISOString(),
      data,
    };
    for (const c of set) {
      try {
        c.res.write(`event: message\n`);
        c.res.write(`data: ${JSON.stringify(payload)}\n\n`);
      } catch (e) {
        // Remove broken client
        set.delete(c);
      }
    }
  }
}

export const voiceEventStream = new VoiceEventStream();
