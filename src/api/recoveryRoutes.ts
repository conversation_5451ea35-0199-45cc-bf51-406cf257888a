import express from 'express';
import { recoverySystem } from '../recovery/RecoveryIntegration';
import { activeTaskManager } from '../recovery/ActiveTaskManager';
import { taskResumptionEngine } from '../recovery/TaskResumptionEngine';
import { hotReloadDetector } from '../recovery/HotReloadDetector';
import { contextPreserver } from '../recovery/ContextPreserver';

const router = express.Router();

// Get recovery system status
router.get('/status', async (req, res) => {
  try {
    const status = recoverySystem.getSystemStatus();
    
    res.json({
      success: true,
      status,
      message: 'Recovery system status retrieved',
    });
  } catch (error) {
    console.error('Failed to get recovery status:', error);
    res.status(500).json({
      error: 'Failed to get recovery status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// List active tasks
router.get('/tasks', async (req, res) => {
  try {
    const {
      sessionId,
      agentName,
      category,
      resumableOnly = false,
      limit = 20,
    } = req.query;

    let tasks = await activeTaskManager.listActiveTasks();

    // Apply filters
    if (sessionId) {
      tasks = tasks.filter(task => task.sessionId === sessionId);
    }
    
    if (agentName) {
      tasks = tasks.filter(task => task.agentName === agentName);
    }
    
    if (category) {
      tasks = tasks.filter(task => task.metadata.category === category);
    }
    
    if (resumableOnly === 'true') {
      tasks = tasks.filter(task => task.isResumable);
    }

    // Limit results
    const limitNum = parseInt(limit as string, 10);
    const limitedTasks = tasks.slice(0, limitNum);

    // Return summary info (not full task data for performance)
    const taskSummaries = limitedTasks.map(task => ({
      taskId: task.taskId,
      sessionId: task.sessionId,
      agentName: task.agentName,
      objective: task.metadata.objective.substring(0, 100) + (task.metadata.objective.length > 100 ? '...' : ''),
      category: task.metadata.category,
      priority: task.metadata.priority,
      progress: task.progress,
      currentStep: task.currentStep,
      isResumable: task.isResumable,
      resumptionConfidence: task.resumptionConfidence,
      createdAt: task.createdAt,
      lastCheckpoint: task.lastCheckpoint,
      fileModifications: task.fileModifications.length,
      toolExecutions: task.toolExecutions.length,
    }));

    res.json({
      success: true,
      tasks: taskSummaries,
      totalFound: tasks.length,
      totalShown: limitedTasks.length,
      filters: { sessionId, agentName, category, resumableOnly },
    });
  } catch (error) {
    console.error('Failed to list active tasks:', error);
    res.status(500).json({
      error: 'Failed to list active tasks',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get specific task details
router.get('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { includeFullContext = false } = req.query;

    const task = await activeTaskManager.loadTaskState(taskId);
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }

    // Optionally exclude full conversation context for performance
    let responseTask = task;
    if (includeFullContext !== 'true') {
      responseTask = {
        ...task,
        conversationContext: {
          messages: task.conversationContext.messages.slice(-5), // Last 5 messages only
          checkpoints: task.conversationContext.checkpoints.slice(-3), // Last 3 checkpoints only
        },
      };
    }

    res.json({
      success: true,
      task: responseTask,
      message: `Task details retrieved for ${taskId}`,
    });
  } catch (error) {
    console.error('Failed to get task details:', error);
    res.status(500).json({
      error: 'Failed to get task details',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Create a new task recovery point
router.post('/tasks', async (req, res) => {
  try {
    const {
      sessionId,
      agentName,
      objective,
      conversationMessages = [],
    } = req.body;

    if (!sessionId || !agentName || !objective) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'sessionId, agentName, and objective are required',
      });
    }

    const task = await recoverySystem.createTaskRecoveryPoint(
      sessionId,
      agentName,
      objective,
      conversationMessages
    );

    // Automatically create initial conversation checkpoint
    if (conversationMessages.length > 0) {
      try {
        const agentContext = {
          agentName,
          model: 'gpt-5',
          instructions: 'Starting task with recovery enabled',
          availableTools: [],
          lastActivity: new Date(),
          state: 'thinking' as const,
        };

        const checkpoint = await contextPreserver.preserveConversationContext(
          task.taskId,
          sessionId,
          conversationMessages,
          agentContext
        );

        res.status(201).json({
          success: true,
          task: {
            taskId: task.taskId,
            sessionId: task.sessionId,
            agentName: task.agentName,
            objective: task.metadata.objective,
            progress: task.progress,
            isResumable: task.isResumable,
            createdAt: task.createdAt,
          },
          checkpoint: {
            messageIndex: checkpoint.messageIndex,
            milestone: checkpoint.milestone,
            timestamp: checkpoint.timestamp,
          },
          message: `Created task recovery point with conversation checkpoint: ${task.taskId}`,
        });
      } catch (checkpointError) {
        console.warn('Failed to create initial checkpoint:', checkpointError);
        
        // Still return success for task creation, just note the checkpoint issue
        res.status(201).json({
          success: true,
          task: {
            taskId: task.taskId,
            sessionId: task.sessionId,
            agentName: task.agentName,
            objective: task.metadata.objective,
            progress: task.progress,
            isResumable: task.isResumable,
            createdAt: task.createdAt,
          },
          warning: 'Task created but initial checkpoint failed',
          message: `Created task recovery point: ${task.taskId}`,
        });
      }
    } else {
      res.status(201).json({
        success: true,
        task: {
          taskId: task.taskId,
          sessionId: task.sessionId,
          agentName: task.agentName,
          objective: task.metadata.objective,
          progress: task.progress,
          isResumable: task.isResumable,
          createdAt: task.createdAt,
        },
        message: `Created task recovery point: ${task.taskId}`,
      });
    }
  } catch (error) {
    console.error('Failed to create task recovery point:', error);
    res.status(500).json({
      error: 'Failed to create task recovery point',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Update task progress
router.put('/tasks/:taskId/progress', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { progress, step, toolExecutions, fileModifications } = req.body;

    if (typeof progress !== 'number' || progress < 0 || progress > 100) {
      return res.status(400).json({
        error: 'Invalid progress value',
        message: 'Progress must be a number between 0 and 100',
      });
    }

    // Update step if provided
    if (step) {
      await recoverySystem.markTaskStep(taskId, step, toolExecutions);
    }

    // Update progress
    const updated = await activeTaskManager.updateTaskProgress(taskId, progress);
    
    if (!updated) {
      return res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }

    // Record file modifications if provided
    if (fileModifications && Array.isArray(fileModifications)) {
      for (const fileMod of fileModifications) {
        await recoverySystem.markFileModification(taskId, fileMod.path);
      }
    }

    res.json({
      success: true,
      message: `Updated progress for task ${taskId} to ${progress}%`,
    });
  } catch (error) {
    console.error('Failed to update task progress:', error);
    res.status(500).json({
      error: 'Failed to update task progress',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Resume an interrupted task
router.post('/tasks/:taskId/resume', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { forceResume = false } = req.body;

    const result = await recoverySystem.resumeInterruptedTask(taskId, forceResume);

    if (result.success) {
      res.json({
        success: true,
        result: {
          taskId: result.taskId,
          resumedAt: result.resumedAt,
          recoveryTime: result.recoveryTime,
          message: result.message,
          nextSteps: result.nextSteps,
          updatedProgress: result.updatedProgress,
        },
        message: 'Task resumed successfully',
      });
    } else {
      res.status(400).json({
        success: false,
        result,
        error: 'Failed to resume task',
        message: result.message,
      });
    }
  } catch (error) {
    console.error('Failed to resume task:', error);
    res.status(500).json({
      error: 'Failed to resume task',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Complete a task
router.post('/tasks/:taskId/complete', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { outcome = 'Task completed successfully' } = req.body;

    const completed = await activeTaskManager.completeTask(taskId, outcome);
    
    if (completed) {
      res.json({
        success: true,
        message: `Task ${taskId} marked as completed`,
      });
    } else {
      res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }
  } catch (error) {
    console.error('Failed to complete task:', error);
    res.status(500).json({
      error: 'Failed to complete task',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Mark task as failed
router.post('/tasks/:taskId/fail', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { error: errorMessage = 'Task failed' } = req.body;

    const failed = await activeTaskManager.failTask(taskId, errorMessage);
    
    if (failed) {
      res.json({
        success: true,
        message: `Task ${taskId} marked as failed`,
      });
    } else {
      res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }
  } catch (error) {
    console.error('Failed to mark task as failed:', error);
    res.status(500).json({
      error: 'Failed to mark task as failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Delete task
router.delete('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;

    const deleted = await activeTaskManager.deleteTaskState(taskId);
    
    if (deleted) {
      res.json({
        success: true,
        message: `Task ${taskId} deleted successfully`,
      });
    } else {
      res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }
  } catch (error) {
    console.error('Failed to delete task:', error);
    res.status(500).json({
      error: 'Failed to delete task',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get recovery history
router.get('/history', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const history = await recoverySystem.getRecoveryHistory(parseInt(limit as string, 10));

    res.json({
      success: true,
      history,
      count: history.length,
    });
  } catch (error) {
    console.error('Failed to get recovery history:', error);
    res.status(500).json({
      error: 'Failed to get recovery history',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get hot-reload detector status
router.get('/hot-reload/status', async (req, res) => {
  try {
    const status = hotReloadDetector.getWatcherStatus();

    res.json({
      success: true,
      hotReloadStatus: status,
    });
  } catch (error) {
    console.error('Failed to get hot-reload status:', error);
    res.status(500).json({
      error: 'Failed to get hot-reload status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get recent file changes
router.get('/hot-reload/changes', async (req, res) => {
  try {
    const { since } = req.query;
    
    const sinceDate = since ? new Date(since as string) : undefined;
    const changes = hotReloadDetector.getRecentChanges(sinceDate);

    res.json({
      success: true,
      changes,
      count: changes.length,
    });
  } catch (error) {
    console.error('Failed to get recent changes:', error);
    res.status(500).json({
      error: 'Failed to get recent changes',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Trigger manual hot-reload check
router.post('/hot-reload/check', async (req, res) => {
  try {
    const event = await hotReloadDetector.forceHotReloadCheck();

    res.json({
      success: true,
      event,
      message: 'Manual hot-reload check triggered',
    });
  } catch (error) {
    console.error('Failed to trigger hot-reload check:', error);
    res.status(500).json({
      error: 'Failed to trigger hot-reload check',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Mark file as Dante modification
router.post('/hot-reload/mark-dante-modification', async (req, res) => {
  try {
    const { filePath } = req.body;

    if (!filePath) {
      return res.status(400).json({
        error: 'Missing file path',
        message: 'filePath is required',
      });
    }

    hotReloadDetector.markDanteModification(filePath);

    res.json({
      success: true,
      message: `Marked ${filePath} as Dante modification`,
    });
  } catch (error) {
    console.error('Failed to mark Dante modification:', error);
    res.status(500).json({
      error: 'Failed to mark Dante modification',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Create conversation checkpoint
router.post('/tasks/:taskId/checkpoint', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { sessionId, messages, agentContext, milestone } = req.body;

    if (!sessionId || !messages || !agentContext) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'sessionId, messages, and agentContext are required',
      });
    }

    const checkpoint = await contextPreserver.preserveConversationContext(
      taskId,
      sessionId,
      messages,
      agentContext
    );

    res.json({
      success: true,
      checkpoint: {
        messageIndex: checkpoint.messageIndex,
        milestone: checkpoint.milestone,
        timestamp: checkpoint.timestamp,
        taskProgress: checkpoint.taskProgress,
      },
      message: `Created conversation checkpoint for task ${taskId}`,
    });
  } catch (error) {
    console.error('Failed to create conversation checkpoint:', error);
    res.status(500).json({
      error: 'Failed to create conversation checkpoint',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Reconstruct task context
router.post('/tasks/:taskId/reconstruct-context', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { checkpointIndex } = req.body;

    const task = await activeTaskManager.loadTaskState(taskId);
    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }

    // Get the specified checkpoint or the latest one
    const checkpoints = task.conversationContext.checkpoints;
    if (checkpoints.length === 0) {
      return res.status(400).json({
        error: 'No checkpoints available',
        message: 'Task has no conversation checkpoints to reconstruct from',
      });
    }

    const checkpointToUse = checkpointIndex !== undefined ? 
      checkpoints[checkpointIndex] : 
      checkpoints[checkpoints.length - 1];

    if (!checkpointToUse) {
      return res.status(400).json({
        error: 'Invalid checkpoint index',
        message: 'Specified checkpoint index does not exist',
      });
    }

    const reconstructed = await contextPreserver.reconstructConversationContext(
      taskId,
      checkpointToUse
    );

    res.json({
      success: true,
      reconstructedContext: {
        messageCount: reconstructed.messages.length,
        agentContext: reconstructed.agentContext,
        reconstructionNotes: reconstructed.reconstructionNotes,
        reconstructionConfidence: reconstructed.reconstructionNotes.length === 1 ? 0.9 : 0.6,
      },
      checkpoint: {
        messageIndex: checkpointToUse.messageIndex,
        milestone: checkpointToUse.milestone,
        timestamp: checkpointToUse.timestamp,
      },
      message: `Reconstructed context from checkpoint for task ${taskId}`,
    });
  } catch (error) {
    console.error('Failed to reconstruct task context:', error);
    res.status(500).json({
      error: 'Failed to reconstruct task context',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Generate resumption context
router.get('/tasks/:taskId/resumption-context', async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await activeTaskManager.loadTaskState(taskId);
    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: `Task with ID "${taskId}" not found`,
      });
    }

    const resumptionContext = await contextPreserver.generateResumptionContext(
      task,
      task.resumptionPoint
    );

    res.json({
      success: true,
      resumptionContext,
      task: {
        taskId: task.taskId,
        objective: task.metadata.objective,
        progress: task.progress,
        currentStep: task.currentStep,
        isResumable: task.isResumable,
        resumptionConfidence: task.resumptionConfidence,
      },
      message: `Generated resumption context for task ${taskId}`,
    });
  } catch (error) {
    console.error('Failed to generate resumption context:', error);
    res.status(500).json({
      error: 'Failed to generate resumption context',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Create minimal recovery context (for emergency scenarios)
router.post('/tasks/:taskId/minimal-context', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { objective, lastKnownStep = 'unknown' } = req.body;

    if (!objective) {
      return res.status(400).json({
        error: 'Missing objective',
        message: 'objective is required for minimal context creation',
      });
    }

    const minimalContext = await contextPreserver.createMinimalRecoveryContext(
      taskId,
      objective,
      lastKnownStep
    );

    res.json({
      success: true,
      minimalContext: {
        messageCount: minimalContext.messages.length,
        agentContext: minimalContext.agentContext,
        resumptionPoint: minimalContext.resumptionPoint,
        reconstructionNotes: minimalContext.reconstructionNotes,
      },
      message: `Created minimal recovery context for task ${taskId}`,
      warning: 'This is a minimal context with limited information. Full context reconstruction is preferred when available.',
    });
  } catch (error) {
    console.error('Failed to create minimal recovery context:', error);
    res.status(500).json({
      error: 'Failed to create minimal recovery context',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get task resumption queue status
router.get('/resumption/queue', async (req, res) => {
  try {
    const queueStatus = taskResumptionEngine.getQueueStatus();
    const resumptionAttempts = taskResumptionEngine.getResumptionAttempts();

    res.json({
      success: true,
      queue: queueStatus,
      attempts: Object.fromEntries(resumptionAttempts),
    });
  } catch (error) {
    console.error('Failed to get resumption queue status:', error);
    res.status(500).json({
      error: 'Failed to get resumption queue status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Detect interrupted tasks
router.post('/detect-interrupted', async (req, res) => {
  try {
    const interruptedTasks = await taskResumptionEngine.detectInterruptedTasks();

    res.json({
      success: true,
      interruptedTasks: interruptedTasks.map(task => ({
        taskId: task.taskId,
        objective: task.metadata.objective.substring(0, 100),
        progress: task.progress,
        lastCheckpoint: task.lastCheckpoint,
        isResumable: task.isResumable,
        resumptionConfidence: task.resumptionConfidence,
      })),
      count: interruptedTasks.length,
      message: `Found ${interruptedTasks.length} interrupted tasks`,
    });
  } catch (error) {
    console.error('Failed to detect interrupted tasks:', error);
    res.status(500).json({
      error: 'Failed to detect interrupted tasks',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Start/Stop recovery system
router.post('/system/start', async (req, res) => {
  try {
    await recoverySystem.start();

    res.json({
      success: true,
      message: 'Recovery system started successfully',
    });
  } catch (error) {
    console.error('Failed to start recovery system:', error);
    res.status(500).json({
      error: 'Failed to start recovery system',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

router.post('/system/stop', async (req, res) => {
  try {
    await recoverySystem.stop();

    res.json({
      success: true,
      message: 'Recovery system stopped successfully',
    });
  } catch (error) {
    console.error('Failed to stop recovery system:', error);
    res.status(500).json({
      error: 'Failed to stop recovery system',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;