import OpenAI from 'openai';
import { config, isGeminiModel } from '../utils/config';
import { cleanParamsForModel } from '../utils/modelParamSanitizer';

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

const geminiClient = new OpenAI({
  apiKey: config.gemini.apiKey,
  baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai/',
});

export async function simpleChat(messages: Array<{ role: string; content: string }>, res: any, model?: string) {
  const targetModel = model || config.openai.defaultModel;
  const client = isGeminiModel(targetModel) ? geminiClient : openai;

  try {
    console.log(`🔄 Simple chat fallback using ${isGeminiModel(targetModel) ? 'Gemini' : 'OpenAI'} endpoint for model: ${targetModel}`);

    const req = {
      model: targetModel,
      messages: messages as any,
      stream: true,
      // temperature intentionally omitted for cross-provider compatibility
    };
    const sanitized = cleanParamsForModel(req);
    const stream: any = await client.chat.completions.create({ ...(sanitized as any), stream: true });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        res.write(content);
      }
    }

    res.end();
  } catch (error) {
    console.error(`${isGeminiModel(targetModel) ? 'Gemini' : 'OpenAI'} API error:`, error);
    throw error;
  }
}
