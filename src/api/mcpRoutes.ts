import express from 'express';
import { 
  initializeMCPServers,
  getMCPStatus,
  cleanupMCP,
  getAllMCPTools,
  getMCPToolsByTags,
  connectMCPServer,
  disconnectMCPServer,
  setMCPServerEnabled,
  invalidateMCPToolCache,
  performMCPHealthCheck
} from '../mcp';
import { mcpAgent } from '../agents/MCPAgent';
import { convertUIMessagesToAgentFormat } from '../utils/messageConverter';
import { validateAndTruncateMessages } from '../utils/tokenLimiter';
import { generateText, streamText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { config } from '../utils/config';

const router = express.Router();

// Initialize OpenAI client for MCP agent
const openai = createOpenAI({
  apiKey: config.openai.apiKey,
});

/**
 * Initialize MCP servers
 * POST /api/mcp/initialize
 */
router.post('/initialize', async (req, res) => {
  try {
    console.log('🔧 Initializing MCP servers via API...');
    await initializeMCPServers();
    const status = getMCPStatus();
    
    res.json({
      success: true,
      message: 'MCP servers initialized successfully',
      status
    });
  } catch (error) {
    console.error('❌ Failed to initialize MCP servers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize MCP servers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get MCP status
 * GET /api/mcp/status
 */
router.get('/status', async (req, res) => {
  try {
    const status = getMCPStatus();
    res.json({
      success: true,
      status
    });
  } catch (error) {
    console.error('❌ Failed to get MCP status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get MCP status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get all available MCP tools
 * GET /api/mcp/tools
 */
router.get('/tools', async (req, res) => {
  try {
    console.log('🔧 Getting all MCP tools via API...');
    const result = await getAllMCPTools();
    
    res.json({
      success: true,
      tools: result.metadata.map(meta => ({
        name: meta.toolName,
        description: meta.description,
        server: meta.serverName,
        serverId: meta.serverId,
        tags: meta.tags,
        schema: meta.schema,
        lastUpdated: meta.lastUpdated
      })),
      totalTools: result.tools.length,
      servers: [...new Set(result.metadata.map(m => m.serverName))]
    });
  } catch (error) {
    console.error('❌ Failed to get MCP tools:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get MCP tools',
      error: error instanceof Error ? error.message : 'Unknown error',
      tools: []
    });
  }
});

/**
 * Get MCP tools by tags
 * GET /api/mcp/tools/tags/:tags
 */
router.get('/tools/tags/:tags', async (req, res) => {
  try {
    const tags = req.params.tags.split(',').map(tag => tag.trim());
    console.log(`🔧 Getting MCP tools for tags: ${tags.join(', ')}`);
    
    const result = await getMCPToolsByTags(tags);
    
    res.json({
      success: true,
      tools: result.metadata.map(meta => ({
        name: meta.toolName,
        description: meta.description,
        server: meta.serverName,
        serverId: meta.serverId,
        tags: meta.tags,
        schema: meta.schema,
        lastUpdated: meta.lastUpdated
      })),
      totalTools: result.tools.length,
      requestedTags: tags
    });
  } catch (error) {
    console.error(`❌ Failed to get MCP tools for tags:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to get MCP tools for tags',
      error: error instanceof Error ? error.message : 'Unknown error',
      tools: []
    });
  }
});

/**
 * Connect to a specific MCP server
 * POST /api/mcp/servers/:serverId/connect
 */
router.post('/servers/:serverId/connect', async (req, res) => {
  try {
    const { serverId } = req.params;
    console.log(`🔗 Connecting to MCP server: ${serverId}`);
    
    await connectMCPServer(serverId);
    
    res.json({
      success: true,
      message: `Connected to MCP server: ${serverId}`
    });
  } catch (error) {
    console.error(`❌ Failed to connect to MCP server ${req.params.serverId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to connect to MCP server: ${req.params.serverId}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Disconnect from a specific MCP server
 * POST /api/mcp/servers/:serverId/disconnect
 */
router.post('/servers/:serverId/disconnect', async (req, res) => {
  try {
    const { serverId } = req.params;
    console.log(`📴 Disconnecting from MCP server: ${serverId}`);
    
    await disconnectMCPServer(serverId);
    
    res.json({
      success: true,
      message: `Disconnected from MCP server: ${serverId}`
    });
  } catch (error) {
    console.error(`❌ Failed to disconnect from MCP server ${req.params.serverId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to disconnect from MCP server: ${req.params.serverId}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Enable or disable a specific MCP server
 * PUT /api/mcp/servers/:serverId/enabled
 */
router.put('/servers/:serverId/enabled', async (req, res) => {
  try {
    const { serverId } = req.params;
    const { enabled } = req.body;
    
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'enabled field must be a boolean'
      });
    }
    
    console.log(`${enabled ? '✅' : '📴'} ${enabled ? 'Enabling' : 'Disabling'} MCP server: ${serverId}`);
    
    setMCPServerEnabled(serverId, enabled);
    
    res.json({
      success: true,
      message: `MCP server ${serverId} ${enabled ? 'enabled' : 'disabled'}`
    });
  } catch (error) {
    console.error(`❌ Failed to update MCP server ${req.params.serverId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to update MCP server: ${req.params.serverId}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Perform health check on all MCP servers
 * POST /api/mcp/health-check
 */
router.post('/health-check', async (req, res) => {
  try {
    console.log('🔍 Performing MCP health check...');
    const results = await performMCPHealthCheck();
    
    res.json({
      success: true,
      message: 'Health check completed',
      results
    });
  } catch (error) {
    console.error('❌ Failed to perform MCP health check:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform health check',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Invalidate MCP tool cache
 * POST /api/mcp/cache/invalidate
 */
router.post('/cache/invalidate', async (req, res) => {
  try {
    const { serverId } = req.body;
    console.log(`🔄 Invalidating MCP tool cache${serverId ? ` for server: ${serverId}` : ''}`);
    
    invalidateMCPToolCache(serverId);
    
    res.json({
      success: true,
      message: `Tool cache invalidated${serverId ? ` for server: ${serverId}` : ''}`
    });
  } catch (error) {
    console.error('❌ Failed to invalidate MCP tool cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to invalidate tool cache',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Cleanup MCP integration
 * POST /api/mcp/cleanup
 */
router.post('/cleanup', async (req, res) => {
  try {
    console.log('🧹 Cleaning up MCP integration...');
    await cleanupMCP();
    
    res.json({
      success: true,
      message: 'MCP integration cleaned up successfully'
    });
  } catch (error) {
    console.error('❌ Failed to cleanup MCP integration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup MCP integration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Execute MCP agent with streaming response
 * POST /api/mcp/execute
 */
router.post('/execute', async (req, res) => {
  try {
    const { messages, model = config.openai.defaultModel, stream = false } = req.body;
    
    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({
        success: false,
        message: 'messages array is required'
      });
    }
    
    console.log(`🤖 Executing MCP agent with ${messages.length} messages`);
    
    // Convert UI messages to agent format
    const convertedMessages = convertUIMessagesToAgentFormat(messages);
    
    // Validate and truncate messages if needed
    const validatedMessages = validateAndTruncateMessages(convertedMessages, model);
    
    if (stream) {
      // Set up streaming response
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
      });
      
      try {
        // Execute MCP agent with streaming
        await mcpAgent.executeStream(validatedMessages, model, (chunk) => {
          res.write(`data: ${JSON.stringify(chunk)}\n\n`);
        });
        
        res.write('data: [DONE]\n\n');
        res.end();
      } catch (error) {
        console.error('❌ MCP agent execution failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.write(`data: ${JSON.stringify({ 
          error: true,
          message: errorMessage 
        })}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
      }
    } else {
      // Non-streaming response
      const result = await mcpAgent.execute(validatedMessages, model);
      
      res.json({
        success: true,
        result,
        messageCount: validatedMessages.length
      });
    }
  } catch (error) {
    console.error('❌ Failed to execute MCP agent:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to execute MCP agent',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get detailed server information
 * GET /api/mcp/servers/:serverId
 */
router.get('/servers/:serverId', async (req, res) => {
  try {
    const { serverId } = req.params;
    const status = getMCPStatus();
    
    const serverStatus = status.servers.find(s => s.id === serverId);
    if (!serverStatus) {
      return res.status(404).json({
        success: false,
        message: `MCP server ${serverId} not found`
      });
    }
    
    // Get tools for this specific server
    let serverTools: any[] = [];
    try {
      const allTools = await getAllMCPTools();
      serverTools = allTools.metadata
        .filter(meta => meta.serverId === serverId)
        .map(meta => ({
          name: meta.toolName,
          description: meta.description,
          schema: meta.schema,
          lastUpdated: meta.lastUpdated
        }));
    } catch (toolsError) {
      console.warn(`⚠️ Failed to get tools for server ${serverId}:`, toolsError);
    }
    
    res.json({
      success: true,
      server: {
        ...serverStatus,
        tools: serverTools,
        toolCount: serverTools.length
      }
    });
  } catch (error) {
    console.error(`❌ Failed to get server info for ${req.params.serverId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to get server information: ${req.params.serverId}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as mcpRoutes };