/**
 * API routes for OAuth and connector management
 */

import express from 'express';
import { connectorService } from '../services/connectorService';
import { ConnectorId } from '../types/connectors';

const router = express.Router();

// OAuth routes have been moved to /api/auth in auth.ts
// Keeping only connector-specific routes here

// OAuth revoke route has been moved to /api/auth in auth.ts

// Auth status route is available at /api/auth/status/:connectorId in auth.ts

/**
 * Connector status for UI
 * Returns a list of known connectors and whether the current user is connected
 */
router.get('/connectors/status', async (req, res) => {
  try {
    // Known connectors tracked in the UI
    const known: ConnectorId[] = [
      'connector_gmail',
      'connector_googlecalendar',
      'connector_googledrive',
    ];

    // Determine current user from authenticated session if available
    const user: any = req.user;
    const userEmail = user?.emails?.[0]?.value || user?.email;
    const userObj = userEmail ? { id: user.id, email: userEmail } : null;

    const connectors = await Promise.all(known.map(async (id) => {
      try {
        let connected = false;
        let tokenExpiresAt: string | undefined;
        let lastConnected: string | undefined;

        if (userObj) {
          const info = await connectorService.getConnectorSession(userObj, id);
          if (info.connected) {
            connected = true;
            tokenExpiresAt = info.tokenExpiresAt ? info.tokenExpiresAt.toISOString() : undefined;
            lastConnected = info.lastConnected ? info.lastConnected.toISOString() : undefined;
          }
        }
        // For unauthenticated users, always return disconnected status

        return {
          id,
          connected,
          tokenExpiresAt,
          lastConnected,
        };
      } catch (e) {
        return { id, connected: false };
      }
    }));

    // Include any pending approval requests tracked by the service
    const pendingApprovals = Array.from(connectorService.getPendingApprovals().values());
    return res.json({ connectors, pendingApprovals });
  } catch (error: any) {
    console.error('Failed to get connector status:', error);
    return res.status(500).json({ error: error.message || 'Failed to get connector status' });
  }
});

/**
 * Execute a connector request directly
 */
router.post('/connector/execute', async (req, res) => {
  try {
    const {
      connector_id,
      input,
      require_approval,
      allowed_tools
    } = req.body;

    if (!connector_id || !input) {
      return res.status(400).json({
        success: false,
        error: 'connector_id and input are required'
      });
    }

    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }
    const currentUser = req.user as any;
    const userIdentifier = currentUser?.email || currentUser?.emails?.[0]?.value || currentUser?.id;
    const { token } = await connectorService.getOAuthTokenForContext(userIdentifier, connector_id as ConnectorId);

    if (!token) {
      const serviceMap: Record<ConnectorId, string> = {
        'connector_gmail': 'gmail',
        'connector_googlecalendar': 'calendar',
        'connector_googledrive': 'drive',
        'connector_dropbox': 'dropbox',
        'connector_microsoftteams': 'teams',
        'connector_outlookcalendar': 'outlook-calendar',
        'connector_outlookemail': 'outlook-email',
        'connector_sharepoint': 'sharepoint'
      };

      const service = serviceMap[connector_id as ConnectorId];

      return res.status(401).json({
        success: false,
        error: `Not authenticated with ${service || connector_id}`,
        authUrl: service ? `/api/auth/google/${service}` : null
      });
    }

    const result = await connectorService.executeConnectorRequest({
      connector_id: connector_id as ConnectorId,
      oauth_token: token.access_token,
      input,
      require_approval: require_approval || 'always',
      allowed_tools
    });

    return res.json({
      success: true,
      ...result
    });
  } catch (error: any) {
    console.error('Connector execution failed:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to execute connector request'
    });
  }
});

/**
 * Handle approval for MCP tool calls
 */
router.post('/connector/approve', async (req, res) => {
  try {
    const {
      approval_request_id,
      approve,
      previous_response_id,
      connector_id,
    } = req.body;

    if (!approval_request_id || approve === undefined || !previous_response_id || !connector_id) {
      return res.status(400).json({
        success: false,
        error: 'approval_request_id, approve, previous_response_id, and connector_id are required'
      });
    }

    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }
    const currentUser = req.user as any;
    const userIdentifier = currentUser?.email || currentUser?.emails?.[0]?.value || currentUser?.id;
    const { token } = await connectorService.getOAuthTokenForContext(userIdentifier, connector_id as ConnectorId);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Not authenticated with connector'
      });
    }

    const result = await connectorService.handleApproval(
      approval_request_id,
      approve,
      previous_response_id,
      {
        type: 'mcp',
        server_label: connector_id,
        connector_id: connector_id as ConnectorId,
        authorization: token.access_token
      }
    );

    return res.json({
      success: true,
      ...result
    });
  } catch (error: any) {
    console.error('Approval handling failed:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to handle approval'
    });
  }
});

export default router;
