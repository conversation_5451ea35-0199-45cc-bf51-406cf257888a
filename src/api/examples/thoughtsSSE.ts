import type { Express, Request, Response } from 'express';
import { ThoughtStream, toSSE, ThoughtMessage } from '../../utils/ThoughtStream';

/**
 * Minimal SSE example for streaming ThoughtMessage events to the UI.
 *
 * Usage (example):
 *   const streams = new Map<string, ThoughtStream>();
 *   registerThoughtsSSE(app, streams);
 *
 *   // When starting a new orchestrator run (prompt):
 *   const stream = new ThoughtStream();
 *   streams.set(runId, stream);
 *   await executeTaskOrchestrator(prompt, { stream: true, thoughtStream: stream });
 */
export function registerThoughtsSSE(app: Express, registry: Map<string, ThoughtStream>) {
  app.get('/api/thoughts/stream/:runId', (req: Request, res: Response) => {
    const { runId } = req.params as { runId: string };
    const stream = registry.get(runId);
    if (!stream) {
      res.status(404).json({ error: 'stream_not_found', runId });
      return;
    }

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders?.();

    const ac = new AbortController();
    const { signal } = ac;

    // Send an initial comment to establish the stream
    res.write(': connected\n\n');

    (async () => {
      try {
        for await (const msg of stream.subscribe({ signal })) {
          // SECURITY: sanitize untrusted content for SSE (no newlines in content JSON is safe)
          res.write(toSSE(msg));
        }
      } catch (e) {
        // Ignore errors on disconnect
      } finally {
        try { res.end(); } catch {}
      }
    })();

    req.on('close', () => ac.abort());
  });
}

// Client-side snippet (React/TS) mapping ThoughtMessage -> UI state:
//
// useEffect(() => {
//   const es = new EventSource(`/api/thoughts/stream/${runId}`);
//   const onThought = (ev: MessageEvent) => {
//     const msg: ThoughtMessage = JSON.parse(ev.data);
//     // Group by stepId/agentId, append msg.content for stage==='delta'
//     setThoughts((prev) => {
//       const key = `${msg.stepId || 'root'}:${msg.agentId}`;
//       const arr = prev[key] || [];
//       return { ...prev, [key]: [...arr, msg] };
//     });
//   };
//   es.addEventListener('thought', onThought);
//   return () => es.close();
// }, [runId]);

