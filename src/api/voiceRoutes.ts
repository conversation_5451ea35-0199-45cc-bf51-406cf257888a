import express from 'express';
import fetch from 'node-fetch';
import OpenAI from 'openai';
import multer from 'multer';
import { voiceEventStream } from './voiceEventStream';
import { processVoiceRequest, formatVoiceResponse, validateVoiceInput } from '../utils/voiceIntegration';

const router = express.Router();

const allowedVoices = new Set(['alloy','ash','ballad','coral','echo','sage','shimmer','verse']);

/**
 * Robust sentence splitting that handles abbreviations, decimals, and other edge cases
 */
function splitIntoSentences(text: string): string[] {
  if (!text.trim()) return [text];
  
  // Common abbreviations that shouldn't trigger sentence breaks
  const abbreviations = new Set([
    'mr', 'mrs', 'ms', 'dr', 'prof', 'sr', 'jr', 'vs', 'etc', 'inc', 'ltd', 'corp',
    'co', 'llc', 'st', 'ave', 'blvd', 'rd', 'ct', 'ln', 'apt', 'no', 'vol',
    'fig', 'e.g', 'i.e', 'cf', 'et al', 'etc', 'ph.d', 'm.d', 'b.a', 'm.a',
    'u.s', 'u.k', 'e.u', 'u.n', 'n.a.s.a', 'f.b.i', 'c.i.a'
  ]);
  
  const sentences: string[] = [];
  let currentSentence = '';
  let i = 0;
  
  while (i < text.length) {
    const char = text[i];
    currentSentence += char;
    
    // Check for sentence-ending punctuation
    if (/[.!?]/.test(char)) {
      // Look ahead to see if this might be an abbreviation
      const beforePunct = text.substring(Math.max(0, i - 10), i).toLowerCase();
      const afterPunct = text.substring(i + 1, Math.min(text.length, i + 5));
      
      // Check if this appears to be an abbreviation
      const words = beforePunct.split(/\s+/);
      const lastWord = words[words.length - 1];
      // Check for decimal numbers by looking at the context around the period
      const beforeDot = text.substring(Math.max(0, i - 5), i);
      const afterDot = text.substring(i + 1, Math.min(text.length, i + 4));
      const isDecimal = /\d$/.test(beforeDot) && /^\d/.test(afterDot);
      
      const isAbbreviation = abbreviations.has(lastWord) || 
                            isDecimal || // decimal numbers
                            (lastWord.length <= 3 && /^[a-z]+$/.test(lastWord)); // short words likely abbreviations
      
      // Check if next character suggests continuation (lowercase letter)
      const nextChar = afterPunct.trim()[0];
      const nextIsLowercase = nextChar && /[a-z]/.test(nextChar);
      
      // Split if it's not an abbreviation and next char isn't lowercase (or is whitespace/end)
      if (!isAbbreviation && !nextIsLowercase) {
        sentences.push(currentSentence.trim());
        currentSentence = '';
      }
    }
    
    i++;
  }
  
  // Add any remaining text as the last sentence
  if (currentSentence.trim()) {
    sentences.push(currentSentence.trim());
  }
  
  // Fallback: if no sentences were found, return the original text
  return sentences.length > 0 ? sentences : [text];
}

// Create an ephemeral Realtime session token for client-side WebRTC connections
router.post('/token', async (req, res) => {
  try {
    const { model, voice } = req.body || {};

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ success: false, error: 'OPENAI_API_KEY not configured' });
    }

    const requestedVoice = typeof voice === 'string' ? voice : undefined;
    const voiceToUse = requestedVoice && allowedVoices.has(requestedVoice) ? requestedVoice : 'verse';
    if (requestedVoice && !allowedVoices.has(requestedVoice)) {
      console.warn(`Voice '${requestedVoice}' not supported. Falling back to 'verse'.`);
    }

    const resp = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model || 'gpt-4o-realtime-preview-2025-06-03',
        voice: voiceToUse,
      }),
    });

    if (!resp.ok) {
      const errText = await resp.text();
      console.error('OpenAI realtime session error:', errText);
      return res.status(resp.status).json({ success: false, error: 'Failed to create realtime session' });
    }

    const session = await resp.json();
    res.json({ success: true, session });
  } catch (error: any) {
    console.error('Failed to create realtime session token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create realtime session token',
      message: error?.message || 'Unknown error'
    });
  }
});

// Delegate complex actions to the unified Dante agent and return plain text
router.post('/delegate', async (req, res) => {
  try {
    const { request, history, model, sessionId, voice } = req.body || {};
    
    // Validate input
    const validation = validateVoiceInput(request, { voice, model, sessionId });
    if (!validation.valid) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid input', 
        details: validation.errors 
      });
    }

    // Broadcast start of processing
    if (sessionId) {
      voiceEventStream.broadcast(sessionId, 'thinking', { 
        message: 'Processing voice request with orchestration', 
        agent: 'Dante'
      });
    }

    // Process the voice request through the integrated orchestration system
    const result = await processVoiceRequest(request, history || [], {
      sessionId,
      voice,
      model,
      stream: true, // Enable streaming for event broadcasting
    }, {
      emitEvent: sessionId ? (eventType, data) => voiceEventStream.broadcast(sessionId, eventType, data) : undefined,
    });

    // Broadcast completion
    if (sessionId) {
      if (result.success) {
        voiceEventStream.broadcast(sessionId, 'complete', { 
          message: result.voiceOptimizedText || result.text || 'Completed',
          metadata: result.metadata,
          agent: 'Dante'
        });
      } else {
        voiceEventStream.broadcast(sessionId, 'error', { 
          message: result.error || 'Processing failed',
          agent: 'Dante'
        });
      }
    }

    // Return formatted response
    const response = formatVoiceResponse(result);
    return res.json(response);

  } catch (error: any) {
    console.error('Voice delegate error:', error);
    
    // Broadcast error if session exists
    if (req.body?.sessionId) {
      voiceEventStream.broadcast(req.body.sessionId, 'error', { 
        message: error?.message || 'Unknown error',
        agent: 'Dante'
      });
    }

    res.status(500).json({ 
      success: false, 
      error: error?.message || 'Unknown error',
      text: '',
      voiceText: ''
    });
  }
});

// SSE endpoint to stream orchestration events for voice sessions
router.get('/events', async (req, res) => {
  const sessionId = (req.query.sessionId as string) || '';
  if (!sessionId) {
    return res.status(400).json({ error: 'sessionId is required' });
  }

  const remove = voiceEventStream.addClient(sessionId, res);
  req.on('close', () => {
    remove();
  });
});

// GET /api/voice/preview?voice=verse&text=...
router.get('/preview', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });

    const requestedVoice = (req.query.voice as string) || 'verse';
    const voiceToUse = allowedVoices.has(requestedVoice) ? requestedVoice : 'verse';
    const text = (req.query.text as string) || `Hi, I am the ${voiceToUse} voice. Let's get to work.`;

    const openai = new OpenAI({ apiKey });
    const speech = await openai.audio.speech.create({
      model: 'gpt-4o-mini-tts',
      voice: voiceToUse as any,
      input: text,
    } as any);

    const arrayBuffer = await (speech as any).arrayBuffer?.();
    if (!arrayBuffer) return res.status(500).json({ error: 'Failed to synthesize audio' });
    const buffer = Buffer.from(arrayBuffer);
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Cache-Control', 'no-store');
    res.send(buffer);
  } catch (error: any) {
    console.error('Voice preview error:', error);
    res.status(500).json({ error: error?.message || 'Failed to generate voice preview' });
  }
});

// Configure multer for audio file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 25 * 1024 * 1024 }, // 25MB limit
  fileFilter: (_req, file, cb) => {
    // Accept common audio formats
    const allowedTypes = /audio\/(wav|webm|mp3|m4a|ogg|flac)/;
    if (allowedTypes.test(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid audio format. Supported: WAV, WebM, MP3, M4A, OGG, FLAC'));
    }
  }
});

// POST /api/voice/transcribe  
// Body: FormData with 'audio' file and optional 'language' field
router.post('/transcribe', upload.single('audio'), async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });

    if (!req.file) return res.status(400).json({ error: 'No audio file provided' });

    const openai = new OpenAI({ apiKey });
    
    // Create a File-like object for the transcription API
    const audioFile = new File([new Uint8Array(req.file.buffer)], req.file.originalname || 'audio.wav', {
      type: req.file.mimetype || 'audio/wav'
    });

    // Support configurable language, defaults to 'en'
    const language = req.body.language || 'en';

    const transcription = await openai.audio.transcriptions.create({
      file: audioFile,
      model: 'whisper-1',
      response_format: 'json',
      language: language,
    });

    res.json({ 
      success: true, 
      text: transcription.text || '',
      language: (transcription as any).language || 'en'
    });
  } catch (error: any) {
    console.error('Voice transcribe error:', error);
    res.status(500).json({ error: error?.message || 'Failed to transcribe audio' });
  }
});

// POST /api/voice/say/stream
// Body: { text: string, voice?: string, format?: 'mp3'|'wav'|'pcm', chunkSize?: number }
// Streams TTS chunks for reduced latency
router.post('/say/stream', async (req, res) => {
  let heartbeat: NodeJS.Timeout | null = null;
  
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });

    const { text, voice, format, chunkSize = 150 } = req.body || {};
    if (!text || typeof text !== 'string') return res.status(400).json({ error: 'Missing text' });

    const voiceToUse = typeof voice === 'string' && allowedVoices.has(voice) ? voice : 'verse';
    const outputFormat = (typeof format === 'string' ? format.toLowerCase() : 'pcm') as 'mp3'|'wav'|'pcm';

    // Set up SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('X-Accel-Buffering', 'no');
    try { (req.socket as any)?.setTimeout?.(0); } catch {}
    try { (req.socket as any)?.setKeepAlive?.(true); } catch {}
    try { res.write(`retry: 5000\n\n`); } catch {}
    try { (res as any).flushHeaders?.(); } catch {}
    
    const openai = new OpenAI({ apiKey });
    
    heartbeat = setInterval(() => {
      try { res.write(`: ping ${Date.now()}\n\n`); } catch {}
    }, 25000);
    
    // Split text into sentences for chunking with robust handling of abbreviations
    const sentences = splitIntoSentences(text);
    let chunkBuffer = '';
    
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim();
      chunkBuffer += sentence + ' ';
      
      // Process chunk when buffer is large enough or at end
      const shouldProcess = chunkBuffer.length >= chunkSize || i === sentences.length - 1;
      
      if (shouldProcess && chunkBuffer.trim()) {
        try {
          const speech = await openai.audio.speech.create({
            model: 'gpt-4o-mini-tts',
            voice: voiceToUse as any,
            input: chunkBuffer.trim(),
            format: outputFormat as any,
          } as any);

          const arrayBuffer = await (speech as any).arrayBuffer?.();
          if (arrayBuffer) {
            const buffer = Buffer.from(arrayBuffer);
            const base64Audio = buffer.toString('base64');
            
            // Send chunk via SSE
            res.write(`data: ${JSON.stringify({
              type: 'chunk',
              data: base64Audio,
              format: outputFormat,
              chunkIndex: Math.floor(i / Math.max(1, chunkSize / 50)),
              isLast: i === sentences.length - 1
            })}\n\n`);
          }
          
          chunkBuffer = '';
        } catch (error) {
          console.error('TTS chunk error:', error);
          res.write(`data: ${JSON.stringify({
            type: 'error',
            message: 'Failed to synthesize chunk'
          })}\n\n`);
        }
      }
    }
    
    // Send completion signal
    res.write(`data: ${JSON.stringify({ type: 'complete' })}\n\n`);
    if (heartbeat) clearInterval(heartbeat);
    res.end();
    
  } catch (error: any) {
    console.error('Voice say stream error:', error);
    if (heartbeat) clearInterval(heartbeat);
    res.status(500).json({ error: error?.message || 'Failed to stream speech' });
  }
});

// POST /api/voice/say
// Body: { text: string, voice?: string, format?: 'mp3'|'wav'|'pcm' }
router.post('/say', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });

    const { text, voice, format } = req.body || {};
    if (!text || typeof text !== 'string') return res.status(400).json({ error: 'Missing text' });

    const voiceToUse = typeof voice === 'string' && allowedVoices.has(voice) ? voice : 'verse';
    const outputFormat = (typeof format === 'string' ? format.toLowerCase() : 'mp3') as 'mp3'|'wav'|'pcm';

    const openai = new OpenAI({ apiKey });
    const speech = await openai.audio.speech.create({
      model: 'gpt-4o-mini-tts',
      voice: voiceToUse as any,
      input: text,
      format: outputFormat as any,
    } as any);

    const arrayBuffer = await (speech as any).arrayBuffer?.();
    if (!arrayBuffer) return res.status(500).json({ error: 'Failed to synthesize audio' });
    const buffer = Buffer.from(arrayBuffer);

    const contentType = outputFormat === 'wav' ? 'audio/wav' : outputFormat === 'pcm' ? 'audio/wave' : 'audio/mpeg';
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'no-store');
    res.send(buffer);
  } catch (error: any) {
    console.error('Voice say error:', error);
    res.status(500).json({ error: error?.message || 'Failed to synthesize speech' });
  }
});

export default router;
