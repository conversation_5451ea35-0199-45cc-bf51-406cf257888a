import { Router } from 'express';
import { memoryManager } from '../memory/MemoryManager';
import { MemoryQuery } from '../memory/types';

const router = Router();

// Search memories
router.post('/search', async (req, res) => {
  try {
    const query: MemoryQuery = req.body;
    const result = await memoryManager.search(query);
    res.json(result);
  } catch (error) {
    console.error('Memory search error:', error);
    res.status(500).json({ error: 'Failed to search memories' });
  }
});

// Semantic search
router.post('/semantic-search', async (req, res) => {
  try {
    const query = req.body;
    const result = await memoryManager.semanticSearch(query);
    res.json(result);
  } catch (error) {
    console.error('Semantic search error:', error);
    res.status(500).json({ error: 'Failed to perform semantic search' });
  }
});

// Get memory stats
router.get('/stats', async (req, res) => {
  try {
    const stats = await memoryManager.getStats();
    res.json(stats);
  } catch (error) {
    console.error('Memory stats error:', error);
    res.status(500).json({ error: 'Failed to get memory stats' });
  }
});

// Delete memory
router.delete('/:id', async (req, res) => {
  try {
    const result = await memoryManager.delete(req.params.id);
    res.json(result);
  } catch (error) {
    console.error('Memory delete error:', error);
    res.status(500).json({ error: 'Failed to delete memory' });
  }
});

// Consolidate memories
router.post('/consolidate', async (req, res) => {
  try {
    await memoryManager.consolidate();
    res.json({ success: true });
  } catch (error) {
    console.error('Memory consolidation error:', error);
    res.status(500).json({ error: 'Failed to consolidate memories' });
  }
});

// Export memories
router.get('/export', async (req, res) => {
  try {
    const data = await memoryManager.exportMemories();
    res.json({ data });
  } catch (error) {
    console.error('Memory export error:', error);
    res.status(500).json({ error: 'Failed to export memories' });
  }
});

// Import memories
router.post('/import', async (req, res) => {
  try {
    const { data } = req.body;
    const result = await memoryManager.importMemories(data);
    res.json(result);
  } catch (error) {
    console.error('Memory import error:', error);
    res.status(500).json({ error: 'Failed to import memories' });
  }
});

// Vector-specific endpoints

// Find related memories
router.get('/related/:id', async (req, res) => {
  try {
    const { topK = 5, threshold = 0.8 } = req.query;
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    const memories = await vectorStore.findRelatedMemories(
      req.params.id,
      Number(topK),
      Number(threshold)
    );
    
    res.json({ memories });
  } catch (error) {
    console.error('Related memories error:', error);
    res.status(500).json({ error: 'Failed to find related memories' });
  }
});

// Cluster memories
router.post('/cluster', async (req, res) => {
  try {
    const { k = 5 } = req.body;
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    const clusters = await vectorStore.clusterMemories(Number(k));
    
    // Convert Map to object for JSON serialization
    const clustersObj: Record<number, any[]> = {};
    clusters.forEach((memories, clusterId) => {
      clustersObj[clusterId] = memories;
    });
    
    res.json({ clusters: clustersObj, totalClusters: clusters.size });
  } catch (error) {
    console.error('Memory clustering error:', error);
    res.status(500).json({ error: 'Failed to cluster memories' });
  }
});

// Get vector store stats
router.get('/vector-stats', async (req, res) => {
  try {
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    const stats = await vectorStore.getStats();
    res.json(stats);
  } catch (error) {
    console.error('Vector stats error:', error);
    res.status(500).json({ error: 'Failed to get vector stats' });
  }
});

// Reindex vector store
router.post('/reindex', async (req, res) => {
  try {
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    await vectorStore.reindex();
    res.json({ success: true, message: 'Reindexing started' });
  } catch (error) {
    console.error('Reindex error:', error);
    res.status(500).json({ error: 'Failed to reindex vector store' });
  }
});

// Create snapshot
router.post('/snapshot', async (req, res) => {
  try {
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    const snapshotId = await vectorStore.createSnapshot();
    res.json({ success: true, snapshotId });
  } catch (error) {
    console.error('Snapshot error:', error);
    res.status(500).json({ error: 'Failed to create snapshot' });
  }
});

// Hybrid search with filters
router.post('/hybrid-search', async (req, res) => {
  try {
    const { query, filters, topK = 10 } = req.body;
    const vectorStore = memoryManager.getVectorStore();
    
    if (!vectorStore) {
      return res.status(400).json({ error: 'Vector store not available' });
    }
    
    const result = await vectorStore.hybridSearch(query, filters, topK);
    res.json(result);
  } catch (error) {
    console.error('Hybrid search error:', error);
    res.status(500).json({ error: 'Failed to perform hybrid search' });
  }
});

export const memoryRoutes = router;