import sys
import unittest
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = BASE_DIR.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from news_db import ArticleDB, compute_hash

TEST_DB = BASE_DIR / "test_tmp.db"


class TestArticleDB(unittest.TestCase):
    def setUp(self):
        # Ensure a fresh DB file
        TEST_DB.unlink(missing_ok=True)
        BASE_DIR.mkdir(parents=True, exist_ok=True)
        self.db = ArticleDB(str(TEST_DB))

    def tearDown(self):
        self.db.close()
        TEST_DB.unlink(missing_ok=True)

    def test_insert_and_deduplicate(self):
        url1 = "https://example.com/a"
        content1 = "payload one"
        r1 = self.db.store_article(url=url1, title="A", content=content1)
        self.assertEqual(r1["status"], "inserted")
        art1 = r1["article"]

        # Duplicate by URL
        r2 = self.db.store_article(url=url1, title="A2", content="different text")
        self.assertEqual(r2["status"], "duplicate")
        self.assertEqual(r2["reason"], "url")
        self.assertEqual(r2["article"].id, art1.id)

        # Duplicate by content hash
        url2 = "https://example.com/b"
        r3 = self.db.store_article(url=url2, title="B", content=content1)
        self.assertEqual(r3["status"], "duplicate")
        self.assertEqual(r3["reason"], "hash")
        self.assertEqual(r3["article"].id, art1.id)

    def test_compute_hash_stability(self):
        t1 = "Line1\nLine2\n"
        t2 = "Line1\r\nLine2\n"
        self.assertEqual(compute_hash(t1), compute_hash(t2))


if __name__ == "__main__":
    unittest.main()
