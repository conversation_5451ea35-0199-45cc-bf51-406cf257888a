import { anthropic } from "@ai-sdk/anthropic";
import { google } from "@ai-sdk/google";
import { ollama } from "ai-sdk-ollama";
import { openai } from '@ai-sdk/openai';

const PROVIDERS = { openai, google, anthropic, ollama } as const;

// Model mapping
export const MODEL_MAP: Record<string, { provider: keyof typeof PROVIDERS; model: string }> = {
  'gpt-4o': { provider: 'openai', model: 'gpt-4o' },
  'gpt-4o-mini': { provider: 'openai', model: 'gpt-4o-mini' },
  'gpt-5': { provider: 'openai', model: 'gpt-5' },
  'gpt-5-codex': { provider: 'openai', model: 'gpt-5-codex' },
  // Add mini variants so we don't silently fall back to gpt-4o
  'gpt-5-mini': { provider: 'openai', model: 'gpt-5-mini' },
  'gpt-4.1': { provider: 'openai', model: 'gpt-4.1' },
  'gpt-4.1-mini': { provider: 'openai', model: 'gpt-4.1-mini' },
  'gemini-2.5-flash': { provider: 'google', model: 'gemini-2.5-flash' },
  'gemini-2.5-pro': { provider: 'google', model: 'gemini-2.5-pro' },
  'gemini-2.0-flash-thinking-exp': { provider: 'google', model: 'gemini-2.0-flash-thinking-exp' },
  'claude-3-5-sonnet': { provider: 'anthropic', model: 'claude-3-5-sonnet-latest' },
  'claude-3-5-haiku': { provider: 'anthropic', model: 'claude-3-5-haiku-latest' }
};

export interface AgentDefinition {
  name: string;
  description: string;
  instructions: string;
  model?: string;
  tools?: Record<string, any>;
  temperature?: number;
  maxSteps?: number;
}

export interface OrchestratorOptions {
  stream?: boolean;
  maxSteps?: number;
  onStepFinish?: (step: any) => void;
  onToolCall?: (toolName: string, args: any) => void;
  temperature?: number;
  onAgentSelected?: (payload: { agent: string; confidence: number }) => void;
  // If provided, the router will use this text instead of the full concatenated input
  routeText?: string;
  sessionId?: string;
  // Hint that this is a non-interactive/background task (prefer offline provider)
  background?: boolean;
  initialWorkingDirectory?: string;
}

export type ToolUsageStatsSnapshot = {
  totalCalls: number;
  fileEdit: number;
  readFile: { total: number; paths: Map<string, number>; maxRepeat: number };
  listDirectory: { total: number; paths: Map<string, number>; maxRepeat: number };
};

export type AnalysisTurnState = {
  count: number;
  readPaths: Set<string>;
  listDirs: Set<string>;
  totalToolCalls: number;
  lastUpdated: number;
};

export type StreamStepAggregation = {
  agent: string;
  textChunks: string[];
  toolCalls: Array<{ id?: string; name: string; args?: any; result?: any }>;
  structuredAnswer?: any;
  writeConfirmed: boolean;
  errors: string[];
};
