export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  contextWindow: string;
  capabilities: string;
  speed: 'fast' | 'balanced' | 'slow';
  cost: 'low' | 'medium' | 'high';
  icon: string;
}

export const AVAILABLE_MODELS: ModelInfo[] = [
  {
    id: 'models/gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: 'Ultimate orchestrator with 1M+ context',
    contextWindow: '1M context',
    capabilities: 'Advanced thinking, complex reasoning, massive context, perfect orchestration',
    speed: 'slow',
    cost: 'high',
    icon: '🎭'
  },
  {
    id: 'models/gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: 'Balanced orchestrator with thinking',
    contextWindow: '1M context',
    capabilities: 'Adaptive thinking, cost efficiency, excellent orchestration, multimodal',
    speed: 'balanced',
    cost: 'medium',
    icon: '⚡'
  },
  {
    id: 'models/gemini-flash-latest',
    name: 'Gemini 2.5 Flash',
    description: 'Balanced orchestrator with thinking',
    contextWindow: '1M context',
    capabilities: 'Adaptive thinking, cost efficiency, excellent orchestration, multimodal',
    speed: 'balanced',
    cost: 'medium',
    icon: '⚡'
  },
  {
    id: 'models/gemini-2.5-flash-lite',
    name: 'Gemini 2.5 Flash-Lite',
    description: 'High-throughput orchestrator',
    contextWindow: '1M context',
    capabilities: 'Most cost-efficient with massive context, high throughput orchestration',
    speed: 'fast',
    cost: 'low',
    icon: '🏃'
  },
  {
    id: 'models/gemini-flash-lite-latest',
    name: 'Gemini 2.5 Flash-Lite',
    description: 'High-throughput orchestrator',
    contextWindow: '1M context',
    capabilities: 'Most cost-efficient with massive context, high throughput orchestration',
    speed: 'fast',
    cost: 'low',
    icon: '🏃'
  },
  {
    id: 'gpt-5',
    name: 'GPT-5',
    description: 'Cutting-edge AI, maximum capability',
    contextWindow: '400K context',
    capabilities: 'State-of-the-art reasoning, complex multi-step tasks, advanced code generation',
    speed: 'slow',
    cost: 'high',
    icon: '🧠'
  },
  {
    id: 'gpt-5-codex',
    name: 'GPT-5-codex',
    description: 'Cutting-edge AI, maximum capability specialized for coding and agentic tasks',
    contextWindow: '400K context',
    capabilities: 'State-of-the-art reasoning, complex multi-step tasks, advanced code generation',
    speed: 'slow',
    cost: 'high',
    icon: '🧠'
  },
  {
    id: 'gpt-5-mini',
    name: 'GPT-5 Mini',
    description: 'Next-gen balanced model',
    contextWindow: '400K context',
    capabilities: 'Enhanced reasoning with huge context, ideal for large documents',
    speed: 'balanced',
    cost: 'medium',
    icon: '✨'
  },
  {
    id: 'gpt-5-nano',
    name: 'GPT-5 Nano',
    description: 'Ultra-fast GPT-5 variant',
    contextWindow: '400K context',
    capabilities: 'Quick GPT-5 quality responses with massive context window',
    speed: 'fast',
    cost: 'low',
    icon: '⚡'
  },
  {
    id: 'gpt-4.1',
    name: 'GPT-4.1',
    description: 'Enhanced GPT-4 with improvements',
    contextWindow: '128K context',
    capabilities: 'Improved reasoning over GPT-4, better code generation, enhanced accuracy',
    speed: 'slow',
    cost: 'high',
    icon: '🎯'
  },
  {
    id: 'gpt-4.1-mini',
    name: 'GPT-4.1 Mini',
    description: 'Efficient GPT-4.1 variant',
    contextWindow: '128K context',
    capabilities: 'GPT-4.1 improvements in a faster package, great for most tasks',
    speed: 'balanced',
    cost: 'medium',
    icon: '🎪'
  },
  {
    id: 'gpt-4.1-nano',
    name: 'GPT-4.1 Nano',
    description: 'Fastest GPT-4.1 model',
    contextWindow: '128K context',
    capabilities: 'Quick responses with GPT-4.1 enhancements, ideal for rapid iteration',
    speed: 'fast',
    cost: 'low',
    icon: '🏃'
  },
  {
    id: 'gpt-5',
    name: 'gpt-5',
    description: 'High power, best for complex tasks',
    contextWindow: '128K context',
    capabilities: 'Advanced reasoning, complex problem solving, code generation',
    speed: 'slow',
    cost: 'high',
    icon: '🚀'
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: 'Balanced, good for most tasks',
    contextWindow: '128K context',
    capabilities: 'General purpose, balanced performance',
    speed: 'balanced',
    cost: 'medium',
    icon: '💡'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: 'Fast & efficient, best for simple queries',
    contextWindow: '16K context',
    capabilities: 'Quick responses, simple tasks, chat',
    speed: 'fast',
    cost: 'low',
    icon: '💨'
  }
];

export type ModelId = typeof AVAILABLE_MODELS[number]['id'];

// Normalize model ids to strip optional "models/" prefix
const normalizeId = (id: string) => id.startsWith('models/') ? id.slice('models/'.length) : id;

export function getModelById(id: string): ModelInfo | undefined {
  const model = AVAILABLE_MODELS.find(model => normalizeId(model.id) === normalizeId(id));
  return model ? { ...model, id: normalizeId(model.id) } : undefined;
}

export function getDefaultModel(): ModelInfo {
  const model = AVAILABLE_MODELS.find(model => normalizeId(model.id) === 'gemini-2.5-flash') || AVAILABLE_MODELS[0];
  return { ...model, id: normalizeId(model.id) };
}
