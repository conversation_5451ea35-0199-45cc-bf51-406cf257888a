export type AgentEventType =
  | 'thinking'
  | 'agent_switch'
  | 'tool_call'
  | 'tool_result'
  | 'plan_created'
  | 'delegation_start'
  | 'delegation_end'
  | 'handoff'
  | 'search'
  | 'file_read'
  | 'file_write'
  | 'code_execution'
  | 'web_search'
  | 'progress'
  | 'message'
  | 'error'
  | 'complete'
  | 'trace_event'
  | 'token_update';

export interface AgentEvent {
  type: AgentEventType;
  timestamp: string;
  data: {
    message?: string;
    agent?: string;
    tool?: string;
    args?: any;
    result?: any;
    progress?: number;
    details?: any;
    tokens?: {
      used?: number;
      model?: string;
      isOutput?: boolean;
    };
    [key: string]: any;
  };
}
