import { z } from 'zod';

export type InterruptionType = 'pause' | 'cancel' | 'steer' | 'replace-next';

export interface InterruptionEvent {
  event: 'agent.interruption';
  type: InterruptionType;
  sessionId: string;
  stepId?: string;
  payload?: {
    message?: string;
    priority?: 'low' | 'medium' | 'high';
    constraints?: Record<string, any>;
  };
  timestamp: string; // ISO string
}

export type InterruptionStatus = 'pending' | 'acknowledged' | 'paused' | 'cancelled' | 'applied' | 'resumed' | 'rejected';

export interface InterruptionRecord {
  lastEvent: InterruptionEvent;
  status: InterruptionStatus;
  lastAppliedPayload?: InterruptionEvent['payload'];
  history: Array<{ status: InterruptionStatus; at: number; note?: string }>;
}

export const interruptionEventSchema = z.object({
  event: z.literal('agent.interruption'),
  type: z.enum(['pause', 'cancel', 'steer', 'replace-next']),
  sessionId: z.string().min(1),
  stepId: z.string().optional(),
  payload: z
    .object({
      message: z.string().optional(),
      priority: z.enum(['low', 'medium', 'high']).optional(),
      constraints: z.record(z.string(), z.any()).optional(),
    })
    .optional(),
  timestamp: z.string().refine((v) => !Number.isNaN(Date.parse(v)), 'timestamp must be ISO string'),
});

export type InterruptionEventInput = z.infer<typeof interruptionEventSchema>;
