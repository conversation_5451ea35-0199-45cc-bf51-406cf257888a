/**
 * OpenAI Connector types and interfaces for external service integrations
 */

export type ConnectorId =
  | 'connector_gmail'
  | 'connector_googlecalendar'
  | 'connector_googledrive'
  | 'connector_dropbox'
  | 'connector_microsoftteams'
  | 'connector_outlookcalendar'
  | 'connector_outlookemail'
  | 'connector_sharepoint';

export type ApprovalMode = 'always' | 'never' | {
  never: {
    tool_names: string[];
  };
};

export interface MCPTool {
  type: 'mcp';
  server_label: string;
  connector_id?: ConnectorId;
  server_url?: string;
  server_description?: string;
  authorization?: string;
  require_approval?: ApprovalMode;
  allowed_tools?: string[];
}

export interface MCPListToolsOutput {
  id: string;
  type: 'mcp_list_tools';
  server_label: string;
  tools: Array<{
    name: string;
    description: string;
    input_schema: Record<string, any>;
    annotations?: any;
  }>;
}

export interface MCPCallOutput {
  id: string;
  type: 'mcp_call';
  approval_request_id: string | null;
  arguments: string;
  error: string | null;
  name: string;
  output: string;
  server_label: string;
}

export interface MCPApprovalRequest {
  id: string;
  type: 'mcp_approval_request';
  arguments: string;
  name: string;
  server_label: string;
}

export interface MCPApprovalResponse {
  type: 'mcp_approval_response';
  approve: boolean;
  approval_request_id: string;
}

export interface ConnectorConfig {
  id: ConnectorId;
  name: string;
  description: string;
  scopes: string[];
  tools?: string[];
}

export const GOOGLE_CONNECTORS: Record<string, ConnectorConfig> = {
  gmail: {
    id: 'connector_gmail',
    name: 'Gmail',
    description: 'Access and search Gmail emails',
    scopes: [
      'https://www.googleapis.com/auth/gmail.modify',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ],
    tools: [
      'get_profile',
      'search_emails',
      'search_email_ids',
      'get_recent_emails',
      'read_email',
      'batch_read_email'
    ]
  },
  calendar: {
    id: 'connector_googlecalendar',
    name: 'Google Calendar',
    description: 'Access and search Google Calendar events',
    scopes: [
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ],
    tools: [
      'get_profile',
      'search_events',
      'fetch',
      'read_event'
    ]
  },
  drive: {
    id: 'connector_googledrive',
    name: 'Google Drive',
    description: 'Access and search Google Drive files',
    scopes: [
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ],
    tools: [
      'get_profile',
      'list_drives',
      'search',
      'recent_documents',
      'fetch'
    ]
  }
};

export interface OAuthToken {
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
  refresh_expires_at?: number;
  token_type: string;
  scope: string;
  id_token?: string;
}

export interface ConnectorSession {
  connector_id: ConnectorId;
  oauth_token: OAuthToken;
  user_email?: string;
  created_at: number;
  updated_at: number;
}
