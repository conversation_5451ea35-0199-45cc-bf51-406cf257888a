
import { createOpenAI } from '@ai-sdk/openai';
import { createOllama } from 'ai-sdk-ollama';
import { config } from '../../utils/config';

export type AIProvider = 'ollama' | 'openai';

export function getAIProvider(provider: AIProvider = config.ai.provider as AIProvider) {
  switch (provider) {
    case 'ollama':
      return createOllama({ baseURL: config.ollama.baseURL });
    case 'openai':
      return createOpenAI({
        apiKey: config.openai.apiKey,
      });
    default:
      const _exhaustiveCheck: never = provider;
      throw new Error(`Unsupported AI provider: ${provider}`);
  }
}
