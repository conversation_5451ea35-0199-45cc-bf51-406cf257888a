# Dante Reminder & Notification System

A comprehensive reminder and automated news monitoring system integrated with Dante's multi-agent architecture.

## Features

### 🔔 Reminder Types
- **One-time reminders**: Simple future notifications  
- **Recurring reminders**: Complex scheduling with cron expressions
- **News monitoring**: Automated polling of news sources for specific topics
- **Event-based reminders**: Triggered by external events or conditions

### 📰 News Monitoring Specialization
Perfect for automated coverage of:
- Product launches and announcements (e.g., "Google Pixel 10 launch")
- Live events and conferences
- Breaking news coverage
- Market updates and earnings
- Sports events and competitions

### 🎯 Coverage Types
- **Live Updates**: Real-time notifications as news breaks
- **Blow-by-Blow**: Detailed chronological coverage of events  
- **Periodic Summary**: Scheduled roundups (hourly, daily)
- **Final Report**: Comprehensive post-event analysis

## Quick Start

### Example: News Monitoring Setup
```typescript
// Create automated news monitoring for an event
const reminder = await reminderManager.createNewsMonitoringReminder({
  title: "Google Pixel 10 Launch Coverage",
  topic: "Google Pixel 10 launch August 20 2025",
  eventStartTime: new Date("2025-08-20T10:00:00-07:00"), // 10 AM PT
  eventEndTime: new Date("2025-08-20T14:00:00-07:00"),   // 2 PM PT
  summaryType: "blow_by_blow", // Detailed coverage
  pollingInterval: 15, // Check every 15 minutes
  notificationChannels: [NotificationChannel.WEB_UI],
});
```

### Example: Using the ReminderAgent
```typescript
// Via agent tools
await createNewsMonitoringTool.execute({
  title: "Live Election Coverage",
  topic: "2024 presidential election results",
  summaryType: "live_updates",
  pollingInterval: 30,
  keywordsToTrack: ["winner", "called", "results", "vote count"]
});
```

## Architecture

### Core Components

1. **ReminderManager** (`./ReminderManager.ts`)
   - Central orchestrator for all reminder operations
   - Handles creation, scheduling, and lifecycle management
   - Integrates with memory system for persistence

2. **ReminderScheduler** (`./scheduler/ReminderScheduler.ts`) 
   - Background task execution using node-cron
   - Handles different reminder types and scheduling patterns
   - Provides graceful recovery from server restarts

3. **NewsPollingService** (`./services/NewsPollingService.ts`)
   - Automated news monitoring with intelligent polling
   - Uses existing web search tools for comprehensive coverage
   - Content deduplication and relevance filtering

4. **NotificationDelivery** (`./notification/NotificationDelivery.ts`)
   - Multi-channel delivery (WebUI, file queue, memory store)
   - Real-time Server-Sent Events for web interface
   - Offline message queuing and persistence

5. **ReminderStorage** (`./storage/ReminderStorage.ts`)
   - Dual persistence: file system + memory system integration
   - Redundant storage for reliability
   - File locking for concurrent access safety

### Agent Integration

6. **ReminderAgent** (`../agents/ReminderAgent.ts`)
   - Specialized agent for reminder and scheduling tasks
   - Natural language processing for complex timing requests
   - Handoff integration with other agents (Research, Task Orchestrator)

## API Endpoints

### REST API (`/api/reminders`)
- `POST /` - Create reminder
- `POST /news-monitoring` - Create news monitoring  
- `GET /` - List reminders with filtering
- `GET /:id` - Get specific reminder details
- `PUT /:id` - Update reminder
- `POST /:id/cancel` - Cancel reminder
- `POST /:id/pause` - Pause reminder
- `POST /:id/resume` - Resume reminder
- `DELETE /:id` - Delete reminder
- `GET /system/status` - System status

### Real-time Notifications
- `GET /notifications/stream/:sessionId` - SSE stream for real-time updates
- `GET /notifications/:sessionId` - Get session notification history
- `POST /notifications/:sessionId/:notificationId/read` - Mark as read

## Persistence & Recovery

### Server Restart Recovery
- Automatic detection and rescheduling of missed reminders
- Recovery data saved on shutdown, loaded on startup
- Dual storage (file system + memory) prevents data loss

### Rate Limiting & Performance
- News polling rate limits (120 requests/hour default)
- Smart polling frequency based on event timing
- Content deduplication to avoid spam
- Concurrent session limits (10 max)

## Integration Points

### With Existing Dante Systems
- **Memory System**: Long-term storage and retrieval of reminders
- **Web Search Tools**: Powers news monitoring functionality  
- **Agent Handoffs**: Seamless delegation between specialized agents
- **Session Management**: User-specific reminder tracking
- **Task Management**: Coordinates with existing task system

### Event System
The reminder system emits events for integration:
```typescript
reminderManager.on('reminderExecuted', (reminder, result) => { ... });
reminderManager.on('newsPollingNeeded', (data) => { ... });
newsPollingService.on('pollingEvent', (event) => { ... });
```

## Usage Examples

### Scenario: Live Event Monitoring
**User Request**: "Monitor live coverage on Aug 20 and give a blow-by-blow summary"

**System Response**:
1. ReminderAgent analyzes request and extracts:
   - Event: Google Pixel 10 launch  
   - Date: August 20, 2025, 10:00 AM PT
   - Type: Live event monitoring with blow-by-blow coverage

2. NewsPollingService sets up:
   - Search queries: ["Google Pixel 10 launch August 20", "Made by Google 2025 live"]
   - Polling frequency: Every 15 minutes starting 2 hours before event
   - Content aggregation for chronological timeline

3. NotificationDelivery provides:
   - Real-time updates via Server-Sent Events
   - Progressive detail levels (breaking news → detailed analysis)
   - Historical timeline of coverage evolution

## Configuration

### Environment Variables
```bash
# Optional: Increase rate limits for heavy monitoring
REMINDER_MAX_NEWS_REQUESTS_PER_HOUR=240
REMINDER_MAX_CONCURRENT_SESSIONS=20

# Optional: Adjust default polling interval (minutes)  
REMINDER_DEFAULT_POLLING_INTERVAL=30

# Optional: Disable reminder system
REMINDER_SYSTEM_ENABLED=false
```

### System Limits
- Maximum active reminders: 100 per system
- Maximum concurrent news sessions: 10
- Maximum retries per reminder: 3
- Default timezone: UTC (configurable per reminder)

## Future Enhancements

- **Smart Scheduling**: AI-powered optimal timing suggestions
- **Multi-language Support**: News monitoring in different languages  
- **Advanced Filtering**: Machine learning-based relevance scoring
- **Mobile Push Notifications**: Integration with mobile apps
- **Calendar Integration**: Sync with external calendar systems
- **Voice Notifications**: Text-to-speech for audio alerts