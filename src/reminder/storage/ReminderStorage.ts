import { promises as fs } from 'fs';
import * as path from 'path';
import * as lockfile from 'proper-lockfile';
import { memoryManager } from '../../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../../memory/types';
import {
  <PERSON>mind<PERSON>,
  ReminderStorage as IReminderStorage,
  ReminderFilters,
  ReminderStatus,
  ReminderType,
} from '../types';

export class ReminderStorage implements IReminderStorage {
  private readonly REMINDER_DIR = path.join(process.cwd(), 'data', 'reminders');
  private readonly REMINDER_INDEX_FILE = path.join(this.REMINDER_DIR, 'index.json');
  private readonly MEMORY_CATEGORY = 'reminder-system';
  
  constructor() {
    this.ensureDirectoryExists();
  }

  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.mkdir(this.REMINDER_DIR, { recursive: true });
    } catch (error) {
      console.warn('Failed to create reminder directory:', error);
    }
  }

  private generateId(): string {
    return `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async withLock<T>(operation: () => Promise<T>): Promise<T> {
    let release: (() => Promise<void>) | undefined;
    
    try {
      // Wait up to 10 seconds for lock
      release = await lockfile.lock(this.REMINDER_INDEX_FILE, {
        retries: { 
          retries: 50, 
          factor: 1.1, 
          minTimeout: 100, 
          maxTimeout: 200 
        }
      });
      return await operation();
    } catch (error) {
      console.error('Lock operation failed:', error);
      throw error;
    } finally {
      if (release) {
        try {
          await release();
        } catch (releaseError) {
          console.warn('Failed to release lock:', releaseError);
        }
      }
    }
  }

  async create(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    const id = this.generateId();
    const now = new Date();
    
    const reminder: Reminder = {
      ...reminderData,
      id,
      createdAt: now,
      updatedAt: now,
      executionCount: 0,
      errorCount: 0,
    };

    // Store in both file system and memory system for redundancy
    await Promise.all([
      this.storeInFileSystem(reminder),
      this.storeInMemorySystem(reminder),
    ]);

    return reminder;
  }

  async get(id: string): Promise<Reminder | null> {
    try {
      // Try memory system first (faster)
      const memoryResult = await this.getFromMemorySystem(id);
      if (memoryResult) return memoryResult;

      // Fallback to file system
      return await this.getFromFileSystem(id);
    } catch (error) {
      console.error(`Failed to get reminder ${id}:`, error);
      return null;
    }
  }

  async update(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    return this.withLock(async () => {
      const existing = await this.get(id);
      if (!existing) return null;

      const updated: Reminder = {
        ...existing,
        ...updates,
        id, // Ensure ID doesn't change
        updatedAt: new Date(),
      };

      // Update in both storage systems
      await Promise.all([
        this.storeInFileSystem(updated),
        this.updateInMemorySystem(updated),
      ]);

      return updated;
    });
  }

  async delete(id: string): Promise<boolean> {
    return this.withLock(async () => {
      try {
        await Promise.all([
          this.deleteFromFileSystem(id),
          this.deleteFromMemorySystem(id),
        ]);
        return true;
      } catch (error) {
        console.error(`Failed to delete reminder ${id}:`, error);
        return false;
      }
    });
  }

  async list(filters?: ReminderFilters): Promise<Reminder[]> {
    try {
      // Get reminders from memory system (has better search capabilities)
      const memoryReminders = await this.listFromMemorySystem(filters);
      
      // If memory system has good results, use them
      if (memoryReminders.length > 0) {
        return memoryReminders;
      }

      // Fallback to file system
      return await this.listFromFileSystem(filters);
    } catch (error) {
      console.error('Failed to list reminders:', error);
      return [];
    }
  }

  async getByStatus(status: ReminderStatus): Promise<Reminder[]> {
    return this.list({ status });
  }

  async getActiveReminders(): Promise<Reminder[]> {
    return this.list({ status: ReminderStatus.ACTIVE });
  }

  async getNextExecutions(limit: number = 10): Promise<Reminder[]> {
    const activeReminders = await this.getActiveReminders();
    
    return activeReminders
      .filter(r => r.nextExecutionAt)
      .sort((a, b) => a.nextExecutionAt!.getTime() - b.nextExecutionAt!.getTime())
      .slice(0, limit);
  }

  // File system storage methods
  private async storeInFileSystem(reminder: Reminder): Promise<void> {
    const filePath = path.join(this.REMINDER_DIR, `${reminder.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(reminder, null, 2));
    await this.updateFileIndex(reminder);
  }

  private async getFromFileSystem(id: string): Promise<Reminder | null> {
    try {
      const filePath = path.join(this.REMINDER_DIR, `${id}.json`);
      const content = await fs.readFile(filePath, 'utf-8');
      const reminder = JSON.parse(content);
      
      // Parse dates
      return this.parseReminderDates(reminder);
    } catch (error) {
      return null;
    }
  }

  private async deleteFromFileSystem(id: string): Promise<void> {
    const filePath = path.join(this.REMINDER_DIR, `${id}.json`);
    try {
      await fs.unlink(filePath);
      await this.removeFromFileIndex(id);
    } catch (error) {
      // File might not exist, that's ok
    }
  }

  private async listFromFileSystem(filters?: ReminderFilters): Promise<Reminder[]> {
    try {
      const files = await fs.readdir(this.REMINDER_DIR);
      const reminderFiles = files.filter(f => f.endsWith('.json') && f !== 'index.json');
      
      const reminders = await Promise.all(
        reminderFiles.map(async (file) => {
          const id = file.replace('.json', '');
          return this.getFromFileSystem(id);
        })
      );
      
      return this.applyFilters(reminders.filter(Boolean) as Reminder[], filters);
    } catch (error) {
      console.error('Failed to list from file system:', error);
      return [];
    }
  }

  private async updateFileIndex(reminder: Reminder): Promise<void> {
    try {
      let index: { [id: string]: { title: string; status: string; type: string; updatedAt: string } } = {};
      
      try {
        const content = await fs.readFile(this.REMINDER_INDEX_FILE, 'utf-8');
        index = JSON.parse(content);
      } catch {
        // Index doesn't exist yet
      }
      
      index[reminder.id] = {
        title: reminder.title,
        status: reminder.status,
        type: reminder.type,
        updatedAt: reminder.updatedAt.toISOString(),
      };
      
      await fs.writeFile(this.REMINDER_INDEX_FILE, JSON.stringify(index, null, 2));
    } catch (error) {
      console.warn('Failed to update file index:', error);
    }
  }

  private async removeFromFileIndex(id: string): Promise<void> {
    try {
      const content = await fs.readFile(this.REMINDER_INDEX_FILE, 'utf-8');
      const index = JSON.parse(content);
      delete index[id];
      await fs.writeFile(this.REMINDER_INDEX_FILE, JSON.stringify(index, null, 2));
    } catch (error) {
      console.warn('Failed to remove from file index:', error);
    }
  }

  // Memory system storage methods
  private async storeInMemorySystem(reminder: Reminder): Promise<void> {
    try {
      await memoryManager.create(
        MemoryType.SEMANTIC,
        {
          id: reminder.id,
          title: reminder.title,
          description: reminder.description,
          type: reminder.type,
          status: reminder.status,
          schedule: reminder.schedule,
          action: reminder.action,
          reminder: reminder, // Store full reminder object
        },
        {
          priority: this.mapPriorityToMemoryPriority(reminder.priority),
          tags: [...reminder.tags, this.MEMORY_CATEGORY, `reminder-${reminder.type}`, `status-${reminder.status}`, `id:${reminder.id}`],
          confidence: 1.0,
        }
      );
    } catch (error) {
      console.warn('Failed to store reminder in memory system:', error);
    }
  }

  private async getFromMemorySystem(id: string): Promise<Reminder | null> {
    try {
      const memories = await memoryManager.recall(`reminder id:${id}`, 1);
      
      if (memories.length > 0) {
        const memory = memories[0];
        if (memory.content && typeof memory.content === 'object' && 'reminder' in memory.content) {
          return this.parseReminderDates((memory.content as any).reminder);
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to get reminder from memory system:', error);
      return null;
    }
  }

  private async updateInMemorySystem(reminder: Reminder): Promise<void> {
    try {
      // Since we can't easily update in the memory system, we'll delete and recreate
      await this.deleteFromMemorySystem(reminder.id);
      await this.storeInMemorySystem(reminder);
    } catch (error) {
      console.warn('Failed to update reminder in memory system:', error);
    }
  }

  private async deleteFromMemorySystem(id: string): Promise<void> {
    try {
      const memories = await memoryManager.recall(`reminder id:${id}`, 5);
      
      for (const memory of memories) {
        try {
          await memoryManager.delete(memory.metadata.id);
        } catch (deleteError) {
          console.warn(`Failed to delete memory ${memory.metadata.id}:`, deleteError);
        }
      }
    } catch (error) {
      console.warn('Failed to delete reminder from memory system:', error);
    }
  }

  private async listFromMemorySystem(filters?: ReminderFilters): Promise<Reminder[]> {
    try {
      const searchQuery = this.buildMemorySearchQuery(filters);
      const memories = await memoryManager.recall(searchQuery, 100);
      
      const reminders = memories
        .map(memory => {
          if (memory.content && typeof memory.content === 'object' && 'reminder' in memory.content) {
            return this.parseReminderDates((memory.content as any).reminder);
          }
          return null;
        })
        .filter(Boolean) as Reminder[];
      
      return this.applyFilters(reminders, filters);
    } catch (error) {
      console.warn('Failed to list reminders from memory system:', error);
      return [];
    }
  }

  // Helper methods
  private parseReminderDates(reminder: any): Reminder {
    return {
      ...reminder,
      createdAt: new Date(reminder.createdAt),
      updatedAt: new Date(reminder.updatedAt),
      lastExecutedAt: reminder.lastExecutedAt ? new Date(reminder.lastExecutedAt) : undefined,
      nextExecutionAt: reminder.nextExecutionAt ? new Date(reminder.nextExecutionAt) : undefined,
      schedule: {
        ...reminder.schedule,
        executeAt: reminder.schedule.executeAt ? new Date(reminder.schedule.executeAt) : undefined,
        pollingStartTime: reminder.schedule.pollingStartTime ? new Date(reminder.schedule.pollingStartTime) : undefined,
        pollingEndTime: reminder.schedule.pollingEndTime ? new Date(reminder.schedule.pollingEndTime) : undefined,
      },
    };
  }

  private mapPriorityToMemoryPriority(priority: string): MemoryPriority {
    switch (priority) {
      case 'critical': return MemoryPriority.CRITICAL;
      case 'high': return MemoryPriority.HIGH;
      case 'medium': return MemoryPriority.MEDIUM;
      case 'low': return MemoryPriority.LOW;
      default: return MemoryPriority.MEDIUM;
    }
  }

  private buildMemorySearchQuery(filters?: ReminderFilters): string {
    let query = this.MEMORY_CATEGORY;
    
    if (filters?.status) {
      query += ` status-${filters.status}`;
    }
    
    if (filters?.type) {
      query += ` reminder-${filters.type}`;
    }
    
    if (filters?.tags?.length) {
      query += ` ${filters.tags.join(' ')}`;
    }
    
    return query;
  }

  private applyFilters(reminders: Reminder[], filters?: ReminderFilters): Reminder[] {
    if (!filters) return reminders;
    
    return reminders.filter(reminder => {
      if (filters.status && reminder.status !== filters.status) return false;
      if (filters.type && reminder.type !== filters.type) return false;
      if (filters.createdBy && reminder.createdBy !== filters.createdBy) return false;
      if (filters.sessionId && reminder.sessionId !== filters.sessionId) return false;
      if (filters.priority && reminder.priority !== filters.priority) return false;
      
      if (filters.tags?.length) {
        const hasAllTags = filters.tags.every(tag => reminder.tags.includes(tag));
        if (!hasAllTags) return false;
      }
      
      if (filters.createdAfter && reminder.createdAt < filters.createdAfter) return false;
      if (filters.createdBefore && reminder.createdAt > filters.createdBefore) return false;
      if (filters.nextExecutionBefore && (!reminder.nextExecutionAt || reminder.nextExecutionAt > filters.nextExecutionBefore)) return false;
      
      return true;
    });
  }
}