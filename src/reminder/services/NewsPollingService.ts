import { EventEmitter } from 'events';
import {
  NewsMonitoringConfig,
  NewsUpdate,
  NewsPollingEvent,
  Remind<PERSON>,
} from '../types';
import { executeWebSearch } from '../../tools/webSearch';
import { webContentChunkTool } from '../../tools/webContentChunk';

interface PollingSession {
  reminderId: string;
  config: NewsMonitoringConfig;
  lastUpdate: Date;
  updateCount: number;
  seenUrls: Set<string>;
  keyDevelopments: NewsUpdate[];
  isActive: boolean;
  pollingInterval?: NodeJS.Timeout;
}

export class NewsPollingService extends EventEmitter {
  private activeSessions = new Map<string, PollingSession>();
  private rateLimiter = new Map<string, { requests: number; resetTime: number }>();
  private readonly MAX_REQUESTS_PER_HOUR = 120;
  private readonly MAX_CONCURRENT_SESSIONS = 10;

  constructor() {
    super();
    this.setupRateLimitReset();
  }

  async startPolling(reminder: Reminder): Promise<boolean> {
    const { action } = reminder;
    
    if (!action.newsConfig) {
      console.error(`Reminder ${reminder.id} missing news configuration`);
      return false;
    }

    if (this.activeSessions.size >= this.MAX_CONCURRENT_SESSIONS) {
      console.warn(`Maximum concurrent news polling sessions reached (${this.MAX_CONCURRENT_SESSIONS})`);
      return false;
    }

    // Check if already polling for this reminder
    if (this.activeSessions.has(reminder.id)) {
      console.warn(`Already polling for reminder ${reminder.id}`);
      return false;
    }

    const session: PollingSession = {
      reminderId: reminder.id,
      config: action.newsConfig,
      lastUpdate: new Date(),
      updateCount: 0,
      seenUrls: new Set(),
      keyDevelopments: [],
      isActive: true,
    };

    this.activeSessions.set(reminder.id, session);
    
    console.log(`📰 Started news polling for: ${action.newsConfig.topic} (${reminder.id})`);
    
    // Perform initial poll
    await this.performPoll(session);
    
    // Setup recurring polling
    this.setupRecurringPoll(session);
    
    this.emitEvent('started', reminder.id);
    return true;
  }

  async stopPolling(reminderId: string): Promise<boolean> {
    const session = this.activeSessions.get(reminderId);
    if (!session) return false;

    session.isActive = false;
    
    if (session.pollingInterval) {
      clearInterval(session.pollingInterval);
    }

    this.activeSessions.delete(reminderId);
    
    console.log(`📰 Stopped news polling for reminder: ${reminderId}`);
    this.emitEvent('stopped', reminderId);
    return true;
  }

  async performManualPoll(reminderId: string): Promise<NewsUpdate[]> {
    const session = this.activeSessions.get(reminderId);
    if (!session) {
      throw new Error(`No active polling session for reminder: ${reminderId}`);
    }

    return await this.performPoll(session);
  }

  getActiveSession(reminderId: string): PollingSession | undefined {
    return this.activeSessions.get(reminderId);
  }

  getAllActiveSessions(): PollingSession[] {
    return Array.from(this.activeSessions.values());
  }

  getSessionStats(reminderId: string) {
    const session = this.activeSessions.get(reminderId);
    if (!session) return null;

    return {
      reminderId,
      topic: session.config.topic,
      updateCount: session.updateCount,
      lastUpdate: session.lastUpdate,
      seenUrlCount: session.seenUrls.size,
      keyDevelopmentCount: session.keyDevelopments.length,
      isActive: session.isActive,
    };
  }

  private async performPoll(session: PollingSession): Promise<NewsUpdate[]> {
    if (!session.isActive) return [];

    const { reminderId, config } = session;
    
    try {
      // Check rate limiting
      if (!this.checkRateLimit()) {
        console.warn(`Rate limit exceeded for news polling`);
        this.emitEvent('error', reminderId, undefined, 'Rate limit exceeded');
        return [];
      }

      console.log(`🔍 Polling news for: ${config.topic}`);
      
      const updates: NewsUpdate[] = [];
      
      // Search with each query
      for (const query of config.searchQueries) {
        try {
          const searchResults = await this.searchNews(query, config);
          const newUpdates = await this.processSearchResults(session, searchResults, query);
          updates.push(...newUpdates);
          
          // Update rate limit counter
          this.updateRateLimit();
          
          // Small delay between queries to be respectful
          await this.delay(1000);
          
        } catch (error) {
          console.error(`Search failed for query "${query}":`, error);
        }
      }

      // Process and filter updates
      const filteredUpdates = this.filterAndRankUpdates(session, updates);
      
      if (filteredUpdates.length > 0) {
        console.log(`📈 Found ${filteredUpdates.length} new updates for: ${config.topic}`);
        
        // Store key developments
        session.keyDevelopments.push(...filteredUpdates);
        session.updateCount += filteredUpdates.length;
        session.lastUpdate = new Date();

        // Emit update events
        for (const update of filteredUpdates) {
          this.emitEvent('update_found', reminderId, update);
        }
      } else {
        this.emitEvent('no_updates', reminderId);
      }

      return filteredUpdates;
      
    } catch (error) {
      console.error(`News polling failed for ${reminderId}:`, error);
      this.emitEvent('error', reminderId, undefined, error instanceof Error ? error.message : 'Unknown error');
      return [];
    }
  }

  private async searchNews(query: string, config: NewsMonitoringConfig): Promise<any> {
    // Use the existing web search functionality
    const searchOptions = {
      query: this.optimizeSearchQuery(query, config),
      maxResults: 10,
      fetchContent: true,
      contentDepth: 'detailed' as const,
      enableChunking: false,
    };

    const result = await executeWebSearch(searchOptions);
    return JSON.parse(result);
  }

  private optimizeSearchQuery(baseQuery: string, config: NewsMonitoringConfig): string {
    let query = baseQuery;
    
    // Add time-based modifiers for live events
    if (config.eventDate) {
      const eventDate = new Date(config.eventDate);
      const today = new Date();
      
      if (this.isSameDay(eventDate, today)) {
        query += ' today live';
      } else if (eventDate > today) {
        query += ' latest news';
      }
    }

    // Add keywords if specified
    if (config.keywordsToTrack?.length) {
      const keywords = config.keywordsToTrack.slice(0, 3).join(' OR ');
      query += ` (${keywords})`;
    }

    // Add recency filters
    const now = new Date();
    const hoursSinceLastUpdate = (now.getTime() - new Date().getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceLastUpdate < 2) {
      query += ' site:twitter.com OR site:reddit.com OR "breaking news"';
    } else {
      query += ' "latest" OR "update" OR "news"';
    }

    return query;
  }

  private async processSearchResults(session: PollingSession, searchResult: any, query: string): Promise<NewsUpdate[]> {
    const updates: NewsUpdate[] = [];
    
    if (!searchResult.success || !searchResult.results?.length) {
      return updates;
    }

    for (const result of searchResult.results) {
      try {
        // Skip if we've already seen this URL
        if (session.seenUrls.has(result.url)) {
          continue;
        }

        // Mark as seen
        session.seenUrls.add(result.url);

        // Analyze relevance
        const relevanceScore = this.calculateRelevance(result, session.config);
        if (relevanceScore < 0.6) {
          continue; // Skip low relevance results
        }

        // Extract key information
        const newsUpdate = await this.extractNewsUpdate(session, result, query, relevanceScore);
        if (newsUpdate) {
          updates.push(newsUpdate);
        }
        
      } catch (error) {
        console.warn(`Failed to process search result:`, error);
      }
    }

    return updates;
  }

  private async extractNewsUpdate(session: PollingSession, result: any, query: string, relevanceScore: number): Promise<NewsUpdate | null> {
    try {
      const content = result.fullContent || result.description || '';
      const title = result.title || '';
      
      // Extract key points using content analysis
      const keyPoints = this.extractKeyPoints(content, session.config);
      
      // Determine change type
      const changeType = this.detectChangeType(content, title, session.keyDevelopments);
      
      // Analyze sentiment if specified
      const sentiment = this.analyzeSentiment(content);

      const update: NewsUpdate = {
        id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        reminderId: session.reminderId,
        timestamp: new Date(),
        title,
        summary: this.generateSummary(content, keyPoints),
        fullContent: content,
        sources: [{
          url: result.url,
          title,
          publishedAt: this.extractPublishDate(result),
          relevanceScore,
        }],
        changeType,
        keyPoints,
        sentiment,
      };

      return update;
      
    } catch (error) {
      console.warn('Failed to extract news update:', error);
      return null;
    }
  }

  private calculateRelevance(result: any, config: NewsMonitoringConfig): number {
    let score = 0;
    const title = result.title?.toLowerCase() || '';
    const description = result.description?.toLowerCase() || '';
    const content = `${title} ${description}`;
    
    // Topic relevance
    const topicWords = config.topic.toLowerCase().split(' ');
    const topicMatches = topicWords.filter(word => content.includes(word)).length;
    score += (topicMatches / topicWords.length) * 0.4;
    
    // Keyword relevance
    if (config.keywordsToTrack?.length) {
      const keywordMatches = config.keywordsToTrack.filter(keyword => 
        content.includes(keyword.toLowerCase())
      ).length;
      score += (keywordMatches / config.keywordsToTrack.length) * 0.3;
    }
    
    // Recency bonus
    const publishDate = this.extractPublishDate(result);
    if (publishDate) {
      const hoursAgo = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60);
      if (hoursAgo < 1) score += 0.2;
      else if (hoursAgo < 6) score += 0.1;
    }
    
    // Source credibility bonus
    const url = result.url?.toLowerCase() || '';
    const credibleSources = ['reuters.com', 'apnews.com', 'bbc.com', 'cnn.com', 'nytimes.com'];
    if (credibleSources.some(source => url.includes(source))) {
      score += 0.1;
    }
    
    return Math.min(1, score);
  }

  private extractKeyPoints(content: string, config: NewsMonitoringConfig): string[] {
    const keyPoints: string[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
    
    // Look for sentences containing topic keywords
    const topicWords = config.topic.toLowerCase().split(' ');
    const trackingWords = config.keywordsToTrack?.map(k => k.toLowerCase()) || [];
    const allKeywords = [...topicWords, ...trackingWords];
    
    for (const sentence of sentences.slice(0, 10)) { // Limit to first 10 sentences
      const lowerSentence = sentence.toLowerCase();
      const keywordMatches = allKeywords.filter(keyword => lowerSentence.includes(keyword)).length;
      
      if (keywordMatches >= 2 || (keywordMatches >= 1 && sentence.length < 200)) {
        keyPoints.push(sentence.trim());
        if (keyPoints.length >= 5) break; // Limit to 5 key points
      }
    }
    
    return keyPoints;
  }

  private detectChangeType(content: string, title: string, previousUpdates: NewsUpdate[]): NewsUpdate['changeType'] {
    const text = `${title} ${content}`.toLowerCase();
    
    // New development indicators
    if (text.includes('breaking') || text.includes('just in') || text.includes('announced')) {
      return 'new_development';
    }
    
    // Update indicators
    if (text.includes('update') || text.includes('latest') || text.includes('now')) {
      return 'update';
    }
    
    // Correction indicators
    if (text.includes('correction') || text.includes('clarification') || text.includes('retraction')) {
      return 'correction';
    }
    
    // Confirmation indicators
    if (text.includes('confirmed') || text.includes('official') || text.includes('statement')) {
      return 'confirmation';
    }
    
    return 'new_development';
  }

  private analyzeSentiment(content: string): 'positive' | 'negative' | 'neutral' {
    const text = content.toLowerCase();
    
    const positiveWords = ['success', 'achievement', 'breakthrough', 'excellent', 'great', 'wonderful', 'impressive'];
    const negativeWords = ['failure', 'problem', 'issue', 'concern', 'disappointing', 'poor', 'terrible'];
    
    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;
    
    if (positiveCount > negativeCount + 1) return 'positive';
    if (negativeCount > positiveCount + 1) return 'negative';
    return 'neutral';
  }

  private generateSummary(content: string, keyPoints: string[]): string {
    if (keyPoints.length > 0) {
      return keyPoints[0].substring(0, 200) + (keyPoints[0].length > 200 ? '...' : '');
    }
    
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
    if (sentences.length > 0) {
      return sentences[0].substring(0, 200) + (sentences[0].length > 200 ? '...' : '');
    }
    
    return content.substring(0, 200) + (content.length > 200 ? '...' : '');
  }

  private extractPublishDate(result: any): Date | undefined {
    // Try to extract publish date from various fields
    if (result.publishedAt) return new Date(result.publishedAt);
    if (result.date) return new Date(result.date);
    
    // Default to current time for live monitoring
    return new Date();
  }

  private filterAndRankUpdates(session: PollingSession, updates: NewsUpdate[]): NewsUpdate[] {
    const { config } = session;
    
    // Remove duplicates based on content similarity
    const filtered = this.removeDuplicates(updates);
    
    // Sort by relevance and recency
    filtered.sort((a, b) => {
      const scoreA = a.sources[0]?.relevanceScore || 0;
      const scoreB = b.sources[0]?.relevanceScore || 0;
      
      if (Math.abs(scoreA - scoreB) > 0.1) {
        return scoreB - scoreA; // Higher relevance first
      }
      
      return b.timestamp.getTime() - a.timestamp.getTime(); // More recent first
    });
    
    // Apply rate limiting for updates
    const maxUpdatesPerHour = config.maxUpdatesPerHour || 10;
    const recentUpdates = session.keyDevelopments.filter(update => 
      Date.now() - update.timestamp.getTime() < 60 * 60 * 1000
    );
    
    const remainingSlots = Math.max(0, maxUpdatesPerHour - recentUpdates.length);
    
    return filtered.slice(0, remainingSlots);
  }

  private removeDuplicates(updates: NewsUpdate[]): NewsUpdate[] {
    const seen = new Set<string>();
    const unique: NewsUpdate[] = [];
    
    for (const update of updates) {
      // Create a signature based on title and key content
      const signature = this.createContentSignature(update.title, update.summary);
      
      if (!seen.has(signature)) {
        seen.add(signature);
        unique.push(update);
      }
    }
    
    return unique;
  }

  private createContentSignature(title: string, summary: string): string {
    const text = `${title} ${summary}`.toLowerCase();
    const words = text.split(/\W+/).filter(word => word.length > 3);
    const significantWords = words.slice(0, 10).sort();
    return significantWords.join('_');
  }

  private setupRecurringPoll(session: PollingSession): void {
    const intervalMs = session.config.pollingInterval * 60 * 1000; // Convert to milliseconds
    
    session.pollingInterval = setInterval(async () => {
      if (session.isActive) {
        // Check if we're still within the polling window
        const now = new Date();
        
        if (session.config.pollingEndTime && now > session.config.pollingEndTime) {
          console.log(`📰 Polling ended for ${session.config.topic}: reached end time`);
          await this.stopPolling(session.reminderId);
          return;
        }
        
        await this.performPoll(session);
      }
    }, intervalMs);
  }

  private checkRateLimit(): boolean {
    const now = Date.now();
    const hourKey = Math.floor(now / (1000 * 60 * 60));
    
    const current = this.rateLimiter.get(hourKey.toString()) || { requests: 0, resetTime: now + 60 * 60 * 1000 };
    
    return current.requests < this.MAX_REQUESTS_PER_HOUR;
  }

  private updateRateLimit(): void {
    const now = Date.now();
    const hourKey = Math.floor(now / (1000 * 60 * 60));
    
    const current = this.rateLimiter.get(hourKey.toString()) || { requests: 0, resetTime: now + 60 * 60 * 1000 };
    current.requests++;
    
    this.rateLimiter.set(hourKey.toString(), current);
  }

  private setupRateLimitReset(): void {
    // Clean up old rate limit entries every hour
    setInterval(() => {
      const now = Date.now();
      const currentHour = Math.floor(now / (1000 * 60 * 60));
      
      for (const [key, data] of this.rateLimiter.entries()) {
        if (parseInt(key) < currentHour - 1) {
          this.rateLimiter.delete(key);
        }
      }
    }, 60 * 60 * 1000); // Every hour
  }

  private emitEvent(type: NewsPollingEvent['type'], reminderId: string, newsUpdate?: NewsUpdate, error?: string): void {
    const event: NewsPollingEvent = {
      type,
      reminderId,
      newsUpdate,
      error,
      timestamp: new Date(),
    };
    
    this.emit('pollingEvent', event);
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const newsPollingService = new NewsPollingService();