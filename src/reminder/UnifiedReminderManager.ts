// Unified Reminder Manager (following MemoryManager pattern)
// Provides seamless cross-platform reminder access like memoryTools

import { IReminderStore, ReminderStoreConfig } from './interfaces/IReminderStore';
import { MemoryReminderStore } from './stores/MemoryReminderStore';
import { <PERSON><PERSON><PERSON>, <PERSON>minderFilt<PERSON>, ReminderStatus, NotificationChannel } from './types';
import { EventEmitter } from 'events';

export interface UnifiedReminderConfig extends ReminderStoreConfig {
  enableQdrantStorage?: boolean;
  enableApiStorage?: boolean;
  enableFileStorage?: boolean;
  primaryStore?: 'api' | 'qdrant' | 'hybrid';
  enableScheduling?: boolean;
}

export class UnifiedReminderManager extends EventEmitter {
  private static instance: UnifiedReminderManager;
  private primaryStore: IReminderStore;
  private config: UnifiedReminderConfig;
  private isInitialized = false;
  private initializationPromise?: Promise<void>;

  private constructor(config: UnifiedReminderConfig = {}) {
    super();
    
    // Detect environment (following MemoryManager pattern)
    const isBrowser = typeof window !== 'undefined';
    
    this.config = {
      // Guard access to `process` for environments where it's not defined (e.g. browsers)
      enableQdrantStorage:
        config.enableQdrantStorage ?? (typeof process !== 'undefined' && process.env && process.env.QDRANT_URL ? true : false),
      enableApiStorage: config.enableApiStorage ?? isBrowser,
      enableFileStorage: config.enableFileStorage ?? !isBrowser,
      primaryStore: config.primaryStore ?? (isBrowser ? 'api' : 'hybrid'),
      enableScheduling: config.enableScheduling ?? !isBrowser,
      ...config,
    };

    // Choose storage strategy based on environment (like MemoryManager)
    if (isBrowser) {
      console.log('🌐 Browser environment: Using memory-based reminder storage');
      this.primaryStore = new MemoryReminderStore(this.config);
      
      // No secondary store needed - memory system works everywhere
    } else {
      console.log('🖥️ Server environment: Using memory-based reminder storage');
      this.primaryStore = new MemoryReminderStore(this.config);
      
      // No secondary store needed - memory system provides cross-platform access
    }
    
    console.log(`📋 UnifiedReminderManager configured: ${this.config.primaryStore} storage`);
  }

  static getInstance(config?: UnifiedReminderConfig): UnifiedReminderManager {
    if (!UnifiedReminderManager.instance) {
      UnifiedReminderManager.instance = new UnifiedReminderManager(config);
    }
    return UnifiedReminderManager.instance;
  }

  async initialize(): Promise<void> {
    // Prevent multiple simultaneous initializations
    if (this.isInitialized) return;
    if (this.initializationPromise) return this.initializationPromise;

    this.initializationPromise = this.performInitialization();
    await this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    try {
      console.log('🚀 Initializing Unified Reminder Manager...');
      
      // Initialize primary store
      await this.primaryStore.initialize();
      console.log('✅ Primary reminder store initialized');
      
      // Memory system provides unified storage across all environments
      
      this.isInitialized = true;
      console.log('✅ Unified Reminder Manager initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize Unified Reminder Manager:', error);
      throw error;
    }
  }

  // Core reminder operations (unified interface)
  async createReminder(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    await this.initialize();
    
    try {
      // Create in primary store
      const reminder = await this.primaryStore.create(reminderData);
      
      // Memory system provides built-in cross-platform access
      console.log(`🧠 Reminder stored in unified memory system: ${reminder.id} (cross-platform accessible)`);
      
      this.emit('reminderCreated', reminder);
      return reminder;
    } catch (error) {
      console.error('Failed to create reminder:', error);
      throw error;
    }
  }

  async createNewsMonitoring(config: {
    title: string;
    topic: string;
    eventDate?: Date;
    eventStartTime?: Date;
    eventEndTime?: Date;
    summaryType?: string;
    pollingInterval?: number;
    searchQueries?: string[];
    keywordsToTrack?: string[];
    notificationChannels?: NotificationChannel[];
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder> {
    await this.initialize();
    
    try {
      // Use primary store's news monitoring creation
      const reminder = await this.primaryStore.createNewsMonitoring(config);
      
      // Memory system provides automatic cross-platform access
      console.log(`🧠 News monitoring stored in unified memory: ${reminder.id}`);
      
      this.emit('newsMonitoringCreated', reminder);
      return reminder;
    } catch (error) {
      console.error('Failed to create news monitoring:', error);
      throw error;
    }
  }

  async getReminder(id: string): Promise<Reminder | null> {
    await this.initialize();
    
    try {
      // Try primary store first
      let reminder = await this.primaryStore.get(id);
      
      // Memory system provides comprehensive search across all environments
      
      return reminder;
    } catch (error) {
      console.error(`Failed to get reminder ${id}:`, error);
      return null;
    }
  }

  async listReminders(filters?: ReminderFilters): Promise<Reminder[]> {
    await this.initialize();
    
    try {
      // Get from unified memory system (works across all environments)
      return await this.primaryStore.list(filters);
    } catch (error) {
      console.error('Failed to list reminders:', error);
      return [];
    }
  }

  async updateReminder(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    await this.initialize();
    
    try {
      // Update in primary store
      const updated = await this.primaryStore.update(id, updates);
      
      // Memory system automatically maintains cross-platform consistency
      
      if (updated) {
        this.emit('reminderUpdated', updated);
      }
      
      return updated;
    } catch (error) {
      console.error(`Failed to update reminder ${id}:`, error);
      return null;
    }
  }

  async deleteReminder(id: string): Promise<boolean> {
    await this.initialize();
    
    try {
      // Delete from primary store
      const deleted = await this.primaryStore.delete(id);
      
      // Memory system handles deletion across all environments
      
      if (deleted) {
        this.emit('reminderDeleted', id);
      }
      
      return deleted;
    } catch (error) {
      console.error(`Failed to delete reminder ${id}:`, error);
      return false;
    }
  }

  // Convenience methods
  async getActiveReminders(): Promise<Reminder[]> {
    return this.listReminders({ status: ReminderStatus.ACTIVE });
  }

  async getSystemStatus() {
    await this.initialize();
    
    const stats = await this.primaryStore.getStats();
    
    return {
      initialized: this.isInitialized,
      environment: typeof window !== 'undefined' ? 'browser' : 'server',
      storageType: 'unified_memory_system',
      totalReminders: stats.totalReminders,
      activeReminders: stats.activeReminders,
      scheduledReminders: stats.scheduledReminders,
      dataIntegrity: {
        protectedFromConsolidation: true,
        schemaValidation: true,
        crossPlatformAccess: true,
      },
      config: this.config,
    };
  }

  async cleanup(): Promise<void> {
    if (!this.isInitialized) return;
    
    console.log('🛑 Cleaning up Unified Reminder Manager...');
    
    try {
      await this.primaryStore.cleanup();
      
      this.isInitialized = false;
      console.log('✅ Unified Reminder Manager cleanup complete');
      this.emit('cleanup');
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}

// Export singleton instance (following MemoryManager pattern)
export const unifiedReminderManager = UnifiedReminderManager.getInstance();