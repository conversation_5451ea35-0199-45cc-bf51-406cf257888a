// Core reminder types and interfaces

export enum ReminderType {
  ONE_TIME = 'one_time',
  RECURRING = 'recurring',
  NEWS_MONITORING = 'news_monitoring',
  EVENT_BASED = 'event_based',
}

export enum ReminderStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  PAUSED = 'paused',
}

export enum NotificationChannel {
  WEB_UI = 'web_ui',
  FILE_QUEUE = 'file_queue',
  MEMORY_STORE = 'memory_store',
}

export interface ReminderSchedule {
  // For one-time reminders
  executeAt?: Date;
  
  // For recurring reminders (cron format)
  cronExpression?: string;
  
  // For news monitoring
  pollingInterval?: number; // minutes
  pollingStartTime?: Date;
  pollingEndTime?: Date;
  
  // Timezone support
  timezone?: string;
}

export interface NewsMonitoringConfig {
  topic: string;
  searchQueries: string[];
  pollingInterval: number; // minutes
  maxUpdatesPerHour?: number;
  eventDate?: Date;
  eventStartTime?: Date;
  eventEndTime?: Date;
  pollingStartTime?: Date;
  pollingEndTime?: Date;
  summaryType: 'live_updates' | 'blow_by_blow' | 'periodic_summary' | 'final_report';
  keywordsToTrack?: string[];
  sourcesToMonitor?: string[];
}

export interface ReminderAction {
  type: 'notification' | 'web_search' | 'news_poll' | 'agent_handoff' | 'custom';
  payload: any;
  notificationChannels: NotificationChannel[];
  
  // For web search actions
  searchQuery?: string;
  searchOptions?: {
    maxResults?: number;
    fetchContent?: boolean;
    contentDepth?: 'summary' | 'detailed' | 'full';
  };
  
  // For news polling actions
  newsConfig?: NewsMonitoringConfig;
  
  // For agent handoff actions
  targetAgent?: string;
  handoffMessage?: string;
}

export interface Reminder {
  id: string;
  title: string;
  description: string;
  type: ReminderType;
  status: ReminderStatus;
  schedule: ReminderSchedule;
  action: ReminderAction;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string; // user or agent identifier
  sessionId?: string;
  
  // Execution tracking
  lastExecutedAt?: Date;
  nextExecutionAt?: Date;
  executionCount: number;
  maxExecutions?: number;
  
  // Error handling
  errorCount: number;
  lastError?: string;
  maxRetries?: number;
  
  // Context and memory
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  memoryContext?: any; // Additional context for memory storage
}

export interface ReminderExecutionResult {
  success: boolean;
  executedAt: Date;
  duration: number;
  output?: any;
  error?: string;
  nextExecutionAt?: Date;
  shouldReschedule: boolean;
}

export interface NewsUpdate {
  id: string;
  reminderId: string;
  timestamp: Date;
  title: string;
  summary: string;
  fullContent?: string;
  sources: Array<{
    url: string;
    title: string;
    publishedAt?: Date;
    relevanceScore: number;
  }>;
  changeType: 'new_development' | 'update' | 'correction' | 'confirmation';
  keyPoints: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface NotificationMessage {
  id: string;
  reminderId: string;
  type: 'reminder' | 'news_update' | 'error' | 'system';
  title: string;
  content: string;
  metadata?: any;
  channels: NotificationChannel[];
  createdAt: Date;
  deliveredAt?: Date;
  readAt?: Date;
  sessionId?: string;
}

// Configuration interfaces
export interface ReminderSystemConfig {
  enabled: boolean;
  maxActiveReminders: number;
  defaultPollingInterval: number; // minutes
  newsMonitoringEnabled: boolean;
  persistenceEnabled: boolean;
  recoveryEnabled: boolean;
  defaultTimezone: string;
  rateLimiting: {
    maxNewsRequestsPerHour: number;
    maxReminderExecutionsPerMinute: number;
  };
}

// Storage interfaces
export interface ReminderStorage {
  create(reminder: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder>;
  get(id: string): Promise<Reminder | null>;
  update(id: string, updates: Partial<Reminder>): Promise<Reminder | null>;
  delete(id: string): Promise<boolean>;
  list(filters?: ReminderFilters): Promise<Reminder[]>;
  getByStatus(status: ReminderStatus): Promise<Reminder[]>;
  getActiveReminders(): Promise<Reminder[]>;
  getNextExecutions(limit?: number): Promise<Reminder[]>;
}

export interface ReminderFilters {
  status?: ReminderStatus;
  type?: ReminderType;
  createdBy?: string;
  sessionId?: string;
  tags?: string[];
  priority?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  nextExecutionBefore?: Date;
}

// Scheduler interfaces
export interface ScheduledTask {
  reminderId: string;
  cronJob?: any; // node-cron job instance
  timeout?: NodeJS.Timeout;
  lastRun?: Date;
  nextRun?: Date;
}

export interface SchedulerStatus {
  activeJobs: number;
  scheduledReminders: number;
  failedJobs: number;
  lastRecoveryAt?: Date;
  uptimeSeconds: number;
}

// Recovery interfaces
export interface RecoveryData {
  reminders: Reminder[];
  scheduledTasks: Array<{
    reminderId: string;
    nextRun: Date;
    missedExecutions: number;
  }>;
  lastShutdownAt: Date;
  version: string;
}

export interface RecoveryResult {
  success: boolean;
  recoveredReminders: number;
  rescheduledTasks: number;
  missedExecutions: number;
  errors: string[];
}

// Event interfaces for real-time updates
export interface ReminderEvent {
  type: 'created' | 'updated' | 'executed' | 'completed' | 'failed' | 'deleted';
  reminderId: string;
  reminder?: Reminder;
  executionResult?: ReminderExecutionResult;
  timestamp: Date;
  sessionId?: string;
}

export interface NewsPollingEvent {
  type: 'started' | 'update_found' | 'no_updates' | 'error' | 'stopped';
  reminderId: string;
  newsUpdate?: NewsUpdate;
  error?: string;
  timestamp: Date;
}