// Memory-based reminder storage with consolidation protection
// Uses the proven memory system backend like memoryTools

import { memoryManager } from '../../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../../memory/types';
import {
  IReminderStore,
  ReminderStoreConfig,
  ReminderStoreError,
} from '../interfaces/IReminderStore';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>minderFilt<PERSON>,
  ReminderStatus,
  ReminderType,
  NotificationChannel,
} from '../types';

export class MemoryReminderStore implements IReminderStore {
  private readonly REMINDER_MEMORY_TYPE = MemoryType.SEMANTIC; // Use semantic for structured data
  private readonly REMINDER_CATEGORY = 'structured-reminder';
  private readonly NO_CONSOLIDATE_TAG = 'no-consolidate';
  private readonly DATA_INTEGRITY_TAG = 'data-integrity-critical';
  private readonly REMINDER_SCHEMA_VERSION = '1.0.0';
  private isInitialized = false;

  constructor(private config: ReminderStoreConfig = {}) {}

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Test memory system availability
      const stats = await memoryManager.getStats();
      console.log(`🧠 Memory-based reminder storage ready (${stats.totalMemories} memories available)`);
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Memory Reminder Store:', error);
      throw new ReminderStoreError('initialize', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async create(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    await this.ensureInitialized();

    const id = this.generateReminderId();
    const now = new Date();
    
    const reminder: Reminder = {
      ...reminderData,
      id,
      createdAt: now,
      updatedAt: now,
      executionCount: 0,
      errorCount: 0,
    };

    // Validate reminder completeness before storage
    this.validateReminderData(reminder);

    try {
      // Store in memory system with special protection tags
      const result = await memoryManager.create(
        this.REMINDER_MEMORY_TYPE,
        {
          // Structured reminder data with complete schema
          reminderData: {
            ...reminder,
            // Ensure all dates are serializable
            createdAt: reminder.createdAt.toISOString(),
            updatedAt: reminder.updatedAt.toISOString(),
            lastExecutedAt: reminder.lastExecutedAt?.toISOString(),
            nextExecutionAt: reminder.nextExecutionAt?.toISOString(),
            schedule: {
              ...reminder.schedule,
              executeAt: reminder.schedule.executeAt?.toISOString(),
              pollingStartTime: reminder.schedule.pollingStartTime?.toISOString(),
              pollingEndTime: reminder.schedule.pollingEndTime?.toISOString(),
            },
          },
          
          // Searchable metadata
          reminderId: id,
          reminderType: reminder.type,
          reminderStatus: reminder.status,
          reminderTitle: reminder.title,
          reminderTopic: reminder.action?.newsConfig?.topic,
          sessionId: reminder.sessionId,
          createdBy: reminder.createdBy,
          
          // Data integrity markers
          dataType: 'structured_reminder',
          schemaVersion: this.REMINDER_SCHEMA_VERSION,
          category: this.REMINDER_CATEGORY,
        },
        {
          // CRITICAL: Prevent auto-consolidation and ensure data integrity
          priority: MemoryPriority.CRITICAL, // Highest priority prevents consolidation
          tags: [
            this.REMINDER_CATEGORY,
            this.NO_CONSOLIDATE_TAG,
            this.DATA_INTEGRITY_TAG,
            `reminder-${reminder.type}`,
            `status-${reminder.status}`,
            `id:${id}`,
            `session:${reminder.sessionId || 'default'}`,
            ...reminder.tags,
          ],
          confidence: 1.0, // Maximum confidence prevents modification
          ttl: reminder.maxExecutions ? undefined : 365 * 24 * 60 * 60 * 1000, // 1 year or indefinite
        }
      );

      if (result.success) {
        console.log(`🧠 Stored reminder in memory system: ${id} (protected from consolidation)`);
        return reminder;
      } else {
        throw new ReminderStoreError('create', result.error || 'Memory storage failed');
      }
    } catch (error) {
      console.error(`Failed to store reminder in memory: ${id}`, error);
      throw new ReminderStoreError('create', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async get(id: string): Promise<Reminder | null> {
    await this.ensureInitialized();

    try {
      // Search for reminder by ID using memory system
      const memories = await memoryManager.recall(`${this.REMINDER_CATEGORY} id:${id}`, 1);
      
      if (memories.length === 0) {
        return null;
      }

      const memory = memories[0];
      if (memory.content && typeof memory.content === 'object' && 'reminderData' in memory.content) {
        const reminderData = (memory.content as any).reminderData;
        
        // Validate data integrity
        this.validateRetrievedData(reminderData, id);
        
        return this.parseReminderDates(reminderData);
      }
      
      return null;
    } catch (error) {
      console.error(`Failed to get reminder from memory: ${id}`, error);
      return null;
    }
  }

  async update(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    await this.ensureInitialized();

    const existing = await this.get(id);
    if (!existing) return null;

    const updated: Reminder = {
      ...existing,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    // Validate updated data
    this.validateReminderData(updated);

    try {
      // Delete old memory entry
      await this.deleteFromMemory(id);
      
      // Create new entry with updated data (maintains data integrity)
      const newReminder = await this.create(updated as any);
      
      console.log(`🧠 Updated reminder in memory: ${id} (data integrity maintained)`);
      return newReminder;
    } catch (error) {
      console.error(`Failed to update reminder in memory: ${id}`, error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();

    try {
      return await this.deleteFromMemory(id);
    } catch (error) {
      console.error(`Failed to delete reminder from memory: ${id}`, error);
      return false;
    }
  }

  async list(filters?: ReminderFilters): Promise<Reminder[]> {
    await this.ensureInitialized();

    try {
      // Build search query for memory system
      let searchQuery = `${this.REMINDER_CATEGORY} structured_reminder`;
      
      if (filters?.status) {
        searchQuery += ` status-${filters.status}`;
      }
      if (filters?.type) {
        searchQuery += ` reminder-${filters.type}`;
      }
      if (filters?.sessionId) {
        searchQuery += ` session:${filters.sessionId}`;
      }
      if (filters?.tags?.length) {
        searchQuery += ` ${filters.tags.join(' ')}`;
      }

      // Search with high limit to get all reminders
      const memories = await memoryManager.recall(searchQuery, 100);
      
      const reminders = memories
        .map(memory => {
          if (memory.content && typeof memory.content === 'object' && 'reminderData' in memory.content) {
            const reminderData = (memory.content as any).reminderData;
            
            try {
              // Validate data integrity for each reminder
              this.validateRetrievedData(reminderData, reminderData.id);
              return this.parseReminderDates(reminderData);
            } catch (error) {
              console.warn(`Data integrity validation failed for reminder ${reminderData.id}:`, error);
              return null;
            }
          }
          return null;
        })
        .filter(Boolean) as Reminder[];

      // Apply additional filters
      return this.applyFilters(reminders, filters);
    } catch (error) {
      console.error('Failed to list reminders from memory:', error);
      return [];
    }
  }

  async getByStatus(status: ReminderStatus): Promise<Reminder[]> {
    return this.list({ status });
  }

  async getActiveReminders(): Promise<Reminder[]> {
    return this.list({ status: ReminderStatus.ACTIVE });
  }

  async getNextExecutions(limit: number = 10): Promise<Reminder[]> {
    const activeReminders = await this.getActiveReminders();
    
    return activeReminders
      .filter(r => r.nextExecutionAt)
      .sort((a, b) => a.nextExecutionAt!.getTime() - b.nextExecutionAt!.getTime())
      .slice(0, limit);
  }

  async createNewsMonitoring(config: {
    title: string;
    topic: string;
    eventDate?: Date;
    eventStartTime?: Date;
    eventEndTime?: Date;
    summaryType?: string;
    pollingInterval?: number;
    searchQueries?: string[];
    keywordsToTrack?: string[];
    notificationChannels?: NotificationChannel[];
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder> {
    const reminderData = {
      title: config.title,
      description: `News monitoring for: ${config.topic}`,
      type: ReminderType.NEWS_MONITORING,
      status: ReminderStatus.ACTIVE,
      schedule: {
        pollingInterval: config.pollingInterval || 30,
        pollingStartTime: config.eventStartTime,
        pollingEndTime: config.eventEndTime,
        timezone: 'UTC',
      },
      action: {
        type: 'news_poll' as const,
        payload: { topic: config.topic },
        notificationChannels: config.notificationChannels || [NotificationChannel.WEB_UI],
        newsConfig: {
          topic: config.topic,
          searchQueries: config.searchQueries || [config.topic],
          pollingInterval: config.pollingInterval || 30,
          summaryType: config.summaryType || 'live_updates',
          keywordsToTrack: config.keywordsToTrack,
          eventDate: config.eventDate,
          eventStartTime: config.eventStartTime,
          eventEndTime: config.eventEndTime,
          maxUpdatesPerHour: 10,
          sourcesToMonitor: [],
        },
      },
      priority: 'high' as const,
      tags: [
        'news-monitoring',
        config.topic.toLowerCase().replace(/\s+/g, '-'),
        ...(config.keywordsToTrack?.map(k => k.toLowerCase().replace(/\s+/g, '-')) || []),
      ],
      sessionId: config.sessionId,
      createdBy: config.createdBy || 'MemoryReminderStore',
    };

    return await this.create(reminderData as any);
  }

  // Scheduling operations (no-op for memory store, handled by scheduler integration)
  async schedule(reminder: Reminder): Promise<boolean> {
    // Memory store just stores data, scheduling is handled by ReminderScheduler
    return true;
  }

  async unschedule(reminderId: string): Promise<boolean> {
    // Update status to paused
    const updated = await this.update(reminderId, { status: ReminderStatus.PAUSED });
    return updated !== null;
  }

  // Environment capabilities
  supportsScheduling(): boolean {
    return false; // Memory store is just storage, doesn't handle scheduling
  }

  supportsFileOperations(): boolean {
    return false; // Memory store abstracts file operations
  }

  supportsRealTimeUpdates(): boolean {
    return true; // Memory system supports real-time access
  }

  async cleanup(): Promise<void> {
    console.log('🧠 Memory Reminder Store cleanup complete');
  }

  async isHealthy(): Promise<boolean> {
    try {
      await memoryManager.getStats();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getStats(): Promise<{
    totalReminders: number;
    activeReminders: number;
    scheduledReminders: number;
    environment: 'browser' | 'server';
    storageType: string;
  }> {
    try {
      const allReminders = await this.list();
      const activeReminders = allReminders.filter(r => r.status === ReminderStatus.ACTIVE);
      const scheduledReminders = allReminders.filter(r => r.nextExecutionAt);
      
      return {
        totalReminders: allReminders.length,
        activeReminders: activeReminders.length,
        scheduledReminders: scheduledReminders.length,
        environment: typeof window !== 'undefined' ? 'browser' : 'server',
        storageType: 'memory_system',
      };
    } catch (error) {
      return {
        totalReminders: 0,
        activeReminders: 0,
        scheduledReminders: 0,
        environment: typeof window !== 'undefined' ? 'browser' : 'server',
        storageType: 'memory_unavailable',
      };
    }
  }

  // Private helper methods
  private generateReminderId(): string {
    return `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  private validateReminderData(reminder: Reminder): void {
    // Comprehensive data validation to prevent incomplete storage
    const requiredFields = ['id', 'title', 'description', 'type', 'status', 'schedule', 'action'];
    
    for (const field of requiredFields) {
      if (!(field in reminder) || reminder[field as keyof Reminder] === undefined) {
        throw new ReminderStoreError(
          'validation', 
          `Missing required field: ${field}`,
          false
        );
      }
    }

    // Validate news monitoring config completeness
    if (reminder.type === ReminderType.NEWS_MONITORING) {
      if (!reminder.action?.newsConfig?.topic) {
        throw new ReminderStoreError(
          'validation',
          'News monitoring reminder missing topic configuration',
          false
        );
      }
      
      if (!reminder.action.newsConfig.searchQueries?.length) {
        throw new ReminderStoreError(
          'validation',
          'News monitoring reminder missing search queries',
          false
        );
      }
    }

    console.log(`✅ Reminder data validation passed: ${reminder.id}`);
  }

  private validateRetrievedData(reminderData: any, expectedId: string): void {
    // Validate retrieved data hasn't been corrupted or consolidated incorrectly
    if (!reminderData || typeof reminderData !== 'object') {
      throw new ReminderStoreError(
        'data_integrity',
        `Retrieved data is not an object for reminder ${expectedId}`,
        false
      );
    }

    if (reminderData.id !== expectedId) {
      throw new ReminderStoreError(
        'data_integrity',
        `Retrieved reminder ID mismatch: expected ${expectedId}, got ${reminderData.id}`,
        false
      );
    }

    // Check for essential fields
    const essentialFields = ['title', 'description', 'type', 'status', 'action'];
    const missingFields = essentialFields.filter(field => !reminderData[field]);
    
    if (missingFields.length > 0) {
      throw new ReminderStoreError(
        'data_integrity',
        `Retrieved reminder missing essential fields: ${missingFields.join(', ')}`,
        false
      );
    }

    console.log(`✅ Retrieved data integrity validated: ${expectedId}`);
  }

  private async deleteFromMemory(id: string): Promise<boolean> {
    try {
      const memories = await memoryManager.recall(`${this.REMINDER_CATEGORY} id:${id}`, 5);
      
      if (memories.length === 0) {
        return false; // Reminder not found
      }

      let deletedCount = 0;
      for (const memory of memories) {
        try {
          await memoryManager.delete(memory.metadata.id);
          deletedCount++;
        } catch (deleteError) {
          console.warn(`Failed to delete memory ${memory.metadata.id}:`, deleteError);
        }
      }

      console.log(`🧠 Deleted ${deletedCount} memory entries for reminder: ${id}`);
      return deletedCount > 0;
    } catch (error) {
      console.error(`Failed to delete reminder from memory: ${id}`, error);
      return false;
    }
  }

  private parseReminderDates(reminderData: any): Reminder {
    return {
      ...reminderData,
      createdAt: new Date(reminderData.createdAt),
      updatedAt: new Date(reminderData.updatedAt),
      lastExecutedAt: reminderData.lastExecutedAt ? new Date(reminderData.lastExecutedAt) : undefined,
      nextExecutionAt: reminderData.nextExecutionAt ? new Date(reminderData.nextExecutionAt) : undefined,
      schedule: {
        ...reminderData.schedule,
        executeAt: reminderData.schedule?.executeAt ? new Date(reminderData.schedule.executeAt) : undefined,
        pollingStartTime: reminderData.schedule?.pollingStartTime ? new Date(reminderData.schedule.pollingStartTime) : undefined,
        pollingEndTime: reminderData.schedule?.pollingEndTime ? new Date(reminderData.schedule.pollingEndTime) : undefined,
      },
    };
  }

  private applyFilters(reminders: Reminder[], filters?: ReminderFilters): Reminder[] {
    if (!filters) return reminders;
    
    return reminders.filter(reminder => {
      if (filters.status && reminder.status !== filters.status) return false;
      if (filters.type && reminder.type !== filters.type) return false;
      if (filters.createdBy && reminder.createdBy !== filters.createdBy) return false;
      if (filters.sessionId && reminder.sessionId !== filters.sessionId) return false;
      if (filters.priority && reminder.priority !== filters.priority) return false;
      
      if (filters.tags?.length) {
        const hasAllTags = filters.tags.every(tag => reminder.tags.includes(tag));
        if (!hasAllTags) return false;
      }
      
      if (filters.createdAfter && reminder.createdAt < filters.createdAfter) return false;
      if (filters.createdBefore && reminder.createdAt > filters.createdBefore) return false;
      if (filters.nextExecutionBefore && (!reminder.nextExecutionAt || reminder.nextExecutionAt > filters.nextExecutionBefore)) return false;
      
      return true;
    });
  }
}