// Qdrant-based reminder store for shared cross-platform data access

import { QdrantClient } from '@qdrant/js-client-rest';
import {
  I<PERSON><PERSON>inderStore,
  ReminderStoreConfig,
  ReminderStoreError,
} from '../interfaces/IReminderStore';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>mind<PERSON>F<PERSON><PERSON>,
  <PERSON>minder<PERSON>tat<PERSON>,
  ReminderType,
  NotificationChannel,
} from '../types';
import { EmbeddingService } from '../../memory/services/EmbeddingService';

export class QdrantReminderStore implements IReminderStore {
  private client: QdrantClient;
  private embeddingService: EmbeddingService;
  private collectionName: string;
  private dimension: number;
  private initialized = false;

  constructor(config: ReminderStoreConfig = {}) {
    this.client = new QdrantClient({
      url: config.qdrantUrl || process.env.QDRANT_URL || 'http://localhost:6333',
      apiKey: config.qdrantApiKey || process.env.QDRANT_API_KEY,
    });
    
    this.embeddingService = new EmbeddingService();
    this.collectionName = config.collectionName || 'dante_reminders';
    this.dimension = 1536; // OpenAI embedding dimension
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Check if collection exists
      const collections = await this.client.getCollections();
      const collectionExists = collections.collections.some(
        c => c.name === this.collectionName
      );

      if (!collectionExists) {
        // Create collection for reminders
        await this.client.createCollection(this.collectionName, {
          vectors: {
            size: this.dimension,
            distance: 'Cosine',
          },
          optimizers_config: {
            deleted_threshold: 0.2,
            vacuum_min_vector_number: 100,
            default_segment_number: 1,
          },
        });
        
        console.log(`📊 Created Qdrant collection: ${this.collectionName}`);
      }

      this.initialized = true;
      console.log('✅ QdrantReminderStore initialized');
    } catch (error) {
      console.error('❌ Failed to initialize QdrantReminderStore:', error);
      throw new ReminderStoreError('initialize', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async create(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    await this.ensureInitialized();

    const id = this.generateReminderId();
    const now = new Date();
    
    const reminder: Reminder = {
      ...reminderData,
      id,
      createdAt: now,
      updatedAt: now,
      executionCount: 0,
      errorCount: 0,
    };

    try {
      // Create embedding from reminder content for searchability
      const searchText = `${reminder.title} ${reminder.description} ${reminder.tags.join(' ')} ${reminder.type}`;
      const embedding = await this.embeddingService.generateEmbedding(searchText);

      // Store in Qdrant
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: [{
          id: id,
          vector: embedding,
          payload: {
            // Store full reminder data
            reminder: JSON.stringify(reminder),
            
            // Searchable fields
            title: reminder.title,
            description: reminder.description,
            type: reminder.type,
            status: reminder.status,
            priority: reminder.priority,
            tags: reminder.tags,
            createdAt: reminder.createdAt.toISOString(),
            nextExecutionAt: reminder.nextExecutionAt?.toISOString(),
            sessionId: reminder.sessionId,
            createdBy: reminder.createdBy,
            
            // Metadata for filtering
            isActive: reminder.status === ReminderStatus.ACTIVE,
            isNewsMonitoring: reminder.type === ReminderType.NEWS_MONITORING,
            hasSchedule: !!reminder.nextExecutionAt,
          },
        }],
      });

      console.log(`📊 Stored reminder in Qdrant: ${id}`);
      return reminder;
    } catch (error) {
      console.error(`Failed to store reminder in Qdrant: ${id}`, error);
      throw new ReminderStoreError('create', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async get(id: string): Promise<Reminder | null> {
    await this.ensureInitialized();

    try {
      const result = await this.client.retrieve(this.collectionName, { ids: [id] });
      
      if (result.length === 0) {
        return null;
      }

      const point = result[0];
      if (point.payload?.reminder) {
        return this.parseReminderDates(JSON.parse(point.payload.reminder as string));
      }
      
      return null;
    } catch (error) {
      console.error(`Failed to get reminder from Qdrant: ${id}`, error);
      return null;
    }
  }

  async update(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    await this.ensureInitialized();

    const existing = await this.get(id);
    if (!existing) return null;

    const updated: Reminder = {
      ...existing,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    try {
      // Update embedding if content changed
      const searchText = `${updated.title} ${updated.description} ${updated.tags.join(' ')} ${updated.type}`;
      const embedding = await this.embeddingService.generateEmbedding(searchText);

      // Update in Qdrant
      await this.client.upsert(this.collectionName, {
        wait: true,
        points: [{
          id: id,
          vector: embedding,
          payload: {
            reminder: JSON.stringify(updated),
            title: updated.title,
            description: updated.description,
            type: updated.type,
            status: updated.status,
            priority: updated.priority,
            tags: updated.tags,
            updatedAt: updated.updatedAt.toISOString(),
            nextExecutionAt: updated.nextExecutionAt?.toISOString(),
            sessionId: updated.sessionId,
            isActive: updated.status === ReminderStatus.ACTIVE,
            isNewsMonitoring: updated.type === ReminderType.NEWS_MONITORING,
            hasSchedule: !!updated.nextExecutionAt,
          },
        }],
      });

      console.log(`📊 Updated reminder in Qdrant: ${id}`);
      return updated;
    } catch (error) {
      console.error(`Failed to update reminder in Qdrant: ${id}`, error);
      throw new ReminderStoreError('update', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();

    try {
      await this.client.delete(this.collectionName, {
        wait: true,
        points: [id],
      });

      console.log(`📊 Deleted reminder from Qdrant: ${id}`);
      return true;
    } catch (error) {
      console.error(`Failed to delete reminder from Qdrant: ${id}`, error);
      return false;
    }
  }

  async list(filters?: ReminderFilters): Promise<Reminder[]> {
    await this.ensureInitialized();

    try {
      const mustConditions: any[] = [];
      
      // Build filter conditions
      if (filters?.status) {
        mustConditions.push({ key: 'status', match: { value: filters.status } });
      }
      if (filters?.type) {
        mustConditions.push({ key: 'type', match: { value: filters.type } });
      }
      if (filters?.sessionId) {
        mustConditions.push({ key: 'sessionId', match: { value: filters.sessionId } });
      }
      if (filters?.priority) {
        mustConditions.push({ key: 'priority', match: { value: filters.priority } });
      }
      
      const filter = mustConditions.length > 0 ? { must: mustConditions } : undefined;
      
      const result = await this.client.scroll(this.collectionName, {
        filter,
        limit: 100,
        with_payload: true,
      });

      const reminders = result.points
        .map(point => point.payload?.reminder ? JSON.parse(point.payload.reminder as string) : null)
        .filter(Boolean)
        .map(this.parseReminderDates);
      
      // Apply additional filters that can't be done at Qdrant level
      let filteredReminders = reminders;
      
      if (filters?.tags?.length) {
        filteredReminders = reminders.filter(r => 
          filters.tags!.some(tag => r.tags.includes(tag))
        );
      }
      
      if (filters?.createdAfter) {
        filteredReminders = filteredReminders.filter(r => r.createdAt >= filters.createdAfter!);
      }
      
      if (filters?.createdBefore) {
        filteredReminders = filteredReminders.filter(r => r.createdAt <= filters.createdBefore!);
      }
      
      return filteredReminders;
    } catch (error) {
      console.error('Failed to list reminders from Qdrant:', error);
      return [];
    }
  }

  async getByStatus(status: ReminderStatus): Promise<Reminder[]> {
    await this.ensureInitialized();

    try {
      const result = await this.client.scroll(this.collectionName, {
        filter: {
          must: [{ key: 'status', match: { value: status } }],
        },
        limit: 100,
        with_payload: true,
      });

      return result.points
        .map(point => point.payload?.reminder ? JSON.parse(point.payload.reminder as string) : null)
        .filter(Boolean)
        .map(this.parseReminderDates);
    } catch (error) {
      console.error(`Failed to get reminders by status from Qdrant: ${status}`, error);
      return [];
    }
  }

  async getActiveReminders(): Promise<Reminder[]> {
    return this.getByStatus(ReminderStatus.ACTIVE);
  }

  async getNextExecutions(limit: number = 10): Promise<Reminder[]> {
    await this.ensureInitialized();

    try {
      const result = await this.client.scroll(this.collectionName, {
        filter: {
          must: [
            { key: 'isActive', match: { value: true } },
            { key: 'hasSchedule', match: { value: true } },
          ],
        },
        limit,
        with_payload: true,
      });

      const reminders = result.points
        .map(point => point.payload?.reminder ? JSON.parse(point.payload.reminder as string) : null)
        .filter(Boolean)
        .map(this.parseReminderDates);

      // Sort by next execution time
      return reminders
        .filter(r => r.nextExecutionAt)
        .sort((a, b) => a.nextExecutionAt!.getTime() - b.nextExecutionAt!.getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get next executions from Qdrant:', error);
      return [];
    }
  }

  async createNewsMonitoring(config: any): Promise<Reminder> {
    // Use the general create method with news monitoring specific setup
    const reminderData = {
      title: config.title,
      description: `News monitoring for: ${config.topic}`,
      type: ReminderType.NEWS_MONITORING,
      status: ReminderStatus.ACTIVE,
      schedule: {
        pollingInterval: config.pollingInterval || 30,
        pollingStartTime: config.eventStartTime,
        pollingEndTime: config.eventEndTime,
        timezone: 'UTC',
      },
      action: {
        type: 'news_poll' as const,
        payload: { topic: config.topic },
        notificationChannels: config.notificationChannels || [NotificationChannel.WEB_UI],
        newsConfig: {
          topic: config.topic,
          searchQueries: config.searchQueries || [config.topic],
          pollingInterval: config.pollingInterval || 30,
          summaryType: config.summaryType || 'live_updates',
          keywordsToTrack: config.keywordsToTrack,
          eventDate: config.eventDate,
          eventStartTime: config.eventStartTime,
          eventEndTime: config.eventEndTime,
        },
      },
      priority: 'high' as const,
      tags: ['news-monitoring', config.topic.toLowerCase().replace(/\s+/g, '-')],
      sessionId: config.sessionId,
      createdBy: config.createdBy || 'QdrantReminderStore',
    };

    return await this.create(reminderData as any);
  }

  // Scheduling delegation (server handles actual scheduling)
  async schedule(reminder: Reminder): Promise<boolean> {
    // Qdrant just stores data, scheduling is handled by server-side components
    return true;
  }

  async unschedule(reminderId: string): Promise<boolean> {
    // Update status to paused
    const updated = await this.update(reminderId, { status: ReminderStatus.PAUSED });
    return updated !== null;
  }

  // Environment capabilities
  supportsScheduling(): boolean {
    return false; // Qdrant is just storage, doesn't handle scheduling
  }

  supportsFileOperations(): boolean {
    return false; // Qdrant is vector storage, not file storage
  }

  supportsRealTimeUpdates(): boolean {
    return false; // Qdrant doesn't provide real-time updates
  }

  async cleanup(): Promise<void> {
    // Close Qdrant connection if needed
    console.log('📊 QdrantReminderStore cleanup complete');
  }

  async isHealthy(): Promise<boolean> {
    try {
      await this.client.getCollections();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getStats(): Promise<any> {
    await this.ensureInitialized();

    try {
      const info = await this.client.getCollection(this.collectionName);
      const activeReminders = await this.getActiveReminders();
      
      return {
        totalReminders: info.points_count || 0,
        activeReminders: activeReminders.length,
        scheduledReminders: 0, // Qdrant doesn't handle scheduling
        environment: typeof window !== 'undefined' ? 'browser' : 'server',
        storageType: 'qdrant',
      };
    } catch (error) {
      return {
        totalReminders: 0,
        activeReminders: 0,
        scheduledReminders: 0,
        environment: typeof window !== 'undefined' ? 'browser' : 'server',
        storageType: 'qdrant_unavailable',
      };
    }
  }

  // Helper methods
  private generateReminderId(): string {
    return `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  private parseReminderDates(reminder: any): Reminder {
    return {
      ...reminder,
      createdAt: new Date(reminder.createdAt),
      updatedAt: new Date(reminder.updatedAt),
      lastExecutedAt: reminder.lastExecutedAt ? new Date(reminder.lastExecutedAt) : undefined,
      nextExecutionAt: reminder.nextExecutionAt ? new Date(reminder.nextExecutionAt) : undefined,
      schedule: {
        ...reminder.schedule,
        executeAt: reminder.schedule?.executeAt ? new Date(reminder.schedule.executeAt) : undefined,
        pollingStartTime: reminder.schedule?.pollingStartTime ? new Date(reminder.schedule.pollingStartTime) : undefined,
        pollingEndTime: reminder.schedule?.pollingEndTime ? new Date(reminder.schedule.pollingEndTime) : undefined,
      },
    };
  }
}