// Browser-compatible reminder store that uses API calls (like browser memory access)

import {
  IReminderStore,
  ReminderStoreConfig,
  ReminderOperationResult,
  ReminderApiError,
  ReminderNotFoundError,
} from '../interfaces/IReminderStore';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>minderFilt<PERSON>,
  ReminderStatus,
  NotificationChannel,
} from '../types';

export class ApiReminderStore implements IReminderStore {
  private baseUrl: string;
  private timeout: number;
  private isInitialized = false;

  constructor(config: ReminderStoreConfig = {}) {
    // Get API base URL (browser gets from current location, server uses localhost)
    this.baseUrl = config.apiBaseUrl || this.getDefaultApiUrl();
    this.timeout = config.apiTimeout || 30000; // 30 second timeout
  }

  private getDefaultApiUrl(): string {
    if (typeof window !== 'undefined') {
      // Browser environment - use current origin
      return `${window.location.protocol}//${window.location.host}`;
    } else {
      // Node.js environment - use localhost
      return 'http://localhost:3001';
    }
  }

  private async makeApiCall(endpoint: string, method: string = 'GET', data?: any): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    
    try {
      const options: RequestInit = {
        method,
        headers: { 'Content-Type': 'application/json' },
        signal: controller.signal,
      };
      
      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }
      
      const response = await fetch(url, options);
      const result = await response.json();
      
      if (!response.ok) {
        throw new ReminderApiError(endpoint, response.status, result.message || result.error);
      }
      
      return result;
    } catch (error) {
      if (error instanceof ReminderApiError) {
        throw error;
      }
      throw new ReminderApiError(endpoint, 0, error instanceof Error ? error.message : 'Unknown error');
    } finally {
      clearTimeout(timeoutId);
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Test API connection
      await this.makeApiCall('/api/health');
      this.isInitialized = true;
      console.log('🌐 ApiReminderStore initialized - connected to server API');
    } catch (error) {
      console.error('❌ Failed to initialize ApiReminderStore:', error);
      throw new ReminderApiError('initialize', 0, 'Cannot connect to reminder API server');
    }
  }

  async create(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder> {
    const result = await this.makeApiCall('/api/reminders', 'POST', reminderData);
    
    if (result.success) {
      return result.reminder;
    } else {
      throw new ReminderApiError('create', 0, result.error || 'Failed to create reminder');
    }
  }

  async get(id: string): Promise<Reminder | null> {
    try {
      const result = await this.makeApiCall(`/api/reminders/${id}`);
      
      if (result.success) {
        return this.parseReminderDates(result.reminder);
      } else {
        return null;
      }
    } catch (error) {
      if (error instanceof ReminderApiError && error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  }

  async update(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    try {
      const result = await this.makeApiCall(`/api/reminders/${id}`, 'PUT', updates);
      
      if (result.success) {
        return this.parseReminderDates(result.reminder);
      } else {
        return null;
      }
    } catch (error) {
      if (error instanceof ReminderApiError && error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.makeApiCall(`/api/reminders/${id}`, 'DELETE');
      return result.success;
    } catch (error) {
      if (error instanceof ReminderApiError && error.statusCode === 404) {
        return false; // Already deleted
      }
      throw error;
    }
  }

  async list(filters?: ReminderFilters): Promise<Reminder[]> {
    const queryParams = new URLSearchParams();
    
    if (filters?.status) queryParams.append('status', filters.status);
    if (filters?.type) queryParams.append('type', filters.type);
    if (filters?.sessionId) queryParams.append('sessionId', filters.sessionId);
    if (filters?.priority) queryParams.append('priority', filters.priority);
    if (filters?.tags?.length) {
      filters.tags.forEach(tag => queryParams.append('tags', tag));
    }
    
    const endpoint = `/api/reminders?${queryParams.toString()}`;
    const result = await this.makeApiCall(endpoint);
    
    if (result.success) {
      return result.reminders.map((r: any) => this.parseReminderDates(r));
    } else {
      throw new ReminderApiError('list', 0, result.error || 'Failed to list reminders');
    }
  }

  async getByStatus(status: ReminderStatus): Promise<Reminder[]> {
    return this.list({ status });
  }

  async getActiveReminders(): Promise<Reminder[]> {
    return this.list({ status: ReminderStatus.ACTIVE });
  }

  async getNextExecutions(limit: number = 10): Promise<Reminder[]> {
    const result = await this.makeApiCall(`/api/reminders/upcoming/${limit}`);
    
    if (result.success) {
      return result.reminders.map((r: any) => this.parseReminderDates(r));
    } else {
      throw new ReminderApiError('getNextExecutions', 0, result.error || 'Failed to get upcoming reminders');
    }
  }

  async createNewsMonitoring(config: {
    title: string;
    topic: string;
    eventDate?: Date;
    eventStartTime?: Date;
    eventEndTime?: Date;
    summaryType?: string;
    pollingInterval?: number;
    searchQueries?: string[];
    keywordsToTrack?: string[];
    notificationChannels?: NotificationChannel[];
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder> {
    const payload = {
      title: config.title,
      topic: config.topic,
      eventDate: config.eventDate?.toISOString(),
      eventStartTime: config.eventStartTime?.toISOString(),
      eventEndTime: config.eventEndTime?.toISOString(),
      summaryType: config.summaryType || 'live_updates',
      pollingInterval: config.pollingInterval || 30,
      searchQueries: config.searchQueries,
      keywordsToTrack: config.keywordsToTrack,
      notificationChannels: config.notificationChannels || ['web_ui'],
      sessionId: config.sessionId,
      createdBy: config.createdBy || 'ApiReminderStore',
    };

    const result = await this.makeApiCall('/api/reminders/news-monitoring', 'POST', payload);
    
    if (result.success) {
      return this.parseReminderDates(result.reminder);
    } else {
      throw new ReminderApiError('createNewsMonitoring', 0, result.error || 'Failed to create news monitoring');
    }
  }

  // Scheduling operations - delegated to server
  async schedule(reminder: Reminder): Promise<boolean> {
    // In API store, scheduling is handled server-side automatically
    // We just need to ensure the reminder is active
    if (reminder.status === ReminderStatus.ACTIVE) {
      return true;
    }
    
    const updated = await this.update(reminder.id, { status: ReminderStatus.ACTIVE });
    return updated !== null;
  }

  async unschedule(reminderId: string): Promise<boolean> {
    const result = await this.makeApiCall(`/api/reminders/${reminderId}/pause`, 'POST');
    return result.success;
  }

  // Environment capabilities
  supportsScheduling(): boolean {
    return true; // Scheduling happens server-side via API
  }

  supportsFileOperations(): boolean {
    return false; // Browser can't access file system directly
  }

  supportsRealTimeUpdates(): boolean {
    return true; // Via SSE from server
  }

  async cleanup(): Promise<void> {
    // No cleanup needed for API store
    console.log('🌐 ApiReminderStore cleanup complete');
  }

  async isHealthy(): Promise<boolean> {
    try {
      await this.makeApiCall('/api/health');
      return true;
    } catch (error) {
      return false;
    }
  }

  async getStats(): Promise<{
    totalReminders: number;
    activeReminders: number;
    scheduledReminders: number;
    environment: 'browser' | 'server';
    storageType: string;
  }> {
    try {
      const result = await this.makeApiCall('/api/reminders/system/status');
      
      if (result.success) {
        const reminders = await this.list();
        const activeReminders = reminders.filter(r => r.status === ReminderStatus.ACTIVE);
        
        return {
          totalReminders: reminders.length,
          activeReminders: activeReminders.length,
          scheduledReminders: result.status.scheduler?.activeJobs || 0,
          environment: 'browser',
          storageType: 'api',
        };
      } else {
        throw new Error('Failed to get stats from API');
      }
    } catch (error) {
      return {
        totalReminders: 0,
        activeReminders: 0,
        scheduledReminders: 0,
        environment: 'browser',
        storageType: 'api_unavailable',
      };
    }
  }

  // Helper method to parse dates from API responses
  private parseReminderDates(reminder: any): Reminder {
    return {
      ...reminder,
      createdAt: new Date(reminder.createdAt),
      updatedAt: new Date(reminder.updatedAt),
      lastExecutedAt: reminder.lastExecutedAt ? new Date(reminder.lastExecutedAt) : undefined,
      nextExecutionAt: reminder.nextExecutionAt ? new Date(reminder.nextExecutionAt) : undefined,
      schedule: reminder.schedule ? {
        ...reminder.schedule,
        executeAt: reminder.schedule.executeAt ? new Date(reminder.schedule.executeAt) : undefined,
        pollingStartTime: reminder.schedule.pollingStartTime ? new Date(reminder.schedule.pollingStartTime) : undefined,
        pollingEndTime: reminder.schedule.pollingEndTime ? new Date(reminder.schedule.pollingEndTime) : undefined,
      } : undefined,
    };
  }
}