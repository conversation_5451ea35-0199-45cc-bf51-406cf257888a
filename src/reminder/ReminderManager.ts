import { EventEmitter } from 'events';
import {
  <PERSON>mind<PERSON>,
  ReminderSystemConfig,
  ReminderType,
  ReminderStatus,
  ReminderSchedule,
  ReminderAction,
  ReminderFilters,
  ReminderExecutionResult,
  RecoveryData,
  RecoveryResult,
  NotificationChannel,
  NewsMonitoringConfig,
} from './types';
import { ReminderStorage } from './storage/ReminderStorage';
import { ReminderScheduler } from './scheduler/ReminderScheduler';
import { promises as fs } from 'fs';
import * as path from 'path';

export class ReminderManager extends EventEmitter {
  private storage: ReminderStorage;
  private scheduler: ReminderScheduler;
  private config: ReminderSystemConfig;
  private isInitialized = false;
  private readonly RECOVERY_FILE = path.join(process.cwd(), 'data', 'reminders', 'recovery.json');

  constructor(config?: Partial<ReminderSystemConfig>) {
    super();
    
    this.config = {
      enabled: true,
      maxActiveReminders: 100,
      defaultPollingInterval: 30,
      newsMonitoringEnabled: true,
      persistenceEnabled: true,
      recoveryEnabled: true,
      defaultTimezone: 'UTC',
      rateLimiting: {
        maxNewsRequestsPerHour: 120,
        maxReminderExecutionsPerMinute: 10,
      },
      ...config,
    };

    this.storage = new ReminderStorage();
    this.scheduler = new ReminderScheduler(this.storage);
    
    this.setupEventHandlers();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    console.log('🚀 Initializing Reminder Manager...');
    
    if (!this.config.enabled) {
      console.log('⚠️ Reminder system is disabled');
      return;
    }

    try {
      // Perform recovery if enabled
      if (this.config.recoveryEnabled) {
        await this.performRecovery();
      }

      // Start the scheduler
      await this.scheduler.start();
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
      this.isInitialized = true;
      console.log('✅ Reminder Manager initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize Reminder Manager:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.isInitialized) return;
    
    console.log('🛑 Shutting down Reminder Manager...');
    
    try {
      // Save recovery data
      if (this.config.recoveryEnabled) {
        await this.saveRecoveryData();
      }
      
      // Stop the scheduler
      await this.scheduler.stop();
      
      this.isInitialized = false;
      console.log('✅ Reminder Manager shut down successfully');
      this.emit('shutdown');
      
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
    }
  }

  // Core reminder management methods
  async createReminder(options: {
    title: string;
    description: string;
    type: ReminderType;
    schedule: ReminderSchedule;
    action: ReminderAction;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    tags?: string[];
    maxExecutions?: number;
    maxRetries?: number;
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder> {
    if (!this.isInitialized) {
      throw new Error('Reminder Manager not initialized');
    }

    const activeReminders = await this.storage.getActiveReminders();
    if (activeReminders.length >= this.config.maxActiveReminders) {
      throw new Error(`Maximum active reminders limit reached (${this.config.maxActiveReminders})`);
    }

    const reminder = await this.storage.create({
      title: options.title,
      description: options.description,
      type: options.type,
      status: ReminderStatus.ACTIVE,
      schedule: options.schedule,
      action: options.action,
      priority: options.priority || 'medium',
      tags: options.tags || [],
      maxExecutions: options.maxExecutions,
      maxRetries: options.maxRetries || 3,
      sessionId: options.sessionId,
      createdBy: options.createdBy,
      executionCount: 0,
      errorCount: 0,
    });

    // Calculate next execution time
    const nextExecutionAt = this.calculateNextExecution(reminder);
    if (nextExecutionAt) {
      await this.storage.update(reminder.id, { nextExecutionAt });
      reminder.nextExecutionAt = nextExecutionAt;
    }

    // Schedule the reminder
    await this.scheduler.scheduleReminder(reminder);
    
    console.log(`📅 Created reminder: ${reminder.title} (${reminder.id})`);
    this.emit('reminderCreated', reminder);
    
    return reminder;
  }

  async createNewsMonitoringReminder(options: {
    title: string;
    topic: string;
    eventDate?: Date;
    eventStartTime?: Date;
    eventEndTime?: Date;
    summaryType?: 'live_updates' | 'blow_by_blow' | 'periodic_summary' | 'final_report';
    pollingInterval?: number;
    searchQueries?: string[];
    keywordsToTrack?: string[];
    notificationChannels?: NotificationChannel[];
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder> {
    const pollingInterval = options.pollingInterval || this.config.defaultPollingInterval;
    const summaryType = options.summaryType || 'live_updates';
    
    // Auto-generate search queries if not provided
    const searchQueries = options.searchQueries || [
      options.topic,
      `${options.topic} live`,
      `${options.topic} news`,
      `${options.topic} updates`,
    ];

    const newsConfig: NewsMonitoringConfig = {
      topic: options.topic,
      searchQueries,
      pollingInterval,
      eventDate: options.eventDate,
      eventStartTime: options.eventStartTime,
      eventEndTime: options.eventEndTime,
      summaryType,
      keywordsToTrack: options.keywordsToTrack,
    };

    const schedule: ReminderSchedule = {
      pollingInterval,
      pollingStartTime: options.eventStartTime || new Date(),
      pollingEndTime: options.eventEndTime,
      timezone: this.config.defaultTimezone,
    };

    const action: ReminderAction = {
      type: 'news_poll',
      payload: { message: `News update for: ${options.topic}` },
      notificationChannels: options.notificationChannels || [NotificationChannel.WEB_UI],
      newsConfig,
    };

    return await this.createReminder({
      title: options.title,
      description: `Monitoring news for: ${options.topic}`,
      type: ReminderType.NEWS_MONITORING,
      schedule,
      action,
      priority: 'high',
      tags: ['news-monitoring', options.topic.toLowerCase().replace(/\s+/g, '-')],
      sessionId: options.sessionId,
      createdBy: options.createdBy,
    });
  }

  async getReminder(id: string): Promise<Reminder | null> {
    return await this.storage.get(id);
  }

  async listReminders(filters?: ReminderFilters): Promise<Reminder[]> {
    return await this.storage.list(filters);
  }

  async updateReminder(id: string, updates: Partial<Reminder>): Promise<Reminder | null> {
    const updated = await this.storage.update(id, updates);
    
    if (updated) {
      // Reschedule if the reminder is still active and scheduling-related fields changed
      if (updated.status === ReminderStatus.ACTIVE && 
          (updates.schedule || updates.status !== undefined)) {
        await this.scheduler.rescheduleReminder(updated);
      }
      
      this.emit('reminderUpdated', updated);
    }
    
    return updated;
  }

  async cancelReminder(id: string): Promise<boolean> {
    const reminder = await this.storage.get(id);
    if (!reminder) return false;

    // Update status to cancelled
    await this.storage.update(id, { 
      status: ReminderStatus.CANCELLED,
      nextExecutionAt: undefined,
    });

    // Unschedule from scheduler
    await this.scheduler.unscheduleReminder(id);
    
    console.log(`🗑️ Cancelled reminder: ${reminder.title} (${id})`);
    this.emit('reminderCancelled', id);
    
    return true;
  }

  async deleteReminder(id: string): Promise<boolean> {
    // Cancel first to clean up scheduling
    await this.cancelReminder(id);
    
    // Then delete from storage
    const deleted = await this.storage.delete(id);
    
    if (deleted) {
      console.log(`🗑️ Deleted reminder: ${id}`);
      this.emit('reminderDeleted', id);
    }
    
    return deleted;
  }

  async pauseReminder(id: string): Promise<boolean> {
    const reminder = await this.storage.get(id);
    if (!reminder || reminder.status !== ReminderStatus.ACTIVE) return false;

    await this.storage.update(id, { status: ReminderStatus.PAUSED });
    await this.scheduler.unscheduleReminder(id);
    
    console.log(`⏸️ Paused reminder: ${reminder.title} (${id})`);
    this.emit('reminderPaused', id);
    
    return true;
  }

  async resumeReminder(id: string): Promise<boolean> {
    const reminder = await this.storage.get(id);
    if (!reminder || reminder.status !== ReminderStatus.PAUSED) return false;

    const updated = await this.storage.update(id, { status: ReminderStatus.ACTIVE });
    if (updated) {
      await this.scheduler.scheduleReminder(updated);
      
      console.log(`▶️ Resumed reminder: ${reminder.title} (${id})`);
      this.emit('reminderResumed', id);
      return true;
    }
    
    return false;
  }

  // Status and monitoring methods
  getSystemStatus() {
    return {
      initialized: this.isInitialized,
      config: this.config,
      scheduler: this.scheduler.getSchedulerStatus(),
    };
  }

  async getActiveReminders(): Promise<Reminder[]> {
    return await this.storage.getActiveReminders();
  }

  async getUpcomingReminders(limit: number = 10): Promise<Reminder[]> {
    return await this.storage.getNextExecutions(limit);
  }

  // Helper methods
  private calculateNextExecution(reminder: Reminder): Date | undefined {
    const { type, schedule } = reminder;
    const now = new Date();

    switch (type) {
      case ReminderType.ONE_TIME:
        return schedule.executeAt;

      case ReminderType.RECURRING:
        // For cron expressions, we'll return a placeholder
        // The actual scheduling is handled by node-cron
        return new Date(now.getTime() + 60 * 1000); // 1 minute from now as placeholder

      case ReminderType.NEWS_MONITORING:
        const startTime = schedule.pollingStartTime || now;
        return startTime > now ? startTime : now;

      default:
        return undefined;
    }
  }

  private setupEventHandlers(): void {
    // Forward scheduler events
    this.scheduler.on('executed', (reminder: Reminder, result: ReminderExecutionResult) => {
      this.emit('reminderExecuted', reminder, result);
    });

    this.scheduler.on('failed', (reminder: Reminder, result: ReminderExecutionResult) => {
      this.emit('reminderFailed', reminder, result);
    });

    this.scheduler.on('notification', (notification: any) => {
      this.emit('notification', notification);
    });

    this.scheduler.on('web_search_needed', (data: any) => {
      this.emit('webSearchNeeded', data);
    });

    this.scheduler.on('news_poll_needed', (data: any) => {
      this.emit('newsPollingNeeded', data);
    });

    this.scheduler.on('agent_handoff_needed', (data: any) => {
      this.emit('agentHandoffNeeded', data);
    });
  }

  // Recovery methods
  private async performRecovery(): Promise<RecoveryResult> {
    try {
      const recoveryData = await this.loadRecoveryData();
      if (!recoveryData) {
        return {
          success: true,
          recoveredReminders: 0,
          rescheduledTasks: 0,
          missedExecutions: 0,
          errors: [],
        };
      }

      console.log('🔄 Performing reminder system recovery...');
      
      const result: RecoveryResult = {
        success: true,
        recoveredReminders: 0,
        rescheduledTasks: 0,
        missedExecutions: 0,
        errors: [],
      };

      const now = new Date();
      
      // Process recovered reminders
      for (const reminder of recoveryData.reminders) {
        try {
          // Check if reminder still exists in storage
          const existing = await this.storage.get(reminder.id);
          if (!existing && reminder.status === ReminderStatus.ACTIVE) {
            // Reminder was lost, recreate it and schedule it
            const created = await this.storage.create(reminder as any);
            try {
              await this.scheduler.scheduleReminder(created);
            } catch (schedErr) {
              // Non-fatal: we'll record the error but continue
              result.errors.push(`Failed to schedule recovered reminder ${created.id}: ${schedErr}`);
            }
            result.recoveredReminders++;
          }
        } catch (error) {
          result.errors.push(`Failed to recover reminder ${reminder.id}: ${error}`);
        }
      }

      // Process scheduled tasks
      for (const task of recoveryData.scheduledTasks) {
        try {
          const reminder = await this.storage.get(task.reminderId);
          if (reminder && reminder.status === ReminderStatus.ACTIVE) {
            // Check if executions were missed
            if (task.nextRun < now) {
              result.missedExecutions += task.missedExecutions || 1;
              
              // Update the reminder's next execution time
              const nextExecution = this.calculateNextExecution(reminder);
              if (nextExecution) {
                await this.storage.update(reminder.id, { nextExecutionAt: nextExecution });
              }
            }

            // Ensure the scheduler is aware of this reminder
            try {
              const toSchedule = await this.storage.get(reminder.id); // get freshest copy
              if (toSchedule) {
                await this.scheduler.scheduleReminder(toSchedule);
                result.rescheduledTasks++;
              }
            } catch (schedErr) {
              result.errors.push(`Failed to reschedule task ${task.reminderId}: ${schedErr}`);
            }
          }
        } catch (error) {
          result.errors.push(`Failed to reschedule task ${task.reminderId}: ${error}`);
        }
      }

      console.log(`✅ Recovery complete: ${result.recoveredReminders} reminders, ${result.rescheduledTasks} tasks, ${result.missedExecutions} missed executions`);
      
      if (result.errors.length > 0) {
        console.warn(`⚠️ Recovery errors: ${result.errors.length}`);
        result.errors.forEach(error => console.warn(`  - ${error}`));
      }

      return result;
      
    } catch (error) {
      console.error('❌ Recovery failed:', error);
      return {
        success: false,
        recoveredReminders: 0,
        rescheduledTasks: 0,
        missedExecutions: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  private async saveRecoveryData(): Promise<void> {
    try {
      const activeReminders = await this.storage.getActiveReminders();
      const schedulerStatus = this.scheduler.getSchedulerStatus();
      
      const recoveryData: RecoveryData = {
        reminders: activeReminders,
        scheduledTasks: activeReminders.map(r => ({
          reminderId: r.id,
          nextRun: r.nextExecutionAt || new Date(),
          missedExecutions: 0,
        })),
        lastShutdownAt: new Date(),
        version: '1.0.0',
      };

      await fs.mkdir(path.dirname(this.RECOVERY_FILE), { recursive: true });
      await fs.writeFile(this.RECOVERY_FILE, JSON.stringify(recoveryData, null, 2));
      
      console.log('💾 Recovery data saved');
    } catch (error) {
      console.error('Failed to save recovery data:', error);
    }
  }

  private async loadRecoveryData(): Promise<RecoveryData | null> {
    try {
      const content = await fs.readFile(this.RECOVERY_FILE, 'utf-8');
      const data = JSON.parse(content);
      
      // Parse dates
      data.lastShutdownAt = new Date(data.lastShutdownAt);
      data.reminders = data.reminders.map((r: any) => ({
        ...r,
        createdAt: new Date(r.createdAt),
        updatedAt: new Date(r.updatedAt),
        lastExecutedAt: r.lastExecutedAt ? new Date(r.lastExecutedAt) : undefined,
        nextExecutionAt: r.nextExecutionAt ? new Date(r.nextExecutionAt) : undefined,
        schedule: {
          ...r.schedule,
          executeAt: r.schedule.executeAt ? new Date(r.schedule.executeAt) : undefined,
          pollingStartTime: r.schedule.pollingStartTime ? new Date(r.schedule.pollingStartTime) : undefined,
          pollingEndTime: r.schedule.pollingEndTime ? new Date(r.schedule.pollingEndTime) : undefined,
        },
      }));
      data.scheduledTasks = data.scheduledTasks.map((t: any) => ({
        ...t,
        nextRun: new Date(t.nextRun),
      }));
      
      return data;
    } catch (error) {
      // Recovery file doesn't exist or is invalid
      return null;
    }
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\n📡 Received ${signal}, shutting down gracefully...`);
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('beforeExit', () => {
      if (this.isInitialized) {
        this.saveRecoveryData().catch(console.error);
      }
    });
  }
}

// Export singleton instance
export const reminderManager = new ReminderManager();