import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';
import {
  NotificationMessage,
  NotificationChannel,
  NewsUpdate,
  Reminder,
} from '../types';

interface NotificationQueue {
  id: string;
  messages: NotificationMessage[];
  lastProcessed: Date;
  isProcessing: boolean;
}

interface DeliveryStats {
  totalSent: number;
  webUISent: number;
  fileQueueSent: number;
  memoryStoreSent: number;
  errors: number;
  lastDelivery: Date | null;
}

export class NotificationDelivery extends EventEmitter {
  private readonly QUEUE_DIR = path.join(process.cwd(), 'data', 'notifications');
  private readonly QUEUE_FILE = path.join(this.QUEUE_DIR, 'queue.json');
  private queue: NotificationQueue;
  private stats: DeliveryStats;
  private isInitialized = false;
  private processingInterval?: NodeJS.Timeout;

  // Store active SSE connections for real-time delivery
  private sseConnections = new Map<string, { res: any; sessionId: string; lastPing: Date }>();

  constructor() {
    super();
    
    this.queue = {
      id: 'main',
      messages: [],
      lastProcessed: new Date(),
      isProcessing: false,
    };

    this.stats = {
      totalSent: 0,
      webUISent: 0,
      fileQueueSent: 0,
      memoryStoreSent: 0,
      errors: 0,
      lastDelivery: null,
    };

    this.setupCleanupInterval();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('📬 Initializing Notification Delivery...');

    try {
      await this.ensureDirectoryExists();
      await this.loadQueue();
      this.startProcessing();
      
      this.isInitialized = true;
      console.log('✅ Notification Delivery initialized');
      this.emit('initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize Notification Delivery:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.isInitialized) return;

    console.log('🛑 Shutting down Notification Delivery...');

    try {
      this.stopProcessing();
      await this.saveQueue();
      this.closeAllSSEConnections();
      
      this.isInitialized = false;
      console.log('✅ Notification Delivery shut down');
      this.emit('shutdown');
      
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
    }
  }

  // SSE Connection Management
  registerSSEConnection(res: any, sessionId: string): string {
    const connectionId = `sse_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    
    this.sseConnections.set(connectionId, {
      res,
      sessionId,
      lastPing: new Date(),
    });

    // Setup connection cleanup
    res.on('close', () => {
      this.sseConnections.delete(connectionId);
      console.log(`📡 SSE connection closed: ${connectionId}`);
    });

    console.log(`📡 SSE connection registered: ${connectionId} (session: ${sessionId})`);
    return connectionId;
  }

  // Core notification methods
  async sendNotification(notification: Omit<NotificationMessage, 'id' | 'createdAt'>): Promise<string> {
    const message: NotificationMessage = {
      ...notification,
      id: this.generateNotificationId(),
      createdAt: new Date(),
    };

    // Add to queue
    this.queue.messages.push(message);
    
    // Immediate delivery attempt for real-time channels
    if (message.channels.includes(NotificationChannel.WEB_UI)) {
      await this.deliverToWebUI(message);
    }

    // Queue other channels for batch processing
    await this.saveQueue();
    
    console.log(`📬 Queued notification: ${message.title} (${message.id})`);
    this.emit('queued', message);
    
    return message.id;
  }

  async sendReminderNotification(reminder: Reminder, customMessage?: string): Promise<string> {
    const message = customMessage || `Reminder: ${reminder.title}`;
    
    return await this.sendNotification({
      reminderId: reminder.id,
      type: 'reminder',
      title: reminder.title,
      content: message,
      metadata: {
        reminderType: reminder.type,
        priority: reminder.priority,
      },
      channels: reminder.action.notificationChannels,
      sessionId: reminder.sessionId,
    });
  }

  async sendNewsUpdateNotification(newsUpdate: NewsUpdate): Promise<string> {
    const title = `News Update: ${newsUpdate.title}`;
    const content = this.formatNewsUpdate(newsUpdate);
    
    return await this.sendNotification({
      reminderId: newsUpdate.reminderId,
      type: 'news_update',
      title,
      content,
      metadata: {
        changeType: newsUpdate.changeType,
        sourceCount: newsUpdate.sources.length,
        keyPointCount: newsUpdate.keyPoints.length,
        sentiment: newsUpdate.sentiment,
      },
      channels: [NotificationChannel.WEB_UI, NotificationChannel.FILE_QUEUE],
    });
  }

  async sendSystemNotification(title: string, content: string, sessionId?: string): Promise<string> {
    return await this.sendNotification({
      reminderId: '',
      type: 'system',
      title,
      content,
      channels: [NotificationChannel.WEB_UI],
      sessionId,
    });
  }

  async sendErrorNotification(error: string, reminderId?: string, sessionId?: string): Promise<string> {
    return await this.sendNotification({
      reminderId: reminderId || '',
      type: 'error',
      title: 'System Error',
      content: error,
      metadata: { errorType: 'system' },
      channels: [NotificationChannel.WEB_UI, NotificationChannel.FILE_QUEUE],
      sessionId,
    });
  }

  // Delivery methods for each channel
  private async deliverToWebUI(message: NotificationMessage): Promise<boolean> {
    try {
      const sseData = {
        type: 'notification',
        id: message.id,
        notification: message,
        timestamp: new Date().toISOString(),
      };

      let delivered = false;

      // Send to all connections, or specific session if specified
      for (const [connectionId, connection] of this.sseConnections) {
        try {
          // Filter by session if specified
          if (message.sessionId && connection.sessionId !== message.sessionId) {
            continue;
          }

          connection.res.write(`data: ${JSON.stringify(sseData)}\n\n`);
          connection.lastPing = new Date();
          delivered = true;
          
        } catch (error) {
          console.warn(`Failed to deliver to SSE connection ${connectionId}:`, error);
          this.sseConnections.delete(connectionId);
        }
      }

      if (delivered) {
        message.deliveredAt = new Date();
        this.stats.webUISent++;
        this.stats.totalSent++;
        this.stats.lastDelivery = new Date();
        
        console.log(`📱 Delivered to WebUI: ${message.title}`);
        this.emit('delivered', message, NotificationChannel.WEB_UI);
      }

      return delivered;
      
    } catch (error) {
      console.error('Failed to deliver to WebUI:', error);
      this.stats.errors++;
      this.emit('deliveryError', message, NotificationChannel.WEB_UI, error);
      return false;
    }
  }

  private async deliverToFileQueue(message: NotificationMessage): Promise<boolean> {
    try {
      const queueFile = path.join(this.QUEUE_DIR, `${message.sessionId || 'global'}_notifications.json`);
      
      let notifications: NotificationMessage[] = [];
      
      try {
        const content = await fs.readFile(queueFile, 'utf-8');
        notifications = JSON.parse(content);
      } catch {
        // File doesn't exist or is invalid
      }
      
      notifications.push(message);
      
      // Keep only last 100 notifications per file
      if (notifications.length > 100) {
        notifications = notifications.slice(-100);
      }
      
      await fs.writeFile(queueFile, JSON.stringify(notifications, null, 2));
      
      message.deliveredAt = new Date();
      this.stats.fileQueueSent++;
      this.stats.totalSent++;
      this.stats.lastDelivery = new Date();
      
      console.log(`📁 Delivered to file queue: ${message.title}`);
      this.emit('delivered', message, NotificationChannel.FILE_QUEUE);
      
      return true;
      
    } catch (error) {
      console.error('Failed to deliver to file queue:', error);
      this.stats.errors++;
      this.emit('deliveryError', message, NotificationChannel.FILE_QUEUE, error);
      return false;
    }
  }

  private async deliverToMemoryStore(message: NotificationMessage): Promise<boolean> {
    try {
      // This would integrate with the memory system to store notifications
      // For now, we'll emit an event that the memory system can listen to
      this.emit('storeInMemory', message);
      
      message.deliveredAt = new Date();
      this.stats.memoryStoreSent++;
      this.stats.totalSent++;
      this.stats.lastDelivery = new Date();
      
      console.log(`🧠 Delivered to memory store: ${message.title}`);
      this.emit('delivered', message, NotificationChannel.MEMORY_STORE);
      
      return true;
      
    } catch (error) {
      console.error('Failed to deliver to memory store:', error);
      this.stats.errors++;
      this.emit('deliveryError', message, NotificationChannel.MEMORY_STORE, error);
      return false;
    }
  }

  // Queue processing
  private startProcessing(): void {
    if (this.processingInterval) return;
    
    // Process queue every 30 seconds
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 30000);
    
    // Initial processing
    setImmediate(() => this.processQueue());
  }

  private stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }
  }

  private async processQueue(): Promise<void> {
    if (this.queue.isProcessing || this.queue.messages.length === 0) {
      return;
    }

    this.queue.isProcessing = true;
    console.log(`📬 Processing ${this.queue.messages.length} queued notifications...`);

    try {
      const messagesToProcess = [...this.queue.messages];
      const processedMessages: string[] = [];

      for (const message of messagesToProcess) {
        try {
          // Skip already delivered messages
          if (message.deliveredAt) {
            processedMessages.push(message.id);
            continue;
          }

          let delivered = false;

          // Deliver to each requested channel
          for (const channel of message.channels) {
            try {
              switch (channel) {
                case NotificationChannel.WEB_UI:
                  await this.deliverToWebUI(message);
                  delivered = true;
                  break;
                
                case NotificationChannel.FILE_QUEUE:
                  await this.deliverToFileQueue(message);
                  delivered = true;
                  break;
                
                case NotificationChannel.MEMORY_STORE:
                  await this.deliverToMemoryStore(message);
                  delivered = true;
                  break;
              }
            } catch (error) {
              console.error(`Failed to deliver to channel ${channel}:`, error);
            }
          }

          if (delivered) {
            processedMessages.push(message.id);
          }
          
        } catch (error) {
          console.error(`Failed to process message ${message.id}:`, error);
        }
      }

      // Remove processed messages from queue
      this.queue.messages = this.queue.messages.filter(m => !processedMessages.includes(m.id));
      this.queue.lastProcessed = new Date();

      // Save updated queue
      await this.saveQueue();
      
      if (processedMessages.length > 0) {
        console.log(`✅ Processed ${processedMessages.length} notifications`);
      }
      
    } catch (error) {
      console.error('Queue processing error:', error);
    } finally {
      this.queue.isProcessing = false;
    }
  }

  // Utility methods
  private formatNewsUpdate(newsUpdate: NewsUpdate): string {
    let content = `**${newsUpdate.title}**\n\n`;
    content += `${newsUpdate.summary}\n\n`;
    
    if (newsUpdate.keyPoints.length > 0) {
      content += `**Key Points:**\n`;
      newsUpdate.keyPoints.forEach((point, index) => {
        content += `${index + 1}. ${point}\n`;
      });
      content += '\n';
    }
    
    if (newsUpdate.sources.length > 0) {
      const primarySource = newsUpdate.sources[0];
      content += `**Source:** [${primarySource.title}](${primarySource.url})\n`;
      
      if (newsUpdate.sources.length > 1) {
        content += `*and ${newsUpdate.sources.length - 1} other source(s)*\n`;
      }
    }
    
    content += `\n*Update Type: ${newsUpdate.changeType}*`;
    if (newsUpdate.sentiment && newsUpdate.sentiment !== 'neutral') {
      content += ` | *Sentiment: ${newsUpdate.sentiment}*`;
    }
    
    return content;
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.mkdir(this.QUEUE_DIR, { recursive: true });
    } catch (error) {
      console.warn('Failed to create notification directory:', error);
    }
  }

  private async loadQueue(): Promise<void> {
    try {
      const content = await fs.readFile(this.QUEUE_FILE, 'utf-8');
      const data = JSON.parse(content);
      
      this.queue = {
        ...data,
        lastProcessed: new Date(data.lastProcessed),
        isProcessing: false, // Reset processing state
        messages: data.messages.map((m: any) => ({
          ...m,
          createdAt: new Date(m.createdAt),
          deliveredAt: m.deliveredAt ? new Date(m.deliveredAt) : undefined,
          readAt: m.readAt ? new Date(m.readAt) : undefined,
        })),
      };
      
      console.log(`📥 Loaded ${this.queue.messages.length} notifications from queue`);
    } catch (error) {
      // Queue file doesn't exist or is invalid, start fresh
      console.log('📥 Starting with empty notification queue');
    }
  }

  private async saveQueue(): Promise<void> {
    try {
      await fs.writeFile(this.QUEUE_FILE, JSON.stringify(this.queue, null, 2));
    } catch (error) {
      console.error('Failed to save notification queue:', error);
    }
  }

  private closeAllSSEConnections(): void {
    for (const [connectionId, connection] of this.sseConnections) {
      try {
        connection.res.end();
      } catch (error) {
        console.warn(`Failed to close SSE connection ${connectionId}:`, error);
      }
    }
    this.sseConnections.clear();
  }

  private setupCleanupInterval(): void {
    // Clean up old SSE connections every 5 minutes
    setInterval(() => {
      const now = new Date();
      const staleThreshold = 10 * 60 * 1000; // 10 minutes
      
      for (const [connectionId, connection] of this.sseConnections) {
        if (now.getTime() - connection.lastPing.getTime() > staleThreshold) {
          console.log(`🧹 Removing stale SSE connection: ${connectionId}`);
          this.sseConnections.delete(connectionId);
        }
      }
    }, 5 * 60 * 1000);
  }

  // Public status methods
  getStats(): DeliveryStats {
    return { ...this.stats };
  }

  getQueueStatus() {
    return {
      queueLength: this.queue.messages.length,
      lastProcessed: this.queue.lastProcessed,
      isProcessing: this.queue.isProcessing,
      activeConnections: this.sseConnections.size,
    };
  }

  async getSessionNotifications(sessionId: string, limit: number = 50): Promise<NotificationMessage[]> {
    try {
      const queueFile = path.join(this.QUEUE_DIR, `${sessionId}_notifications.json`);
      const content = await fs.readFile(queueFile, 'utf-8');
      const notifications = JSON.parse(content);
      
      return notifications
        .slice(-limit)
        .map((n: any) => ({
          ...n,
          createdAt: new Date(n.createdAt),
          deliveredAt: n.deliveredAt ? new Date(n.deliveredAt) : undefined,
          readAt: n.readAt ? new Date(n.readAt) : undefined,
        }));
        
    } catch (error) {
      return [];
    }
  }

  async markAsRead(notificationId: string, sessionId: string): Promise<boolean> {
    try {
      const queueFile = path.join(this.QUEUE_DIR, `${sessionId}_notifications.json`);
      const content = await fs.readFile(queueFile, 'utf-8');
      const notifications = JSON.parse(content);
      
      const notification = notifications.find((n: any) => n.id === notificationId);
      if (notification) {
        notification.readAt = new Date().toISOString();
        await fs.writeFile(queueFile, JSON.stringify(notifications, null, 2));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }
  }
}

// Export singleton instance
export const notificationDelivery = new NotificationDelivery();