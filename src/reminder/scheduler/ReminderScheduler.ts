import * as cron from 'node-cron';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>minderExecut<PERSON>R<PERSON>ult,
  ReminderStatus,
  ReminderType,
  ScheduledTask,
  SchedulerStatus,
} from '../types';
import { ReminderStorage } from '../storage/ReminderStorage';
import { EventEmitter } from 'events';

export class ReminderScheduler extends EventEmitter {
  private scheduledTasks = new Map<string, ScheduledTask>();
  private storage: ReminderStorage;
  private isRunning = false;
  private startTime: Date;
  private maintenanceInterval?: NodeJS.Timeout;

  constructor(storage: ReminderStorage) {
    super();
    this.storage = storage;
    this.startTime = new Date();
    this.setupMaintenanceTasks();
  }

  async start(): Promise<void> {
    if (this.isRunning) return;
    
    console.log('🕐 Starting Reminder Scheduler...');
    this.isRunning = true;
    
    // Load and schedule all active reminders
    await this.loadActiveReminders();
    
    console.log(`✅ Reminder Scheduler started with ${this.scheduledTasks.size} active reminders`);
    this.emit('started');
  }

  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    console.log('🛑 Stopping Reminder Scheduler...');
    this.isRunning = false;
    
    // Clear all scheduled tasks
    for (const [reminderId, task] of this.scheduledTasks) {
      this.clearScheduledTask(reminderId, task);
    }
    
    this.scheduledTasks.clear();
    
    if (this.maintenanceInterval) {
      clearInterval(this.maintenanceInterval);
    }
    
    console.log('✅ Reminder Scheduler stopped');
    this.emit('stopped');
  }

  async scheduleReminder(reminder: Reminder): Promise<boolean> {
    if (!this.isRunning) {
      console.warn(`Cannot schedule reminder ${reminder.id}: scheduler not running`);
      return false;
    }

    try {
      // Clear any existing schedule for this reminder
      await this.unscheduleReminder(reminder.id);
      
      const task = await this.createScheduledTask(reminder);
      if (task) {
        this.scheduledTasks.set(reminder.id, task);
        console.log(`📅 Scheduled reminder: ${reminder.title} (${reminder.id})`);
        this.emit('scheduled', reminder);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Failed to schedule reminder ${reminder.id}:`, error);
      return false;
    }
  }

  async unscheduleReminder(reminderId: string): Promise<boolean> {
    const task = this.scheduledTasks.get(reminderId);
    if (!task) return false;
    
    this.clearScheduledTask(reminderId, task);
    this.scheduledTasks.delete(reminderId);
    
    console.log(`🗑️ Unscheduled reminder: ${reminderId}`);
    this.emit('unscheduled', reminderId);
    return true;
  }

  async rescheduleReminder(reminder: Reminder): Promise<boolean> {
    await this.unscheduleReminder(reminder.id);
    return await this.scheduleReminder(reminder);
  }

  getSchedulerStatus(): SchedulerStatus {
    const now = Date.now();
    const uptimeSeconds = Math.floor((now - this.startTime.getTime()) / 1000);
    
    let failedJobs = 0;
    for (const task of this.scheduledTasks.values()) {
      // Count tasks that haven't run for a while as potentially failed
      if (task.lastRun && now - task.lastRun.getTime() > 24 * 60 * 60 * 1000) {
        failedJobs++;
      }
    }
    
    return {
      activeJobs: this.scheduledTasks.size,
      scheduledReminders: this.scheduledTasks.size,
      failedJobs,
      uptimeSeconds,
    };
  }

  private async loadActiveReminders(): Promise<void> {
    try {
      const activeReminders = await this.storage.getActiveReminders();
      console.log(`📥 Loading ${activeReminders.length} active reminders...`);
      
      for (const reminder of activeReminders) {
        await this.scheduleReminder(reminder);
      }
    } catch (error) {
      console.error('Failed to load active reminders:', error);
    }
  }

  private async createScheduledTask(reminder: Reminder): Promise<ScheduledTask | null> {
    const { schedule, type } = reminder;
    
    switch (type) {
      case ReminderType.ONE_TIME:
        return this.createOneTimeTask(reminder);
      
      case ReminderType.RECURRING:
        return this.createRecurringTask(reminder);
      
      case ReminderType.NEWS_MONITORING:
        return this.createNewsMonitoringTask(reminder);
      
      case ReminderType.EVENT_BASED:
        return this.createEventBasedTask(reminder);
      
      default:
        console.warn(`Unknown reminder type: ${type}`);
        return null;
    }
  }

  private createOneTimeTask(reminder: Reminder): ScheduledTask | null {
    const { executeAt } = reminder.schedule;
    if (!executeAt) {
      console.warn(`One-time reminder ${reminder.id} missing executeAt`);
      return null;
    }
    
    const now = new Date();
    const delay = executeAt.getTime() - now.getTime();
    
    if (delay <= 0) {
      // Execute immediately if time has passed
      console.log(`⚡ Executing overdue reminder immediately: ${reminder.title}`);
      setImmediate(() => this.executeReminder(reminder));
      return null;
    }
    
    const timeout = setTimeout(() => {
      this.executeReminder(reminder);
    }, delay);
    
    return {
      reminderId: reminder.id,
      timeout,
      nextRun: executeAt,
    };
  }

  private createRecurringTask(reminder: Reminder): ScheduledTask | null {
    const { cronExpression, timezone } = reminder.schedule;
    if (!cronExpression) {
      console.warn(`Recurring reminder ${reminder.id} missing cronExpression`);
      return null;
    }
    
    if (!cron.validate(cronExpression)) {
      console.warn(`Invalid cron expression for reminder ${reminder.id}: ${cronExpression}`);
      return null;
    }
    
    const options: any = {
      scheduled: false,
    };
    
    if (timezone) {
      options.timezone = timezone;
    }
    
    const cronJob = cron.schedule(cronExpression, () => {
      this.executeReminder(reminder);
    }, options);
    
    cronJob.start();
    
    return {
      reminderId: reminder.id,
      cronJob,
      nextRun: new Date(), // Will be updated by maintenance
    };
  }

  private createNewsMonitoringTask(reminder: Reminder): ScheduledTask | null {
    const { pollingInterval = 30, pollingStartTime, pollingEndTime } = reminder.schedule;
    
    const now = new Date();
    let nextRun = now;
    
    // If we have a start time and it's in the future, wait until then
    if (pollingStartTime && pollingStartTime > now) {
      nextRun = pollingStartTime;
    }
    
    // Create a recurring task that polls every X minutes
    const intervalMs = pollingInterval * 60 * 1000; // Convert to milliseconds
    
    const executePolling = () => {
      const currentTime = new Date();
      
      // Check if we're within the polling window
      if (pollingEndTime && currentTime > pollingEndTime) {
        console.log(`📰 News monitoring ended for: ${reminder.title}`);
        this.completeReminder(reminder.id);
        return;
      }
      
      this.executeReminder(reminder);
      
      // Schedule next polling
      const task = this.scheduledTasks.get(reminder.id);
      if (task) {
        task.timeout = setTimeout(executePolling, intervalMs);
        task.nextRun = new Date(Date.now() + intervalMs);
      }
    };
    
    const initialDelay = nextRun.getTime() - now.getTime();
    const timeout = setTimeout(executePolling, Math.max(0, initialDelay));
    
    return {
      reminderId: reminder.id,
      timeout,
      nextRun,
    };
  }

  private createEventBasedTask(reminder: Reminder): ScheduledTask | null {
    // Event-based reminders are typically triggered by external events
    // For now, we'll treat them like one-time reminders
    return this.createOneTimeTask(reminder);
  }

  private async executeReminder(reminder: Reminder): Promise<void> {
    const startTime = new Date();
    console.log(`🚀 Executing reminder: ${reminder.title} (${reminder.id})`);
    
    try {
      const result = await this.performReminderAction(reminder);
      
      // Update execution tracking
      await this.updateReminderExecution(reminder, result);
      
      // Emit execution event
      this.emit('executed', reminder, result);
      
      console.log(`✅ Reminder executed successfully: ${reminder.title}`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Reminder execution failed: ${reminder.title} - ${errorMessage}`);
      
      const result: ReminderExecutionResult = {
        success: false,
        executedAt: startTime,
        duration: Date.now() - startTime.getTime(),
        error: errorMessage,
        shouldReschedule: true,
      };
      
      await this.handleExecutionError(reminder, result);
      this.emit('failed', reminder, result);
    }
  }

  private async performReminderAction(reminder: Reminder): Promise<ReminderExecutionResult> {
    const startTime = new Date();
    const { action } = reminder;
    
    try {
      let output: any;
      
      switch (action.type) {
        case 'notification':
          output = await this.sendNotification(reminder, action.payload);
          break;
        
        case 'web_search':
          output = await this.performWebSearch(reminder, action);
          break;
        
        case 'news_poll':
          output = await this.performNewsPolling(reminder, action);
          break;
        
        case 'agent_handoff':
          output = await this.performAgentHandoff(reminder, action);
          break;
        
        case 'custom':
          output = await this.performCustomAction(reminder, action);
          break;
        
        default:
          throw new Error(`Unknown action type: ${action.type}`);
      }
      
      const duration = Date.now() - startTime.getTime();
      
      return {
        success: true,
        executedAt: startTime,
        duration,
        output,
        shouldReschedule: this.shouldReschedule(reminder),
      };
      
    } catch (error) {
      const duration = Date.now() - startTime.getTime();
      throw error;
    }
  }

  private async sendNotification(reminder: Reminder, payload: any): Promise<any> {
    // This will be implemented by the NotificationDelivery system
    this.emit('notification', {
      reminderId: reminder.id,
      title: reminder.title,
      content: payload.message || reminder.description,
      channels: reminder.action.notificationChannels,
    });
    
    return { sent: true, channels: reminder.action.notificationChannels };
  }

  private async performWebSearch(reminder: Reminder, action: any): Promise<any> {
    // This will integrate with existing web search tools
    this.emit('web_search_needed', {
      reminderId: reminder.id,
      query: action.searchQuery,
      options: action.searchOptions,
    });
    
    return { searchRequested: true, query: action.searchQuery };
  }

  private async performNewsPolling(reminder: Reminder, action: any): Promise<any> {
    // This will be implemented by the NewsPollingService
    this.emit('news_poll_needed', {
      reminderId: reminder.id,
      config: action.newsConfig,
    });
    
    return { pollingRequested: true, topic: action.newsConfig?.topic };
  }

  private async performAgentHandoff(reminder: Reminder, action: any): Promise<any> {
    // This will integrate with the agent system
    this.emit('agent_handoff_needed', {
      reminderId: reminder.id,
      targetAgent: action.targetAgent,
      message: action.handoffMessage,
    });
    
    return { handoffRequested: true, targetAgent: action.targetAgent };
  }

  private async performCustomAction(reminder: Reminder, action: any): Promise<any> {
    // Custom actions can be extended by implementing handlers
    this.emit('custom_action_needed', {
      reminderId: reminder.id,
      payload: action.payload,
    });
    
    return { customActionRequested: true };
  }

  private shouldReschedule(reminder: Reminder): boolean {
    return reminder.type === ReminderType.RECURRING || 
           reminder.type === ReminderType.NEWS_MONITORING;
  }

  private async updateReminderExecution(reminder: Reminder, result: ReminderExecutionResult): Promise<void> {
    try {
      const updates: Partial<Reminder> = {
        lastExecutedAt: result.executedAt,
        executionCount: reminder.executionCount + 1,
      };
      
      if (!result.success) {
        updates.errorCount = reminder.errorCount + 1;
        updates.lastError = result.error;
      }
      
      // Calculate next execution time for recurring reminders
      if (result.shouldReschedule) {
        updates.nextExecutionAt = this.calculateNextExecution(reminder);
      } else {
        updates.status = ReminderStatus.COMPLETED;
        updates.nextExecutionAt = undefined;
      }
      
      await this.storage.update(reminder.id, updates);
    } catch (error) {
      console.error(`Failed to update reminder execution: ${reminder.id}`, error);
    }
  }

  private calculateNextExecution(reminder: Reminder): Date | undefined {
    const { type, schedule } = reminder;
    
    if (type === ReminderType.RECURRING && schedule.cronExpression) {
      // For cron expressions, we'll estimate the next run
      // This is a simplified calculation - node-cron handles the actual scheduling
      return new Date(Date.now() + 60 * 60 * 1000); // Rough estimate: 1 hour from now
    }
    
    if (type === ReminderType.NEWS_MONITORING && schedule.pollingInterval) {
      const intervalMs = schedule.pollingInterval * 60 * 1000;
      return new Date(Date.now() + intervalMs);
    }
    
    return undefined;
  }

  private async handleExecutionError(reminder: Reminder, result: ReminderExecutionResult): Promise<void> {
    const maxRetries = reminder.maxRetries || 3;
    
    if (reminder.errorCount >= maxRetries) {
      console.warn(`Reminder ${reminder.id} exceeded max retries, marking as failed`);
      await this.storage.update(reminder.id, {
        status: ReminderStatus.FAILED,
        lastError: result.error,
      });
      
      await this.unscheduleReminder(reminder.id);
    }
  }

  private async completeReminder(reminderId: string): Promise<void> {
    await this.storage.update(reminderId, {
      status: ReminderStatus.COMPLETED,
      nextExecutionAt: undefined,
    });
    
    await this.unscheduleReminder(reminderId);
  }

  private clearScheduledTask(reminderId: string, task: ScheduledTask): void {
    if (task.cronJob) {
      task.cronJob.destroy();
    }
    
    if (task.timeout) {
      clearTimeout(task.timeout);
    }
  }

  private setupMaintenanceTasks(): void {
    // Run maintenance every 5 minutes
    this.maintenanceInterval = setInterval(() => {
      this.performMaintenance();
    }, 5 * 60 * 1000);
  }

  private async performMaintenance(): Promise<void> {
    if (!this.isRunning) return;
    
    try {
      // Check for reminders that should have executed but didn't
      const activeReminders = await this.storage.getActiveReminders();
      const now = new Date();
      
      for (const reminder of activeReminders) {
        if (reminder.nextExecutionAt && reminder.nextExecutionAt < now) {
          const timeDiff = now.getTime() - reminder.nextExecutionAt.getTime();
          
          // If execution is more than 5 minutes overdue, execute it
          if (timeDiff > 5 * 60 * 1000) {
            console.warn(`⚠️ Found overdue reminder: ${reminder.title} (${timeDiff / 1000}s late)`);
            await this.executeReminder(reminder);
          }
        }
      }
      
      // Clean up completed/failed reminders from scheduler
      for (const [reminderId] of this.scheduledTasks) {
        const reminder = await this.storage.get(reminderId);
        if (!reminder || reminder.status !== ReminderStatus.ACTIVE) {
          await this.unscheduleReminder(reminderId);
        }
      }
      
    } catch (error) {
      console.error('Maintenance task failed:', error);
    }
  }
}