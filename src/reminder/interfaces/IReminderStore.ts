// Unified reminder storage interface (following IVectorStore pattern)

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NewsMonitoringConfig,
  NotificationChannel,
} from '../types';

export interface ReminderStoreConfig {
  // Common configuration
  maxReminders?: number;
  defaultPollingInterval?: number;
  enableScheduling?: boolean;
  
  // API-specific (browser environment)
  apiBaseUrl?: string;
  apiTimeout?: number;
  
  // File storage specific (server environment)
  storageDir?: string;
  enableFileLocking?: boolean;
  
  // Qdrant specific (shared storage)
  qdrantUrl?: string;
  qdrantApiKey?: string;
  collectionName?: string;
}

export interface IReminderStore {
  // Core CRUD operations
  create(reminderData: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reminder>;
  get(id: string): Promise<Reminder | null>;
  update(id: string, updates: Partial<Reminder>): Promise<Reminder | null>;
  delete(id: string): Promise<boolean>;
  list(filters?: ReminderFilters): Promise<Reminder[]>;
  
  // Status-based queries
  getByStatus(status: ReminderStatus): Promise<Reminder[]>;
  getActiveReminders(): Promise<Reminder[]>;
  getNextExecutions(limit?: number): Promise<Reminder[]>;
  
  // News monitoring specific
  createNewsMonitoring(config: {
    title: string;
    topic: string;
    eventDate?: Date;
    eventStartTime?: Date;
    eventEndTime?: Date;
    summaryType?: string;
    pollingInterval?: number;
    searchQueries?: string[];
    keywordsToTrack?: string[];
    notificationChannels?: NotificationChannel[];
    sessionId?: string;
    createdBy?: string;
  }): Promise<Reminder>;
  
  // Scheduling operations (may be no-op in browser)
  schedule(reminder: Reminder): Promise<boolean>;
  unschedule(reminderId: string): Promise<boolean>;
  
  // Environment-specific capabilities
  supportsScheduling(): boolean;
  supportsFileOperations(): boolean;
  supportsRealTimeUpdates(): boolean;
  
  // Initialization and cleanup
  initialize(): Promise<void>;
  cleanup(): Promise<void>;
  
  // Health and status
  isHealthy(): Promise<boolean>;
  getStats(): Promise<{
    totalReminders: number;
    activeReminders: number;
    scheduledReminders: number;
    environment: 'browser' | 'server';
    storageType: string;
  }>;
}

export interface ReminderOperationResult {
  success: boolean;
  reminderId?: string;
  error?: string;
  message?: string;
  data?: any;
}

export interface ReminderSearchResult {
  reminders: Reminder[];
  totalFound: number;
  searchTime: number;
  source: 'file' | 'api' | 'qdrant' | 'memory';
}

// Error types for unified error handling
export class ReminderStoreError extends Error {
  constructor(
    public operation: string,
    public reason: string,
    public recoverable: boolean = true
  ) {
    super(`${operation} failed: ${reason}`);
    this.name = 'ReminderStoreError';
  }
}

export class ReminderNotFoundError extends ReminderStoreError {
  constructor(reminderId: string) {
    super('get', `Reminder ${reminderId} not found`, false);
    this.name = 'ReminderNotFoundError';
  }
}

export class ReminderApiError extends ReminderStoreError {
  constructor(
    public endpoint: string,
    public statusCode: number,
    reason: string
  ) {
    super('api_call', `${endpoint} (${statusCode}): ${reason}`, true);
    this.name = 'ReminderApiError';
  }
}