// <PERSON><PERSON> removed — rely on <PERSON><PERSON>/X11 or Browser-Use MCP elsewhere
import Docker from 'dockerode';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import { config } from '../../utils/config';

const execAsync = promisify(exec);

export interface EnvironmentConfig {
  type: 'browser' | 'docker';
  width: number;
  height: number;
  headless?: boolean;
  sandbox?: boolean;
}

export interface ComputerAction {
  type: 'click' | 'scroll' | 'keypress' | 'type' | 'wait' | 'screenshot';
  x?: number;
  y?: number;
  button?: 'left' | 'right' | 'middle';
  scrollX?: number;
  scrollY?: number;
  keys?: string[];
  text?: string;
}

// BrowserEnvironment removed

export class DockerEnvironment {
  private docker: Docker;
  private container: Docker.Container | null = null;
  private config: EnvironmentConfig;
  private containerName: string;
  private display: string = ':99';

  constructor(config: EnvironmentConfig, containerName: string = 'dante-computer-use') {
    this.config = config;
    this.containerName = containerName;
    this.docker = new Docker();
  }

  async initialize(): Promise<void> {
    try {
      // Check if container already exists
      const containers = await this.docker.listContainers({ all: true });
      const existingContainer = containers.find(c => c.Names.includes(`/${this.containerName}`));

      if (existingContainer) {
        this.container = this.docker.getContainer(existingContainer.Id);
        
        if (existingContainer.State !== 'running') {
          await this.container.start();
        }
      } else {
        // Create new container
        await this.createContainer();
      }

      // Wait for container to be ready
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      throw new Error(`Failed to initialize Docker environment: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async createContainer(): Promise<void> {
    const exposeVnc = (process.env.COMPUTER_USE_VNC_EXPOSE || '').toLowerCase() === 'true';
    const containerConfig: any = {
      Image: 'dante-computer-use:latest',
      name: this.containerName,
      Env: [
        `DISPLAY=${this.display}`
      ]
    };

    if (exposeVnc) {
      containerConfig.ExposedPorts = { '5900/tcp': {} };
      containerConfig.HostConfig = {
        PortBindings: {
          '5900/tcp': [{ HostPort: process.env.COMPUTER_USE_VNC_PORT || '5900' }]
        }
      };
    }

    // Ensure base image exists; build automatically if missing
    const ensureImage = async () => {
      try {
        await this.docker.getImage('dante-computer-use:latest').inspect();
        return true;
      } catch {
        return false;
      }
    };

    const imagePresent = await ensureImage();
    if (!imagePresent) {
      const buildContext = path.resolve(process.cwd(), 'docker/computer-use');
      const autoBuild = config.computerUse.autoBuild;

      if (!autoBuild) {
        throw new Error(
          `Computer-use Docker image missing. Manual build required. Run: \n` +
          `  bun run computer-use:build\n` +
          `Or enable auto-build by setting COMPUTER_USE_AUTOBUILD=true`
        );
      }

      // Streamed build to avoid buffering large output in memory
      console.log(`🛠️ Building Docker image 'dante-computer-use:latest' from ${buildContext}...`);
      await new Promise<void>((resolve, reject) => {
        const child = spawn('docker', ['build', '--platform=linux/amd64', '-t', 'dante-computer-use', buildContext], {
          stdio: 'pipe'
        });
        child.stdout?.on('data', (d) => { try { process.stdout.write(d); } catch {} });
        child.stderr?.on('data', (d) => { try { process.stderr.write(d); } catch {} });
        child.on('error', reject);
        child.on('exit', (code) => {
          if (code === 0) {
            console.log('✅ Docker image built successfully');
            resolve();
          } else {
            reject(new Error(`Docker build exited with code ${code}`));
          }
        });
      });
    }

    try {
      this.container = await this.docker.createContainer(containerConfig);
    } catch (err: any) {
      // If creation fails because image was still not found, attempt one more build and retry
      const msg = err?.message || '';
      if (/No such image/i.test(msg) || err?.statusCode === 404) {
        const buildContext = path.resolve(process.cwd(), 'docker/computer-use');
        console.warn('⚠️ Base image missing at container create; rebuilding image and retrying...');
        await new Promise<void>((resolve, reject) => {
          const child = spawn('docker', ['build', '--platform=linux/amd64', '-t', 'dante-computer-use', buildContext], {
            stdio: 'pipe'
          });
          child.stdout?.on('data', (d) => { try { process.stdout.write(d); } catch {} });
          child.stderr?.on('data', (d) => { try { process.stderr.write(d); } catch {} });
          child.on('error', reject);
          child.on('exit', (code) => (code === 0 ? resolve() : reject(new Error(`Docker build exited with code ${code}`))));
        });
        this.container = await this.docker.createContainer(containerConfig);
      } else {
        throw err;
      }
    }

    await this.container.start();
  }

  async navigateTo(url: string): Promise<void> {
    if (!this.container) throw new Error('Docker environment not initialized');
    // Try common mechanisms to open a URL inside the container environment
    const candidates = [
      `xdg-open '${url}'`,
      `sensible-browser '${url}'`,
      `google-chrome --no-sandbox '${url}'`,
      `chromium --no-sandbox '${url}'`,
      `chromium-browser --no-sandbox '${url}'`,
      `firefox '${url}'`
    ];
    let lastError: any = null;
    for (const cmd of candidates) {
      try {
        await this.dockerExec(`DISPLAY=${this.display} ${cmd}`);
        return;
      } catch (err) {
        lastError = err;
      }
    }
    throw new Error(`Failed to open URL in Docker environment: ${lastError instanceof Error ? lastError.message : String(lastError)}`);
  }

  async executeAction(action: ComputerAction): Promise<void> {
    if (!this.container) {
      throw new Error('Docker environment not initialized');
    }

    try {
      switch (action.type) {
        case 'click':
          if (action.x !== undefined && action.y !== undefined) {
            const buttonMap = { left: 1, middle: 2, right: 3 };
            const button = buttonMap[action.button || 'left'] || 1;
            await this.dockerExec(`DISPLAY=${this.display} xdotool mousemove ${action.x} ${action.y} click ${button}`);
          }
          break;

        case 'scroll':
          if (action.x !== undefined && action.y !== undefined && action.scrollY !== undefined) {
            await this.dockerExec(`DISPLAY=${this.display} xdotool mousemove ${action.x} ${action.y}`);
            
            if (action.scrollY !== 0) {
              const button = action.scrollY < 0 ? 4 : 5; // 4 = scroll up, 5 = scroll down
              const clicks = Math.abs(action.scrollY);
              for (let i = 0; i < clicks; i++) {
                await this.dockerExec(`DISPLAY=${this.display} xdotool click ${button}`);
              }
            }
          }
          break;

        case 'keypress':
          if (action.keys) {
            for (const key of action.keys) {
              if (key.toLowerCase().includes('enter')) {
                await this.dockerExec(`DISPLAY=${this.display} xdotool key 'Return'`);
              } else if (key.toLowerCase().includes('space')) {
                await this.dockerExec(`DISPLAY=${this.display} xdotool key 'space'`);
              } else {
                await this.dockerExec(`DISPLAY=${this.display} xdotool key '${key}'`);
              }
            }
          }
          break;

        case 'type':
          if (action.text) {
            const escapedText = action.text.replace(/'/g, "\\'");
            await this.dockerExec(`DISPLAY=${this.display} xdotool type '${escapedText}'`);
          }
          break;

        case 'wait':
          await new Promise(resolve => setTimeout(resolve, 2000));
          break;

        case 'screenshot':
          // Screenshot is handled separately in getScreenshot()
          break;

        default:
          console.warn(`Unrecognized action type: ${action.type}`);
      }
    } catch (error) {
      console.error(`Error executing action ${action.type}:`, error);
      throw error;
    }
  }

  async getScreenshot(): Promise<Buffer> {
    if (!this.container) {
      throw new Error('Docker environment not initialized');
    }

    try {
      const cmd = `export DISPLAY=${this.display} && import -window root png:-`;
      const result = await this.dockerExec(cmd, false);
      return Buffer.from(result, 'binary');
    } catch (error) {
      throw new Error(`Failed to capture screenshot: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async dockerExec(command: string, decode: boolean = true): Promise<string> {
    if (!this.container) {
      throw new Error('Docker environment not initialized');
    }

    const exec = await this.container.exec({
      Cmd: ['sh', '-c', command],
      AttachStdout: true,
      AttachStderr: true
    });

    const stream = await exec.start({ hijack: true, stdin: false });
    
    return new Promise((resolve, reject) => {
      let output = '';
      let errorOutput = '';

      stream.on('data', (chunk: Buffer) => {
        if (decode) {
          output += chunk.toString('utf-8');
        } else {
          output += chunk.toString('binary');
        }
      });

      stream.on('error', (error) => {
        reject(error);
      });

      stream.on('end', () => {
        if (errorOutput) {
          reject(new Error(errorOutput));
        } else {
          resolve(output);
        }
      });
    });
  }

  async cleanup(): Promise<void> {
    try {
      if (this.container) {
        await this.container.stop();
        await this.container.remove();
      }
    } catch (error) {
      console.error('Error during Docker cleanup:', error);
    } finally {
      this.container = null;
    }
  }
}

export class EnvironmentManager {
  private environments: Map<string, DockerEnvironment> = new Map();

  async createEnvironment(id: string, config: EnvironmentConfig): Promise<DockerEnvironment> {
    // Always create Docker environment to avoid Node Playwright
    const environment = new DockerEnvironment(config, `dante-computer-use-${id}`);
    await environment.initialize();
    this.environments.set(id, environment);
    return environment;
  }

  getEnvironment(id: string): DockerEnvironment | undefined {
    return this.environments.get(id);
  }

  async cleanupEnvironment(id: string): Promise<void> {
    const environment = this.environments.get(id);
    if (environment) {
      await environment.cleanup();
      this.environments.delete(id);
    }
  }

  async cleanupAll(): Promise<void> {
    const cleanupPromises = Array.from(this.environments.keys()).map(id => 
      this.cleanupEnvironment(id)
    );
    await Promise.all(cleanupPromises);
  }
}

export const environmentManager = new EnvironmentManager();
