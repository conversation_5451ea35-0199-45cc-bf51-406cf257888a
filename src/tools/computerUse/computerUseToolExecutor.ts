/**
 * Computer Use Tool Executor - Vercel AI SDK Implementation
 *
 * Handles the execution of computer use actions through various environments
 * with proper session management, safety controls, and Docker integration.
 */

import { generateText, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { z } from 'zod';
import { computerUseManager, runComputerUseTool } from './computerUseTool';
import { EnvironmentManager } from './environmentManager';
import { ComputerUseSafety, defaultSafetyConfig } from './safetyFramework';

export interface ComputerUseExecutionContext {
  sessionId?: string;
  taskId?: string;
  userId?: string;
  requestId?: string;
}

export interface ComputerUseExecutionOptions {
  stream?: boolean;
  useGemini?: boolean;
  maxSteps?: number;
  temperature?: number;
  timeout?: number;
  safetyLevel?: 'low' | 'medium' | 'high';
}

export interface ComputerUseExecutionResult {
  success: boolean;
  sessionId?: string;
  taskId?: string;
  result?: any;
  error?: string;
  screenshots?: string[];
  actions?: any[];
  safetyChecks?: any[];
  metadata?: any;
}

/**
 * Main executor class for computer use operations
 */
export class ComputerUseToolExecutor {
  private environmentManager: EnvironmentManager;
  private safetyManager: ComputerUseSafety;
  private activeSessions: Map<string, any> = new Map();

  constructor() {
    this.environmentManager = new EnvironmentManager();
    this.safetyManager = new ComputerUseSafety(defaultSafetyConfig);
  }

  /**
   * Execute a computer use task with full orchestration
   */
  async executeComputerUseTask(
    action: 'start_session' | 'execute_task' | 'acknowledge_safety' | 'continue_session' | 'close_session',
    parameters: any,
    context: ComputerUseExecutionContext = {},
    options: ComputerUseExecutionOptions = {}
  ): Promise<ComputerUseExecutionResult> {

    const startTime = Date.now();
    console.log(`🚀 Executing computer use task: ${action}`, {
      sessionId: context.sessionId,
      taskId: context.taskId,
      hasParameters: !!parameters
    });

    try {
      // Prepare the computer use tool parameters
      const toolParams = {
        action,
        ...parameters,
        sessionId: context.sessionId
      };

      // Execute the computer use tool
      const result = await runComputerUseTool(toolParams);
      const parsedResult = JSON.parse(result as string);

      // Track active sessions
      if (action === 'start_session' && parsedResult.success) {
        this.activeSessions.set(parsedResult.sessionId, {
          startedAt: new Date(),
          context,
          lastActivity: new Date()
        });
      } else if (action === 'close_session' && context.sessionId) {
        this.activeSessions.delete(context.sessionId);
      }

      const executionTime = Date.now() - startTime;
      console.log(`✅ Computer use task completed in ${executionTime}ms:`, {
        action,
        success: parsedResult.success,
        status: parsedResult.status,
        sessionId: parsedResult.sessionId || context.sessionId
      });

      return {
        success: parsedResult.success !== false,
        sessionId: parsedResult.sessionId || context.sessionId,
        taskId: context.taskId,
        result: parsedResult,
        screenshots: parsedResult.screenshot ? [parsedResult.screenshot] : undefined,
        actions: parsedResult.actions,
        safetyChecks: parsedResult.safetyChecks,
        metadata: {
          executionTime,
          action,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ Computer use task failed after ${executionTime}ms:`, error);

      return {
        success: false,
        sessionId: context.sessionId,
        taskId: context.taskId,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          executionTime,
          action,
          timestamp: new Date().toISOString(),
          failed: true
        }
      };
    }
  }

  /**
   * Execute computer use with AI-powered analysis and decision making
   */
  async executeWithAIGuidance(
    userPrompt: string,
    context: ComputerUseExecutionContext = {},
    options: ComputerUseExecutionOptions = {}
  ): Promise<ComputerUseExecutionResult> {

    console.log('🤖 Executing computer use with AI guidance:', {
      promptLength: userPrompt.length,
      sessionId: context.sessionId,
      useGemini: options.useGemini
    });

    try {
      // Choose model based on options
      const model = options.useGemini ? google('gemini-2.5-flash') : openai('gpt-5');

      // Create AI-powered execution plan
      const planningPrompt = `You are Dante's Computer Use Specialist. Analyze this user request and create an execution plan:

User Request: "${userPrompt}"

Current Context:
- Session ID: ${context.sessionId || 'new session'}
- Available environments: browser, docker
- Safety level: ${options.safetyLevel || 'medium'}

Please analyze the request and provide:
1. Environment type needed (browser/docker)
2. Initial URL if browser (if applicable)
3. Step-by-step execution plan
4. Safety considerations
5. Expected outcomes

Respond in JSON format with fields: environmentType, initialUrl, executionPlan, safetyNotes, expectedOutcome`;

      const planningResponse = await generateText({
        model,
        prompt: planningPrompt,
          providerOptions: {
          google: {
            thinkingConfig: { includeThoughts: true }
          },
          openai: {
            reasoningSummary: 'auto',
            reasoningEffort: 'high',
          }
    },
      });

      let executionPlan;
      try {
        executionPlan = JSON.parse(planningResponse.text);
      } catch {
        // Fallback if JSON parsing fails
        executionPlan = {
          environmentType: 'browser',
          executionPlan: ['Execute the user request'],
          safetyNotes: 'Standard safety checks apply'
        };
      }

      console.log('📋 AI execution plan:', executionPlan);

      // Start session if needed
      let sessionId = context.sessionId;
      if (!sessionId) {
        const sessionResult = await this.executeComputerUseTask(
          'start_session',
          {
            environmentType: executionPlan.environmentType || 'browser',
            initialUrl: executionPlan.initialUrl,
            userPrompt: userPrompt,
            displayWidth: 1024,
            displayHeight: 768
          },
          context,
          options
        );

        if (!sessionResult.success) {
          return sessionResult;
        }

        sessionId = sessionResult.result.sessionId;
        context.sessionId = sessionId;
      }

      // Execute the main task
      const taskResult = await this.executeComputerUseTask(
        'execute_task',
        {
          userPrompt: userPrompt,
          environmentType: executionPlan.environmentType || 'browser',
          initialUrl: executionPlan.initialUrl
        },
        context,
        options
      );

      return {
        ...taskResult,
        metadata: {
          ...taskResult.metadata,
          aiGuidance: true,
          executionPlan: executionPlan.executionPlan,
          safetyNotes: executionPlan.safetyNotes,
          expectedOutcome: executionPlan.expectedOutcome
        }
      };

    } catch (error) {
      console.error('❌ AI-guided computer use execution failed:', error);

      return {
        success: false,
        sessionId: context.sessionId,
        taskId: context.taskId,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          aiGuidance: true,
          failed: true,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Execute computer use with streaming updates
   */
  async *executeWithStreaming(
    userPrompt: string,
    context: ComputerUseExecutionContext = {},
    options: ComputerUseExecutionOptions = {}
  ): AsyncGenerator<any> {

    console.log('🌊 Starting streaming computer use execution');

    try {
      yield {
        type: 'start',
        message: 'Starting computer use execution...',
        timestamp: new Date().toISOString()
      };

      // Start session
      yield {
        type: 'status',
        message: 'Initializing session...',
        step: 1,
        totalSteps: 4
      };

      const sessionResult = await this.executeComputerUseTask(
        'start_session',
        {
          environmentType: 'browser',
          userPrompt: userPrompt
        },
        context,
        options
      );

      if (!sessionResult.success) {
        yield {
          type: 'error',
          error: sessionResult.error,
          timestamp: new Date().toISOString()
        };
        return;
      }

      const sessionId = sessionResult.result.sessionId;
      context.sessionId = sessionId;

      yield {
        type: 'session_started',
        sessionId: sessionId,
        step: 2,
        totalSteps: 4
      };

      // Execute task
      yield {
        type: 'status',
        message: 'Executing computer use task...',
        step: 3,
        totalSteps: 4
      };

      const taskResult = await this.executeComputerUseTask(
        'execute_task',
        { userPrompt: userPrompt },
        context,
        options
      );

      yield {
        type: 'task_result',
        result: taskResult.result,
        success: taskResult.success,
        step: 4,
        totalSteps: 4
      };

      // Send screenshot if available
      if (taskResult.result?.screenshot) {
        yield {
          type: 'screenshot',
          screenshot: taskResult.result.screenshot,
          currentUrl: taskResult.result.currentUrl
        };
      }

      yield {
        type: 'complete',
        finalResult: taskResult,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      yield {
        type: 'error',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Handle safety check acknowledgment and continuation
   */
  async acknowledgeSafetyAndContinue(
    sessionId: string,
    safetyChecks: any[],
    context: ComputerUseExecutionContext = {},
    options: ComputerUseExecutionOptions = {}
  ): Promise<ComputerUseExecutionResult> {

    console.log(`🛡️ Acknowledging safety checks for session ${sessionId}:`, safetyChecks);

    return await this.executeComputerUseTask(
      'acknowledge_safety',
      {
        acknowledgedSafetyChecks: safetyChecks
      },
      { ...context, sessionId },
      options
    );
  }

  /**
   * Continue a computer use session with new screenshot
   */
  async continueWithScreenshot(
    sessionId: string,
    callId: string,
    context: ComputerUseExecutionContext = {},
    options: ComputerUseExecutionOptions = {}
  ): Promise<ComputerUseExecutionResult> {

    console.log(`📸 Continuing session ${sessionId} with screenshot, callId: ${callId}`);

    return await this.executeComputerUseTask(
      'continue_session',
      { callId },
      { ...context, sessionId },
      options
    );
  }

  /**
   * Close a computer use session and cleanup
   */
  async closeSession(
    sessionId: string,
    context: ComputerUseExecutionContext = {}
  ): Promise<ComputerUseExecutionResult> {

    console.log(`🚪 Closing computer use session: ${sessionId}`);

    const result = await this.executeComputerUseTask(
      'close_session',
      {},
      { ...context, sessionId }
    );

    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    return result;
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): Array<{ sessionId: string; startedAt: Date; lastActivity: Date; context: any }> {
    return Array.from(this.activeSessions.entries()).map(([sessionId, session]) => ({
      sessionId,
      startedAt: session.startedAt,
      lastActivity: session.lastActivity,
      context: session.context
    }));
  }

  /**
   * Cleanup inactive sessions
   */
  async cleanupInactiveSessions(): Promise<number> {
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
    let cleaned = 0;

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.lastActivity.getTime() < thirtyMinutesAgo) {
        try {
          await this.closeSession(sessionId);
          cleaned++;
          console.log(`🧹 Cleaned up inactive session: ${sessionId}`);
        } catch (error) {
          console.error(`❌ Failed to cleanup session ${sessionId}:`, error);
        }
      }
    }

    return cleaned;
  }

  /**
   * Health check for the executor
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    activeSessions: number;
    environmentManagerStatus: string;
    safetyManagerStatus: string;
    errors?: string[];
  }> {
    const errors: string[] = [];
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    try {
      // Check active sessions count
      const activeSessionCount = this.activeSessions.size;

      // Check environment manager
      let envStatus = 'healthy';
      try {
        // Test environment manager functionality if needed
        envStatus = 'healthy';
      } catch (error) {
        envStatus = 'error';
        errors.push(`Environment manager error: ${error}`);
        status = 'degraded';
      }

      // Check safety manager
      let safetyStatus = 'healthy';
      try {
        // Basic safety manager check
        safetyStatus = 'healthy';
      } catch (error) {
        safetyStatus = 'error';
        errors.push(`Safety manager error: ${error}`);
        status = 'degraded';
      }

      // If too many active sessions, mark as degraded
      if (activeSessionCount > 10) {
        status = 'degraded';
        errors.push(`High number of active sessions: ${activeSessionCount}`);
      }

      return {
        status: errors.length > 2 ? 'unhealthy' : status,
        activeSessions: activeSessionCount,
        environmentManagerStatus: envStatus,
        safetyManagerStatus: safetyStatus,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        activeSessions: 0,
        environmentManagerStatus: 'error',
        safetyManagerStatus: 'error',
        errors: [`Health check failed: ${error}`]
      };
    }
  }
}

// Global executor instance
export const computerUseExecutor = new ComputerUseToolExecutor();

// Periodic cleanup of inactive sessions
setInterval(async () => {
  try {
    const cleaned = await computerUseExecutor.cleanupInactiveSessions();
    if (cleaned > 0) {
      console.log(`🧹 Computer use executor cleaned up ${cleaned} inactive sessions`);
    }
  } catch (error) {
    console.error('❌ Error during periodic session cleanup:', error);
  }
}, 15 * 60 * 1000); // Every 15 minutes

// Thin wrapper to expose standard executor API that returns parsed JSON (matches tests' expectations)
export async function executeComputerUseTool(params: any): Promise<any> {
  const result = await runComputerUseTool(params);
  try {
    return JSON.parse(result as string);
  } catch {
    return result as any;
  }
}
