import { ComputerAction } from './environmentManager';

export interface SafetyCheck {
  id: string;
  code: 'malicious_instructions' | 'irrelevant_domain' | 'sensitive_domain' | 'custom';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SafetyConfig {
  allowedDomains: string[];
  blockedDomains: string[];
  sensitiveKeywords: string[];
  requireConfirmationForActions: boolean;
  enableDomainChecking: boolean;
  enableContentAnalysis: boolean;
  maxActionsPerSession: number;
}

export interface ActionLog {
  timestamp: Date;
  action: ComputerAction;
  url?: string;
  screenshot?: string; // base64
  success: boolean;
  error?: string;
  safetyChecks?: SafetyCheck[];
}

export class ComputerUseSafety {
  private config: SafetyConfig;
  private actionLog: ActionLog[] = [];
  private sessionActionCount: number = 0;

  constructor(config: SafetyConfig) {
    this.config = config;
  }

  async validateUrl(url: string): Promise<SafetyCheck[]> {
    const checks: SafetyCheck[] = [];

    if (!this.config.enableDomainChecking) {
      return checks;
    }

    try {
      const domain = new URL(url).hostname.toLowerCase();

      // Check blocked domains
      if (this.config.blockedDomains.some(blocked => 
          domain.includes(blocked.toLowerCase()) || blocked.includes(domain)
      )) {
        checks.push({
          id: `blocked_${Date.now()}`,
          code: 'irrelevant_domain',
          message: `Domain ${domain} is in the blocked list and access is not allowed.`,
          severity: 'critical'
        });
      }

      // Check if domain is in allowed list (if allowlist is not empty)
      if (this.config.allowedDomains.length > 0) {
        const isAllowed = this.config.allowedDomains.some(allowed => 
          domain.includes(allowed.toLowerCase()) || allowed.includes(domain)
        );
        
        if (!isAllowed) {
          checks.push({
            id: `unallowed_${Date.now()}`,
            code: 'irrelevant_domain',
            message: `Domain ${domain} is not in the allowed domains list.`,
            severity: 'high'
          });
        }
      }

      // Check for sensitive domains (banking, government, etc.)
      const sensitiveDomains = [
        'bank', 'banking', 'paypal', 'stripe', 'gov', 'admin', 
        'login', 'auth', 'password', 'secure', 'private'
      ];

      if (sensitiveDomains.some(sensitive => domain.includes(sensitive))) {
        checks.push({
          id: `sensitive_${Date.now()}`,
          code: 'sensitive_domain',
          message: `Domain ${domain} appears to be sensitive. Extra caution is recommended.`,
          severity: 'medium'
        });
      }

    } catch (error) {
      checks.push({
        id: `invalid_url_${Date.now()}`,
        code: 'custom',
        message: `Invalid URL format: ${url}`,
        severity: 'medium'
      });
    }

    return checks;
  }

  async validateAction(action: ComputerAction, context?: { url?: string, screenshot?: string }): Promise<SafetyCheck[]> {
    const checks: SafetyCheck[] = [];

    // Include URL-based checks if URL context is provided
    if (context?.url) {
      try {
        const urlChecks = await this.validateUrl(context.url);
        checks.push(...urlChecks);
      } catch {
        // Ignore URL validation errors here; validateUrl already reports invalid URL
      }
    }

    // Check session limits
    if (this.sessionActionCount >= this.config.maxActionsPerSession) {
      checks.push({
        id: `session_limit_${Date.now()}`,
        code: 'custom',
        message: `Maximum actions per session (${this.config.maxActionsPerSession}) exceeded.`,
        severity: 'high'
      });
    }

    // Analyze text input for sensitive content
    if (action.type === 'type' && action.text && this.config.enableContentAnalysis) {
      const textChecks = this.analyzeTextContent(action.text);
      checks.push(...textChecks);
    }

    // Check for potentially dangerous key combinations
    if (action.type === 'keypress' && action.keys) {
      const dangerousKeyCombos = [
        ['ctrl', 'alt', 'del'],
        ['cmd', 'alt', 'esc'],
        ['alt', 'f4']
      ];

      const keysLower = action.keys.map(k => k.toLowerCase());
      for (const combo of dangerousKeyCombos) {
        if (combo.every(key => keysLower.includes(key))) {
          checks.push({
            id: `dangerous_keys_${Date.now()}`,
            code: 'custom',
            message: `Potentially dangerous key combination detected: ${action.keys.join('+')}`,
            severity: 'high'
          });
        }
      }
    }

    return checks;
  }

  // Determine if an action should require user confirmation before execution
  async shouldRequireConfirmation(action: ComputerAction, context?: { url?: string, screenshot?: string }): Promise<{ require: boolean; reason?: string; checks?: SafetyCheck[] }> {
    // Always require if config mandates confirmation for actions
    if (this.config.requireConfirmationForActions) {
      return { require: true, reason: 'Configuration requires confirmation for actions' };
    }

    const checks = await this.validateAction(action, context);
    const hasHighRisk = checks.some(c => c.severity === 'high' || c.severity === 'critical');

    if (hasHighRisk) {
      return { require: true, reason: 'High-risk safety checks present', checks };
    }

    return { require: false, checks };
  }

  private analyzeTextContent(text: string): SafetyCheck[] {
    const checks: SafetyCheck[] = [];
    const textLower = text.toLowerCase();

    // Check for sensitive information patterns
    const patterns = [
      { pattern: /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/, message: 'Credit card number detected' },
      { pattern: /\b\d{3}-\d{2}-\d{4}\b/, message: 'SSN pattern detected' },
      { pattern: /password\s*[:=]\s*\S+/i, message: 'Password being entered' },
      { pattern: /api[_-]?key\s*[:=]\s*\S+/i, message: 'API key detected' },
      { pattern: /token\s*[:=]\s*\S+/i, message: 'Token detected' },
    ];

    for (const { pattern, message } of patterns) {
      if (pattern.test(text)) {
        checks.push({
          id: `sensitive_text_${Date.now()}`,
          code: 'custom',
          message: `Sensitive information detected: ${message}`,
          severity: 'high'
        });
      }
    }

    // Check for sensitive keywords
    for (const keyword of this.config.sensitiveKeywords) {
      if (textLower.includes(keyword.toLowerCase())) {
        checks.push({
          id: `keyword_${Date.now()}`,
          code: 'custom',
          message: `Sensitive keyword detected: ${keyword}`,
          severity: 'medium'
        });
      }
    }

    return checks;
  }

  logAction(action: ComputerAction, context?: { url?: string, screenshot?: string, success?: boolean, error?: string, safetyChecks?: SafetyCheck[] }): void {
    const logEntry: ActionLog = {
      timestamp: new Date(),
      action,
      url: context?.url,
      screenshot: context?.screenshot,
      success: context?.success ?? true,
      error: context?.error,
      safetyChecks: context?.safetyChecks || []
    };

    this.actionLog.push(logEntry);
    this.sessionActionCount++;

    // Keep log size manageable
    if (this.actionLog.length > 1000) {
      this.actionLog.splice(0, 100); // Remove oldest 100 entries
    }
  }

  getActionLog(): ActionLog[] {
    return [...this.actionLog];
  }

  getSessionStats(): { actionCount: number, safetyViolations: number, domains: string[] } {
    const domains = new Set<string>();
    let safetyViolations = 0;

    for (const log of this.actionLog) {
      if (log.url) {
        try {
          domains.add(new URL(log.url).hostname);
        } catch {
          // Invalid URL, skip
        }
      }
      if (log.safetyChecks && log.safetyChecks.length > 0) {
        safetyViolations += log.safetyChecks.length;
      }
    }

    return {
      actionCount: this.sessionActionCount,
      safetyViolations,
      domains: Array.from(domains)
    };
  }

  clearSession(): void {
    this.actionLog = [];
    this.sessionActionCount = 0;
  }

  updateConfig(newConfig: Partial<SafetyConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

export const defaultSafetyConfig: SafetyConfig = {
  allowedDomains: [], // Empty means all domains allowed (unless blocked)
  blockedDomains: [
    'localhost:22', // SSH
    '127.0.0.1:22',
    'admin.local',
    'phishing-site.com'
  ],
  sensitiveKeywords: [
    'password', 'secret', 'private_key', 'api_key', 'token',
    'ssn', 'social_security', 'credit_card', 'banking'
  ],
  requireConfirmationForActions: true,
  enableDomainChecking: true,
  enableContentAnalysis: true,
  maxActionsPerSession: 100
};