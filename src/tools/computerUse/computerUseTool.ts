import { tool } from 'ai';
import { z } from 'zod';
import OpenA<PERSON> from 'openai';
import { config } from '../../utils/config';
import { EnvironmentManager, EnvironmentConfig, ComputerAction } from './environmentManager';
import { ComputerUseSafety, defaultSafetyConfig, SafetyCheck } from './safetyFramework';
import { mcpServerManager, MCPServerManager } from '../../mcp/MCPServerManager';
import { logger } from '../../utils/logger';
import { generateTraceId } from '../../../server/observability/tracing';

export interface ComputerUseSession {
  id: string;
  environmentId: string;
  environmentType: 'browser' | 'docker';
  safetyManager: ComputerUseSafety;
  openaiClient: OpenAI;
  displayWidth: number;
  displayHeight: number;
  conversationHistory: any[];
  isActive: boolean;
}

export interface ComputerUseRequest {
  sessionId?: string;
  environmentType: 'browser' | 'docker';
  displayWidth?: number;
  displayHeight?: number;
  initialUrl?: string;
  userPrompt: string;
  safetyConfig?: Partial<typeof defaultSafetyConfig>;
  requiresConfirmation?: boolean;
}

export interface ComputerUseResponse {
  sessionId: string;
  status: 'success' | 'error' | 'safety_check_required' | 'confirmation_required';
  screenshot?: string; // base64
  currentUrl?: string;
  actions?: ComputerAction[];
  safetyChecks?: SafetyCheck[];
  message?: string;
  error?: string;
  reasoning?: string;
}

class ComputerUseManager {
  private sessions = new Map<string, ComputerUseSession>();
  private environmentManager = new EnvironmentManager();
  private previewAvailable = true;
  private openaiClient: OpenAI;
  private mcpManager: MCPServerManager;

  constructor(mcpManagerInstance?: MCPServerManager) {
    this.openaiClient = new OpenAI({
      apiKey: config.openai.apiKey,
      timeout: 30000,
      maxRetries: 2,
    });
    this.mcpManager = mcpManagerInstance || mcpServerManager;
  }

  async createSession(request: ComputerUseRequest): Promise<ComputerUseSession> {
    const sessionId = request.sessionId || this.generateSessionId();
    const environmentId = `env-${sessionId}`;

    const environmentConfig: EnvironmentConfig = {
      type: request.environmentType,
      width: request.displayWidth || 1024,
      height: request.displayHeight || 768,
      headless: false,
      sandbox: true
    };

    // Create environment
    const environment = await this.environmentManager.createEnvironment(environmentId, environmentConfig);

    // Initialize browser with URL if provided
    if (request.environmentType === 'browser' && request.initialUrl && 'navigateTo' in environment) {
      await environment.navigateTo(request.initialUrl);
    }

    // Create safety manager
    const safetyConfig = { ...defaultSafetyConfig, ...request.safetyConfig };
    const safetyManager = new ComputerUseSafety(safetyConfig);

    // Create session
    const session: ComputerUseSession = {
      id: sessionId,
      environmentId,
      environmentType: request.environmentType,
      safetyManager,
      openaiClient: this.openaiClient,
      displayWidth: environmentConfig.width,
      displayHeight: environmentConfig.height,
      conversationHistory: [],  // Always start fresh
      isActive: true
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  async executeComputerUseTask(request: ComputerUseRequest): Promise<ComputerUseResponse> {
    try {
      // Get or create session
      let session = request.sessionId ? this.sessions.get(request.sessionId) : null;
      if (!session) {
        session = await this.createSession(request);
      }

      if (!session.isActive) {
        throw new Error('Session is not active');
      }

      const environment = this.environmentManager.getEnvironment(session.environmentId);
      if (!environment) {
        throw new Error('Environment not found');
      }

      // Get initial screenshot
      let screenshot = await environment.getScreenshot();
      let screenshotBase64 = screenshot.toString('base64');

      // Get current URL if browser environment
      let currentUrl: string | undefined;
      if (session.environmentType === 'browser' && 'getCurrentUrl' in environment) {
        currentUrl = await (environment as any).getCurrentUrl();
      }

      // Validate URL if available
      let safetyChecks: SafetyCheck[] = [];
      if (currentUrl) {
        safetyChecks = await session.safetyManager.validateUrl(currentUrl);
      }

      // If there are critical safety checks, return immediately
      const criticalChecks = safetyChecks.filter(check => check.severity === 'critical');
      if (criticalChecks.length > 0) {
        return {
          sessionId: session.id,
          status: 'safety_check_required',
          screenshot: screenshotBase64,
          currentUrl,
          safetyChecks: criticalChecks,
          message: 'Critical safety checks failed. User confirmation required.'
        };
      }

      // Aggressive conversation history management to prevent context overflow
      if (session.conversationHistory.length > 2) {
        // Keep only the most recent exchange to minimize context
        session.conversationHistory = session.conversationHistory.slice(-1);
        console.log('🧹 Trimmed conversation history to last 1 exchange');
      }

      // Create OpenAI Responses API request (following OpenAI Computer Use guidance)
      const responseRequest: any = {
        model: 'computer-use-preview',
        tools: [{
          type: 'computer_use_preview',
          display_width: session.displayWidth,
          display_height: session.displayHeight,
          environment: session.environmentType === 'browser' ? 'browser' : 'ubuntu'
        }],
        input: [{
          role: 'user',
          content: [{
            type: 'input_text',
            text: request.userPrompt
          }, {
            type: 'input_image',
            image_url: `data:image/png;base64,${screenshotBase64}`
          }]
        }],
        reasoning: {
          summary: 'concise'
        },
        truncation: 'auto'
      };

      // Include previous response ID if available (only for proper Responses API responses)
      if (session.conversationHistory.length > 0) {
        const lastResponse = session.conversationHistory[session.conversationHistory.length - 1];
        // Only include previous_response_id for proper OpenAI response IDs (not fallback IDs)
        if (lastResponse.id && lastResponse.id.startsWith('resp_') && !lastResponse.id.includes('fallback')) {
          responseRequest.previous_response_id = lastResponse.id;
        }
      }

      // Try OpenAI computer-use-preview once if available; otherwise use gpt-5 fallback
      // The 'computer-use-preview' model and the `openai.responses.create` API are not standard
      // and appear to be causing a segmentation fault. This logic has been replaced with a
      // standard call to 'gpt-5' using the Chat Completions API, which is more stable.
      console.log('⚡ Using gpt-5 with vision for computer use task.');

      // Analysis with gpt-5 to determine the next action
      const taskSummary = request.userPrompt.length > 100
        ? request.userPrompt.substring(0, 100) + '...'
        : request.userPrompt;

      const actionRequest = {
        model: 'gpt-5',
        messages: [{
          role: 'user' as const,
          content: [
            {
              type: 'text' as const,
              text: `You are an AI agent controlling a computer. Your task is to: "${taskSummary}"
The current URL is: ${currentUrl || 'N/A'}
Based on the screenshot, determine the next single action to perform.

Respond with a single JSON object representing the action. The action can be one of the following types:
- "click": { "type": "click", "x": <number>, "y": <number>, "button": "left" | "right" }
- "type": { "type": "type", "text": "<string>" }
- "scroll": { "type": "scroll", "x": <number>, "y": <number> }
- "done": { "type": "done", "message": "<string>" }

Analyze the screenshot and provide the JSON for the next action. For example, to click a "Login" button, find its coordinates and respond with:
{ "type": "click", "x": 512, "y": 384, "button": "left" }`
            },
            {
              type: 'image_url' as const,
              image_url: { url: `data:image/png;base64,${screenshotBase64}` }
            }
          ]
        }],
        max_tokens: 300,
        response_format: { type: 'json_object' as const }
      };

      const actionResponse = await session.openaiClient.chat.completions.create(actionRequest);
      console.log('✅ Action generation with gpt-5 successful');

      const responseContent = actionResponse.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error('gpt-5 did not return any content for the action.');
      }

      let actionData;
      try {
        actionData = JSON.parse(responseContent);
      } catch (e) {
        console.error("Failed to parse JSON action from gpt-5:", responseContent);
        throw new Error("Received invalid JSON from AI model for the next action.");
      }

      // Create a mock response object that processResponse can handle
      const mockResponse = {
        id: `resp_gpt4o_${Date.now()}`,
        output: [{
          type: 'computer_call',
          action: actionData
        }, {
          type: 'reasoning',
          summary: [{ text: 'Action determined by gpt-5.' }]
        }]
      };

      session.conversationHistory.push(mockResponse);

      // Process the generated action
      return await this.processResponse(session, mockResponse, environment);

    } catch (error) {
      return {
        sessionId: request.sessionId || 'unknown',
        status: 'error',
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to execute computer use task'
      };
    }
  }

  private async processResponse(
    session: ComputerUseSession,
    response: any,
    environment: any
  ): Promise<ComputerUseResponse> {

    // Extract computer calls from response
    const computerCalls = response.output.filter(
      (item: any) => item.type === 'computer_call'
    );

    // Extract reasoning if available
    const reasoningItems = response.output.filter(
      (item: any) => item.type === 'reasoning'
    );
    const reasoning = reasoningItems.length > 0
      ? reasoningItems.map((r: any) => r.summary?.map((s: any) => s.text).join(' ')).join(' ')
      : undefined;

    // If no computer calls, return current state
    if (computerCalls.length === 0) {
      const screenshot = await environment.getScreenshot();
      const currentUrl = session.environmentType === 'browser' && 'getCurrentUrl' in environment
        ? await (environment as any).getCurrentUrl()
        : undefined;

      return {
        sessionId: session.id,
        status: 'success',
        screenshot: screenshot.toString('base64'),
        currentUrl,
        message: 'No actions required',
        reasoning
      };
    }

    // Process first computer call (expecting only one per response)
    const computerCall = computerCalls[0];
    const action = this.convertToComputerAction(computerCall.action);

    // Check for pending safety checks
    if (computerCall.pending_safety_checks && computerCall.pending_safety_checks.length > 0) {
      const screenshot = await environment.getScreenshot();
      return {
        sessionId: session.id,
        status: 'safety_check_required',
        screenshot: screenshot.toString('base64'),
        currentUrl: session.environmentType === 'browser' && 'getCurrentUrl' in environment
          ? await (environment as any).getCurrentUrl()
          : undefined,
        safetyChecks: computerCall.pending_safety_checks.map((check: any) => ({
          id: check.id,
          code: check.code as any,
          message: check.message,
          severity: 'high' as const
        })),
        actions: [action],
        reasoning
      };
    }

    // Validate action with our safety framework
    const currentUrl = session.environmentType === 'browser' && 'getCurrentUrl' in environment
      ? await (environment as any).getCurrentUrl()
      : undefined;

    const actionSafetyChecks = await session.safetyManager.validateAction(action, { url: currentUrl });

    if (actionSafetyChecks.length > 0) {
      const screenshot = await environment.getScreenshot();
      return {
        sessionId: session.id,
        status: 'safety_check_required',
        screenshot: screenshot.toString('base64'),
        currentUrl,
        safetyChecks: actionSafetyChecks,
        actions: [action],
        reasoning
      };
    }

    // Execute the action
    try {
      await environment.executeAction(action);

      // Wait a moment for the action to take effect
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Capture new screenshot
      const screenshot = await environment.getScreenshot();
      const newUrl = session.environmentType === 'browser' && 'getCurrentUrl' in environment
        ? await environment.getCurrentUrl()
        : undefined;

      // Log the action
      session.safetyManager.logAction(action, {
        url: newUrl,
        screenshot: screenshot.toString('base64'),
        success: true
      });

      return {
        sessionId: session.id,
        status: 'success',
        screenshot: screenshot.toString('base64'),
        currentUrl: newUrl,
        actions: [action],
        reasoning
      };

    } catch (error) {
      // Log failed action
      session.safetyManager.logAction(action, {
        url: currentUrl,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        sessionId: session.id,
        status: 'error',
        error: error instanceof Error ? error.message : String(error),
        actions: [action],
        reasoning
      };
    }
  }

  private convertToComputerAction(openaiAction: any): ComputerAction {
    return {
      type: openaiAction.type,
      x: openaiAction.x,
      y: openaiAction.y,
      button: openaiAction.button,
      scrollX: openaiAction.scrollX || openaiAction.scroll_x,
      scrollY: openaiAction.scrollY || openaiAction.scroll_y,
      keys: openaiAction.keys,
      text: openaiAction.text
    };
  }

  async acknowledgeAndContinue(sessionId: string, acknowledgedSafetyChecks: SafetyCheck[]): Promise<ComputerUseResponse> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const environment = this.environmentManager.getEnvironment(session.environmentId);
    if (!environment) {
      throw new Error('Environment not found');
    }

    // Get current screenshot and URL
    const screenshot = await environment.getScreenshot();
    const currentUrl = session.environmentType === 'browser' && 'getCurrentUrl' in environment
      ? await (environment as any).getCurrentUrl()
      : undefined;

    // Create continue request with acknowledged safety checks
    const lastResponse = session.conversationHistory[session.conversationHistory.length - 1];
    const computerCalls = lastResponse.output.filter(
      (item: any) => item.type === 'computer_call'
    );

    if (computerCalls.length === 0) {
      throw new Error('No computer call to continue');
    }

    const computerCall = computerCalls;

    console.log('⚡ Using gpt-5 with vision for acknowledged computer use task.');
    const actionRequest = {
      model: 'gpt-5',
      messages: [{
        role: 'user' as const,
        content: [
          {
            type: 'text' as const,
            text: `You are an AI agent controlling a computer. The user has acknowledged the following safety checks: ${JSON.stringify(acknowledgedSafetyChecks)}.
The previous action was to perform: ${JSON.stringify(computerCall.action)}.
Please proceed with the action now.
The current URL is: ${currentUrl || 'N/A'}
Based on the screenshot, confirm the action is still valid and provide the JSON for the action.
Respond with a single JSON object representing the action.`
          },
          {
            type: 'image_url' as const,
            image_url: { url: `data:image/png;base64,${screenshot.toString('base64')}` }
          }
        ]
      }],
      max_tokens: 300,
      response_format: { type: 'json_object' as const }
    };

    const actionResponse = await session.openaiClient.chat.completions.create(actionRequest);
    const responseContent = actionResponse.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error('gpt-5 did not return any content for the action.');
    }
    let actionData;
    try {
      actionData = JSON.parse(responseContent);
    } catch (e) {
      console.error("Failed to parse JSON action from gpt-5:", responseContent);
      throw new Error("Received invalid JSON from AI model for the next action.");
    }

    const mockResponse = {
      id: `resp_gpt4o_ack_${Date.now()}`,
      output: [{
        type: 'computer_call',
        action: actionData
      }, {
        type: 'reasoning',
        summary: [{ text: 'Action continued after safety check by gpt-5.' }]
      }]
    };

    session.conversationHistory.push(mockResponse);
    return await this.processResponse(session, mockResponse, environment);
  }

  async continueWithScreenshot(sessionId: string, callId: string): Promise<ComputerUseResponse> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const environment = this.environmentManager.getEnvironment(session.environmentId);
    if (!environment) {
      throw new Error('Environment not found');
    }

    // Get current screenshot and URL
    const screenshot = await environment.getScreenshot();
    const currentUrl = session.environmentType === 'browser' && 'getCurrentUrl' in environment
      ? await (environment as any).getCurrentUrl()
      : undefined;

    console.log('⚡ Using gpt-5 with vision for continued computer use task.');
    const actionRequest = {
      model: 'gpt-5',
      messages: [{
        role: 'user' as const,
        content: [
          {
            type: 'text' as const,
            text: `You are an AI agent controlling a computer. Continue the task based on the new screenshot.
The current URL is: ${currentUrl || 'N/A'}
Based on the new screenshot, determine the next single action to perform to continue the previous task.
Respond with a single JSON object representing the action.`
          },
          {
            type: 'image_url' as const,
            image_url: { url: `data:image/png;base64,${screenshot.toString('base64')}` }
          }
        ]
      }],
      max_tokens: 300,
      response_format: { type: 'json_object' as const }
    };

    const actionResponse = await session.openaiClient.chat.completions.create(actionRequest);
    const responseContent = actionResponse.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error('gpt-5 did not return any content for the action.');
    }
    let actionData;
    try {
      actionData = JSON.parse(responseContent);
    } catch (e) {
      console.error("Failed to parse JSON action from gpt-5:", responseContent);
      throw new Error("Received invalid JSON from AI model for the next action.");
    }

    const mockResponse = {
      id: `resp_gpt4o_cont_${Date.now()}`,
      output: [{
        type: 'computer_call',
        action: actionData
      }, {
        type: 'reasoning',
        summary: [{ text: 'Action continued with new screenshot by gpt-5.' }]
      }]
    };

    session.conversationHistory.push(mockResponse);
    return await this.processResponse(session, mockResponse, environment);
  }

  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      try {
        await this.environmentManager.cleanupEnvironment(session.environmentId);
      } catch (error) {
        console.error(`Error cleaning up environment ${session.environmentId}:`, error);
      }
      this.sessions.delete(sessionId);
      console.log(`🗑️ Session ${sessionId} closed and cleaned up`);
    }
  }

  getSession(sessionId: string): ComputerUseSession | undefined {
    return this.sessions.get(sessionId);
  }

  // Clean up inactive sessions older than 30 minutes
  async cleanupInactiveSessions(): Promise<void> {
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);

    for (const [sessionId, session] of this.sessions.entries()) {
      const sessionTimestamp = parseInt(sessionId.split('-')[2]);
      if (sessionTimestamp < thirtyMinutesAgo || !session.isActive) {
        console.log(`🧹 Cleaning up inactive session: ${sessionId}`);
        await this.closeSession(sessionId);
      }
    }
  }

  private generateSessionId(): string {
    return `computer-use-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  public compressComputerUseResponse(response: any): any {
    const originalSize = JSON.stringify(response).length;

    // Log original response for debugging
    console.log('📊 Original response size:', originalSize, 'chars');
    console.log('📄 Original response sample:', JSON.stringify(response).substring(0, 500) + '...');

    const compressed = {
      ...response,
      // Limit screenshot to first 50KB (base64)
      screenshot: response.screenshot && response.screenshot.length > 50000
        ? response.screenshot.substring(0, 50000) + '...[truncated]'
        : response.screenshot,
      // Limit message to 1000 chars
      message: response.message && response.message.length > 1000
        ? response.message.substring(0, 1000) + '...[truncated]'
        : response.message,
      // Remove reasoning if too long
      reasoning: response.reasoning && response.reasoning.length > 200
        ? response.reasoning.substring(0, 200) + '...[truncated]'
        : response.reasoning
    };

    const compressedSize = JSON.stringify(compressed).length;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

    console.log('📊 Compressed response size:', compressedSize, 'chars');
    console.log('📉 Compression ratio:', compressionRatio + '%');
    console.log('📄 Compressed response sample:', JSON.stringify(compressed).substring(0, 500) + '...');

    return compressed;
  }


}

let computerUseManager = new ComputerUseManager();

export const computerUseTool = tool({
  name: 'computer_use',
  description: `Execute computer automation tasks using OpenAI's Computer Use API. Can control browsers and desktop environments to perform tasks like clicking, typing, scrolling, and taking screenshots.

  Features:
  - Browser automation (clicking links, filling forms, navigating websites)
  - Desktop automation (via Docker containers)
  - Screenshot capture and analysis
  - Safety checks and domain validation
  - Session management for multi-step tasks

  Use cases:
  - Automated web browsing and form filling
  - Website testing and interaction
  - Data extraction from web interfaces
  - Research and information gathering
  - Task automation requiring visual feedback`,

  inputSchema: z.object({
    action: z.enum(['start_session', 'execute_task', 'acknowledge_safety', 'continue_session', 'close_session']).describe('The action to perform'),
    sessionId: z.string().optional().describe('Session ID for continuing existing sessions'),
    environmentType: z.enum(['browser', 'docker']).optional().describe('Type of environment to use (browser recommended for most tasks)'),
    displayWidth: z.number().optional().describe('Display width in pixels (default: 1024)'),
    displayHeight: z.number().optional().describe('Display height in pixels (default: 768)'),
    initialUrl: z.string().optional().describe('Initial URL to navigate to (browser environment only)'),
    userPrompt: z.string().optional().describe('Task description for the computer use AI to execute'),
    acknowledgedSafetyChecks: z.array(z.object({
      id: z.string(),
      code: z.string(),
      message: z.string()
    })).optional().describe('Safety checks being acknowledged to continue'),
    callId: z.string().optional().describe('Call ID for continuing with screenshot')
  }) as any,

  execute: async (params: any, context?: any) => {
    const traceId = generateTraceId();
    const startTime = Date.now();
    let errorName: string | undefined;
    let finalEmitted = false;

    try {
      // Hard gate: allow turning off computer_use entirely (esp. during dev)
      if (!config.computerUse.enabled) {
        throw new Error('computer_use is disabled in this environment');
      }
      // Clean up inactive sessions first
      await computerUseManager.cleanupInactiveSessions();

      const { action, sessionId, environmentType, displayWidth, displayHeight, initialUrl, userPrompt, acknowledgedSafetyChecks, callId } = params;

      // Log input parameters for debugging
      logger.debug('🔧 Computer use tool called with action:', { action, sessionId });

      let result;
      switch (action) {
        case 'start_session':
          if (!environmentType || !userPrompt) {
            throw new Error('environmentType and userPrompt are required for starting a session');
          }
          if (environmentType !== 'docker' && !config.computerUse.allowBrowserEnv) {
            throw new Error('environmentType="browser" is not allowed for computer_use');
          }
          const session = await computerUseManager.createSession({
            environmentType: environmentType as 'browser' | 'docker',
            displayWidth,
            displayHeight,
            initialUrl,
            userPrompt,
            requiresConfirmation: true
          });
          result = {
            success: true,
            sessionId: session.id,
            message: 'Computer use session started successfully'
          };
          break;

        case 'execute_task':
          if (!userPrompt) {
            throw new Error('userPrompt is required for task execution');
          }
          const validSessionId = sessionId && sessionId.trim() !== '' ? sessionId : undefined;
          const taskResult = await computerUseManager.executeComputerUseTask({
            sessionId: validSessionId,
            environmentType: (environmentType as 'browser' | 'docker') || 'browser',
            displayWidth,
            displayHeight,
            initialUrl,
            userPrompt,
            requiresConfirmation: true
          });
          result = computerUseManager.compressComputerUseResponse(taskResult);
          finalEmitted = true;
          break;

        case 'acknowledge_safety':
          if (!sessionId || !acknowledgedSafetyChecks) {
            throw new Error('sessionId and acknowledgedSafetyChecks are required');
          }
          result = await computerUseManager.acknowledgeAndContinue(sessionId, acknowledgedSafetyChecks);
          break;

        case 'continue_session':
          if (!sessionId || !callId) {
            throw new Error('sessionId and callId are required');
          }
          result = await computerUseManager.continueWithScreenshot(sessionId, callId);
          break;

        case 'close_session':
          if (!sessionId || sessionId.trim() === '') {
            throw new Error('sessionId is required');
          }
          await computerUseManager.closeSession(sessionId);
          result = {
            success: true,
            message: 'Session closed successfully'
          };
          break;

        default:
          throw new Error(`Unknown action: ${action}`);
      }
      return JSON.stringify(result);
    } catch (error) {
      errorName = error instanceof Error ? error.name : 'UnknownError';
      logger.error('Computer use operation failed', { traceId, error });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Computer use operation failed'
      });
    } finally {
      const latency = Date.now() - startTime;
      logger.info('Tool execution completed', {
        traceId,
        runId: context?.runId,
        toolName: `computer_use:${params.action}`,
        latency,
        errorName,
        finalEmitted
      });
    }
  }
} as any);

export { computerUseManager };

// Helper to safely invoke the tool's execute with proper ToolCallOptions
export async function runComputerUseTool(params: any, mcpManagerInstance?: MCPServerManager): Promise<any> {
  if (mcpManagerInstance) {
    computerUseManager = new ComputerUseManager(mcpManagerInstance);
  }
  const exec = (computerUseTool as any)?.execute;
  if (typeof exec !== 'function') {
    throw new Error('computerUseTool.execute is not available');
  }
  return exec(params, { toolCallId: `manual_${Date.now()}`, messages: [] });
}
