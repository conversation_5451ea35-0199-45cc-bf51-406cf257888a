import { tool } from 'ai';
import { z } from 'zod';

// Tool to summarize and consolidate search results to prevent token overflow
export const summarizeSearchResultsTool = tool({
  name: 'summarize_search_results',
  description: 'Summarize and consolidate multiple search results to prevent token overflow. Use this after gathering search data but before proceeding with analysis.',
  inputSchema: z.object({
    searchQuery: z.string().describe('The original search query or pattern'),
    searchResults: z.array(z.object({
      toolName: z.string().describe('Name of the tool used'),
      resultSummary: z.string().describe('Brief summary of results from this tool'),
      keyFindings: z.array(z.string()).describe('Key findings or file paths found')
    })).describe('Array of search results to summarize'),
    targetUseCase: z.string().describe('What the user wants to do with these results'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { searchQuery, searchResults, targetUseCase } = params;

      // Consolidate all findings
      const allFiles = new Set<string>();
      const allFindings = new Set<string>();
      const toolsUsed = new Set<string>();

      for (const result of searchResults) {
        toolsUsed.add(result.toolName);

        // Extract file paths and findings
        for (const finding of result.keyFindings) {
          if (finding.includes('/') || finding.includes('.')) {
            // Looks like a file path
            allFiles.add(finding);
          } else {
            // Looks like a finding or match
            allFindings.add(finding);
          }
        }
      }

      // Create consolidated summary
      const summary = {
        searchQuery,
        totalResults: searchResults.length,
        toolsUsed: Array.from(toolsUsed),
        filesFound: Array.from(allFiles).slice(0, 20), // Limit to 20 files
        keyFindings: Array.from(allFindings).slice(0, 10), // Limit to 10 findings
        truncated: {
          files: allFiles.size > 20,
          findings: allFindings.size > 10
        },
        recommendation: generateRecommendation(targetUseCase, allFiles.size, allFindings.size)
      };

      return {
        success: true,
        summary,
        message: `Consolidated ${searchResults.length} search results: found ${allFiles.size} files and ${allFindings.size} matches for "${searchQuery}"`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to summarize search results',
      };
    }
  },
});

// Tool to create a focused search plan to avoid excessive tool calls
export const createSearchPlanTool = tool({
  name: 'create_search_plan',
  description: 'Create a focused search plan to find specific information efficiently without causing token overflow.',
  inputSchema: z.object({
    userQuery: z.string().describe('The user\'s original query'),
    searchTarget: z.string().describe('What specifically needs to be found'),
    currentKnowledge: z.string().optional().describe('What we already know about the codebase'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { userQuery, searchTarget, currentKnowledge } = params;

      const plan = createOptimalSearchPlan(userQuery, searchTarget, currentKnowledge);

      return {
        success: true,
        plan,
        message: `Created focused search plan with ${plan.steps.length} steps to find: ${searchTarget}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create search plan',
      };
    }
  },
});

function generateRecommendation(targetUseCase: string, fileCount: number, findingCount: number): string {
  const recommendations = [];

  if (fileCount > 50) {
    recommendations.push('Large number of files found - consider narrowing search criteria');
  }

  if (findingCount > 100) {
    recommendations.push('Many matches found - results may need further filtering');
  }

  switch (targetUseCase.toLowerCase()) {
    case 'replacement':
    case 'update':
      recommendations.push('Use find-and-replace tools on the identified files');
      break;
    case 'analysis':
    case 'review':
      recommendations.push('Focus on the most relevant files first, then expand if needed');
      break;
    case 'listing':
    case 'inventory':
      recommendations.push('Current results provide a good overview of usage patterns');
      break;
    default:
      recommendations.push('Review the key findings to determine next steps');
  }

  return recommendations.join('; ');
}

function createOptimalSearchPlan(userQuery: string, searchTarget: string, currentKnowledge?: string): any {
  const plan = {
    objective: searchTarget,
    steps: [] as Array<{step: number, tool: string, parameters: any, rationale: string}>,
    tokenBudget: 15000, // Keep under rate limits
    expectedOutcome: ''
  };

  // Analyze the query to determine the best search strategy
  const query = userQuery.toLowerCase();
  const target = searchTarget.toLowerCase();

  if (target.includes('gpt-4.1') || target.includes('model')) {
    // Specific model search
    plan.steps.push({
      step: 1,
      tool: 'grep_code',
      parameters: {
        pattern: 'gpt-4.1',
        path: '/Users/<USER>/dante-gpt',
        fileTypes: ['ts', 'js', 'json', 'md'],
        caseSensitive: false
      },
      rationale: 'Search for exact model name in code files'
    });

    plan.expectedOutcome = 'List of files containing gpt-4.1 references';
  } else if (target.includes('function') || target.includes('class')) {
    // Code structure search
    plan.steps.push({
      step: 1,
      tool: 'grep_code',
      parameters: {
        pattern: target.replace('function ', '').replace('class ', ''),
        path: '/Users/<USER>/dante-gpt',
        fileTypes: ['ts', 'js', 'tsx', 'jsx'],
        caseSensitive: false
      },
      rationale: 'Search for code definitions'
    });

    plan.expectedOutcome = 'Locations of code definitions';
  } else {
    // General search
    plan.steps.push({
      step: 1,
      tool: 'search_files',
      parameters: {
        path: '/Users/<USER>/dante-gpt',
        pattern: target,
        excludePatterns: ['node_modules', '.git']
      },
      rationale: 'General file and content search'
    });

    plan.expectedOutcome = 'Files containing the search target';
  }

  // Add consolidation step
  plan.steps.push({
    step: 2,
    tool: 'summarize_search_results',
    parameters: {
      searchQuery: target,
      targetUseCase: userQuery.includes('find') ? 'listing' : 'analysis'
    },
    rationale: 'Consolidate results to prevent token overflow'
  });

  return plan;
}

// Export all search summarization tools
export const searchSummarizationTools = [
  summarizeSearchResultsTool,
  createSearchPlanTool,
];
