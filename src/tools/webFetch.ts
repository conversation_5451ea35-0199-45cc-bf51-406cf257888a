import { tool } from 'ai';
import { z } from 'zod';
import { fetchWebContent } from './webSearchHelpers';

export const webFetchTool = tool({
  name: 'web_fetch',
  description: 'Fetch and extract content from a specific URL. Returns cleaned text content and metadata.',
  inputSchema: z.object({
    url: z.string().describe('The URL to fetch content from'),
    extractMarkdown: z.boolean().optional().default(true).describe('Whether to extract content as markdown'),
  }) as any,
  execute: async ({ url, extractMarkdown }: any): Promise<string> => {
    const result = await fetchWebContent(url, extractMarkdown ?? true);
    return JSON.stringify(result, null, 2);
  },
} as any);