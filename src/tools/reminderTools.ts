import { tool } from 'ai';
import { z } from 'zod';
import { reminderManager } from '../reminder/ReminderManager';
import { newsPollingService } from '../reminder/services/NewsPollingService';
import { notificationDelivery } from '../reminder/notification/NotificationDelivery';
import { 
  ReminderType, 
  ReminderStatus, 
  NotificationChannel,
  type ReminderSchedule,
  type ReminderAction,
} from '../reminder/types';

// Create reminder tool
export const createReminderTool = tool({
  name: 'create_reminder',
  description: 'Create a new reminder with flexible scheduling options. Supports one-time, recurring, and event-based reminders.',
  inputSchema: z.object({
    title: z.string().describe('Clear title for the reminder'),
    description: z.string().describe('Detailed description of what this reminder is for'),
    type: z.enum(['one_time', 'recurring', 'news_monitoring', 'event_based']).describe('Type of reminder to create'),
    executeAt: z.string().optional().describe('ISO date string for one-time reminders (e.g., "2025-08-20T10:00:00Z")'),
    cronExpression: z.string().optional().describe('Cron expression for recurring reminders (e.g., "0 9 * * MON-FRI" for weekdays at 9am)'),
    timezone: z.string().optional().describe('Timezone for scheduling (e.g., "America/New_York", defaults to "UTC")'),
    actionType: z.enum(['notification', 'web_search', 'news_poll', 'agent_handoff', 'custom']).describe('What action to perform when reminder triggers'),
    // Restrict payload schema to satisfy OpenAI tool JSON schema (additionalProperties: false)
    actionPayload: z.union([
      z.object({ message: z.string().describe('Notification message') }).strict(),
      z.object({ query: z.string().describe('Search query') }).strict(),
      z.object({ url: z.string().url().describe('Target URL') }).strict(),
    ]).optional().describe('Action-specific configuration (message, query, or url)'),
    notificationChannels: z.array(z.enum(['web_ui', 'file_queue', 'memory_store'])).default(['web_ui']).describe('How to deliver notifications'),
    priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium').describe('Priority level of the reminder'),
    tags: z.array(z.string()).default([]).describe('Tags for organizing reminders'),
    maxExecutions: z.number().optional().describe('Maximum number of times to execute (for recurring reminders)'),
    maxRetries: z.number().default(3).describe('Maximum retry attempts if execution fails'),
    sessionId: z.string().optional().describe('Session ID to associate with this reminder'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { title, description, type, executeAt, cronExpression, timezone, actionType, actionPayload, notificationChannels, priority, tags, maxExecutions, maxRetries, sessionId } = params;
      
      // Build schedule based on type
      const schedule: ReminderSchedule = {
        timezone: timezone || 'UTC',
      };
      
      if (type === 'one_time' && executeAt) {
        schedule.executeAt = new Date(executeAt);
      } else if (type === 'recurring' && cronExpression) {
        schedule.cronExpression = cronExpression;
      }
      
      // Build action configuration
      const action: ReminderAction = {
        type: actionType,
        payload: actionPayload,
        notificationChannels: notificationChannels.map((ch: string) => {
          switch (ch) {
            case 'web_ui': return NotificationChannel.WEB_UI;
            case 'file_queue': return NotificationChannel.FILE_QUEUE;
            case 'memory_store': return NotificationChannel.MEMORY_STORE;
            default: return NotificationChannel.WEB_UI;
          }
        }),
      };
      
      const reminder = await reminderManager.createReminder({
        title,
        description,
        type: type as ReminderType,
        schedule,
        action,
        priority,
        tags,
        maxExecutions,
        maxRetries,
        sessionId,
        createdBy: 'ReminderAgent',
      });
      
      return {
        success: true,
        reminderId: reminder.id,
        reminder: {
          id: reminder.id,
          title: reminder.title,
          type: reminder.type,
          status: reminder.status,
          nextExecutionAt: reminder.nextExecutionAt,
          priority: reminder.priority,
        },
        message: `Successfully created ${type} reminder: "${title}"`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create reminder. Please check your parameters and try again.',
      };
    }
  },
});

// Create news monitoring reminder tool
export const createNewsMonitoringTool = tool({
  name: 'create_news_monitoring',
  description: 'Set up automated news monitoring for events, topics, or live coverage with intelligent polling and summarization.',
  inputSchema: z.object({
    title: z.string().describe('Title for the monitoring task'),
    topic: z.string().describe('Main topic to monitor (e.g., "Google Pixel 10 launch", "Apple earnings", "SpaceX launch")'),
    eventDate: z.string().optional().describe('ISO date string for the event (e.g., "2025-08-20")'),
    eventStartTime: z.string().optional().describe('ISO datetime string for when event starts (e.g., "2025-08-20T10:00:00-07:00")'),
    eventEndTime: z.string().optional().describe('ISO datetime string for when event ends'),
    summaryType: z.enum(['live_updates', 'blow_by_blow', 'periodic_summary', 'final_report']).default('live_updates').describe('Type of coverage to provide'),
    pollingInterval: z.number().default(30).describe('How often to check for updates (in minutes, default: 30)'),
    searchQueries: z.array(z.string()).optional().describe('Custom search queries (auto-generated if not provided)'),
    keywordsToTrack: z.array(z.string()).optional().describe('Specific keywords to watch for in updates'),
    maxUpdatesPerHour: z.number().default(10).describe('Maximum number of updates to send per hour'),
    notificationChannels: z.array(z.enum(['web_ui', 'file_queue', 'memory_store'])).default(['web_ui']).describe('How to deliver updates'),
    sessionId: z.string().optional().describe('Session ID to associate with this monitoring'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { title, topic, eventDate, eventStartTime, eventEndTime, summaryType, pollingInterval, searchQueries, keywordsToTrack, maxUpdatesPerHour, notificationChannels, sessionId } = params;
      
      // Parse dates
      let parsedEventDate: Date | undefined;
      let parsedStartTime: Date | undefined;
      let parsedEndTime: Date | undefined;
      
      if (eventDate) parsedEventDate = new Date(eventDate);
      if (eventStartTime) parsedStartTime = new Date(eventStartTime);
      if (eventEndTime) parsedEndTime = new Date(eventEndTime);
      
      const reminder = await reminderManager.createNewsMonitoringReminder({
        title,
        topic,
        eventDate: parsedEventDate,
        eventStartTime: parsedStartTime,
        eventEndTime: parsedEndTime,
        summaryType,
        pollingInterval,
        searchQueries,
        keywordsToTrack,
        notificationChannels: notificationChannels?.map((ch: string) => {
          switch (ch) {
            case 'web_ui': return NotificationChannel.WEB_UI;
            case 'file_queue': return NotificationChannel.FILE_QUEUE;
            case 'memory_store': return NotificationChannel.MEMORY_STORE;
            default: return NotificationChannel.WEB_UI;
          }
        }),
        sessionId,
        createdBy: 'ReminderAgent',
      });
      
      return {
        success: true,
        reminderId: reminder.id,
        monitoring: {
          id: reminder.id,
          title: reminder.title,
          topic,
          pollingInterval,
          summaryType,
          nextCheck: reminder.nextExecutionAt,
          status: reminder.status,
        },
        message: `Successfully set up news monitoring for "${topic}". ${summaryType === 'blow_by_blow' ? 'You\'ll receive detailed blow-by-blow coverage as events unfold.' : `You'll receive ${summaryType.replace('_', ' ')} updates every ${pollingInterval} minutes.`}`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to set up news monitoring. Please check your parameters and try again.',
      };
    }
  },
});

// List reminders tool
export const listRemindersTool = tool({
  name: 'list_reminders',
  description: 'List existing reminders with optional filtering by status, type, or other criteria.',
  inputSchema: z.object({
    status: z.enum(['active', 'completed', 'cancelled', 'failed', 'paused']).optional().describe('Filter by reminder status'),
    type: z.enum(['one_time', 'recurring', 'news_monitoring', 'event_based']).optional().describe('Filter by reminder type'),
    sessionId: z.string().optional().describe('Filter by session ID'),
    tags: z.array(z.string()).optional().describe('Filter by tags'),
    limit: z.number().default(20).describe('Maximum number of reminders to return'),
    includeCompleted: z.boolean().default(false).describe('Include completed reminders in results'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { status, type, sessionId, tags, limit, includeCompleted } = params;
      
      const filters: any = {};
      if (status) filters.status = status as ReminderStatus;
      if (type) filters.type = type as ReminderType;
      if (sessionId) filters.sessionId = sessionId;
      if (tags) filters.tags = tags;
      
      const allReminders = await reminderManager.listReminders(filters);
      
      // Filter out completed unless specifically requested
      const filteredReminders = includeCompleted ? 
        allReminders : 
        allReminders.filter(r => r.status !== ReminderStatus.COMPLETED);
      
      const limitedReminders = filteredReminders.slice(0, limit);
      
      const reminderSummaries = limitedReminders.map(r => ({
        id: r.id,
        title: r.title,
        type: r.type,
        status: r.status,
        priority: r.priority,
        nextExecutionAt: r.nextExecutionAt,
        lastExecutedAt: r.lastExecutedAt,
        executionCount: r.executionCount,
        tags: r.tags,
        createdAt: r.createdAt,
      }));
      
      return {
        success: true,
        reminders: reminderSummaries,
        totalFound: filteredReminders.length,
        totalShown: limitedReminders.length,
        filters: { status, type, sessionId, tags },
        message: `Found ${filteredReminders.length} reminders${filters.status ? ` with status "${filters.status}"` : ''}${filters.type ? ` of type "${filters.type}"` : ''}`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to list reminders.',
      };
    }
  },
});

// Get reminder details tool
export const getReminderTool = tool({
  name: 'get_reminder',
  description: 'Get detailed information about a specific reminder by ID.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to retrieve'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId } = params;
      
      const reminder = await reminderManager.getReminder(reminderId);
      
      if (!reminder) {
        return {
          success: false,
          message: `Reminder with ID "${reminderId}" not found.`,
        };
      }
      
      // Get additional status for news monitoring reminders
      let additionalInfo: any = {};
      
      if (reminder.type === ReminderType.NEWS_MONITORING) {
        const session = newsPollingService.getActiveSession(reminderId);
        if (session) {
          additionalInfo.newsMonitoring = {
            topic: session.config.topic,
            updateCount: session.updateCount,
            lastUpdate: session.lastUpdate,
            seenUrlCount: session.seenUrls.size,
            keyDevelopmentCount: session.keyDevelopments.length,
            isActive: session.isActive,
          };
        }
      }
      
      return {
        success: true,
        reminder: {
          ...reminder,
          ...additionalInfo,
        },
        message: `Retrieved details for reminder: "${reminder.title}"`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to retrieve reminder details.',
      };
    }
  },
});

// Update reminder tool
export const updateReminderTool = tool({
  name: 'update_reminder',
  description: 'Update an existing reminder\'s properties, schedule, or action configuration.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to update'),
    title: z.string().optional().describe('New title for the reminder'),
    description: z.string().optional().describe('New description for the reminder'),
    executeAt: z.string().optional().describe('New execution time for one-time reminders (ISO date string)'),
    cronExpression: z.string().optional().describe('New cron expression for recurring reminders'),
    priority: z.enum(['low', 'medium', 'high', 'critical']).optional().describe('New priority level'),
    tags: z.array(z.string()).optional().describe('New tags (replaces existing tags)'),
    maxExecutions: z.number().optional().describe('New maximum execution count'),
    actionPayload: z.object({}).passthrough().optional().describe('New action payload'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId, title, description, executeAt, cronExpression, priority, tags, maxExecutions, actionPayload } = params;
      
      const updates: any = {};
      
      if (title !== undefined) updates.title = title;
      if (description !== undefined) updates.description = description;
      if (priority !== undefined) updates.priority = priority;
      if (tags !== undefined) updates.tags = tags;
      if (maxExecutions !== undefined) updates.maxExecutions = maxExecutions;
      
      // Handle schedule updates
      if (executeAt !== undefined || cronExpression !== undefined) {
        updates.schedule = {};
        if (executeAt) updates.schedule.executeAt = new Date(executeAt);
        if (cronExpression) updates.schedule.cronExpression = cronExpression;
      }
      
      // Handle action updates
      if (actionPayload !== undefined) {
        updates.action = { payload: actionPayload };
      }
      
      const updatedReminder = await reminderManager.updateReminder(reminderId, updates);
      
      if (!updatedReminder) {
        return {
          success: false,
          message: `Reminder with ID "${reminderId}" not found.`,
        };
      }
      
      return {
        success: true,
        reminder: {
          id: updatedReminder.id,
          title: updatedReminder.title,
          type: updatedReminder.type,
          status: updatedReminder.status,
          priority: updatedReminder.priority,
          nextExecutionAt: updatedReminder.nextExecutionAt,
          updatedAt: updatedReminder.updatedAt,
        },
        message: `Successfully updated reminder: "${updatedReminder.title}"`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to update reminder.',
      };
    }
  },
});

// Cancel reminder tool
export const cancelReminderTool = tool({
  name: 'cancel_reminder',
  description: 'Cancel an active reminder, stopping all future executions.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to cancel'),
    reason: z.string().optional().describe('Optional reason for cancellation'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId, reason } = params;
      
      const reminder = await reminderManager.getReminder(reminderId);
      if (!reminder) {
        return {
          success: false,
          message: `Reminder with ID "${reminderId}" not found.`,
        };
      }
      
      const cancelled = await reminderManager.cancelReminder(reminderId);
      
      if (cancelled) {
        // If it's a news monitoring reminder, stop the polling
        if (reminder.type === ReminderType.NEWS_MONITORING) {
          await newsPollingService.stopPolling(reminderId);
        }
        
        // Send cancellation notification if requested
        await notificationDelivery.sendSystemNotification(
          'Reminder Cancelled',
          `Reminder "${reminder.title}" has been cancelled.${reason ? ` Reason: ${reason}` : ''}`,
          reminder.sessionId
        );
        
        return {
          success: true,
          message: `Successfully cancelled reminder: "${reminder.title}"`,
        };
      } else {
        return {
          success: false,
          message: 'Failed to cancel reminder.',
        };
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to cancel reminder.',
      };
    }
  },
});

// Pause/Resume reminder tools
export const pauseReminderTool = tool({
  name: 'pause_reminder',
  description: 'Temporarily pause an active reminder without cancelling it.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to pause'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId } = params;
      
      const paused = await reminderManager.pauseReminder(reminderId);
      
      if (paused) {
        return {
          success: true,
          message: 'Reminder paused successfully. Use resume_reminder to restart it.',
        };
      } else {
        return {
          success: false,
          message: 'Failed to pause reminder. It may not exist or may not be active.',
        };
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to pause reminder.',
      };
    }
  },
});

export const resumeReminderTool = tool({
  name: 'resume_reminder',
  description: 'Resume a paused reminder.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to resume'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId } = params;
      
      const resumed = await reminderManager.resumeReminder(reminderId);
      
      if (resumed) {
        return {
          success: true,
          message: 'Reminder resumed successfully.',
        };
      } else {
        return {
          success: false,
          message: 'Failed to resume reminder. It may not exist or may not be paused.',
        };
      }
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to resume reminder.',
      };
    }
  },
});

// System status tool
export const getReminderSystemStatusTool = tool({
  name: 'get_reminder_system_status',
  description: 'Get current status of the reminder system, including active reminders, news polling sessions, and delivery statistics.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(false).describe('Include detailed information about active sessions'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { includeDetails } = params;
      
      const systemStatus = reminderManager.getSystemStatus();
      const queueStatus = notificationDelivery.getQueueStatus();
      const deliveryStats = notificationDelivery.getStats();
      const activeSessions = newsPollingService.getAllActiveSessions();
      
      const status = {
        system: {
          initialized: systemStatus.initialized,
          enabled: systemStatus.config.enabled,
          maxActiveReminders: systemStatus.config.maxActiveReminders,
          scheduler: systemStatus.scheduler,
        },
        notifications: {
          queueLength: queueStatus.queueLength,
          activeConnections: queueStatus.activeConnections,
          isProcessing: queueStatus.isProcessing,
          stats: deliveryStats,
        },
        newsPolling: {
          activeSessions: activeSessions.length,
          maxConcurrentSessions: 10, // From NewsPollingService
        },
      };
      
      if (includeDetails) {
        (status as any).details = {
          activeSessions: activeSessions.map(session => ({
            reminderId: session.reminderId,
            topic: session.config.topic,
            updateCount: session.updateCount,
            lastUpdate: session.lastUpdate,
            seenUrlCount: session.seenUrls.size,
            isActive: session.isActive,
          })),
        };
      }
      
      return {
        success: true,
        status,
        message: `Reminder system is ${systemStatus.initialized ? 'active' : 'inactive'}. ${activeSessions.length} news monitoring sessions running.`,
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get system status.',
      };
    }
  },
});

// Export missing tools referenced in ReminderAgent
export const deleteReminderTool = cancelReminderTool; // Alias for compatibility
export const searchRemindersTool = listRemindersTool; // Use list with filtering for search

// Export all reminder tools
export const reminderTools = [
  createReminderTool,
  createNewsMonitoringTool,
  listRemindersTool,
  getReminderTool,
  updateReminderTool,
  cancelReminderTool,
  deleteReminderTool,
  searchRemindersTool,
  pauseReminderTool,
  resumeReminderTool,
  getReminderSystemStatusTool,
];
