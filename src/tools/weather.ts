import { tool } from 'ai';
import { z } from 'zod';
import fetch from 'node-fetch';
import { config } from '../utils/config';

export const weatherTool = tool({
  name: 'get_weather',
  description: 'Get current weather information for a specific location',
  inputSchema: z.object({
    location: z.string().describe('City name or location'),
    units: z.enum(['metric', 'imperial']).optional().default('metric'),
  }) as any,
  execute: async ({ location, units }: any) => {
    try {
      if (!config.weather.apiKey) {
        // Simulate weather data when API key is not available
        return JSON.stringify({
          location,
          condition: 'Partly cloudy',
          humidity: 65,
          windSpeed: 10,
          units,
          source: 'simulated',
        });
      }

      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(
          location
        )}&units=${units}&appid=${config.weather.apiKey}`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.statusText}`);
      }

      const data = await response.json() as any;

      return JSON.stringify({
        location: data.name,
        condition: data.weather[0].description,
        humidity: data.main.humidity,
        windSpeed: data.wind.speed,
        units,
      });
    } catch (error) {
      return JSON.stringify({
        error: `Failed to get weather: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  },
} as any);
