// Environment-aware tool loading for browser vs Node.js compatibility

import { tool } from 'ai';
import { z } from 'zod';
import { reminderTools } from './reminderTools';
import { browserReminderTools } from './browserReminderTools';

// Detect if we're running in a browser environment
export function isBrowserEnvironment(): boolean {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

// Get the appropriate reminder tools based on environment
export function getReminderToolsForEnvironment() {
  if (isBrowserEnvironment()) {
    console.log('🌐 Using browser-compatible reminder tools (API-based)');
    return browserReminderTools;
  } else {
    console.log('🖥️ Using server-side reminder tools (direct)');
    return reminderTools;
  }
}

// Get environment-specific tool configuration
export function getEnvironmentConfig() {
  const env = isBrowserEnvironment() ? 'browser' : 'server';
  
  return {
    environment: env,
    reminderTools: getReminderToolsForEnvironment(),
    features: {
      fileSystemAccess: !isBrowserEnvironment(),
      backgroundProcesses: !isBrowserEnvironment(),
      nodeModules: !isBrowserEnvironment(),
      apiCallsRequired: isBrowserEnvironment(),
      persistentStorage: true, // Both environments support some form
      realTimeUpdates: true,   // Both support via different mechanisms
    },
    limitations: isBrowserEnvironment() ? [
      'No direct file system access',
      'No background processes', 
      'Requires server API for persistent operations',
      'Limited to browser storage APIs'
    ] : [
      'Direct file system access available',
      'Background processes supported',
      'No browser-specific APIs'
    ],
  };
}

// Initialize environment-appropriate reminder system
export async function initializeEnvironmentAwareReminders(): Promise<{
  success: boolean;
  environment: string;
  reminderSystemAvailable: boolean;
  apiServerRequired: boolean;
  message: string;
}> {
  const env = isBrowserEnvironment() ? 'browser' : 'server';
  
  try {
    if (isBrowserEnvironment()) {
      // In browser, test API connection
      try {
        const response = await fetch('/api/reminders/system/status');
        const result = await response.json();
        
        return {
          success: true,
          environment: env,
          reminderSystemAvailable: result.success,
          apiServerRequired: true,
          message: result.success ? 
            'Browser environment: Connected to reminder API' : 
            'Browser environment: API server not responding',
        };
      } catch (error) {
        return {
          success: false,
          environment: env,
          reminderSystemAvailable: false,
          apiServerRequired: true,
          message: 'Browser environment: API server not available. Start with: bun run dev:api',
        };
      }
    } else {
      // In Node.js, reminders should work directly
      const { reminderManager } = await import('../reminder/ReminderManager');
      
      try {
        await reminderManager.initialize();
        
        return {
          success: true,
          environment: env,
          reminderSystemAvailable: true,
          apiServerRequired: false,
          message: 'Server environment: Direct reminder system available',
        };
      } catch (error) {
        return {
          success: false,
          environment: env,
          reminderSystemAvailable: false,
          apiServerRequired: false,
          message: `Server environment: Failed to initialize reminder system: ${error}`,
        };
      }
    }
  } catch (error) {
    return {
      success: false,
      environment: env,
      reminderSystemAvailable: false,
      apiServerRequired: isBrowserEnvironment(),
      message: `Failed to initialize reminder system: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Tool to check environment and reminder system availability
export const checkEnvironmentTool = tool({
  name: 'check_environment',
  description: 'Check current environment and reminder system availability',
  inputSchema: z.object({
    checkApiConnection: z.boolean().default(true).describe('Test API server connection'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { checkApiConnection } = params;
      
      const config = getEnvironmentConfig();
      let initResult;
      
      if (checkApiConnection) {
        initResult = await initializeEnvironmentAwareReminders();
      } else {
        initResult = {
          success: true,
          environment: config.environment,
          reminderSystemAvailable: false,
          apiServerRequired: config.environment === 'browser',
          message: 'Skipped API connection test',
        };
      }
      
      return {
        success: true,
        environment: config.environment,
        features: config.features,
        limitations: config.limitations,
        reminderSystem: {
          available: initResult.reminderSystemAvailable,
          apiRequired: initResult.apiServerRequired,
          status: initResult.message,
        },
        toolsAvailable: config.reminderTools.length,
        message: `Running in ${config.environment} environment with ${config.reminderTools.length} reminder tools`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to check environment',
      };
    }
  },
});

// Export environment-aware tools
export const environmentAwareTools = [checkEnvironmentTool];