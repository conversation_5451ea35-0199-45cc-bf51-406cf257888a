import { tool } from 'ai';
import { z } from 'zod';
import { geminiContextService } from '../services/geminiContextService';

/**
 * Tool for consolidating large files using Gemini's context window
 */
export const consolidateLargeFileTool = tool({
  name: 'consolidate_large_file',
  description: 'Use Gemini to intelligently summarize large files with structure analysis and line references',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the large file to consolidate'),
    focusAreas: z.array(z.string()).optional().describe('Specific areas to focus on in the summary'),
    includeLineNumbers: z.boolean().optional().describe('Include line number references'),
    maxOutputTokens: z.number().optional().describe('Maximum tokens for the summary')
  }) as any,
  execute: async ({ filePath, focusAreas, includeLineNumbers = true, maxOutputTokens }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available. Please configure GEMINI_API_KEY.'
        };
      }

      const result = await geminiContextService.summarizeFile(filePath, {
        focusAreas,
        includeLineNumbers,
        maxOutputTokens
      });

      return {
        success: true,
        summary: result.summary,
        structure: result.structure,
        keyElements: result.keyElements,
        lineReferences: result.lineReferences,
        hint: 'Use line references to navigate to specific parts of the file'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to consolidate file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for analyzing code with Gemini's advanced reasoning
 */
export const analyzeWithContextTool = tool({
  name: 'analyze_with_context',
  description: 'Use Gemini\'s reasoning capabilities for deep code analysis across multiple files',
  inputSchema: z.object({
    filePaths: z.array(z.string()).describe('Paths to files to analyze'),
    analysisType: z.enum(['structure', 'dependencies', 'patterns', 'comprehensive']).describe('Type of analysis'),
    includeRecommendations: z.boolean().optional().describe('Include recommendations')
  }) as any,
  execute: async ({ filePaths, analysisType, includeRecommendations = true }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const options = {
        includeImports: analysisType === 'dependencies' || analysisType === 'comprehensive',
        includeDependencies: analysisType === 'dependencies' || analysisType === 'comprehensive',
        includeComments: analysisType === 'comprehensive'
      };

      const result = await geminiContextService.analyzeCodeStructure(filePaths, options);

      return {
        success: true,
        overview: result.overview,
        dependencies: analysisType === 'dependencies' || analysisType === 'comprehensive' ? result.dependencies : undefined,
        architecture: result.architecture,
        recommendations: includeRecommendations ? result.recommendations : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for debugging with Gemini's reasoning capabilities
 */
export const debugWithReasoningTool = tool({
  name: 'debug_with_reasoning',
  description: 'Use Gemini\'s advanced reasoning to debug complex issues in large files',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file with the issue'),
    errorMessage: z.string().optional().describe('Error message if available'),
    stackTrace: z.string().optional().describe('Stack trace if available'),
    symptoms: z.array(z.string()).optional().describe('Observable symptoms of the issue')
  }) as any,
  execute: async ({ filePath, errorMessage, stackTrace, symptoms }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const result = await geminiContextService.debugLargeFile(filePath, {
        errorMessage,
        stackTrace,
        symptoms
      });

      return {
        success: true,
        analysis: result.analysis,
        rootCause: result.rootCause,
        suggestedFixes: result.suggestedFixes,
        verificationSteps: result.verificationSteps,
        hint: 'Apply suggested fixes and follow verification steps to confirm resolution'
      };
    } catch (error) {
      return {
        success: false,
        error: `Debug analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for extracting relevant code sections
 */
export const extractRelevantCodeTool = tool({
  name: 'extract_relevant_code',
  description: 'Extract specific sections from large files based on a query',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to search'),
    query: z.string().describe('What to look for in the file'),
    maxSections: z.number().optional().describe('Maximum number of sections to return')
  }) as any,
  execute: async ({ filePath, query, maxSections = 5 }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const sections = await geminiContextService.extractRelevantSections(filePath, query, maxSections);

      return {
        success: true,
        sections,
        totalFound: sections.length,
        hint: `Found ${sections.length} relevant sections. Use line numbers to navigate.`
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to extract sections: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for creating strategic plans using Gemini
 */
export const createStrategicPlanTool = tool({
  name: 'create_strategic_plan',
  description: 'Use Gemini\'s reasoning to create detailed execution plans for complex tasks',
  inputSchema: z.object({
    request: z.string().describe('The task or request to plan for'),
    filesAnalyzed: z.array(z.string()).optional().describe('Files already analyzed'),
    previousConclusions: z.array(z.string()).optional().describe('Previous findings or conclusions'),
    maxTokensPerAgent: z.number().optional().describe('Token limit per agent'),
    availableAgents: z.array(z.string()).optional().describe('List of available agents')
  }) as any,
  execute: async ({ request, filesAnalyzed, previousConclusions, maxTokensPerAgent, availableAgents }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const plan = await geminiContextService.createStrategicPlan(request, {
        filesAnalyzed,
        previousConclusions,
        constraints: {
          maxTokensPerAgent,
          availableAgents
        }
      });

      return {
        success: true,
        plan: {
          description: plan.description,
          reasoning: plan.reasoning,
          steps: plan.steps,
          totalEstimatedTokens: plan.totalEstimatedTokens,
          riskAssessment: plan.riskAssessment,
          alternativeApproaches: plan.alternativeApproaches
        },
        hint: 'Execute the plan steps in order, respecting dependencies'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to create plan: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for analyzing progress and determining next steps
 */
export const analyzeProgressTool = tool({
  name: 'analyze_progress',
  description: 'Analyze completed work and synthesize recommendations for next steps',
  inputSchema: z.object({
    completedSteps: z.array(z.object({
      step: z.string(),
      result: z.string()
    })).describe('Steps that have been completed'),
    remainingWork: z.array(z.string()).describe('Work that still needs to be done'),
    includeAdjustedPlan: z.boolean().optional().describe('Whether to create an adjusted plan')
  }) as any,
  execute: async ({ completedSteps, remainingWork, includeAdjustedPlan = false }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const analysis = await geminiContextService.synthesizeProgress(
        completedSteps,
        remainingWork
      );

      return {
        success: true,
        completedWork: analysis.completedWork,
        remainingWork: analysis.remainingWork,
        recommendations: analysis.recommendations,
        potentialBlockers: analysis.potentialBlockers,
        adjustedPlan: includeAdjustedPlan ? analysis.adjustedPlan : undefined,
        hint: 'Follow recommendations to efficiently complete remaining work'
      };
    } catch (error) {
      return {
        success: false,
        error: `Progress analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for comparing files and identifying patterns
 */
export const compareFilesTool = tool({
  name: 'compare_files',
  description: 'Compare multiple files to identify similarities, differences, and patterns',
  inputSchema: z.object({
    filePaths: z.array(z.string()).describe('Paths to files to compare'),
    analysisType: z.enum(['similarity', 'differences', 'patterns', 'all']).optional().describe('Type of comparison')
  }) as any,
  execute: async ({ filePaths, analysisType = 'all' }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const comparison = await geminiContextService.compareFiles(filePaths, analysisType);

      return {
        success: true,
        comparison: comparison.comparison,
        similarities: comparison.similarities,
        differences: comparison.differences,
        patterns: comparison.patterns,
        recommendations: comparison.recommendations
      };
    } catch (error) {
      return {
        success: false,
        error: `Comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

/**
 * Tool for generating refactoring plans
 */
export const generateRefactorPlanTool = tool({
  name: 'generate_refactor_plan',
  description: 'Create a detailed refactoring plan for improving code quality',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to refactor'),
    goals: z.array(z.string()).optional().describe('Refactoring goals'),
    patterns: z.array(z.string()).optional().describe('Patterns to apply'),
    constraints: z.array(z.string()).optional().describe('Constraints to respect')
  }) as any,
  execute: async ({ filePath, goals, patterns, constraints }: any) => {
    try {
      if (!geminiContextService.isAvailable()) {
        return {
          success: false,
          error: 'Gemini context service is not available'
        };
      }

      const plan = await geminiContextService.generateRefactorPlan(filePath, {
        goals,
        patterns,
        constraints
      });

      return {
        success: true,
        plan: plan.plan,
        steps: plan.steps,
        benefits: plan.benefits,
        testingStrategy: plan.testingStrategy,
        hint: 'Review the plan before executing. Test after each major change.'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to generate refactor plan: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
} as any);

// Export all Gemini context tools as a collection
export const geminiContextTools = [
  consolidateLargeFileTool,
  analyzeWithContextTool,
  debugWithReasoningTool,
  extractRelevantCodeTool,
  createStrategicPlanTool,
  analyzeProgressTool,
  compareFilesTool,
  generateRefactorPlanTool
];

// Export a helper to check if Gemini tools are available
export function areGeminiToolsAvailable(): boolean {
  return geminiContextService.isAvailable();
}