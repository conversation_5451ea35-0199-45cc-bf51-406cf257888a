import { tool } from 'ai';
import { z } from 'zod';
import { 
  getAllMCPTools,
  getMCPToolsByTags,
  getMCPStatus,
  connectMCPServer,
  disconnectMCPServer,
  performMCPHealthCheck,
  invalidateMCPToolCache
} from '../mcp';

/**
 * MCP Tools - High-level tools for interacting with MCP integration
 * These tools allow other agents to discover and manage MCP servers
 */

export const mcpStatusTool = tool({
  name: 'get_mcp_status',
  description: 'Get the current status of all MCP servers and integration health',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const status = getMCPStatus();
      return JSON.stringify({
        enabled: status.enabled,
        initialized: status.initialized,
        serverCount: status.serverCount,
        connectedCount: status.connectedCount,
        servers: status.servers.map(server => ({
          id: server.id,
          name: server.name,
          status: server.status,
          toolCount: server.toolCount,
          lastConnected: server.lastConnected,
          lastError: server.lastError
        })),
        toolFactory: status.toolFactory
      }, null, 2);
    } catch (error) {
      return `<PERSON>rror getting MCP status: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

export const mcpDiscoverToolsTool = tool({
  name: 'discover_mcp_tools',
  description: 'Discover all available MCP tools from connected servers, optionally filtered by tags',
  inputSchema: z.object({
    tags: z.array(z.string()).nullable().optional().describe('Optional tags to filter tools (e.g., ["filesystem", "git"])')
  }),
  execute: async ({ tags }) => {
    try {
      const result = tags && tags.length > 0 
        ? await getMCPToolsByTags(tags)
        : await getAllMCPTools();
      
      return JSON.stringify({
        totalTools: result.tools.length,
        tools: result.metadata.map(meta => ({
          name: meta.toolName,
          description: meta.description,
          server: meta.serverName,
          serverId: meta.serverId,
          tags: meta.tags,
          schema: meta.schema
        })),
        ...(tags && { filteredByTags: tags })
      }, null, 2);
    } catch (error) {
      return `Error discovering MCP tools: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

export const mcpHealthCheckTool = tool({
  name: 'mcp_health_check',
  description: 'Perform a health check on all connected MCP servers to verify connectivity',
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const results = await performMCPHealthCheck();
      const healthSummary = Object.entries(results).map(([serverId, isHealthy]) => ({
        serverId,
        status: isHealthy ? 'healthy' : 'unhealthy'
      }));
      
      return JSON.stringify({
        healthCheck: 'completed',
        timestamp: new Date().toISOString(),
        results: healthSummary,
        totalServers: healthSummary.length,
        healthyServers: healthSummary.filter(s => s.status === 'healthy').length,
        unhealthyServers: healthSummary.filter(s => s.status === 'unhealthy').length
      }, null, 2);
    } catch (error) {
      return `Error performing MCP health check: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

export const mcpConnectServerTool = tool({
  name: 'connect_mcp_server',
  description: 'Connect to a specific MCP server by ID to make its tools available',
  inputSchema: z.object({
    serverId: z.string().describe('The ID of the MCP server to connect to')
  }),
  execute: async ({ serverId }) => {
    try {
      await connectMCPServer(serverId);
      return `Successfully connected to MCP server: ${serverId}`;
    } catch (error) {
      return `Error connecting to MCP server ${serverId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

export const mcpDisconnectServerTool = tool({
  name: 'disconnect_mcp_server',
  description: 'Disconnect from a specific MCP server by ID to free resources',
  inputSchema: z.object({
    serverId: z.string().describe('The ID of the MCP server to disconnect from')
  }),
  execute: async ({ serverId }) => {
    try {
      await disconnectMCPServer(serverId);
      return `Successfully disconnected from MCP server: ${serverId}`;
    } catch (error) {
      return `Error disconnecting from MCP server ${serverId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

export const mcpRefreshToolsTool = tool({
  name: 'refresh_mcp_tools',
  description: 'Invalidate and refresh the MCP tool cache to discover newly available tools',
  inputSchema: z.object({
    serverId: z.string().optional().describe('Optional server ID to refresh cache for specific server only')
  }),
  execute: async ({ serverId }) => {
    try {
      invalidateMCPToolCache(serverId);
      return serverId 
        ? `Successfully refreshed MCP tool cache for server: ${serverId}`
        : 'Successfully refreshed MCP tool cache for all servers';
    } catch (error) {
      return `Error refreshing MCP cache: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
});

// Export all MCP tools as an array for easy integration
export const mcpTools = [
  mcpStatusTool,
  mcpDiscoverToolsTool,
  mcpHealthCheckTool,
  mcpConnectServerTool,
  mcpDisconnectServerTool,
  mcpRefreshToolsTool
];

// Export individual tools
export {
  mcpStatusTool as getMCPStatusTool,
  mcpDiscoverToolsTool as discoverMCPToolsTool,
  mcpConnectServerTool as connectMCPServerTool,
  mcpDisconnectServerTool as disconnectMCPServerTool,
  mcpRefreshToolsTool as refreshMCPToolsTool
};