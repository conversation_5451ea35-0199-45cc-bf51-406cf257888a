import fetch from 'node-fetch';
import * as cheerio from 'cheerio';

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
  publishedDate?: string;
}

/**
 * Search using DuckDuckGo HTML scraping (more reliable than instant answer API)
 */
export async function searchDuckDuckGoHTML(query: string): Promise<SearchResult[]> {
  try {
    const encodedQuery = encodeURIComponent(query);
    const url = `https://html.duckduckgo.com/html/?q=${encodedQuery}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        // 'Accept-Encoding': 'gzip, deflate, br', // Remove to get uncompressed response
        'Cache-Control': 'max-age=0',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"macOS"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
      },
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`DuckDuckGo HTML search error: ${response.status}`);
    }
    
    const html = await response.text();
    const $ = cheerio.load(html);
    const results: SearchResult[] = [];
    
    
    // Parse search results
    $('.result').each((index, element) => {
      if (index >= 10) return; // Limit to 10 results
      
      const $result = $(element);
      const $title = $result.find('.result__title');
      const $snippet = $result.find('.result__snippet');
      const $url = $result.find('.result__url');
      
      const titleText = $title.text().trim();
      const snippetText = $snippet.text().trim();
      const urlHref = $title.find('a').attr('href');
      
      if (titleText && urlHref) {
        // DuckDuckGo uses a redirect URL, extract the actual URL
        const actualUrl = urlHref.includes('uddg=') 
          ? decodeURIComponent(urlHref.split('uddg=')[1].split('&')[0])
          : urlHref;
        
        results.push({
          title: titleText,
          url: actualUrl,
          snippet: snippetText || 'No description available',
          source: 'DuckDuckGo',
          publishedDate: new Date().toISOString(),
        });
      }
    });
    
    // If no regular results, check for instant answer box
    if (results.length === 0) {
      const instantAnswer = $('.zci__body').text().trim();
      if (instantAnswer) {
        results.push({
          title: query,
          url: `https://duckduckgo.com/?q=${encodedQuery}`,
          snippet: instantAnswer.substring(0, 500),
          source: 'DuckDuckGo Instant Answer',
          publishedDate: new Date().toISOString(),
        });
      }
    }
    
    return results;
  } catch (error) {
    console.error('DuckDuckGo HTML search error:', error);
    return [];
  }
}