import { tool } from 'ai';
import { z } from 'zod';

// Browser-compatible reminder tools that use API calls to the server

// API base URL - will use current origin in browser
const getApiBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Browser environment
    return `${window.location.protocol}//${window.location.host}`;
  } else {
    // Node.js environment (fallback)
    return 'http://localhost:3001';
  }
};

// Helper function to make API calls
async function makeApiCall(endpoint: string, method: string = 'GET', data?: any): Promise<any> {
  const baseUrl = getApiBaseUrl();
  const url = `${baseUrl}${endpoint}`;

  const options: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || result.error || 'API call failed');
    }

    return result;
  } catch (error) {
    console.error(`API call failed: ${method} ${endpoint}`, error);
    throw error;
  }
}

// Create news monitoring reminder (browser-compatible)
export const createBrowserNewsMonitoringTool = tool({
  name: 'create_browser_news_monitoring',
  description: 'Set up automated news monitoring for events using server-side API. Works in browser environment.',
  inputSchema: z.object({
    title: z.string().describe('Title for the monitoring task'),
    topic: z.string().describe('Main topic to monitor'),
    eventDate: z.string().optional().describe('ISO date string for the event'),
    eventStartTime: z.string().optional().describe('ISO datetime string for when event starts'),
    eventEndTime: z.string().optional().describe('ISO datetime string for when event ends'),
    summaryType: z.enum(['live_updates', 'blow_by_blow', 'periodic_summary', 'final_report']).default('live_updates'),
    pollingInterval: z.number().default(30).describe('How often to check for updates (in minutes)'),
    searchQueries: z.array(z.string()).optional().describe('Custom search queries'),
    keywordsToTrack: z.array(z.string()).optional().describe('Keywords to watch for'),
    maxUpdatesPerHour: z.number().default(10).describe('Maximum updates per hour'),
    sessionId: z.string().optional().describe('Session ID'),
  }) as any,
  execute: async (params: any) => {
    try {
      const {
        title,
        topic,
        eventDate,
        eventStartTime,
        eventEndTime,
        summaryType,
        pollingInterval,
        searchQueries,
        keywordsToTrack,
        maxUpdatesPerHour,
        sessionId,
      } = params;

      console.log(`📰 Creating news monitoring via API: ${topic}`);

      const result = await makeApiCall('/api/reminders/news-monitoring', 'POST', {
        title,
        topic,
        eventDate,
        eventStartTime,
        eventEndTime,
        summaryType,
        pollingInterval,
        searchQueries,
        keywordsToTrack,
        maxUpdatesPerHour,
        notificationChannels: ['web_ui'],
        sessionId: sessionId || 'browser-session',
        createdBy: 'DanteOrchestrator-Browser',
      });

      if (result.success) {
        console.log(`✅ News monitoring created successfully: ${result.reminder.id}`);

        return {
          success: true,
          reminderId: result.reminder.id,
          monitoring: result.reminder,
          message: result.message,
          apiResponse: 'Successfully created via server API',
        };
      } else {
        throw new Error(result.error || 'Failed to create news monitoring');
      }
    } catch (error) {
      console.error('Failed to create browser news monitoring:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create news monitoring. The server API may not be available.',
        suggestion: 'Make sure the API server is running on port 3001',
      };
    }
  },
});

// List reminders (browser-compatible)
export const listBrowserRemindersTool = tool({
  name: 'list_browser_reminders',
  description: 'List existing reminders using server-side API. Works in browser environment.',
  inputSchema: z.object({
    status: z.enum(['active', 'completed', 'cancelled', 'failed', 'paused']).optional(),
    type: z.enum(['one_time', 'recurring', 'news_monitoring', 'event_based']).optional(),
    sessionId: z.string().optional(),
    limit: z.number().default(20).describe('Maximum number of reminders to return'),
    includeCompleted: z.boolean().default(false).describe('Include completed reminders'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { status, type, sessionId, limit, includeCompleted } = params;

      const queryParams = new URLSearchParams();
      if (status) queryParams.append('status', status);
      if (type) queryParams.append('type', type);
      if (sessionId) queryParams.append('sessionId', sessionId);
      if (limit) queryParams.append('limit', limit.toString());
      if (includeCompleted) queryParams.append('includeCompleted', includeCompleted.toString());

      const endpoint = `/api/reminders?${queryParams.toString()}`;
      const result = await makeApiCall(endpoint);

      if (result.success) {
        return {
          success: true,
          reminders: result.reminders,
          totalFound: result.totalFound,
          totalShown: result.totalShown,
          message: `Found ${result.totalFound} reminders`,
        };
      } else {
        throw new Error(result.error || 'Failed to list reminders');
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to list reminders via API',
      };
    }
  },
});

// Get specific reminder details (browser-compatible)
export const getBrowserReminderTool = tool({
  name: 'get_browser_reminder',
  description: 'Get detailed information about a specific reminder using server-side API.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to retrieve'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId } = params;

      const result = await makeApiCall(`/api/reminders/${reminderId}`);

      if (result.success) {
        return {
          success: true,
          reminder: result.reminder,
          message: `Retrieved details for reminder: ${result.reminder.title}`,
        };
      } else {
        throw new Error(result.error || 'Reminder not found');
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get reminder details via API',
        suggestion: 'Check if the reminder ID is correct and the API server is running',
      };
    }
  },
});

// Cancel reminder (browser-compatible)
export const cancelBrowserReminderTool = tool({
  name: 'cancel_browser_reminder',
  description: 'Cancel an active reminder using server-side API.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to cancel'),
    reason: z.string().optional().describe('Reason for cancellation'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId, reason } = params;

      const result = await makeApiCall(`/api/reminders/${reminderId}/cancel`, 'POST', { reason });

      if (result.success) {
        return {
          success: true,
          message: result.message,
        };
      } else {
        throw new Error(result.error || 'Failed to cancel reminder');
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to cancel reminder via API',
      };
    }
  },
});

// Get reminder system status (browser-compatible)
export const getBrowserReminderStatusTool = tool({
  name: 'get_browser_reminder_status',
  description: 'Get the current status of the reminder system using server-side API.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(false).describe('Include detailed session information'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { includeDetails } = params;

      const result = await makeApiCall('/api/reminders/system/status');

      if (result.success) {
        return {
          success: true,
          status: result.status,
          message: result.message,
          serverConnection: 'Connected to reminder API',
        };
      } else {
        throw new Error(result.error || 'Failed to get system status');
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get reminder system status. Server may not be running.',
        suggestion: 'Start the API server with: bun run dev:api',
      };
    }
  },
});

// Create recovery-aware reminder (browser-compatible)
export const createBrowserRecoveryReminderTool = tool({
  name: 'create_browser_recovery_reminder',
  description: 'Create a reminder with recovery capabilities using server-side API. Ideal for development tasks.',
  inputSchema: z.object({
    title: z.string().describe('Title for the reminder'),
    description: z.string().describe('Detailed description'),
    executeAt: z.string().describe('ISO datetime string for execution'),
    actionType: z.enum(['notification', 'web_search', 'news_poll']).default('notification'),
    actionPayload: z.any().describe('Action configuration'),
    priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
    tags: z.array(z.string()).default([]).describe('Tags for organization'),
    sessionId: z.string().optional().describe('Session ID'),
    createRecoveryPoint: z.boolean().default(false).describe('Also create a recovery point for this task'),
  }) as any,
  execute: async (params: any) => {
    try {
      const {
        title,
        description,
        executeAt,
        actionType,
        actionPayload,
        priority,
        tags,
        sessionId,
        createRecoveryPoint,
      } = params;

      // Create the reminder via API
      const reminderResult = await makeApiCall('/api/reminders', 'POST', {
        title,
        description,
        type: 'one_time',
        schedule: {
          executeAt,
          timezone: 'UTC',
        },
        action: {
          type: actionType,
          payload: actionPayload,
          notificationChannels: ['web_ui'],
        },
        priority,
        tags,
        sessionId: sessionId || 'browser-session',
        createdBy: 'DanteOrchestrator-Browser',
      });

      let recoveryTaskId: string | undefined;

      // Optionally create recovery point
      if (createRecoveryPoint && reminderResult.success) {
        try {
          const recoveryResult = await makeApiCall('/api/recovery/tasks', 'POST', {
            sessionId: sessionId || 'browser-session',
            agentName: 'DanteCore',
            objective: `Recovery for reminder: ${title}`,
            conversationMessages: [
              {
                role: 'system',
                content: `Recovery point for reminder: ${title}`,
                timestamp: Date.now(),
              },
            ],
          });

          if (recoveryResult.success) {
            recoveryTaskId = recoveryResult.task.taskId;
          }
        } catch (recoveryError) {
          console.warn('Failed to create recovery point:', recoveryError);
        }
      }

      if (reminderResult.success) {
        return {
          success: true,
          reminderId: reminderResult.reminder.id,
          reminder: reminderResult.reminder,
          recoveryTaskId,
          message: reminderResult.message,
          apiResponse: 'Successfully created via server API',
        };
      } else {
        throw new Error(reminderResult.error || 'Failed to create reminder');
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create reminder via API',
        suggestion: 'Make sure the API server is running on port 3001',
      };
    }
  },
});

// Export browser-compatible reminder tools
export const browserReminderTools = [
  createBrowserNewsMonitoringTool,
  listBrowserRemindersTool,
  getBrowserReminderTool,
  cancelBrowserReminderTool,
  getBrowserReminderStatusTool,
  createBrowserRecoveryReminderTool,
];
