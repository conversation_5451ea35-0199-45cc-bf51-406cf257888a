/**
 * System Health Tools
 * Tools for checking system health, diagnosing errors, and performing recovery actions
 */

import { tool } from 'ai';
import { z } from 'zod';
import { systemHealthMonitor } from '../services/SystemHealthMonitor';
import { mcpServerManager } from '../mcp/MCPServerManager';
import { exec } from 'child_process';
import { promisify } from 'util';
import { config } from '../utils/config';
import { processManager } from '../utils/processManager';
import * as fs from 'fs';
import * as path from 'path';

const execAsync = promisify(exec);

export const checkSystemHealthTool = tool({
  name: 'check_system_health',
  description: 'Check the overall health status of all system services and components.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(true).describe('Include detailed information about each service'),
    forceRefresh: z.boolean().default(false).describe('Force a fresh health check instead of using cached results'),
  }),
  execute: async (params: any) => {
    try {
      const { includeDetails, forceRefresh } = params;
      
      if (forceRefresh) {
        // Force fresh checks on all services
        await systemHealthMonitor.startMonitoring();
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for checks to complete
      }
      
      const status = systemHealthMonitor.getSystemStatus();
      
      return {
        success: true,
        systemStatus: status.overall,
        summary: {
          total: status.diagnostics.totalServices,
          healthy: status.diagnostics.healthyServices,
          degraded: status.diagnostics.degradedServices,
          failed: status.diagnostics.failedServices,
        },
        services: includeDetails ? status.services : status.services.map(s => ({
          name: s.name,
          status: s.status,
          type: s.type,
        })),
        recommendations: status.recommendations || [],
        timestamp: status.timestamp,
        message: `System is ${status.overall}. ${status.diagnostics.failedServices} services failed, ${status.diagnostics.degradedServices} degraded.`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to check system health',
      };
    }
  },
});

export const diagnoseErrorTool = tool({
  name: 'diagnose_error',
  description: 'Analyze a specific error and provide diagnostic information with suggested fixes.',
  inputSchema: z.object({
    error: z.string().describe('The error message or description to diagnose'),
    context: z.string().optional().describe('Additional context about when/where the error occurred'),
    serviceName: z.string().optional().describe('Name of the service that encountered the error'),
  }),
  execute: async (params: any) => {
    try {
      const { error, context, serviceName } = params;
      
      // Create a basic diagnostic context (DiagnosticAgent temporarily disabled during migration)
      const diagnosticContext = {
        error: error instanceof Error ? error.message : String(error),
        systemError: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
        context: context || 'Unknown',
        serviceName: serviceName || 'Unknown',
        recovery: {
          suggested: false,
          actions: [],
          priority: 'low',
          strategy: 'none',
          confidence: 0,
          isRecoverable: false,
          suggestedActions: []
        }
      };
      
      let serviceInfo = null;
      if (serviceName) {
        const diagnosis = await systemHealthMonitor.diagnoseService(serviceName);
        serviceInfo = diagnosis;
      }
      
      const errorPatterns = systemHealthMonitor.getErrorPatterns()
        .filter(p => p.pattern.includes(error.substring(0, 20)))
        .slice(0, 3);
      
      return {
        success: true,
        diagnosis: {
          isSystemError: diagnosticContext.systemError,
          errorType: diagnosticContext.recovery.strategy,
          confidence: diagnosticContext.recovery.confidence,
          isRecoverable: diagnosticContext.recovery.isRecoverable,
        },
        analysis: {
          error: diagnosticContext.error,
          timestamp: diagnosticContext.timestamp,
          possibleCauses: serviceInfo?.possibleCauses || ['Unknown cause'],
          suggestedActions: diagnosticContext.recovery.suggestedActions,
        },
        serviceInfo: serviceInfo?.service ? {
          name: serviceInfo.service.name,
          status: serviceInfo.service.status,
          lastCheck: serviceInfo.service.lastCheck,
          error: serviceInfo.service.error,
        } : null,
        patterns: errorPatterns.map(p => ({
          pattern: p.pattern.substring(0, 100),
          occurrences: p.count,
          lastSeen: p.lastOccurred,
          suggestedFix: p.suggestedFix,
        })),
        message: diagnosticContext.systemError 
          ? `System error detected: ${diagnosticContext.recovery.strategy} strategy recommended`
          : `User error detected: Manual intervention needed`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to diagnose error',
      };
    }
  },
});

export const restartServiceTool = tool({
  name: 'restart_service',
  description: 'Restart a failed service or reconnect to a disconnected server.',
  inputSchema: z.object({
    serviceName: z.string().describe('Name of the service to restart'),
    serviceType: z.enum(['mcp_server', 'tool_server', 'api_endpoint', 'process']).optional()
      .describe('Type of service to help determine restart method'),
    force: z.boolean().default(false).describe('Force restart even if service appears healthy'),
  }),
  execute: async (params: any) => {
    try {
      const { serviceName, serviceType, force } = params;
      
      const actions: string[] = [];
      
      // Check current service status first
      const currentStatus = systemHealthMonitor.getSystemStatus();
      const service = currentStatus.services.find(s => s.name === serviceName);
      
      if (!service) {
        return {
          success: false,
          message: `Service '${serviceName}' not found in monitoring system`,
          availableServices: currentStatus.services.map(s => s.name),
        };
      }
      
      if (service.status === 'healthy' && !force) {
        return {
          success: true,
          message: `Service '${serviceName}' is already healthy`,
          currentStatus: service.status,
        };
      }
      
      // Attempt service-specific restart logic
      const actualType = serviceType || service.type;
      
      switch (actualType) {
        case 'mcp_server':
          actions.push('Restarting MCP servers...');
          try {
            // Get all server statuses and restart failed ones
            const servers = mcpServerManager.getAllServerStatuses();
            const failedServers = servers.filter(s => s.status !== 'connected');
            
            for (const server of failedServers) {
              actions.push(`Reconnecting MCP server: ${server.name}`);
              await mcpServerManager.connectServer(server.id);
            }
            
            actions.push(`✅ Restarted ${failedServers.length} MCP servers`);
          } catch (error) {
            actions.push(`❌ MCP restart failed: ${error}`);
          }
          break;
          
        case 'tool_server':
          actions.push('Attempting to restart tool server...');
          const toolPort = service.metadata?.port;
          const toolName = service.metadata?.serverName || serviceName;
          
          try {
            // Kill any existing process on the port
            if (toolPort) {
              await processManager.killProcessByPort(toolPort);
              actions.push(`Cleared port ${toolPort}`);
            }
            
            // Restart the tool server
            const toolInfo = await processManager.restartToolServer(toolName, toolPort);
            actions.push(`✅ Started ${toolName} server (PID: ${toolInfo.pid})`);
          } catch (error) {
            actions.push(`❌ Tool server restart failed: ${error}`);
          }
          break;
          
        case 'api_endpoint':
          actions.push('Restarting API server...');
          const apiPort = service.metadata?.port || 3001;
          
          try {
            // Kill any existing process on the port
            await processManager.killProcessByPort(apiPort);
            actions.push(`Cleared port ${apiPort}`);
            
            // Restart the API server
            const apiInfo = await processManager.restartAPIServer();
            actions.push(`✅ Started API server (PID: ${apiInfo.pid}, Port: ${apiPort})`);
            
            // Wait for server to be ready
            await new Promise(resolve => setTimeout(resolve, 3000));
          } catch (error) {
            actions.push(`❌ API restart failed: ${error}`);
          }
          break;
          
        case 'process':
          actions.push('Process restart not supported - manual intervention required');
          break;
          
        default:
          actions.push('Unknown service type - attempting generic recovery');
      }
      
      // Wait a moment then recheck
      await new Promise(resolve => setTimeout(resolve, 2000));
      const updatedStatus = systemHealthMonitor.getSystemStatus();
      const updatedService = updatedStatus.services.find(s => s.name === serviceName);
      
      const success = updatedService?.status === 'healthy';
      
      return {
        success,
        serviceName,
        previousStatus: service.status,
        currentStatus: updatedService?.status || 'unknown',
        actions,
        message: success 
          ? `Successfully restarted ${serviceName}`
          : `Restart attempted but ${serviceName} is still ${updatedService?.status}`,
        nextSteps: success 
          ? ['Service is now operational'] 
          : [
              'Check service logs for errors',
              'Verify service configuration', 
              'Consider manual restart',
            ],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: `Failed to restart service: ${params.serviceName}`,
      };
    }
  },
});

export const verifyDependenciesTool = tool({
  name: 'verify_dependencies',
  description: 'Check if all required dependencies and services are installed and available.',
  inputSchema: z.object({
    checkAll: z.boolean().default(true).describe('Check all known dependencies'),
    specificDependency: z.string().optional().describe('Check a specific dependency by name'),
  }),
  execute: async (params: any) => {
    try {
      const { specificDependency } = params;
      
      const dependencies = [
        { name: 'node', command: 'node --version', type: 'runtime' },
        { name: 'bun', command: 'bun --version', type: 'runtime' },
        { name: 'npm', command: 'npm --version', type: 'package_manager' },
        { name: 'git', command: 'git --version', type: 'tool' },
      ];
      
      const toCheck = specificDependency 
        ? dependencies.filter(d => d.name === specificDependency)
        : dependencies;
      
      const results = [];
      
      for (const dep of toCheck) {
        try {
          const { stdout } = await execAsync(dep.command);
          results.push({
            name: dep.name,
            type: dep.type,
            status: 'available',
            version: stdout.trim(),
          });
        } catch (error) {
          results.push({
            name: dep.name,
            type: dep.type,
            status: 'missing',
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
      
      const available = results.filter(r => r.status === 'available');
      const missing = results.filter(r => r.status === 'missing');
      
      return {
        success: true,
        summary: {
          total: results.length,
          available: available.length,
          missing: missing.length,
        },
        dependencies: results,
        missingDependencies: missing.map(d => ({
          name: d.name,
          type: d.type,
          installInstructions: getInstallInstructions(d.name),
        })),
        message: missing.length > 0 
          ? `${missing.length} dependencies are missing`
          : 'All dependencies are available',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to verify dependencies',
      };
    }
  },
});

export const selfRepairTool = tool({
  name: 'self_repair',
  description: 'Attempt to automatically fix common system issues and restore normal operation.',
  inputSchema: z.object({
    repairType: z.enum(['full', 'services', 'connections', 'configuration']).default('full')
      .describe('Type of repair to attempt'),
    dryRun: z.boolean().default(false).describe('Show what would be repaired without making changes'),
  }),
  execute: async (params: any) => {
    try {
      const { repairType, dryRun } = params;
      
      const actions: string[] = [];
      const repairs: string[] = [];
      
      // Get current system status
      const status = systemHealthMonitor.getSystemStatus();
      const failedServices = status.services.filter(s => s.status === 'failed');
      const degradedServices = status.services.filter(s => s.status === 'degraded');
      
      if (failedServices.length === 0 && degradedServices.length === 0) {
        return {
          success: true,
          message: 'No repairs needed - system is healthy',
          currentStatus: status.overall,
        };
      }
      
      // Plan repairs based on type
      if (repairType === 'full' || repairType === 'services') {
        for (const service of failedServices) {
          const repair = `Restart ${service.name} (${service.type})`;
          repairs.push(repair);
          
          if (!dryRun) {
            actions.push(`Attempting to repair: ${repair}`);
            try {
              const result = await systemHealthMonitor.attemptRecovery(service.name);
              actions.push(`Recovery result: ${result.message}`);
            } catch (error) {
              actions.push(`Recovery failed: ${error}`);
            }
          }
        }
      }
      
      if (repairType === 'full' || repairType === 'connections') {
        // Check MCP connections
        const mcpServers = mcpServerManager.getAllServerStatuses();
        const disconnectedMCP = mcpServers.filter(s => s.status !== 'connected');
        
        for (const server of disconnectedMCP) {
          const repair = `Reconnect MCP server: ${server.name}`;
          repairs.push(repair);
          
          if (!dryRun) {
            actions.push(`Attempting: ${repair}`);
            try {
              await mcpServerManager.connectServer(server.id);
              actions.push(`Successfully reconnected ${server.name}`);
            } catch (error) {
              actions.push(`Failed to reconnect ${server.name}: ${error}`);
            }
          }
        }
      }
      
      if (repairType === 'full' || repairType === 'configuration') {
        // Check basic configuration
        repairs.push('Verify system configuration');
        
        if (!dryRun) {
          actions.push('Checking configuration...');
          
          // Check environment variables
          const requiredEnvVars = ['OPENAI_API_KEY'];
          
          for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
              actions.push(`❌ Missing environment variable: ${envVar}`);
              repairs.push(`Set ${envVar} environment variable`);
            } else {
              actions.push(`✅ Found ${envVar}`);
            }
          }
          
          // Check critical directories
          const criticalDirs = [
            path.join(process.cwd(), 'src'),
            path.join(process.cwd(), 'node_modules'),
            path.join(process.cwd(), '.memory'),
          ];
          
          for (const dir of criticalDirs) {
            if (fs.existsSync(dir)) {
              actions.push(`✅ Directory exists: ${path.basename(dir)}`);
            } else {
              actions.push(`❌ Missing directory: ${path.basename(dir)}`);
              
              // Create missing memory directory if needed
              if (dir.endsWith('.memory')) {
                try {
                  fs.mkdirSync(dir, { recursive: true });
                  actions.push(`✅ Created missing .memory directory`);
                } catch (error) {
                  actions.push(`❌ Failed to create .memory directory: ${error}`);
                }
              }
            }
          }
          
          // Check package.json exists
          const packageJsonPath = path.join(process.cwd(), 'package.json');
          if (fs.existsSync(packageJsonPath)) {
            actions.push('✅ package.json found');
            
            // Verify dependencies are installed
            const nodeModulesPath = path.join(process.cwd(), 'node_modules');
            if (!fs.existsSync(nodeModulesPath)) {
              actions.push('❌ node_modules missing - run "bun install" to install dependencies');
            }
          } else {
            actions.push('❌ package.json not found - not in a valid project directory');
          }
          
          // Check port availability
          const portsToCheck = [3001, 3002]; // API and Web UI ports
          for (const port of portsToCheck) {
            const processInfo = processManager.findProcessByPort(port);
            if (processInfo) {
              actions.push(`✅ Port ${port} is in use by ${processInfo.name}`);
            } else {
              actions.push(`⚠️ Port ${port} is available`);
            }
          }
          
          actions.push('Configuration check completed');
        }
      }
      
      // Wait for repairs to take effect
      if (!dryRun && repairs.length > 0) {
        actions.push('Waiting for repairs to take effect...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check final status
        const finalStatus = systemHealthMonitor.getSystemStatus();
        const remainingIssues = finalStatus.services.filter(s => s.status === 'failed').length;
        
        actions.push(`Repair complete. ${remainingIssues} issues remaining.`);
      }
      
      return {
        success: true,
        repairType,
        dryRun,
        plannedRepairs: repairs,
        actionsPerformed: actions,
        summary: {
          servicesRepaired: failedServices.length,
          connectionsFixed: 0, // Would be calculated based on actual repairs
          issuesRemaining: dryRun ? failedServices.length : 'unknown',
        },
        message: dryRun 
          ? `Would perform ${repairs.length} repairs`
          : `Attempted ${repairs.length} repairs`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Self-repair failed',
      };
    }
  },
});

export const getSystemStatusTool = tool({
  name: 'get_system_status',
  description: 'Get a quick overview of current system status and any active issues.',
  inputSchema: z.object({
    format: z.enum(['summary', 'detailed', 'json']).default('summary')
      .describe('Format of the status report'),
  }),
  execute: async (params: any) => {
    try {
      const { format } = params;
      
      const status = systemHealthMonitor.getSystemStatus();
      const errorPatterns = systemHealthMonitor.getErrorPatterns().slice(0, 5);
      
      if (format === 'summary') {
        return {
          success: true,
          status: status.overall,
          summary: `${status.diagnostics.healthyServices}/${status.diagnostics.totalServices} services healthy`,
          issues: status.diagnostics.failedServices + status.diagnostics.degradedServices,
          recommendations: status.recommendations?.slice(0, 3) || [],
          timestamp: status.timestamp,
        };
      }
      
      if (format === 'detailed') {
        return {
          success: true,
          systemStatus: status,
          errorPatterns: errorPatterns.map(p => ({
            service: p.service,
            pattern: p.pattern.substring(0, 100),
            count: p.count,
            lastSeen: p.lastOccurred,
          })),
          activeIssues: status.services.filter(s => s.status !== 'healthy'),
          healthySince: status.timestamp,
        };
      }
      
      // JSON format
      return {
        success: true,
        data: {
          status,
          errorPatterns,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to get system status',
      };
    }
  },
});

/**
 * Helper function to provide installation instructions for missing dependencies
 */
function getInstallInstructions(dependency: string): string {
  const instructions: Record<string, string> = {
    node: 'Install Node.js from https://nodejs.org/ or use a version manager like nvm',
    bun: 'Install Bun from https://bun.sh/ with: curl -fsSL https://bun.sh/install | bash',
    npm: 'npm is included with Node.js installation',
    git: 'Install Git from https://git-scm.com/ or use your system package manager',
  };
  
  return instructions[dependency] || `Install ${dependency} using your system package manager`;
}

// Export all system health tools
export const systemHealthTools = [
  checkSystemHealthTool,
  diagnoseErrorTool,
  restartServiceTool,
  verifyDependenciesTool,
  selfRepairTool,
  getSystemStatusTool,
];