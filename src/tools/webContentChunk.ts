import { tool } from 'ai';
import { z } from 'zod';
import { fetchWebContent } from './webSearchHelpers';

/**
 * Tool for fetching specific chunks of web content
 * Useful for progressive content loading when initial search shows promising results
 */
export const webContentChunkTool = tool({
  name: 'web_content_chunk',
  description: 'Fetch specific chunks or deeper content from a web page URL. Use this after web_search to get more details from promising articles.',
  inputSchema: z.object({
    url: z.string().describe('The URL to fetch content from'),
    chunkIds: z.array(z.string()).optional().describe('Specific chunk IDs to fetch (e.g., ["intro", "section-1", "conclusion"])'),
    contentDepth: z.enum(['summary', 'detailed', 'full']).optional().default('detailed').describe('Level of content detail'),
    includeChunks: z.boolean().optional().default(true).describe('Whether to return chunk breakdown'),
  }) as any,
  execute: async ({ url, chunkIds, contentDepth, includeChunks }: any) => {
    try {
      const fetchData = await fetchWebContent(url, true, {
        enableChunking: includeChunks || true,
        contentDepth: contentDepth || 'detailed'
      });

      if (!fetchData.success) {
        return JSON.stringify({
          success: false,
          error: fetchData.error || 'Failed to fetch content',
          url,
        });
      }

      let responseData: any = {
        success: true,
        url,
        title: fetchData.title,
        timestamp: new Date().toISOString(),
      };

      // If specific chunks requested, return only those
      if (chunkIds && chunkIds.length > 0 && fetchData.chunks) {
        const requestedChunks = fetchData.chunks.filter(chunk => 
          chunkIds.includes(chunk.id)
        );
        
        responseData.chunks = requestedChunks;
        responseData.content = requestedChunks
          .map(chunk => `## ${chunk.title}\n${chunk.content}`)
          .join('\n\n');
      } else {
        // Return appropriate content based on depth
        const limits = {
          summary: 500,
          detailed: 2000,
          full: 15000
        };
        
        const limit = limits[contentDepth as keyof typeof limits] || limits.detailed;
        
        responseData.content = fetchData.content?.substring(0, limit) + 
          (fetchData.content && fetchData.content.length > limit ? '...' : '');
        
        if (fetchData.summary) {
          responseData.summary = fetchData.summary;
        }
        
        if (includeChunks && fetchData.chunks) {
          responseData.availableChunks = fetchData.chunks.map(chunk => ({
            id: chunk.id,
            title: chunk.title,
            type: chunk.type,
            priority: chunk.priority,
            wordCount: chunk.wordCount,
            preview: chunk.content.substring(0, 100) + '...'
          }));
        }
      }

      return JSON.stringify(responseData, null, 2);
    } catch (error) {
      return JSON.stringify({
        success: false,
        error: `Failed to fetch content: ${error instanceof Error ? error.message : 'Unknown error'}`,
        url,
        timestamp: new Date().toISOString(),
      }, null, 2);
    }
  },
} as any);