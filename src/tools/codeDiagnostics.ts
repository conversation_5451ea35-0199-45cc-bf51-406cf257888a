import { tool } from 'ai';
import { z } from 'zod';
import * as path from 'path';
import { sanitizeToolOutput } from '../mcp/utils';
import { diagnoseFile, diagnoseFiles, getModifiedFiles, runBuildCheck } from '../utils/codeDiagnostics';
import { agentTaskManager } from './taskManagement';
import { getContextValue } from '../utils/requestContext';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export const diagnoseFileSyntaxTool = tool({
  name: 'diagnose_file_syntax',
  description: 'Run syntax/type/format diagnostics on a single file and return precise errors with line and snippet.',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to diagnose'),
    includeFormatting: z.boolean().optional().describe('Include Prettier formatting/syntax check (default: false)')
  }) as any,
  execute: async ({ filePath, includeFormatting = false }: any) => {
    try {
      const abs = path.resolve(filePath);
      const result = await diagnoseFile(abs, { includeFormatting });
      return sanitizeToolOutput({ success: true, diagnostics: result });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'diagnose_file_syntax_failed' });
    }
  }
} as any);

export const diagnoseFilesSyntaxTool = tool({
  name: 'diagnose_files_syntax',
  description: 'Run syntax/type/format diagnostics on multiple files at once.',
  inputSchema: z.object({
    filePaths: z.array(z.string()).nonempty().describe('Files to diagnose'),
    includeFormatting: z.boolean().optional().describe('Include Prettier formatting/syntax checks')
  }) as any,
  execute: async ({ filePaths, includeFormatting = false }: any) => {
    try {
      const cwd = getContextValue<string>('cwd');
      const prev = process.cwd();
      // Resolve paths relative to the requested cwd to avoid
      // mismatches when running diagnostics in a different directory.
      const abs = filePaths.map((f: string) => path.resolve(cwd ?? process.cwd(), f));
      try { if (cwd) process.chdir(cwd); } catch {}
      const result = await diagnoseFiles(abs, { includeFormatting });
      try { process.chdir(prev); } catch {}
      return sanitizeToolOutput({ success: true, ...result });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'diagnose_files_syntax_failed' });
    }
  }
} as any);

export const listModifiedFilesTool = tool({
  name: 'list_modified_files',
  description: 'List modified files from git status (includes new/untracked by default).',
  inputSchema: z.object({
    includeUntracked: z.boolean().optional().describe('Include untracked files (default: true)')
  }) as any,
  execute: async ({ includeUntracked = true }: any) => {
    try {
      const cwd = getContextValue<string>('cwd');
      const files = await getModifiedFiles({ includeUntracked, cwd });
      return sanitizeToolOutput({ success: true, files });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'list_modified_files_failed' });
    }
  }
} as any);

export const diagnoseModifiedFilesTool = tool({
  name: 'diagnose_modified_files',
  description: 'Diagnose all modified files (from git) in a single pass and return errors grouped by file.',
  inputSchema: z.object({
    includeUntracked: z.boolean().optional(),
    includeFormatting: z.boolean().optional()
  }) as any,
  execute: async ({ includeUntracked = true, includeFormatting = false }: any) => {
    try {
      const cwd = getContextValue<string>('cwd');
      const files = await getModifiedFiles({ includeUntracked, cwd });
      if (files.length === 0) return sanitizeToolOutput({ success: true, ok: true, results: [] });
      const prev = process.cwd();
      try { if (cwd) process.chdir(cwd); } catch {}
      const result = await diagnoseFiles(files, { includeFormatting });
      try { process.chdir(prev); } catch {}
      return sanitizeToolOutput({ success: true, ...result });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'diagnose_modified_files_failed' });
    }
  }
} as any);

export const runBuildCheckTool = tool({
  name: 'run_build_check',
  description: 'Run a project build/typecheck or black (for python) and return compiler errors with file/line/snippet when available.',
  inputSchema: z.object({
    cwd: z.string().optional().describe('Working directory'),
    thorough: z.boolean().optional().describe('Run heavier language-specific checks (e.g., Flutter build_runner, slower)')
  }) as any,
  execute: async ({ cwd, thorough = false }: any) => {
    try {
      const cwdEff = cwd || getContextValue<string>('cwd');
      const result = await runBuildCheck(cwdEff, { thorough });
      return sanitizeToolOutput({ success: true, ...result });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'run_build_check_failed' });
    }
  }
} as any);

export const codeDiagnosticsTools = [
  diagnoseFileSyntaxTool,
  diagnoseFilesSyntaxTool,
  listModifiedFilesTool,
  diagnoseModifiedFilesTool,
  runBuildCheckTool,
];

export const formatFileTool = tool({
  name: 'format_file',
  description: 'Auto-format a source file using language-appropriate formatter (Prettier, dart format, black, gofmt, rustfmt).',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to format'),
  }) as any,
  execute: async ({ filePath }: { filePath: string }) => {
    try {
      const abs = path.resolve(filePath);
      const ext = (abs.split('.').pop() || '').toLowerCase();
      const cwd = getContextValue<string>('cwd');
      const run = async (cmd: string) => await execAsync(cmd, { cwd, maxBuffer: 5 * 1024 * 1024 });

      let command: string | null = null;
      if (['ts', 'tsx', 'js', 'jsx', 'json', 'md', 'css', 'scss', 'html', 'yml', 'yaml'].includes(ext)) {
        command = `bunx prettier --write "${abs.replace(/"/g, '\\"')}"`;
      } else if (ext === 'dart') {
        command = `dart format "${abs.replace(/"/g, '\\"')}"`;
      } else if (ext === 'py') {
        command = `black "${abs.replace(/"/g, '\\"')}"`;
      } else if (ext === 'go') {
        command = `gofmt -w "${abs.replace(/"/g, '\\"')}"`;
      } else if (ext === 'rs') {
        command = `rustfmt "${abs.replace(/"/g, '\\"')}"`;
      }

      if (!command) {
        return sanitizeToolOutput({ success: false, error: `No formatter configured for *.${ext}` });
      }

      let ok = true;
      let stdout = '';
      let stderr = '';
      try {
        const res = await run(command);
        stdout = res.stdout || '';
        stderr = res.stderr || '';
      } catch (e: any) {
        ok = false;
        stdout = e?.stdout || '';
        stderr = e?.stderr || e?.message || '';
      }

      // Prettier fallback via npx if bunx failed
      if (!ok && command.startsWith('bunx prettier')) {
        try {
          const res = await run(command.replace('bunx', 'npx -y'));
          ok = true;
          stdout = res.stdout || '';
          stderr = res.stderr || '';
        } catch (e: any) {
          ok = false;
          stdout = e?.stdout || stdout;
          stderr = e?.stderr || e?.message || stderr;
        }
      }

      // Flutter formatter fallback if dart format is unavailable
      if (!ok && command.startsWith('dart format')) {
        try {
          const res = await run(`flutter format "${abs.replace(/"/g, '\\"')}"`);
          ok = true;
          stdout = res.stdout || '';
          stderr = res.stderr || '';
        } catch (e: any) {
          ok = false;
          stdout = e?.stdout || stdout;
          stderr = e?.stderr || e?.message || stderr;
        }
      }

      return sanitizeToolOutput({ success: ok, path: abs, stdout, stderr });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'format_file_failed' });
    }
  }
} as any);

// Also export format tool
codeDiagnosticsTools.push(formatFileTool);

export const raiseBuildFailureTaskTool = tool({
  name: 'raise_build_failure_task',
  description: 'Create a Planning task to address build/typecheck or black (for python) failures with details and suggested next steps.',
  inputSchema: z.object({
    summary: z.string().describe('Short summary of the failure'),
    errors: z.array(z.object({ file: z.string().optional(), line: z.number().optional(), message: z.string() })).optional(),
    relatedFiles: z.array(z.string()).optional(),
  }) as any,
  execute: async ({ summary, errors = [], relatedFiles = [] }: any) => {
    try {
      const description = `Build/Typecheck or black (for python) failure: ${summary}\n\n` +
        (relatedFiles.length ? `Files: ${relatedFiles.join(', ')}\n\n` : '') +
        (errors.length ? `Errors (first 5):\n` + errors.slice(0, 5).map((e: any) => `- ${e.file ? e.file : ''}${e.line ? ':' + e.line : ''} ${e.message}`).join('\n') : '');
      const taskId = agentTaskManager.createTask({
        description,
        priority: 'high',
        status: 'pending',
        assignedAgent: 'PlanningAgent',
        metadata: { kind: 'build_failure', relatedFiles, errors }
      } as any);
      return sanitizeToolOutput({ success: true, taskId, message: 'Planning task created for build failure' });
    } catch (e: any) {
      return sanitizeToolOutput({ success: false, error: e?.message || 'raise_build_failure_task_failed' });
    }
  }
} as any);

// Also export this tool in the default collection
codeDiagnosticsTools.push(raiseBuildFailureTaskTool);
