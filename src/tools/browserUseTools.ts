import { tool } from 'ai';
import { z } from 'zod';
import { mcpServerManager } from '../mcp/MCPServerManager';
import { checkCancellation } from '../utils/cancel';
import { reportProgress } from '../utils/progress';

/**
 * Browser-Use MCP Tools
 *
 * Advanced browser automation tools using the browser-use Python MCP server.
 * These tools provide more sophisticated browser control including:
 * - Tab management
 * - AI-powered content extraction
 * - Session persistence
 * - Advanced interaction patterns
 */

// Helper to ensure browser-use MCP is connected
async function ensureBrowserUseConnected() {
  const server = mcpServerManager.getServer('browser-use');
  if (!server) {
    try {
      await mcpServerManager.connectServer('browser-use');
      return mcpServerManager.getServer('browser-use');
    } catch (error) {
      throw new Error(`Browser-Use MCP server not available. Run 'bun run browser-use:setup' first. Error: ${error}`);
    }
  }
  return server;
}

// Helper to execute browser-use tool
async function executeBrowserUseTool(toolName: string, params: any) {
  const server = await ensureBrowserUseConnected();
  if (!server) {
    throw new Error('Browser-Use MCP server not available');
  }

  try {
    const result = await server.callTool(toolName, params);

    // Surface common MCP-side schema/trace errors as real errors for callers
    const asText = typeof result === 'string' ? result : (() => { try { return JSON.stringify(result); } catch { return String(result); } })();
    if (/not valid under any of the given schemas/i.test(asText) || /Failed validating/i.test(asText) || /Traceback \(most recent call last\)/i.test(asText)) {
      throw new Error(`browser-use server error: ${asText.slice(0, 400)}...`);
    }

    return result;
  } catch (error) {
    console.error(`Error executing browser-use tool ${toolName}:`, error);
    throw error;
  }
}

export const browserUseNavigateTool = tool({
  name: 'browser_use_navigate',
  description: `Navigate to a URL using browser-use advanced automation. Supports tab management.`,
  inputSchema: z.object({
    url: z.string().describe('The URL to navigate to'),
    new_tab: z.boolean().optional().default(false).describe('Open in new tab')
  }),
  execute: async ({ url, new_tab }) => {
    try {
      reportProgress('browser_use: navigate', 5, { url, new_tab });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_navigate', { url, new_tab });

      // Light readiness: avoid heavy DOM snapshots; rely on tabs first
      const started = Date.now();
      const maxWait = 15000;
      const poll = 750;
      let attempts = 0;

      const toText = (val: any): string => {
        if (typeof val === 'string') return val;
        try {
          if (val && Array.isArray(val.content)) {
            const t = val.content.find((i: any) => i?.type === 'text');
            return typeof t?.text === 'string' ? t.text : JSON.stringify(val);
          }
        } catch {}
        try { return JSON.stringify(val); } catch { return String(val); }
      };

      const targetHost = (() => { try { return new URL(url).hostname.replace(/^www\./i, ''); } catch { return undefined; } })();

      const quickHostCheck = async () => {
        try {
          const tabs = await executeBrowserUseTool('browser_list_tabs', {});
          const text = toText(tabs);
          const m = text.match(/\{[^}]*\}/g) || [];
          const candidates = m.map((x) => { try { return JSON.parse(x); } catch { return null; } }).filter(Boolean) as Array<{ url?: string }>;
          const hostOk = candidates.some(c => {
            const h = (() => { try { return c.url ? new URL(c.url).hostname.replace(/^www\./i, '') : undefined; } catch { return undefined; } })();
            return targetHost && h ? (h === targetHost || h.endsWith('.' + targetHost)) : false;
          });
          return hostOk;
        } catch {
          return false;
        }
      };

      // Quick readiness loop
      let hostOk = await quickHostCheck();
      if (!hostOk) {
        // Try lightweight recovery: re-navigate same tab
        while (!hostOk && (Date.now() - started) < maxWait) {
          attempts++;
          try { await executeBrowserUseTool('browser_navigate', { url, new_tab: false }); } catch {}
          await new Promise(r => setTimeout(r, poll));
          hostOk = await quickHostCheck();
        }
      }

      const ready = hostOk; // defer heavy DOM checks to separate get_state calls

      reportProgress('browser_use: navigate complete', 100, { url, new_tab, ready, attempts });

      return JSON.stringify({
        success: ready,
        result,
        message: ready
          ? `Navigated to ${url} (verified by tabs)`
          : `Navigation attempted; target tab not detected within ${Math.round(maxWait/1000)}s`,
        ready
      });
    } catch (error) {
      console.error(`Browser navigate failure for ${url}:`, error);
      reportProgress('browser_use: navigate error', 100, { url, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseOpenAndWaitTool = tool({
  name: 'browser_use_open_and_wait',
  description: `Open a URL and wait until the page is ready with interactive elements. Avoids screenshots unless requested. Performs tab recovery and same-tab retry if needed.`,
  inputSchema: z.object({
    url: z.string().describe('The URL to open'),
    new_tab: z.boolean().optional().default(false).describe('Open in new tab (default false)'),
    include_screenshot: z.boolean().optional().default(false).describe('Include base64 screenshot in final state'),
    max_wait_ms: z.number().optional().default(120000).describe('Max time to wait for readiness'),
    poll_ms: z.number().optional().default(1000).describe('Polling interval during readiness checks'),
    settle_ms: z.number().optional().default(750).describe('Extra settle time after first ready state before returning'),
    require_url_contains: z.string().optional().describe('Optional substring that must be present in final URL'),
    retries: z.number().optional().default(5).describe('Max same-tab re-navigate attempts if still blank'),
    // New: prevent refresh on timeout and use exponential backoff
    no_refresh_on_timeout: z.boolean().optional().default(true).describe('If true, do not re-navigate on readiness timeouts; keep waiting and checking.'),
    backoff_initial_ms: z.number().optional().default(1500).describe('Initial backoff delay between checks when waiting for readiness'),
    backoff_factor: z.number().optional().default(1.5).describe('Exponential backoff factor between readiness checks'),
    backoff_max_ms: z.number().optional().default(10000).describe('Maximum backoff delay between readiness checks')
  }),
  execute: async ({ url, new_tab, include_screenshot, max_wait_ms, poll_ms, settle_ms, require_url_contains, retries, no_refresh_on_timeout, backoff_initial_ms, backoff_factor, backoff_max_ms }) => {
    try {
      reportProgress('browser_use: open_and_wait - navigate', 5, { url, new_tab });
      checkCancellation();

      const toText = (val: any): string => {
        if (typeof val === 'string') return val;
        try {
          if (val && Array.isArray(val.content)) {
            const t = val.content.find((i: any) => i?.type === 'text');
            return typeof t?.text === 'string' ? t.text : JSON.stringify(val);
          }
        } catch {}
        try { return JSON.stringify(val); } catch { return String(val); }
      };

      const parseUrlFromText = (text: string): string | undefined => {
        const m = text.match(/\"url\"\s*:\s*\"([^\"]+)\"/);
        return m?.[1];
      };

      const targetHost = (() => { try { return new URL(url).hostname.replace(/^www\./i, ''); } catch { return undefined; } })();

      const checkReady = async () => {
        try {
          const state = await executeBrowserUseTool('browser_get_state', { include_screenshot: !!include_screenshot });
          const text = toText(state);
          const isAboutBlank = /\"url\"\s*:\s*\"about:blank\"/.test(text) || /Empty Tab/.test(text) || /ignore this tab/i.test(text);
          const hasElements = /interactive_elements\s*\"?\s*:\s*\[/.test(text) || /elements\"?\s*:\s*\[/.test(text);
          const currentUrl = parseUrlFromText(text);
          const hostOk = (() => {
            if (!require_url_contains && !targetHost) return true;
            const h = (() => { try { return currentUrl ? new URL(currentUrl).hostname.replace(/^www\./i, '') : undefined; } catch { return undefined; } })();
            const matchHost = targetHost && h ? (h === targetHost || h.endsWith('.' + targetHost)) : true;
            const includesReq = require_url_contains ? (currentUrl || '').includes(require_url_contains) : true;
            return matchHost && includesReq;
          })();
          return { state, text, isAboutBlank, hasElements, currentUrl, hostOk };
        } catch (e) {
          // Fallback: try a light-weight tab check to infer host readiness
          try {
            const tabs = await executeBrowserUseTool('browser_list_tabs', {});
            const tabsText = toText(tabs);
            const m = tabsText.match(/\{[^}]*\}/g) || [];
            const candidates = m
              .map((s) => { try { return JSON.parse(s); } catch { return null; } })
              .filter(Boolean) as Array<{ url?: string; title?: string }>;
            const currentUrl = (() => {
              const hit = candidates.find(c => (c.url || '').includes(targetHost || '')) || candidates[0];
              return hit?.url;
            })();
            const hostOk = (() => {
              if (!require_url_contains && !targetHost) return true;
              const h = (() => { try { return currentUrl ? new URL(currentUrl).hostname.replace(/^www\./i, '') : undefined; } catch { return undefined; } })();
              const matchHost = targetHost && h ? (h === targetHost || h.endsWith('.' + targetHost)) : true;
              const includesReq = require_url_contains ? (currentUrl || '').includes(require_url_contains) : true;
              return matchHost && includesReq;
            })();
            return { state: undefined, text: tabsText, isAboutBlank: false, hasElements: false, currentUrl, hostOk };
          } catch {
            return { state: undefined, text: 'Error', isAboutBlank: true, hasElements: false, currentUrl: undefined, hostOk: false };
          }
        }
      };

      // Initial navigate
      await executeBrowserUseTool('browser_navigate', { url, new_tab: !!new_tab });

      const started = Date.now();
      let attempts = 0;
      let renavAttempts = 0;
      let s = await checkReady();

      // Try quick tab recovery if blank
      const tryTabRecovery = async () => {
        try {
          const tabs = await executeBrowserUseTool('browser_list_tabs', {});
          const tabsText = toText(tabs);
          const m = tabsText.match(/\{[^}]*\}/g) || [];
          const candidates = m
            .map((x) => { try { return JSON.parse(x); } catch { return null; } })
            .filter(Boolean) as Array<{ tab_id?: string; url?: string; title?: string }>;
          const pick = candidates.find(c => (c.url || '').includes(targetHost || ''))
                    || candidates.find(c => (c.url || '').startsWith('http'))
                    || candidates[0];
          if (pick?.tab_id) {
            try {
              await executeBrowserUseTool('browser_switch_tab', { tab_id: String(pick.tab_id) });
            } catch (e) {
              if (pick.url && /^https?:\/\//i.test(pick.url)) {
                try { await executeBrowserUseTool('browser_navigate', { url: pick.url, new_tab: false }); } catch {}
              }
            }
            await new Promise(r => setTimeout(r, poll_ms || 1000));
            s = await checkReady();
          }
        } catch {}
      };

      // Wait loop with recovery (prefer not to refresh if requested)
      let backoff = Math.max(200, backoff_initial_ms || 1500);
      const factor = Math.max(1.1, backoff_factor || 1.5);
      const maxBackoff = Math.max(backoff, backoff_max_ms || 10000);
      while ((s.isAboutBlank || !s.hasElements || !s.hostOk) && (Date.now() - started) < (max_wait_ms || 60000)) {
        attempts++;
        if (attempts === 1) {
          await tryTabRecovery();
        } else if (!no_refresh_on_timeout && renavAttempts < (retries || 2)) {
          renavAttempts++;
          try {
            await executeBrowserUseTool('browser_navigate', { url, new_tab: false });
          } catch {}
        } else {
          // Exponential backoff wait-and-check without refreshing
          await new Promise(r => setTimeout(r, backoff));
          backoff = Math.min(Math.ceil(backoff * factor), maxBackoff);
        }
        await new Promise(r => setTimeout(r, Math.max(250, poll_ms || 1000)));
        s = await checkReady();
      }

      // Extra settle
      if (!s.isAboutBlank && s.hasElements && s.hostOk && (settle_ms || 0) > 0) {
        await new Promise(r => setTimeout(r, settle_ms));
        s = await checkReady();
      }

      const ready = !s.isAboutBlank && s.hasElements && s.hostOk;

      reportProgress('browser_use: open_and_wait complete', 100, { ready, attempts, renavAttempts });
      return JSON.stringify({
        success: ready,
        state: s.state,
        currentUrl: s.currentUrl,
        message: ready ? 'Page opened and ready' : 'Page did not become ready within timeout',
        attempted: { attempts, renavAttempts },
        ready
      });
    } catch (error) {
      reportProgress('browser_use: open_and_wait error', 100, { url, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseGetStateTool = tool({
  name: 'browser_use_get_state',
  description: `Get current browser state with all interactive elements indexed for interaction. Optionally wait for page readiness.`,
  inputSchema: z.object({
    include_screenshot: z.boolean().optional().default(false).describe('Include base64 screenshot'),
    wait_until_ready: z.boolean().optional().default(false).describe('Wait and retry until page is ready'),
    max_wait_ms: z.number().optional().default(45000).describe('Max time to wait in ms when waiting for readiness'),
    poll_ms: z.number().optional().default(1000).describe('Polling interval in ms when waiting for readiness'),
    // New: avoid refresh on timeout and check with exponential backoff
    no_refresh_on_timeout: z.boolean().optional().default(true).describe('If true, do not reload the page on timeout; keep checking with backoff.'),
    backoff_initial_ms: z.number().optional().default(1500).describe('Initial backoff delay between checks'),
    backoff_factor: z.number().optional().default(1.5).describe('Exponential backoff factor'),
    backoff_max_ms: z.number().optional().default(10000).describe('Maximum backoff delay')
  }),
  execute: async ({ include_screenshot, wait_until_ready, max_wait_ms, poll_ms, no_refresh_on_timeout, backoff_initial_ms, backoff_factor, backoff_max_ms }) => {
    try {
      const started = Date.now();
      const attempt = async () => {
        reportProgress('browser_use: get_state', 10, { include_screenshot });
        checkCancellation();
        const result = await executeBrowserUseTool('browser_get_state', { include_screenshot: !!include_screenshot });

        const toText = (val: any): string => {
          if (typeof val === 'string') return val;
          try {
            if (val && Array.isArray(val.content)) {
              const t = val.content.find((i: any) => i?.type === 'text');
              return typeof t?.text === 'string' ? t.text : JSON.stringify(val);
            }
          } catch {}
          try { return JSON.stringify(val); } catch { return String(val); }
        };

        const text = toText(result);
        const isAboutBlank = /\"url\"\s*:\s*\"about:blank\"/.test(text) || /Empty Tab/.test(text) || /ignore this tab/i.test(text);
        const hasElements = /interactive_elements\s*\"?\s*:\s*\[/.test(text) || /elements\"?\s*:\s*\[/.test(text);

        return { result, text, isAboutBlank, hasElements };
      };

      let out = await attempt();

      if (wait_until_ready) {
        let didTabRecovery = false;
        let backoff = Math.max(200, backoff_initial_ms || 1500);
        const factor = Math.max(1.1, backoff_factor || 1.5);
        const maxBackoff = Math.max(backoff, backoff_max_ms || 10000);

        while ((out.isAboutBlank || !out.hasElements) && Date.now() - started < (max_wait_ms || 20000)) {
          if (!didTabRecovery) {
            // Single tab recovery attempt (non-destructive)
            try {
              const tabs = await executeBrowserUseTool('browser_list_tabs', {});
              const tabsText = typeof tabs === 'string' ? tabs : JSON.stringify(tabs);
              const m = tabsText.match(/\{[^}]*\}/g) || [];
              const candidates = m
                .map((s) => { try { return JSON.parse(s); } catch { return null; } })
                .filter(Boolean) as Array<{ tab_id?: string; url?: string; title?: string }>;
              const pick = candidates.find(c => (c.url || '').startsWith('http') && !(c.title || '').toLowerCase().includes('ignore this tab'))
                         || candidates.find(c => (c.url || '').includes('music.youtube.com'));
              if (pick?.tab_id) {
                try { await executeBrowserUseTool('browser_switch_tab', { tab_id: String(pick.tab_id) }); } catch {}
              }
            } catch {}
            didTabRecovery = true;
          }

          // Exponential backoff wait-and-check without forcing refresh
          await new Promise(r => setTimeout(r, backoff));
          backoff = Math.min(Math.ceil(backoff * factor), maxBackoff);
          out = await attempt();
        }

        // Optional soft reload if still not ready and allowed
        if (!no_refresh_on_timeout && (out.isAboutBlank || !out.hasElements)) {
          try {
            const m = out.text.match(/\"url\"\s*:\s*\"([^\"]+)\"/);
            const currentUrl = m?.[1];
            if (currentUrl && /^https?:\/\//i.test(currentUrl)) {
              await executeBrowserUseTool('browser_navigate', { url: currentUrl, new_tab: false });
              await new Promise(r => setTimeout(r, poll_ms || 1000));
              out = await attempt();
            }
          } catch {}
        }
      }

      reportProgress('browser_use: get_state complete', 100, { waited: wait_until_ready });
      return JSON.stringify({
        success: true,
        state: out.result,
        message: out.isAboutBlank ? 'State retrieved but page appears blank/placeholder' : 'Retrieved browser state with interactive elements',
        waited: !!wait_until_ready,
        ready: !out.isAboutBlank && out.hasElements
      });
    } catch (error) {
      reportProgress('browser_use: get_state error', 100, { error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseClickTool = tool({
  name: 'browser_use_click',
  description: `Click an element by its index from the browser state.`,
  inputSchema: z.object({
    index: z.number().describe('Element index from browser_get_state'),
    new_tab: z.boolean().optional().default(false).describe('Open link in new tab')
  }),
  execute: async ({ index, new_tab }) => {
    try {
      reportProgress('browser_use: click', 10, { index, new_tab });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_click', { index, new_tab });
      reportProgress('browser_use: click complete', 100, { index, new_tab });
      return JSON.stringify({
        success: true,
        result,
        message: `Clicked element at index ${index}`
      });
    } catch (error) {
      reportProgress('browser_use: click error', 100, { index, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseTypeTool = tool({
  name: 'browser_use_type',
  description: `Type text into an input field by its index.`,
  inputSchema: z.object({
    index: z.number().describe('Element index from browser_get_state'),
    text: z.string().describe('Text to type into the field')
  }),
  execute: async ({ index, text }) => {
    try {
      reportProgress('browser_use: type', 10, { index });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_type', { index, text });
      reportProgress('browser_use: type complete', 100, { index });
      return JSON.stringify({
        success: true,
        result,
        message: `Typed text into element at index ${index}`
      });
    } catch (error) {
      reportProgress('browser_use: type error', 100, { index, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseExtractContentTool = tool({
  name: 'browser_use_extract',
  description: `Extract structured content from the page using AI. Requires OPENAI_API_KEY.`,
  inputSchema: z.object({
    query: z.string().describe('What to extract (e.g., "all product prices", "contact information")'),
    extract_links: z.boolean().optional().default(false).describe('Include links in extraction')
  }),
  execute: async ({ query, extract_links }) => {
    try {
      reportProgress('browser_use: extract', 10, { query });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_extract_content', { query, extract_links });
      reportProgress('browser_use: extract complete', 100);
      return JSON.stringify({
        success: true,
        content: result,
        message: 'Content extracted successfully'
      });
    } catch (error) {
      reportProgress('browser_use: extract error', 100, { error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseScrollTool = tool({
  name: 'browser_use_scroll',
  description: `Scroll the page up or down.`,
  inputSchema: z.object({
    direction: z.enum(['up', 'down']).optional().default('down').describe('Scroll direction')
  }),
  execute: async ({ direction }) => {
    try {
      reportProgress('browser_use: scroll', 10, { direction });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_scroll', { direction });
      reportProgress('browser_use: scroll complete', 100, { direction });
      return JSON.stringify({
        success: true,
        result,
        message: `Scrolled ${direction}`
      });
    } catch (error) {
      reportProgress('browser_use: scroll error', 100, { error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseListTabsTool = tool({
  name: 'browser_use_list_tabs',
  description: `List all open browser tabs with their IDs and titles.`,
  inputSchema: z.object({}),
  execute: async () => {
    try {
      reportProgress('browser_use: list_tabs', 10);
      checkCancellation();
      const result = await executeBrowserUseTool('browser_list_tabs', {});
      reportProgress('browser_use: list_tabs complete', 100, { count: Array.isArray(result) ? result.length : undefined });
      return JSON.stringify({
        success: true,
        tabs: result,
        message: 'Retrieved tab list'
      });
    } catch (error) {
      reportProgress('browser_use: list_tabs error', 100, { error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseSwitchTabTool = tool({
  name: 'browser_use_switch_tab',
  description: `Switch to a specific browser tab by its ID.`,
  inputSchema: z.object({
    tab_id: z.union([z.string(), z.number()]).describe('Tab ID as returned by browser_use_list_tabs')
  }),
  execute: async ({ tab_id }) => {
    try {
      reportProgress('browser_use: switch_tab', 10, { tab_id });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_switch_tab', { tab_id });
      reportProgress('browser_use: switch_tab complete', 100, { tab_id });
      return JSON.stringify({
        success: true,
        result,
        message: `Switched to tab ${tab_id}`
      });
    } catch (error) {
      reportProgress('browser_use: switch_tab error', 100, { tab_id, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseCloseTabTool = tool({
  name: 'browser_use_close_tab',
  description: `Close a specific browser tab by its ID.`,
  inputSchema: z.object({
    tab_id: z.string().describe('Tab ID to close (last 4 chars of TargetID)')
  }),
  execute: async ({ tab_id }) => {
    try {
      reportProgress('browser_use: close_tab', 10, { tab_id });
      checkCancellation();
      const result = await executeBrowserUseTool('browser_close_tab', { tab_id });
      reportProgress('browser_use: close_tab complete', 100, { tab_id });
      return JSON.stringify({
        success: true,
        result,
        message: `Closed tab ${tab_id}`
      });
    } catch (error) {
      reportProgress('browser_use: close_tab error', 100, { tab_id, error: error instanceof Error ? error.message : String(error) });
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

export const browserUseGoBackTool = tool({
  name: 'browser_use_go_back',
  description: `Navigate back in browser history.`,
  inputSchema: z.object({}),
  execute: async () => {
    try {
      const result = await executeBrowserUseTool('browser_go_back', {});
      return JSON.stringify({
        success: true,
        result,
        message: 'Navigated back'
      });
    } catch (error) {
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
});

// Export all browser-use tools as a collection
export const browserUseTools = [
  browserUseNavigateTool,
  browserUseOpenAndWaitTool,
  browserUseGetStateTool,
  browserUseClickTool,
  browserUseTypeTool,
  browserUseExtractContentTool,
  browserUseScrollTool,
  browserUseListTabsTool,
  browserUseSwitchTabTool,
  browserUseCloseTabTool,
  browserUseGoBackTool
];

/**
 * Check if browser-use MCP server is available
 */
export async function isBrowserUseAvailable(): Promise<boolean> {
  try {
    const server = await ensureBrowserUseConnected();
    return server !== undefined;
  } catch {
    return false;
  }
}
