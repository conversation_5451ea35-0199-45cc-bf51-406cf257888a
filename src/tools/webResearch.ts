import { tool } from 'ai';
import { z } from 'zod';
import {
  searchBrave,
  aggregateResults,
  fetchWebContent,
  type SearchResult
} from './webSearchHelpers';
import { reportProgress } from '../utils/progress';
import { checkCancellation } from '../utils/cancel';

interface ResearchResult {
  query: string;
  summary: string;
  sources: Array<{
    title: string;
    url: string;
    snippet: string;
    content?: string;
    relevanceScore?: number;
  }>;
  citations: string[];
  timestamp: string;
}

/**
 * Perform comprehensive web research by searching and fetching content
 */
async function performResearch(
  query: string,
  depth: number = 3,
  fetchContent: boolean = true,
  apiKey?: string,
): Promise<ResearchResult> {
  const result: ResearchResult = {
    query,
    summary: '',
    sources: [],
    citations: [],
    timestamp: new Date().toISOString(),
  };

  try {
    // Step 1: Perform web search using multiple engines
    reportProgress('web_research: searching', 5, { query });
    checkCancellation();
    const searchResults = await searchBrave(query, apiKey);
    checkCancellation();
    reportProgress('web_research: aggregating', 20);

    if (searchResults.length === 0) {
      // Return empty result instead of throwing, making it more resilient
      return result;
    }

    const aggregatedResults = aggregateResults([searchResults]);

    // Step 2: Fetch content from top results if requested
    if (fetchContent) {
      reportProgress('web_research: fetching content', 30, { depth });
      const fetchPromises = aggregatedResults
        .slice(0, depth)
        .map(async (searchResult, idx) => {
          try {
            checkCancellation();
            const fetchData = await fetchWebContent(searchResult.url, true);

            if (fetchData.success) {
              const done = idx + 1;
              const pct = 30 + Math.round((done / Math.max(1, Math.min(depth, aggregatedResults.length))) * 50);
              reportProgress('web_research: fetched source', pct, { done, url: searchResult.url });
              return {
                ...searchResult,
                content: fetchData.markdown || fetchData.content,
                fetchedTitle: fetchData.title,
              };
            }
          } catch (error) {
            console.error(`Failed to fetch ${searchResult.url}:`, error);
          }

          return searchResult;
        });

      const enrichedResults = await Promise.all(fetchPromises);
      result.sources = enrichedResults;
    } else {
      result.sources = aggregatedResults.slice(0, depth);
    }

    // Step 3: Generate citations
    reportProgress('web_research: generating citations', 85);
    checkCancellation();
    result.citations = result.sources.map((source, index) => {
      const citation = `[${index + 1}] ${source.title}. ${source.url}`;
      return citation;
    });

    // Step 4: Create summary
    const summaryParts = [
      `Research Results for: "${query}"`,
      `Found ${result.sources.length} relevant sources.`,
      '',
      'Key Findings:',
    ];

    result.sources.forEach((source, index) => {
      summaryParts.push(`${index + 1}. ${source.title}`);
      if (source.snippet) {
        summaryParts.push(`   ${source.snippet.substring(0, 150)}...`);
      }
    });

    checkCancellation();
    result.summary = summaryParts.join('\n');
    reportProgress('web_research: summarizing', 95);

    return result;
  } catch (error) {
    console.error('Research error:', error);
    reportProgress('web_research: error', 100, { error: error instanceof Error ? error.message : String(error) });
    result.summary = `Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    return result;
  }
}

export const webResearchTool = tool({
  name: 'web_research',
  description: 'Perform comprehensive web research by searching multiple sources and fetching detailed content. Returns synthesized information with citations.',
  inputSchema: z.object({
    query: z.string().describe('The research query or topic'),
    depth: z.number().optional().default(3).describe('Number of sources to fetch in depth (1-10)'),
    fetchContent: z.boolean().optional().default(true).describe('Whether to fetch full content from sources'),
    focusAreas: z.array(z.string()).optional().describe('Specific aspects to focus on in the research'),
  }) as any,
  execute: async ({ query, depth, fetchContent, focusAreas }: any): Promise<string> => {
    try {
      // Enhance query with focus areas if provided
      let enhancedQuery = query;
      if (focusAreas && focusAreas.length > 0) {
        enhancedQuery = `${query} ${focusAreas.join(' ')}`;
      }

      // Perform the research
      const research = await performResearch(
        enhancedQuery,
        Math.min(depth || 3, 10),
        fetchContent ?? true,
        process.env.BRAVE_API_KEY,
      );

      // Check for research failure
      if (research.summary.startsWith('Research failed:')) {
        return JSON.stringify({
          success: false,
          error: research.summary,
          query: query,
          timestamp: research.timestamp,
        }, null, 2);
      }

      // Format the response
      const response = {
        success: true,
        query: query,
        enhancedQuery: enhancedQuery,
        summary: research.summary,
        sourcesCount: research.sources.length,
        sources: research.sources.map(source => ({
          title: source.title,
          url: source.url,
          snippet: source.snippet,
          hasFullContent: !!source.content,
          contentPreview: source.content ? source.content.substring(0, 500) + '...' : undefined,
        })),
        citations: research.citations,
        timestamp: research.timestamp,
        note: 'Full content available in source.content field when fetchContent=true',
      };

      return JSON.stringify(response, null, 2);
    } catch (error) {
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Research failed',
        query: query,
        timestamp: new Date().toISOString(),
      }, null, 2);
    }
  },
} as any);
