/**
 * Computer Use Dispatcher - Vercel AI SDK Implementation
 *
 * This tool manages computer use sessions and coordinates task execution
 * across different environments (browser/desktop) with safety controls.
 */

import { tool } from 'ai';
import { z } from 'zod';
import { computerUseTool, computerUseManager, runComputerUseTool } from './computerUse/computerUseTool';

// Task management for computer use operations
interface ComputerUseTask {
  id: string;
  sessionId?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'safety_check' | 'cancelled';
  request: any;
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  cancelRequested?: boolean;
}

class ComputerUseTaskManager {
  private tasks = new Map<string, ComputerUseTask>();
  private runningTasks = new Set<string>();

  createTask(request: any): string {
    const taskId = `cu_task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const task: ComputerUseTask = {
      id: taskId,
      sessionId: request.sessionId,
      status: 'pending',
      request,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.tasks.set(taskId, task);
    console.log(`📝 Created computer use task: ${taskId}`);
    return taskId;
  }

  getTask(taskId: string): ComputerUseTask | undefined {
    return this.tasks.get(taskId);
  }

  updateTask(taskId: string, updates: Partial<ComputerUseTask>): void {
    const task = this.tasks.get(taskId);
    if (task) {
      Object.assign(task, updates);
      task.updatedAt = new Date();
      this.tasks.set(taskId, task);
      console.log(`📝 Updated task ${taskId}: status=${task.status}`);
    }
  }

  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (task && task.status === 'running') {
      task.cancelRequested = true;
      task.status = 'cancelled';
      task.updatedAt = new Date();
      this.runningTasks.delete(taskId);
      console.log(`❌ Cancelled task: ${taskId}`);
      return true;
    }
    return false;
  }

  async executeTask(taskId: string): Promise<any> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    if (task.status !== 'pending') {
      throw new Error(`Task ${taskId} is not in pending status`);
    }

    this.updateTask(taskId, { status: 'running' });
    this.runningTasks.add(taskId);

    try {
      // Check for cancellation before execution
      if (task.cancelRequested) {
        this.updateTask(taskId, { status: 'cancelled' });
        return { success: false, cancelled: true };
      }

      // Execute the computer use task with timeout protection
      const TASK_TIMEOUT_MS = 120000; // 2 minutes timeout for computer use tasks
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Task ${taskId} timed out after ${TASK_TIMEOUT_MS/1000} seconds`)), TASK_TIMEOUT_MS);
      });

      const executionPromise = runComputerUseTool(task.request);
      const result = await Promise.race([executionPromise, timeoutPromise]);
      const parsedResult = JSON.parse(result as string);

      // Check if it requires safety acknowledgment
      if (parsedResult.status === 'safety_check_required') {
        this.updateTask(taskId, {
          status: 'safety_check',
          result: parsedResult
        });
      } else if (parsedResult.success === false || parsedResult.status === 'error') {
        this.updateTask(taskId, {
          status: 'failed',
          error: parsedResult.error || 'Task execution failed',
          result: parsedResult
        });
      } else {
        this.updateTask(taskId, {
          status: 'completed',
          result: parsedResult
        });
      }

      return parsedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.updateTask(taskId, {
        status: 'failed',
        error: errorMessage,
        result: { success: false, error: errorMessage }
      });

      console.error(`❌ Task ${taskId} failed:`, error);
      throw error;
    } finally {
      this.runningTasks.delete(taskId);
    }
  }

  getAllTasks(): ComputerUseTask[] {
    return Array.from(this.tasks.values());
  }

  getTasksBySession(sessionId: string): ComputerUseTask[] {
    return Array.from(this.tasks.values()).filter(task => task.sessionId === sessionId);
  }

  cleanupOldTasks(): void {
    // Clean up tasks older than 1 hour (not 1 day) to prevent memory accumulation
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    let cleaned = 0;

    for (const [taskId, task] of this.tasks.entries()) {
      // Clean up completed, failed, or cancelled tasks after 1 hour
      // Keep running tasks and recent tasks
      if (task.updatedAt < oneHourAgo &&
          !this.runningTasks.has(taskId) &&
          ['completed', 'failed', 'cancelled'].includes(task.status)) {
        // Clear any large result objects to free memory
        if (task.result) {
          delete task.result;
        }
        this.tasks.delete(taskId);
        cleaned++;
      }
    }

    // Also limit total tasks to prevent unbounded growth
    if (this.tasks.size > 100) {
      const sortedTasks = Array.from(this.tasks.entries())
        .sort((a, b) => b[1].updatedAt.getTime() - a[1].updatedAt.getTime());

      // Keep only the 50 most recent tasks
      const toDelete = sortedTasks.slice(50);
      for (const [taskId] of toDelete) {
        if (!this.runningTasks.has(taskId)) {
          this.tasks.delete(taskId);
          cleaned++;
        }
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old computer use tasks`);
    }
  }
}

// Global task manager instance
const taskManager = new ComputerUseTaskManager();

// Cleanup old tasks more frequently to prevent memory accumulation
setInterval(() => {
  taskManager.cleanupOldTasks();
}, 5 * 60 * 1000); // Every 5 minutes instead of every hour

/**
 * Computer Use Dispatcher Tool - Main interface for computer automation
 */
export const computerUseDispatcher = tool({
  name: 'computer_use_dispatcher',
  description: `Advanced computer automation dispatcher that manages sessions and tasks for browser and desktop automation.

  This tool provides:
  - Task creation and management for computer automation
  - Session-based browser and desktop control
  - Safety checks and validation
  - Screenshot capture and visual feedback
  - Multi-step workflow coordination

  Use this for:
  - Web automation (clicking, typing, form filling)
  - Data extraction from websites
  - Testing user interfaces
  - Automated research and information gathering
  - Multi-step workflows requiring visual feedback`,

  inputSchema: z.object({
    operation: z.enum([
      'create_task',
      'get_task_status',
      'execute_task',
      'cancel_task',
      'get_all_tasks',
      'cleanup_session'
    ]).describe('The operation to perform'),

    taskId: z.string().nullable().optional().describe('Task ID for status/execution operations'),

    // Task creation parameters
    action: z.enum(['start_session', 'execute_task', 'acknowledge_safety', 'continue_session', 'close_session']).nullable().optional().describe('Computer use action to perform'),
    sessionId: z.string().nullable().optional().describe('Session ID for continuing operations'),
    environmentType: z.enum(['browser', 'docker']).nullable().optional().describe('Environment type (browser recommended)'),
    displayWidth: z.number().nullable().optional().describe('Display width in pixels'),
    displayHeight: z.number().nullable().optional().describe('Display height in pixels'),
    initialUrl: z.string().nullable().optional().describe('Initial URL for browser sessions'),
    userPrompt: z.string().nullable().optional().describe('Task description for AI execution'),
    acknowledgedSafetyChecks: z.array(z.object({
      id: z.string(),
      code: z.string(),
      message: z.string()
    })).nullable().optional().describe('Safety checks being acknowledged'),
    callId: z.string().nullable().optional().describe('Call ID for continuation')
  }) as any,

  execute: async (params: any, _context?: any) => {
    try {
      const { operation, taskId, ...taskParams } = params;

      console.log(`🚀 Computer use dispatcher called: operation=${operation}, taskId=${taskId}`);

      switch (operation) {
        case 'create_task':
          if (!taskParams.action) {
            return JSON.stringify({
              success: false,
              error: 'action parameter is required for task creation'
            });
          }

          const newTaskId = taskManager.createTask(taskParams);
          return JSON.stringify({
            success: true,
            taskId: newTaskId,
            message: 'Computer use task created successfully'
          });

        case 'get_task_status':
          if (!taskId) {
            return JSON.stringify({
              success: false,
              error: 'taskId is required for status check'
            });
          }

          const task = taskManager.getTask(taskId);
          if (!task) {
            return JSON.stringify({
              success: false,
              error: `Task not found: ${taskId}`
            });
          }

          return JSON.stringify({
            success: true,
            task: {
              id: task.id,
              sessionId: task.sessionId,
              status: task.status,
              result: task.result,
              error: task.error,
              createdAt: task.createdAt.toISOString(),
              updatedAt: task.updatedAt.toISOString()
            }
          });

        case 'execute_task':
          if (!taskId) {
            return JSON.stringify({
              success: false,
              error: 'taskId is required for task execution'
            });
          }

          try {
            const result = await taskManager.executeTask(taskId);
            return JSON.stringify({
              success: true,
              taskId,
              result
            });
          } catch (error) {
            return JSON.stringify({
              success: false,
              taskId,
              error: error instanceof Error ? error.message : String(error)
            });
          }

        case 'cancel_task':
          if (!taskId) {
            return JSON.stringify({
              success: false,
              error: 'taskId is required for task cancellation'
            });
          }

          const cancelled = taskManager.cancelTask(taskId);
          return JSON.stringify({
            success: cancelled,
            taskId,
            message: cancelled ? 'Task cancelled successfully' : 'Task could not be cancelled (not running or not found)'
          });

        case 'get_all_tasks':
          const allTasks = taskManager.getAllTasks().map(task => ({
            id: task.id,
            sessionId: task.sessionId,
            status: task.status,
            createdAt: task.createdAt.toISOString(),
            updatedAt: task.updatedAt.toISOString(),
            hasResult: !!task.result,
            hasError: !!task.error
          }));

          return JSON.stringify({
            success: true,
            tasks: allTasks,
            totalTasks: allTasks.length
          });

        case 'cleanup_session':
          if (!params.sessionId) {
            return JSON.stringify({
              success: false,
              error: 'sessionId is required for cleanup'
            });
          }

          // Close the computer use session
          const closeResult = await runComputerUseTool({
            action: 'close_session',
            sessionId: params.sessionId
          });

          // Get tasks for this session
          const sessionTasks = taskManager.getTasksBySession(params.sessionId);

          return JSON.stringify({
            success: true,
            sessionId: params.sessionId,
            closedSession: JSON.parse(closeResult as string),
            tasksInSession: sessionTasks.length,
            message: `Session ${params.sessionId} cleaned up successfully`
          });

        default:
          return JSON.stringify({
            success: false,
            error: `Unknown operation: ${operation}`
          });
      }
    } catch (error) {
      console.error('❌ Computer use dispatcher error:', error);
      return JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Computer use dispatcher operation failed'
      });
    }
  }
} as any);

// Export the task manager for use in other modules
export { taskManager as computerUseTaskManager };
export type { ComputerUseTask };

// Helper to safely invoke the dispatcher's execute with proper ToolCallOptions
export async function runComputerUseDispatcher(params: any): Promise<any> {
  const exec = (computerUseDispatcher as any)?.execute;
  if (typeof exec !== 'function') {
    throw new Error('computerUseDispatcher.execute is not available');
  }
  return exec(params, { toolCallId: `manual_${Date.now()}`, messages: [] });
}
