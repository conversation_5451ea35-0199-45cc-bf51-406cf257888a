import { tool } from 'ai';
import { z } from 'zod';
import { 
  searchDuckDuckGo, 
  searchSearXNG, 
  searchBrave, 
  searchGoogle,
  aggregateResults,
  type SearchResult 
} from './webSearchHelpers';

interface SearchResponse {
  success: boolean;
  query: string;
  results: SearchResult[];
  error?: string;
  timestamp: string;
}

export const enhancedWebSearchTool = tool({
  name: 'enhanced_web_search',
  description: 'Perform web searches across multiple search engines and return aggregated results with sources',
  inputSchema: z.object({
    query: z.string().describe('The search query'),
    maxResults: z.number().optional().default(10).describe('Maximum number of results to return'),
    searchEngines: z.array(z.enum(['duckduckgo', 'searxng', 'brave', 'google']))
      .nullable()
      .optional()
      .default(['duckduckgo', 'searxng'])
      .describe('Which search engines to use'),
  }) as any,
  execute: async ({ query, maxResults, searchEngines }: any): Promise<string> => {
    const response: SearchResponse = {
      success: false,
      query,
      results: [],
      timestamp: new Date().toISOString(),
    };
    
    try {
      const searchPromises: Promise<SearchResult[]>[] = [];
      const engines = searchEngines || ['duckduckgo', 'searxng'];
      
      // Add selected search engines
      if (engines.includes('duckduckgo')) {
        searchPromises.push(searchDuckDuckGo(query));
      }
      
      if (engines.includes('searxng')) {
        searchPromises.push(searchSearXNG(query));
      }
      
      if (engines.includes('brave')) {
        const braveApiKey = process.env.BRAVE_SEARCH_API_KEY;
        searchPromises.push(searchBrave(query, braveApiKey));
      }
      
      if (engines.includes('google')) {
        const googleApiKey = process.env.GOOGLE_SEARCH_API_KEY;
        const googleCx = process.env.GOOGLE_SEARCH_CX;
        searchPromises.push(searchGoogle(query, googleApiKey, googleCx));
      }
      
      // Execute all searches in parallel
      const allResults = await Promise.all(searchPromises);
      
      // Aggregate and deduplicate results
      const aggregatedResults = aggregateResults(allResults);
      
      // Limit to maxResults
      response.results = aggregatedResults.slice(0, maxResults || 10);
      response.success = response.results.length > 0;
      
      if (response.results.length === 0) {
        response.error = 'No results found from any search engine';
      }
      
      return JSON.stringify(response, null, 2);
    } catch (error) {
      response.error = error instanceof Error ? error.message : 'Unknown error occurred';
      return JSON.stringify(response, null, 2);
    }
  },
} as any);