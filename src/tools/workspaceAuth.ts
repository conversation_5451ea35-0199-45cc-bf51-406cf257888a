/**
 * Workspace Authentication Precheck Tool
 * Lets agents quickly detect if Gmail/Calendar/Drive are connected and surface the authUrl when not.
 */

import { tool } from 'ai';
import { z } from 'zod';
import { connectorService } from '../services/connectorService';
import { ConnectorId } from '../types/connectors';

const serviceToConnector: Record<string, ConnectorId> = {
  gmail: 'connector_gmail',
  calendar: 'connector_googlecalendar',
  drive: 'connector_googledrive',
};

function authUrlForService(service: 'gmail' | 'calendar' | 'drive'): string {
  return `/api/auth/google/${service}`;
}

export const ensureWorkspaceAuthTool = tool({
  name: 'ensure_workspace_auth',
  description: 'Check if Gmail/Calendar/Drive are authenticated; returns authUrl when not connected.',
  inputSchema: z.object({
    service: z.enum(['gmail', 'calendar', 'drive']).describe('Workspace service to check'),
    userId: z.string().optional().describe('Optional user id/email for session scoping'),
  }) as any,
  execute: async ({ service, userId }: { service: 'gmail' | 'calendar' | 'drive'; userId?: string }) => {
    try {
      const connectorId = serviceToConnector[service];
      const resolved = await connectorService.getOAuthTokenForContext(userId, connectorId);
      let session = null;

      if (resolved.user) {
        session = await connectorService.getConnectorSession(resolved.user, connectorId);
      } else {
        const fallbackUser = await connectorService.getDefaultUserForConnector(connectorId);
        if (fallbackUser) {
          session = await connectorService.getConnectorSession(fallbackUser, connectorId);
        }
      }

      if (session?.connected) {
        return JSON.stringify({
          connected: true,
          service,
          connectorId,
          tokenExpiresAt: session.tokenExpiresAt?.toISOString(),
          lastConnected: session.lastConnected?.toISOString(),
        });
      }

      return JSON.stringify({
        connected: false,
        service,
        connectorId,
        authUrl: authUrlForService(service),
        message: `Not authenticated with ${service}. Visit authUrl to connect and retry.`,
      });
    } catch (error: any) {
      return JSON.stringify({
        connected: false,
        service,
        error: error?.message || 'Failed to check authentication',
        authUrl: authUrlForService(service),
      });
    }
  }
} as any);

export const workspaceAuthTools = [ensureWorkspaceAuthTool];
