/**
 * Helper functions for web search functionality
 * These are extracted to be reusable across different tools
 */

import fetch from 'node-fetch';
import * as cheerio from 'cheerio';
import { JSD<PERSON> } from 'jsdom';
import TurndownService from 'turndown';

// Initialize Turndown for HTML to Markdown conversion
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
});

// Configure Turndown to handle various HTML elements better
turndownService.addRule('removeScripts', {
  filter: ['script', 'style', 'noscript'],
  replacement: () => '',
});

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
  publishedDate?: string;
  fullContent?: string;
  chunks?: ContentChunk[];
  chunkCount?: number;
}

export interface ContentChunk {
  id: string;
  title?: string;
  content: string;
  type: 'intro' | 'section' | 'conclusion' | 'misc';
  priority: number; // 1-10, higher is more important
  wordCount: number;
}

export interface FetchOptions {
  enableChunking?: boolean;
  contentDepth?: 'summary' | 'detailed' | 'full';
  chunkSize?: number;
}

export interface FetchResult {
  success: boolean;
  url: string;
  title?: string;
  content?: string;
  markdown?: string;
  error?: string;
  timestamp: string;
  chunks?: ContentChunk[];
  summary?: string;
}

/**
 * Search using DuckDuckGo's instant answer API
 */
export async function searchDuckDuckGo(query: string): Promise<SearchResult[]> {
  try {
    const encodedQuery = encodeURIComponent(query);
    const url = `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1&skip_disambig=1`;

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'DanteAI/1.0',
      },
    });

    if (!response.ok) {
      throw new Error(`DuckDuckGo API error: ${response.status}`);
    }

    const data: any = await response.json();
    const results: SearchResult[] = [];

    // Process instant answer
    if (data.Abstract && data.AbstractURL) {
      results.push({
        title: data.Heading || query,
        url: data.AbstractURL,
        snippet: data.Abstract,
        source: 'DuckDuckGo Instant Answer',
        publishedDate: new Date().toISOString(),
      });
    }

    // Process related topics
    if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
      for (const topic of data.RelatedTopics.slice(0, 3)) {
        if (topic.FirstURL && topic.Text) {
          results.push({
            title: topic.Text.split(' - ')[0] || topic.Text,
            url: topic.FirstURL,
            snippet: topic.Text,
            source: 'DuckDuckGo Related',
          });
        }
      }
    }

    return results;
  } catch (error) {
    console.error('DuckDuckGo search error:', error);
    return [];
  }
}

/**
 * Search using SearXNG public instances
 */
export async function searchSearXNG(query: string): Promise<SearchResult[]> {
  // Note: Many SearXNG instances have rate limits or require specific configurations
  // This is a fallback that returns empty results if no instances work
  const instances = [
    'https://searx.be',
  ];

  for (const instance of instances) {
    try {
      const encodedQuery = encodeURIComponent(query);
      const url = `${instance}/search?q=${encodedQuery}&format=json&engines=google,bing,duckduckgo`;

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'DanteAI/1.0',
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        continue; // Try next instance
      }

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error(`SearXNG instance ${instance} returned non-JSON response`);
        continue; // Try next instance
      }

      const data: any = await response.json();
      const results: SearchResult[] = [];

      if (data.results && Array.isArray(data.results)) {
        for (const result of data.results.slice(0, 5)) {
          results.push({
            title: result.title,
            url: result.url,
            snippet: result.content || '',
            source: `SearXNG (${result.engine || 'mixed'})`,
            publishedDate: result.publishedDate,
          });
        }
      }

      if (results.length > 0) {
        return results;
      }
    } catch (error) {
      console.error(`SearXNG instance ${instance} error:`, error);
      continue; // Try next instance
    }
  }

  return [];
}

/**
 * Search using Brave Search API (requires API key)
 */
export async function searchBrave(query: string, apiKey?: string): Promise<SearchResult[]> {
  if (!apiKey) {
    return [];
  }

  try {
    const encodedQuery = encodeURIComponent(query);
    const url = `https://api.search.brave.com/res/v1/web/search?q=${encodedQuery}&count=5`;

    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'X-Subscription-Token': apiKey,
      },
    });

    if (!response.ok) {
      throw new Error(`Brave API error: ${response.status}`);
    }

    const data: any = await response.json();
    const results: SearchResult[] = [];

    if (data.web && data.web.results) {
      for (const result of data.web.results) {
        results.push({
          title: result.title,
          url: result.url,
          snippet: result.description,
          source: 'Brave Search',
          publishedDate: result.age,
        });
      }
    }

    return results;
  } catch (error) {
    console.error('Brave search error:', error);
    return [];
  }
}

/**
 * Search using Google Custom Search API (requires API key and CX)
 */
export async function searchGoogle(query: string, apiKey?: string, cx?: string): Promise<SearchResult[]> {
  if (!apiKey || !cx) {
    return [];
  }

  try {
    const encodedQuery = encodeURIComponent(query);
    const url = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${cx}&q=${encodedQuery}&num=5`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Google API error: ${response.status}`);
    }

    const data: any = await response.json();
    const results: SearchResult[] = [];

    if (data.items && Array.isArray(data.items)) {
      for (const item of data.items) {
        results.push({
          title: item.title,
          url: item.link,
          snippet: item.snippet,
          source: 'Google Search',
          publishedDate: item.pagemap?.metatags?.[0]?.['article:published_time'],
        });
      }
    }

    return results;
  } catch (error) {
    console.error('Google search error:', error);
    return [];
  }
}

/**
 * Aggregate and deduplicate results from multiple sources
 */
export function aggregateResults(allResults: SearchResult[][]): SearchResult[] {
  const seen = new Set<string>();
  const aggregated: SearchResult[] = [];

  for (const results of allResults) {
    for (const result of results) {
      const key = result.url.toLowerCase();
      if (!seen.has(key)) {
        seen.add(key);
        aggregated.push(result);
      }
    }
  }

  return aggregated;
}

/**
 * Clean and extract text content from HTML
 */
export function extractTextContent(html: string, url: string): { title: string; content: string; markdown: string } {
  try {
    const $ = cheerio.load(html);

    // Remove unwanted elements
    $('script, style, noscript, iframe, nav, header, footer, aside').remove();

    // Extract title
    const title = $('title').text() ||
                  $('h1').first().text() ||
                  $('meta[property="og:title"]').attr('content') ||
                  'Untitled Page';

    // Try to find main content areas with news site optimizations
    let contentHtml = '';
    const contentSelectors = [
      // News-specific selectors
      '.post-content',
      '.entry-content',
      '.article-content',
      '.story-body',
      '.post-body',
      '.article__content',
      // The Verge specific
      '.duet--article--article-body',
      '.c-entry-content',
      // 9to5Google specific
      '.entry-content',
      '.post-content',
      // Android Authority specific
      '.entry-content',
      '.post__content',
      // Generic fallbacks
      'main',
      'article',
      '[role="main"]',
      '#content',
      '.content',
      '#main',
      '.main',
      'body'
    ];

    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        contentHtml = element.html() || '';
        break;
      }
    }

    // If no main content found, get body content
    if (!contentHtml) {
      contentHtml = $('body').html() || html;
    }

    // Convert to markdown
    const markdown = turndownService.turndown(contentHtml);

    // Derive plain text from markdown while preserving paragraph breaks
    const plainFromMarkdown = markdown
      .replace(/^#{1,6}\s+/gm, '')            // strip heading markers
      .replace(/\*\*(.*?)\*\*/g, '$1')        // bold
      .replace(/__(.*?)__/g, '$1')            // underline/emphasis
      .replace(/`{1,3}[\s\S]*?`{1,3}/g, '')   // inline/code fences
      .replace(/\[(.*?)\]\((.*?)\)/g, '$1')   // links -> text
      .replace(/^\s*>\s?/gm, '')              // blockquotes
      .replace(/^\s*[-*+]\s+/gm, '');         // list markers

    const cleanedText = plainFromMarkdown
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    const cleanedMarkdown = markdown
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    return {
      title: title.trim(),
      content: cleanedText.substring(0, 10000), // Limit content length
      markdown: cleanedMarkdown.substring(0, 15000), // Slightly longer for markdown
    };
  } catch (error) {
    console.error('Error extracting content:', error);
    return {
      title: 'Error extracting content',
      content: 'Failed to extract content from the page',
      markdown: 'Failed to extract content from the page',
    };
  }
}

/**
 * Intelligently chunk content based on structure
 */
export function chunkContent(content: string, title: string): ContentChunk[] {
  const chunks: ContentChunk[] = [];
  const paragraphs = content.split('\n\n').filter(p => p.trim().length > 50);

  // Detect intro (first 1-2 paragraphs)
  if (paragraphs.length > 0) {
    const introContent = paragraphs.slice(0, 2).join('\n\n');
    chunks.push({
      id: 'intro',
      title: 'Introduction',
      content: introContent,
      type: 'intro',
      priority: 10,
      wordCount: introContent.split(' ').length
    });
  }

  // Detect conclusion (consider the last non-empty paragraph even if short)
  {
    const rawParagraphs = content.split('\n\n').map(p => p.trim()).filter(p => p.length > 0);
    if (rawParagraphs.length > 0) {
      const lastParagraphRaw = rawParagraphs[rawParagraphs.length - 1];
      const conclusionWords = ['in conclusion', 'conclusion', 'finally', 'in summary', 'overall', 'to summarize'];
      const isConclusion = conclusionWords.some(word =>
        lastParagraphRaw.toLowerCase().includes(word)
      );

      if (isConclusion) {
        chunks.push({
          id: 'conclusion',
          title: 'Conclusion',
          content: lastParagraphRaw,
          type: 'conclusion',
          priority: 9,
          wordCount: lastParagraphRaw.split(' ').length
        });
      }
    }
  }

  // Create sections from remaining content
  const middleParagraphs = paragraphs.slice(2, -1);
  for (let i = 0; i < middleParagraphs.length; i += 3) {
    const sectionContent = middleParagraphs.slice(i, i + 3).join('\n\n');
    chunks.push({
      id: `section-${Math.floor(i / 3) + 1}`,
      title: `Section ${Math.floor(i / 3) + 1}`,
      content: sectionContent,
      type: 'section',
      priority: 5 + Math.max(0, 3 - Math.floor(i / 3)), // Earlier sections get higher priority
      wordCount: sectionContent.split(' ').length
    });
  }

  return chunks.sort((a, b) => b.priority - a.priority);
}

/**
 * Generate a smart summary from chunks
 */
export function generateSummary(chunks: ContentChunk[], maxLength: number = 500): string {
  // Prioritize intro and conclusion, then high-priority sections
  const importantChunks = chunks
    .filter(c => c.type === 'intro' || c.type === 'conclusion' || c.priority >= 7)
    .sort((a, b) => b.priority - a.priority);

  let summary = '';
  for (const chunk of importantChunks) {
    // Extract key sentences instead of just truncating
    const sentences = chunk.content.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const keyContent = sentences.slice(0, 2).join('. ') + (sentences.length > 2 ? '.' : '');

    const addition = keyContent + ' ';
    if (summary.length + addition.length <= maxLength) {
      summary += addition;
    } else {
      break;
    }
  }

  return summary.trim();
}

/**
 * Extract structured content with better article detection
 */
function enhancedContentExtraction(html: string, url: string): { title: string; content: string; structure: any } {
  try {
    const $ = cheerio.load(html);

    // Enhanced title extraction
    const title = $('h1.entry-title, h1.post-title, h1.article-title, h1').first().text() ||
                  $('title').text() ||
                  $('meta[property="og:title"]').attr('content') ||
                  'Article';

    // Article date extraction
    const publishDate = $('time[datetime], .published-date, .post-date, meta[property="article:published_time"]')
      .first().attr('datetime') || $('meta[property="article:published_time"]').attr('content');

    // Article author
    const author = $('.author, .byline, [rel="author"], meta[name="author"]')
      .first().text() || $('meta[name="author"]').attr('content');

    // Remove unwanted elements more aggressively
    $('script, style, noscript, iframe, nav, header, footer, aside, .ad, .ads, .advertisement, .social-share, .comments').remove();

    // Extract structured content
    const structure = {
      title: title.trim(),
      author: author?.trim(),
      publishDate: publishDate,
      headings: [] as string[],
      images: [] as string[]
    };

    // Extract headings for structure
    $('h1, h2, h3, h4').each((_, el) => {
      const heading = $(el).text().trim();
      if (heading && heading.length < 200) {
        structure.headings.push(heading);
      }
    });

    // Extract images with alt text
    $('img').each((_, el) => {
      const src = $(el).attr('src');
      const alt = $(el).attr('alt');
      if (src && alt) {
        structure.images.push(`${alt} (${src})`);
      }
    });

    return { ...extractTextContent(html, url), structure };
  } catch (error) {
    console.error('Enhanced extraction failed:', error);
    return { ...extractTextContent(html, url), structure: {} };
  }
}

/**
 * Fetch content from a URL
 */
export async function fetchWebContent(url: string, extractMarkdown: boolean = true, options?: FetchOptions): Promise<FetchResult> {
  const result: FetchResult = {
    success: false,
    url,
    timestamp: new Date().toISOString(),
  };

  try {
    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 20000); // 20 second timeout for better site coverage

    const response = await fetch(url, {
      signal: controller.signal as any,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; DanteAI/1.0; +https://github.com/dante-ai)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
      },
    });

    clearTimeout(timeout);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type') || '';

    // Handle different content types
    if (contentType.includes('application/json')) {
      const jsonData = await response.json();
      result.success = true;
      result.title = 'JSON Response';
      result.content = JSON.stringify(jsonData, null, 2);
      result.markdown = '```json\n' + JSON.stringify(jsonData, null, 2) + '\n```';
    } else if (contentType.includes('text/html')) {
      const html = await response.text();
      const extracted = extractTextContent(html, url);
      result.success = true;
      result.title = extracted.title;
      result.content = extracted.content;
      if (extractMarkdown) {
        result.markdown = extracted.markdown;
      }

      // Add chunking if enabled
      if (options?.enableChunking && extracted.content) {
        result.chunks = chunkContent(extracted.content, extracted.title);
        result.summary = generateSummary(result.chunks, 500);
      }
    } else if (contentType.includes('text/')) {
      const text = await response.text();
      result.success = true;
      result.title = new URL(url).hostname;
      result.content = text.substring(0, 10000);
      result.markdown = text.substring(0, 10000);
    } else {
      throw new Error(`Unsupported content type: ${contentType}`);
    }

    return result;
  } catch (error) {
    // Preserve original error message for tests and debugging, while adding helpful context
    const message = error instanceof Error ? error.message : 'Unknown error occurred';
    let contextualHint = '';

    if (error instanceof Error) {
      if (error.name === 'AbortError' || /abort/i.test(message) || /timeout/i.test(message)) {
        contextualHint = 'Request may have been aborted or timed out';
      } else if (message.includes('ENOTFOUND')) {
        contextualHint = 'Domain not found - please check the URL';
      } else if (message.includes('ECONNREFUSED')) {
        contextualHint = 'Connection refused - the site may be down';
      }
    }

    result.error = contextualHint ? `${message} - ${contextualHint}` : message;
    return result;
  }
}
