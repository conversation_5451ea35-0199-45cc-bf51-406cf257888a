/**
 * Tool Result Contract (v1)
 * Minimal, strict JSON result shape for tools. Always return a plain object with:
 * - success: boolean
 * - tool: string
 * - version: 'v1'
 * Optional:
 * - meta: Record<string, any>
 * - error: { message, code?, details? } on failure
 * Data payload fields are spread at the top level to minimize churn in tests.
 * The helpers ensure outputs are plain objects and never strings.
 */

export interface ToolError {
  message: string;
  code?: string | number;
  details?: any;
}

export interface BaseToolResult {
  success: boolean;
  tool: string;
  version: 'v1';
  meta?: Record<string, any>;
  error?: ToolError;
}

export type ToolResult<T extends object> = BaseToolResult & T;

const RESERVED_KEYS = new Set(['success', 'tool', 'version', 'error', 'meta']);

/**
 * Runtime guard for plain objects (object literals and objects with null prototype).
 */
export function isPlainObject(value: unknown): value is Record<string, any> {
  if (Object.prototype.toString.call(value) !== '[object Object]') return false;
  const proto = Object.getPrototypeOf(value);
  return proto === Object.prototype || proto === null;
}

/**
 * Create a shallow plain object copy of enumerable own props, excluding reserved keys.
 */
function toShallowPlainObject<T extends object>(value: T): Record<string, any> {
  if (!value) return {};
  const out: Record<string, any> = {};
  for (const key of Object.keys(value as object)) {
    if (RESERVED_KEYS.has(key)) continue;
    out[key] = (value as any)[key];
  }
  return out;
}

/**
 * Construct a success result. Payload fields are hoisted to top-level.
 */
export function makeSuccess<T extends object>(
  tool: string,
  data: T,
  meta?: Record<string, any>
): ToolResult<T> {
  const payload = toShallowPlainObject(data || ({} as T));
  const base: BaseToolResult = { success: true, tool, version: 'v1' };
  const result = Object.assign({}, payload, base) as ToolResult<T>;
  if (meta !== undefined) {
    (result as BaseToolResult).meta = meta;
  }
  return result;
}

/**
 * Construct an error result with optional code/details and optional extra data at top-level.
 */
export function makeError(
  tool: string,
  message: string,
  code?: string | number,
  details?: any,
  data?: Record<string, any>
): ToolResult<{}> {
  const payload = data ? toShallowPlainObject(data) : {};
  const error: ToolError = { message };
  if (code !== undefined) error.code = code;
  if (details !== undefined) error.details = details;
  const base: BaseToolResult = { success: false, tool, version: 'v1', error };
  const result = Object.assign({}, payload, base) as ToolResult<{}>;
  return result;
}
