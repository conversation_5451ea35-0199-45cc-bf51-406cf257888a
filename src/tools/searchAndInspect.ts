import { tool } from 'ai';
import { z } from 'zod';
import { guidedCodeSearchTool } from './guidedCodeSearch';
import { readFilesTool } from './fileOperations';

export const searchAndInspectTool = tool({
  name: 'search_and_inspect',
  description: 'One-shot: guided code search followed by targeted chunk reads. Returns top files with extracted regions (and optional summaries) to minimize context size.',
  inputSchema: z.object({
    keywords: z.union([
      z.array(z.string()).nonempty(),
      z.string().min(1)
    ]).describe('Keywords to search for (or a single regex string)'),
    searchPath: z.string().optional().describe('Base path to search (default: src)'),
    fileTypes: z.array(z.string()).optional().describe('Extensions to include (e.g., ["ts","tsx"])'),
    caseSensitive: z.boolean().optional().describe('Case sensitive search (default: false)'),
    topKFiles: z.number().optional().describe('How many top files to inspect (default: 3)'),
    maxMatchesPerFile: z.number().optional().describe('Max matches per file during search (default: 5)'),
    contextLines: z.number().optional().describe('Snippet context lines (search stage, default: 1)'),
    summarizeChunks: z.boolean().optional().describe('Summarize each extracted chunk via Gemini when available'),
    windowBefore: z.number().optional().describe('If regions missing, lines before match for windows (default: 10)'),
    windowAfter: z.number().optional().describe('If regions missing, lines after match for windows (default: 30)'),
    maxCharsPerChunk: z.number().optional().describe('Cap chunk text size (default: 4000)')
  }),
  execute: async (params: any) => {
    try {
      const {
        keywords,
        searchPath = 'src',
        fileTypes = ['ts','tsx','js','jsx'],
        caseSensitive = false,
        topKFiles = 3,
        maxMatchesPerFile = 5,
        contextLines = 1,
        summarizeChunks = false,
        windowBefore = 10,
        windowAfter = 30,
        maxCharsPerChunk = 4000,
      } = params;

      // 1) Guided search
      const searchRes = await (guidedCodeSearchTool as any).execute({
        keywords,
        searchPath,
        fileTypes,
        caseSensitive,
        topKFiles,
        maxMatchesPerFile,
        contextLines,
      });

      if (!searchRes?.success || !Array.isArray(searchRes.topFiles) || searchRes.topFiles.length === 0) {
        return {
          success: false,
          stage: 'search',
          message: searchRes?.error || 'No matching files found',
          result: searchRes
        };
      }

      // 2) Build regions from search output, or fall back to pattern windows
      const topFiles: Array<{ file: string; regions?: Array<{ startLine: number; endLine: number }> }> = searchRes.topFiles;
      const filePaths = topFiles.map((f: any) => f.file);

      // Extract regions per file, if any
      const regions: Array<{ filePath: string; startLine: number; endLine: number }> = [];
      for (const f of topFiles) {
        if (Array.isArray(f.regions) && f.regions.length > 0) {
          for (const r of f.regions) {
            if (typeof r.startLine === 'number' && typeof r.endLine === 'number') {
              regions.push({ filePath: f.file, startLine: r.startLine, endLine: r.endLine });
            }
          }
        }
      }

      // Build a regex pattern from keywords for the windowing fallback
      const keywordList: string[] = Array.isArray(keywords) ? keywords : [String(keywords)];
      const pattern = keywordList.length === 1 ? keywordList[0] : keywordList.map(k => `(${k})`).join('|');

      // 3) Read targeted chunks
      const readRes = await (readFilesTool as any).execute({
        filePaths,
        regions: regions.length > 0 ? regions : undefined,
        pattern: regions.length > 0 ? undefined : pattern,
        windowBefore,
        windowAfter,
        summarizeChunks,
        maxCharsPerChunk
      });

      return {
        success: true,
        stage: 'search_and_inspect',
        message: `Searched ${searchRes.totalFilesWithMatches} files and inspected ${filePaths.length} top candidates` ,
        search: searchRes,
        inspection: readRes
      };
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'search_and_inspect failed'
      };
    }
  }
});

export const searchInspectionTools = [searchAndInspectTool];

