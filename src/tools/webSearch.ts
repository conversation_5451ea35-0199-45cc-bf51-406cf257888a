import { tool } from 'ai';
import { z } from 'zod';
import {
  searchDuckDuckGo,
  searchSearXNG,
  searchBrave,
  searchGoogle,
  aggregateResults,
  fetchWebContent,
  type SearchResult
} from './webSearchHelpers';
import { searchDuckDuckGoHTML } from './duckduckgoSearch';
import { reportProgress } from '../utils/progress';
import { checkCancellation } from '../utils/cancel';

/**
 * Core web search execution logic - exported for testing
 */
export async function executeWebSearch({ query, maxResults, fetchContent, contentDepth, enableChunking }: any) {
    try {
      reportProgress('web_search: starting', 2, { query });
      checkCancellation();
      // Perform searches across multiple engines
      const searchPromises: Promise<SearchResult[]>[] = [
        searchDuckDuckGoHTML(query), // Use HTML scraping for more reliable results
        searchDuckDuckGo(query), // Fallback to instant answer API
        searchSearXNG(query),
      ];

      // Add optional search engines if API keys are available
      const braveApiKey = process.env.BRAVE_SEARCH_API_KEY;
      if (braveApiKey) {
        searchPromises.push(searchBrave(query, braveApiKey));
      }

      const googleApiKey = process.env.GOOGLE_SEARCH_API_KEY;
      const googleCx = process.env.GOOGLE_SEARCH_CX;
      if (googleApiKey && googleCx) {
        searchPromises.push(searchGoogle(query, googleApiKey, googleCx));
      }

      // Execute all searches in parallel
      reportProgress('web_search: querying engines', 10);
      const allResults = await Promise.all(searchPromises);
      checkCancellation();
      reportProgress('web_search: aggregating results', 25, { engines: searchPromises.length });


      // Aggregate and deduplicate results
      const aggregatedResults = aggregateResults(allResults);
      
      // Limit to maxResults
      let results = aggregatedResults.slice(0, maxResults || 5);
      checkCancellation();
      reportProgress('web_search: results ready', 35, { count: results.length });

      // If fetchContent is requested, fetch the top 5 results for better coverage
      if (fetchContent && results.length > 0) {
        const enrichedResults = await Promise.all(
          results.slice(0, 5).map(async (result, idx) => {
            try {
              checkCancellation();
              const fetchData = await fetchWebContent(result.url, true, {
                enableChunking: enableChunking || false,
                contentDepth: contentDepth || 'summary'
              });

              if (fetchData.success) {
                const done = idx + 1;
                const total = Math.min(results.length, 5);
                const pct = 35 + Math.round((done / total) * 60); // 35->95
                reportProgress('web_search: fetching content', pct, { done, total, url: result.url });
                const contentLimits = {
                  summary: 500,
                  detailed: 2000,
                  full: 10000
                };
                
                const limit = contentLimits[contentDepth as keyof typeof contentLimits] || contentLimits.summary;
                
                const enrichedResult = {
                  ...result,
                  fullContent: fetchData.content?.substring(0, limit) + (fetchData.content && fetchData.content.length > limit ? '...' : ''),
                };

                // Add chunking metadata if enabled
                if (enableChunking && fetchData.chunks) {
                  enrichedResult.chunks = fetchData.chunks;
                  enrichedResult.chunkCount = fetchData.chunks.length;
                }

                return enrichedResult;
              }
            } catch (error) {
              console.error(`Failed to fetch ${result.url}:`, error);
            }
            return result;
          })
        );

        // Replace the first 5 results with enriched versions
        checkCancellation();
        results = [...enrichedResults, ...results.slice(5)];
        reportProgress('web_search: completed fetch', 98, { enriched: Math.min(results.length, 5) });
      }

      reportProgress('web_search: done', 100);
      return JSON.stringify({
        success: results.length > 0,
        results,
        query,
        timestamp: new Date().toISOString(),
        sourcesUsed: results.length > 0 ? [...new Set(results.map(r => r.source))] : [],
      }, null, 2);
    } catch (error) {
      // Fallback response if search fails
      console.error('Web search error:', error);
      reportProgress('web_search: error', 100, { error: error instanceof Error ? error.message : String(error) });

      return JSON.stringify({
        success: false,
        error: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        query,
        results: [],
        timestamp: new Date().toISOString(),
        sourcesUsed: [],
      }, null, 2);
    }
}

/**
 * Main web search tool that uses real search capabilities
 */
export const webSearchTool = tool({
  name: 'web_search',
  description: 'Search the internet for current information, facts, and news. Returns real results from multiple search engines.',
  inputSchema: z.object({
    query: z.string().describe('The search query'),
    maxResults: z.number().optional().default(5).describe('Maximum number of results to return'),
    fetchContent: z.boolean().optional().default(false).describe('Whether to fetch full content from top results'),
    contentDepth: z.enum(['summary', 'detailed', 'full']).optional().default('summary').describe('Level of content detail: summary (500 chars), detailed (2000 chars), full (10000+ chars)'),
    enableChunking: z.boolean().optional().default(false).describe('Enable intelligent content chunking for large articles'),
  }) as any,
  execute: executeWebSearch,
} as any);
