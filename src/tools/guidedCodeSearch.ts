import { tool } from 'ai';
import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';

type Match = { line: number; text: string };
type Region = { startLine: number; endLine: number };
type FileHit = { file: string; totalMatches: number; matches: Match[]; regions: Region[] };

const IGNORED_DIRS = new Set([
  'node_modules','dist','build','coverage','.next','.nuxt','out','public','static','assets','vendor','tmp','temp'
]);

function scoreFile(filePath: string, hitCount: number, keywords: string[]): number {
  const name = path.basename(filePath).toLowerCase();
  const dir = path.dirname(filePath).toLowerCase();
  let score = hitCount * 10;
  for (const k of keywords) {
    const kw = k.toLowerCase();
    if (name.includes(kw)) score += 5;
    if (dir.includes(kw)) score += 3;
  }
  // small boost for TypeScript over JS
  if (name.endsWith('.ts') || name.endsWith('.tsx')) score += 1;
  return score;
}

function computeRegion(lines: string[], centerIndex: number, before = 10, after = 30): Region {
  const n = lines.length;
  let start = Math.max(0, centerIndex - before);
  let end = Math.min(n - 1, centerIndex + after);

  // Expand to blank line boundaries to capture a logical block
  let s = start;
  for (let i = centerIndex; i >= Math.max(0, centerIndex - 60); i--) {
    if (lines[i].trim() === '' && i < centerIndex) { s = i + 1; break; }
    s = Math.max(0, centerIndex - before);
  }
  let e = end;
  for (let i = centerIndex; i <= Math.min(n - 1, centerIndex + 120); i++) {
    if (lines[i].trim() === '' && i > centerIndex) { e = i - 1; break; }
    e = Math.min(n - 1, centerIndex + after);
  }

  // Ensure at least a few lines
  if (e - s < 6) {
    s = Math.max(0, centerIndex - 3);
    e = Math.min(n - 1, centerIndex + 3);
  }

  return { startLine: s + 1, endLine: e + 1 };
}

export const guidedCodeSearchTool = tool({
  name: 'guided_code_search',
  description: 'Search code for keywords and return the top matching files with line-numbered snippets. Use this to avoid repeated generic searches and immediately inspect likely files.',
  inputSchema: z.object({
    keywords: z.array(z.string()).nonempty().describe('Keywords to search for (e.g., ["memory","recall"])').or(z.string().min(1).describe('Regex pattern string')),
    searchPath: z.string().optional().describe('Base path to search (default: src)'),
    fileTypes: z.array(z.string()).optional().describe('Extensions to include (e.g., ["ts","tsx"])'),
    caseSensitive: z.boolean().optional().describe('Case sensitive search (default: false)'),
    topKFiles: z.number().optional().describe('How many top files to return (default: 3)'),
    maxMatchesPerFile: z.number().optional().describe('Max matches per file (default: 5)'),
    contextLines: z.number().optional().describe('Context lines around each match (default: 1)')
  }) as any,
  execute: async (params: any) => {
    try {
      const {
        keywords,
        searchPath = 'src',
        fileTypes = ['ts','tsx','js','jsx'],
        caseSensitive = false,
        topKFiles = 3,
        maxMatchesPerFile = 5,
        contextLines = 1,
      } = params;

      const root = path.resolve(searchPath);
      const keywordList: string[] = Array.isArray(keywords) ? keywords : [String(keywords)];
      const flags = caseSensitive ? 'g' : 'gi';
      const pattern = keywordList.length === 1 ? keywordList[0] : keywordList.map(k => `(${k})`).join('|');
      const regex = new RegExp(pattern, flags);

      const hits: FileHit[] = [];

      const walk = async (dir: string): Promise<void> => {
        let entries;
        try { entries = await fs.readdir(dir, { withFileTypes: true }); } catch { return; }
        for (const entry of entries) {
          if (entry.name.startsWith('.')) continue;
          if (entry.isDirectory()) {
            if (IGNORED_DIRS.has(entry.name)) continue;
            await walk(path.join(dir, entry.name));
          } else {
            const ext = path.extname(entry.name).slice(1);
            if (fileTypes && fileTypes.length > 0 && !fileTypes.includes(ext)) continue;
            const full = path.join(dir, entry.name);
            let content: string;
            try { content = await fs.readFile(full, 'utf-8'); } catch { continue; }
            const lines = content.split('\n');
            const matches: Match[] = [];
            const regions: Region[] = [];
            for (let i = 0; i < lines.length; i++) {
              if (regex.test(lines[i])) {
                const start = Math.max(0, i - contextLines);
                const end = Math.min(lines.length - 1, i + contextLines);
                for (let j = start; j <= end; j++) {
                  matches.push({ line: j + 1, text: lines[j] });
                }
                regions.push(computeRegion(lines, i));
                if (matches.length >= maxMatchesPerFile) break;
              }
            }
            if (matches.length > 0) {
              // Deduplicate overlapping regions (simple merge)
              regions.sort((a,b) => a.startLine - b.startLine);
              const merged: Region[] = [];
              for (const r of regions) {
                const last = merged[merged.length - 1];
                if (!last || r.startLine > last.endLine + 2) {
                  merged.push({ ...r });
                } else {
                  last.endLine = Math.max(last.endLine, r.endLine);
                }
              }
              hits.push({ file: full, totalMatches: matches.length, matches, regions: merged });
            }
          }
        }
      };

      await walk(root);

      // Rank files and take topK
      const ranked = hits
        .map(h => ({ ...h, _score: scoreFile(h.file, h.totalMatches, keywordList) }))
        .sort((a, b) => b._score - a._score)
        .slice(0, Math.max(1, Math.min(topKFiles, 20)))
        .map(({ _score, ...rest }) => rest);

      return {
        success: true,
        query: pattern,
        basePath: root,
        totalFilesWithMatches: hits.length,
        topFiles: ranked,
        recommendation: ranked.length > 0 ? `Inspect the above files first. If inconclusive, broaden keywords or increase topKFiles.` : 'No matches found. Try different keywords or paths.'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'guided_code_search failed'
      };
    }
  }
});

export const codeInspectionTools = [guidedCodeSearchTool];
