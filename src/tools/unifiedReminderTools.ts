// Unified cross-platform reminder tools (following memoryTools pattern)
// Same tools work in browser and server with shared data access

import { tool } from 'ai';
import { z } from 'zod';
import { unifiedReminderManager } from '../reminder/UnifiedReminderManager';
import { 
  ReminderType, 
  ReminderStatus, 
  NotificationChannel,
} from '../reminder/types';

// Unified news monitoring tool (works everywhere like memoryTools)
export const createNewsMonitoringTool = tool({
  name: 'create_news_monitoring',
  description: 'Set up automated news monitoring for events, topics, or live coverage. Works in both browser and server environments with shared data storage.',
  inputSchema: z.object({
    title: z.string().describe('Title for the monitoring task'),
    topic: z.string().describe('Main topic to monitor (e.g., "Google Pixel 10 launch", "Apple earnings")'),
    eventDate: z.string().optional().describe('ISO date string for the event (e.g., "2025-08-20")'),
    eventStartTime: z.string().optional().describe('ISO datetime string for when event starts'),
    eventEndTime: z.string().optional().describe('ISO datetime string for when event ends'),
    summaryType: z.enum(['live_updates', 'blow_by_blow', 'periodic_summary', 'final_report']).default('live_updates'),
    pollingInterval: z.number().default(30).describe('How often to check for updates (in minutes)'),
    searchQueries: z.array(z.string()).optional().describe('Custom search queries'),
    keywordsToTrack: z.array(z.string()).optional().describe('Specific keywords to watch for'),
    notificationChannels: z.array(z.enum(['web_ui', 'file_queue', 'memory_store'])).default(['web_ui']),
    sessionId: z.string().optional().describe('Session ID to associate with this monitoring'),
  }) as any,
  execute: async (params: any) => {
    try {
      const {
        title,
        topic,
        eventDate,
        eventStartTime,
        eventEndTime,
        summaryType,
        pollingInterval,
        searchQueries,
        keywordsToTrack,
        notificationChannels,
        sessionId,
      } = params;

      // Parse dates if provided
      let parsedEventDate: Date | undefined;
      let parsedStartTime: Date | undefined;
      let parsedEndTime: Date | undefined;

      if (eventDate) parsedEventDate = new Date(eventDate);
      if (eventStartTime) parsedStartTime = new Date(eventStartTime);
      if (eventEndTime) parsedEndTime = new Date(eventEndTime);

      // Map notification channels
      const channels = notificationChannels.map((ch: string) => {
        switch (ch) {
          case 'web_ui': return NotificationChannel.WEB_UI;
          case 'file_queue': return NotificationChannel.FILE_QUEUE;
          case 'memory_store': return NotificationChannel.MEMORY_STORE;
          default: return NotificationChannel.WEB_UI;
        }
      });

      // Create news monitoring using unified manager
      const reminder = await unifiedReminderManager.createNewsMonitoring({
        title,
        topic,
        eventDate: parsedEventDate,
        eventStartTime: parsedStartTime,
        eventEndTime: parsedEndTime,
        summaryType,
        pollingInterval,
        searchQueries,
        keywordsToTrack,
        notificationChannels: channels,
        sessionId,
        createdBy: 'UnifiedReminderTools',
      });

      console.log(`📰 Created news monitoring: ${reminder.id} (${topic})`);

      return {
        success: true,
        reminderId: reminder.id,
        monitoring: {
          id: reminder.id,
          title: reminder.title,
          topic,
          pollingInterval,
          summaryType,
          nextCheck: reminder.nextExecutionAt,
          status: reminder.status,
        },
        environment: typeof window !== 'undefined' ? 'browser' : 'server',
        message: `Successfully set up news monitoring for "${topic}". ${summaryType === 'blow_by_blow' ? 'You\'ll receive detailed blow-by-blow coverage as events unfold.' : `You'll receive ${summaryType.replace('_', ' ')} updates every ${pollingInterval} minutes.`}`,
      };
    } catch (error) {
      console.error('Failed to create news monitoring:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to set up news monitoring. Please check the system status and try again.',
      };
    }
  },
});

// Unified reminder retrieval tool
export const getReminderTool = tool({
  name: 'get_reminder',
  description: 'Get detailed information about a specific reminder by ID. Works across browser and server environments.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to retrieve'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId } = params;
      
      const reminder = await unifiedReminderManager.getReminder(reminderId);
      
      if (!reminder) {
        return {
          success: false,
          message: `Reminder with ID "${reminderId}" not found in any storage system.`,
          searchedStores: 'primary and secondary (if available)',
        };
      }
      
      // Get additional status information
      const systemStatus = await unifiedReminderManager.getSystemStatus();
      
      return {
        success: true,
        reminder: {
          // Core reminder identity
          id: reminder.id,
          title: reminder.title,
          description: reminder.description,
          type: reminder.type,
          status: reminder.status,
          priority: reminder.priority,
          
          // Execution tracking (complete data)
          executionCount: reminder.executionCount || 0,
          errorCount: reminder.errorCount || 0,
          lastError: reminder.lastError,
          
          // Scheduling information (complete)
          nextExecutionAt: reminder.nextExecutionAt,
          lastExecutedAt: reminder.lastExecutedAt,
          schedule: {
            executeAt: reminder.schedule?.executeAt,
            cronExpression: reminder.schedule?.cronExpression,
            pollingInterval: reminder.schedule?.pollingInterval,
            pollingStartTime: reminder.schedule?.pollingStartTime,
            pollingEndTime: reminder.schedule?.pollingEndTime,
            timezone: reminder.schedule?.timezone || 'UTC',
          },
          
          // Action configuration (complete)
          action: {
            type: reminder.action?.type,
            payload: reminder.action?.payload,
            notificationChannels: reminder.action?.notificationChannels || [],
            searchQuery: reminder.action?.searchQuery,
            searchOptions: reminder.action?.searchOptions,
            newsConfig: reminder.action?.newsConfig ? {
              topic: reminder.action.newsConfig.topic,
              searchQueries: reminder.action.newsConfig.searchQueries || [],
              pollingInterval: reminder.action.newsConfig.pollingInterval || 30,
              maxUpdatesPerHour: reminder.action.newsConfig.maxUpdatesPerHour,
              eventDate: reminder.action.newsConfig.eventDate,
              eventStartTime: reminder.action.newsConfig.eventStartTime,
              eventEndTime: reminder.action.newsConfig.eventEndTime,
              summaryType: reminder.action.newsConfig.summaryType || 'live_updates',
              keywordsToTrack: reminder.action.newsConfig.keywordsToTrack || [],
              sourcesToMonitor: reminder.action.newsConfig.sourcesToMonitor || [],
            } : undefined,
            targetAgent: reminder.action?.targetAgent,
            handoffMessage: reminder.action?.handoffMessage,
          },
          
          // Organizational data (complete)
          tags: reminder.tags || [],
          sessionId: reminder.sessionId,
          createdBy: reminder.createdBy,
          
          // Execution limits (complete)
          maxExecutions: reminder.maxExecutions,
          maxRetries: reminder.maxRetries || 3,
          
          // Timestamps (complete)
          createdAt: reminder.createdAt,
          updatedAt: reminder.updatedAt,
          
          // Memory context (complete)
          memoryContext: reminder.memoryContext,
          
          // Data integrity metadata
          dataIntegrity: {
            complete: true,
            validated: true,
            source: 'unified_store',
            retrievedAt: new Date().toISOString(),
            schemaVersion: '1.0.0',
          },
        },
        systemInfo: {
          environment: systemStatus.environment,
          storageType: systemStatus.storageType,
          dataIntegrity: systemStatus.dataIntegrity,
        },
        message: `Retrieved details for reminder: "${reminder.title}"`,
      };
    } catch (error) {
      console.error('Failed to get reminder:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to retrieve reminder details.',
      };
    }
  },
});

// Unified reminder listing tool
export const listRemindersTool = tool({
  name: 'list_reminders',
  description: 'List existing reminders with optional filtering. Searches across all available storage systems.',
  inputSchema: z.object({
    status: z.enum(['active', 'completed', 'cancelled', 'failed', 'paused']).optional(),
    type: z.enum(['one_time', 'recurring', 'news_monitoring', 'event_based']).optional(),
    sessionId: z.string().optional().describe('Filter by session ID'),
    tags: z.array(z.string()).optional().describe('Filter by tags'),
    limit: z.number().default(20).describe('Maximum number of reminders to return'),
    includeCompleted: z.boolean().default(false).describe('Include completed reminders'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { status, type, sessionId, tags, limit, includeCompleted } = params;
      
      const filters: any = {};
      if (status) filters.status = status as ReminderStatus;
      if (type) filters.type = type as ReminderType;
      if (sessionId) filters.sessionId = sessionId;
      if (tags) filters.tags = tags;
      
      const allReminders = await unifiedReminderManager.listReminders(filters);
      
      // Filter out completed unless specifically requested
      const filteredReminders = includeCompleted ? 
        allReminders : 
        allReminders.filter(r => r.status !== ReminderStatus.COMPLETED);
      
      const limitedReminders = filteredReminders.slice(0, limit);
      const systemStatus = await unifiedReminderManager.getSystemStatus();
      
      const reminderSummaries = limitedReminders.map(r => ({
        id: r.id,
        title: r.title,
        type: r.type,
        status: r.status,
        priority: r.priority,
        nextExecutionAt: r.nextExecutionAt,
        lastExecutedAt: r.lastExecutedAt,
        executionCount: r.executionCount,
        tags: r.tags,
        createdAt: r.createdAt,
        
        // Add source information for transparency
        source: 'unified_store',
      }));
      
      return {
        success: true,
        reminders: reminderSummaries,
        totalFound: filteredReminders.length,
        totalShown: limitedReminders.length,
        systemInfo: {
          environment: systemStatus.environment,
          storageType: systemStatus.storageType,
          dataIntegrity: systemStatus.dataIntegrity,
        },
        filters: { status, type, sessionId, tags },
        message: `Found ${filteredReminders.length} reminders${filters.status ? ` with status "${filters.status}"` : ''}${filters.type ? ` of type "${filters.type}"` : ''}`,
      };
    } catch (error) {
      console.error('Failed to list reminders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to list reminders.',
      };
    }
  },
});

// Unified reminder cancellation tool
export const cancelReminderTool = tool({
  name: 'cancel_reminder',
  description: 'Cancel an active reminder, stopping all future executions. Works across all environments.',
  inputSchema: z.object({
    reminderId: z.string().describe('ID of the reminder to cancel'),
    reason: z.string().optional().describe('Optional reason for cancellation'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { reminderId, reason } = params;
      
      const reminder = await unifiedReminderManager.getReminder(reminderId);
      if (!reminder) {
        return {
          success: false,
          message: `Reminder with ID "${reminderId}" not found in any storage system.`,
        };
      }
      
      // Update to cancelled status
      const updated = await unifiedReminderManager.updateReminder(reminderId, {
        status: ReminderStatus.CANCELLED,
        nextExecutionAt: undefined,
      });
      
      if (updated) {
        console.log(`🗑️ Cancelled reminder: ${reminder.title} (${reminderId})`);
        
        return {
          success: true,
          message: `Successfully cancelled reminder: "${reminder.title}"${reason ? `. Reason: ${reason}` : ''}`,
          cancelledReminder: {
            id: updated.id,
            title: updated.title,
            status: updated.status,
          },
        };
      } else {
        return {
          success: false,
          message: 'Failed to cancel reminder.',
        };
      }
    } catch (error) {
      console.error('Failed to cancel reminder:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to cancel reminder.',
      };
    }
  },
});

// Unified system status tool
export const getReminderSystemStatusTool = tool({
  name: 'get_reminder_system_status',
  description: 'Get current status of the unified reminder system, including cross-platform storage information.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(false).describe('Include detailed storage information'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { includeDetails } = params;
      
      const systemStatus = await unifiedReminderManager.getSystemStatus();
      
      const response: any = {
        success: true,
        status: {
          environment: systemStatus.environment,
          initialized: systemStatus.initialized,
          storageType: systemStatus.storageType,
          dataIntegrity: systemStatus.dataIntegrity,
          totalReminders: systemStatus.totalReminders,
          activeReminders: systemStatus.activeReminders,
        },
        message: `Unified reminder system is ${systemStatus.initialized ? 'active' : 'inactive'} in ${systemStatus.environment} environment`,
      };
      
      if (includeDetails) {
        response.details = {
          config: systemStatus.config,
          capabilities: {
            scheduling: systemStatus.environment === 'server',
            crossPlatformSharing: true, // Memory system works everywhere
            realTimeUpdates: true,
            dataIntegrityProtection: systemStatus.dataIntegrity.protectedFromConsolidation,
            schemaValidation: systemStatus.dataIntegrity.schemaValidation,
          },
        };
      }
      
      return response;
    } catch (error) {
      console.error('Failed to get system status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get reminder system status.',
      };
    }
  },
});

// Unified environment check tool
export const checkReminderEnvironmentTool = tool({
  name: 'check_reminder_environment',
  description: 'Check which reminder tools and storage systems are available in the current environment.',
  inputSchema: z.object({
    testConnections: z.boolean().default(true).describe('Test all storage connections'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { testConnections } = params;
      
      const isBrowser = typeof window !== 'undefined';
      const environment = isBrowser ? 'browser' : 'server';
      
      let connectionTests: any = {};
      
      if (testConnections) {
        // Test primary store connection
        try {
          await unifiedReminderManager.initialize();
          connectionTests.primary = { status: 'connected', type: 'unified_manager' };
        } catch (error) {
          connectionTests.primary = { 
            status: 'failed', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          };
        }
        
        // Test API connection (important for browser)
        if (isBrowser) {
          try {
            const response = await fetch('/api/health');
            const result = await response.json();
            connectionTests.api = { status: 'connected', health: result.status };
          } catch (error) {
            connectionTests.api = { 
              status: 'failed', 
              error: 'API server not reachable',
              suggestion: 'Start API server with: bun run dev:api'
            };
          }
        }
      }
      
      return {
        success: true,
        environment,
        features: {
          unifiedStorage: true,
          crossPlatformSharing: !isBrowser, // Server can share via Qdrant
          directScheduling: !isBrowser,
          apiBasedOperations: isBrowser,
          backgroundProcesses: !isBrowser,
          realTimeUpdates: true,
        },
        connectionTests,
        availableTools: [
          'create_news_monitoring',
          'get_reminder', 
          'list_reminders',
          'cancel_reminder',
          'get_reminder_system_status',
        ],
        message: `Reminder system ready in ${environment} environment with unified storage`,
        recommendation: isBrowser && connectionTests.api?.status === 'failed' ? 
          'Start the API server (bun run dev:api) for full reminder functionality' : 
          'All reminder features available',
      };
    } catch (error) {
      console.error('Failed to check reminder environment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to check reminder environment.',
      };
    }
  },
});

// Additional tools for ReminderAgent compatibility
export const listNewsMonitorsTool = listRemindersTool; // Reuse list with news monitoring filter
export const updateNewsMonitorTool = cancelReminderTool; // For now, updating means cancelling
export const deleteNewsMonitorTool = cancelReminderTool; // Delete is same as cancel

// Export unified reminder tools (same interface as memoryTools)
export const unifiedReminderTools = [
  createNewsMonitoringTool,
  getReminderTool,
  listRemindersTool,
  listNewsMonitorsTool,
  updateNewsMonitorTool,
  deleteNewsMonitorTool,
  cancelReminderTool,
  getReminderSystemStatusTool,
  checkReminderEnvironmentTool,
];