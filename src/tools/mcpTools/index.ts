/**
 * Pre-configured MCP Tool Registry
 * 
 * This module provides easy-to-use configurations for common MCP servers
 * and tools, making it simple to integrate popular MCP capabilities.
 */

import { MCPServerConfig } from '../../mcp/MCPServerManager';
import {
  createFilesystemMCPConfig,
  createGitMCPConfig,
  createDatabaseMCPConfig,
  createWebSearchMCPConfig
} from '../../mcp/utils';

/**
 * Pre-configured MCP server templates
 */
export const MCPServerTemplates = {
  /**
   * Local filesystem access
   */
  localFileSystem: (rootPath: string = process.cwd(), options: {
    allowWrite?: boolean;
    priority?: number;
    enabled?: boolean;
  } = {}): MCPServerConfig => createFilesystemMCPConfig(
    'filesystem-local',
    'Local Filesystem Access',
    rootPath,
    {
      priority: 100,
      enabled: true,
      tags: ['filesystem', 'local', 'files'],
      allowWrite: false,
      ...options
    }
  ),

  /**
   * Git repository access
   */
  localGitRepo: (repoPath: string = process.cwd(), options: {
    remote?: string;
    priority?: number;
    enabled?: boolean;
  } = {}): MCPServerConfig => createGitMCPConfig(
    'git-local',
    'Local Git Repository',
    repoPath,
    {
      priority: 90,
      enabled: true,
      tags: ['git', 'local', 'version-control'],
      ...options
    }
  ),

  /**
   * Database access via MCP
   */
  database: (connectionString: string, serverUrl: string, options: {
    name?: string;
    priority?: number;
    enabled?: boolean;
    tags?: string[];
    authProvider?: any;
  } = {}): MCPServerConfig => createDatabaseMCPConfig(
    options.name ? `database-${options.name.toLowerCase().replace(/\s+/g, '-')}` : 'database-primary',
    options.name || 'Primary Database',
    connectionString,
    serverUrl,
    {
      priority: 80,
      enabled: true,
      tags: ['database', 'sql', 'data'],
      ...options
    }
  ),

  /**
   * Web search capabilities
   */
  webSearch: (serverUrl: string, options: {
    name?: string;
    apiKey?: string;
    priority?: number;
    enabled?: boolean;
  } = {}): MCPServerConfig => createWebSearchMCPConfig(
    'web-search',
    options.name || 'Web Search Service',
    serverUrl,
    {
      priority: 70,
      enabled: true,
      tags: ['web', 'search', 'research'],
      ...options
    }
  ),

  /**
   * Custom stdio MCP server
   */
  customStdio: (
    id: string,
    name: string,
    command: string,
    options: {
      priority?: number;
      enabled?: boolean;
      tags?: string[];
      cacheToolsList?: boolean;
    } = {}
  ): MCPServerConfig => ({
    id,
    name,
    type: 'stdio',
    config: {
      fullCommand: command,
      cacheToolsList: options.cacheToolsList !== false
    },
    enabled: options.enabled !== false,
    priority: options.priority || 50,
    tags: options.tags || ['custom']
  }),

  /**
   * Custom HTTP MCP server
   */
  customHttp: (
    id: string,
    name: string,
    url: string,
    options: {
      priority?: number;
      enabled?: boolean;
      tags?: string[];
      authProvider?: any;
      cacheToolsList?: boolean;
    } = {}
  ): MCPServerConfig => ({
    id,
    name,
    type: 'streamable_http',
    config: {
      url,
      authProvider: options.authProvider,
      cacheToolsList: options.cacheToolsList !== false
    },
    enabled: options.enabled !== false,
    priority: options.priority || 50,
    tags: options.tags || ['custom']
  })
};

/**
 * Common MCP server collections for specific use cases
 */
export const MCPServerCollections = {
  /**
   * Development workflow servers
   */
  development: (projectPath: string = process.cwd()): MCPServerConfig[] => [
    MCPServerTemplates.localFileSystem(projectPath, { allowWrite: true, priority: 100 }),
    MCPServerTemplates.localGitRepo(projectPath, { priority: 90 })
  ],

  /**
   * Research and analysis servers
   */
  research: (): MCPServerConfig[] => [
    MCPServerTemplates.localFileSystem(process.cwd(), { priority: 80 }),
    // Web search would be added here if URL is configured
    ...(process.env.MCP_WEB_SEARCH_URL ? [
      MCPServerTemplates.webSearch(process.env.MCP_WEB_SEARCH_URL, {
        apiKey: process.env.MCP_WEB_SEARCH_API_KEY,
        priority: 70
      })
    ] : [])
  ],

  /**
   * Data processing servers
   */
  dataProcessing: (): MCPServerConfig[] => [
    MCPServerTemplates.localFileSystem(process.cwd(), { priority: 90 }),
    // Database would be added here if configured
    ...(process.env.MCP_DATABASE_URL && process.env.MCP_DATABASE_SERVER_URL ? [
      MCPServerTemplates.database(
        process.env.MCP_DATABASE_URL,
        process.env.MCP_DATABASE_SERVER_URL,
        { priority: 85 }
      )
    ] : [])
  ],

  /**
   * Full-featured setup with all available servers
   */
  comprehensive: (projectPath: string = process.cwd()): MCPServerConfig[] => [
    MCPServerTemplates.localFileSystem(projectPath, { allowWrite: true, priority: 100 }),
    MCPServerTemplates.localGitRepo(projectPath, { priority: 90 }),
    ...(process.env.MCP_DATABASE_URL && process.env.MCP_DATABASE_SERVER_URL ? [
      MCPServerTemplates.database(
        process.env.MCP_DATABASE_URL,
        process.env.MCP_DATABASE_SERVER_URL,
        { priority: 80 }
      )
    ] : []),
    ...(process.env.MCP_WEB_SEARCH_URL ? [
      MCPServerTemplates.webSearch(process.env.MCP_WEB_SEARCH_URL, {
        apiKey: process.env.MCP_WEB_SEARCH_API_KEY,
        priority: 70
      })
    ] : [])
  ]
};

/**
 * Popular MCP server configurations from the ecosystem
 */
export const PopularMCPServers = {
  /**
   * @modelcontextprotocol/server-filesystem
   */
  filesystemServer: (rootPath: string, allowWrite: boolean = false): MCPServerConfig => 
    MCPServerTemplates.customStdio(
      'mcp-filesystem',
      'MCP Filesystem Server',
      `npx -y @modelcontextprotocol/server-filesystem ${/[\s'"\\$`!*?[\]{}();|&<>]/.test(rootPath) ? `"${rootPath.replace(/"/g, '\\"')}"` : rootPath}`,
      {
        priority: 100,
        tags: ['filesystem', 'files', 'official']
      }
    ),

  /**
   * @modelcontextprotocol/server-git
   */
  gitServer: (repoPath: string): MCPServerConfig =>
    MCPServerTemplates.customStdio(
      'mcp-git',
      'MCP Git Server',
      `npx -y @modelcontextprotocol/server-git ${/[\s'"\\$`!*?[\]{}();|&<>]/.test(repoPath) ? `"${repoPath.replace(/"/g, '\\"')}"` : repoPath}`,
      {
        priority: 90,
        tags: ['git', 'version-control', 'official']
      }
    ),

  /**
   * @modelcontextprotocol/server-sqlite
   */
  sqliteServer: (dbPath: string): MCPServerConfig =>
    MCPServerTemplates.customStdio(
      'mcp-sqlite',
      'MCP SQLite Server',
      `npx -y @modelcontextprotocol/server-sqlite ${/[\s'"\\$`!*?[\]{}();|&<>]/.test(dbPath) ? `"${dbPath.replace(/"/g, '\\"')}"` : dbPath}`,
      {
        priority: 80,
        tags: ['database', 'sqlite', 'official']
      }
    ),

  /**
   * @modelcontextprotocol/server-postgres
   */
  postgresServer: (connectionString: string): MCPServerConfig =>
    MCPServerTemplates.customStdio(
      'mcp-postgres',
      'MCP PostgreSQL Server',
      `npx -y @modelcontextprotocol/server-postgres "${connectionString}"`,
      {
        priority: 80,
        tags: ['database', 'postgresql', 'official']
      }
    ),

  /**
   * Community MCP servers (examples)
   */
  dockerServer: (): MCPServerConfig =>
    MCPServerTemplates.customStdio(
      'mcp-docker',
      'MCP Docker Server',
      'npx -y mcp-server-docker',
      {
        priority: 60,
        tags: ['docker', 'containers', 'community']
      }
    ),

  awsServer: (region: string = 'us-east-1'): MCPServerConfig =>
    MCPServerTemplates.customStdio(
      'mcp-aws',
      'MCP AWS Server',
      `npx -y mcp-server-aws --region ${region}`,
      {
        priority: 60,
        tags: ['aws', 'cloud', 'community']
      }
    )
};

/**
 * Utility functions for managing MCP server collections
 */
export const MCPRegistryUtils = {
  /**
   * Filter servers by tags
   */
  filterByTags: (servers: MCPServerConfig[], tags: string[]): MCPServerConfig[] =>
    servers.filter(server => 
      tags.some(tag => server.tags.includes(tag))
    ),

  /**
   * Sort servers by priority
   */
  sortByPriority: (servers: MCPServerConfig[]): MCPServerConfig[] =>
    [...servers].sort((a, b) => b.priority - a.priority),

  /**
   * Enable/disable servers by pattern
   */
  toggleServers: (servers: MCPServerConfig[], pattern: string | RegExp, enabled: boolean): MCPServerConfig[] =>
    servers.map(server => ({
      ...server,
      enabled: (typeof pattern === 'string' ? 
        server.id.includes(pattern) || server.name.includes(pattern) :
        pattern.test(server.id) || pattern.test(server.name)) ? enabled : server.enabled
    })),

  /**
   * Validate server configurations
   */
  validateConfigs: (servers: MCPServerConfig[]): { valid: MCPServerConfig[]; invalid: { server: MCPServerConfig; errors: string[] }[] } => {
    const valid: MCPServerConfig[] = [];
    const invalid: { server: MCPServerConfig; errors: string[] }[] = [];

    for (const server of servers) {
      const errors: string[] = [];

      if (!server.id) errors.push('Missing server ID');
      if (!server.name) errors.push('Missing server name');
      if (!['stdio', 'streamable_http', 'hosted'].includes(server.type)) {
        errors.push('Invalid server type');
      }

      if (server.type === 'stdio' && !server.config.fullCommand) {
        errors.push('Missing fullCommand for stdio server');
      }
      if (server.type === 'streamable_http' && !server.config.url) {
        errors.push('Missing url for streamable_http server');
      }

      if (errors.length > 0) {
        invalid.push({ server, errors });
      } else {
        valid.push(server);
      }
    }

    return { valid, invalid };
  },

  /**
   * Merge server collections
   */
  mergeCollections: (...collections: MCPServerConfig[][]): MCPServerConfig[] => {
    const merged: MCPServerConfig[] = [];
    const seenIds = new Set<string>();

    for (const collection of collections) {
      for (const server of collection) {
        if (!seenIds.has(server.id)) {
          merged.push(server);
          seenIds.add(server.id);
        }
      }
    }

    return merged;
  }
};

// Export commonly used configurations
export {
  createFilesystemMCPConfig,
  createGitMCPConfig,
  createDatabaseMCPConfig,
  createWebSearchMCPConfig
} from '../../mcp/utils';