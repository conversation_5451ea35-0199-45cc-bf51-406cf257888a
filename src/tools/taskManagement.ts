import { tool } from 'ai';
import { z } from 'zod';

// Since we're in a Node.js environment for agents, we need a way to interface with the UI task store
// We'll create a task management interface that can work across the agent/UI boundary

interface TaskData {
  id: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedAgent?: string;
  parentTaskId?: string;
  subtasks: string[];
  progress: number;
  estimatedTokens?: number;
  actualTokens?: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

// In-memory task store for agent-side operations
// This will sync with the UI task store through events/messaging
class AgentTaskManager {
  private tasks = new Map<string, TaskData>();
  private taskOrder: string[] = [];
  private listeners: ((event: string, data: any) => void)[] = [];

  // Add event listener for task changes
  addListener(callback: (event: string, data: any) => void) {
    this.listeners.push(callback);
  }

  // Emit events to sync with UI
  private emit(event: string, data: any) {
    this.listeners.forEach(callback => callback(event, data));
  }

  createTask(taskData: Omit<TaskData, 'id' | 'createdAt' | 'subtasks' | 'progress'>): string {
    const id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const task: TaskData = {
      ...taskData,
      id,
      createdAt: new Date(),
      subtasks: [],
      progress: 0,
    };

    this.tasks.set(id, task);
    this.taskOrder.push(id);

    this.emit('task_created', { task });

    return id;
  }

  updateTask(id: string, updates: Partial<TaskData>): boolean {
    const task = this.tasks.get(id);
    if (!task) return false;

    const updatedTask = { ...task, ...updates };
    this.tasks.set(id, updatedTask);

    this.emit('task_updated', { task: updatedTask });

    return true;
  }

  startTask(id: string, agentName?: string): boolean {
    const updates: Partial<TaskData> = {
      status: 'in_progress',
      startedAt: new Date(),
    };

    if (agentName) {
      updates.assignedAgent = agentName;
    }

    const success = this.updateTask(id, updates);
    if (success) {
      this.emit('task_started', { taskId: id, agentName });
    }

    return success;
  }

  completeTask(id: string, actualTokens?: number): boolean {
    const updates: Partial<TaskData> = {
      status: 'completed',
      completedAt: new Date(),
      progress: 100,
    };

    if (actualTokens) {
      updates.actualTokens = actualTokens;
    }

    const success = this.updateTask(id, updates);
    if (success) {
      this.emit('task_completed', { taskId: id, actualTokens });
    }

    return success;
  }

  failTask(id: string, error: string): boolean {
    const success = this.updateTask(id, {
      status: 'failed',
      error,
      completedAt: new Date(),
    });

    if (success) {
      this.emit('task_failed', { taskId: id, error });
    }

    return success;
  }

  updateProgress(id: string, progress: number): boolean {
    const normalizedProgress = Math.min(100, Math.max(0, progress));
    const success = this.updateTask(id, { progress: normalizedProgress });

    if (success) {
      this.emit('task_progress', { taskId: id, progress: normalizedProgress });
    }

    return success;
  }

  getTask(id: string): TaskData | undefined {
    return this.tasks.get(id);
  }

  getAllTasks(): TaskData[] {
    return this.taskOrder.map(id => this.tasks.get(id)).filter(Boolean) as TaskData[];
  }

  getTasksByStatus(status: TaskData['status']): TaskData[] {
    return Array.from(this.tasks.values()).filter(task => task.status === status);
  }

  getTasksByAgent(agentName: string): TaskData[] {
    return Array.from(this.tasks.values()).filter(task => task.assignedAgent === agentName);
  }

  createSubtask(parentId: string, subtaskData: Omit<TaskData, 'id' | 'createdAt' | 'subtasks' | 'progress' | 'parentTaskId'>): string {
    const subtaskId = this.createTask({ ...subtaskData, parentTaskId: parentId });

    const parent = this.tasks.get(parentId);
    if (parent) {
      parent.subtasks.push(subtaskId);
      this.tasks.set(parentId, parent);
      this.emit('subtask_created', { parentId, subtaskId });
    }

    return subtaskId;
  }
}

// Global task manager instance
export const agentTaskManager = new AgentTaskManager();

// Tool for creating task trackers
export const createTaskTrackerTool = tool({
  name: 'create_task_tracker',
  description: 'Create a new task tracker to monitor progress of work you are doing yourself (not for delegating to other agents)',
  inputSchema: z.object({
    description: z.string().describe('Clear description of what needs to be done'),
    priority: z.enum(['low', 'medium', 'high', 'critical']).describe('Task priority level'),
    estimatedTokens: z.number().optional().describe('Estimated token usage for this task'),
    assignedAgent: z.string().optional().describe('Agent that should handle this task'),
    metadata: z.record(z.string(), z.string()).optional().describe('Additional task-specific data'),
  }) as any,
  execute: async (params: { description: string; priority: 'low' | 'medium' | 'high' | 'critical'; estimatedTokens?: number | null; assignedAgent?: string | null; metadata?: Record<string, any> | null }) => {
    const { description, priority, estimatedTokens, assignedAgent, metadata } = params;
    try {
      const taskId = agentTaskManager.createTask({
        description,
        priority,
        status: 'pending',
        estimatedTokens: estimatedTokens || undefined,
        assignedAgent: assignedAgent || undefined,
        metadata: metadata || undefined,
      });

      return {
        success: true,
        taskId,
        message: `Task created successfully: ${description}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error creating task',
      };
    }
  },
});

// Tool for updating task status and progress
export const updateTaskTrackerTool = tool({
  name: 'update_task_tracker',
  description: 'Update an existing task tracker with new status, progress, or other data',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to update'),
    status: z.enum(['pending', 'in_progress', 'completed', 'failed', 'cancelled']).optional().describe('New task status'),
    progress: z.number().min(0).max(100).optional().describe('Progress percentage (0-100)'),
    assignedAgent: z.string().optional().describe('Agent assigned to this task'),
    error: z.string().optional().describe('Error message if task failed'),
    actualTokens: z.number().optional().describe('Actual tokens used for completed task'),
    metadata: z.record(z.string(), z.string()).optional().describe('Additional task-specific data'),
  }) as any,
  execute: async (params: { taskId: string; status?: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | null; progress?: number | null; assignedAgent?: string | null; error?: string | null; actualTokens?: number | null; metadata?: Record<string, any> | null }) => {
    const { taskId, status, progress, assignedAgent, error, actualTokens, metadata } = params;
    try {
      const task = agentTaskManager.getTask(taskId);
      if (!task) {
        return {
          success: false,
          error: `Task not found: ${taskId}`,
        };
      }

      const updates: Partial<TaskData> = {};

      if (status !== undefined && status !== null) updates.status = status;
      if (progress !== undefined && progress !== null) updates.progress = progress;
      if (assignedAgent !== undefined && assignedAgent !== null) updates.assignedAgent = assignedAgent;
      if (error !== undefined && error !== null) updates.error = error;
      if (actualTokens !== undefined && actualTokens !== null) updates.actualTokens = actualTokens;
      if (metadata !== undefined) updates.metadata = { ...task.metadata, ...metadata };

      // Handle status-specific updates
      if (status === 'in_progress' && !task.startedAt) {
        updates.startedAt = new Date();
      }
      if ((status === 'completed' || status === 'failed' || status === 'cancelled') && !task.completedAt) {
        updates.completedAt = new Date();
      }
      if (status === 'completed' && progress === undefined) {
        updates.progress = 100;
      }

      const success = agentTaskManager.updateTask(taskId, updates);

      return {
        success,
        message: success ? `Task updated successfully` : `Failed to update task`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error updating task',
      };
    }
  },
});

// Tool for starting a task
export const startTaskTrackerTool = tool({
  name: 'start_task_tracker',
  description: 'Mark a task tracker as in progress and optionally assign it to an agent',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to start'),
    agentName: z.string().optional().describe('Name of the agent starting the task'),
  }) as any,
  execute: async (params: { taskId: string; agentName?: string | null }) => {
    const { taskId, agentName } = params;
    try {
      const success = agentTaskManager.startTask(taskId, agentName || undefined);

      return {
        success,
        message: success
          ? `Task started successfully${agentName ? ` by ${agentName}` : ''}`
          : 'Failed to start task - task not found',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error starting task',
      };
    }
  },
});

// Tool for completing a task
export const completeTaskTrackerTool = tool({
  name: 'complete_task_tracker',
  description: 'Mark a task tracker as completed with optional token usage reporting',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to complete'),
    actualTokens: z.number().optional().describe('Actual tokens used to complete the task'),
    summary: z.string().optional().describe('Summary of what was accomplished'),
  }) as any,
  execute: async (params: { taskId: string; actualTokens?: number | null; summary?: string | null }) => {
    const { taskId, actualTokens, summary } = params;
    try {
      const task = agentTaskManager.getTask(taskId);
      if (!task) {
        return {
          success: false,
          error: `Task not found: ${taskId}`,
        };
      }

      // Update metadata with summary if provided
      if (summary) {
        const updatedMetadata = { ...task.metadata, completionSummary: summary };
        agentTaskManager.updateTask(taskId, { metadata: updatedMetadata });
      }

      const success = agentTaskManager.completeTask(taskId, actualTokens || undefined);

      return {
        success,
        message: success
          ? `Task completed successfully: ${task.description}`
          : 'Failed to complete task',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error completing task',
      };
    }
  },
});

// Tool for creating subtask trackers
export const createSubtaskTrackerTool = tool({
  name: 'create_subtask_tracker',
  description: 'Create a subtask tracker under an existing parent task tracker',
  inputSchema: z.object({
    parentTaskId: z.string().describe('ID of the parent task'),
    description: z.string().describe('Description of the subtask'),
    priority: z.enum(['low', 'medium', 'high', 'critical']).describe('Subtask priority'),
    estimatedTokens: z.number().optional().describe('Estimated tokens for this subtask'),
    assignedAgent: z.string().optional().describe('Agent to assign this subtask to'),
  }) as any,
  execute: async (params: { parentTaskId: string; description: string; priority: 'low' | 'medium' | 'high' | 'critical'; estimatedTokens?: number | null; assignedAgent?: string | null }) => {
    const { parentTaskId, description, priority, estimatedTokens, assignedAgent } = params;
    try {
      const subtaskId = agentTaskManager.createSubtask(parentTaskId, {
        description,
        priority,
        status: 'pending',
        estimatedTokens: estimatedTokens || undefined,
        assignedAgent: assignedAgent || undefined,
      });

      return {
        success: true,
        subtaskId,
        message: `Subtask created: ${description}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error creating subtask',
      };
    }
  },
});

// Tool for querying tasks
export const queryTaskTrackersTool = tool({
  name: 'query_task_trackers',
  description: 'Query task trackers by status, agent, or other criteria',
  inputSchema: z.object({
    status: z.enum(['pending', 'in_progress', 'completed', 'failed', 'cancelled']).optional().describe('Filter by task status'),
    assignedAgent: z.string().optional().describe('Filter by assigned agent'),
    includeCompleted: z.boolean().default(false).describe('Include completed tasks in results'),
  }) as any,
  execute: async (params: { status?: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | null; assignedAgent?: string | null; includeCompleted?: boolean }) => {
    const { status, assignedAgent, includeCompleted } = params;
    try {
      let tasks = agentTaskManager.getAllTasks();

      // Apply filters
      if (status) {
        tasks = tasks.filter(task => task.status === status);
      }

      if (assignedAgent) {
        tasks = tasks.filter(task => task.assignedAgent === assignedAgent);
      }

      if (!includeCompleted) {
        tasks = tasks.filter(task => task.status !== 'completed');
      }

      // Return summary information
      const taskSummaries = tasks.map(task => ({
        id: task.id,
        description: task.description,
        status: task.status,
        priority: task.priority,
        assignedAgent: task.assignedAgent,
        progress: task.progress,
        estimatedTokens: task.estimatedTokens,
        actualTokens: task.actualTokens,
        subtaskCount: task.subtasks.length,
      }));

      return {
        success: true,
        tasks: taskSummaries,
        totalTasks: taskSummaries.length,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error querying tasks',
      };
    }
  },
});

// Export all task tracker management tools
export const taskManagementTools = [
  createTaskTrackerTool,
  updateTaskTrackerTool,
  startTaskTrackerTool,
  completeTaskTrackerTool,
  createSubtaskTrackerTool,
  queryTaskTrackersTool,
];
