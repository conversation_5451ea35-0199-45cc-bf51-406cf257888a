import { tool } from 'ai';
import { z } from 'zod';
import * as fs from 'fs/promises';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { createTwoFilesPatch } from 'diff';
import { makeSuccess, makeError } from './toolResult';
import { diagnoseFile } from '../utils/codeDiagnostics';
import { codeDiagnosticsTools } from './codeDiagnostics';
import { geminiContextService } from '../services/geminiContextService';
import { resolveUploadPublicUrlToPath } from '../api/utils/uploads';
import { countTokens } from '../utils/tokenLimiter';
import { config } from '../utils/config';
import type { UnifiedHunk } from '../utils/unifiedDiff';
import { parseUnifiedDiffText, stripABPrefix, applyUnifiedHunksStrategy, tryGitApplyThreeWay, serializeUnifiedDiffFromMap } from '../utils/unifiedDiff';
import { getContextValue, setContextValue } from '../utils/requestContext';
import { canonicalizePlan, hashPlan } from '../agents/orchestration/journal/canonicalize';
import * as fsSync from 'fs';

const execAsync = promisify(exec);

// 2PC Mutation guard: only file_edit may mutate via our wrappers
const __MUTATION_META = { writes: new Set<string>() as Set<string> };
let __CURRENT_MUTATOR: 'file_edit' | null = null;
export function isMutationPathAllowed(_filePath: string): boolean {
  return __CURRENT_MUTATOR === 'file_edit';
}
function __markWrite(absPath: string) {
  try { __MUTATION_META.writes.add(absPath); } catch {}
}
function __withFileEditMutator<T>(fn: () => Promise<T> | T): Promise<T> | T {
  const prev = __CURRENT_MUTATOR;
  __CURRENT_MUTATOR = 'file_edit';
  try { return fn(); } finally { __CURRENT_MUTATOR = prev; }
}

function resolvePotentialUploadedPath(pth: string): string | null {
  const raw = typeof pth === 'string' ? pth.trim() : '';
  if (!raw) return null;

  const projectRoot = process.cwd();
  const projectName = path.basename(projectRoot);

  // Allow agents to reference real workspace files via /uploads/<projectName>/...
  // (common when they attempt to avoid touching actual repo state).
  const normalizedForMatch = raw.replace(/\\+/g, '/');
  const uploadsPrefix = '/uploads/';
  if (normalizedForMatch.startsWith(uploadsPrefix)) {
    const remainder = normalizedForMatch.slice(uploadsPrefix.length);
    const segments = remainder.split('/').filter(Boolean);
    if (segments.length > 0 && segments[0] === projectName) {
      const restSegments = segments.slice(1);
      const target = restSegments.length > 0
        ? path.resolve(projectRoot, ...restSegments)
        : path.resolve(projectRoot);
      return target;
    }
  }

  try {
    return resolveUploadPublicUrlToPath(raw);
  } catch (err: any) {
    const message = err?.message || '';
    if (message.includes('uploads public base')) {
      return null;
    }
    // Propagate security-related failures such as traversal detection
    throw err;
  }
}

// Resolve paths respecting request-scoped working directory and uploaded file helpers
function resolveWithContext(pth: string): string {
  const normalized = typeof pth === 'string' ? pth : String(pth ?? '');
  const uploaded = resolvePotentialUploadedPath(normalized);
  if (uploaded) return uploaded;
  try {
    const override = getContextValue<string>('cwd');
    if (override && !path.isAbsolute(normalized)) return path.resolve(override, normalized);
  } catch {}
  return path.resolve(normalized);
}

// Build safe regex flags string, ensuring uniqueness and validity.
function combineRegexFlags(base: string | undefined, addGlobal: boolean): string {
  const valid = new Set(['g', 'i', 'm', 's', 'u', 'y']);
  const out = new Set<string>();
  for (const ch of String(base || '')) {
    if (valid.has(ch)) out.add(ch);
  }
  if (addGlobal) out.add('g');
  return Array.from(out).join('');
}

// Allowed roots helpers
function getAllowedRoots(): string[] {
  const cfgRoots = (config as any)?.fileEdit?.allowedRoots || [];
  const envRoots = String(process.env.FILE_EDIT_ALLOWED_ROOTS || '')
    .split(',').map(s => s.trim()).filter(Boolean);
  return [...new Set([...cfgRoots, ...envRoots])].map(p => path.resolve(p));
}
function isUnderAllowedRoots(abs: string, allowedRoots: string[]): boolean {
  if (allowedRoots.length === 0) return true;
  const normalized = path.resolve(abs);
  return allowedRoots.some(root => {
    const rel = path.relative(root, normalized);
    return rel === '' || (!rel.startsWith('..') && !path.isAbsolute(rel));
  });
}

// IO helpers
async function safeWriteFileAtomic(absPath: string, data: string) {
  if (!isMutationPathAllowed(absPath)) {
    throw new Error('mutation_guard_blocked');
  }
  const dir = path.dirname(absPath);
  const base = path.basename(absPath);
  const tmp = path.join(dir, `.${base}.${Date.now()}.${Math.random().toString(36).slice(2)}.tmp`);
  await fs.writeFile(tmp, data, 'utf-8');
  await fs.rename(tmp, absPath);
  __markWrite(absPath);
}
async function writeAtomicOrDirectAbs(absPath: string, data: string, atomic: boolean) {
  if (!isMutationPathAllowed(absPath)) {
    throw new Error('mutation_guard_blocked');
  }
  if (!atomic) {
    await fs.writeFile(absPath, data, 'utf-8');
    __markWrite(absPath);
    return;
  }
  await safeWriteFileAtomic(absPath, data);
}
async function ensureParentDirs(absPath: string, createDirs: boolean) {
  if (createDirs) {
    await fs.mkdir(path.dirname(absPath), { recursive: true });
  }
}
async function readIfExistsAbs(absPath: string): Promise<{ existed: boolean; original: string | undefined }> {
  try {
    const original = await fs.readFile(absPath, 'utf-8');
    return { existed: true, original };
  } catch {
    return { existed: false, original: undefined };
  }
}
async function computeSha256(s: string): Promise<string> {
  const crypto = await import('crypto');
  return crypto.createHash('sha256').update(s).digest('hex');
}
async function runDiagnosticsMaybe(absPath: string, run: boolean): Promise<any | undefined> {
  if (!run) return undefined;
  try { return await diagnoseFile(absPath, { includeFormatting: true }); } catch { return undefined; }
}

// Configuration for automatic Gemini consolidation
const GEMINI_THRESHOLDS = {
  fileSize: config.gemini?.consolidation?.thresholds?.fileSize || 50000, // 50KB
  lineCount: config.gemini?.consolidation?.thresholds?.lineCount || 3000,
  tokenCount: config.gemini?.consolidation?.thresholds?.tokenCount || 20000,
  autoConsolidate: config.gemini?.consolidation?.autoConsolidate !== false // Default true
};

export const readFileTool = tool({
  name: 'read_file',
  description: 'Read the contents of a file (automatically consolidates large files with Gemini when available)',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to read'),
    forceFullContent: z.boolean().optional().describe('Force reading full content without Gemini consolidation'),
    focusAreas: z.array(z.string()).optional().describe('Specific areas to focus on when consolidating')
  }) as any,
  execute: async ({ filePath, forceFullContent = false, focusAreas }: any) => {
    try {
      const absolutePath = resolveWithContext(filePath);
      const stats = await fs.stat(absolutePath);
      const content = await fs.readFile(absolutePath, 'utf-8');
      const lines = content.split('\n');
      const lineCount = lines.length;
      const tokenCount = countTokens(content);
      const CONSOLIDATION_TIMEOUT_MS = 120000; // increased timeout for Gemini consolidation

      const truncateContent = (text: string) => {
        const MAX_CHARS = 8000; // safety cap for fallback
        return text.length > MAX_CHARS ? text.slice(0, MAX_CHARS) : text;
      };

      // Check if we should use Gemini for consolidation
      const shouldConsolidate = !forceFullContent &&
        GEMINI_THRESHOLDS.autoConsolidate &&
        geminiContextService.isAvailable() &&
        (stats.size > GEMINI_THRESHOLDS.fileSize ||
         lineCount > GEMINI_THRESHOLDS.lineCount ||
         tokenCount > GEMINI_THRESHOLDS.tokenCount);

      if (shouldConsolidate) {
        console.log(`📊 Large file detected (${lineCount} lines, ~${tokenCount} tokens). Using Gemini for intelligent consolidation...`);

        try {
          // Timebox consolidation to prevent blocking the stream too long
          const consolidation = await Promise.race([
            geminiContextService.summarizeFile(absolutePath, {
              focusAreas,
              includeLineNumbers: true,
              maxOutputTokens: 3000
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('consolidation_timeout')), CONSOLIDATION_TIMEOUT_MS))
          ]);

          return makeSuccess('read_file', {
            path: absolutePath,
            consolidated: true,
            summary: (consolidation as any).summary,
            structure: (consolidation as any).structure,
            keyElements: (consolidation as any).keyElements,
            lineReferences: (consolidation as any).lineReferences,
            metadata: {
              originalLines: lineCount,
              originalTokens: tokenCount,
              fileSize: stats.size,
              hint: 'Full content available with forceFullContent=true'
            }
          });
        } catch (geminiError: any) {
          console.warn('⚠️ Gemini consolidation failed or timed out, returning truncated content:', geminiError?.message || geminiError);
          // Fall back to truncated content if Gemini fails or times out
          const truncated = truncateContent(content);
          return makeSuccess('read_file', {
            path: absolutePath,
            content: truncated,
            lines: truncated.split('\n').length,
            tokens: countTokens(truncated),
            consolidated: false,
            truncated: true,
            metadata: { originalLines: lineCount, originalTokens: tokenCount, fileSize: stats.size }
          });
        }
      }

      // Return full content (either forced or as fallback)
      return makeSuccess('read_file', {
        path: absolutePath,
        content,
        lines: lineCount,
        tokens: tokenCount,
        consolidated: false
      });
    } catch (error) {
      return makeError('read_file', `Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

// Unified file edit tool (write, replace, anchored patch)
export const fileEditTool = tool({
  name: 'file_edit',
  description: 'Unified file editor: write/create, search/replace, or anchored before→after patch with optional regex and atomic writes.',
  inputSchema: z.object({
    operation: z.enum(['write', 'replace', 'patch']).describe('Operation type'),
    filePath: z.string().optional().describe('Path to the file'),
    dryRun: z.boolean().optional().describe('2PC: when true, plan only and return previewDiff and patchPlanHash'),
    patchPlan: z.union([z.string(), z.any()]).optional().describe('2PC: unified diff string or structured plan (ops)'),

    // Common options
    createDirs: z.boolean().optional().describe('Create parent directories if missing'),
    atomic: z.boolean().optional().describe('Use atomic write (temp file + rename)'),
    ifMatchHash: z.string().optional().describe('Precondition: apply only if current content hash equals this'),
    diagnostics: z.boolean().optional().describe('Run diagnose_file_syntax after write (default: true)'),
    idempotent: z.boolean().optional().describe('Treat already-applied changes as success (modified=false)'),
    mergeStrategy: z.enum(['strict','fuzzy','git_3way','conflict_markers']).optional().describe('Conflict handling for unified diffs (default: strict)'),
    audit: z.object({
      reason: z.string().optional(),
      actor: z.string().optional(),
      tags: z.array(z.string()).optional(),
    }).optional().describe('Optional audit metadata'),

    // write
    content: z.string().optional().describe('Content for write/overwrite/append/prepend'),
    mode: z.enum(['overwrite', 'append', 'prepend']).optional().describe('Write mode (default: overwrite)'),

    // replace
    pattern: z.string().optional().describe('Search pattern (literal or regex depending on useRegex)'),
    replacement: z.string().optional().describe('Replacement text'),
    useRegex: z.boolean().optional().describe('Interpret pattern/before as regex (default: false)'),
    regexFlags: z.string().optional().describe('Regex flags to use when useRegex=true (e.g., i,m,s). g added automatically for scope=all'),
    scope: z.enum(['one', 'all']).optional().describe('Replace first or all occurrences (default: one)'),

    // patch
    patchFormat: z.enum(['anchored', 'unified_diff']).optional().describe('Patch format (default: anchored)'),
    before: z.string().optional().describe('Exact before segment (for anchored patch)'),
    after: z.string().optional().describe('Replacement segment (for anchored patch)'),
    diff: z.string().optional().describe('Unified diff string for patchFormat=unified_diff'),
    fuzzy: z.boolean().optional().describe('Allow best-effort context matching for unified diff'),

    // batch
    ops: z.array(z.object({
      operation: z.enum(['write', 'replace', 'patch']),
      filePath: z.string(),
      createDirs: z.boolean().optional(),
      atomic: z.boolean().optional(),
      ifMatchHash: z.string().optional(),
      diagnostics: z.boolean().optional(),
      idempotent: z.boolean().optional(),
      // write
      content: z.string().optional(),
      mode: z.enum(['overwrite', 'append', 'prepend']).optional(),
      // replace
      pattern: z.string().optional(),
      replacement: z.string().optional(),
      useRegex: z.boolean().optional(),
      regexFlags: z.string().optional(),
      scope: z.enum(['one', 'all']).optional(),
      // patch
      patchFormat: z.enum(['anchored', 'unified_diff']).optional(),
      before: z.string().optional(),
      after: z.string().optional(),
      diff: z.string().optional(),
      fuzzy: z.boolean().optional(),
      audit: z.object({ reason: z.string().optional(), actor: z.string().optional(), tags: z.array(z.string()).optional() }).optional(),
      mergeStrategy: z.enum(['strict','fuzzy','git_3way','conflict_markers']).optional(),
    })).optional().describe('Batch operations'),
    transaction: z.enum(['all_or_nothing', 'best_effort']).optional().describe('Batch mode transaction semantics'),
    gitCommit: z.object({ message: z.string().optional(), addPaths: z.array(z.string()).optional() }).optional().describe('Optional Git commit after successful apply'),
  }) as any,
  execute: async (args: any) => {
    try {
      const {
        operation,
        filePath,
        dryRun,
        patchPlan,
        createDirs = false,
        atomic = false,
        ifMatchHash,
        diagnostics: runDiagnostics = true,
        idempotent = false,
        audit,
        mergeStrategy,
        gitCommit,
        // write
        content,
        mode = 'overwrite',
        // replace
        pattern,
        replacement,
        useRegex = false,
        regexFlags,
        scope = 'one',
        // patch
        patchFormat = 'anchored',
        before,
        after,
        diff,
        fuzzy = false,
        // note: regexFlags also applies to anchored patch
        // batch
        ops,
        transaction = 'best_effort',
      } = args || {};

      // Allowed roots (from config.fileEdit.allowedRoots and env FILE_EDIT_ALLOWED_ROOTS)
      const ALLOWED_ROOTS = getAllowedRoots();

      const absolutePath = filePath != null ? resolveWithContext(String(filePath)) : '';
      // 2PC: if a patchPlan is provided, use the dedicated handler (dry-run or apply)
      if (patchPlan !== undefined) {
        return await handlePatchPlan({
          patchPlan,
          dryRun: !!dryRun,
          allowedRoots: ALLOWED_ROOTS,
          runDiagnostics: !!runDiagnostics,
          mergeStrategy: mergeStrategy as any,
          fuzzy: !!fuzzy,
          audit,
        });
      }

      // Helper: atomic write (temp + rename)
      const writeFileAtomic = async (absPath: string, data: string) => {
        const dir = path.dirname(absPath);
        const base = path.basename(absPath);
        const tmp = path.join(dir, `.${base}.${Date.now()}.${Math.random().toString(36).slice(2)}.tmp`);
        await fs.writeFile(tmp, data, 'utf-8');
        await fs.rename(tmp, absPath);
      };

      // Batch mode (ops provided)
      if (Array.isArray(ops) && ops.length > 0) {
        // Determine all target files across ops (including unified_diff)
        const targets = new Set<string>();
        for (const o of ops) {
          if (o.operation === 'patch' && (o.patchFormat === 'unified_diff' || typeof o.diff === 'string')) {
            const parsed = parseUnifiedDiffText(String(o.diff || ''));
            for (const pf of parsed.files) {
              targets.add(resolveWithContext(stripABPrefix(pf.path)));
            }
          } else if (o.filePath) {
            targets.add(resolveWithContext(String(o.filePath)));
          }
        }
        // Acquire locks in deterministic order to avoid deadlocks
        const targetList = Array.from(targets).sort();
        for (const t of targetList) { if (!isUnderAllowedRoots(t, ALLOWED_ROOTS)) return makeError('file_edit', `Permission denied: ${t} is outside allowed roots`, 'permission_denied', { path: t }); }
        const releases: Array<() => void> = [];
        try {
          for (const t of targetList) releases.push(await getFileMutexMap().lock(t));

          // Maintain file state across ops
          const fileInitial = new Map<string, string>();
          const fileInitiallyExisted = new Map<string, boolean>();
          const fileNext = new Map<string, string>();
          async function getContent(abs: string): Promise<string> {
            if (fileNext.has(abs)) return fileNext.get(abs)!;
            let v = '';
            try {
              v = await fs.readFile(abs, 'utf-8');
              fileInitiallyExisted.set(abs, true);
            } catch {
              v = '';
              fileInitiallyExisted.set(abs, false);
            }
            fileInitial.set(abs, v);
            fileNext.set(abs, v);
            return v;
          }

          const results: any[] = [];
          // Plan phase: compute next contents for all files
          for (let i = 0; i < ops.length; i++) {
            const o = ops[i];
            try {
              if (o.operation === 'write' || o.operation === 'replace' || (o.operation === 'patch' && (o.patchFormat ?? 'anchored') === 'anchored')) {
                const abs = resolveWithContext(String(o.filePath));
                const prev = await getContent(abs);
                const preview = previewSingleEditContent(o, prev);
                if (!preview.ok) throw new Error(preview.reason || 'planning_failed');
                fileNext.set(abs, preview.next);
                results[i] = makeSuccess('file_edit', { path: abs, operation: o.operation, planned: true });
              } else if (o.operation === 'patch' && (o.patchFormat === 'unified_diff' || typeof o.diff === 'string')) {
                const parsed = parseUnifiedDiffText(String(o.diff || ''));
                if (parsed.files.length === 0) throw new Error('empty_unified_diff');
                const strat = (o.mergeStrategy || mergeStrategy || (o.fuzzy ? 'fuzzy' : 'strict')) as 'strict'|'fuzzy'|'conflict_markers';
                for (const pf of parsed.files) {
                  const abs = resolveWithContext(stripABPrefix(pf.path));
                  const prev = await getContent(abs);
                  const applied = applyUnifiedHunksStrategy(prev, pf.hunks, strat);
                  if (!applied.ok) throw new Error(applied.error || 'apply_failed');
                  fileNext.set(abs, applied.next);
                }
                results[i] = makeSuccess('file_edit', { operation: 'patch', patchFormat: 'unified_diff', planned: true });
              } else {
                throw new Error('unsupported_op');
              }
            } catch (e: any) {
              if (transaction === 'all_or_nothing') {
                return makeError('file_edit', `Batch plan failed at op ${i}: ${e?.message || e}`, 'invalid_patch', { index: i });
              }
              results[i] = makeError('file_edit', e?.message || 'planning_failed', 'invalid_patch', { index: i });
            }
          }

          // Apply phase: write all changed files atomically per file
          const changedPaths: string[] = [];
          try {
            await __withFileEditMutator(async () => {
              for (const [abs, next] of fileNext.entries()) {
                const prev = fileInitial.get(abs) ?? '';
                if (prev !== next) {
                  await safeWriteFileAtomic(abs, next);
                  changedPaths.push(abs);
                }
              }
            });
          } catch (e: any) {
            if (transaction === 'all_or_nothing') {
              // Roll back to initial contents
              for (const abs of changedPaths) {
                try {
                  if (fileInitiallyExisted.get(abs)) {
                    await safeWriteFileAtomic(abs, fileInitial.get(abs) ?? '');
                  } else {
                    try { await fs.unlink(abs); } catch {}
                  }
                } catch {}
              }
              return makeError('file_edit', `Batch apply failed: ${e?.message || e}`, 'apply_failed', { error: String(e) });
            }
          }

          // Optional Git commit if requested on batch and at least one success
          try {
            const commitSpec = (args && args.gitCommit) || undefined;
            if (commitSpec && changedPaths.length > 0) {
              await tryGitCommit(changedPaths, commitSpec.message || 'Apply file_edit batch', commitSpec.addPaths);
            }
          } catch {}

          return makeSuccess('file_edit', { batch: true, transaction, results });
        } finally {
          for (const r of releases) { try { r(); } catch {} }
        }
      }

      // per-file helpers moved to top-level

      if (absolutePath && !isUnderAllowedRoots(absolutePath, ALLOWED_ROOTS)) {
        return makeError('file_edit', `Permission denied: ${absolutePath} is outside allowed roots`, 'permission_denied', { path: absolutePath });
      }

      // Delegate unified diff to specialized handler (manages its own locking)
      if (operation === 'patch' && patchFormat === 'unified_diff') {
        return await handlePatchUnified({
          absolutePath,
          diff: String(diff || ''),
          mergeStrategy: mergeStrategy as any,
          fuzzy: !!fuzzy,
          runDiagnostics: !!runDiagnostics,
          audit,
          allowedRoots: ALLOWED_ROOTS,
        });
      }

      // Per-file mutex to serialize concurrent edits
      const __release = await getFileMutexMap().lock(absolutePath);
      try {
        // Process by operation
        if (operation === 'write') {
          return await handleWrite({ absolutePath, createDirs, atomic, ifMatchHash, runDiagnostics, idempotent, audit, mode, content });
        }
        if (operation === 'replace') {
          return await handleReplace({ absolutePath, useRegex, regexFlags, scope, pattern, replacement, ifMatchHash, runDiagnostics, idempotent, audit, atomic });
        }
        if (operation === 'patch') {
          if (patchFormat === 'anchored') {
            return await handlePatchAnchored({ absolutePath, createDirs, useRegex, regexFlags, scope, before, after, idempotent, ifMatchHash, runDiagnostics, audit, atomic });
          }
          // unified_diff patches are handled earlier via handlePatchUnified to avoid deadlocks
        }
        return makeError('file_edit', 'Invalid operation', 'invalid_operation', { path: filePath ? resolveWithContext(filePath) : '', modified: false });
      } finally {
        try { __release(); } catch {}
      }
    } catch (error) {
      return makeError('file_edit', `Failed to edit file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

// --- Concurrency: simple per-file mutex ---
type ReleaseFn = () => void;
class AsyncMutex {
  private queue: Array<() => void> = [];
  private locked = false;
  async lock(): Promise<ReleaseFn> {
    return new Promise<ReleaseFn>(resolve => {
      const attempt = () => {
        if (!this.locked) {
          this.locked = true;
          resolve(() => {
            this.locked = false;
            const next = this.queue.shift();
            if (next) next();
          });
        } else {
          this.queue.push(attempt);
        }
      };
      attempt();
    });
  }
}
const __fileMutexes = new Map<string, AsyncMutex>();
function getFileMutexMap() {
  return {
    async lock(pathKey: string): Promise<ReleaseFn> {
      let m = __fileMutexes.get(pathKey);
      if (!m) { m = new AsyncMutex(); __fileMutexes.set(pathKey, m); }
      return m.lock();
    }
  };
}

// --- Preview single edit helper (no IO) ---
function previewSingleEditContent(op: any, prev: string): { ok: true; next: string } | { ok: false; reason?: string } {
  try {
    if (op.operation === 'write') {
      const mode = op.mode || 'overwrite';
      const content = String(op.content ?? '');
      const next = mode === 'append' ? prev + content : mode === 'prepend' ? content + prev : content;
      return { ok: true, next };
    }
    if (op.operation === 'replace') {
      const useRegex = !!op.useRegex;
      const scope = op.scope || 'one';
      const pattern = String(op.pattern ?? '');
      const replacement = String(op.replacement ?? '');
      let next = prev;
      if (useRegex) {
        if (pattern.length === 0) return { ok: false, reason: 'invalid_pattern' };
        const flags = combineRegexFlags(op.regexFlags, scope === 'all');
        const rx = new RegExp(pattern, flags);
        next = prev.replace(rx, replacement);
      } else {
        if (scope === 'all') next = prev.split(pattern).join(replacement);
        else {
          const idx = pattern.length > 0 ? prev.indexOf(pattern) : -1;
          next = idx >= 0 ? prev.slice(0, idx) + replacement + prev.slice(idx + pattern.length) : prev;
        }
      }
      if (next === prev) return { ok: false, reason: 'pattern_not_found' };
      return { ok: true, next };
    }
    if (op.operation === 'patch' && (op.patchFormat ?? 'anchored') === 'anchored') {
      const before = String(op.before ?? '');
      const after = String(op.after ?? '');
      const useRegex = !!op.useRegex;
      const scope = op.scope || 'one';
      let next = prev;
      if (useRegex) {
        if (before.length === 0) return { ok: false, reason: 'invalid_pattern' };
        const flags = combineRegexFlags(op.regexFlags, scope === 'all');
        const rx = new RegExp(before, flags);
        next = prev.replace(rx, after);
      } else {
        if (scope === 'all') next = prev.split(before).join(after);
        else {
          const idx = before.length > 0 ? prev.indexOf(before) : -1;
          next = idx >= 0 ? prev.slice(0, idx) + after + prev.slice(idx + before.length) : prev;
        }
      }
      if (next === prev) return { ok: false, reason: 'pattern_not_found' };
      return { ok: true, next };
    }
    return { ok: false, reason: 'unsupported_op_for_preview' };
  } catch (e: any) { return { ok: false, reason: String(e?.message || e) }; }
}

// unified diff helpers moved to ../utils/unifiedDiff

// serializeUnifiedDiffFromMap moved to ../utils/unifiedDiff

// applyUnifiedHunks and applyUnifiedHunksStrategy moved to ../utils/unifiedDiff

// --- Operation Handlers ---
async function handleWrite(params: {
  absolutePath: string;
  createDirs: boolean;
  atomic: boolean;
  ifMatchHash?: string;
  runDiagnostics: boolean;
  idempotent: boolean;
  audit?: any;
  mode: 'overwrite'|'append'|'prepend';
  content?: string;
}) {
  const { absolutePath, createDirs, atomic, ifMatchHash, runDiagnostics, audit, mode, content } = params;
  const { existed, original } = await readIfExistsAbs(absolutePath);
  if (!existed) await ensureParentDirs(absolutePath, createDirs);
  const prev = original ?? '';
  let next = prev;
  if (mode === 'append') next = prev + (content ?? '');
  else if (mode === 'prepend') next = (content ?? '') + prev;
  else next = content ?? '';
  const beforeHash = existed ? await computeSha256(prev) : undefined;
  if (ifMatchHash && beforeHash !== ifMatchHash) {
    return makeError('file_edit', 'Conflict: precondition failed (ifMatchHash)', 'conflict', { path: absolutePath, modified: false, conflict: true, expected: ifMatchHash, actual: beforeHash });
  }
  await __withFileEditMutator(async () => {
    await writeAtomicOrDirectAbs(absolutePath, next, atomic);
  });
  const afterHash = await computeSha256(next);
  const diag = await runDiagnosticsMaybe(absolutePath, runDiagnostics);
  const success = makeSuccess('file_edit', {
    path: absolutePath,
    operation: 'write',
    mode,
    bytesWritten: Buffer.byteLength(next, 'utf-8'),
    existed,
    modified: (original ?? '') !== next,
    hashes: { before: beforeHash, after: afterHash },
    diagnostics: diag,
    audit,
  });
  try { recordFileEditAudit('write', { path: absolutePath, actor: audit?.actor, reason: audit?.reason }); } catch {}
  return success;
}

async function handleReplace(params: {
  absolutePath: string;
  useRegex: boolean;
  regexFlags?: string;
  scope: 'one'|'all';
  pattern?: string;
  replacement?: string;
  ifMatchHash?: string;
  runDiagnostics: boolean;
  idempotent: boolean;
  audit?: any;
  atomic: boolean;
}) {
  const { absolutePath, useRegex, regexFlags, scope, pattern, replacement, ifMatchHash, runDiagnostics, idempotent, audit, atomic } = params;
  const { existed, original } = await readIfExistsAbs(absolutePath);
  if (!existed) {
    return makeError('file_edit', 'File not found', 'not_found', { path: absolutePath, modified: false, replacements: 0 });
  }
  const prev = original as string;
  let next = prev;
  let replacements = 0;
  if (useRegex) {
    const patStr = String(pattern ?? '');
    if (patStr.length === 0) {
      return makeError('file_edit', 'Empty regex pattern', 'invalid_pattern', { path: absolutePath, modified: false, replacements: 0 });
    }
    const countRegex = new RegExp(patStr, 'g');
    const matches = prev.match(countRegex);
    replacements = matches ? matches.length : 0;
    const flags = combineRegexFlags(regexFlags, scope === 'all');
    const replaceRegex = new RegExp(patStr, flags);
    next = prev.replace(replaceRegex, String(replacement ?? ''));
    if (scope === 'one' && replacements > 0) replacements = 1;
  } else {
    const needle = String(pattern ?? '');
    if (scope === 'all') {
      if (needle.length > 0) replacements = prev.split(needle).length - 1;
      next = prev.split(needle).join(String(replacement ?? ''));
    } else {
      const idx = needle.length > 0 ? prev.indexOf(needle) : -1;
      replacements = idx >= 0 ? 1 : 0;
      next = idx >= 0 ? prev.slice(0, idx) + String(replacement ?? '') + prev.slice(idx + needle.length) : prev;
    }
  }
  if (next === prev || replacements === 0) {
    if (idempotent && typeof replacement === 'string' && prev.includes(String(replacement))) {
      return makeSuccess('file_edit', { path: absolutePath, operation: 'replace', modified: false, replacements: 0, hashes: { before: undefined, after: undefined }, diagnostics: undefined, audit });
    }
    return makeError('file_edit', 'Pattern not found; no changes applied', 'pattern_not_found', { path: absolutePath, modified: false, replacements: 0 });
  }
  const beforeHash = await computeSha256(prev);
  if (ifMatchHash && beforeHash !== ifMatchHash) {
    return makeError('file_edit', 'Conflict: precondition failed (ifMatchHash)', 'conflict', { path: absolutePath, modified: false, conflict: true, expected: ifMatchHash, actual: beforeHash });
  }
  await __withFileEditMutator(async () => {
    await writeAtomicOrDirectAbs(absolutePath, next, atomic);
  });
  const afterHash = await computeSha256(next);
  const diag = await runDiagnosticsMaybe(absolutePath, runDiagnostics);
  const success = makeSuccess('file_edit', {
    path: absolutePath,
    operation: 'replace',
    modified: true,
    replacements,
    hashes: { before: beforeHash, after: afterHash },
    diagnostics: diag,
    audit,
  });
  try { recordFileEditAudit('replace', { path: absolutePath, actor: audit?.actor, reason: audit?.reason, replacements }); } catch {}
  return success;
}

async function handlePatchAnchored(params: {
  absolutePath: string;
  createDirs: boolean;
  useRegex: boolean;
  regexFlags?: string;
  scope: 'one'|'all';
  before?: string;
  after?: string;
  idempotent: boolean;
  ifMatchHash?: string;
  runDiagnostics: boolean;
  audit?: any;
  atomic: boolean;
}) {
  const { absolutePath, createDirs, useRegex, regexFlags, scope, before, after, idempotent, ifMatchHash, runDiagnostics, audit, atomic } = params;
  const { existed, original } = await readIfExistsAbs(absolutePath);
  if (!existed) {
    if (!createDirs) return makeError('file_edit', 'File not found', 'not_found', { path: absolutePath, modified: false, replacements: 0 });
    await ensureParentDirs(absolutePath, createDirs);
  }
  const prev = original ?? '';
  let next = prev;
  let replacements = 0;
  if (useRegex) {
    const beforeStr = String(before ?? '');
    if (beforeStr.length === 0) {
      return makeError('file_edit', 'Empty regex pattern', 'invalid_pattern', { path: absolutePath, modified: false, replacements: 0 });
    }
    const countRegex = new RegExp(beforeStr, 'g');
    const matches = prev.match(countRegex);
    replacements = matches ? matches.length : 0;
    const flags = combineRegexFlags(regexFlags, scope === 'all');
    const rx = new RegExp(beforeStr, flags);
    next = prev.replace(rx, String(after ?? ''));
    if (scope === 'one' && replacements > 0) replacements = 1;
  } else {
    const needle = String(before ?? '');
    if (scope === 'all') {
      if (needle.length > 0) replacements = prev.split(needle).length - 1;
      next = prev.split(needle).join(String(after ?? ''));
    } else {
      const idx = needle.length > 0 ? prev.indexOf(needle) : -1;
      replacements = idx >= 0 ? 1 : 0;
      next = idx >= 0 ? prev.slice(0, idx) + String(after ?? '') + prev.slice(idx + needle.length) : prev;
    }
  }
  if (next === prev || replacements === 0) {
    if (idempotent && typeof after === 'string' && prev.includes(String(after))) {
      return makeSuccess('file_edit', { path: absolutePath, operation: 'patch', modified: false, replacements: 0, hashes: { before: undefined, after: undefined }, diagnostics: undefined, audit });
    }
    return makeError('file_edit', 'Before segment not found; no changes applied', 'pattern_not_found', { path: absolutePath, modified: false, replacements: 0 });
  }
  const beforeHash = existed ? await computeSha256(prev) : undefined;
  if (ifMatchHash && beforeHash !== ifMatchHash) {
    return makeError('file_edit', 'Conflict: precondition failed (ifMatchHash)', 'conflict', { path: absolutePath, modified: false, conflict: true, expected: ifMatchHash, actual: beforeHash });
  }
  await __withFileEditMutator(async () => {
    await writeAtomicOrDirectAbs(absolutePath, next, atomic);
  });
  const afterHash = await computeSha256(next);
  const diag = await runDiagnosticsMaybe(absolutePath, runDiagnostics);
  const success = makeSuccess('file_edit', {
    path: absolutePath,
    operation: 'patch',
    patchFormat: 'anchored',
    modified: true,
    existed,
    replacements,
    hashes: { before: beforeHash, after: afterHash },
    diagnostics: diag,
    audit,
  });
  try { recordFileEditAudit('patch_anchored', { path: absolutePath, actor: audit?.actor, reason: audit?.reason, replacements }); } catch {}
  return success;
}

async function handlePatchUnified(params: {
  absolutePath: string; // optional restriction ('' means apply to all in diff)
  diff: string;
  mergeStrategy?: 'strict'|'fuzzy'|'git_3way'|'conflict_markers';
  fuzzy: boolean;
  runDiagnostics: boolean;
  audit?: any;
  allowedRoots: string[];
}) {
  const { absolutePath, diff, mergeStrategy, fuzzy, runDiagnostics, audit, allowedRoots } = params;
  if (!diff || typeof diff !== 'string' || diff.trim().length === 0) {
    return makeError('file_edit', 'Missing unified diff string', 'invalid_patch', { path: absolutePath });
  }
  const parsed = parseUnifiedDiffText(diff);
  if (parsed.files.length === 0) {
    return makeError('file_edit', 'Unified diff contains no file hunks', 'invalid_patch', { path: absolutePath });
  }
  const perFile = new Map<string, { hunks: UnifiedHunk[] }>();
  for (const pf of parsed.files) {
    const resolved = resolveWithContext(stripABPrefix(pf.path));
    if (!isUnderAllowedRoots(resolved, allowedRoots)) {
      return makeError('file_edit', `Permission denied: ${resolved} is outside allowed roots`, 'permission_denied', { path: resolved });
    }
    if (absolutePath && absolutePath.length > 0 && resolved !== absolutePath) continue;
    perFile.set(resolved, { hunks: pf.hunks });
  }
  if (perFile.size === 0) {
    return makeError('file_edit', 'No matching hunks for provided filePath', 'invalid_patch', { path: absolutePath });
  }
  const results: any[] = [];
  const strategy = (mergeStrategy ? mergeStrategy : (fuzzy ? 'fuzzy' : 'strict')) as 'strict'|'fuzzy'|'git_3way'|'conflict_markers';
  const multiReleases: Array<() => void> = [];
  const targetPaths = Array.from(perFile.keys()).sort();
  try {
    for (const p of targetPaths) multiReleases.push(await getFileMutexMap().lock(p));
    const originals = new Map<string, string>();
    for (const p of targetPaths) {
      try { originals.set(p, await fs.readFile(p, 'utf-8')); }
      catch { return makeError('file_edit', `Target file does not exist: ${p}`, 'not_found', { path: p }); }
    }
    if (strategy === 'git_3way') {
      const filteredDiff = serializeUnifiedDiffFromMap(perFile);
      const ok = await tryGitApplyThreeWay(filteredDiff);
      if (!ok) return makeError('file_edit', 'git_3way apply failed', 'invalid_patch', { path: targetPaths[0] });
      for (const abs of targetPaths) {
        const original = originals.get(abs) || '';
        const next = await fs.readFile(abs, 'utf-8');
        const beforeHash = await computeSha256(original);
        const afterHash = await computeSha256(next);
        const diag = await runDiagnosticsMaybe(abs, runDiagnostics);
        const res = makeSuccess('file_edit', { path: abs, operation: 'patch', patchFormat: 'unified_diff', modified: original !== next, hashes: { before: beforeHash, after: afterHash }, diagnostics: diag, audit });
        try { recordFileEditAudit('patch_unified', { path: abs, actor: audit?.actor, reason: audit?.reason }); } catch {}
        results.push(res);
      }
    } else {
      for (const abs of targetPaths) {
        const { hunks } = perFile.get(abs)!;
        const original = originals.get(abs) || '';
        const applied = applyUnifiedHunksStrategy(original, hunks, strategy);
        if (!applied.ok) return makeError('file_edit', `Failed to apply unified diff: ${applied.error}`, 'invalid_patch', { path: abs });
        await safeWriteFileAtomic(abs, applied.next);
        const beforeHash = await computeSha256(original);
        const afterHash = await computeSha256(applied.next);
        const diag = await runDiagnosticsMaybe(abs, runDiagnostics);
        const res = makeSuccess('file_edit', { path: abs, operation: 'patch', patchFormat: 'unified_diff', modified: original !== applied.next, hashes: { before: beforeHash, after: afterHash }, diagnostics: diag, audit, conflicts: (applied as any).conflicts === true });
        try { recordFileEditAudit('patch_unified', { path: abs, actor: audit?.actor, reason: audit?.reason }); } catch {}
        results.push(res);
      }
    }
  } finally {
    for (const r of multiReleases) { try { r(); } catch {} }
  }
  if (results.length === 1) return results[0];
  return makeSuccess('file_edit', { batch: true, results });
}

// 2PC: Plan handler (dry-run/apply) with idempotent hashing and unified preview diff
async function handlePatchPlan(params: {
  patchPlan: any;
  dryRun: boolean;
  allowedRoots: string[];
  runDiagnostics: boolean;
  mergeStrategy?: 'strict'|'fuzzy'|'git_3way'|'conflict_markers';
  fuzzy: boolean;
  audit?: any;
}) {
  const { patchPlan, dryRun, allowedRoots, runDiagnostics, mergeStrategy, fuzzy, audit } = params;
  const kind = normalizePlanInput(patchPlan);

  const normalizedPatch = canonicalizePlan(patchPlan);
  const patchPlanHash = hashPlan(patchPlan);

  const fileInitial = new Map<string, string>();
  const fileNext = new Map<string, string>();

  try {
    if (kind.kind === 'diff') {
      const parsed = parseUnifiedDiffText(String(kind.diff || ''));
      if (parsed.files.length === 0) {
        return makeError('file_edit', 'Unified diff contains no file hunks', 'invalid_patch');
      }
      // compute next content for each file without writing
      for (const pf of parsed.files) {
        const abs = resolveWithContext(stripABPrefix(pf.path));
        if (!isUnderAllowedRoots(abs, allowedRoots)) {
          return makeError('file_edit', `Permission denied: ${abs} is outside allowed roots`, 'permission_denied', { path: abs });
        }
        let prev = '';
        try { prev = await fs.readFile(abs, 'utf-8'); } catch { prev = ''; }
        fileInitial.set(abs, prev);
        const strat = (mergeStrategy ? mergeStrategy : (fuzzy ? 'fuzzy' : 'strict')) as 'strict'|'fuzzy'|'conflict_markers';
        const applied = applyUnifiedHunksStrategy(prev, pf.hunks, strat);
        if (!applied.ok) return makeError('file_edit', `Failed to apply diff: ${applied.error}`, 'invalid_patch', { path: abs });
        fileNext.set(abs, applied.next);
      }

      if (dryRun) {
        const perFile = new Map<string, { hunks: UnifiedHunk[] }>();
        for (const pf of parseUnifiedDiffText(String(kind.diff || '')).files) {
          const abs = resolveWithContext(stripABPrefix(pf.path));
          perFile.set(abs, { hunks: pf.hunks });
        }
        const previewDiff = serializeUnifiedDiffFromMap(perFile);
        return makeSuccess('file_edit', {
          twoPhase: true,
          dryRun: true,
          previewDiff,
          normalizedPatch,
          patchPlanHash
        });
      }

      // Apply all-or-nothing
      const changedPaths: string[] = [];
      const originals = new Map(fileInitial);
      try {
        await __withFileEditMutator(async () => {
          for (const [abs, next] of fileNext.entries()) {
            if ((originals.get(abs) ?? '') !== next) {
              await safeWriteFileAtomic(abs, next);
              changedPaths.push(abs);
            }
          }
        });
      } catch (e: any) {
        // Revert best-effort
        for (const abs of changedPaths) {
          try {
            await safeWriteFileAtomic(abs, originals.get(abs) ?? '');
          } catch {}
        }
        return makeError('file_edit', `Batch apply failed: ${e?.message || e}`, 'apply_failed', { error: String(e) });
      }

      return makeSuccess('file_edit', {
        twoPhase: true,
        dryRun: false,
        appliedFiles: Array.from(fileNext.keys()),
        resultingDiffSummary: { filesChanged: Array.from(fileNext.keys()).length },
        patchPlanHash
      });
    } else {
      // ops-based plan
      const ops = kind.ops || [];
      // Build next states
      for (const o of ops) {
        if (!o || typeof o !== 'object') continue;
        const abs = resolveWithContext(String(o.filePath));
        if (!isUnderAllowedRoots(abs, allowedRoots)) {
          return makeError('file_edit', `Permission denied: ${abs} is outside allowed roots`, 'permission_denied', { path: abs });
        }
        let prev = fileNext.has(abs) ? fileNext.get(abs)! : (await readIfExistsAbs(abs)).original ?? '';
        if (!fileInitial.has(abs)) {
          const { original } = await readIfExistsAbs(abs);
          fileInitial.set(abs, original ?? '');
        }
        const preview = previewSingleEditContent(o, prev);
        if (!preview.ok) return makeError('file_edit', `planning_failed: ${preview.reason || 'unknown'}`, 'invalid_patch', { path: abs });
        fileNext.set(abs, preview.next);
      }

      if (dryRun) {
        const previewDiff = buildSimpleUnifiedDiff(fileInitial, fileNext);
        return makeSuccess('file_edit', {
          twoPhase: true,
          dryRun: true,
          previewDiff,
          normalizedPatch,
          patchPlanHash
        });
      }

      // Apply all changes with revert-on-failure
      const changedPaths: string[] = [];
      try {
        await __withFileEditMutator(async () => {
          for (const [abs, next] of fileNext.entries()) {
            const prev = fileInitial.get(abs) ?? '';
            if (prev !== next) {
              await safeWriteFileAtomic(abs, next);
              changedPaths.push(abs);
            }
          }
        });
      } catch (e: any) {
        // rollback
        for (const abs of changedPaths) {
          try {
            await safeWriteFileAtomic(abs, fileInitial.get(abs) ?? '');
          } catch {}
        }
        return makeError('file_edit', `Batch apply failed: ${e?.message || e}`, 'apply_failed', { error: String(e) });
      }

      return makeSuccess('file_edit', {
        twoPhase: true,
        dryRun: false,
        appliedFiles: Array.from(fileNext.keys()),
        resultingDiffSummary: { filesChanged: Array.from(fileNext.keys()).length },
        patchPlanHash
      });
    }
  } catch (error: any) {
    return makeError('file_edit', `2PC plan handling failed: ${error?.message || error}`, 'apply_failed');
  }
}

function normalizePlanInput(plan: any): { kind: 'diff' | 'ops'; diff?: string; ops?: any[] } {
  if (typeof plan === 'string') return { kind: 'diff', diff: plan };
  if (plan && typeof plan === 'object') {
    if (typeof (plan as any).diff === 'string') return { kind: 'diff', diff: (plan as any).diff };
    if (Array.isArray((plan as any).ops)) return { kind: 'ops', ops: (plan as any).ops };
    if (Array.isArray((plan as any).operations)) return { kind: 'ops', ops: (plan as any).operations };
  }
  return { kind: 'ops', ops: [] };
}

function buildSimpleUnifiedDiff(initials: Map<string, string>, nexts: Map<string, string>): string {
  const patches: string[] = [];
  for (const [abs, next] of nexts.entries()) {
    const prev = initials.get(abs) ?? '';
    if (prev === next) continue;

    const rawPatch = createTwoFilesPatch(abs, abs, prev, next, '', '', { context: 3 });
    const normalized = rawPatch
      .split('\n')
      .filter(line => !line.startsWith('Index: ') && !line.startsWith('==================================================================='))
      .join('\n')
      .trimEnd();

    if (normalized) patches.push(normalized);
  }

  return patches.join('\n') + (patches.length ? '\n' : '');
}

// --- Audit & Git helpers ---
function recordFileEditAudit(event: string, details: any) {
  try {
    const payload = { event, t: new Date().toISOString(), ...(details || {}) };
    console.log(`[file_edit:audit] ${JSON.stringify(payload)}`);
  } catch {}
}

async function tryGitCommit(paths: string[], message: string, addPaths?: string[]) {
  try {
    // Check if inside a git repo
    const { stdout } = await execAsync('git rev-parse --is-inside-work-tree');
    if (String(stdout || '').trim() !== 'true') return;
  } catch { return; }
  try {
    const toAdd = (addPaths && addPaths.length > 0) ? addPaths : paths;
    const addCmd = `git add ${toAdd.map(p => `'${p.replace(/'/g, "'\\''")}'`).join(' ')}`;
    await execAsync(addCmd);
    await execAsync(`git commit -m ${JSON.stringify(message || 'Apply file_edit batch')}`);
  } catch (e) {
    console.warn('git commit failed (non-fatal):', e);
  }
}

// tryGitApplyThreeWay moved to ../utils/unifiedDiff

export const readFilesTool = tool({
  name: 'read_files',
  description: 'Read multiple files efficiently with optional regex filtering, targeted regions, and intelligent chunk summaries to avoid token bloat.',
  inputSchema: z.object({
    filePaths: z.array(z.string()).nonempty().describe('List of file paths to read'),
    pattern: z.string().optional().describe('Optional regex pattern to filter lines'),
    contextLines: z.number().optional().describe('Number of context lines around each match (default: 1)'),
    forceFullContent: z.boolean().optional().describe('Force reading full content without consolidation'),
    maxFiles: z.number().optional().describe('Limit number of files to read (default: all provided)'),
    regions: z.array(z.object({
      filePath: z.string().describe('File path for the region'),
      startLine: z.number().describe('1-based start line'),
      endLine: z.number().describe('1-based end line (inclusive)')
    })).optional().describe('Optional explicit regions to extract instead of reading the whole file'),
    windowBefore: z.number().optional().describe('Lines before a match to include in a window (default: 10)'),
    windowAfter: z.number().optional().describe('Lines after a match to include in a window (default: 30)'),
    summarizeChunks: z.boolean().optional().describe('If true and Gemini is available, summarize each extracted chunk instead of returning full text'),
    maxCharsPerChunk: z.number().optional().describe('Cap chunk size in characters (default: 4000)')
  }) as any,
  execute: async ({ filePaths, pattern, contextLines = 1, forceFullContent = false, maxFiles, regions, windowBefore = 10, windowAfter = 30, summarizeChunks = false, maxCharsPerChunk = 4000 }: any) => {
    try {
      const requested = Array.isArray(filePaths) ? filePaths : [];
      const fileHardCap = typeof maxFiles === 'number' && maxFiles > 0 ? Math.floor(maxFiles) : 200;

      // Helper: recursively walk a directory and collect candidate text files
      const IGNORED_DIRS = new Set(['node_modules', '.git', 'dist', 'build', 'coverage', '.next', 'out', 'public', 'docker', '.cache']);
      const TEXT_EXTS = new Set([
        'ts','tsx','js','jsx','json','md','mdx','txt','yaml','yml','css','scss','html','tsconfig','graphql','gql','sh'
      ]);

      const isProbableTextFile = (p: string) => {
        const ext = path.extname(p).replace(/^\./, '').toLowerCase();
        if (!ext) return true; // files without extension often are scripts/configs
        return TEXT_EXTS.has(ext);
      };

      async function* walk(dir: string): AsyncGenerator<string> {
        let entries: any[] = [];
        try {
          entries = await fs.readdir(dir, { withFileTypes: true });
        } catch {
          return; // unreadable directory
        }
        for (const entry of entries) {
          const name: string = entry.name;
          if (IGNORED_DIRS.has(name)) continue;
          const full = path.join(dir, name);
          if (entry.isDirectory()) {
            yield* walk(full);
          } else if (entry.isFile()) {
            if (isProbableTextFile(full)) yield full;
          }
        }
      }

      // Expand any directory paths into files (up to cap)
      const expandedPaths: string[] = [];
      for (const p of requested) {
        const abs = resolveWithContext(p);
        try {
          const stats = await fs.stat(abs);
          if (stats.isDirectory()) {
            for await (const f of walk(abs)) {
              expandedPaths.push(f);
              if (expandedPaths.length >= fileHardCap) break;
            }
          } else if (stats.isFile()) {
            expandedPaths.push(abs);
          }
        } catch {
          // skip invalid path
        }
        if (expandedPaths.length >= fileHardCap) break;
      }

      // If nothing expanded, keep original (to preserve error reporting below)
      const selected = expandedPaths.length > 0
        ? expandedPaths.slice(0, fileHardCap)
        : requested.slice(0, fileHardCap);
      const flags = 'g';
      const usePattern = typeof pattern === 'string' && pattern.length > 0;
      const regex = usePattern ? new RegExp(pattern, flags) : null;

      const results: any[] = [];

      for (const filePath of selected) {
        const absolutePath = resolveWithContext(filePath);
        try {
          const stats = await fs.stat(absolutePath);
          if (!stats.isFile()) {
            results.push({ file: absolutePath, success: false, error: 'Path is not a file' });
            continue;
          }
          const content = await fs.readFile(absolutePath, 'utf-8');
          const lines = content.split('\n');

          // If explicit regions provided for this file, extract those windows
          const fileRegions = Array.isArray(regions)
            ? regions.filter((r: any) => path.resolve(r.filePath) === absolutePath)
            : [];

          const windows: Array<{ startLine: number; endLine: number; text: string; summary?: string }> = [];

          const clamp = (v: number, min: number, max: number) => Math.max(min, Math.min(max, v));

          if (fileRegions.length > 0) {
            for (const r of fileRegions) {
              const s = clamp(r.startLine - 1, 0, lines.length - 1);
              const e = clamp(r.endLine - 1, s, lines.length - 1);
              let text = lines.slice(s, e + 1).join('\n');
              if (maxCharsPerChunk && text.length > maxCharsPerChunk) {
                text = text.slice(0, maxCharsPerChunk);
              }
              windows.push({ startLine: s + 1, endLine: e + 1, text });
            }
          } else if (usePattern) {
            // Build windows around matches using windowBefore/After
            const matchedIndices: number[] = [];
            for (let i = 0; i < lines.length; i++) {
              if (regex!.test(lines[i])) matchedIndices.push(i);
            }
            // Merge overlapping windows
            const rawWindows: Array<{ s: number; e: number }> = matchedIndices.map(idx => ({
              s: clamp(idx - windowBefore, 0, lines.length - 1),
              e: clamp(idx + windowAfter, 0, lines.length - 1)
            }));
            rawWindows.sort((a,b) => a.s - b.s);
            const merged: Array<{ s: number; e: number }> = [];
            for (const w of rawWindows) {
              const last = merged[merged.length - 1];
              if (!last || w.s > last.e + 2) {
                merged.push({ ...w });
              } else {
                last.e = Math.max(last.e, w.e);
              }
            }
            for (const w of merged) {
              let text = lines.slice(w.s, w.e + 1).join('\n');
              if (maxCharsPerChunk && text.length > maxCharsPerChunk) {
                text = text.slice(0, maxCharsPerChunk);
              }
              windows.push({ startLine: w.s + 1, endLine: w.e + 1, text });
            }
          } else {
            // No pattern/regions: use existing read_file path for safety and consolidation
            const res = await (readFileTool as any).execute({ filePath: absolutePath, forceFullContent });
            results.push({ file: absolutePath, ...res, mode: res.consolidated ? 'consolidated' : 'full' });
            continue;
          }

          // Optional summarization of each chunk via Gemini (if configured)
          if (summarizeChunks) {
            try {
              const { geminiClient } = require('../utils/geminiClient');
              if (geminiClient && geminiClient.isConfigured()) {
                for (const w of windows) {
                  // Timebox each chunk summary to avoid blocking the stream too long
                  const SUMMARY_TIMEOUT_MS = 4000;
                  const response = await Promise.race([
                    geminiClient.createChatCompletion({
                      model: 'gemini-2.5-flash',
                      messages: [
                        { role: 'system', content: 'You are an expert code summarizer. Summarize the given code chunk focusing on purpose and how it fits in the file/system.' },
                        { role: 'user', content: `Summarize this code chunk (lines ${w.startLine}-${w.endLine}):\n\n${w.text}` }
                      ],
                                      max_tokens: 600
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('chunk_summary_timeout')), SUMMARY_TIMEOUT_MS))
                  ]).catch(() => null);
                  const text = (response as any)?.choices?.[0]?.message?.content || '[summary unavailable]';
                  w.summary = text;
                  // Reduce raw text size further if we have a summary
                  if (w.text.length > maxCharsPerChunk) {
                    w.text = w.text.slice(0, Math.max(500, Math.floor(maxCharsPerChunk / 4)));
                  }
                }
              }
            } catch (e) {
              // If summarization fails, continue with raw windows
            }
          }

          results.push({
            file: absolutePath,
            success: true,
            mode: 'windows',
            size: stats.size,
            totalLines: lines.length,
            windows
          });
        } catch (err: any) {
          results.push({ file: absolutePath, success: false, error: err?.message || 'Failed to read file' });
        }
      }

      return makeSuccess('read_files', {
        totalRequested: filePaths.length,
        totalProcessed: selected.length,
        results
      });
    } catch (error) {
      return makeError('read_files', `Failed to read files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

// (Old edit_file and apply_text_patch consolidated into file_edit)

// Wrapper: read a user-uploaded file via its /uploads URL
export const readUploadedFileTool = tool({
  name: 'read_uploaded_file',
  description: 'Read a user-uploaded file by its /uploads URL (or absolute URL pointing under /uploads).',
  inputSchema: z.object({
    url: z.string().describe('Public URL like /uploads/xyz or absolute URL under /uploads'),
  }) as any,
  execute: async ({ url }: any) => {
    try {
      const absPath = resolveUploadPublicUrlToPath(url);
      return await (readFileTool as any).execute({ filePath: absPath });
    } catch (error) {
      return makeError('read_uploaded_file', `Failed to read uploaded file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const readUploadedFilesTool = tool({
  name: 'read_uploaded_files',
  description: 'Read multiple user-uploaded files by their /uploads URLs.',
  inputSchema: z.object({
    urls: z.array(z.string()).min(1).describe('List of public URLs under /uploads'),
  }) as any,
  execute: async ({ urls }: any) => {
    try {
      const paths = urls.map((u: string) => resolveUploadPublicUrlToPath(u));
      return await (readFilesTool as any).execute({ filePaths: paths });
    } catch (error) {
      return makeError('read_uploaded_files', `Failed to read uploaded files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const assertFileContainsTool = tool({
  name: 'assert_file_contains',
  description: 'Verify that a file contains specific strings or regex patterns. Useful for read-after-write assertions.',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to check'),
    includes: z.array(z.object({
      text: z.string().optional().describe('Literal text to search for'),
      regex: z.string().optional().describe('Regex pattern to search for'),
      caseSensitive: z.boolean().optional().describe('Case sensitive text search')
    })).nonempty().describe('List of patterns that must be found'),
    failFast: z.boolean().optional().describe('Return on first missing (default: true)'),
  }) as any,
  execute: async ({ filePath, includes, failFast = true }: any) => {
    try {
      const absolutePath = path.resolve(filePath);
      const content = await fs.readFile(absolutePath, 'utf-8');
      const details: Array<{ index: number; kind: 'text' | 'regex'; pattern: string; found: boolean }> = [];
      let allFound = true;
      for (let i = 0; i < includes.length; i++) {
        const spec = includes[i] || {};
        let found = false;
        if (typeof spec.regex === 'string' && spec.regex.length > 0) {
          const rx = new RegExp(spec.regex, 'g');
          found = rx.test(content);
          details.push({ index: i, kind: 'regex', pattern: spec.regex, found });
        } else if (typeof spec.text === 'string') {
          const hay = spec.caseSensitive ? content : content.toLowerCase();
          const needle = spec.caseSensitive ? spec.text : spec.text.toLowerCase();
          found = hay.includes(needle);
          details.push({ index: i, kind: 'text', pattern: spec.text, found });
        } else {
          details.push({ index: i, kind: 'text', pattern: '', found: false });
        }
        if (!found) {
          allFound = false;
          if (failFast) break;
        }
      }
      return makeSuccess('assert_file_contains', { path: absolutePath, checks: details, allFound });
    } catch (error) {
      return makeError('assert_file_contains', `Failed to assert file contents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const listDirectoryTool = tool({
  name: 'list_directory',
  description: 'List contents of a directory',
  inputSchema: z.object({
    dirPath: z.string().describe('Path to the directory'),
    recursive: z.boolean().optional().describe('List recursively'),
    includeHidden: z.boolean().optional().describe('Include hidden files'),
    maxFiles: z.number().optional().describe('Maximum number of entries/files to return (default varies by mode)'),
  }) as any,
  execute: async ({ dirPath, recursive = false, includeHidden = false, maxFiles }: any) => {
    try {
      const absolutePath = resolveWithContext(dirPath);
      // Sensible defaults with a safe hard cap. Honor caller-provided maxFiles.
      const DEFAULT_NON_RECURSIVE_LIMIT = 100;
      const DEFAULT_RECURSIVE_LIMIT = 1000;
      const HARD_CAP = 5000;
      const requested = typeof maxFiles === 'number' && maxFiles > 0
        ? Math.floor(maxFiles)
        : (recursive ? DEFAULT_RECURSIVE_LIMIT : DEFAULT_NON_RECURSIVE_LIMIT);
      const fileLimit = Math.min(requested, HARD_CAP);

      if (!recursive) {
        const entries = await fs.readdir(absolutePath, { withFileTypes: true });
        const files = entries
          .filter(entry => includeHidden || !entry.name.startsWith('.'))
          .slice(0, fileLimit)
          .map(entry => ({
            name: entry.name,
            type: entry.isDirectory() ? 'directory' : 'file',
            path: path.join(absolutePath, entry.name),
          }));

        const result = {
          success: true,
          path: absolutePath,
          files,
          truncated: entries.length > fileLimit,
          totalCount: entries.length,
        };

        return makeSuccess('list_directory', result);
      } else {
        const allFiles: string[] = [];
        let fileCount = 0;
        let truncated = false;

        const walk = async (dir: string): Promise<void> => {
          if (fileCount >= fileLimit) {
            truncated = true;
            return;
          }

          try {
            const entries = await fs.readdir(dir, { withFileTypes: true });
            for (const entry of entries) {
              if (fileCount >= fileLimit) {
                truncated = true;
                break;
              }

              if (!includeHidden && entry.name.startsWith('.')) continue;

              // Skip directories that contain non-source code or build artifacts
              if (entry.name === 'node_modules' ||
                  entry.name === 'dist' ||
                  entry.name === 'build' ||
                  entry.name === 'coverage' ||
                  entry.name === '.next' ||
                  entry.name === '.nuxt' ||
                  entry.name === 'out' ||
                  entry.name === 'public' ||
                  entry.name === 'static' ||
                  entry.name === 'assets' ||
                  entry.name === 'vendor' ||
                  entry.name === 'tmp' ||
                  entry.name === 'temp'
              ) continue; // Skip non-source directories to reduce token usage

              const fullPath = path.join(dir, entry.name);
              if (entry.isDirectory()) {
                await walk(fullPath);
              } else {
                allFiles.push(fullPath);
                fileCount++;
              }
            }
          } catch (err) {
            // Skip directories we can't read
          }
        };

        await walk(absolutePath);

        const result = {
          success: true,
          path: absolutePath,
          files: allFiles,
          truncated,
          totalCount: fileCount,
        };

        return makeSuccess('list_directory', result);
      }
    } catch (error) {
      return makeError('list_directory', `Failed to list directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const executeCodeTool = tool({
  name: 'execute_code',
  description: 'Execute code in various languages',
  inputSchema: z.object({
    language: z.enum(['javascript', 'typescript', 'python', 'bash', 'shell']).describe('Programming language'),
    code: z.string().describe('Code to execute'),
    timeout: z.number().optional().describe('Timeout in milliseconds'),
  }) as any,
  execute: async ({ language, code, timeout = 30000 }: any) => {
    try {
      let command: string;

      switch (language) {
        case 'javascript':
        case 'typescript':
          const tempFile = `/tmp/temp_${Date.now()}.${language === 'typescript' ? 'ts' : 'js'}`;
          await fs.writeFile(tempFile, code);
          command = `bun run ${tempFile}`;
          break;

        case 'python':
          command = `python3 -c "${code.replace(/"/g, '\\"')}"`;
          break;

        case 'bash':
        case 'shell':
          command = code;
          break;

        default:
          throw new Error(`Unsupported language: ${language}`);
      }

      const { stdout, stderr } = await execAsync(command, { timeout });

      return makeSuccess('execute_code', {
        language,
        stdout,
        stderr,
        exitCode: 0,
      });
    } catch (error: any) {
      return makeError('execute_code', error.message || 'Execution failed', error.code || 1, { stdout: error.stdout || '', stderr: error.stderr || '' });
    }
  },
} as any);

export const analyzeProjectTool = tool({
  name: 'analyze_project',
  description: 'Analyze project structure and dependencies',
  inputSchema: z.object({
    projectPath: z.string().describe('Path to the project root'),
  }) as any,
  execute: async ({ projectPath }: any) => {
    try {
      const absolutePath = resolveWithContext(projectPath);
      const analysis: any = {
        path: absolutePath,
        files: {},
        dependencies: {},
        structure: {},
      };

      // Check for package.json
      try {
        const packageJson = await fs.readFile(path.join(absolutePath, 'package.json'), 'utf-8');
        const pkg = JSON.parse(packageJson);
        analysis.name = pkg.name;
        analysis.version = pkg.version;
        analysis.dependencies = {
          production: Object.keys(pkg.dependencies || {}),
          development: Object.keys(pkg.devDependencies || {}),
        };
        analysis.scripts = Object.keys(pkg.scripts || {});
      } catch {}

      // Check for TypeScript
      try {
        await fs.access(path.join(absolutePath, 'tsconfig.json'));
        analysis.typescript = true;
      } catch {
        analysis.typescript = false;
      }

      // Count file types
      const walk = async (dir: string): Promise<void> => {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
          if (entry.name.startsWith('.') || entry.name === 'node_modules') continue;

          const fullPath = path.join(dir, entry.name);
          if (entry.isDirectory()) {
            await walk(fullPath);
          } else {
            const ext = path.extname(entry.name);
            analysis.files[ext] = (analysis.files[ext] || 0) + 1;
          }
        }
      };

      await walk(absolutePath);

      const result = {
        success: true,
        analysis,
      };

      return makeSuccess('analyze_project', result);
    } catch (error) {
      return makeError('analyze_project', `Failed to analyze project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const gitOperationTool = tool({
  name: 'git_operation',
  description: 'Perform git operations',
  inputSchema: z.object({
    operation: z.enum(['status', 'diff', 'add', 'commit', 'push', 'pull', 'branch', 'checkout', 'log']),
    args: z.array(z.string()).optional().describe('Additional arguments for the git command'),
    message: z.string().optional().describe('Commit message (for commit operation)'),
  }) as any,
  execute: async ({ operation, args = [], message }: any) => {
    try {
      let command = `git ${operation}`;

      if (operation === 'commit' && message) {
        command = `git commit -m "${message}"`;
      } else if (args.length > 0) {
        command += ` ${args.join(' ')}`;
      }

      const { stdout, stderr } = await execAsync(command);

      return makeSuccess('git_operation', {
        operation,
        output: stdout || stderr,
      });
    } catch (error: any) {
      return makeError('git_operation', error.message || 'Git operation failed', undefined, { output: error.stdout || error.stderr || '' });
    }
  },
} as any);

export const searchCodeTool = tool({
  name: 'search_code',
  description: 'Search for patterns in code files',
  inputSchema: z.object({
    pattern: z.string().describe('Search pattern (regex supported)'),
    path: z.string().optional().describe('Path to search in'),
    fileTypes: z.array(z.string()).optional().describe('File extensions to search (e.g., ["ts", "js"])'),
    caseSensitive: z.boolean().optional().describe('Case sensitive search'),
  }) as any,
  execute: async ({ pattern, path: searchPath = '.', fileTypes = [], caseSensitive = false }: any) => {
    try {
      const absolutePath = resolveWithContext(searchPath);
      const flags = caseSensitive ? 'g' : 'gi';
      const regex = new RegExp(pattern, flags);
      const results: Array<{ file: string; line: number; match: string }> = [];

      const walk = async (dir: string): Promise<void> => {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          // Skip common directories that shouldn't be searched for source code
          if (entry.name.startsWith('.') ||
              entry.name === 'node_modules' ||
              entry.name === 'dist' ||
              entry.name === 'build' ||
              entry.name === 'coverage' ||
              entry.name === '.next' ||
              entry.name === '.nuxt' ||
              entry.name === 'out' ||
              entry.name === 'public' ||
              entry.name === 'static' ||
              entry.name === 'assets' ||
              entry.name === 'vendor' ||
              entry.name === 'lib' && entry.isDirectory() // Skip lib directories but allow lib files
          ) continue;

          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            await walk(fullPath);
          } else {
            const ext = path.extname(entry.name).slice(1);

            if (fileTypes.length === 0 || fileTypes.includes(ext)) {
              const content = await fs.readFile(fullPath, 'utf-8');
              const lines = content.split('\n');

              lines.forEach((line, index) => {
                if (regex.test(line)) {
                  results.push({
                    file: fullPath,
                    line: index + 1,
                    match: line.trim(),
                  });
                }
              });
            }
          }
        }
      };

      await walk(absolutePath);

      const result = {
        success: true,
        pattern,
        matches: results.length,
        results: results.slice(0, 10), // Limit to 10 results for token safety
        truncated: results.length > 10,
      };

      // Return the result object directly - sanitization should happen at a higher level
      // This ensures proper JSON encoding for tool calls
      return makeSuccess('search_code', result);
    } catch (error) {
      return makeError('search_code', `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const setWorkingDirectoryTool = tool({
  name: 'set_working_directory',
  description: 'Set the current working directory for file operations',
  inputSchema: z.object({
    dirPath: z.string().describe('Path to set as working directory'),
  }) as any,
  execute: async ({ dirPath }: any) => {
    try {
      const absolutePath = path.resolve(dirPath);

      // Verify directory exists
      const stats = await fs.stat(absolutePath);
      if (!stats.isDirectory()) {
        return makeError('set_working_directory', 'Path is not a directory');
      }

      // Request-scoped working directory (does not alter global process cwd)
      try { setContextValue('cwd', absolutePath); } catch {}
      // Also set process cwd as a fallback for tools outside ALS
      try { process.chdir(absolutePath); } catch {}

      return makeSuccess('set_working_directory', {
        path: absolutePath,
        previous: process.cwd(),
      });
    } catch (error) {
      return makeError('set_working_directory', `Failed to set working directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const getWorkingDirectoryTool = tool({
  name: 'get_working_directory',
  description: 'Get the current working directory',
  inputSchema: z.object({}) as any,
  execute: async () => {
    try {
      const override = getContextValue<string>('cwd');
      const currentDir = override || process.cwd();
      return makeSuccess('get_working_directory', {
        path: currentDir,
      });
    } catch (error) {
      return makeError('get_working_directory', `Failed to get working directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

export const getProjectContextTool = tool({
  name: 'get_project_context',
  description: 'Get current project context information',
  inputSchema: z.object({
    includeDependencies: z.boolean().optional().describe('Include dependency information'),
    includeGitInfo: z.boolean().optional().describe('Include git repository information'),
  }) as any,
  execute: async ({ includeDependencies = true, includeGitInfo = true }: any) => {
    try {
      const currentDir = process.cwd();
      const context: any = {
        workingDirectory: currentDir,
        projectName: path.basename(currentDir),
      };

      // Helpers
      const exists = async (p: string) => { try { await fs.stat(p); return true; } catch { return false; } };
      const which = async (cmd: string) => {
        try {
          const { stdout } = await execAsync(process.platform === 'win32' ? `where ${cmd}` : `which ${cmd}`);
          return !!stdout.trim();
        } catch { return false; }
      };
      const readSafe = async (p: string) => { try { return await fs.readFile(p, 'utf-8'); } catch { return ''; } };

      // Check for package.json
      try {
        const packageJsonPath = path.join(currentDir, 'package.json');
        const packageJsonContent = await fs.readFile(packageJsonPath, 'utf-8');
        const packageJson = JSON.parse(packageJsonContent);

        context.packageJson = {
          name: packageJson.name,
          version: packageJson.version,
          description: packageJson.description,
          main: packageJson.main,
          scripts: Object.keys(packageJson.scripts || {}),
        };

        if (includeDependencies) {
          context.dependencies = {
            production: Object.keys(packageJson.dependencies || {}),
            development: Object.keys(packageJson.devDependencies || {}),
          };
        }
      } catch {
        // No package.json found
      }

      // Language/framework/tooling detection
      const projectFiles = await fs.readdir(currentDir);
      const hasPackageJson = projectFiles.includes('package.json');
      const hasTsConfig = projectFiles.includes('tsconfig.json') || projectFiles.includes('tsconfig.build.json');
      const hasPubspec = projectFiles.includes('pubspec.yaml');
      const hasPy = projectFiles.includes('pyproject.toml') || projectFiles.includes('requirements.txt');
      const hasCargo = projectFiles.includes('Cargo.toml');
      const hasGoMod = projectFiles.includes('go.mod');

      // Node/TS detection
      const node = hasPackageJson;
      const typescript = hasTsConfig || !!context.dependencies?.development?.includes('typescript') || !!context.dependencies?.production?.includes('typescript');

      // Read pubspec to detect Flutter/build_runner
      const pubspec = hasPubspec ? await readSafe(path.join(currentDir, 'pubspec.yaml')) : '';
      const dart = hasPubspec || /\.(dart)$/i.test(pubspec) || await exists(path.join(currentDir, 'lib'));
      const flutterDependency = /\bflutter\s*:\s*/i.test(pubspec);
      const buildRunnerDependency = /build_runner\s*:/i.test(pubspec);
      const flutterInstalled = await which('flutter');
      const dartInstalled = await which('dart');

      // Python tools
      const python = hasPy;
      const ruffInstalled = await which('ruff');
      const blackInstalled = await which('black');

      // Other stacks
      const rust = hasCargo;
      const go = hasGoMod;

      // Package managers / toolchains
      const bunInstalled = await which('bun');
      const npmInstalled = await which('npm');
      const pnpmInstalled = await which('pnpm');
      const yarnInstalled = await which('yarn');

      // Project type heuristic
      context.projectType = 'generic';
      if (node) {
        context.projectType = 'node';
        if (context.dependencies?.production?.includes('react')) context.projectType = 'react';
      } else if (hasCargo) {
        context.projectType = 'rust';
      } else if (hasGoMod) {
        context.projectType = 'go';
      } else if (python) {
        context.projectType = 'python';
      } else if (dart) {
        context.projectType = flutterDependency ? 'flutter' : 'dart';
      }

      context.languages = {
        node,
        typescript,
        dart,
        flutter: flutterDependency || flutterInstalled,
        python,
        rust,
        go,
      };

      context.tooling = {
        bun: bunInstalled,
        npm: npmInstalled,
        pnpm: pnpmInstalled,
        yarn: yarnInstalled,
        dart: dartInstalled,
        flutter: flutterInstalled,
        ruff: ruffInstalled,
        black: blackInstalled,
      };

      // Recommend verification commands per language
      const recommended: any = { steps: [] as Array<{ name: string; command: string; when?: string }> };
      if (node) {
        // Prefer bun if available
        const useBun = bunInstalled;
        const scripts = context.packageJson?.scripts || [];
        const hasTypecheck = Array.isArray(scripts) ? scripts.includes('typecheck') : false;
        if (typescript) {
          recommended.steps.push({ name: 'typecheck', command: useBun && hasTypecheck ? 'bun run typecheck' : (useBun ? 'bunx tsc -p tsconfig.json --noEmit --pretty false' : 'npx -y tsc -p tsconfig.json --noEmit --pretty false') });
        }
        if (Array.isArray(scripts) && scripts.includes('build')) {
          recommended.steps.push({ name: 'build', command: useBun ? 'bun run build' : 'npm run build' });
        }
        if (Array.isArray(scripts) && scripts.includes('lint')) {
          recommended.steps.push({ name: 'lint', command: useBun ? 'bun run lint' : 'npm run lint' });
        }
        if (Array.isArray(scripts) && scripts.includes('black')) {
          recommended.steps.push({ name: 'black', command: 'black' });
        }
      }
      if (dart) {
        recommended.steps.push({ name: 'analyze', command: flutterInstalled ? 'flutter analyze' : 'dart analyze' });
        if (buildRunnerDependency && dartInstalled) {
          recommended.steps.push({ name: 'codegen', command: 'dart run build_runner build -d', when: 'thorough' });
        }
      }
      if (python) {
        if (ruffInstalled) recommended.steps.push({ name: 'lint', command: 'ruff check .' });
        if (blackInstalled) recommended.steps.push({ name: 'format-check', command: 'black --check .' });
      }
      if (rust) recommended.steps.push({ name: 'check', command: 'cargo check' });
      if (go) recommended.steps.push({ name: 'build', command: 'go build ./...' });

      if (recommended.steps.length > 0) context.recommendedVerification = recommended;

      // Git information
      if (includeGitInfo) {
        try {
          const { stdout: gitBranch } = await execAsync('git branch --show-current');
          const { stdout: gitStatus } = await execAsync('git status --porcelain');

          context.git = {
            branch: gitBranch.trim(),
            hasChanges: gitStatus.trim().length > 0,
            isRepo: true,
          };
        } catch {
          context.git = { isRepo: false };
        }
      }

      return makeSuccess('get_project_context', { context });
    } catch (error) {
      return makeError('get_project_context', `Failed to get project context: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
} as any);

// Import validation wrapper (using require to avoid circular dependencies)
function getValidationWrapper() {
  try {
    return require('../utils/toolErrorHandler').createValidatingToolWrapper;
  } catch (error) {
    console.warn('⚠️ Could not load tool validation, tools will run without validation');
    return (tool: any) => tool; // Identity function as fallback
  }
}

// Create validated versions of all tools
function createValidatedFileTools() {
  const wrapper = getValidationWrapper();
  const baseTools = [
    readFileTool,
    readFilesTool,
    readUploadedFileTool,
    readUploadedFilesTool,
    fileEditTool,
    assertFileContainsTool,
    listDirectoryTool,
    executeCodeTool,
    analyzeProjectTool,
    gitOperationTool,
    setWorkingDirectoryTool,
    getWorkingDirectoryTool,
    getProjectContextTool,
    // Diagnostics & build tools
    ...codeDiagnosticsTools,
  ];

  console.log('🔧 Creating validated file operation tools...');

  return baseTools.map(tool => {
    try {
      const validatedTool = wrapper(tool, (tool as any).name || 'unknown_tool');
      console.log(`✅ Validated: ${(tool as any).name}`);
      return validatedTool;
    } catch (error) {
      console.warn(`⚠️ Failed to wrap ${(tool as any).name} with validation:`, error);
      return tool; // Return unwrapped if validation fails
    }
  });
}

// Export validated file operation tools
export const fileOperationTools = createValidatedFileTools();
