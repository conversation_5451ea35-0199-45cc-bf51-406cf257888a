import { tool } from 'ai';
import { z } from 'zod';
import * as path from 'path';
import { fileAnalysisWorker } from '../agents/workers/FileAnalysisWorker';
import { readFileTool } from './fileOperations';

export const runFileAnalysisTool = tool({
  name: 'run_file_analysis',
  description: 'Generate a structured analysis for a repository file using the File Analysis Worker.',
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to analyze (relative or absolute).'),
    focusAreas: z.array(z.string()).optional().describe('Optional focus topics (e.g., security, performance).'),
    instruction: z.string().optional().describe('Optional extra guidance for the analysis worker.'),
  }) as any,
  execute: async ({ filePath, focusAreas, instruction }: { filePath: string; focusAreas?: string[]; instruction?: string }) => {
    const readResult = await (readFileTool as any).execute({ filePath, forceFullContent: true });
    if (!readResult || readResult.success === false) {
      throw new Error(typeof readResult?.error?.message === 'string' ? readResult.error.message : `Failed to read file: ${filePath}`);
    }

    const resolvedPath = typeof readResult.path === 'string' && readResult.path.length > 0 ? readResult.path : filePath;
    const content = typeof readResult.content === 'string' ? readResult.content : '';

    const payload = JSON.stringify({
      instruction: instruction || undefined,
      file: {
        path: resolvedPath,
        name: path.basename(resolvedPath),
        type: path.extname(resolvedPath).replace(/^\./, '') || 'text',
        content,
      },
    });

    const analysis = await fileAnalysisWorker.execute(payload, { focusAreas });

    return {
      success: true,
      path: resolvedPath,
      focusAreas: Array.isArray(focusAreas) && focusAreas.length > 0 ? focusAreas : undefined,
      analysis,
    };
  },
} as any);
