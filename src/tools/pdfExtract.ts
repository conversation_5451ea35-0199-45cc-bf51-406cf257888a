import { tool } from 'ai';
import { z } from 'zod';

// Reuse the shared PDF extraction utility
import {
  extractPdfFromFile,
  PathSecurityError,
  FileAccessError,
  PdfExtractionError,
} from '../utils/pdf-extractor';

/**
 * pdf_extract tool
 *
 * Extracts text and metadata from a local PDF file with secure path handling.
 * Defaults to allowing files under the project working directory. For most
 * use cases, pass a relative path like 'uploads/<file>.pdf'.
 */
export const pdfExtractTool = tool({
  name: 'pdf_extract',
  description:
    'Extract text and metadata from a local PDF file (use paths under uploads/ by default). Returns { file, numpages, info, metadata, text }.',
  inputSchema: z
    .object({
      filePath: z
        .string()
        .describe('Path to the PDF file. Prefer relative paths under uploads/. Example: uploads/1757963490637-389422180.pdf'),
      baseDir: z
        .string()
        .optional()
        .describe('Base directory to resolve file paths against (defaults to current working directory).'),
      allowOutsideBase: z
        .boolean()
        .optional()
        .describe('Allow access to files outside baseDir (use with caution). Defaults to false.'),
    }) as any,
  execute: async ({ filePath, baseDir, allowOutsideBase }: any) => {
    try {
      const result = await extractPdfFromFile(filePath, {
        baseDir: baseDir || process.cwd(),
        allowOutsideBase: !!allowOutsideBase,
        validateExtension: true,
      });
      // Return as JSON string so downstream LLM steps can read it or parse it.
      return JSON.stringify(result, null, 2);
    } catch (err: any) {
      // Normalize error messages for the model and users
      if (err instanceof PathSecurityError) {
        throw new Error(`PathSecurityError: ${err.message}`);
      }
      if (err instanceof FileAccessError) {
        throw new Error(`FileAccessError: ${err.message}`);
      }
      if (err instanceof PdfExtractionError) {
        throw new Error(`PdfExtractionError: ${err.message}`);
      }
      throw err;
    }
  },
} as any);

export default pdfExtractTool;
