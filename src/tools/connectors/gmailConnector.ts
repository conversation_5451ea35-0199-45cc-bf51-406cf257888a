/**
 * Gmail Connector Tool for Dante
 * Provides Gmail integration using Vercel AI SDK
 * Converts from OpenAI SDK to Vercel AI SDK patterns
 */

import { tool } from 'ai';
import { z } from 'zod';
import { connectorService } from '../../services/connectorService';
import { ConnectorId } from '../../types/connectors';

const GMAIL_CONNECTOR_ID: ConnectorId = 'connector_gmail';

// Gmail search parameters
const gmailSearchParams = z.object({
  query: z.string().optional().describe('Search query (e.g., "from:<EMAIL> subject:meeting")'),
  maxResults: z.number().optional().default(10).describe('Maximum number of results to return'),
  includeSpamTrash: z.boolean().optional().default(false).describe('Include spam and trash messages'),
  userId: z.string().optional().describe('User ID for session management')
});

// Read email parameters
const readEmailParams = z.object({
  emailId: z.string().describe('The ID of the email to read'),
  userId: z.string().optional().describe('User ID for session management')
});

// Batch read parameters
const batchReadParams = z.object({
  emailIds: z.array(z.string()).describe('Array of email IDs to read'),
  userId: z.string().optional().describe('User ID for session management')
});

/**
 * Search Gmail emails
 */
export const searchGmail = tool({
  name: 'search_gmail',
  description: 'Search Gmail for emails matching a query. Requires Gmail OAuth authentication.',
  inputSchema: gmailSearchParams as any,
  execute: async ({ query, maxResults, includeSpamTrash, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, GMAIL_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Gmail not authenticated. Please authenticate with Gmail first.',
          authUrl: `/api/auth/google/gmail`
        });
      }

      // Build the search query for the connector
      let connectorQuery = 'Search my Gmail';
      if (query) {
        connectorQuery = `Search Gmail for: ${query}`;
      }
      if (maxResults && maxResults !== 10) {
        connectorQuery += ` (limit: ${maxResults} results)`;
      }
      if (includeSpamTrash) {
        connectorQuery += ' including spam and trash';
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: GMAIL_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search_emails', 'search_email_ids']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Gmail search failed:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to search Gmail'
      });
    }
  }
} as any);

/**
 * Get recent Gmail emails
 */
export const getRecentGmailEmails = tool({
  name: 'get_recent_gmail_emails',
  description: 'Get the most recent emails from Gmail. Requires Gmail OAuth authentication.',
  inputSchema: gmailSearchParams as any,
  execute: async ({ maxResults, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, GMAIL_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Gmail not authenticated. Please authenticate with Gmail first.',
          authUrl: `/api/auth/google/gmail`
        });
      }

      const connectorQuery = `Get my ${maxResults || 10} most recent Gmail emails`;

      const result = await connectorService.executeConnectorRequest({
        connector_id: GMAIL_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['get_recent_emails']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to get recent emails:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to get recent emails'
      });
    }
  }
} as any);

/**
 * Read a specific Gmail email
 */
export const readGmailEmail = tool({
  name: 'read_gmail_email',
  description: 'Read a specific Gmail email by its ID. Requires Gmail OAuth authentication.',
  inputSchema: readEmailParams as any,
  execute: async ({ emailId, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, GMAIL_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Gmail not authenticated. Please authenticate with Gmail first.',
          authUrl: `/api/auth/google/gmail`
        });
      }

      const connectorQuery = `Read Gmail email with ID: ${emailId}`;

      const result = await connectorService.executeConnectorRequest({
        connector_id: GMAIL_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['read_email']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to read email:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to read email'
      });
    }
  }
} as any);

/**
 * Read multiple Gmail emails in batch
 */
export const batchReadGmailEmails = tool({
  name: 'batch_read_gmail_emails',
  description: 'Read multiple Gmail emails in one call. Requires Gmail OAuth authentication.',
  inputSchema: batchReadParams as any,
  execute: async ({ emailIds, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, GMAIL_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Gmail not authenticated. Please authenticate with Gmail first.',
          authUrl: `/api/auth/google/gmail`
        });
      }

      const connectorQuery = `Read these Gmail emails: ${emailIds.join(', ')}`;

      const result = await connectorService.executeConnectorRequest({
        connector_id: GMAIL_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['batch_read_email']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to batch read emails:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to batch read emails'
      });
    }
  }
} as any);

// Export all Gmail tools
export const gmailTools = [
  searchGmail,
  getRecentGmailEmails,
  readGmailEmail,
  batchReadGmailEmails
];
