/**
 * Google Drive Connector Tool for Dante
 * Provides Google Drive integration using Vercel AI SDK
 * Converts from OpenAI SDK to Vercel AI SDK patterns
 */

import { tool } from 'ai';
import { z } from 'zod';
import { connectorService } from '../../services/connectorService';
import { ConnectorId } from '../../types/connectors';

const DRIVE_CONNECTOR_ID: ConnectorId = 'connector_googledrive';

// Drive search parameters
const driveSearchParams = z.object({
  query: z.string().optional().describe('Search query (e.g., "presentation", "budget 2024")'),
  mimeType: z.string().optional().describe('File type filter (e.g., "application/vnd.google-apps.document")'),
  maxResults: z.number().optional().default(20).describe('Maximum number of results'),
  includeShared: z.boolean().optional().default(true).describe('Include shared drives'),
  userId: z.string().optional().describe('User ID for session management')
});

// File fetch parameters
const fileFetchParams = z.object({
  fileId: z.string().optional().describe('File ID to fetch (if known)'),
  filePath: z.string().optional().describe('File path to fetch'),
  downloadContent: z.boolean().optional().default(true).describe('Download file content'),
  userId: z.string().optional().describe('User ID for session management')
});

// Recent documents parameters
const recentDocsParams = z.object({
  maxResults: z.number().optional().default(10).describe('Number of recent documents'),
  fileType: z.string().optional().describe('Filter by file type'),
  userId: z.string().optional().describe('User ID for session management')
});

/**
 * Search Google Drive files
 */
export const searchDrive = tool({
  name: 'search_google_drive',
  description: 'Search Google Drive for files and folders. Requires Drive OAuth authentication.',
  inputSchema: driveSearchParams as any,
  execute: async ({ query, mimeType, maxResults, includeShared, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, DRIVE_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Drive not authenticated. Please authenticate with Google Drive first.',
          authUrl: `/api/auth/google/drive`
        });
      }

      // Build the search query for the connector
      let connectorQuery = 'Search my Google Drive';
      
      if (query) {
        connectorQuery = `Search Drive for: ${query}`;
      }
      
      if (mimeType) {
        const typeMap: Record<string, string> = {
          'application/vnd.google-apps.document': 'Google Docs',
          'application/vnd.google-apps.spreadsheet': 'Google Sheets',
          'application/vnd.google-apps.presentation': 'Google Slides',
          'application/pdf': 'PDF files',
          'image/': 'images',
          'video/': 'videos'
        };
        
        const readableType = Object.entries(typeMap).find(([key]) => 
          mimeType.startsWith(key)
        )?.[1] || mimeType;
        
        connectorQuery += ` (type: ${readableType})`;
      }
      
      if (maxResults && maxResults !== 20) {
        connectorQuery += ` (limit: ${maxResults})`;
      }
      
      if (!includeShared) {
        connectorQuery += ' (exclude shared drives)';
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: DRIVE_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Drive search failed:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to search Drive'
      });
    }
  }
} as any);

/**
 * Get recent documents from Google Drive
 */
export const getRecentDocuments = tool({
  name: 'get_recent_drive_documents',
  description: 'Get recently modified documents from Google Drive. Requires Drive OAuth authentication.',
  inputSchema: recentDocsParams as any,
  execute: async ({ maxResults, fileType, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, DRIVE_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Drive not authenticated. Please authenticate with Google Drive first.',
          authUrl: `/api/auth/google/drive`
        });
      }

      let connectorQuery = `Get my ${maxResults || 10} most recent documents from Drive`;
      
      if (fileType) {
        connectorQuery += ` (type: ${fileType})`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: DRIVE_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['recent_documents']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to get recent documents:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to get recent documents'
      });
    }
  }
} as any);

/**
 * Fetch/download a file from Google Drive
 */
export const fetchDriveFile = tool({
  name: 'fetch_drive_file',
  description: 'Fetch or download a file from Google Drive. Requires Drive OAuth authentication.',
  inputSchema: fileFetchParams as any,
  execute: async ({ fileId, filePath, downloadContent, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, DRIVE_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Drive not authenticated. Please authenticate with Google Drive first.',
          authUrl: `/api/auth/google/drive`
        });
      }

      let connectorQuery: string;
      
      if (fileId) {
        connectorQuery = `Fetch Drive file with ID: ${fileId}`;
      } else if (filePath) {
        connectorQuery = `Fetch Drive file at path: ${filePath}`;
      } else {
        return JSON.stringify({
          success: false,
          error: 'Either fileId or filePath must be provided'
        });
      }
      
      if (downloadContent) {
        connectorQuery += ' and download its content';
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: DRIVE_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['fetch']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to fetch file:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to fetch Drive file'
      });
    }
  }
} as any);

/**
 * List shared drives
 */
export const listSharedDrives = tool({
  name: 'list_shared_drives',
  description: 'List all shared drives accessible to the user. Requires Drive OAuth authentication.',
  inputSchema: z.object({
    userId: z.string().optional().describe('User ID for session management')
  }) as any,
  execute: async ({ userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, DRIVE_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Drive not authenticated. Please authenticate with Google Drive first.',
          authUrl: `/api/auth/google/drive`
        });
      }

      const connectorQuery = 'List all my shared drives';

      const result = await connectorService.executeConnectorRequest({
        connector_id: DRIVE_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['list_drives']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to list shared drives:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to list shared drives'
      });
    }
  }
} as any);

/**
 * Search for specific file types
 */
export const searchDriveByType = tool({
  name: 'search_drive_by_type',
  description: 'Search Google Drive for specific file types (documents, spreadsheets, presentations, etc.). Requires Drive OAuth authentication.',
  inputSchema: z.object({
    fileType: z.enum(['document', 'spreadsheet', 'presentation', 'pdf', 'image', 'video', 'folder'])
      .describe('Type of file to search for'),
    query: z.string().optional().describe('Additional search query'),
    maxResults: z.number().optional().default(20).describe('Maximum number of results'),
    userId: z.string().optional().describe('User ID for session management')
  }) as any,
  execute: async ({ fileType, query, maxResults, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, DRIVE_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Drive not authenticated. Please authenticate with Google Drive first.',
          authUrl: `/api/auth/google/drive`
        });
      }

      const typeMap: Record<string, string> = {
        'document': 'Google Docs documents',
        'spreadsheet': 'Google Sheets spreadsheets',
        'presentation': 'Google Slides presentations',
        'pdf': 'PDF files',
        'image': 'images',
        'video': 'videos',
        'folder': 'folders'
      };

      let connectorQuery = `Find all ${typeMap[fileType] || fileType} in my Drive`;
      
      if (query) {
        connectorQuery += ` matching "${query}"`;
      }
      
      if (maxResults && maxResults !== 20) {
        connectorQuery += ` (limit: ${maxResults})`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: DRIVE_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to search by type:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to search Drive by type'
      });
    }
  }
} as any);

// Export all Drive tools
export const driveTools = [
  searchDrive,
  getRecentDocuments,
  fetchDriveFile,
  listSharedDrives,
  searchDriveByType
];
