/**
 * Google Calendar Connector Tool for Dante
 * Provides Google Calendar integration using Vercel AI SDK
 * Converts from OpenAI SDK to Vercel AI SDK patterns
 */

import { tool } from 'ai';
import { z } from 'zod';
import { connectorService } from '../../services/connectorService';
import { ConnectorId } from '../../types/connectors';

const CALENDAR_CONNECTOR_ID: ConnectorId = 'connector_googlecalendar';

// Calendar search parameters
const calendarSearchParams = z.object({
  timeMin: z.string().optional().describe('Start time in ISO format (e.g., "2025-01-01T00:00:00Z")'),
  timeMax: z.string().optional().describe('End time in ISO format'),
  query: z.string().optional().describe('Text to search for in event details'),
  maxResults: z.number().optional().default(50).describe('Maximum number of events to return'),
  calendarId: z.string().optional().describe('Specific calendar ID to search (default: primary)'),
  userId: z.string().optional().describe('User ID for session management')
});

// Event fetch parameters
const eventFetchParams = z.object({
  eventId: z.string().describe('The ID of the event to fetch'),
  calendarId: z.string().optional().describe('Calendar ID (default: primary)'),
  userId: z.string().optional().describe('User ID for session management')
});

// Today's events parameters
const todayEventsParams = z.object({
  timezone: z.string().optional().describe('Timezone (e.g., "America/New_York")'),
  userId: z.string().optional().describe('User ID for session management')
});

/**
 * Search Google Calendar events
 */
export const searchCalendarEvents = tool({
  name: 'search_calendar_events',
  description: 'Search Google Calendar for events within a time range or matching a query. Requires Calendar OAuth authentication.',
  inputSchema: calendarSearchParams as any,
  execute: async ({ timeMin, timeMax, query, maxResults, calendarId, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, CALENDAR_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Calendar not authenticated. Please authenticate with Google Calendar first.',
          authUrl: `/api/auth/google/calendar`
        });
      }

      // Build the search query for the connector
      let connectorQuery = 'Search my calendar';
      
      if (timeMin && timeMax) {
        connectorQuery = `Search calendar events from ${timeMin} to ${timeMax}`;
      } else if (timeMin) {
        connectorQuery = `Search calendar events starting from ${timeMin}`;
      } else if (timeMax) {
        connectorQuery = `Search calendar events until ${timeMax}`;
      }
      
      if (query) {
        connectorQuery += ` containing "${query}"`;
      }
      
      if (maxResults && maxResults !== 50) {
        connectorQuery += ` (limit: ${maxResults})`;
      }
      
      if (calendarId && calendarId !== 'primary') {
        connectorQuery += ` in calendar ${calendarId}`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: CALENDAR_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search_events', 'search']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Calendar search failed:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to search calendar'
      });
    }
  }
} as any);

/**
 * Get today's events from Google Calendar
 */
export const getTodayEvents = tool({
  name: 'get_today_calendar_events',
  description: 'Get all events scheduled for today from Google Calendar. Requires Calendar OAuth authentication.',
  inputSchema: todayEventsParams as any,
  execute: async ({ timezone, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, CALENDAR_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Calendar not authenticated. Please authenticate with Google Calendar first.',
          authUrl: `/api/auth/google/calendar`
        });
      }

      let connectorQuery = "What's on my Google Calendar for today?";
      if (timezone) {
        connectorQuery += ` (timezone: ${timezone})`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: CALENDAR_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search_events']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to get today\'s events:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to get today\'s events'
      });
    }
  }
} as any);

/**
 * Get this week's events from Google Calendar
 */
export const getWeekEvents = tool({
  name: 'get_week_calendar_events',
  description: 'Get all events scheduled for this week from Google Calendar. Requires Calendar OAuth authentication.',
  inputSchema: todayEventsParams as any,
  execute: async ({ timezone, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, CALENDAR_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Calendar not authenticated. Please authenticate with Google Calendar first.',
          authUrl: `/api/auth/google/calendar`
        });
      }

      let connectorQuery = "Show me my calendar events for this week";
      if (timezone) {
        connectorQuery += ` (timezone: ${timezone})`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: CALENDAR_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search_events']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to get week\'s events:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to get this week\'s events'
      });
    }
  }
} as any);

/**
 * Read a specific calendar event
 */
export const readCalendarEvent = tool({
  name: 'read_calendar_event',
  description: 'Read details of a specific Google Calendar event by its ID. Requires Calendar OAuth authentication.',
  inputSchema: eventFetchParams as any,
  execute: async ({ eventId, calendarId, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, CALENDAR_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Calendar not authenticated. Please authenticate with Google Calendar first.',
          authUrl: `/api/auth/google/calendar`
        });
      }

      let connectorQuery = `Get details for calendar event ID: ${eventId}`;
      if (calendarId && calendarId !== 'primary') {
        connectorQuery += ` from calendar ${calendarId}`;
      }

      const result = await connectorService.executeConnectorRequest({
        connector_id: CALENDAR_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['fetch', 'read_event']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to read event:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to read calendar event'
      });
    }
  }
} as any);

/**
 * Get upcoming meetings
 */
export const getUpcomingMeetings = tool({
  name: 'get_upcoming_meetings',
  description: 'Get upcoming meetings from Google Calendar. Requires Calendar OAuth authentication.',
  inputSchema: calendarSearchParams as any,
  execute: async ({ maxResults, userId }: any) => {
    try {
      const { token } = await connectorService.getOAuthTokenForContext(userId, CALENDAR_CONNECTOR_ID);
      
      if (!token) {
        return JSON.stringify({
          success: false,
          error: 'Google Calendar not authenticated. Please authenticate with Google Calendar first.',
          authUrl: `/api/auth/google/calendar`
        });
      }

      const connectorQuery = `Find my next ${maxResults || 10} upcoming meetings`;

      const result = await connectorService.executeConnectorRequest({
        connector_id: CALENDAR_CONNECTOR_ID,
        oauth_token: token.access_token,
        input: connectorQuery,
        require_approval: 'never',
        allowed_tools: ['search_events']
      });

      return JSON.stringify({
        success: true,
        ...result
      });
    } catch (error: any) {
      console.error('Failed to get upcoming meetings:', error);
      return JSON.stringify({
        success: false,
        error: error.message || 'Failed to get upcoming meetings'
      });
    }
  }
} as any);

// Export all Calendar tools
export const calendarTools = [
  searchCalendarEvents,
  getTodayEvents,
  getWeekEvents,
  readCalendarEvent,
  getUpcomingMeetings
];
