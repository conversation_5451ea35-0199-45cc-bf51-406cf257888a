import { tool } from 'ai';
import { z } from 'zod';
import { recoverySystem } from '../recovery/RecoveryIntegration';
import { activeTaskManager } from '../recovery/ActiveTaskManager';
import { taskResumptionEngine } from '../recovery/TaskResumptionEngine';
import { hotReloadDetector } from '../recovery/HotReloadDetector';

// Create task recovery point tool
export const createTaskRecoveryPointTool = tool({
  name: 'create_task_recovery_point',
  description: 'Create a recovery checkpoint for the current task to enable resumption after hot-reload or restart.',
  inputSchema: z.object({
    sessionId: z.string().describe('Current session ID'),
    agentName: z.string().describe('Name of the agent handling this task'),
    objective: z.string().describe('Main objective of the task'),
    conversationMessages: z.array(z.object({
      role: z.string(),
      content: z.string(),
      timestamp: z.number().optional()
    })).default([]).describe('Current conversation messages'),
    currentStep: z.string().optional().describe('Current step being worked on'),
    expectedHotReloads: z.number().default(1).describe('Number of hot-reloads expected for this task'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { sessionId, agentName, objective, conversationMessages, currentStep, expectedHotReloads } = params;
      
      const task = await recoverySystem.createTaskRecoveryPoint(
        sessionId,
        agentName,
        objective,
        conversationMessages
      );
      
      // Update task with additional information
      if (currentStep) {
        await recoverySystem.markTaskStep(task.taskId, currentStep);
      }
      
      // Update expected hot-reloads
      if (expectedHotReloads > 1) {
        await activeTaskManager.updateTaskProgress(task.taskId, 0);
        const updatedTask = await activeTaskManager.loadTaskState(task.taskId);
        if (updatedTask) {
          updatedTask.metadata.expectedHotReloads = expectedHotReloads;
          await activeTaskManager.saveTaskState(updatedTask);
        }
      }
      
      return {
        success: true,
        taskId: task.taskId,
        recoveryPointCreated: true,
        message: `Created recovery point for task: ${objective.substring(0, 50)}...`,
        nextSteps: [
          'Continue with your implementation',
          'Mark file modifications with mark_file_modification',
          'Update progress with update_task_progress',
        ],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create task recovery point',
      };
    }
  },
});

// Mark file modification tool
export const markFileModificationTool = tool({
  name: 'mark_file_modification',
  description: 'Mark a file as being modified by Dante to enable proper hot-reload tracking and recovery.',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task this file modification belongs to'),
    filePath: z.string().describe('Path to the file being modified'),
    modificationType: z.enum(['create', 'edit', 'delete']).default('edit').describe('Type of modification being made'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { taskId, filePath, modificationType } = params;
      
      // Mark with hot-reload detector
      hotReloadDetector.markDanteModification(filePath);
      
      // Record in task recovery system
      await recoverySystem.markFileModification(taskId, filePath);
      
      return {
        success: true,
        message: `Marked file modification: ${filePath} (${modificationType})`,
        filePath,
        modificationType,
        trackingEnabled: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to mark file modification',
      };
    }
  },
});

// Update task progress tool
export const updateTaskProgressTool = tool({
  name: 'update_task_progress',
  description: 'Update the progress of an active task with current step and completion percentage.',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to update'),
    progress: z.number().min(0).max(100).describe('Progress percentage (0-100)'),
    currentStep: z.string().describe('Description of the current step being worked on'),
    completedActions: z.array(z.string()).default([]).describe('List of actions completed in this step'),
    toolExecutions: z.array(z.object({
      toolName: z.string(),
      status: z.string(),
      startTime: z.string().optional(),
      result: z.string().optional()
    })).default([]).describe('Recent tool executions to record'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { taskId, progress, currentStep, completedActions, toolExecutions } = params;
      
      // Mark the current step as complete if we have completed actions
      if (completedActions.length > 0) {
        for (const action of completedActions) {
          await recoverySystem.markTaskStep(taskId, action, toolExecutions);
        }
      }
      
      // Update overall progress
      const updated = await activeTaskManager.updateTaskProgress(taskId, progress);
      
      if (!updated) {
        return {
          success: false,
          message: `Task ${taskId} not found`,
        };
      }
      
      return {
        success: true,
        taskId,
        progress,
        currentStep,
        message: `Updated task progress to ${progress}% - ${currentStep}`,
        recoveryPointUpdated: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to update task progress',
      };
    }
  },
});

// Check for interrupted tasks tool
export const checkInterruptedTasksTool = tool({
  name: 'check_interrupted_tasks',
  description: 'Check for tasks that may have been interrupted by hot-reload or restart and can be resumed.',
  inputSchema: z.object({
    sessionId: z.string().optional().describe('Filter by session ID'),
    autoResume: z.boolean().default(false).describe('Automatically resume resumable tasks'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { sessionId, autoResume } = params;
      
      const interruptedTasks = await taskResumptionEngine.detectInterruptedTasks();
      
      // Filter by session if provided
      let filteredTasks = interruptedTasks;
      if (sessionId) {
        filteredTasks = interruptedTasks.filter(task => task.sessionId === sessionId);
      }
      
      const resumableTasks = filteredTasks.filter(task => task.isResumable);
      
      if (autoResume && resumableTasks.length > 0) {
        const resumeResults = [];
        
        for (const task of resumableTasks.slice(0, 3)) { // Limit to 3 for safety
          try {
            const result = await taskResumptionEngine.resumeTask(task.taskId);
            resumeResults.push({
              taskId: task.taskId,
              success: result.success,
              message: result.message,
            });
          } catch (error) {
            resumeResults.push({
              taskId: task.taskId,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        }
        
        return {
          success: true,
          interruptedTasks: filteredTasks.length,
          resumableTasks: resumableTasks.length,
          resumeResults,
          message: `Found ${filteredTasks.length} interrupted tasks, attempted to resume ${resumeResults.length}`,
        };
      } else {
        return {
          success: true,
          interruptedTasks: filteredTasks.map(task => ({
            taskId: task.taskId,
            objective: task.metadata.objective.substring(0, 100),
            progress: task.progress,
            isResumable: task.isResumable,
            resumptionConfidence: task.resumptionConfidence,
            lastCheckpoint: task.lastCheckpoint,
          })),
          resumableTasks: resumableTasks.length,
          totalFound: filteredTasks.length,
          message: `Found ${filteredTasks.length} interrupted tasks (${resumableTasks.length} resumable)`,
          suggestion: resumableTasks.length > 0 ? 'Use autoResume: true to automatically resume resumable tasks' : 'No resumable tasks found',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to check for interrupted tasks',
      };
    }
  },
});

// Resume specific task tool
export const resumeTaskTool = tool({
  name: 'resume_task',
  description: 'Resume a specific interrupted task by ID.',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to resume'),
    forceResume: z.boolean().default(false).describe('Force resume even if confidence is low'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { taskId, forceResume } = params;
      
      const result = await taskResumptionEngine.resumeTask(taskId, forceResume);
      
      if (result.success) {
        return {
          success: true,
          taskId: result.taskId,
          resumptionPoint: result.resumedAt,
          recoveryTime: result.recoveryTime,
          nextSteps: result.nextSteps,
          continuationPlan: result.continuationPlan,
          message: result.message,
          resumptionInstructions: `TASK RESUMED: ${result.message}

Next Actions:
${result.nextSteps.map(step => `- ${step}`).join('\n')}

Continuation Plan:
${result.continuationPlan.map(action => `- ${action}`).join('\n')}

Please proceed with the next actions while being aware this is a resumed task.`,
        };
      } else {
        return {
          success: false,
          taskId: result.taskId,
          error: result.error,
          message: result.message,
          suggestion: 'Consider using forceResume: true or creating a new task',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to resume task',
      };
    }
  },
});

// Complete task tool
export const completeTaskTool = tool({
  name: 'complete_task',
  description: 'Mark a task as completed and clean up recovery data.',
  inputSchema: z.object({
    taskId: z.string().describe('ID of the task to complete'),
    outcome: z.string().default('Task completed successfully').describe('Description of the task outcome'),
    finalNotes: z.string().optional().describe('Any final notes or lessons learned'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { taskId, outcome, finalNotes } = params;
      
      const completed = await activeTaskManager.completeTask(taskId, outcome);
      
      if (completed) {
        // Store completion pattern for learning
        if (finalNotes) {
          const task = await activeTaskManager.loadTaskState(taskId);
          if (task) {
            await activeTaskManager.recordSuccessfulRecovery(taskId, 'task_completion');
          }
        }
        
        return {
          success: true,
          taskId,
          outcome,
          message: `Task ${taskId} completed successfully`,
          recoveryDataCleaned: true,
        };
      } else {
        return {
          success: false,
          message: `Task ${taskId} not found or could not be completed`,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to complete task',
      };
    }
  },
});

// Get recovery system status tool
export const getRecoveryStatusTool = tool({
  name: 'get_recovery_status',
  description: 'Get the current status of the recovery system including active tasks and hot-reload detection.',
  inputSchema: z.object({
    includeDetails: z.boolean().default(false).describe('Include detailed information about active tasks'),
  }) as any,
  execute: async (params: any) => {
    try {
      const { includeDetails } = params;
      
      const systemStatus = recoverySystem.getSystemStatus();
      const activeTasks = await activeTaskManager.listActiveTasks();
      
      const status = {
        recoverySystem: systemStatus,
        activeTasks: {
          total: activeTasks.length,
          resumable: activeTasks.filter(t => t.isResumable).length,
          inProgress: activeTasks.filter(t => t.progress > 0 && t.progress < 100).length,
        },
        hotReload: {
          watching: systemStatus.hotReloadDetector.isWatching,
          trackedFiles: systemStatus.hotReloadDetector.trackedFiles,
          recentChanges: systemStatus.hotReloadDetector.recentChanges,
        },
      };
      
      if (includeDetails) {
        (status as any).taskDetails = activeTasks.map(task => ({
          taskId: task.taskId,
          objective: task.metadata.objective.substring(0, 100),
          progress: task.progress,
          currentStep: task.currentStep,
          isResumable: task.isResumable,
          confidence: task.resumptionConfidence,
          lastCheckpoint: task.lastCheckpoint,
          fileModifications: task.fileModifications.length,
          toolExecutions: task.toolExecutions.length,
        }));
      }
      
      return {
        success: true,
        status,
        message: `Recovery system is ${systemStatus.isRunning ? 'active' : 'inactive'} with ${activeTasks.length} active tasks`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get recovery status',
      };
    }
  },
});

// Export all recovery tools
export const recoveryTools = [
  createTaskRecoveryPointTool,
  markFileModificationTool,
  updateTaskProgressTool,
  checkInterruptedTasksTool,
  resumeTaskTool,
  completeTaskTool,
  getRecoveryStatusTool,
];