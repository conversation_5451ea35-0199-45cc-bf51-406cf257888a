/**
 * Contextual Exploration Tools
 * 
 * Tools that allow agents to progressively explore and request more project context
 * as needed, without overwhelming the initial context window.
 */

import { tool } from 'ai';
import { z } from 'zod';
import { ContextualProjectFeeder } from '../utils/contextualProjectFeeder';
import { countTokens } from '../utils/tokenLimiter';
import { promises as fs } from 'fs';
import path from 'path';

// Global instance for the current session
let projectFeeder: ContextualProjectFeeder | null = null;

// Initialize the project feeder
export function initializeProjectFeeder(projectRoot: string) {
  projectFeeder = new ContextualProjectFeeder(projectRoot);
}

/**
 * Request initial project context based on the current task
 */
export const requestProjectContext = tool({
  name: 'request_project_context',
  description: 'Request relevant project context based on your current task. Use this when you need to understand the project structure or find relevant files.',
  inputSchema: z.object({
    taskDescription: z.string().describe('Brief description of what you are trying to accomplish'),
    specificAreas: z.array(z.string()).optional().describe('Specific areas, files, or components you are interested in'),
    model: z.string().default('gpt-5').describe('The model being used (affects token budget)'),
  }) as any,
  execute: async (input: any) => {
    if (!projectFeeder) {
      return {
        success: false,
        error: 'Project feeder not initialized. Please check project configuration.',
      };
    }

    try {
      // Create a conversation history context if specific areas mentioned
      const conversationHistory = input.specificAreas ? [
        { role: 'user', content: `I need help with: ${input.specificAreas.join(', ')}` }
      ] : [];

      const result = await projectFeeder.getContextualProjectInfo(
        input.taskDescription,
        conversationHistory,
        input.model
      );

      return {
        success: true,
        context: result.context,
        reasoning: result.reasoning,
        tokensUsed: result.tokensUsed,
        filesIncluded: result.context.relevantFiles.length,
        summary: `Provided context for ${result.context.relevantFiles.length} relevant files using ${result.tokensUsed} tokens.`
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to generate project context: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

/**
 * Search for specific files or patterns in the project
 */
export const searchProjectFiles = tool({
  name: 'search_project_files',
  description: 'Search for specific files, patterns, or content within the project. Use this when you need to find files related to a specific feature or functionality.',
  inputSchema: z.object({
    searchTerms: z.array(z.string()).describe('Terms to search for in file names or paths'),
    fileExtensions: z.array(z.string()).optional().describe('File extensions to filter by (e.g., [".ts", ".tsx"])'),
    excludePatterns: z.array(z.string()).optional().describe('Patterns to exclude (e.g., ["test", "spec"])'),
    maxResults: z.number().default(20).describe('Maximum number of results to return'),
  }) as any,
  execute: async (input: any) => {
    if (!projectFeeder) {
      return {
        success: false,
        error: 'Project feeder not initialized.',
      };
    }

    try {
      const projectRoot = (projectFeeder as any).projectRoot;
      const foundFiles: Array<{path: string, relevance: number}> = [];

      // Get all project files
      const allFiles = await getAllProjectFiles(projectRoot, 4);

      // Score files based on search terms
      for (const file of allFiles) {
        let relevanceScore = 0;
        const fileName = path.basename(file).toLowerCase();
        const filePath = file.toLowerCase();

        // Check search terms
        for (const term of input.searchTerms) {
          const termLower = term.toLowerCase();
          if (fileName.includes(termLower)) relevanceScore += 3;
          if (filePath.includes(termLower)) relevanceScore += 1;
        }

        // Filter by file extensions
        if (input.fileExtensions && input.fileExtensions.length > 0) {
          const hasMatchingExtension = input.fileExtensions.some((ext: string) => 
            file.endsWith(ext.startsWith('.') ? ext : `.${ext}`)
          );
          if (!hasMatchingExtension) continue;
        }

        // Exclude patterns
        if (input.excludePatterns && input.excludePatterns.length > 0) {
          const shouldExclude = input.excludePatterns.some((pattern: string) => 
            filePath.includes(pattern.toLowerCase())
          );
          if (shouldExclude) continue;
        }

        if (relevanceScore > 0) {
          foundFiles.push({ path: file, relevance: relevanceScore });
        }
      }

      // Sort by relevance and limit results
      const sortedFiles = foundFiles
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, input.maxResults);

      return {
        success: true,
        files: sortedFiles.map(f => ({
          path: path.relative(projectRoot, f.path),
          absolutePath: f.path,
          relevance: f.relevance
        })),
        totalFound: foundFiles.length,
        summary: `Found ${foundFiles.length} files matching your search criteria. Showing top ${sortedFiles.length} results.`
      };
    } catch (error) {
      return {
        success: false,
        error: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

/**
 * Get detailed information about specific files
 */
export const getFileDetails = tool({
  name: 'get_file_details',
  description: 'Get detailed information about specific files including content, size, and relationships. Use this to examine files found through search or context.',
  inputSchema: z.object({
    filePaths: z.array(z.string()).describe('Paths to files you want to examine (relative to project root)'),
    includeContent: z.boolean().default(true).describe('Whether to include file content'),
    maxTokensPerFile: z.number().default(2000).describe('Maximum tokens of content per file'),
    analyzeImports: z.boolean().default(true).describe('Whether to analyze imports and dependencies'),
  }) as any,
  execute: async (input: any) => {
    if (!projectFeeder) {
      return {
        success: false,
        error: 'Project feeder not initialized.',
      };
    }

    try {
      const projectRoot = (projectFeeder as any).projectRoot;
      const fileDetails = [];

      for (const filePath of input.filePaths) {
        const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(projectRoot, filePath);
        
        try {
          const stats = await fs.stat(absolutePath);
          const relativePath = path.relative(projectRoot, absolutePath);
          
          let content = '';
          let truncated = false;
          let imports: string[] = [];

          if (input.includeContent && stats.isFile()) {
            const rawContent = await fs.readFile(absolutePath, 'utf-8');
            const contentTokens = countTokens(rawContent);
            
            if (contentTokens <= input.maxTokensPerFile) {
              content = rawContent;
            } else {
              // Truncate content but preserve structure
              const lines = rawContent.split('\n');
              let currentTokens = 0;
              const selectedLines = [];
              
              for (const line of lines) {
                const lineTokens = countTokens(line);
                if (currentTokens + lineTokens <= input.maxTokensPerFile) {
                  selectedLines.push(line);
                  currentTokens += lineTokens;
                } else {
                  break;
                }
              }
              
              content = selectedLines.join('\n');
              truncated = true;
            }

            // Analyze imports if requested
            if (input.analyzeImports) {
              imports = extractImports(content);
            }
          }

          fileDetails.push({
            path: relativePath,
            absolutePath,
            size: stats.size,
            lastModified: stats.mtime,
            content: input.includeContent ? content : undefined,
            truncated,
            imports: input.analyzeImports ? imports : undefined,
            tokensUsed: input.includeContent ? countTokens(content) : 0,
          });
        } catch (fileError) {
          fileDetails.push({
            path: filePath,
            error: `Failed to read file: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`,
          });
        }
      }

      const totalTokens = fileDetails.reduce((sum, file) => sum + (file.tokensUsed || 0), 0);

      return {
        success: true,
        files: fileDetails,
        totalTokensUsed: totalTokens,
        summary: `Analyzed ${fileDetails.length} files using ${totalTokens} tokens.`
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get file details: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

/**
 * Request expanded context in a specific area
 */
export const expandContext = tool({
  name: 'expand_context',
  description: 'Request more detailed context about a specific area of the project. Use this when you need deeper information about a particular domain or component.',
  inputSchema: z.object({
    area: z.string().describe('The specific area to expand context on (e.g., "authentication", "user management", "API endpoints")'),
    includeTests: z.boolean().default(false).describe('Whether to include test files in the expanded context'),
    maxFiles: z.number().default(15).describe('Maximum number of files to include'),
    tokenBudget: z.number().default(8000).describe('Maximum tokens to use for this expansion'),
  }) as any,
  execute: async (input: any) => {
    if (!projectFeeder) {
      return {
        success: false,
        error: 'Project feeder not initialized.',
      };
    }

    try {
      // Create a focused task analysis
      const taskAnalysis = await projectFeeder.analyzeTask(
        `I need detailed information about ${input.area}`,
        []
      );

      // Generate a more permissive strategy for expansion
      const strategy = projectFeeder.generateContextStrategy(taskAnalysis, 'gpt-5');
      strategy.maxFileCount = input.maxFiles;
      strategy.tokenBudget = input.tokenBudget;
      
      if (input.includeTests) {
        strategy.prioritizePatterns.push('test/', 'spec/', '*.test.*', '*.spec.*');
      }

      // Add area-specific patterns
      strategy.prioritizePatterns.push(
        `*${input.area}*`,
        `${input.area}/`,
        `*${input.area.toLowerCase()}*`
      );

      const selectedFiles = await projectFeeder.selectRelevantFiles(taskAnalysis, strategy);
      const expandedContext = await projectFeeder.generateProjectContext(selectedFiles, taskAnalysis, strategy);

      return {
        success: true,
        expandedContext,
        area: input.area,
        filesIncluded: selectedFiles.length,
        tokensUsed: countTokens(JSON.stringify(expandedContext)),
        summary: `Expanded context for "${input.area}" with ${selectedFiles.length} relevant files.`
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to expand context: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

// Helper functions

async function getAllProjectFiles(projectRoot: string, maxDepth: number): Promise<string[]> {
  const files: string[] = [];
  
  async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
    if (currentDepth > maxDepth) return;

    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory() && !entry.name.startsWith('.') && 
            !['node_modules', 'build', 'dist', '.next', 'coverage'].includes(entry.name)) {
          await scanDirectory(fullPath, currentDepth + 1);
        } else if (entry.isFile()) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }

  await scanDirectory(projectRoot, 0);
  return files;
}

function extractImports(content: string): string[] {
  const imports: string[] = [];
  
  // TypeScript/JavaScript imports
  const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"];?/g;
  let match;
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }

  // Require statements
  const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
  while ((match = requireRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }

  return [...new Set(imports)]; // Remove duplicates
}

// Export all tools
export const contextualExplorationTools = [
  requestProjectContext,
  searchProjectFiles,
  getFileDetails,
  expandContext,
];