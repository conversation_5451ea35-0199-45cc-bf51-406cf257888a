import React, { useEffect, useMemo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Search, 
  FileText, 
  Edit, 
  Code, 
  Globe, 
  GitBranch,
  Users,
  Loader,
  CheckCircle,
  AlertCircle,
  Mic
} from 'lucide-react';
import ComputerUseMonitor from '../ui/components/ComputerUse/ComputerUseMonitor';
import { useAgentStore } from '../ui/stores/agentStore';
import { useProjectStore } from '../ui/stores/projectStore';

export interface AgentEvent {
  type?: string;
  timestamp: string;
  data?: {
    type?: string;
    timestamp?: string;
    message?: string;
    agent?: string;
    tool?: string;
    args?: any;
    result?: any;
  };
}

interface AgentActivityProps {
  events: AgentEvent[];
  isActive: boolean;
  currentOrchestrator?: string;
  orchestrationDetails?: {
    contextUsage?: { used: number; total: number };
    thinkingBudget?: { used: number; total: number };
    activeAgents?: string[];
  };
  onCancelRun?: (agentName?: string) => void;
  onRetryRun?: (agentName?: string) => void;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  thinking: Brain,
  search: Search,
  file_read: FileText,
  file_write: Edit,
  code_execution: Code,
  web_search: Globe,
  git_operation: GitBranch,
  handoff: Users,
  plan_created: Brain,
  delegation_start: Users,
  delegation_end: CheckCircle,
  tool_call: Loader,
  complete: CheckCircle,
  error: AlertCircle,
};

const colorMap: Record<string, string> = {
  thinking: 'text-blue-500',
  search: 'text-purple-500',
  file_read: 'text-green-500',
  file_write: 'text-yellow-500',
  code_execution: 'text-orange-500',
  web_search: 'text-indigo-500',
  git_operation: 'text-pink-500',
  handoff: 'text-cyan-500',
  plan_created: 'text-blue-500',
  delegation_start: 'text-amber-500',
  delegation_end: 'text-green-500',
  tool_call: 'text-gray-500',
  complete: 'text-green-600',
  error: 'text-red-500',
};

// Small pill toggle button used in plan controls
const PlanToggle: React.FC<{ label: string; activeLabel?: string; isActive: boolean; onClick: () => void }>
  = ({ label, activeLabel, isActive, onClick }) => (
    <button
      onClick={onClick}
      className={`px-2 py-0.5 rounded text-[11px] border transition-colors ${
        isActive
          ? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300'
          : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-300'
      }`}
      title={isActive ? (activeLabel || label) : label}
    >
      {isActive ? (activeLabel || label) : label}
    </button>
  );

export const AgentActivity: React.FC<AgentActivityProps> = ({ events, isActive, currentOrchestrator, orchestrationDetails, onCancelRun, onRetryRun }) => {
  const { agents, activeAgents } = useAgentStore();
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});
  const [showAllPlanSteps, setShowAllPlanSteps] = useState(false);
  const [showOnlyBlocked, setShowOnlyBlocked] = useState(false);
  // Filter out malformed events - must be objects with type and timestamp
  // Also accept events with nested event data (from EnhancedStreamHandler format)
  const validEvents = events.filter(event => 
    event && 
    typeof event === 'object' && 
    (
      (typeof event.type === 'string' && typeof event.timestamp === 'string') ||
      (event.data && typeof event.data.type === 'string' && typeof event.data.timestamp === 'string')
    )
  );
  const recentEvents = validEvents.slice(-5);

  // Separate voice-tagged events (from voice SSE) for a small dedicated section
  const voiceEvents = useMemo(() => {
    return validEvents.filter((e: any) => e?.source === 'voice').slice(-5);
  }, [validEvents]);

  // Build delegated agents list with computed freshness and stuck status
  const delegatedAgents = useMemo(() => {
    const list = activeAgents
      .map(name => agents.get(name))
      .filter((a): a is NonNullable<typeof a> => !!a && a.name !== 'Dante');

    const now = Date.now();
    return list.map(a => {
      const lastEntry = a.activityLog[a.activityLog.length - 1];
      const lastTs = lastEntry?.timestamp?.getTime?.() || a.startTime?.getTime?.() || now;
      const idleMs = Math.max(0, now - lastTs);
      const isStuck = a.status !== 'completed' && a.status !== 'error' && idleMs > 20000; // >20s idle
      return {
        ref: a,
        lastTs,
        idleMs,
        isStuck,
      };
    });
  }, [agents, activeAgents]);
  
  // Debug: log valid events to see their types
  React.useEffect(() => {
    if (validEvents.length > 0) {
      console.log('Valid agent events:', validEvents.map(e => ({type: e.type, message: e.data?.message})));
    }
  }, [validEvents.length]);
  
  // Helper to determine if orchestrator is Gemini
  const isGeminiOrchestrator = currentOrchestrator?.startsWith('gemini-') || false;

  // Derive execution plan and step status from events
  const planInfo = useMemo(() => {
    const planEvt = [...validEvents].reverse().find(e => (e.type || e.data?.type) === 'plan_created');
    if (!planEvt) return null as null | { summary?: string; steps: Array<{ id: string; agent?: string; title?: string; description?: string; status: 'pending'|'in_progress'|'completed'|'failed'; reason?: string }> };
    const steps = (planEvt as any).data?.steps || [];
    const result = (steps as any[]).map((s: any) => ({ id: String(s.id), agent: s.agent, title: s.title, description: s.description, status: 'pending' as const }));
    for (const ev of validEvents as any[]) {
      const t = ev.type || ev.data?.type;
      const d: any = (ev as any).data || {};
      if (t === 'delegation_start') {
        const idx = result.findIndex(r => r.id === d.stepId);
        if (idx >= 0) {
          // If this is a synthetic skip, mark failed with reason from description
          if ((d.title || '').toString() === 'Skipped') {
            (result[idx] as any).status = 'failed';
            if (d.description) (result[idx] as any).reason = d.description;
          } else {
            (result[idx] as any).status = 'in_progress';
          }
        }
      } else if (t === 'delegation_end') {
        const idx = result.findIndex(r => r.id === d.stepId);
        if (idx >= 0) (result[idx] as any).status = d.success === false ? 'failed' : 'completed';
      } else if (t === 'delegation_skipped') {
        const idx = result.findIndex(r => r.id === d.stepId);
        if (idx >= 0) {
          (result[idx] as any).status = 'failed';
          if (d.reason) (result[idx] as any).reason = d.reason;
        }
      }
    }
    return { summary: (planEvt as any).data?.summary, steps: result };
  }, [validEvents]);

  // Extract file references from tool results (grep_code/read_file/read_files)
  const fileRefs = useMemo(() => {
    const out = new Set<string>();
    const tryAddFromString = (s: string) => {
      // Match project-relative paths like src/... or any path with .ts/.tsx/.js/.json etc.
      const re = /(?:^|\s)([A-Za-z0-9_./-]+\.(?:ts|tsx|js|jsx|json|md|yml|yaml|css|scss|html))/g;
      let m: RegExpExecArray | null;
      while ((m = re.exec(s)) !== null) {
        const p = m[1];
        if (p && p.length < 200) out.add(p);
      }
    };
    for (const ev of validEvents as any[]) {
      const t = ev.type || ev.data?.type;
      if (t === 'tool_result') {
        const tool = (ev.data?.tool || '').toString();
        if (/grep|read_file|read_files|search|analyze|list_directory/i.test(tool)) {
          const r = ev.data?.result;
          if (typeof r === 'string') tryAddFromString(r);
          else if (r) tryAddFromString(JSON.stringify(r));
        }
      }
    }
    return Array.from(out).slice(0, 20);
  }, [validEvents]);

  return (
    <div className="space-y-4">
      {/* Orchestrator Status */}
      {currentOrchestrator && (
        <div className={`rounded-lg p-4 border ${
          isGeminiOrchestrator
            ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-700'
            : 'bg-gradient-to-r from-blue-50 to-blue-50 dark:from-blue-900/20 dark:to-blue-900/20 border-blue-200 dark:border-blue-700'
        }`}>
          <div className="flex items-center gap-3 mb-3">
            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
              isGeminiOrchestrator
                ? 'bg-gradient-to-r from-purple-500 to-blue-600'
                : 'bg-gradient-to-r from-blue-500 to-blue-700'
            }`}>
              <span className="text-white text-sm font-bold">
                {isGeminiOrchestrator ? '🎭' : '🤖'}
              </span>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                {isGeminiOrchestrator ? 'Gemini Orchestrator' : 'OpenAI Agent'}
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {currentOrchestrator}
              </p>
            </div>
          </div>

          {/* Plan warning if any steps are blocked/failed before start */}
          {planInfo && planInfo.steps.some(s => s.status === 'failed') && (
            <div className="mb-2 p-2 rounded bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
              <span className="text-xs text-yellow-800 dark:text-yellow-200 font-medium">
                Plan has {planInfo.steps.filter(s => s.status === 'failed').length} blocked step(s)
              </span>
            </div>
          )}

          {/* Compact Plan Breakdown */}
          {planInfo && (
            <div className="mb-2">
              <div className="flex items-center justify-between mb-1">
                <p className="text-xs text-gray-600 dark:text-gray-400">Plan Steps</p>
                <div className="flex items-center gap-1">
                  {/* Only blocked toggle */}
                  <PlanToggle
                    label="Only blocked"
                    activeLabel="Blocked only"
                    isActive={showOnlyBlocked}
                    onClick={() => setShowOnlyBlocked(v => !v)}
                  />
                  {/* Expand/Collapse toggle */}
                  <PlanToggle
                    label="Show all"
                    activeLabel="Show less"
                    isActive={showAllPlanSteps}
                    onClick={() => setShowAllPlanSteps(v => !v)}
                  />
                </div>
              </div>

              {(() => {
                const blocked = planInfo.steps.filter(s => s.status === 'failed');
                const list = showOnlyBlocked ? blocked : planInfo.steps;
                if (showOnlyBlocked && blocked.length === 0) {
                  return (
                    <div className="text-[11px] text-gray-500 dark:text-gray-400">No blocked steps</div>
                  );
                }
                const items = showAllPlanSteps ? list : list.slice(0, 8);
                return (
                  <div className="space-y-1">
                {items.map((s, idx) => {
                  const color = s.status === 'completed' ? 'bg-green-500' : s.status === 'in_progress' ? 'bg-amber-500' : s.status === 'failed' ? 'bg-red-500' : 'bg-gray-400';
                  const label = s.title || s.id;
                  const hover = ((s as any).reason as string | undefined) || (s.description as string | undefined) || '';
                  return (
                    <div key={`${s.id}-${idx}`} className="flex items-center gap-2 text-xs text-gray-700 dark:text-gray-300 truncate" title={hover}>
                      <span className={`w-2 h-2 rounded-full ${color}`} />
                      <span className="truncate">[{(s.agent || '').toString().replace(/Agent$/,'').replace(/([a-z])([A-Z])/g,'$1 $2')}] {label}</span>
                    </div>
                  );
                })}
                {!showAllPlanSteps && list.length > 8 && (
                  <div className="text-[11px] text-gray-500 dark:text-gray-400">…and {list.length - 8} more</div>
                )}
                  </div>
                );
              })()}
            </div>
          )}

          {/* Orchestration Metrics */}
          {orchestrationDetails && (
            <div className="space-y-2">
              {orchestrationDetails.contextUsage && (
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-gray-600 dark:text-gray-400">Context Usage</span>
                    <span className="font-medium">
                      {Math.round((orchestrationDetails.contextUsage.used / orchestrationDetails.contextUsage.total) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div
                      className="h-1.5 bg-purple-500 rounded-full transition-all"
                      style={{ width: `${(orchestrationDetails.contextUsage.used / orchestrationDetails.contextUsage.total) * 100}%` }}
                    />
                  </div>
                </div>
              )}

              {orchestrationDetails.thinkingBudget && isGeminiOrchestrator && (
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-gray-600 dark:text-gray-400">Thinking Budget</span>
                    <span className="font-medium">
                      {Math.round((orchestrationDetails.thinkingBudget.used / orchestrationDetails.thinkingBudget.total) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div
                      className="h-1.5 bg-blue-500 rounded-full transition-all"
                      style={{ width: `${(orchestrationDetails.thinkingBudget.used / orchestrationDetails.thinkingBudget.total) * 100}%` }}
                    />
                  </div>
                </div>
              )}

              {orchestrationDetails.activeAgents && orchestrationDetails.activeAgents.length > 0 && (
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Active Agents:</p>
                  <div className="flex flex-wrap gap-1">
                    {orchestrationDetails.activeAgents.map((agent, index) => (
                      <span
                        key={index}
                        className="px-2 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300 text-xs rounded-full"
                      >
                        {agent}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Plan Panel */}
      {planInfo && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Brain size={16} className="text-blue-500" />
            <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Execution Plan</h3>
          </div>
          {planInfo.summary && (
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{planInfo.summary}</p>
          )}
          <div className="space-y-1">
            {planInfo.steps.map((s: any) => (
              <div key={s.id} className="flex items-center justify-between text-xs px-2 py-1 bg-gray-50 dark:bg-gray-900 rounded">
                <div className="truncate">
                  <span className="font-medium text-gray-700 dark:text-gray-200 mr-2">{s.title || s.id}</span>
                  {s.agent && (<span className="text-gray-500 dark:text-gray-400">{s.agent}</span>)}
                </div>
                <div>
                  <span className={`px-1.5 py-0.5 rounded text-[10px] ${
                    s.status === 'completed' ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' :
                    s.status === 'in_progress' ? 'bg-amber-100 dark:bg-amber-900 text-amber-700 dark:text-amber-300' :
                    s.status === 'failed' ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300' :
                    'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200'
                  }`}>{s.status.replace('_',' ')}</span>
                </div>
              </div>
            ))}
          </div>

          {fileRefs.length > 0 && (
            <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-1">Files referenced</p>
              <div className="flex flex-wrap gap-1.5">
                {fileRefs.map((p) => (
                  <button
                    key={p}
                    className="text-[11px] px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                    title={p}
                    onClick={() => {
                      try { useProjectStore.getState().addRecentFile(p); } catch {}
                      console.log('File clicked:', p);
                    }}
                  >
                    {p}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Regular Agent Activity */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        {/* Voice Session Activity (if present) */}
        {voiceEvents.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Mic size={16} className="text-indigo-500" />
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Voice Activity</h3>
            </div>
            <div className="space-y-2">
              {voiceEvents.map((event, idx) => {
                const etype = event.type || event.data?.type || 'message';
                const etext = event.data?.message || etype.replace(/_/g, ' ');
                const ts = event.timestamp || event.data?.timestamp || new Date().toISOString();
                return (
                  <div key={idx} className="flex items-start justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                    <div className="text-xs text-gray-600 dark:text-gray-300 truncate pr-2">{etext}</div>
                    <div className="text-[10px] text-gray-400 dark:text-gray-500 whitespace-nowrap">{new Date(ts).toLocaleTimeString()}</div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        {/* Delegated Agents */}
        {delegatedAgents.length > 0 && (
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Delegated Agents</h3>
            <div className="space-y-2">
              {delegatedAgents.map(({ ref: a, idleMs, isStuck }) => (
                <div key={a.name} className="p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 min-w-0">
                      <span className={`w-2 h-2 rounded-full ${
                        a.status === 'error' ? 'bg-red-500' : a.status === 'completed' ? 'bg-green-500' : a.status === 'thinking' ? 'bg-blue-500' : 'bg-yellow-500'
                      }`} />
                      <span className="text-sm font-medium text-gray-800 dark:text-gray-100 truncate">{a.name}</span>
                      {a.currentTool && (
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 truncate">{a.currentTool.replace(/_/g, ' ')}</span>
                      )}
                      {typeof a.progress === 'number' && (
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">{Math.round(a.progress)}%</span>
                      )}
                      {isStuck && (
                        <span className="ml-2 text-xs text-red-500">possibly stuck</span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">idle {Math.round(idleMs/1000)}s</span>
                      {onRetryRun && (
                        <button
                          className="text-xs px-2 py-1 rounded bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800"
                          onClick={() => onRetryRun(a.name)}
                          title="Retry this agent's task"
                        >
                          Retry
                        </button>
                      )}
                      {onCancelRun && (
                        <button
                          className="text-xs px-2 py-1 rounded bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-800"
                          onClick={() => onCancelRun(a.name)}
                          title="Cancel current run"
                        >
                          Cancel
                        </button>
                      )}
                      <button
                        className="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                        onClick={() => setExpanded(e => ({ ...e, [a.name]: !e[a.name] }))}
                      >
                        {expanded[a.name] ? 'Hide' : 'Peek'}
                      </button>
                    </div>
                  </div>
                  {expanded[a.name] && (
                    <div className="mt-2 border-t border-gray-100 dark:border-gray-700 pt-2">
                      {typeof a.progress === 'number' && (
                        <div className="mb-2">
                          <div className="flex justify-between text-[10px] mb-1 text-gray-500 dark:text-gray-400">
                            <span>{a.progressMessage || 'Progress'}</span>
                            <span>{Math.round(a.progress)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded h-1.5">
                            <div className="h-1.5 bg-blue-500 rounded transition-all" style={{ width: `${Math.round(a.progress)}%` }} />
                          </div>
                        </div>
                      )}
                      {(a.activityLog.slice(-5)).map((entry, idx) => (
                        <div key={idx} className="flex items-start justify-between text-xs text-gray-600 dark:text-gray-300 py-0.5">
                          <div className="pr-2 truncate">
                            <span className="uppercase text-[10px] mr-2 text-gray-500">{entry.type}</span>
                            <span className="truncate inline-block align-top">{entry.content}</span>
                          </div>
                          <span className="text-gray-400 dark:text-gray-500">{new Date(entry.timestamp).toLocaleTimeString()}</span>
                        </div>
                      ))}
                      {a.status === 'error' && (
                        <div className="text-xs text-red-500 mt-1">Encountered an error</div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
            Recent Activity
          </h3>
          {isActive && (
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="flex items-center gap-2"
            >
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-xs text-green-500">Active</span>
            </motion.div>
          )}
        </div>
      
      <div className="space-y-2">
        <AnimatePresence mode="popLayout">
          {recentEvents.map((event, index) => {
            // Handle both direct event format and nested event format
            const eventType = event.type || event.data?.type || 'unknown';
            const eventData = event.data || {};
            const title = (eventData as any).title as string | undefined;
            const description = (eventData as any).description as string | undefined;
            const eventTimestamp = event.timestamp || event.data?.timestamp || new Date().toISOString();
            
            const Icon = iconMap[eventType] || Loader;
            const color = colorMap[eventType] || 'text-gray-500';
            
            return (
              <motion.div
                key={`${eventTimestamp}-${index}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="flex items-start gap-3 p-2 bg-white dark:bg-gray-800 rounded"
              >
                <div className={`mt-0.5 ${color}`}>
                  <Icon size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {eventData.message || title || eventType.replace(/_/g, ' ') || 'Unknown event'}
                  </p>
                  {eventData.agent && eventData.agent !== 'Dante' && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
                      Agent: {eventData.agent}
                    </p>
                  )}
                  {eventData.tool && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
                      Tool: {eventData.tool?.replace(/_/g, ' ') || eventData.tool}
                    </p>
                  )}
                  {/* Skip reason badge for delegation skips */}
                  {eventType === 'delegation_start' && title === 'Skipped' && (
                    <div className="mt-1 inline-flex items-center gap-1 text-[10px] px-1.5 py-0.5 rounded bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200" title={description || 'Blocked step skipped'}>
                      <AlertCircle size={12} className="inline mr-1" />
                      {description || 'Blocked step skipped'}
                    </div>
                  )}
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-600">
                  {new Date(eventTimestamp).toLocaleTimeString()}
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
        
        {recentEvents.length === 0 && !isActive && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-600 text-sm">
            No recent activity
          </div>
        )}
      </div>
      
        {/* Computer Use Tasks Monitor (opt-in via env flag) */}
        {import.meta.env.VITE_SHOW_COMPUTER_USE_MONITOR === 'true' && (
          <div className="mt-4">
            <ComputerUseMonitor 
              showIsolatedTasks={true}
              refreshInterval={5000}
              className="mb-4"
            />
          </div>
        )}
      </div>
    </div>
  );
};
