import React from 'react';
import { motion } from 'framer-motion';
import { Brain, Sparkles } from 'lucide-react';

interface ThinkingIndicatorProps {
  message?: string;
  variant?: 'default' | 'minimal' | 'detailed';
}

export const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ 
  message = '<PERSON> is thinking...', 
  variant = 'default' 
}) => {
  if (variant === 'minimal') {
    return (
      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <Brain size={16} />
        </motion.div>
        <span className="text-sm">{message}</span>
      </div>
    );
  }
  
  if (variant === 'detailed') {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Brain className="text-blue-600 dark:text-blue-400" size={20} />
            </motion.div>
            <span className="text-blue-900 dark:text-blue-100 font-medium">
              {message}
            </span>
          </div>
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <Sparkles className="text-purple-500" size={16} />
          </motion.div>
        </div>
        
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex-1"
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>
      </div>
    );
  }
  
  // Default variant
  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <motion.div
        className="relative"
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
      >
        <Brain className="text-blue-500" size={20} />
        <motion.div
          className="absolute inset-0"
          animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Brain className="text-blue-300" size={20} />
        </motion.div>
      </motion.div>
      
      <div className="flex-1">
        <p className="text-sm text-gray-700 dark:text-gray-300">{message}</p>
        <div className="flex gap-1 mt-1">
          {[0, 1, 2].map((i) => (
            <motion.span
              key={i}
              className="w-1 h-1 bg-blue-500 rounded-full"
              animate={{ opacity: [0, 1, 0] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.3,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};