/**
 * Service Auto-Recovery System
 * Provides automatic recovery mechanisms for critical services
 */

import { EventEmitter } from 'events';
import { exec } from 'child_process';
import { promisify } from 'util';
import { systemHealthMonitor, ServiceHealth } from '../services/SystemHealthMonitor';
import { mcpServerManager } from '../mcp/MCPServerManager';
import { config } from '../utils/config';
import { 
  isPortInUse,
  canConnectToPort,
  findProcessUsingPort
} from '../utils/platformUtils';

const execAsync = promisify(exec);

export interface RecoveryAction {
  id: string;
  type: 'restart_service' | 'reconnect_mcp' | 'kill_process' | 'start_process' | 'fix_config' | 'custom';
  serviceName: string;
  description: string;
  command?: string;
  customAction?: () => Promise<boolean>;
  timeout?: number;
  retries?: number;
}

export interface RecoveryPlan {
  id: string;
  serviceName: string;
  issue: string;
  actions: RecoveryAction[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedTime: number;
  successRate: number; // Based on historical data
}

export interface RecoveryExecution {
  planId: string;
  startTime: Date;
  endTime?: Date;
  success: boolean;
  actionsExecuted: string[];
  errors: string[];
  finalStatus: string;
}

export class ServiceRecovery extends EventEmitter {
  private recoveryPlans: Map<string, RecoveryPlan> = new Map();
  private executionHistory: RecoveryExecution[] = [];
  private activeRecoveries: Set<string> = new Set();
  private recoveryStats: Map<string, { attempts: number; successes: number; lastSuccess?: Date }> = new Map();

  constructor() {
    super();
    this.initializeDefaultRecoveryPlans();
    this.startAutoRecoveryMonitoring();
  }

  /**
   * Initialize default recovery plans for common services
   */
  private initializeDefaultRecoveryPlans(): void {
    // MCP Server Recovery Plan
    this.addRecoveryPlan({
      id: 'mcp_server_disconnect',
      serviceName: 'MCP Server Manager',
      issue: 'MCP servers disconnected or failed to connect',
      priority: 'high',
      estimatedTime: 15000, // 15 seconds
      successRate: 0.85,
      actions: [
        {
          id: 'check_mcp_status',
          type: 'custom',
          serviceName: 'MCP Server Manager',
          description: 'Check MCP server statuses',
          customAction: async () => {
            const statuses = mcpServerManager.getAllServerStatuses();
            const disconnected = statuses.filter(s => s.status !== 'connected');
            this.emit('recoveryLog', `Found ${disconnected.length} disconnected MCP servers`);
            return disconnected.length > 0;
          },
        },
        {
          id: 'reconnect_mcp_servers',
          type: 'reconnect_mcp',
          serviceName: 'MCP Server Manager',
          description: 'Reconnect all disconnected MCP servers',
          timeout: 10000,
          retries: 2,
        },
        {
          id: 'verify_mcp_tools',
          type: 'custom',
          serviceName: 'MCP Server Manager',
          description: 'Verify MCP tools are available',
          customAction: async () => {
            // Wait a moment for connections to stabilize
            await new Promise(resolve => setTimeout(resolve, 2000));
            const statuses = mcpServerManager.getAllServerStatuses();
            const connected = statuses.filter(s => s.status === 'connected');
            this.emit('recoveryLog', `${connected.length} MCP servers now connected`);
            return connected.length > 0;
          },
        },
      ],
    });

    // Tool Server Recovery Plan
    this.addRecoveryPlan({
      id: 'tool_server_not_running',
      serviceName: 'Tool Server',
      issue: 'Tool server is not running or not responding',
      priority: 'critical',
      estimatedTime: 20000, // 20 seconds
      successRate: 0.75,
      actions: [
        {
          id: 'check_tool_server_port',
          type: 'custom',
          serviceName: 'Tool Server',
          description: 'Check if tool server port is in use',
          customAction: async () => {
            const portInUse = await isPortInUse(8000);
            const canConnect = await canConnectToPort(8000, 'localhost', 2000);
            return portInUse || canConnect;
          },
        },
        {
          id: 'start_tool_server',
          type: 'start_process',
          serviceName: 'Tool Server',
          description: 'Start the tool server',
          command: 'bun run tool-server', // This would be configured based on the actual setup
          timeout: 15000,
        },
        {
          id: 'verify_tool_server',
          type: 'custom',
          serviceName: 'Tool Server',
          description: 'Verify tool server is responding',
          customAction: async () => {
            try {
              // This would depend on the specific tool server implementation
              const response = await fetch('http://localhost:8000/health', {
                signal: AbortSignal.timeout(5000),
              });
              return response.ok;
            } catch {
              return false;
            }
          },
        },
      ],
    });

    // API Server Recovery Plan
    this.addRecoveryPlan({
      id: 'api_server_not_responding',
      serviceName: 'API Server',
      issue: 'API server is not responding or has crashed',
      priority: 'high',
      estimatedTime: 10000, // 10 seconds
      successRate: 0.9,
      actions: [
        {
          id: 'check_api_port',
          type: 'custom',
          serviceName: 'API Server',
          description: 'Check if API port is listening',
          customAction: async () => {
            const port = config.api?.port || 3001;
            const portInUse = await isPortInUse(port);
            const canConnect = await canConnectToPort(port, 'localhost', 2000);
            return portInUse || canConnect;
          },
        },
        {
          id: 'test_api_endpoint',
          type: 'custom',
          serviceName: 'API Server',
          description: 'Test API endpoint health',
          customAction: async () => {
            const port = config.api?.port || 3001;
            try {
              const response = await fetch(`http://localhost:${port}/health`, {
                signal: AbortSignal.timeout(5000),
              });
              return response.ok;
            } catch {
              return false;
            }
          },
        },
      ],
    });

    // Port Conflict Recovery Plan
    this.addRecoveryPlan({
      id: 'port_conflict',
      serviceName: 'System',
      issue: 'Port conflict detected (EADDRINUSE)',
      priority: 'medium',
      estimatedTime: 8000, // 8 seconds
      successRate: 0.8,
      actions: [
        {
          id: 'identify_port_conflict',
          type: 'custom',
          serviceName: 'System',
          description: 'Identify process using the conflicted port',
          customAction: async () => {
            // This would be determined from the error context
            // For now, we'll check common ports
            const commonPorts = [3001, 8000, 3000];
            for (const port of commonPorts) {
              const processInfo = await findProcessUsingPort(port);
              if (processInfo) {
                const details = processInfo.name 
                  ? `${processInfo.name} (PID: ${processInfo.pid || 'unknown'})`
                  : `PID: ${processInfo.pid || 'unknown'}`;
                this.emit('recoveryLog', `Port ${port} is in use by: ${details}`);
              }
            }
            return true;
          },
        },
        {
          id: 'kill_conflicting_process',
          type: 'kill_process',
          serviceName: 'System',
          description: 'Kill process using conflicted port',
          timeout: 5000,
        },
      ],
    });
  }

  /**
   * Add a recovery plan
   */
  public addRecoveryPlan(plan: RecoveryPlan): void {
    this.recoveryPlans.set(plan.id, plan);
    this.emit('planAdded', plan);
  }

  /**
   * Remove a recovery plan
   */
  public removeRecoveryPlan(planId: string): void {
    this.recoveryPlans.delete(planId);
    this.emit('planRemoved', planId);
  }

  /**
   * Start auto-recovery monitoring
   */
  private startAutoRecoveryMonitoring(): void {
    // Listen for system health alerts
    systemHealthMonitor.on('serviceError', (service: ServiceHealth) => {
      this.handleServiceError(service);
    });

    systemHealthMonitor.on('criticalAlert', () => {
      this.handleCriticalAlert();
    });

    // Start monitoring if not already started
    systemHealthMonitor.startMonitoring().catch(error => {
      console.error('Failed to start system health monitoring:', error);
    });
  }

  /**
   * Handle service error
   */
  private async handleServiceError(service: ServiceHealth): Promise<void> {
    if (this.activeRecoveries.has(service.name)) {
      this.emit('recoveryLog', `Recovery already in progress for ${service.name}`);
      return;
    }

    const plan = this.findRecoveryPlan(service);
    if (!plan) {
      this.emit('recoveryLog', `No recovery plan found for ${service.name}`);
      return;
    }

    this.emit('recoveryLog', `Attempting automatic recovery for ${service.name}`);
    await this.executeRecoveryPlan(plan, service);
  }

  /**
   * Handle critical system alert
   */
  private async handleCriticalAlert(): Promise<void> {
    this.emit('recoveryLog', 'Critical system alert - attempting comprehensive recovery');
    
    const status = systemHealthMonitor.getSystemStatus();
    const failedServices = status.services.filter(s => s.status === 'failed');
    
    // Execute recovery plans for all failed services
    const recoveryPromises = failedServices.map(service => {
      const plan = this.findRecoveryPlan(service);
      return plan ? this.executeRecoveryPlan(plan, service) : Promise.resolve(false);
    });

    const results = await Promise.all(recoveryPromises);
    const successCount = results.filter(r => r).length;
    
    this.emit('recoveryLog', `Critical recovery completed: ${successCount}/${failedServices.length} services recovered`);
  }

  /**
   * Find appropriate recovery plan for a service
   */
  private findRecoveryPlan(service: ServiceHealth): RecoveryPlan | null {
    // Look for exact service match first
    for (const plan of this.recoveryPlans.values()) {
      if (plan.serviceName === service.name) {
        return plan;
      }
    }

    // Look for service type matches
    const typeMatches: Record<string, string> = {
      'mcp_server': 'mcp_server_disconnect',
      'api_endpoint': 'api_server_not_responding',
      'tool_server': 'tool_server_not_running',
    };

    const planId = typeMatches[service.type];
    if (planId && this.recoveryPlans.has(planId)) {
      return this.recoveryPlans.get(planId)!;
    }

    // Look for error-specific matches
    if (service.error) {
      if (service.error.toLowerCase().includes('eaddrinuse') || service.error.includes('port')) {
        return this.recoveryPlans.get('port_conflict') || null;
      }
    }

    return null;
  }

  /**
   * Execute a recovery plan
   */
  public async executeRecoveryPlan(plan: RecoveryPlan, service?: ServiceHealth): Promise<boolean> {
    if (this.activeRecoveries.has(plan.serviceName)) {
      this.emit('recoveryLog', `Recovery already active for ${plan.serviceName}`);
      return false;
    }

    this.activeRecoveries.add(plan.serviceName);
    
    const execution: RecoveryExecution = {
      planId: plan.id,
      startTime: new Date(),
      success: false,
      actionsExecuted: [],
      errors: [],
      finalStatus: 'in_progress',
    };

    try {
      this.emit('recoveryStarted', { plan, service });
      this.emit('recoveryLog', `Starting recovery plan: ${plan.id} for ${plan.serviceName}`);

      for (const action of plan.actions) {
        try {
          this.emit('recoveryLog', `Executing: ${action.description}`);
          const success = await this.executeRecoveryAction(action);
          
          execution.actionsExecuted.push(action.id);
          
          if (!success) {
            execution.errors.push(`Action failed: ${action.id}`);
            this.emit('recoveryLog', `Action failed: ${action.description}`);
            
            // If a critical action fails, stop the recovery
            if (action.retries === undefined || action.retries <= 0) {
              break;
            }
          } else {
            this.emit('recoveryLog', `Action succeeded: ${action.description}`);
          }
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error);
          execution.errors.push(`Action error: ${action.id} - ${errorMsg}`);
          this.emit('recoveryLog', `Action error: ${action.description} - ${errorMsg}`);
        }
      }

      // Check if recovery was successful
      const finalSuccess = await this.verifyRecoverySuccess(plan, service);
      execution.success = finalSuccess;
      execution.finalStatus = finalSuccess ? 'success' : 'failed';
      
      // Update recovery stats
      this.updateRecoveryStats(plan.id, finalSuccess);
      
      this.emit('recoveryLog', `Recovery ${finalSuccess ? 'succeeded' : 'failed'}: ${plan.serviceName}`);
      this.emit('recoveryCompleted', { plan, service, execution });
      
      return finalSuccess;
    } catch (error) {
      execution.errors.push(`Recovery error: ${error}`);
      execution.finalStatus = 'error';
      
      this.emit('recoveryLog', `Recovery error for ${plan.serviceName}: ${error}`);
      this.emit('recoveryCompleted', { plan, service, execution });
      
      return false;
    } finally {
      execution.endTime = new Date();
      this.executionHistory.push(execution);
      
      // Keep only last 100 executions
      if (this.executionHistory.length > 100) {
        this.executionHistory.shift();
      }
      
      this.activeRecoveries.delete(plan.serviceName);
    }
  }

  /**
   * Execute a single recovery action
   */
  private async executeRecoveryAction(action: RecoveryAction): Promise<boolean> {
    const timeout = action.timeout || 10000; // 10 second default timeout
    
    try {
      switch (action.type) {
        case 'restart_service':
          return await this.restartService(action);
        
        case 'reconnect_mcp':
          return await this.reconnectMCPServers();
        
        case 'kill_process':
          return await this.killConflictingProcess(action);
        
        case 'start_process':
          return await this.startProcess(action);
        
        case 'fix_config':
          return await this.fixConfiguration(action);
        
        case 'custom':
          if (action.customAction) {
            return await Promise.race([
              action.customAction(),
              new Promise<boolean>((_, reject) => 
                setTimeout(() => reject(new Error('Action timeout')), timeout)
              ),
            ]);
          }
          return false;
        
        default:
          this.emit('recoveryLog', `Unknown action type: ${action.type}`);
          return false;
      }
    } catch (error) {
      this.emit('recoveryLog', `Action execution failed: ${error}`);
      return false;
    }
  }

  /**
   * Restart a service
   */
  private async restartService(action: RecoveryAction): Promise<boolean> {
    // This would depend on the specific service implementation
    // For now, we'll use the system health monitor's recovery mechanism
    try {
      const result = await systemHealthMonitor.attemptRecovery(action.serviceName);
      return result.success;
    } catch {
      return false;
    }
  }

  /**
   * Reconnect MCP servers
   */
  private async reconnectMCPServers(): Promise<boolean> {
    try {
      const statuses = mcpServerManager.getAllServerStatuses();
      const disconnected = statuses.filter(s => s.status !== 'connected');
      
      let successCount = 0;
      for (const server of disconnected) {
        try {
          await mcpServerManager.connectServer(server.id);
          successCount++;
        } catch (error) {
          this.emit('recoveryLog', `Failed to reconnect ${server.name}: ${error}`);
        }
      }
      
      return successCount > 0;
    } catch {
      return false;
    }
  }

  /**
   * Kill conflicting process
   */
  private async killConflictingProcess(_action: RecoveryAction): Promise<boolean> {
    // This would need more context about which process to kill
    // For safety, we won't implement automatic process killing
    this.emit('recoveryLog', 'Process killing requires manual intervention for safety');
    return false;
  }

  /**
   * Start a process
   */
  private async startProcess(action: RecoveryAction): Promise<boolean> {
    if (!action.command) {
      return false;
    }

    try {
      const { stdout, stderr } = await execAsync(action.command);
      this.emit('recoveryLog', `Process started: ${stdout || stderr}`);
      return true;
    } catch (error) {
      this.emit('recoveryLog', `Failed to start process: ${error}`);
      return false;
    }
  }

  /**
   * Fix configuration
   */
  private async fixConfiguration(_action: RecoveryAction): Promise<boolean> {
    // Configuration fixes would be specific to each service
    // This is a placeholder for now
    this.emit('recoveryLog', 'Configuration fixes require manual implementation');
    return false;
  }

  /**
   * Verify recovery success
   */
  private async verifyRecoverySuccess(_plan: RecoveryPlan, service?: ServiceHealth): Promise<boolean> {
    // Wait a moment for changes to take effect
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (service) {
      // Re-check the specific service
      const diagnosis = await systemHealthMonitor.diagnoseService(service.name);
      return diagnosis.service?.status === 'healthy';
    }
    
    // Check overall system health
    const status = systemHealthMonitor.getSystemStatus();
    return status.overall !== 'critical';
  }

  /**
   * Update recovery statistics
   */
  private updateRecoveryStats(planId: string, success: boolean): void {
    const stats = this.recoveryStats.get(planId) || { attempts: 0, successes: 0 };
    
    stats.attempts++;
    if (success) {
      stats.successes++;
      stats.lastSuccess = new Date();
    }
    
    this.recoveryStats.set(planId, stats);
    
    // Update plan success rate
    const plan = this.recoveryPlans.get(planId);
    if (plan) {
      plan.successRate = stats.successes / stats.attempts;
    }
  }

  /**
   * Get recovery statistics
   */
  public getRecoveryStats(): {
    totalPlans: number;
    totalExecutions: number;
    successfulExecutions: number;
    activeRecoveries: number;
    planStats: Array<{
      planId: string;
      serviceName: string;
      attempts: number;
      successes: number;
      successRate: number;
      lastSuccess?: Date;
    }>;
  } {
    const totalExecutions = this.executionHistory.length;
    const successfulExecutions = this.executionHistory.filter(e => e.success).length;
    
    const planStats = Array.from(this.recoveryStats.entries()).map(([planId, stats]) => {
      const plan = this.recoveryPlans.get(planId);
      return {
        planId,
        serviceName: plan?.serviceName || 'Unknown',
        attempts: stats.attempts,
        successes: stats.successes,
        successRate: stats.successes / stats.attempts,
        lastSuccess: stats.lastSuccess,
      };
    });

    return {
      totalPlans: this.recoveryPlans.size,
      totalExecutions,
      successfulExecutions,
      activeRecoveries: this.activeRecoveries.size,
      planStats,
    };
  }

  /**
   * Get execution history
   */
  public getExecutionHistory(): RecoveryExecution[] {
    return [...this.executionHistory];
  }

  /**
   * Get available recovery plans
   */
  public getRecoveryPlans(): RecoveryPlan[] {
    return Array.from(this.recoveryPlans.values());
  }
}

// Export singleton instance
export const serviceRecovery = new ServiceRecovery();