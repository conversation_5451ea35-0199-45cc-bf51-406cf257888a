import { EventEmitter } from 'events';
import {
  ActiveTaskState,
  TaskResumptionResult,
  ResumptionPoint,
  HotReloadEvent,
  RecoveryError,
  AgentContext,
  RecoveryConfiguration,
} from './types';
import { activeTaskManager } from './ActiveTaskManager';
import { contextPreserver } from './ContextPreserver';
import { hotReloadDetector } from './HotReloadDetector';
import { reminderManager } from '../reminder/ReminderManager';
import { memoryManager } from '../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../memory/types';

export class TaskResumptionEngine extends EventEmitter {
  private resumptionQueue = new Map<string, { task: ActiveTaskState; priority: number }>();
  private isProcessingQueue = false;
  private processingInterval?: NodeJS.Timeout;
  private resumptionAttempts = new Map<string, number>();

  constructor(private config: RecoveryConfiguration) {
    super();
    this.setupEventListeners();
    this.setupProcessingInterval();
  }

  async initialize(): Promise<void> {
    console.log('🚀 Initializing Task Resumption Engine...');

    try {
      // Check for tasks that need recovery on startup
      await this.detectInterruptedTasks();
      
      console.log('✅ Task Resumption Engine initialized');
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Task Resumption Engine:', error);
      throw error;
    }
  }

  async detectInterruptedTasks(): Promise<ActiveTaskState[]> {
    try {
      console.log('🔍 Detecting interrupted tasks...');
      
      const allTasks = await activeTaskManager.listActiveTasks();
      const interruptedTasks: ActiveTaskState[] = [];
      
      for (const task of allTasks) {
        if (await this.isTaskInterrupted(task)) {
          interruptedTasks.push(task);
          console.log(`🔄 Found interrupted task: ${task.taskId} (${task.metadata.objective.substring(0, 50)}...)`);
        }
      }
      
      if (interruptedTasks.length > 0) {
        console.log(`📋 Found ${interruptedTasks.length} interrupted tasks`);
        
        if (this.config.autoResumeEnabled && !this.config.requireUserConfirmation) {
          // Auto-resume tasks
          for (const task of interruptedTasks) {
            await this.queueTaskForResumption(task, this.calculateResumptionPriority(task));
          }
        } else {
          // Emit event for user confirmation
          this.emit('interruptedTasksDetected', interruptedTasks);
        }
      }
      
      return interruptedTasks;
    } catch (error) {
      console.error('Failed to detect interrupted tasks:', error);
      return [];
    }
  }

  async resumeTask(taskId: string, forceResume: boolean = false): Promise<TaskResumptionResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Attempting to resume task: ${taskId}`);
      
      // Load task state
      const task = await activeTaskManager.loadTaskState(taskId);
      if (!task) {
        throw new RecoveryError(taskId, 'Task state not found', false);
      }
      
      // Validate resumability
      if (!forceResume && !await this.validateResumability(task)) {
        const confidence = task.resumptionConfidence;
        throw new RecoveryError(
          taskId, 
          `Task not resumable (confidence: ${confidence})`, 
          confidence > 0.3,
          confidence > 0.3 ? 'Try force resume or restart task' : 'Consider restarting task'
        );
      }
      
      // Check resumption attempts
      const attempts = this.resumptionAttempts.get(taskId) || 0;
      if (attempts >= this.config.maxRecoveryAttempts) {
        throw new RecoveryError(taskId, 'Maximum resumption attempts exceeded', false);
      }
      
      this.resumptionAttempts.set(taskId, attempts + 1);
      
      // Reconstruct context
      const reconstructedContext = await this.reconstructTaskContext(task);
      
      // Create resumption point
      const resumptionPoint = await this.createOptimalResumptionPoint(task);
      
      // Generate resumption instructions
      const resumptionInstructions = await this.generateResumptionInstructions(task, resumptionPoint);
      
      // Update task with new context
      await activeTaskManager.updateTaskProgress(
        taskId, 
        task.progress,
        {
          messageIndex: reconstructedContext.messages.length - 1,
          agentContext: reconstructedContext.agentContext,
          toolExecutions: task.toolExecutions,
          taskProgress: task.progress,
          milestone: 'task_resumed',
          timestamp: new Date(),
        }
      );
      
      // Record successful recovery pattern
      await activeTaskManager.recordSuccessfulRecovery(taskId, resumptionPoint.recoveryStrategy);
      
      // Clear resumption attempts
      this.resumptionAttempts.delete(taskId);
      
      const recoveryTime = Date.now() - startTime;
      
      const result: TaskResumptionResult = {
        success: true,
        taskId,
        resumedAt: resumptionPoint,
        recoveryTime,
        message: `Task successfully resumed at ${resumptionPoint.description}`,
        nextSteps: resumptionPoint.nextActions,
        newContext: reconstructedContext,
        updatedProgress: task.progress,
        continuationPlan: this.generateContinuationPlan(task, resumptionPoint),
      };
      
      console.log(`✅ Successfully resumed task: ${taskId} (${recoveryTime}ms)`);
      this.emit('taskResumed', result);
      
      return result;
      
    } catch (error) {
      const recoveryTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Record failed recovery
      await activeTaskManager.recordFailedRecovery(taskId, errorMessage);
      
      const result: TaskResumptionResult = {
        success: false,
        taskId,
        resumedAt: {
          type: 'analysis_phase',
          description: 'Recovery failed',
          lastSuccessfulAction: 'recovery_attempted',
          nextActions: ['analyze_failure', 'restart_or_abandon'],
          context: { error: errorMessage },
          recoveryStrategy: 'escalate',
        },
        recoveryTime,
        message: `Failed to resume task: ${errorMessage}`,
        nextSteps: ['analyze_recovery_failure', 'consider_manual_intervention'],
        error: errorMessage,
        updatedProgress: 0,
        continuationPlan: ['manual_review_required'],
      };
      
      console.error(`❌ Failed to resume task: ${taskId} - ${errorMessage}`);
      this.emit('taskResumptionFailed', result);
      
      return result;
    }
  }

  async handleHotReloadEvent(event: HotReloadEvent): Promise<void> {
    try {
      console.log(`🔥 Handling hot-reload event: ${event.trigger}`);
      
      // Find tasks that might be affected by this hot-reload
      const affectedTasks = await this.findTasksAffectedByHotReload(event);
      
      if (affectedTasks.length === 0) {
        console.log('No tasks affected by hot-reload event');
        return;
      }
      
      console.log(`📋 Found ${affectedTasks.length} tasks affected by hot-reload`);
      
      // Update hot-reload tracking
      for (const task of affectedTasks) {
        task.metadata.actualHotReloads++;
        
        // If task is hot-reload tolerant, queue for immediate resumption
        if (task.metadata.hotReloadTolerant) {
          await this.queueTaskForResumption(task, 10); // High priority
        } else {
          // Mark as potentially interrupted
          task.isResumable = false;
          task.resumptionConfidence *= 0.5; // Reduce confidence
        }
        
        await activeTaskManager.saveTaskState(task);
      }
      
      // Update event with related tasks
      event.relatedTasks = affectedTasks.map(t => t.taskId);
      event.recovered = affectedTasks.some(t => t.metadata.hotReloadTolerant);
      
      this.emit('hotReloadHandled', event, affectedTasks);
      
    } catch (error) {
      console.error('Failed to handle hot-reload event:', error);
    }
  }

  async queueTaskForResumption(task: ActiveTaskState, priority: number = 1): Promise<void> {
    this.resumptionQueue.set(task.taskId, { task, priority });
    console.log(`📥 Queued task for resumption: ${task.taskId} (priority: ${priority})`);
    
    if (!this.isProcessingQueue) {
      this.processResumptionQueue();
    }
  }

  // Private methods
  private async isTaskInterrupted(task: ActiveTaskState): Promise<boolean> {
    // Task is considered interrupted if:
    
    // 1. It's been inactive for more than the recovery timeout
    const inactiveTime = Date.now() - task.lastCheckpoint.getTime();
    const timeoutMs = this.config.recoveryTimeout * 60 * 1000;
    
    if (inactiveTime > timeoutMs) {
      return true;
    }
    
    // 2. It has incomplete tool executions
    const incompleteTools = task.toolExecutions.filter(te => 
      te.status === 'running' || te.status === 'pending'
    );
    
    if (incompleteTools.length > 0) {
      return true;
    }
    
    // 3. Expected hot-reloads don't match actual
    if (task.metadata.expectedHotReloads > 0 && 
        task.metadata.actualHotReloads < task.metadata.expectedHotReloads) {
      return true;
    }
    
    // 4. File modifications were made but task didn't complete
    if (task.fileModifications.length > 0 && task.progress < 100) {
      return true;
    }
    
    return false;
  }

  private async validateResumability(task: ActiveTaskState): Promise<boolean> {
    // Check basic resumability flags
    if (!task.isResumable || task.resumptionConfidence < 0.3) {
      return false;
    }
    
    // Check if too much time has passed
    const ageHours = (Date.now() - task.lastCheckpoint.getTime()) / (1000 * 60 * 60);
    if (ageHours > 24) { // More than 24 hours old
      return false;
    }
    
    // Check if dependencies still exist
    for (const dep of task.dependencies) {
      try {
        await import(dep);
      } catch {
        console.warn(`Dependency not available: ${dep}`);
        return false;
      }
    }
    
    // Check if recent file modifications are valid
    const recentMods = task.fileModifications.filter(fm => 
      Date.now() - fm.timestamp.getTime() < 60 * 60 * 1000 // Last hour
    );
    
    for (const mod of recentMods) {
      if (mod.action === 'change' || mod.action === 'add') {
        // File should still exist
        try {
          const fs = await import('fs/promises');
          await fs.access(mod.path);
        } catch {
          console.warn(`Modified file no longer exists: ${mod.path}`);
          return false;
        }
      }
    }
    
    return true;
  }

  private async reconstructTaskContext(task: ActiveTaskState): Promise<{
    messages: any[];
    agentContext: AgentContext;
    reconstructionNotes: string[];
  }> {
    const latestCheckpoint = task.conversationContext.checkpoints[
      task.conversationContext.checkpoints.length - 1
    ];
    
    if (latestCheckpoint) {
      // Use the latest checkpoint
      return await contextPreserver.reconstructConversationContext(task.taskId, latestCheckpoint);
    } else {
      // Create minimal context
      return await contextPreserver.createMinimalRecoveryContext(
        task.taskId,
        task.metadata.objective,
        task.currentStep
      );
    }
  }

  private async createOptimalResumptionPoint(task: ActiveTaskState): Promise<ResumptionPoint> {
    // Analyze task state to determine the best resumption point
    
    const lastTool = task.toolExecutions[task.toolExecutions.length - 1];
    const recentProgress = task.progress;
    
    let resumptionType: ResumptionPoint['type'] = 'analysis_phase';
    let description = 'Resuming task analysis';
    let recoveryStrategy: ResumptionPoint['recoveryStrategy'] = 'continue';
    let nextActions: string[] = ['analyze_current_state'];
    
    // Determine resumption point based on progress and last activities
    if (recentProgress < 20) {
      resumptionType = 'analysis_phase';
      description = 'Resuming initial analysis';
      nextActions = ['complete_analysis', 'plan_implementation'];
    } else if (recentProgress < 70) {
      resumptionType = 'implementation_phase';
      description = 'Resuming implementation';
      nextActions = ['continue_implementation', 'verify_progress'];
    } else if (recentProgress < 90) {
      resumptionType = 'testing_phase';
      description = 'Resuming testing and validation';
      nextActions = ['run_tests', 'validate_implementation'];
    } else {
      resumptionType = 'implementation_phase';
      description = 'Resuming final steps';
      nextActions = ['complete_task', 'verify_completion'];
    }
    
    // Adjust based on last tool execution
    if (lastTool) {
      if (lastTool.status === 'failed') {
        recoveryStrategy = 'retry';
        nextActions.unshift(`retry_${lastTool.toolName}`);
      } else if (lastTool.status === 'running' || lastTool.status === 'pending') {
        resumptionType = 'tool_execution';
        description = `Resuming ${lastTool.toolName} execution`;
        recoveryStrategy = 'continue';
      }
    }
    
    // Check for recent file modifications
    const recentMods = task.fileModifications.filter(fm => 
      Date.now() - fm.timestamp.getTime() < 30 * 60 * 1000 // Last 30 minutes
    );
    
    if (recentMods.length > 0 && recentMods.some(fm => fm.source === 'dante')) {
      resumptionType = 'file_operation';
      description = 'Resuming after file modifications';
      nextActions.unshift('verify_file_changes', 'test_modifications');
    }
    
    return {
      type: resumptionType,
      description,
      lastSuccessfulAction: task.completedSteps[task.completedSteps.length - 1] || 'task_created',
      nextActions,
      context: {
        progress: recentProgress,
        lastTool: lastTool?.toolName,
        recentModifications: recentMods.length,
        resumptionConfidence: task.resumptionConfidence,
      },
      recoveryStrategy,
    };
  }

  private async generateResumptionInstructions(
    task: ActiveTaskState, 
    resumptionPoint: ResumptionPoint
  ): Promise<string> {
    const instructions = [
      `Task Resumption Instructions for ${task.taskId}:`,
      ``,
      `OBJECTIVE: ${task.metadata.objective}`,
      ``,
      `CURRENT STATE:`,
      `- Progress: ${task.progress}%`,
      `- Current Step: ${task.currentStep}`,
      `- Resumption Point: ${resumptionPoint.description}`,
      `- Recovery Strategy: ${resumptionPoint.recoveryStrategy}`,
      ``,
      `COMPLETED STEPS:`,
      task.completedSteps.map(step => `✅ ${step}`).join('\n'),
      ``,
      `NEXT ACTIONS:`,
      resumptionPoint.nextActions.map(action => `🔄 ${action}`).join('\n'),
      ``,
      `CONTEXT NOTES:`,
      `- Last successful action: ${resumptionPoint.lastSuccessfulAction}`,
      `- Recent tool executions: ${task.toolExecutions.slice(-3).map(te => te.toolName).join(', ')}`,
      `- File modifications: ${task.fileModifications.length} files changed`,
      `- Hot-reload events: ${task.metadata.actualHotReloads}/${task.metadata.expectedHotReloads}`,
      ``,
      `RECOVERY CONFIDENCE: ${(task.resumptionConfidence * 100).toFixed(1)}%`,
      ``,
      `Please proceed with the next actions while being aware that this is a resumed task.`,
      `If any context seems unclear or inconsistent, please ask for clarification or restart the analysis phase.`,
    ];
    
    return instructions.join('\n');
  }

  private generateContinuationPlan(task: ActiveTaskState, resumptionPoint: ResumptionPoint): string[] {
    const plan: string[] = [];
    
    // Add immediate next actions
    plan.push(...resumptionPoint.nextActions);
    
    // Add phase-specific continuations
    switch (resumptionPoint.type) {
      case 'analysis_phase':
        plan.push('complete_requirements_analysis', 'create_implementation_plan');
        break;
      case 'implementation_phase':
        plan.push('implement_core_functionality', 'add_error_handling', 'optimize_performance');
        break;
      case 'testing_phase':
        plan.push('run_unit_tests', 'perform_integration_tests', 'validate_edge_cases');
        break;
      case 'file_operation':
        plan.push('verify_file_integrity', 'test_file_changes', 'commit_if_successful');
        break;
    }
    
    // Add common final steps
    if (task.progress > 70) {
      plan.push('perform_final_validation', 'document_changes', 'mark_task_complete');
    }
    
    return plan;
  }

  private calculateResumptionPriority(task: ActiveTaskState): number {
    let priority = 1;
    
    // Higher priority for newer tasks
    const ageHours = (Date.now() - task.createdAt.getTime()) / (1000 * 60 * 60);
    priority += Math.max(0, 10 - ageHours);
    
    // Higher priority for tasks with more progress
    priority += task.progress / 10;
    
    // Higher priority for self-improvement tasks
    if (task.metadata.isSelfImprovement) {
      priority += 5;
    }
    
    // Higher priority for critical tasks
    if (task.metadata.priority === 'critical') {
      priority += 10;
    } else if (task.metadata.priority === 'high') {
      priority += 5;
    }
    
    // Higher priority for tasks with recent file modifications
    const recentMods = task.fileModifications.filter(fm => 
      Date.now() - fm.timestamp.getTime() < 60 * 60 * 1000
    );
    priority += recentMods.length * 2;
    
    return Math.min(20, priority); // Cap at 20
  }

  private async findTasksAffectedByHotReload(event: HotReloadEvent): Promise<ActiveTaskState[]> {
    const allTasks = await activeTaskManager.listActiveTasks();
    const affected: ActiveTaskState[] = [];
    
    for (const task of allTasks) {
      // Check if any of the affected files are related to this task
      for (const affectedFile of event.affectedFiles) {
        // Check file modifications
        const hasFileModification = task.fileModifications.some(fm => 
          fm.path === affectedFile || fm.path.includes(affectedFile)
        );
        
        // Check dependencies
        const hasDependency = task.dependencies.some(dep => 
          dep.includes(affectedFile) || affectedFile.includes(dep)
        );
        
        // Check if it's a tool/agent modification (likely self-improvement)
        const isSelfImprovementFile = affectedFile.includes('tools/') || 
                                     affectedFile.includes('agents/');
        
        if (hasFileModification || hasDependency || 
            (task.metadata.isSelfImprovement && isSelfImprovementFile)) {
          affected.push(task);
          break; // Don't add the same task multiple times
        }
      }
    }
    
    return affected;
  }

  private async processResumptionQueue(): Promise<void> {
    if (this.isProcessingQueue || this.resumptionQueue.size === 0) {
      return;
    }
    
    this.isProcessingQueue = true;
    console.log(`⚙️ Processing resumption queue: ${this.resumptionQueue.size} tasks`);
    
    try {
      // Sort tasks by priority
      const sortedTasks = Array.from(this.resumptionQueue.entries())
        .sort((a, b) => b[1].priority - a[1].priority);
      
      // Process highest priority task
      if (sortedTasks.length > 0) {
        const [taskId, { task }] = sortedTasks[0];
        this.resumptionQueue.delete(taskId);
        
        try {
          await this.resumeTask(taskId);
        } catch (error) {
          console.error(`Failed to resume task from queue: ${taskId}`, error);
        }
      }
    } finally {
      this.isProcessingQueue = false;
      
      // Continue processing if there are more tasks
      if (this.resumptionQueue.size > 0) {
        setTimeout(() => this.processResumptionQueue(), 1000);
      }
    }
  }

  private setupEventListeners(): void {
    // Listen to hot-reload events
    hotReloadDetector.on('hotReload', (event: HotReloadEvent) => {
      this.handleHotReloadEvent(event);
    });
    
    // Listen to task manager events
    activeTaskManager.on('taskCreated', (task: ActiveTaskState) => {
      // Track new tasks for potential recovery
      console.log(`📋 Tracking new task for recovery: ${task.taskId}`);
    });
    
    activeTaskManager.on('taskUpdated', (task: ActiveTaskState) => {
      // Update resumption confidence based on activity
      if (task.isResumable && this.resumptionQueue.has(task.taskId)) {
        // Task is active again, remove from resumption queue
        this.resumptionQueue.delete(task.taskId);
      }
    });
  }

  private setupProcessingInterval(): void {
    // Check for tasks that need resumption every 30 seconds
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessingQueue && this.resumptionQueue.size > 0) {
        await this.processResumptionQueue();
      }
    }, 30000);
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Task Resumption Engine...');
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    // Process any remaining tasks in the queue
    this.isProcessingQueue = false;
    await this.processResumptionQueue();
    
    console.log('✅ Task Resumption Engine shutdown complete');
  }

  // Public API for monitoring
  getQueueStatus() {
    return {
      queueLength: this.resumptionQueue.size,
      isProcessing: this.isProcessingQueue,
      pendingTasks: Array.from(this.resumptionQueue.keys()),
    };
  }

  getResumptionAttempts(): Map<string, number> {
    return new Map(this.resumptionAttempts);
  }
}

// Export singleton instance
export const taskResumptionEngine = new TaskResumptionEngine({
  enabled: true,
  maxActiveTasks: 10,
  checkpointInterval: 5,
  recoveryTimeout: 30,
  watchPaths: ['src/**/*.{ts,js,tsx,jsx,vue}'],
  ignorePaths: ['src/tests/**'],
  debounceDelay: 1000,
  autoResumeEnabled: true,
  requireUserConfirmation: false,
  maxRecoveryAttempts: 3,
  learningEnabled: true,
  patternRecognition: true,
  adaptiveRecovery: true,
});