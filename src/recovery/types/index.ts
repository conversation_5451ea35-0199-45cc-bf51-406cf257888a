// Active Task Recovery System Types

export interface FileChange {
  path: string;
  action: 'add' | 'change' | 'unlink';
  timestamp: Date;
  size?: number;
  hash?: string;
  source: 'user' | 'dante' | 'system';
  relatedTaskId?: string;
}

export interface ToolExecution {
  toolName: string;
  parameters: any;
  startTime: Date;
  endTime?: Date;
  result?: any;
  error?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  agentName?: string;
}

export interface AgentContext {
  agentName: string;
  model: string;
  instructions: string;
  availableTools: string[];
  currentHandoff?: string;
  lastActivity: Date;
  state: 'idle' | 'thinking' | 'using_tool' | 'handoff' | 'waiting';
}

export interface ConversationCheckpoint {
  messageIndex: number;
  agentContext: AgentContext;
  toolExecutions: ToolExecution[];
  taskProgress: number;
  milestone: string;
  timestamp: Date;
}

export interface ActiveTaskState {
  taskId: string;
  sessionId: string;
  agentName: string;
  conversationContext: {
    messages: Array<{
      role: string;
      content: string;
      timestamp: number;
      agentEvents?: any[];
    }>;
    checkpoints: ConversationCheckpoint[];
  };
  
  // Task execution state
  currentStep: string;
  completedSteps: string[];
  totalSteps?: number;
  progress: number; // 0-100
  
  // Code/file modification tracking
  toolExecutions: ToolExecution[];
  fileModifications: FileChange[];
  dependencies: string[]; // Files/modules this task depends on
  
  // Recovery information
  resumptionPoint: ResumptionPoint;
  metadata: TaskMetadata;
  
  // Timing information
  createdAt: Date;
  lastCheckpoint: Date;
  estimatedCompletion?: Date;
  
  // State validation
  isResumable: boolean;
  resumptionConfidence: number; // 0-1
  
  // Integration tracking
  relatedReminders: string[];
  memoryReferences: string[];
}

export interface ResumptionPoint {
  type: 'tool_execution' | 'agent_handoff' | 'file_operation' | 'analysis_phase' | 'implementation_phase' | 'testing_phase';
  description: string;
  lastSuccessfulAction: string;
  nextActions: string[];
  context: any; // Specific to resumption type
  recoveryStrategy: 'continue' | 'retry' | 'restart_phase' | 'escalate';
}

export interface TaskMetadata {
  objective: string;
  approach: string;
  expectedOutcome: string;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  category: 'bug_fix' | 'feature_addition' | 'integration' | 'optimization' | 'research' | 'self_improvement';
  priority: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
  
  // Development context
  isCodeModification: boolean;
  isSelfImprovement: boolean;
  isAPIIntegration: boolean;
  affectedComponents: string[];
  
  // Hot-reload context
  expectedHotReloads: number;
  actualHotReloads: number;
  hotReloadTolerant: boolean;
}

export interface HotReloadEvent {
  timestamp: Date;
  trigger: 'file_save' | 'vite_restart' | 'server_restart' | 'manual';
  affectedFiles: string[];
  relatedTasks: string[];
  restartDuration?: number;
  recovered: boolean;
}

export interface RecoverySession {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  hotReloadEvents: HotReloadEvent[];
  activeTasks: ActiveTaskState[];
  recoveredTasks: string[];
  failedRecoveries: string[];
  
  // Recovery statistics
  totalRecoveries: number;
  successfulRecoveries: number;
  averageRecoveryTime: number;
  
  // Learning data
  commonFailurePoints: string[];
  successfulPatterns: string[];
  improvementSuggestions: string[];
}

export interface TaskResumptionResult {
  success: boolean;
  taskId: string;
  resumedAt: ResumptionPoint;
  recoveryTime: number;
  message: string;
  nextSteps: string[];
  error?: string;
  
  // Post-recovery state
  newContext?: any;
  updatedProgress: number;
  continuationPlan: string[];
}

export interface RecoveryConfiguration {
  enabled: boolean;
  maxActiveTasks: number;
  checkpointInterval: number; // minutes
  recoveryTimeout: number; // minutes
  
  // File watching
  watchPaths: string[];
  ignorePaths: string[];
  debounceDelay: number; // milliseconds
  
  // Recovery strategies
  autoResumeEnabled: boolean;
  requireUserConfirmation: boolean;
  maxRecoveryAttempts: number;
  
  // Learning
  learningEnabled: boolean;
  patternRecognition: boolean;
  adaptiveRecovery: boolean;
}

// Recovery Storage Interfaces
export interface RecoveryStorage {
  saveTaskState(task: ActiveTaskState): Promise<boolean>;
  loadTaskState(taskId: string): Promise<ActiveTaskState | null>;
  listActiveTasks(): Promise<ActiveTaskState[]>;
  deleteTaskState(taskId: string): Promise<boolean>;
  updateTaskProgress(taskId: string, progress: number, checkpoint?: ConversationCheckpoint): Promise<boolean>;
  
  // Recovery session management
  createRecoverySession(): Promise<RecoverySession>;
  updateRecoverySession(session: RecoverySession): Promise<boolean>;
  getRecoveryHistory(limit?: number): Promise<RecoverySession[]>;
  
  // Analytics and learning
  recordSuccessfulRecovery(taskId: string, strategy: string): Promise<void>;
  recordFailedRecovery(taskId: string, reason: string): Promise<void>;
  getRecoveryPatterns(): Promise<any>;
}

// Event System Types
export interface RecoveryEvent {
  type: 'task_created' | 'task_updated' | 'hot_reload_detected' | 'recovery_started' | 'recovery_completed' | 'recovery_failed';
  taskId?: string;
  sessionId?: string;
  timestamp: Date;
  data: any;
}

export interface RecoveryEventListener {
  onTaskCreated?(task: ActiveTaskState): void;
  onTaskUpdated?(task: ActiveTaskState): void;
  onHotReloadDetected?(event: HotReloadEvent): void;
  onRecoveryStarted?(taskId: string): void;
  onRecoveryCompleted?(result: TaskResumptionResult): void;
  onRecoveryFailed?(taskId: string, error: string): void;
}

// Integration Types
export interface RecoveryIntegration {
  // Reminder system integration
  createRecoveryReminder(task: ActiveTaskState): Promise<string>;
  cancelRecoveryReminder(taskId: string): Promise<boolean>;
  
  // Memory system integration
  storeRecoveryPattern(pattern: any): Promise<boolean>;
  recallSimilarTasks(objective: string): Promise<ActiveTaskState[]>;
  
  // Agent system integration
  notifyAgentOfRecovery(agentName: string, task: ActiveTaskState): Promise<boolean>;
  requestAgentStateRestoration(agentName: string, context: AgentContext): Promise<boolean>;
}

// Utility Types
export type RecoveryStrategy = 'immediate' | 'delayed' | 'user_prompt' | 'scheduled';
export type TaskCategory = ActiveTaskState['metadata']['category'];
export type TaskComplexity = ActiveTaskState['metadata']['complexity'];
export type RecoveryConfidence = 'high' | 'medium' | 'low' | 'uncertain';

// Error Types
export class RecoveryError extends Error {
  constructor(
    public taskId: string,
    public reason: string,
    public recoverable: boolean = true,
    public suggestedAction?: string
  ) {
    super(`Recovery failed for task ${taskId}: ${reason}`);
    this.name = 'RecoveryError';
  }
}

export class HotReloadDetectionError extends Error {
  constructor(
    public filePath: string,
    public reason: string
  ) {
    super(`Hot reload detection failed for ${filePath}: ${reason}`);
    this.name = 'HotReloadDetectionError';
  }
}