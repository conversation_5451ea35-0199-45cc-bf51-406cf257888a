import { EventEmitter } from 'events';
import {
  ActiveTaskState,
  RecoveryIntegration as IRecoveryIntegration,
  AgentContext,
  HotReloadEvent,
  TaskResumptionResult,
} from './types';
import { activeTaskManager } from './ActiveTaskManager';
import { taskResumptionEngine } from './TaskResumptionEngine';
import { hotReloadDetector } from './HotReloadDetector';
import { contextPreserver } from './ContextPreserver';
import { reminderManager } from '../reminder/ReminderManager';
import { memoryManager } from '../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../memory/types';
import { ReminderType, NotificationChannel } from '../reminder/types';

export class RecoveryIntegration extends EventEmitter implements IRecoveryIntegration {
  private isInitialized = false;
  private recoveryReminders = new Map<string, string>(); // taskId -> reminderId

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔗 Initializing Recovery Integration...');

    try {
      // Initialize all recovery components
      await activeTaskManager.initialize();
      await taskResumptionEngine.initialize();
      await hotReloadDetector.startWatching();

      // Setup cross-system event listeners
      this.setupIntegrationEvents();

      this.isInitialized = true;
      console.log('✅ Recovery Integration initialized');
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Recovery Integration:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.isInitialized) return;

    console.log('🛑 Shutting down Recovery Integration...');

    try {
      await hotReloadDetector.stopWatching();
      await taskResumptionEngine.shutdown();
      await contextPreserver.shutdown();

      this.isInitialized = false;
      console.log('✅ Recovery Integration shutdown complete');
      this.emit('shutdown');
    } catch (error) {
      console.error('Error during Recovery Integration shutdown:', error);
    }
  }

  // IRecoveryIntegration implementation
  async createRecoveryReminder(task: ActiveTaskState): Promise<string> {
    try {
      // Create a reminder to check on this task periodically
      const reminder = await reminderManager.createReminder({
        title: `Recovery Check: ${task.metadata.objective.substring(0, 50)}...`,
        description: `Periodic check for task ${task.taskId} to ensure it can be recovered if interrupted`,
        type: ReminderType.RECURRING,
        schedule: {
          cronExpression: '*/5 * * * *', // Every 5 minutes
          timezone: 'UTC',
        },
        action: {
          type: 'custom',
          payload: {
            taskId: task.taskId,
            action: 'recovery_check',
          },
          notificationChannels: [NotificationChannel.MEMORY_STORE],
        },
        priority: 'medium',
        tags: ['recovery', 'task-monitoring', task.metadata.category],
        maxExecutions: 60, // Run for up to 5 hours
        sessionId: task.sessionId,
        createdBy: 'RecoveryIntegration',
      });

      this.recoveryReminders.set(task.taskId, reminder.id);
      console.log(`⏰ Created recovery reminder for task: ${task.taskId}`);

      return reminder.id;
    } catch (error) {
      console.error(`Failed to create recovery reminder for task ${task.taskId}:`, error);
      throw error;
    }
  }

  async cancelRecoveryReminder(taskId: string): Promise<boolean> {
    try {
      const reminderId = this.recoveryReminders.get(taskId);
      if (!reminderId) return false;

      const cancelled = await reminderManager.cancelReminder(reminderId);
      if (cancelled) {
        this.recoveryReminders.delete(taskId);
        console.log(`⏰ Cancelled recovery reminder for task: ${taskId}`);
      }

      return cancelled;
    } catch (error) {
      console.error(`Failed to cancel recovery reminder for task ${taskId}:`, error);
      return false;
    }
  }

  async storeRecoveryPattern(pattern: any): Promise<boolean> {
    try {
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        {
          type: 'recovery_pattern',
          pattern,
          createdAt: new Date(),
          usage: 'task_recovery',
        },
        {
          priority: MemoryPriority.HIGH,
          tags: ['recovery', 'pattern', 'task_resumption'],
          confidence: 0.9,
        }
      );

      console.log('🧠 Stored recovery pattern in memory');
      return true;
    } catch (error) {
      console.error('Failed to store recovery pattern:', error);
      return false;
    }
  }

  async recallSimilarTasks(objective: string): Promise<ActiveTaskState[]> {
    try {
      // Search for similar tasks in memory
      const memories = await memoryManager.recall(`task objective: ${objective}`, 5);
      
      const similarTaskIds = memories
        .filter(memory => memory.content && typeof memory.content === 'object')
        .map(memory => (memory.content as any).taskId)
        .filter(Boolean);

      // Load the actual task states
      const similarTasks: ActiveTaskState[] = [];
      for (const taskId of similarTaskIds) {
        const task = await activeTaskManager.loadTaskState(taskId);
        if (task) {
          similarTasks.push(task);
        }
      }

      console.log(`🔍 Found ${similarTasks.length} similar tasks for objective: ${objective.substring(0, 50)}...`);
      return similarTasks;
    } catch (error) {
      console.error('Failed to recall similar tasks:', error);
      return [];
    }
  }

  async notifyAgentOfRecovery(agentName: string, task: ActiveTaskState): Promise<boolean> {
    try {
      // This would integrate with the agent system to notify specific agents
      // For now, we'll emit an event that the agent system can listen to
      this.emit('agentRecoveryNotification', {
        agentName,
        taskId: task.taskId,
        objective: task.metadata.objective,
        resumptionPoint: task.resumptionPoint,
        message: `Task ${task.taskId} has been recovered and needs your attention`,
      });

      console.log(`📢 Notified ${agentName} of task recovery: ${task.taskId}`);
      return true;
    } catch (error) {
      console.error(`Failed to notify agent ${agentName} of recovery:`, error);
      return false;
    }
  }

  async requestAgentStateRestoration(agentName: string, context: AgentContext): Promise<boolean> {
    try {
      // This would integrate with the agent system to restore agent state
      this.emit('agentStateRestoration', {
        agentName,
        context,
        message: `Please restore state for agent ${agentName}`,
      });

      console.log(`🔄 Requested state restoration for agent: ${agentName}`);
      return true;
    } catch (error) {
      console.error(`Failed to request state restoration for agent ${agentName}:`, error);
      return false;
    }
  }

  // Enhanced recovery methods
  async createDevelopmentSessionRecovery(sessionId: string): Promise<void> {
    try {
      // Create a comprehensive recovery checkpoint for the entire development session
      const activeTasks = await activeTaskManager.listActiveTasks();
      const sessionTasks = activeTasks.filter(task => task.sessionId === sessionId);

      if (sessionTasks.length === 0) return;

      // Store session-level recovery data
      await memoryManager.create(
        MemoryType.EPISODIC,
        {
          type: 'development_session',
          sessionId,
          taskCount: sessionTasks.length,
          tasks: sessionTasks.map(task => ({
            taskId: task.taskId,
            objective: task.metadata.objective,
            progress: task.progress,
            category: task.metadata.category,
          })),
          timestamp: new Date(),
        },
        {
          priority: MemoryPriority.HIGH,
          tags: ['development_session', 'recovery', sessionId],
          confidence: 0.95,
        }
      );

      console.log(`💾 Created development session recovery for ${sessionTasks.length} tasks`);
    } catch (error) {
      console.error('Failed to create development session recovery:', error);
    }
  }

  async handleCriticalTaskInterruption(taskId: string, reason: string): Promise<void> {
    try {
      const task = await activeTaskManager.loadTaskState(taskId);
      if (!task) return;

      // For critical tasks, create immediate recovery reminder
      if (task.metadata.priority === 'critical' || task.metadata.isSelfImprovement) {
        const emergencyReminder = await reminderManager.createReminder({
          title: `URGENT: Interrupted Critical Task`,
          description: `Critical task "${task.metadata.objective}" was interrupted: ${reason}`,
          type: ReminderType.ONE_TIME,
          schedule: {
            executeAt: new Date(Date.now() + 30000), // 30 seconds from now
            timezone: 'UTC',
          },
          action: {
            type: 'notification',
            payload: {
              message: `Critical task ${taskId} needs immediate attention. Reason: ${reason}`,
              urgency: 'critical',
            },
            notificationChannels: [NotificationChannel.WEB_UI, NotificationChannel.FILE_QUEUE],
          },
          priority: 'critical',
          tags: ['emergency', 'critical_task', 'recovery'],
          sessionId: task.sessionId,
          createdBy: 'RecoveryIntegration',
        });

        console.log(`🚨 Created emergency recovery reminder for critical task: ${taskId}`);
      }
    } catch (error) {
      console.error(`Failed to handle critical task interruption for ${taskId}:`, error);
    }
  }

  async learnFromSuccessfulRecovery(result: TaskResumptionResult): Promise<void> {
    try {
      // Store successful recovery pattern for future learning
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        {
          type: 'successful_recovery',
          taskId: result.taskId,
          resumptionPoint: result.resumedAt,
          recoveryTime: result.recoveryTime,
          strategy: result.resumedAt.recoveryStrategy,
          nextSteps: result.nextSteps,
          continuationPlan: result.continuationPlan,
          learningTimestamp: new Date(),
        },
        {
          priority: MemoryPriority.HIGH,
          tags: ['recovery_success', result.resumedAt.type, result.resumedAt.recoveryStrategy],
          confidence: 0.9,
        }
      );

      console.log(`🎓 Learned from successful recovery: ${result.taskId}`);
    } catch (error) {
      console.error('Failed to learn from successful recovery:', error);
    }
  }

  async getRecoveryRecommendations(task: ActiveTaskState): Promise<string[]> {
    try {
      // Get recommendations based on similar past recoveries
      const similarPatterns = await memoryManager.recall(
        `recovery ${task.metadata.category} ${task.metadata.complexity}`,
        3
      );

      const recommendations: string[] = [];

      for (const pattern of similarPatterns) {
        if (pattern.content && typeof pattern.content === 'object') {
          const content = pattern.content as any;
          if (content.type === 'successful_recovery' && content.strategy) {
            recommendations.push(`Try ${content.strategy} strategy based on similar task recovery`);
          }
        }
      }

      // Add default recommendations
      if (recommendations.length === 0) {
        recommendations.push(
          'Start with analysis phase to assess current state',
          'Verify file modifications and dependencies',
          'Check tool execution status and retry if needed',
          'Consider agent handoff if complexity is high'
        );
      }

      return recommendations;
    } catch (error) {
      console.error('Failed to get recovery recommendations:', error);
      return ['Manual analysis recommended due to error in recommendation system'];
    }
  }

  // Status and monitoring
  getIntegrationStatus() {
    return {
      initialized: this.isInitialized,
      activeTaskManager: activeTaskManager.listActiveTasks().then(tasks => ({
        activeTasks: tasks.length,
        resumableTasks: tasks.filter(t => t.isResumable).length,
      })),
      hotReloadDetector: hotReloadDetector.getWatcherStatus(),
      taskResumptionEngine: taskResumptionEngine.getQueueStatus(),
      recoveryReminders: this.recoveryReminders.size,
    };
  }

  private setupIntegrationEvents(): void {
    // Integrate task management with reminder system
    activeTaskManager.on('taskCreated', async (task: ActiveTaskState) => {
      if (task.metadata.isCodeModification || task.metadata.isSelfImprovement) {
        // Create recovery reminder for code modification tasks
        await this.createRecoveryReminder(task);
      }
    });

    activeTaskManager.on('taskCompleted', async (task: ActiveTaskState) => {
      // Cancel recovery reminder when task completes
      await this.cancelRecoveryReminder(task.taskId);
    });

    // Integrate hot-reload detection with task resumption
    hotReloadDetector.on('hotReload', async (event: HotReloadEvent) => {
      console.log(`🔥 Hot-reload detected, checking for affected tasks...`);
      await taskResumptionEngine.handleHotReloadEvent(event);
    });

    // Learn from successful recoveries
    taskResumptionEngine.on('taskResumed', async (result: TaskResumptionResult) => {
      await this.learnFromSuccessfulRecovery(result);
    });

    // Handle critical task interruptions
    taskResumptionEngine.on('taskResumptionFailed', async (result: TaskResumptionResult) => {
      if (result.error?.includes('critical') || result.taskId.includes('self_improvement')) {
        await this.handleCriticalTaskInterruption(result.taskId, result.error || 'Unknown error');
      }
    });

    // Integrate context preservation with memory system
    contextPreserver.on('contextPreserved', async (taskId: string, checkpoint: any) => {
      // Store important context in memory for cross-session learning
      await memoryManager.create(
        MemoryType.EPISODIC,
        {
          type: 'task_checkpoint',
          taskId,
          checkpoint,
          preservedAt: new Date(),
        },
        {
          priority: MemoryPriority.MEDIUM,
          tags: ['task_checkpoint', 'recovery', taskId],
          confidence: 0.8,
        }
      );
    });
  }
}

// Main Recovery System Orchestrator
export class RecoverySystem extends EventEmitter {
  private integration: RecoveryIntegration;
  private isRunning = false;

  constructor() {
    super();
    this.integration = new RecoveryIntegration();
  }

  async start(): Promise<void> {
    if (this.isRunning) return;

    console.log('🚀 Starting Dante Recovery System...');

    try {
      await this.integration.initialize();
      this.isRunning = true;

      console.log('✅ Dante Recovery System is now active');
      console.log('   - Hot-reload detection enabled');
      console.log('   - Active task persistence enabled');
      console.log('   - Automatic task resumption enabled');
      console.log('   - Cross-system integration active');

      this.emit('started');
    } catch (error) {
      console.error('❌ Failed to start Recovery System:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) return;

    console.log('🛑 Stopping Dante Recovery System...');

    try {
      await this.integration.shutdown();
      this.isRunning = false;

      console.log('✅ Dante Recovery System stopped');
      this.emit('stopped');
    } catch (error) {
      console.error('Error stopping Recovery System:', error);
    }
  }

  // Public API for manual operations
  async createTaskRecoveryPoint(
    sessionId: string,
    agentName: string,
    objective: string,
    conversationMessages: any[]
  ): Promise<ActiveTaskState> {
    if (!this.isRunning) {
      throw new Error('Recovery System not running');
    }

    const task = await activeTaskManager.createTask(
      sessionId,
      agentName,
      objective,
      conversationMessages
    );

    // Mark Dante as actively working on this task
    hotReloadDetector.markDanteModification(`task_${task.taskId}`);

    console.log(`📋 Created task recovery point: ${task.taskId}`);
    this.emit('taskRecoveryPointCreated', task);

    return task;
  }

  async markTaskStep(taskId: string, step: string, toolExecutions?: any[]): Promise<void> {
    const task = await activeTaskManager.loadTaskState(taskId);
    if (!task) return;

    task.completedSteps.push(step);
    task.currentStep = step;

    if (toolExecutions) {
      task.toolExecutions.push(...toolExecutions);
    }

    // Calculate new progress
    const newProgress = Math.min(100, (task.completedSteps.length / (task.totalSteps || 10)) * 100);
    
    await activeTaskManager.updateTaskProgress(taskId, newProgress);
    console.log(`📊 Marked step complete: ${step} (${newProgress}%)`);
  }

  async markFileModification(taskId: string, filePath: string): Promise<void> {
    // Mark that Dante is about to modify this file
    hotReloadDetector.markDanteModification(filePath);

    // Record the modification in the task
    await activeTaskManager.addFileModification(taskId, {
      path: filePath,
      action: 'change',
      timestamp: new Date(),
      source: 'dante',
      relatedTaskId: taskId,
    });

    console.log(`📝 Marked file modification: ${filePath} for task ${taskId}`);
  }

  async resumeInterruptedTask(taskId: string, userApproval: boolean = false): Promise<TaskResumptionResult> {
    if (!this.isRunning) {
      throw new Error('Recovery System not running');
    }

    return await taskResumptionEngine.resumeTask(taskId, userApproval);
  }

  // Status and monitoring
  getSystemStatus() {
    return {
      isRunning: this.isRunning,
      integration: this.integration.getIntegrationStatus(),
      hotReloadDetector: hotReloadDetector.getWatcherStatus(),
      taskResumptionEngine: taskResumptionEngine.getQueueStatus(),
    };
  }

  async getRecoveryHistory(limit: number = 10) {
    return await activeTaskManager.getRecoveryHistory(limit);
  }
}

// Export singleton instance
export const recoverySystem = new RecoverySystem();