import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as lockfile from 'proper-lockfile';
import {
  ActiveTaskState,
  RecoveryStorage,
  RecoverySession,
  ConversationCheckpoint,
  ToolExecution,
  FileChange,
  TaskMetadata,
  ResumptionPoint,
  AgentContext,
  RecoveryError,
} from './types';
import { memoryManager } from '../memory/MemoryManager';
import { MemoryType, MemoryPriority } from '../memory/types';

export class ActiveTaskManager extends EventEmitter implements RecoveryStorage {
  private readonly RECOVERY_DIR = path.join(process.cwd(), 'data', 'recovery');
  private readonly TASKS_DIR = path.join(this.RECOVERY_DIR, 'tasks');
  private readonly SESSIONS_DIR = path.join(this.RECOVERY_DIR, 'sessions');
  private readonly INDEX_FILE = path.join(this.RECOVERY_DIR, 'index.json');
  
  private activeTasks = new Map<string, ActiveTaskState>();
  private currentSession?: RecoverySession;
  private saveQueue = new Set<string>();
  private saveInterval?: NodeJS.Timeout;
  
  constructor() {
    super();
    this.setupAutoSave();
    this.setupGracefulShutdown();
  }

  async initialize(): Promise<void> {
    console.log('🔄 Initializing Active Task Manager...');
    
    try {
      await this.ensureDirectories();
      await this.loadActiveTasks();
      await this.createOrLoadCurrentSession();
      
      console.log(`✅ Active Task Manager initialized with ${this.activeTasks.size} active tasks`);
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Active Task Manager:', error);
      throw error;
    }
  }

  async createTask(
    sessionId: string,
    agentName: string,
    objective: string,
    conversationMessages: any[]
  ): Promise<ActiveTaskState> {
    const taskId = this.generateTaskId();
    
    const task: ActiveTaskState = {
      taskId,
      sessionId,
      agentName,
      conversationContext: {
        messages: conversationMessages.map(msg => ({
          ...msg,
          timestamp: msg.timestamp || Date.now(),
        })),
        checkpoints: [],
      },
      
      currentStep: 'initialization',
      completedSteps: [],
      progress: 0,
      
      toolExecutions: [],
      fileModifications: [],
      dependencies: [],
      
      resumptionPoint: {
        type: 'analysis_phase',
        description: 'Starting task analysis',
        lastSuccessfulAction: 'task_created',
        nextActions: ['analyze_requirements', 'plan_approach'],
        context: { phase: 'initial' },
        recoveryStrategy: 'continue',
      },
      
      metadata: this.generateTaskMetadata(objective, conversationMessages),
      
      createdAt: new Date(),
      lastCheckpoint: new Date(),
      
      isResumable: true,
      resumptionConfidence: 1.0,
      
      relatedReminders: [],
      memoryReferences: [],
    };
    
    // Store task
    this.activeTasks.set(taskId, task);
    await this.saveTaskState(task);
    
    // Update session
    if (this.currentSession) {
      this.currentSession.activeTasks.push(task);
      await this.updateRecoverySession(this.currentSession);
    }
    
    console.log(`📋 Created active task: ${taskId} (${objective.substring(0, 50)}...)`);
    this.emit('taskCreated', task);
    
    return task;
  }

  async updateTaskProgress(
    taskId: string,
    progress: number,
    checkpoint?: ConversationCheckpoint
  ): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    task.progress = Math.min(100, Math.max(0, progress));
    task.lastCheckpoint = new Date();
    
    if (checkpoint) {
      task.conversationContext.checkpoints.push(checkpoint);
      
      // Keep only last 10 checkpoints to prevent excessive memory usage
      if (task.conversationContext.checkpoints.length > 10) {
        task.conversationContext.checkpoints = task.conversationContext.checkpoints.slice(-10);
      }
    }
    
    // Update resumption confidence based on progress
    task.resumptionConfidence = this.calculateResumptionConfidence(task);
    task.isResumable = task.resumptionConfidence > 0.3;
    
    // Queue for saving
    this.queueTaskForSaving(taskId);
    
    console.log(`📊 Updated task progress: ${taskId} (${progress}%)`);
    this.emit('taskUpdated', task);
    
    return true;
  }

  async addToolExecution(taskId: string, toolExecution: ToolExecution): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    task.toolExecutions.push(toolExecution);
    
    // Update resumption point based on tool execution
    if (toolExecution.status === 'completed') {
      task.resumptionPoint = {
        type: 'tool_execution',
        description: `Completed ${toolExecution.toolName}`,
        lastSuccessfulAction: `tool_${toolExecution.toolName}`,
        nextActions: this.predictNextActions(task, toolExecution),
        context: { lastTool: toolExecution.toolName, result: toolExecution.result },
        recoveryStrategy: 'continue',
      };
    } else if (toolExecution.status === 'failed') {
      task.resumptionPoint.recoveryStrategy = 'retry';
    }
    
    this.queueTaskForSaving(taskId);
    this.emit('taskUpdated', task);
    
    return true;
  }

  async addFileModification(taskId: string, fileChange: FileChange): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    // Mark file change as related to this task
    fileChange.relatedTaskId = taskId;
    task.fileModifications.push(fileChange);
    
    // Update metadata
    task.metadata.isCodeModification = true;
    if (fileChange.path.includes('tools/') || fileChange.path.includes('agents/')) {
      task.metadata.isSelfImprovement = true;
    }
    
    // Expect hot-reload for code modifications
    if (fileChange.source === 'dante' && this.isHotReloadTriggerFile(fileChange.path)) {
      task.metadata.expectedHotReloads++;
      task.metadata.hotReloadTolerant = true;
    }
    
    this.queueTaskForSaving(taskId);
    this.emit('fileModified', task, fileChange);
    
    return true;
  }

  async completeTask(taskId: string, outcome: string): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    task.progress = 100;
    task.currentStep = 'completed';
    task.completedSteps.push('task_completion');
    task.isResumable = false;
    task.resumptionConfidence = 0;
    
    task.resumptionPoint = {
      type: 'implementation_phase',
      description: 'Task completed successfully',
      lastSuccessfulAction: 'task_completed',
      nextActions: [],
      context: { outcome },
      recoveryStrategy: 'continue',
    };
    
    // Store in memory system as completed task pattern
    await this.storeCompletedTaskPattern(task);
    
    // Save final state before removing
    await this.saveTaskState(task);
    
    // Remove from active tasks
    this.activeTasks.delete(taskId);
    
    console.log(`✅ Completed task: ${taskId}`);
    this.emit('taskCompleted', task);
    
    return true;
  }

  async failTask(taskId: string, error: string): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    if (!task) return false;
    
    task.isResumable = error.includes('recoverable') || task.progress > 20;
    task.resumptionConfidence = task.isResumable ? 0.5 : 0;
    
    task.resumptionPoint = {
      type: 'analysis_phase',
      description: `Task failed: ${error}`,
      lastSuccessfulAction: task.completedSteps[task.completedSteps.length - 1] || 'task_created',
      nextActions: ['analyze_error', 'retry_or_restart'],
      context: { error, failurePoint: task.currentStep },
      recoveryStrategy: task.isResumable ? 'retry' : 'escalate',
    };
    
    this.queueTaskForSaving(taskId);
    
    console.log(`❌ Failed task: ${taskId} - ${error}`);
    this.emit('taskFailed', task, error);
    
    return true;
  }

  // RecoveryStorage implementation
  async saveTaskState(task: ActiveTaskState): Promise<boolean> {
    try {
      const taskFile = path.join(this.TASKS_DIR, `${task.taskId}.json`);
      
      // Create empty file first if it doesn't exist
      try {
        await fs.access(taskFile);
      } catch {
        await fs.writeFile(taskFile, '{}');
      }
      
      const release = await lockfile.lock(taskFile, {
        retries: { retries: 3, minTimeout: 100, maxTimeout: 500 }
      });
      
      try {
        await fs.writeFile(taskFile, JSON.stringify(task, null, 2));
        await this.updateTaskIndex(task);
        return true;
      } finally {
        await release();
      }
    } catch (error) {
      console.error(`Failed to save task state ${task.taskId}:`, error);
      return false;
    }
  }

  async loadTaskState(taskId: string): Promise<ActiveTaskState | null> {
    try {
      const taskFile = path.join(this.TASKS_DIR, `${taskId}.json`);
      const content = await fs.readFile(taskFile, 'utf-8');
      const task = JSON.parse(content);
      
      // Parse dates
      return this.parseTaskDates(task);
    } catch (error) {
      return null;
    }
  }

  async listActiveTasks(): Promise<ActiveTaskState[]> {
    return Array.from(this.activeTasks.values());
  }

  async deleteTaskState(taskId: string): Promise<boolean> {
    try {
      const taskFile = path.join(this.TASKS_DIR, `${taskId}.json`);
      await fs.unlink(taskFile);
      await this.removeFromTaskIndex(taskId);
      return true;
    } catch (error) {
      console.error(`Failed to delete task state ${taskId}:`, error);
      return false;
    }
  }

  async createRecoverySession(): Promise<RecoverySession> {
    const session: RecoverySession = {
      sessionId: this.generateSessionId(),
      startTime: new Date(),
      hotReloadEvents: [],
      activeTasks: Array.from(this.activeTasks.values()),
      recoveredTasks: [],
      failedRecoveries: [],
      totalRecoveries: 0,
      successfulRecoveries: 0,
      averageRecoveryTime: 0,
      commonFailurePoints: [],
      successfulPatterns: [],
      improvementSuggestions: [],
    };
    
    this.currentSession = session;
    await this.saveRecoverySession(session);
    
    return session;
  }

  async updateRecoverySession(session: RecoverySession): Promise<boolean> {
    try {
      this.currentSession = session;
      await this.saveRecoverySession(session);
      return true;
    } catch (error) {
      console.error('Failed to update recovery session:', error);
      return false;
    }
  }

  async getRecoveryHistory(limit: number = 10): Promise<RecoverySession[]> {
    try {
      const files = await fs.readdir(this.SESSIONS_DIR);
      const sessionFiles = files
        .filter(f => f.endsWith('.json'))
        .sort((a, b) => b.localeCompare(a)) // Sort by filename (contains timestamp)
        .slice(0, limit);
      
      const sessions = await Promise.all(
        sessionFiles.map(async (file) => {
          const content = await fs.readFile(path.join(this.SESSIONS_DIR, file), 'utf-8');
          return this.parseSessionDates(JSON.parse(content));
        })
      );
      
      return sessions;
    } catch (error) {
      console.error('Failed to get recovery history:', error);
      return [];
    }
  }

  async recordSuccessfulRecovery(taskId: string, strategy: string): Promise<void> {
    if (this.currentSession) {
      this.currentSession.recoveredTasks.push(taskId);
      this.currentSession.successfulRecoveries++;
      this.currentSession.totalRecoveries++;
      
      if (!this.currentSession.successfulPatterns.includes(strategy)) {
        this.currentSession.successfulPatterns.push(strategy);
      }
      
      await this.updateRecoverySession(this.currentSession);
    }
    
    // Store pattern in memory system
    await this.storeRecoveryPattern(taskId, strategy, true);
  }

  async recordFailedRecovery(taskId: string, reason: string): Promise<void> {
    if (this.currentSession) {
      this.currentSession.failedRecoveries.push(taskId);
      this.currentSession.totalRecoveries++;
      
      if (!this.currentSession.commonFailurePoints.includes(reason)) {
        this.currentSession.commonFailurePoints.push(reason);
      }
      
      await this.updateRecoverySession(this.currentSession);
    }
    
    // Store pattern in memory system
    await this.storeRecoveryPattern(taskId, reason, false);
  }

  async getRecoveryPatterns(): Promise<any> {
    try {
      const patterns = await memoryManager.recall('recovery pattern task resumption', 10);
      return patterns.map(memory => memory.content);
    } catch (error) {
      console.error('Failed to get recovery patterns:', error);
      return [];
    }
  }

  // Task analysis and prediction methods
  private generateTaskMetadata(objective: string, messages: any[]): TaskMetadata {
    // Analyze objective and messages to determine metadata
    const lowerObjective = objective.toLowerCase();
    
    let category: TaskMetadata['category'] = 'research';
    if (lowerObjective.includes('bug') || lowerObjective.includes('fix') || lowerObjective.includes('error')) {
      category = 'bug_fix';
    } else if (lowerObjective.includes('add') || lowerObjective.includes('create') || lowerObjective.includes('implement')) {
      category = 'feature_addition';
    } else if (lowerObjective.includes('api') || lowerObjective.includes('integrate')) {
      category = 'integration';
    } else if (lowerObjective.includes('improve') || lowerObjective.includes('optimize') || lowerObjective.includes('enhance')) {
      category = 'optimization';
    } else if (lowerObjective.includes('self') || lowerObjective.includes('tool') || lowerObjective.includes('agent')) {
      category = 'self_improvement';
    }
    
    let complexity: TaskMetadata['complexity'] = 'moderate';
    if (messages.length > 10 || lowerObjective.length > 200) {
      complexity = 'complex';
    } else if (messages.length < 3 && lowerObjective.length < 50) {
      complexity = 'simple';
    }
    
    return {
      objective,
      approach: 'incremental',
      expectedOutcome: 'successful_completion',
      complexity,
      category,
      priority: 'medium',
      tags: this.extractTags(objective),
      isCodeModification: category !== 'research',
      isSelfImprovement: category === 'self_improvement',
      isAPIIntegration: category === 'integration',
      affectedComponents: [],
      expectedHotReloads: category === 'self_improvement' ? 2 : 1,
      actualHotReloads: 0,
      hotReloadTolerant: true,
    };
  }

  private extractTags(objective: string): string[] {
    const commonTags = ['api', 'tool', 'agent', 'bug', 'feature', 'test', 'docs', 'ui', 'backend'];
    const lowerObjective = objective.toLowerCase();
    
    return commonTags.filter(tag => lowerObjective.includes(tag));
  }

  private calculateResumptionConfidence(task: ActiveTaskState): number {
    let confidence = 1.0;
    
    // Reduce confidence based on age
    const ageHours = (Date.now() - task.lastCheckpoint.getTime()) / (1000 * 60 * 60);
    confidence -= Math.min(0.5, ageHours * 0.1);
    
    // Reduce confidence for failed tools
    const failedTools = task.toolExecutions.filter(t => t.status === 'failed');
    confidence -= failedTools.length * 0.2;
    
    // Increase confidence for completed steps
    confidence += task.completedSteps.length * 0.1;
    
    // Increase confidence for recent activity
    const recentActivity = task.toolExecutions.filter(
      t => t.startTime && Date.now() - t.startTime.getTime() < 30 * 60 * 1000 // 30 minutes
    );
    confidence += recentActivity.length * 0.1;
    
    return Math.min(1.0, Math.max(0.0, confidence));
  }

  private predictNextActions(task: ActiveTaskState, lastTool: ToolExecution): string[] {
    const actions: string[] = [];
    
    switch (lastTool.toolName) {
      case 'web_search':
        actions.push('analyze_search_results', 'extract_relevant_info');
        break;
      case 'read_file':
        actions.push('analyze_file_content', 'identify_modifications_needed');
        break;
      case 'file_edit':
        actions.push('validate_changes', 'run_tests');
        break;
      default:
        actions.push('continue_with_next_step', 'validate_progress');
    }
    
    return actions;
  }

  private isHotReloadTriggerFile(filePath: string): boolean {
    const hotReloadExtensions = ['.ts', '.js', '.tsx', '.jsx', '.vue'];
    return hotReloadExtensions.some(ext => filePath.endsWith(ext));
  }

  // Utility methods
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  }

  private queueTaskForSaving(taskId: string): void {
    this.saveQueue.add(taskId);
  }

  private setupAutoSave(): void {
    this.saveInterval = setInterval(async () => {
      if (this.saveQueue.size > 0) {
        const tasksToSave = Array.from(this.saveQueue);
        this.saveQueue.clear();
        
        for (const taskId of tasksToSave) {
          const task = this.activeTasks.get(taskId);
          if (task) {
            await this.saveTaskState(task);
          }
        }
      }
    }, 5000); // Save every 5 seconds
  }

  private async ensureDirectories(): Promise<void> {
    const dirs = [this.RECOVERY_DIR, this.TASKS_DIR, this.SESSIONS_DIR];
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.warn(`Failed to create directory ${dir}:`, error);
      }
    }
  }

  private async loadActiveTasks(): Promise<void> {
    try {
      const files = await fs.readdir(this.TASKS_DIR);
      const taskFiles = files.filter(f => f.endsWith('.json'));
      
      for (const file of taskFiles) {
        const taskId = file.replace('.json', '');
        const task = await this.loadTaskState(taskId);
        
        if (task && task.isResumable) {
          this.activeTasks.set(taskId, task);
        }
      }
      
      console.log(`📥 Loaded ${this.activeTasks.size} resumable tasks`);
    } catch (error) {
      console.warn('Failed to load active tasks:', error);
    }
  }

  private async createOrLoadCurrentSession(): Promise<void> {
    try {
      // Try to load the most recent session
      const sessions = await this.getRecoveryHistory(1);
      
      if (sessions.length > 0 && !sessions[0].endTime) {
        // Continue existing session
        this.currentSession = sessions[0];
        console.log(`📋 Continuing recovery session: ${this.currentSession.sessionId}`);
      } else {
        // Create new session
        await this.createRecoverySession();
        console.log(`📋 Created new recovery session: ${this.currentSession?.sessionId}`);
      }
    } catch (error) {
      console.warn('Failed to load/create recovery session:', error);
      await this.createRecoverySession();
    }
  }

  private async saveRecoverySession(session: RecoverySession): Promise<void> {
    const sessionFile = path.join(this.SESSIONS_DIR, `${session.sessionId}.json`);
    await fs.writeFile(sessionFile, JSON.stringify(session, null, 2));
  }

  private async updateTaskIndex(task: ActiveTaskState): Promise<void> {
    // Simple index for quick lookup
    try {
      let index: any = {};
      
      try {
        const content = await fs.readFile(this.INDEX_FILE, 'utf-8');
        index = JSON.parse(content);
      } catch {
        // Index doesn't exist
      }
      
      index[task.taskId] = {
        sessionId: task.sessionId,
        agentName: task.agentName,
        progress: task.progress,
        isResumable: task.isResumable,
        lastCheckpoint: task.lastCheckpoint.toISOString(),
      };
      
      await fs.writeFile(this.INDEX_FILE, JSON.stringify(index, null, 2));
    } catch (error) {
      console.warn('Failed to update task index:', error);
    }
  }

  private async removeFromTaskIndex(taskId: string): Promise<void> {
    try {
      const content = await fs.readFile(this.INDEX_FILE, 'utf-8');
      const index = JSON.parse(content);
      delete index[taskId];
      await fs.writeFile(this.INDEX_FILE, JSON.stringify(index, null, 2));
    } catch (error) {
      console.warn('Failed to remove from task index:', error);
    }
  }

  private parseTaskDates(task: any): ActiveTaskState {
    return {
      ...task,
      createdAt: new Date(task.createdAt),
      lastCheckpoint: new Date(task.lastCheckpoint),
      estimatedCompletion: task.estimatedCompletion ? new Date(task.estimatedCompletion) : undefined,
      conversationContext: {
        ...task.conversationContext,
        checkpoints: task.conversationContext.checkpoints.map((cp: any) => ({
          ...cp,
          timestamp: new Date(cp.timestamp),
        })),
      },
      toolExecutions: task.toolExecutions.map((te: any) => ({
        ...te,
        startTime: new Date(te.startTime),
        endTime: te.endTime ? new Date(te.endTime) : undefined,
      })),
      fileModifications: task.fileModifications.map((fm: any) => ({
        ...fm,
        timestamp: new Date(fm.timestamp),
      })),
    };
  }

  private parseSessionDates(session: any): RecoverySession {
    return {
      ...session,
      startTime: new Date(session.startTime),
      endTime: session.endTime ? new Date(session.endTime) : undefined,
      hotReloadEvents: session.hotReloadEvents.map((event: any) => ({
        ...event,
        timestamp: new Date(event.timestamp),
      })),
      activeTasks: session.activeTasks.map(this.parseTaskDates.bind(this)),
    };
  }

  private async storeCompletedTaskPattern(task: ActiveTaskState): Promise<void> {
    try {
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        {
          type: 'completed_task_pattern',
          objective: task.metadata.objective,
          approach: task.metadata.approach,
          steps: task.completedSteps,
          toolsUsed: task.toolExecutions.map(te => te.toolName),
          duration: task.lastCheckpoint.getTime() - task.createdAt.getTime(),
          success: true,
        },
        {
          priority: MemoryPriority.HIGH,
          tags: ['task_completion', task.metadata.category, ...task.metadata.tags],
          confidence: 0.9,
        }
      );
    } catch (error) {
      console.warn('Failed to store completed task pattern:', error);
    }
  }

  private async storeRecoveryPattern(taskId: string, strategy: string, success: boolean): Promise<void> {
    try {
      await memoryManager.create(
        MemoryType.PROCEDURAL,
        {
          type: 'recovery_pattern',
          taskId,
          strategy,
          success,
          timestamp: new Date(),
        },
        {
          priority: success ? MemoryPriority.HIGH : MemoryPriority.MEDIUM,
          tags: ['recovery', success ? 'successful' : 'failed', strategy],
          confidence: 0.8,
        }
      );
    } catch (error) {
      console.warn('Failed to store recovery pattern:', error);
    }
  }

  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('🛑 Shutting down Active Task Manager...');
      
      if (this.saveInterval) {
        clearInterval(this.saveInterval);
      }
      
      // Save any pending tasks
      if (this.saveQueue.size > 0) {
        console.log(`💾 Saving ${this.saveQueue.size} pending tasks...`);
        const tasksToSave = Array.from(this.saveQueue);
        
        for (const taskId of tasksToSave) {
          const task = this.activeTasks.get(taskId);
          if (task) {
            await this.saveTaskState(task);
          }
        }
      }
      
      // Close current session
      if (this.currentSession && !this.currentSession.endTime) {
        this.currentSession.endTime = new Date();
        await this.saveRecoverySession(this.currentSession);
      }
      
      console.log('✅ Active Task Manager shut down complete');
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    process.on('beforeExit', shutdown);
  }
}

// Export singleton instance
export const activeTaskManager = new ActiveTaskManager();
