import { EventEmitter } from 'events';
import {
  ActiveTaskState,
  AgentContext,
  ConversationCheckpoint,
  ToolExecution,
  ResumptionPoint,
} from './types';
import { sessionManager } from '../utils/sessionManager';

// Model switching interfaces
interface ModelSwitchContext {
  switchId: string;
  fromModel: string;
  toModel: string;
  reason?: string;
  userRequested: boolean;
  timestamp: Date;
  preservedState: AgentStateSnapshot;
}

interface AgentStateSnapshot {
  conversationHistory: any[];
  toolStates: Map<string, any>;
  memoryContext: any;
  mcpConnections: any[];
  activeHandoffs: string[];
  taskProgress: any;
  performanceMetrics: any;
}

interface ModelSwitchCheckpoint extends ConversationCheckpoint {
  modelSwitchContext: ModelSwitchContext;
  preSwitch: AgentStateSnapshot;
  postSwitch?: AgentStateSnapshot;
  continuityScore: number;
}

export class ContextPreserver extends EventEmitter {
  private preservationQueue = new Map<string, any>();
  private preservationInterval?: NodeJS.Timeout;
  private readonly PRESERVATION_DELAY = 2000; // 2 seconds

  // Model switching state management
  private modelSwitchHistory = new Map<string, ModelSwitchCheckpoint>();
  private activeModelSwitches = new Map<string, ModelSwitchContext>();
  private continuityMetrics = new Map<string, { switches: number; avgScore: number; lastSwitch: Date }>();

  constructor() {
    super();
    this.setupAutoPreservation();
  }

  async preserveConversationContext(
    taskId: string,
    sessionId: string,
    messages: any[],
    agentContext: AgentContext
  ): Promise<ConversationCheckpoint> {
    try {
      // Create checkpoint with current state
      const checkpoint: ConversationCheckpoint = {
        messageIndex: messages.length - 1,
        agentContext: this.cloneAgentContext(agentContext),
        toolExecutions: [], // Will be populated separately
        taskProgress: 0, // Will be updated by ActiveTaskManager
        milestone: this.determineMilestone(messages, agentContext),
        timestamp: new Date(),
      };

      // Store conversation state in session manager
      await this.storeConversationState(sessionId, messages, agentContext);

      // Queue for preservation
      this.queueForPreservation(taskId, {
        type: 'conversation_checkpoint',
        checkpoint,
        messages,
        agentContext,
      });

      console.log(`💾 Preserved conversation context for task: ${taskId}`);
      this.emit('contextPreserved', taskId, checkpoint);

      return checkpoint;
    } catch (error) {
      console.error(`Failed to preserve conversation context for task ${taskId}:`, error);
      throw error;
    }
  }

  async preserveAgentState(
    taskId: string,
    agentName: string,
    currentState: any,
    availableTools: string[],
    lastActivity: Date
  ): Promise<AgentContext> {
    try {
      const agentContext: AgentContext = {
        agentName,
        model: currentState.model || 'gpt-5',
        instructions: currentState.instructions || '',
        availableTools,
        currentHandoff: currentState.currentHandoff,
        lastActivity,
        state: this.determineAgentState(currentState),
      };

      this.queueForPreservation(taskId, {
        type: 'agent_state',
        agentContext,
        rawState: currentState,
      });

      console.log(`🤖 Preserved agent state for task: ${taskId} (${agentName})`);
      this.emit('agentStatePreserved', taskId, agentContext);

      return agentContext;
    } catch (error) {
      console.error(`Failed to preserve agent state for task ${taskId}:`, error);
      throw error;
    }
  }

  async preserveToolExecutionContext(
    taskId: string,
    toolExecution: ToolExecution,
    executionContext: any
  ): Promise<void> {
    try {
      const preservedExecution = {
        ...toolExecution,
        executionContext: this.sanitizeExecutionContext(executionContext),
        preservedAt: new Date(),
      };

      this.queueForPreservation(taskId, {
        type: 'tool_execution',
        toolExecution: preservedExecution,
      });

      console.log(`🔧 Preserved tool execution for task: ${taskId} (${toolExecution.toolName})`);
      this.emit('toolExecutionPreserved', taskId, preservedExecution);
    } catch (error) {
      console.error(`Failed to preserve tool execution for task ${taskId}:`, error);
    }
  }

  async reconstructConversationContext(
    taskId: string,
    checkpoint: ConversationCheckpoint
  ): Promise<{
    messages: any[];
    agentContext: AgentContext;
    reconstructionNotes: string[];
  }> {
    try {
      console.log(`🔄 Reconstructing conversation context for task: ${taskId}`);

      const reconstructionNotes: string[] = [];

      // Try to get full conversation from session manager
      let messages: any[] = [];
      try {
        const sessionData = await sessionManager.getCurrentSession();
        if (sessionData?.messages) {
          messages = sessionData.messages.slice(0, checkpoint.messageIndex + 1);
          reconstructionNotes.push('Retrieved full conversation from session');
        }
      } catch (error) {
        reconstructionNotes.push('Could not retrieve full session data');
      }

      // If we don't have messages, create minimal context
      if (messages.length === 0) {
        messages = [
          {
            role: 'system',
            content: `Resuming task: ${taskId}. Previous context was lost, proceeding with available information.`,
            timestamp: Date.now(),
          },
        ];
        reconstructionNotes.push('Created minimal conversation context');
      }

      // Reconstruct agent context
      const agentContext = this.reconstructAgentContext(checkpoint.agentContext, reconstructionNotes);

      // Add reconstruction metadata to the context
      const reconstructionMessage = {
        role: 'system',
        content: `Context Reconstruction Summary:
- Milestone: ${checkpoint.milestone}
- Tool executions: ${checkpoint.toolExecutions.length}
- Reconstruction notes: ${reconstructionNotes.join(', ')}
- Confidence: ${this.calculateReconstructionConfidence(checkpoint, messages)}`,
        timestamp: Date.now(),
        agentEvents: [{
          type: 'context_reconstruction',
          taskId,
          checkpoint,
          notes: reconstructionNotes,
        }],
      };

      messages.push(reconstructionMessage);

      console.log(`✅ Reconstructed conversation context for task: ${taskId}`);
      this.emit('contextReconstructed', taskId, { messages, agentContext, reconstructionNotes });

      return { messages, agentContext, reconstructionNotes };
    } catch (error) {
      console.error(`Failed to reconstruct conversation context for task ${taskId}:`, error);
      throw error;
    }
  }

  async generateResumptionContext(
    task: ActiveTaskState,
    resumptionPoint: ResumptionPoint
  ): Promise<string> {
    try {
      const context = [
        `Task Resumption Context:`,
        ``,
        `Task ID: ${task.taskId}`,
        `Objective: ${task.metadata.objective}`,
        `Progress: ${task.progress}%`,
        `Current Step: ${task.currentStep}`,
        ``,
        `Resumption Point:`,
        `- Type: ${resumptionPoint.type}`,
        `- Description: ${resumptionPoint.description}`,
        `- Last Action: ${resumptionPoint.lastSuccessfulAction}`,
        `- Strategy: ${resumptionPoint.recoveryStrategy}`,
        ``,
        `Completed Steps:`,
        task.completedSteps.map(step => `- ${step}`).join('\n'),
        ``,
        `Recent Tool Executions:`,
        task.toolExecutions
          .slice(-3)
          .map(te => `- ${te.toolName}: ${te.status} (${te.startTime.toISOString()})`)
          .join('\n'),
        ``,
        `File Modifications:`,
        task.fileModifications
          .slice(-5)
          .map(fm => `- ${fm.action}: ${fm.path} (${fm.source})`)
          .join('\n'),
        ``,
        `Next Actions:`,
        resumptionPoint.nextActions.map(action => `- ${action}`).join('\n'),
        ``,
        `Recovery Confidence: ${task.resumptionConfidence.toFixed(2)}`,
        `Is Resumable: ${task.isResumable}`,
      ];

      return context.join('\n');
    } catch (error) {
      console.error(`Failed to generate resumption context for task ${task.taskId}:`, error);
      return `Failed to generate resumption context: ${error}`;
    }
  }

  async createMinimalRecoveryContext(
    taskId: string,
    objective: string,
    lastKnownStep: string
  ): Promise<{
    messages: any[];
    agentContext: AgentContext;
    resumptionPoint: ResumptionPoint;
    reconstructionNotes: string[];
  }> {
    // Create minimal context when full context is lost
    const messages = [
      {
        role: 'user',
        content: objective,
        timestamp: Date.now() - 60000, // 1 minute ago
      },
      {
        role: 'system',
        content: `Task recovery in progress. Last known step: ${lastKnownStep}. Some context may have been lost during recovery.`,
        timestamp: Date.now(),
        agentEvents: [{
          type: 'minimal_recovery',
          taskId,
          lastKnownStep,
        }],
      },
    ];

    const agentContext: AgentContext = {
      agentName: 'DanteOrchestrator', // Default to core agent
      model: 'gemini-2.5-pro',
      instructions: 'You are recovering from an interrupted task. Analyze the available context and determine the best way to proceed.',
      availableTools: [], // Will be populated by the resumption engine
      lastActivity: new Date(),
      state: 'thinking',
    };

    const resumptionPoint: ResumptionPoint = {
      type: 'analysis_phase',
      description: 'Minimal recovery - analyzing available context',
      lastSuccessfulAction: lastKnownStep || 'task_created',
      nextActions: ['assess_current_state', 'determine_next_steps', 'continue_or_restart'],
      context: { recoveryType: 'minimal', confidence: 0.3 },
      recoveryStrategy: 'restart_phase',
    };

    console.log(`🔧 Created minimal recovery context for task: ${taskId}`);

    const reconstructionNotes = ['Minimal recovery context created'];
    return { messages, agentContext, resumptionPoint, reconstructionNotes };
  }

  // Private helper methods
  private cloneAgentContext(agentContext: AgentContext): AgentContext {
    return {
      ...agentContext,
      availableTools: [...agentContext.availableTools],
      lastActivity: new Date(agentContext.lastActivity),
    };
  }

  private determineMilestone(messages: any[], agentContext: AgentContext): string {
    const lastMessage = messages[messages.length - 1];
    const messageCount = messages.length;

    if (messageCount <= 2) {
      return 'task_initialization';
    } else if (lastMessage?.content?.includes('analysis') || lastMessage?.content?.includes('research')) {
      return 'analysis_phase';
    } else if (lastMessage?.content?.includes('implement') || lastMessage?.content?.includes('create')) {
      return 'implementation_phase';
    } else if (lastMessage?.content?.includes('test') || lastMessage?.content?.includes('verify')) {
      return 'testing_phase';
    } else if (agentContext.state === 'using_tool') {
      return 'tool_execution';
    } else if (agentContext.currentHandoff) {
      return 'agent_handoff';
    } else {
      return 'in_progress';
    }
  }

  private determineAgentState(currentState: any): AgentContext['state'] {
    if (currentState.isExecutingTool) {
      return 'using_tool';
    } else if (currentState.isHandingOff || currentState.pendingHandoff) {
      return 'handoff';
    } else if (currentState.isWaiting || currentState.awaitingInput) {
      return 'waiting';
    } else if (currentState.isThinking || currentState.analyzing) {
      return 'thinking';
    } else {
      return 'idle';
    }
  }

  private sanitizeExecutionContext(context: any): any {
    // Remove sensitive or large data from execution context
    const sanitized = { ...context };

    // Remove potentially sensitive data
    delete sanitized.apiKeys;
    delete sanitized.secrets;
    delete sanitized.passwords;

    // Truncate large strings
    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'string' && value.length > 10000) {
        sanitized[key] = value.substring(0, 10000) + '... [truncated]';
      }
    }

    return sanitized;
  }

  private reconstructAgentContext(
    checkpointContext: AgentContext,
    reconstructionNotes: string[]
  ): AgentContext {
    const reconstructed = this.cloneAgentContext(checkpointContext);

    // Update state based on time elapsed
    const timeSinceActivity = Date.now() - reconstructed.lastActivity.getTime();

    if (timeSinceActivity > 60000) { // More than 1 minute
      reconstructed.state = 'thinking'; // Reset to thinking state
      reconstructionNotes.push('Reset agent state due to time elapsed');
    }

    // Clear any pending handoffs (they may no longer be valid)
    if (reconstructed.currentHandoff) {
      reconstructed.currentHandoff = undefined;
      reconstructionNotes.push('Cleared pending handoff');
    }

    return reconstructed;
  }

  private calculateReconstructionConfidence(
    checkpoint: ConversationCheckpoint,
    messages: any[]
  ): number {
    let confidence = 1.0;

    // Reduce confidence based on age
    const ageMinutes = (Date.now() - checkpoint.timestamp.getTime()) / (1000 * 60);
    confidence -= Math.min(0.6, ageMinutes * 0.01);

    // Reduce confidence if we have fewer messages than expected
    const expectedMessages = checkpoint.messageIndex + 1;
    if (messages.length < expectedMessages) {
      confidence -= 0.3;
    }

    // Increase confidence for recent tool executions
    const recentTools = checkpoint.toolExecutions.filter(
      te => Date.now() - te.startTime.getTime() < 30 * 60 * 1000
    );
    confidence += recentTools.length * 0.1;

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  private async storeConversationState(
    sessionId: string,
    messages: any[],
    agentContext: AgentContext
  ): Promise<void> {
    try {
      // Store key conversation elements in session
      for (const message of messages.slice(-5)) { // Last 5 messages
        await sessionManager.addMessage({
          role: message.role,
          content: message.content,
          timestamp: message.timestamp || Date.now(),
          agentEvents: message.agentEvents,
        });
      }
    } catch (error) {
      console.warn('Failed to store conversation state in session:', error);
    }
  }

  private queueForPreservation(taskId: string, data: any): void {
    this.preservationQueue.set(taskId, {
      ...data,
      timestamp: new Date(),
    });
  }

  private setupAutoPreservation(): void {
    this.preservationInterval = setInterval(() => {
      this.processPreservationQueue();
    }, this.PRESERVATION_DELAY);
  }

  private async processPreservationQueue(): Promise<void> {
    if (this.preservationQueue.size === 0) return;

    const items = Array.from(this.preservationQueue.entries());
    this.preservationQueue.clear();

    for (const [taskId, data] of items) {
      try {
        // Here we could store the preservation data in a more permanent way
        // For now, we just emit events for other systems to handle
        this.emit('preservationProcessed', taskId, data);
      } catch (error) {
        console.error(`Failed to process preservation for task ${taskId}:`, error);
      }
    }
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Context Preserver...');

    if (this.preservationInterval) {
      clearInterval(this.preservationInterval);
    }

    // Process any remaining items in the queue
    await this.processPreservationQueue();

    console.log('✅ Context Preserver shutdown complete');
  }

  /**
   * Preserve context before a model switch with comprehensive state capture
   */
  async preserveModelSwitchContext(
    sessionId: string,
    switchContext: {
      fromModel: string;
      toModel: string;
      reason?: string;
      userRequested: boolean;
      conversationHistory: any[];
      toolStates: Map<string, any>;
      memoryContext: any;
      mcpConnections: any[];
      activeHandoffs: string[];
      taskProgress: any;
    }
  ): Promise<ModelSwitchCheckpoint> {
    const switchId = this.generateSwitchId();

    try {
      console.log(`💾 Preserving model switch context: ${switchContext.fromModel} → ${switchContext.toModel}`);

      // Create comprehensive state snapshot
      const stateSnapshot: AgentStateSnapshot = {
        conversationHistory: this.deepClone(switchContext.conversationHistory),
        toolStates: new Map(switchContext.toolStates),
        memoryContext: this.deepClone(switchContext.memoryContext),
        mcpConnections: [...switchContext.mcpConnections],
        activeHandoffs: [...switchContext.activeHandoffs],
        taskProgress: this.deepClone(switchContext.taskProgress),
        performanceMetrics: await this.capturePerformanceMetrics(switchContext.fromModel)
      };

      // Create model switch context
      const modelSwitchContext: ModelSwitchContext = {
        switchId,
        fromModel: switchContext.fromModel,
        toModel: switchContext.toModel,
        reason: switchContext.reason,
        userRequested: switchContext.userRequested,
        timestamp: new Date(),
        preservedState: stateSnapshot
      };

      // Create conversation checkpoint with model switch extensions
      const checkpoint: ModelSwitchCheckpoint = {
        messageIndex: switchContext.conversationHistory.length - 1,
        agentContext: {
          agentName: 'DanteUnified',
          model: switchContext.fromModel,
          instructions: `Pre-switch context for ${switchContext.fromModel}`,
          availableTools: Array.from(switchContext.toolStates.keys()),
          lastActivity: new Date(),
          state: 'model_switching' as any
        },
        toolExecutions: this.extractToolExecutions(switchContext.conversationHistory),
        taskProgress: this.calculateTaskProgress(switchContext.taskProgress),
        milestone: 'model_switch_preparation',
        timestamp: new Date(),
        modelSwitchContext,
        preSwitch: stateSnapshot,
        continuityScore: this.calculatePreSwitchContinuityScore(stateSnapshot)
      };

      // Store in active switches and history
      this.activeModelSwitches.set(switchId, modelSwitchContext);
      this.modelSwitchHistory.set(switchId, checkpoint);

      // Preserve in session manager with model switch metadata
      await this.storeModelSwitchInSession(sessionId, checkpoint);

      // Queue for preservation
      this.queueForPreservation(switchId, {
        type: 'model_switch_preservation',
        checkpoint,
        sessionId
      });

      console.log(`✅ Model switch context preserved with ID: ${switchId}`);
      this.emit('modelSwitchContextPreserved', switchId, checkpoint);

      return checkpoint;

    } catch (error) {
      console.error(`❌ Failed to preserve model switch context:`, error);
      throw error;
    }
  }

  /**
   * Complete a model switch by validating context continuity
   */
  async completeModelSwitch(
    switchId: string,
    postSwitchState: {
      conversationHistory: any[];
      toolStates: Map<string, any>;
      memoryContext: any;
      mcpConnections: any[];
      activeHandoffs: string[];
      taskProgress: any;
    }
  ): Promise<{ success: boolean; continuityScore: number; issues: string[] }> {
    try {
      const checkpoint = this.modelSwitchHistory.get(switchId);
      if (!checkpoint) {
        throw new Error(`Model switch checkpoint not found for ID: ${switchId}`);
      }

      console.log(`🔄 Completing model switch: ${checkpoint.modelSwitchContext.fromModel} → ${checkpoint.modelSwitchContext.toModel}`);

      // Create post-switch snapshot
      const postSwitchSnapshot: AgentStateSnapshot = {
        conversationHistory: [...postSwitchState.conversationHistory],
        toolStates: new Map(postSwitchState.toolStates),
        memoryContext: { ...postSwitchState.memoryContext },
        mcpConnections: [...postSwitchState.mcpConnections],
        activeHandoffs: [...postSwitchState.activeHandoffs],
        taskProgress: { ...postSwitchState.taskProgress },
        performanceMetrics: await this.capturePerformanceMetrics(checkpoint.modelSwitchContext.toModel)
      };

      // Validate context continuity
      const continuityAnalysis = this.validateContextContinuity(checkpoint.preSwitch, postSwitchSnapshot);

      // Update checkpoint with post-switch data
      checkpoint.postSwitch = postSwitchSnapshot;
      checkpoint.continuityScore = continuityAnalysis.score;

      // Update continuity metrics
      this.updateContinuityMetrics(
        checkpoint.modelSwitchContext.toModel,
        continuityAnalysis.score
      );

      // Remove from active switches
      this.activeModelSwitches.delete(switchId);

      // Log detailed results
      console.log(`✅ Model switch completed:`, {
        switchId,
        continuityScore: continuityAnalysis.score.toFixed(3),
        issues: continuityAnalysis.issues,
        success: continuityAnalysis.score > 0.7
      });

      this.emit('modelSwitchCompleted', {
        switchId,
        checkpoint,
        continuityScore: continuityAnalysis.score,
        issues: continuityAnalysis.issues
      });

      return {
        success: continuityAnalysis.score > 0.7,
        continuityScore: continuityAnalysis.score,
        issues: continuityAnalysis.issues
      };

    } catch (error) {
      console.error(`❌ Failed to complete model switch for ${switchId}:`, error);
      throw error;
    }
  }

  /**
   * Restore context after a failed model switch
   */
  async restoreFromModelSwitchFailure(switchId: string): Promise<AgentStateSnapshot> {
    try {
      const checkpoint = this.modelSwitchHistory.get(switchId);
      if (!checkpoint) {
        throw new Error(`Model switch checkpoint not found for restoration: ${switchId}`);
      }

      console.log(`🔧 Restoring from failed model switch: ${switchId}`);

      // Get the preserved pre-switch state
      const restoredState = checkpoint.preSwitch;

      // Add restoration metadata to conversation history
      restoredState.conversationHistory.push({
        role: 'system',
        content: `Model switch from ${checkpoint.modelSwitchContext.fromModel} to ${checkpoint.modelSwitchContext.toModel} failed and was restored. Previous context has been recovered.`,
        timestamp: Date.now(),
        agentEvents: [{
          type: 'model_switch_failure_restoration',
          switchId,
          originalModel: checkpoint.modelSwitchContext.fromModel,
          attemptedModel: checkpoint.modelSwitchContext.toModel,
          restorationTime: new Date()
        }]
      });

      // Remove from active switches
      this.activeModelSwitches.delete(switchId);

      console.log(`✅ Context restored from model switch failure: ${switchId}`);
      this.emit('modelSwitchRestored', switchId, restoredState);

      return restoredState;

    } catch (error) {
      console.error(`❌ Failed to restore from model switch failure:`, error);
      throw error;
    }
  }

  /**
   * Get model switching analytics and performance data
   */
  getModelSwitchAnalytics(): {
    totalSwitches: number;
    avgContinuityScore: number;
    modelPerformance: Map<string, { switches: number; avgScore: number; lastUsed: Date }>;
    recentSwitches: Array<{
      switchId: string;
      fromModel: string;
      toModel: string;
      timestamp: Date;
      continuityScore: number;
      userRequested: boolean;
    }>;
    activeSwitches: number;
  } {
    const recentSwitches = Array.from(this.modelSwitchHistory.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 20)
      .map(checkpoint => ({
        switchId: checkpoint.modelSwitchContext.switchId,
        fromModel: checkpoint.modelSwitchContext.fromModel,
        toModel: checkpoint.modelSwitchContext.toModel,
        timestamp: checkpoint.modelSwitchContext.timestamp,
        continuityScore: checkpoint.continuityScore,
        userRequested: checkpoint.modelSwitchContext.userRequested
      }));

    const totalSwitches = this.modelSwitchHistory.size;
    const avgContinuityScore = totalSwitches > 0 ?
      Array.from(this.modelSwitchHistory.values())
        .reduce((sum, cp) => sum + cp.continuityScore, 0) / totalSwitches : 0;

    return {
      totalSwitches,
      avgContinuityScore,
      modelPerformance: new Map(Array.from(this.continuityMetrics.entries()).map(([model, data]) => [
        model,
        { switches: data.switches, avgScore: data.avgScore, lastUsed: data.lastSwitch }
      ])),
      recentSwitches,
      activeSwitches: this.activeModelSwitches.size
    };
  }

  // Private helper methods for model switching

  private generateSwitchId(): string {
    return `switch_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * Deep clone utility with guardrails.
   *
   * Behavior:
   * - Attempts `structuredClone` first when available, with fallback to manual cloning.
   * - Handles primitives, `null`, Arrays, `Map`, `Set`, `Date`, `RegExp`, and plain objects.
   * - Preserves circular references via a `WeakMap`.
   * - Enforces a `maxDepth` after which it returns the original object (no deep copy) to avoid runaway recursion.
   *
   * Important limitation:
   * - For class instances and other complex objects that are not plain objects/Arrays/Maps/Sets/Dates/RegExps,
   *   this method returns the original instance (i.e., no deep clone). A warning is logged:
   *   "Returning shallow copy for complex object type: <CtorName>". This avoids breaking behavior of custom
   *   classes, DOM nodes, functions, etc., but callers should be aware that a true deep copy is not performed
   *   for these types. If you need deep cloning of such instances, provide a type-specific cloning strategy
   *   or serialize/deserialize in a controlled manner.
   */
  private deepClone<T>(obj: T, depth: number = 0, maxDepth: number = 10, seen = new WeakMap()): T {
    // Use structuredClone if available (Node.js 17+ and modern browsers)
    if (depth === 0 && typeof structuredClone !== 'undefined') {
      try {
        return structuredClone(obj);
      } catch (error) {
        // Fall back to manual cloning if structuredClone fails
        console.warn('structuredClone failed, falling back to manual cloning:', error);
      }
    }

    // Prevent infinite recursion
    if (depth > maxDepth) {
      console.warn(`Deep clone exceeded max depth of ${maxDepth}, returning shallow copy`);
      return obj;
    }

    // Handle primitives and null
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    // Handle circular references
    if (seen.has(obj as any)) {
      return seen.get(obj as any);
    }

    let cloned: any;

    // Handle special object types
    if (obj instanceof Date) {
      cloned = new Date(obj.getTime());
    } else if (obj instanceof RegExp) {
      cloned = new RegExp(obj.source, obj.flags);
    } else if (obj instanceof Map) {
      cloned = new Map();
      seen.set(obj as any, cloned);
      obj.forEach((value, key) => {
        cloned.set(key, this.deepClone(value, depth + 1, maxDepth, seen));
      });
    } else if (obj instanceof Set) {
      cloned = new Set();
      seen.set(obj as any, cloned);
      obj.forEach(value => {
        cloned.add(this.deepClone(value, depth + 1, maxDepth, seen));
      });
    } else if (Array.isArray(obj)) {
      cloned = [];
      seen.set(obj as any, cloned);
      for (let i = 0; i < obj.length; i++) {
        cloned[i] = this.deepClone(obj[i], depth + 1, maxDepth, seen);
      }
    } else if (obj.constructor === Object || obj.constructor === undefined) {
      // Only clone plain objects to avoid issues with class instances
      cloned = {};
      seen.set(obj as any, cloned);
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          cloned[key] = this.deepClone((obj as any)[key], depth + 1, maxDepth, seen);
        }
      }
    } else {
      // For class instances and other complex objects, return a shallow copy
      // This prevents issues with functions, DOM elements, etc.
      console.warn(`Returning shallow copy for complex object type: ${obj.constructor?.name}`);
      return obj;
    }

    return cloned;
  }

  private async capturePerformanceMetrics(model: string): Promise<any> {
    // Capture performance metrics for the model
    return {
      model,
      timestamp: new Date(),
      memoryUsage: process.memoryUsage ? process.memoryUsage() : {},
      uptime: process.uptime ? process.uptime() : 0,
      // Add more performance metrics as needed
    };
  }

  private extractToolExecutions(conversationHistory: any[]): ToolExecution[] {
    const toolExecutions: ToolExecution[] = [];

    for (const message of conversationHistory) {
      if (message.agentEvents) {
        for (const event of message.agentEvents) {
          if (event.type === 'tool_execution' || event.type === 'tool_call') {
            toolExecutions.push({
              toolName: event.toolName || event.name || 'unknown',
              startTime: new Date(event.timestamp || message.timestamp),
              endTime: new Date((event.timestamp || message.timestamp) + (event.duration || 1000)),
              status: event.success ? 'completed' : 'failed',
              parameters: event.input || event.arguments || {},
              result: event.output || event.result || '',
              error: event.error
            });
          }
        }
      }
    }

    return toolExecutions;
  }

  private calculateTaskProgress(taskProgress: any): number {
    if (!taskProgress) return 0;

    if (typeof taskProgress === 'number') {
      return Math.max(0, Math.min(100, taskProgress));
    }

    if (taskProgress.percentage) {
      return Math.max(0, Math.min(100, taskProgress.percentage));
    }

    if (taskProgress.completed && taskProgress.total) {
      return Math.round((taskProgress.completed / taskProgress.total) * 100);
    }

    return 0;
  }

  private calculatePreSwitchContinuityScore(stateSnapshot: AgentStateSnapshot): number {
    let score = 1.0;

    // Factor in conversation length (more context = higher continuity potential)
    const conversationLength = stateSnapshot.conversationHistory.length;
    if (conversationLength < 5) score -= 0.2;
    else if (conversationLength > 20) score += 0.1;

    // Factor in active tool states
    const activeToolStates = stateSnapshot.toolStates.size;
    if (activeToolStates === 0) score -= 0.1;
    else score += Math.min(0.2, activeToolStates * 0.05);

    // Factor in memory context
    const memoryContextSize = Object.keys(stateSnapshot.memoryContext).length;
    score += Math.min(0.2, memoryContextSize * 0.02);

    // Factor in MCP connections
    if (stateSnapshot.mcpConnections.length > 0) score += 0.1;

    // Factor in active handoffs (negative impact for continuity)
    if (stateSnapshot.activeHandoffs.length > 0) score -= 0.1;

    return Math.max(0.1, Math.min(1.0, score));
  }

  private validateContextContinuity(
    preSwitch: AgentStateSnapshot,
    postSwitch: AgentStateSnapshot
  ): { score: number; issues: string[] } {
    const issues: string[] = [];
    let score = 1.0;

    // Validate conversation history preservation
    if (postSwitch.conversationHistory.length < preSwitch.conversationHistory.length) {
      issues.push('Conversation history was truncated during switch');
      score -= 0.3;
    }

    // Validate tool states
    const preSwitchTools = preSwitch.toolStates.size;
    const postSwitchTools = postSwitch.toolStates.size;
    if (postSwitchTools < preSwitchTools * 0.8) {
      issues.push('Significant tool states were lost during switch');
      score -= 0.2;
    }

    // Validate memory context
    const preMemoryKeys = Object.keys(preSwitch.memoryContext).length;
    const postMemoryKeys = Object.keys(postSwitch.memoryContext).length;
    if (postMemoryKeys < preMemoryKeys * 0.9) {
      issues.push('Memory context was partially lost during switch');
      score -= 0.2;
    }

    // Validate MCP connections
    if (postSwitch.mcpConnections.length < preSwitch.mcpConnections.length) {
      issues.push('MCP connections were lost during switch');
      score -= 0.1;
    }

    // Validate task progress continuity
    if (preSwitch.taskProgress && !postSwitch.taskProgress) {
      issues.push('Active task progress was lost during switch');
      score -= 0.3;
    }

    // Check for positive continuity indicators
    if (issues.length === 0) {
      score = Math.min(1.0, score + 0.1); // Bonus for perfect continuity
    }

    return {
      score: Math.max(0.0, score),
      issues
    };
  }

  private updateContinuityMetrics(model: string, continuityScore: number): void {
    const existing = this.continuityMetrics.get(model) || {
      switches: 0,
      avgScore: 0,
      lastSwitch: new Date()
    };

    const newSwitches = existing.switches + 1;
    const newAvgScore = ((existing.avgScore * existing.switches) + continuityScore) / newSwitches;

    this.continuityMetrics.set(model, {
      switches: newSwitches,
      avgScore: newAvgScore,
      lastSwitch: new Date()
    });
  }

  private async storeModelSwitchInSession(sessionId: string, checkpoint: ModelSwitchCheckpoint): Promise<void> {
    try {
      // Add model switch event to session
      await sessionManager.addMessage({
        role: 'system',
        content: `Model switch initiated: ${checkpoint.modelSwitchContext.fromModel} → ${checkpoint.modelSwitchContext.toModel}`,
        timestamp: checkpoint.timestamp.getTime(),
        model: checkpoint.modelSwitchContext.toModel,
        agentEvents: [{
          type: 'model_switch',
          switchId: checkpoint.modelSwitchContext.switchId,
          fromModel: checkpoint.modelSwitchContext.fromModel,
          toModel: checkpoint.modelSwitchContext.toModel,
          reason: checkpoint.modelSwitchContext.reason,
          userRequested: checkpoint.modelSwitchContext.userRequested,
          continuityScore: checkpoint.continuityScore
        }]
      });

    } catch (error) {
      console.warn('⚠️ Failed to store model switch in session:', error);
    }
  }
}

// Export singleton instance
export const contextPreserver = new ContextPreserver();
