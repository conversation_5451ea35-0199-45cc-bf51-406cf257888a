import chokidar from 'chokidar';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import {
  FileChange,
  HotReloadEvent,
  RecoveryConfiguration,
  HotReloadDetectionError,
} from './types';

export class HotReloadDetector extends EventEmitter {
  private watcher?: any;
  private isWatching = false;
  private fileHashes = new Map<string, string>();
  private recentChanges = new Map<string, Date>();
  private danteModifications = new Set<string>();
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  
  constructor(
    private config: RecoveryConfiguration,
    private projectRoot: string = process.cwd()
  ) {
    super();
    this.setupGracefulShutdown();
  }

  async startWatching(): Promise<void> {
    if (this.isWatching) {
      console.warn('HotReloadDetector is already watching');
      return;
    }

    try {
      console.log('🔍 Starting hot-reload detection...');
      
      // Initialize file hashes for existing files
      await this.initializeFileHashes();
      
      // Setup chokidar watcher
      this.watcher = chokidar.watch(this.config.watchPaths, {
        ignored: this.buildIgnorePatterns(),
        persistent: true,
        ignoreInitial: true,
        awaitWriteFinish: {
          stabilityThreshold: 1000,
          pollInterval: 100,
        },
        usePolling: false,
        interval: 1000,
        binaryInterval: 3000,
      });

      // Setup event listeners
      this.setupWatcherEvents();
      
      this.isWatching = true;
      console.log(`✅ Hot-reload detection active for paths: ${this.config.watchPaths.join(', ')}`);
      this.emit('started');
      
    } catch (error) {
      console.error('Failed to start hot-reload detection:', error);
      throw new HotReloadDetectionError('startup', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async stopWatching(): Promise<void> {
    if (!this.isWatching) return;

    console.log('🛑 Stopping hot-reload detection...');
    
    // Clear debounce timers
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();
    
    // Close watcher
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = undefined;
    }
    
    this.isWatching = false;
    console.log('✅ Hot-reload detection stopped');
    this.emit('stopped');
  }

  markDanteModification(filePath: string): void {
    const relativePath = path.relative(this.projectRoot, filePath);
    this.danteModifications.add(relativePath);
    
    // Auto-remove after 30 seconds to prevent memory leaks
    setTimeout(() => {
      this.danteModifications.delete(relativePath);
    }, 30000);
    
    console.log(`🤖 Marked as Dante modification: ${relativePath}`);
  }

  isDanteModification(filePath: string): boolean {
    const relativePath = path.relative(this.projectRoot, filePath);
    return this.danteModifications.has(relativePath);
  }

  async detectViteRestart(): Promise<boolean> {
    try {
      // Check for common Vite restart indicators
      const indicators = [
        '.vite',
        'dist',
        'node_modules/.vite',
      ];

      for (const indicator of indicators) {
        const indicatorPath = path.join(this.projectRoot, indicator);
        try {
          const stats = await fs.stat(indicatorPath);
          const now = Date.now();
          const modifiedTime = stats.mtime.getTime();
          
          // If modified within last 5 seconds, likely a restart
          if (now - modifiedTime < 5000) {
            console.log(`🔄 Detected Vite restart via ${indicator}`);
            return true;
          }
        } catch {
          // Indicator doesn't exist, continue
        }
      }

      return false;
    } catch (error) {
      console.warn('Error detecting Vite restart:', error);
      return false;
    }
  }

  getRecentChanges(since?: Date): FileChange[] {
    const sinceTime = since || new Date(Date.now() - 30000); // Last 30 seconds
    const changes: FileChange[] = [];
    
    for (const [filePath, changeTime] of this.recentChanges.entries()) {
      if (changeTime >= sinceTime) {
        changes.push({
          path: filePath,
          action: 'change',
          timestamp: changeTime,
          source: this.isDanteModification(filePath) ? 'dante' : 'user',
        });
      }
    }
    
    return changes.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  private async initializeFileHashes(): Promise<void> {
    try {
      console.log('📊 Initializing file hashes...');
      
      for (const watchPath of this.config.watchPaths) {
        const fullPath = path.resolve(this.projectRoot, watchPath);
        await this.hashDirectory(fullPath);
      }
      
      console.log(`✅ Initialized hashes for ${this.fileHashes.size} files`);
    } catch (error) {
      console.warn('Failed to initialize file hashes:', error);
    }
  }

  private async hashDirectory(dirPath: string): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = path.relative(this.projectRoot, fullPath);
        
        // Skip ignored paths
        if (this.isIgnored(relativePath)) continue;
        
        if (entry.isDirectory()) {
          await this.hashDirectory(fullPath);
        } else if (entry.isFile()) {
          try {
            const hash = await this.calculateFileHash(fullPath);
            this.fileHashes.set(relativePath, hash);
          } catch (error) {
            // Skip files we can't read
          }
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }

  private async calculateFileHash(filePath: string): Promise<string> {
    const content = await fs.readFile(filePath);
    return crypto.createHash('md5').update(content).digest('hex');
  }

  private setupWatcherEvents(): void {
    if (!this.watcher) return;

    this.watcher
      .on('add', (filePath: string) => this.handleFileEvent('add', filePath))
      .on('change', (filePath: string) => this.handleFileEvent('change', filePath))
      .on('unlink', (filePath: string) => this.handleFileEvent('unlink', filePath))
      .on('error', (error: any) => {
        console.error('File watcher error:', error);
        this.emit('error', error);
      })
      .on('ready', () => {
        console.log('🎯 File watcher ready');
        this.emit('ready');
      });
  }

  private handleFileEvent(action: 'add' | 'change' | 'unlink', filePath: string): void {
    const relativePath = path.relative(this.projectRoot, filePath);
    
    // Skip ignored files
    if (this.isIgnored(relativePath)) return;
    
    // Debounce rapid changes
    this.debounceFileEvent(action, relativePath);
  }

  private debounceFileEvent(action: 'add' | 'change' | 'unlink', relativePath: string): void {
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(relativePath);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    
    // Set new timer
    const timer = setTimeout(async () => {
      this.debounceTimers.delete(relativePath);
      await this.processFileEvent(action, relativePath);
    }, this.config.debounceDelay);
    
    this.debounceTimers.set(relativePath, timer);
  }

  private async processFileEvent(action: 'add' | 'change' | 'unlink', relativePath: string): Promise<void> {
    try {
      const fullPath = path.join(this.projectRoot, relativePath);
      const now = new Date();
      
      let fileHash: string | undefined;
      let fileSize: number | undefined;
      
      if (action !== 'unlink') {
        try {
          fileHash = await this.calculateFileHash(fullPath);
          const stats = await fs.stat(fullPath);
          fileSize = stats.size;
          
          // Check if file actually changed (for 'change' events)
          if (action === 'change') {
            const previousHash = this.fileHashes.get(relativePath);
            if (previousHash === fileHash) {
              // File didn't actually change, ignore
              return;
            }
          }
          
          this.fileHashes.set(relativePath, fileHash);
        } catch (error) {
          console.warn(`Failed to process file ${relativePath}:`, error);
          return;
        }
      } else {
        // File was deleted
        this.fileHashes.delete(relativePath);
      }
      
      // Update recent changes
      this.recentChanges.set(relativePath, now);
      
      // Create file change object
      const fileChange: FileChange = {
        path: relativePath,
        action,
        timestamp: now,
        size: fileSize,
        hash: fileHash,
        source: this.isDanteModification(relativePath) ? 'dante' : 'user',
      };
      
      console.log(`📝 File ${action}: ${relativePath} (${fileChange.source})`);
      
      // Emit file change event
      this.emit('fileChange', fileChange);
      
      // Check for hot-reload triggers
      await this.checkForHotReload(fileChange);
      
      // Clean up old recent changes (keep last 5 minutes)
      this.cleanupRecentChanges();
      
    } catch (error) {
      console.error(`Error processing file event for ${relativePath}:`, error);
    }
  }

  private async checkForHotReload(fileChange: FileChange): Promise<void> {
    // Determine if this change should trigger a hot-reload check
    const hotReloadExtensions = ['.ts', '.js', '.tsx', '.jsx', '.vue', '.css', '.scss', '.less'];
    const isHotReloadFile = hotReloadExtensions.some(ext => fileChange.path.endsWith(ext));
    
    if (!isHotReloadFile) return;
    
    // Check if Vite has restarted recently
    const viteRestarted = await this.detectViteRestart();
    
    if (viteRestarted || this.shouldTriggerHotReloadCheck(fileChange)) {
      const hotReloadEvent: HotReloadEvent = {
        timestamp: new Date(),
        trigger: viteRestarted ? 'vite_restart' : 'file_save',
        affectedFiles: [fileChange.path],
        relatedTasks: [], // Will be populated by ActiveTaskManager
        recovered: false,
      };
      
      console.log(`🔥 Hot-reload event detected: ${hotReloadEvent.trigger}`);
      this.emit('hotReload', hotReloadEvent);
    }
  }

  private shouldTriggerHotReloadCheck(fileChange: FileChange): boolean {
    // Trigger hot-reload check for Dante modifications in critical paths
    if (fileChange.source === 'dante') {
      const criticalPaths = ['src/tools/', 'src/agents/', 'src/api/'];
      return criticalPaths.some(path => fileChange.path.startsWith(path));
    }
    
    return false;
  }

  private cleanupRecentChanges(): void {
    const cutoff = new Date(Date.now() - 5 * 60 * 1000); // 5 minutes ago
    
    for (const [filePath, changeTime] of this.recentChanges.entries()) {
      if (changeTime < cutoff) {
        this.recentChanges.delete(filePath);
      }
    }
  }

  private buildIgnorePatterns(): string[] {
    const defaultIgnores = [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/.vscode/**',
      '**/.idea/**',
      '**/tmp/**',
      '**/temp/**',
      '**/*.log',
      '**/*.tmp',
      '**/*.swp',
      '**/*.DS_Store',
    ];
    
    return [...defaultIgnores, ...this.config.ignorePaths];
  }

  private isIgnored(filePath: string): boolean {
    const ignorePatterns = this.buildIgnorePatterns();
    
    return ignorePatterns.some(pattern => {
      // Simple glob pattern matching
      const regex = pattern
        .replace(/\*\*/g, '.*')
        .replace(/\*/g, '[^/]*')
        .replace(/\?/g, '.');
      
      return new RegExp(regex).test(filePath);
    });
  }

  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('📡 Received shutdown signal, stopping hot-reload detection...');
      await this.stopWatching();
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    process.on('beforeExit', shutdown);
  }

  // Public API for monitoring
  getWatcherStatus() {
    return {
      isWatching: this.isWatching,
      watchedPaths: this.config.watchPaths,
      trackedFiles: this.fileHashes.size,
      recentChanges: this.recentChanges.size,
      danteModifications: this.danteModifications.size,
    };
  }

  async forceHotReloadCheck(): Promise<HotReloadEvent> {
    const recentChanges = this.getRecentChanges();
    
    const event: HotReloadEvent = {
      timestamp: new Date(),
      trigger: 'manual',
      affectedFiles: recentChanges.map(c => c.path),
      relatedTasks: [],
      recovered: false,
    };
    
    this.emit('hotReload', event);
    return event;
  }
}

// Export singleton instance
export const hotReloadDetector = new HotReloadDetector({
  enabled: true,
  maxActiveTasks: 10,
  checkpointInterval: 5,
  recoveryTimeout: 30,
  watchPaths: ['src/**/*.{ts,js,tsx,jsx,vue}'],
  ignorePaths: ['src/tests/**', 'src/**/*.test.ts', 'src/**/*.spec.ts'],
  debounceDelay: 1000,
  autoResumeEnabled: true,
  requireUserConfirmation: false,
  maxRecoveryAttempts: 3,
  learningEnabled: true,
  patternRecognition: true,
  adaptiveRecovery: true,
});