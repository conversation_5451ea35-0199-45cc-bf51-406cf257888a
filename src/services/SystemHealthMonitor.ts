/**
 * System Health Monitor Service
 * Monitors the health of critical services and provides diagnostic information
 */

import { EventEmitter } from 'events';
import { exec } from 'child_process';
import { promisify } from 'util';
import { mcpServerManager, MCPServerStatus } from '../mcp/MCPServerManager';
import { config } from '../utils/config';
import { 
  isPortInUse, 
  canConnectToPort, 
  getProcessVersion,
  findProcessUsingPort
} from '../utils/platformUtils';
import { processManager } from '../utils/processManager';

const execAsync = promisify(exec);

export interface ServiceHealth {
  name: string;
  type: 'mcp_server' | 'api_endpoint' | 'tool_server' | 'process' | 'dependency';
  status: 'healthy' | 'degraded' | 'failed' | 'unknown';
  lastCheck: Date;
  responseTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'critical';
  services: ServiceHealth[];
  timestamp: Date;
  diagnostics: {
    totalServices: number;
    healthyServices: number;
    degradedServices: number;
    failedServices: number;
  };
  recommendations?: string[];
}

export interface ErrorPattern {
  pattern: string;
  count: number;
  lastOccurred: Date;
  service: string;
  possibleCause?: string;
  suggestedFix?: string;
}

export class SystemHealthMonitor extends EventEmitter {
  private services: Map<string, ServiceHealth> = new Map();
  private errorPatterns: Map<string, ErrorPattern> = new Map();
  private monitoringInterval: NodeJS.Timer | null = null;
  private readonly checkInterval = 30000; // 30 seconds
  private isMonitoring = false;

  constructor() {
    super();
    this.initializeServices();
  }

  /**
   * Initialize the list of services to monitor
   */
  private initializeServices(): void {
    // Add MCP servers
    if (config.mcp?.enabled) {
      this.addService({
        name: 'MCP Server Manager',
        type: 'mcp_server',
        status: 'unknown',
        lastCheck: new Date(),
      });
    }

    // Add critical processes
    this.addService({
      name: 'Node.js Runtime',
      type: 'process',
      status: 'unknown',
      lastCheck: new Date(),
    });

    // Add Bun runtime if used
    this.addService({
      name: 'Bun Runtime',
      type: 'process',
      status: 'unknown',
      lastCheck: new Date(),
    });

    // Add API endpoints
    if (config.api?.enabled) {
      this.addService({
        name: 'API Server',
        type: 'api_endpoint',
        status: 'unknown',
        lastCheck: new Date(),
        metadata: {
          port: config.api.port || 3001,
          host: config.api.host || 'localhost',
        },
      });
    }
  }

  /**
   * Add a service to monitor
   */
  public addService(service: ServiceHealth): void {
    this.services.set(service.name, service);
    this.emit('serviceAdded', service);
  }

  /**
   * Remove a service from monitoring
   */
  public removeService(serviceName: string): void {
    this.services.delete(serviceName);
    this.emit('serviceRemoved', serviceName);
  }

  /**
   * Start monitoring all services
   */
  public async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    
    // Do initial check
    await this.checkAllServices();

    // Set up interval
    this.monitoringInterval = setInterval(async () => {
      await this.checkAllServices();
    }, this.checkInterval);

    this.emit('monitoringStarted');
  }

  /**
   * Stop monitoring
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.isMonitoring = false;
    this.emit('monitoringStopped');
  }

  /**
   * Check all services
   */
  private async checkAllServices(): Promise<void> {
    const checks = Array.from(this.services.values()).map(service => 
      this.checkService(service)
    );

    await Promise.all(checks);
    
    const status = this.getSystemStatus();
    this.emit('healthCheck', status);

    // Emit alerts for critical issues
    if (status.overall === 'critical') {
      this.emit('criticalAlert', status);
    }
  }

  /**
   * Check individual service health
   */
  private async checkService(service: ServiceHealth): Promise<void> {
    const startTime = Date.now();
    
    try {
      switch (service.type) {
        case 'mcp_server':
          await this.checkMCPServer(service);
          break;
        case 'api_endpoint':
          await this.checkAPIEndpoint(service);
          break;
        case 'process':
          await this.checkProcess(service);
          break;
        case 'tool_server':
          await this.checkToolServer(service);
          break;
        default:
          service.status = 'unknown';
      }

      service.responseTime = Date.now() - startTime;
      service.lastCheck = new Date();
      service.error = undefined;

      // Update stored service
      this.services.set(service.name, service);

    } catch (error) {
      service.status = 'failed';
      service.error = error instanceof Error ? error.message : String(error);
      service.lastCheck = new Date();
      service.responseTime = Date.now() - startTime;
      
      // Track error pattern
      this.trackErrorPattern(service.name, service.error);
      
      // Update stored service
      this.services.set(service.name, service);
      
      this.emit('serviceError', service);
    }
  }

  /**
   * Check MCP server health
   */
  private async checkMCPServer(service: ServiceHealth): Promise<void> {
    try {
      const statuses = mcpServerManager.getAllServerStatuses();
      const connectedCount = statuses.filter(s => s.status === 'connected').length;
      const totalCount = statuses.length;

      if (totalCount === 0) {
        service.status = 'failed';
        service.error = 'No MCP servers configured';
      } else if (connectedCount === 0) {
        service.status = 'failed';
        service.error = 'All MCP servers disconnected';
      } else if (connectedCount < totalCount) {
        service.status = 'degraded';
        service.metadata = {
          connected: connectedCount,
          total: totalCount,
          disconnected: statuses.filter(s => s.status !== 'connected').map(s => s.name),
        };
      } else {
        service.status = 'healthy';
        service.metadata = {
          connected: connectedCount,
          total: totalCount,
        };
      }
    } catch (error) {
      throw new Error(`MCP server check failed: ${error}`);
    }
  }

  /**
   * Check API endpoint health
   */
  private async checkAPIEndpoint(service: ServiceHealth): Promise<void> {
    const port = service.metadata?.port || 3001;
    const host = service.metadata?.host || 'localhost';
    
    try {
      const response = await fetch(`http://${host}:${port}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });

      if (response.ok) {
        service.status = 'healthy';
      } else {
        service.status = 'degraded';
        service.error = `API returned status ${response.status}`;
      }
    } catch (error) {
      // Platform-agnostic port check
      const portInUse = await isPortInUse(port, host);
      const canConnect = await canConnectToPort(port, host, 2000);
      
      if (portInUse || canConnect) {
        service.status = 'degraded';
        service.error = 'API endpoint not responding but port is listening';
        
        // Try to identify the process using the port
        const processInfo = await findProcessUsingPort(port);
        if (processInfo) {
          service.metadata = {
            ...service.metadata,
            processUsingPort: processInfo
          };
        }
      } else {
        service.status = 'failed';
        service.error = 'API server not running';
      }
    }
  }

  /**
   * Check process health
   */
  private async checkProcess(service: ServiceHealth): Promise<void> {
    const processName = service.name.toLowerCase();
    
    try {
      let version: string | null = null;
      
      if (processName.includes('node')) {
        version = await getProcessVersion('node');
        if (version) {
          service.status = 'healthy';
          service.metadata = { version };
        } else {
          service.status = 'failed';
          service.error = 'Node.js not found in system PATH';
        }
      } else if (processName.includes('bun')) {
        version = await getProcessVersion('bun');
        if (version) {
          service.status = 'healthy';
          service.metadata = { version };
        } else {
          service.status = 'failed';
          service.error = 'Bun runtime not found in system PATH';
        }
      } else {
        service.status = 'unknown';
      }
    } catch (error) {
      service.status = 'failed';
      throw new Error(`Process check failed: ${error}`);
    }
  }

  /**
   * Check tool server health
   */
  private async checkToolServer(service: ServiceHealth): Promise<void> {
    // This would check specific tool servers like browser-use, etc.
    // For now, we'll do a basic port check
    const port = service.metadata?.port;
    const host = service.metadata?.host || 'localhost';
    
    if (!port) {
      service.status = 'unknown';
      return;
    }

    // Platform-agnostic port check
    const portInUse = await isPortInUse(port, host);
    const canConnect = await canConnectToPort(port, host, 2000);
    
    if (portInUse || canConnect) {
      service.status = 'healthy';
      
      // Try to identify the process using the port
      const processInfo = await findProcessUsingPort(port);
      if (processInfo) {
        service.metadata = {
          ...service.metadata,
          processInfo
        };
      }
    } else {
      service.status = 'failed';
      service.error = 'Tool server not running';
    }
  }

  /**
   * Track error patterns for analysis
   */
  private trackErrorPattern(serviceName: string, error: string): void {
    const key = `${serviceName}:${error.substring(0, 50)}`;
    
    const existing = this.errorPatterns.get(key);
    if (existing) {
      existing.count++;
      existing.lastOccurred = new Date();
    } else {
      this.errorPatterns.set(key, {
        pattern: error,
        count: 1,
        lastOccurred: new Date(),
        service: serviceName,
        possibleCause: this.analyzePossibleCause(error),
        suggestedFix: this.suggestFix(error),
      });
    }
  }

  /**
   * Analyze possible cause of error
   */
  private analyzePossibleCause(error: string): string {
    if (error.includes('ECONNREFUSED')) {
      return 'Service is not running or port is blocked';
    }
    if (error.includes('timeout')) {
      return 'Service is overloaded or network issues';
    }
    if (error.includes('not found') || error.includes('not installed')) {
      return 'Required dependency is missing';
    }
    if (error.includes('permission')) {
      return 'Insufficient permissions to access resource';
    }
    if (error.includes('EADDRINUSE')) {
      return 'Port is already in use by another process';
    }
    return 'Unknown cause - manual investigation required';
  }

  /**
   * Suggest fix for error
   */
  private suggestFix(error: string): string {
    if (error.includes('ECONNREFUSED')) {
      return 'Start the service or check firewall settings';
    }
    if (error.includes('timeout')) {
      return 'Restart the service or check system resources';
    }
    if (error.includes('not found') || error.includes('not installed')) {
      return 'Install the required dependency using package manager';
    }
    if (error.includes('permission')) {
      return 'Run with appropriate permissions or fix file ownership';
    }
    if (error.includes('EADDRINUSE')) {
      return 'Kill the process using the port or use a different port';
    }
    if (error.includes('MCP') || error.includes('tool server')) {
      return 'Restart the MCP server or verify configuration';
    }
    return 'Check logs for more details and restart the service';
  }

  /**
   * Get current system health status
   */
  public getSystemStatus(): SystemHealthStatus {
    const services = Array.from(this.services.values());
    const healthyCount = services.filter(s => s.status === 'healthy').length;
    const degradedCount = services.filter(s => s.status === 'degraded').length;
    const failedCount = services.filter(s => s.status === 'failed').length;
    
    let overall: 'healthy' | 'degraded' | 'critical';
    if (failedCount > 0) {
      overall = 'critical';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    const recommendations = this.generateRecommendations(services);

    return {
      overall,
      services,
      timestamp: new Date(),
      diagnostics: {
        totalServices: services.length,
        healthyServices: healthyCount,
        degradedServices: degradedCount,
        failedServices: failedCount,
      },
      recommendations,
    };
  }

  /**
   * Generate recommendations based on current health
   */
  private generateRecommendations(services: ServiceHealth[]): string[] {
    const recommendations: string[] = [];
    
    const failedServices = services.filter(s => s.status === 'failed');
    const degradedServices = services.filter(s => s.status === 'degraded');

    for (const service of failedServices) {
      if (service.error) {
        const fix = this.suggestFix(service.error);
        recommendations.push(`${service.name}: ${fix}`);
      }
    }

    for (const service of degradedServices) {
      if (service.error) {
        recommendations.push(`${service.name} is degraded: ${service.error}`);
      }
    }

    // Check for repeated errors
    const frequentErrors = Array.from(this.errorPatterns.values())
      .filter(p => p.count > 3)
      .sort((a, b) => b.count - a.count);

    for (const pattern of frequentErrors.slice(0, 3)) {
      if (pattern.suggestedFix) {
        recommendations.push(`Frequent error in ${pattern.service}: ${pattern.suggestedFix}`);
      }
    }

    return recommendations;
  }

  /**
   * Get error patterns for analysis
   */
  public getErrorPatterns(): ErrorPattern[] {
    return Array.from(this.errorPatterns.values())
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Clear error patterns
   */
  public clearErrorPatterns(): void {
    this.errorPatterns.clear();
  }

  /**
   * Perform diagnostic check on specific service
   */
  public async diagnoseService(serviceName: string): Promise<{
    service: ServiceHealth | undefined;
    diagnosis: string;
    possibleCauses: string[];
    suggestedActions: string[];
  }> {
    const service = this.services.get(serviceName);
    
    if (!service) {
      return {
        service: undefined,
        diagnosis: 'Service not found in monitoring list',
        possibleCauses: ['Service not registered', 'Service name mismatch'],
        suggestedActions: ['Check service name', 'Register service for monitoring'],
      };
    }

    // Perform fresh check
    await this.checkService(service);

    const possibleCauses: string[] = [];
    const suggestedActions: string[] = [];

    if (service.status === 'failed') {
      if (service.error) {
        possibleCauses.push(this.analyzePossibleCause(service.error));
        suggestedActions.push(this.suggestFix(service.error));
      }

      // Check for patterns
      const patterns = Array.from(this.errorPatterns.values())
        .filter(p => p.service === serviceName);
      
      if (patterns.length > 0) {
        possibleCauses.push(`This error has occurred ${patterns[0].count} times`);
        if (patterns[0].count > 5) {
          suggestedActions.push('Consider permanent fix or configuration change');
        }
      }
    }

    const diagnosis = service.status === 'healthy' 
      ? 'Service is operating normally'
      : service.status === 'degraded'
      ? 'Service is operational but experiencing issues'
      : 'Service is not operational';

    return {
      service,
      diagnosis,
      possibleCauses,
      suggestedActions,
    };
  }

  /**
   * Attempt to recover a failed service
   */
  public async attemptRecovery(serviceName: string): Promise<{
    success: boolean;
    message: string;
    actions: string[];
  }> {
    const service = this.services.get(serviceName);
    const actions: string[] = [];
    
    if (!service) {
      return {
        success: false,
        message: 'Service not found',
        actions,
      };
    }

    if (service.status === 'healthy') {
      return {
        success: true,
        message: 'Service is already healthy',
        actions: ['No recovery needed'],
      };
    }

    try {
      switch (service.type) {
        case 'mcp_server':
          actions.push('Attempting to reconnect MCP servers...');
          try {
            // Get all MCP server statuses
            const servers = mcpServerManager.getAllServerStatuses();
            const disconnected = servers.filter(s => s.status !== 'connected');
            
            for (const server of disconnected) {
              try {
                await mcpServerManager.connectServer(server.id);
                actions.push(`✅ Reconnected to ${server.name}`);
              } catch (error) {
                actions.push(`❌ Failed to reconnect ${server.name}: ${error}`);
              }
            }
          } catch (error) {
            actions.push(`MCP reconnection error: ${error}`);
          }
          await this.checkService(service);
          break;
          
        case 'api_endpoint':
          actions.push('Checking if API server needs restart...');
          const apiPort = service.metadata?.port || 3001;
          
          try {
            // First try to kill any process on the port
            await processManager.killProcessByPort(apiPort);
            actions.push(`Cleared port ${apiPort}`);
            
            // Start the API server
            const apiInfo = await processManager.restartAPIServer();
            actions.push(`✅ Started API server (PID: ${apiInfo.pid})`);
            
            // Wait for server to be ready
            await new Promise(resolve => setTimeout(resolve, 3000));
          } catch (error) {
            actions.push(`❌ Failed to restart API: ${error}`);
          }
          await this.checkService(service);
          break;
          
        case 'tool_server':
          actions.push('Attempting to restart tool server...');
          const toolPort = service.metadata?.port;
          const toolName = service.metadata?.serverName || 'unknown';
          
          try {
            if (toolPort) {
              await processManager.killProcessByPort(toolPort);
              actions.push(`Cleared port ${toolPort}`);
            }
            
            const toolInfo = await processManager.restartToolServer(toolName, toolPort);
            actions.push(`✅ Started ${toolName} server (PID: ${toolInfo.pid})`);
            
            // Wait for server to be ready
            await new Promise(resolve => setTimeout(resolve, 3000));
          } catch (error) {
            actions.push(`❌ Failed to restart tool server: ${error}`);
          }
          await this.checkService(service);
          break;
          
        default:
          actions.push('No automatic recovery available for this service type');
      }

      // Recheck service
      await this.checkService(service);
      
      // Get the updated service from the map
      const updatedService = this.services.get(serviceName);
      const isHealthy = updatedService?.status === 'healthy';
      
      if (isHealthy) {
        return {
          success: true,
          message: `Successfully recovered ${serviceName}`,
          actions,
        };
      } else {
        return {
          success: false,
          message: `Recovery attempted but ${serviceName} is still ${updatedService?.status || 'unknown'}`,
          actions,
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Recovery failed: ${error}`,
        actions,
      };
    }
  }
}

// Export singleton instance
export const systemHealthMonitor = new SystemHealthMonitor();