/**
 * Production-ready Connector Integration Service
 * Brings together all connector components into a unified, enterprise-grade system
 */

import { ConnectorId, ApprovalMode } from '../types/connectors';
import { connectorService } from './connectorService';
import { tokenVault } from './tokenVault';
import { oauthManager } from './oauthManager';
import { approvalManager } from './approvalManager';
import { rateLimiter } from './rateLimiter';
import { connectorMonitor } from './connectorMonitor';
import { healthChecker } from './healthChecker';
import { ResponsesAPIClient } from './responsesAPIClient';
import { EventEmitter } from 'events';

export interface ConnectorIntegrationConfig {
  enableMonitoring?: boolean;
  enableHealthChecks?: boolean;
  enableRateLimiting?: boolean;
  enableApprovals?: boolean;
  defaultApprovalMode?: ApprovalMode;
  healthCheckInterval?: number;
  maxConcurrentRequests?: number;
}

export interface IntegrationRequest {
  userId: string;
  connectorId: ConnectorId;
  operation: string;
  input: string;
  metadata?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  requireApproval?: ApprovalMode;
}

export interface IntegrationResponse {
  success: boolean;
  data?: any;
  error?: string;
  approvalRequired?: boolean;
  approvalId?: string;
  metrics?: {
    latency: number;
    tokensUsed?: number;
    cost?: number;
  };
}

class ConnectorIntegrationService extends EventEmitter {
  private config: ConnectorIntegrationConfig;
  private responsesClient: ResponsesAPIClient;
  private isInitialized: boolean = false;
  private activeRequests: Map<string, IntegrationRequest> = new Map();

  constructor(config: ConnectorIntegrationConfig = {}) {
    super();
    this.config = {
      enableMonitoring: true,
      enableHealthChecks: true,
      enableRateLimiting: true,
      enableApprovals: true,
      defaultApprovalMode: 'always',
      healthCheckInterval: 60000, // 1 minute
      maxConcurrentRequests: 100,
      ...config
    };

    this.responsesClient = new ResponsesAPIClient({
      enableLogging: process.env.NODE_ENV === 'development'
    });
  }

  /**
   * Initialize all connector subsystems
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize token vault
      await tokenVault.initialize();
      console.log('✅ Token vault initialized');

      // Initialize OAuth manager with providers
      await oauthManager.initialize();
      console.log('✅ OAuth manager initialized');

      // Start monitoring if enabled
      if (this.config.enableMonitoring) {
        await connectorMonitor.startMonitoring();
        console.log('✅ Monitoring system started');
      }

      // Start health checks if enabled
      if (this.config.enableHealthChecks) {
        await healthChecker.startHealthChecks();
        console.log('✅ Health checks started');
      }

      // Initialize rate limiter if enabled
      if (this.config.enableRateLimiting) {
        await rateLimiter.initialize();
        console.log('✅ Rate limiter initialized');
      }

      // Initialize approval manager if enabled
      if (this.config.enableApprovals) {
        await approvalManager.initialize();
        console.log('✅ Approval manager initialized');
      }

      this.isInitialized = true;
      this.emit('initialized');
      console.log('🚀 Connector integration service fully initialized');
    } catch (error) {
      console.error('Failed to initialize connector integration:', error);
      throw error;
    }
  }

  /**
   * Execute a connector request with full production features
   */
  async executeRequest(request: IntegrationRequest): Promise<IntegrationResponse> {
    const startTime = Date.now();
    const requestId = `${request.userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Track active request
      this.activeRequests.set(requestId, request);
      
      // Log request
      connectorMonitor.logConnectorRequest(
        request.connectorId,
        request.userId,
        request.operation,
        request.metadata
      );

      // Check rate limits
      if (this.config.enableRateLimiting) {
        const canProceed = await rateLimiter.checkLimit(request.userId, request.connectorId);
        if (!canProceed) {
          const retryAfter = await rateLimiter.getRetryAfter(request.userId, request.connectorId);
          throw new Error(`Rate limit exceeded. Retry after ${retryAfter}ms`);
        }
      }

      // Check if approval is needed
      if (this.config.enableApprovals && request.requireApproval !== 'never') {
        const needsApproval = await approvalManager.requiresApproval(
          request.connectorId,
          request.operation,
          request.userId
        );

        if (needsApproval) {
          const approvalRequest = await approvalManager.createApprovalRequest({
            connectorId: request.connectorId,
            userId: request.userId,
            operation: request.operation,
            data: { input: request.input },
            requiredRole: 'user',
            priority: request.priority || 'normal',
            expiresIn: 300000 // 5 minutes
          });

          return {
            success: false,
            approvalRequired: true,
            approvalId: approvalRequest.id,
            metrics: {
              latency: Date.now() - startTime
            }
          };
        }
      }

      // Get OAuth token from secure vault
      const session = await tokenVault.getSession(request.userId, request.connectorId);
      if (!session) {
        throw new Error(`No active session for ${request.connectorId}. Please authenticate first.`);
      }

      // Check token expiration and refresh if needed
      if (await tokenVault.isTokenExpired(session.id)) {
        await tokenVault.refreshToken(session.id);
        // Get refreshed session
        const refreshedSession = await tokenVault.getSession(request.userId, request.connectorId);
        if (!refreshedSession) {
          throw new Error('Failed to refresh token');
        }
      }

      // Execute the connector request
      const response = await connectorService.executeConnectorRequest({
        connector_id: request.connectorId,
        oauth_token: session.token.access_token,
        input: request.input,
        require_approval: request.requireApproval || this.config.defaultApprovalMode,
        allowed_tools: undefined, // Let the connector decide
        model: process.env.DEFAULT_MODEL || 'gpt-5'
      });

      // Track metrics
      const latency = Date.now() - startTime;
      connectorMonitor.trackConnectorLatency(request.connectorId, latency);
      connectorMonitor.trackConnectorUsage(request.connectorId, request.userId, {
        operation: request.operation,
        tokensUsed: response.tokensUsed,
        cost: response.cost
      });

      // Update rate limiter usage
      if (this.config.enableRateLimiting) {
        await rateLimiter.recordUsage(request.userId, request.connectorId, {
          tokens: response.tokensUsed || 0,
          cost: response.cost || 0
        });
      }

      // Log success
      connectorMonitor.logConnectorResponse(
        request.connectorId,
        request.userId,
        response.output_text,
        { success: true, latency }
      );

      return {
        success: true,
        data: response,
        metrics: {
          latency,
          tokensUsed: response.tokensUsed,
          cost: response.cost
        }
      };
    } catch (error: any) {
      // Track error
      connectorMonitor.trackConnectorError(request.connectorId, error);
      connectorMonitor.logConnectorResponse(
        request.connectorId,
        request.userId,
        error.message,
        { success: false, error: error.message }
      );

      return {
        success: false,
        error: error.message,
        metrics: {
          latency: Date.now() - startTime
        }
      };
    } finally {
      // Clean up active request
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * Handle OAuth flow for a connector
   */
  async authenticateConnector(
    userId: string,
    connectorId: ConnectorId,
    redirectUri?: string
  ): Promise<string> {
    try {
      // Map connector ID to provider
      const providerMap: Record<ConnectorId, string> = {
        'connector_gmail': 'google',
        'connector_googlecalendar': 'google',
        'connector_googledrive': 'google',
        'connector_dropbox': 'dropbox',
        'connector_microsoftteams': 'microsoft',
        'connector_outlookcalendar': 'microsoft',
        'connector_outlookemail': 'microsoft',
        'connector_sharepoint': 'microsoft'
      };

      const provider = providerMap[connectorId];
      if (!provider) {
        throw new Error(`Unsupported connector: ${connectorId}`);
      }

      // Generate authorization URL with PKCE
      const authUrl = await oauthManager.getAuthorizationUrl(provider, {
        state: JSON.stringify({ userId, connectorId }),
        redirectUri
      });

      return authUrl;
    } catch (error: any) {
      console.error('Failed to generate auth URL:', error);
      throw error;
    }
  }

  /**
   * Complete OAuth flow and store tokens securely
   */
  async completeAuthentication(
    code: string,
    state: string,
    provider: string
  ): Promise<{ userId: string; connectorId: ConnectorId }> {
    try {
      // Validate state
      const stateData = JSON.parse(state);
      const { userId, connectorId } = stateData;

      // Exchange code for tokens
      const tokens = await oauthManager.exchangeCodeForTokens(provider, code);

      // Store tokens securely in vault
      await tokenVault.storeToken(userId, connectorId, tokens);

      // Log authentication
      connectorMonitor.logConnectorRequest(
        connectorId,
        userId,
        'authentication',
        { provider, success: true }
      );

      return { userId, connectorId };
    } catch (error: any) {
      console.error('Failed to complete authentication:', error);
      throw error;
    }
  }

  /**
   * Get connector status and health
   */
  async getConnectorStatus(connectorId: ConnectorId): Promise<any> {
    const health = await healthChecker.checkConnectorHealth(connectorId);
    const metrics = connectorMonitor.getConnectorMetrics(connectorId);
    const rateLimitStatus = this.config.enableRateLimiting 
      ? await rateLimiter.getStatus('system', connectorId)
      : null;

    return {
      connectorId,
      health,
      metrics,
      rateLimits: rateLimitStatus,
      isHealthy: health.status === 'healthy',
      lastChecked: health.lastChecked
    };
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId: string): Promise<any[]> {
    const sessions = await tokenVault.getUserSessions(userId);
    return sessions.map((session: any) => ({
      connectorId: session.connectorId,
      createdAt: session.createdAt,
      lastUsed: session.lastUsed,
      expiresAt: session.expiresAt,
      isActive: !tokenVault.isTokenExpired(session.id)
    }));
  }

  /**
   * Revoke a connector session
   */
  async revokeSession(userId: string, connectorId: ConnectorId): Promise<void> {
    await tokenVault.revokeToken(userId, connectorId);
    connectorMonitor.logConnectorRequest(
      connectorId,
      userId,
      'revoke',
      { success: true }
    );
  }

  /**
   * Get system metrics and dashboard data
   */
  async getSystemMetrics(): Promise<any> {
    const metrics = {
      connectors: {} as Record<string, any>,
      system: {
        activeRequests: this.activeRequests.size,
        totalSessions: await tokenVault.getTotalSessions(),
        healthStatus: await healthChecker.getOverallHealth()
      },
      monitoring: this.config.enableMonitoring 
        ? connectorMonitor.getSystemMetrics()
        : null,
      rateLimits: this.config.enableRateLimiting
        ? await rateLimiter.getSystemStatus()
        : null
    };

    // Get individual connector metrics
    const connectorIds: ConnectorId[] = [
      'connector_gmail',
      'connector_googlecalendar',
      'connector_googledrive'
    ];

    for (const id of connectorIds) {
      metrics.connectors[id] = await this.getConnectorStatus(id);
    }

    return metrics;
  }

  /**
   * Shutdown the integration service gracefully
   */
  async shutdown(): Promise<void> {
    console.log('Shutting down connector integration service...');

    // Stop health checks
    if (this.config.enableHealthChecks) {
      await healthChecker.stopHealthChecks();
    }

    // Stop monitoring
    if (this.config.enableMonitoring) {
      await connectorMonitor.stopMonitoring();
    }

    // Clear sessions
    connectorService.clearSessions();

    // Close database connections
    await tokenVault.close();

    this.isInitialized = false;
    this.emit('shutdown');
    console.log('Connector integration service shut down successfully');
  }
}

// Export singleton instance
export const connectorIntegration = new ConnectorIntegrationService();

// Auto-initialize if in production
if (process.env.NODE_ENV === 'production') {
  connectorIntegration.initialize().catch(console.error);

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    await connectorIntegration.shutdown();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    await connectorIntegration.shutdown();
    process.exit(0);
  });
}