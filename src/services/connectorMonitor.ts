import { EventEmitter } from 'events';
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { register, Counter, Histogram, Gauge, Summary, collectDefaultMetrics } from 'prom-client';
import express from 'express';
import { z } from 'zod';
import Redis from 'ioredis';

// Monitoring schemas
const MetricEventSchema = z.object({
  timestamp: z.date(),
  connectorId: z.string(),
  userId: z.string().optional(),
  operation: z.string(),
  duration: z.number(), // milliseconds
  success: z.boolean(),
  error: z.string().optional(),
  metadata: z.record(z.string(), z.any()).optional()
});

const AlertConfigSchema = z.object({
  name: z.string(),
  metric: z.string(),
  threshold: z.number(),
  condition: z.enum(['gt', 'lt', 'gte', 'lte', 'eq']),
  windowMs: z.number(),
  cooldownMs: z.number().optional(),
  actions: z.array(z.enum(['log', 'webhook', 'email', 'slack']))
});

const DashboardDataSchema = z.object({
  period: z.enum(['1h', '24h', '7d', '30d']),
  connectorId: z.string().optional(),
  metrics: z.object({
    totalRequests: z.number(),
    successRate: z.number(),
    errorRate: z.number(),
    avgLatency: z.number(),
    p95Latency: z.number(),
    p99Latency: z.number(),
    throughput: z.number(),
    activeConnections: z.number(),
    errors: z.array(z.object({
      type: z.string(),
      count: z.number(),
      lastOccurrence: z.date()
    }))
  })
});

type MetricEvent = z.infer<typeof MetricEventSchema>;
type AlertConfig = z.infer<typeof AlertConfigSchema>;
type DashboardData = z.infer<typeof DashboardDataSchema>;

export interface MonitorConfig {
  redis?: Redis;
  logLevel?: string;
  logDir?: string;
  metricsPort?: number;
  enablePrometheus?: boolean;
  enableDashboard?: boolean;
  dashboardPort?: number;
  alertWebhook?: string;
  slackWebhook?: string;
  emailConfig?: {
    service: string;
    auth: {
      user: string;
      pass: string;
    };
    recipients: string[];
  };
}

export class ConnectorMonitor extends EventEmitter {
  private logger: winston.Logger;
  private redis?: Redis;
  private app?: express.Application;
  private metricsApp?: express.Application;

  // Prometheus metrics
  private requestCounter!: Counter<string>;
  private errorCounter!: Counter<string>;
  private latencyHistogram!: Histogram<string>;
  private activeConnectionsGauge!: Gauge<string>;
  private throughputGauge!: Gauge<string>;
  private quotaUsageGauge!: Gauge<string>;
  private healthStatusGauge!: Gauge<string>;

  // Internal state
  private metricEvents: MetricEvent[] = [];
  private alerts: Map<string, AlertConfig> = new Map();
  private alertCooldowns: Map<string, number> = new Map();
  private dashboardData: Map<string, DashboardData> = new Map();

  constructor(private config: MonitorConfig = {}) {
    super();

    // Initialize Winston logger
    this.logger = this.createLogger();

    // Initialize Redis if provided
    this.redis = config.redis;

    // Initialize Prometheus metrics
    if (config.enablePrometheus !== false) {
      this.initializeMetrics();
      this.setupMetricsEndpoint();
    }

    // Initialize dashboard
    if (config.enableDashboard) {
      this.setupDashboard();
    }

    // Start periodic tasks
    this.startPeriodicTasks();
  }

  private createLogger(): winston.Logger {
    const logDir = this.config.logDir || './logs';

    const transports: winston.transport[] = [
      // Console transport
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.colorize(),
          winston.format.simple()
        )
      })
    ];

    // File transport with daily rotation
    if (this.config.logDir) {
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/connector-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );

      // Error log file
      transports.push(
        new DailyRotateFile({
          filename: `${logDir}/connector-error-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '30d',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );
    }

    return winston.createLogger({
      level: this.config.logLevel || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.splat(),
        winston.format.json()
      ),
      transports,
      exceptionHandlers: [
        new winston.transports.File({
          filename: `${logDir}/exceptions.log`
        })
      ],
      rejectionHandlers: [
        new winston.transports.File({
          filename: `${logDir}/rejections.log`
        })
      ]
    });
  }

  private initializeMetrics(): void {
    // Collect default Node.js metrics
    collectDefaultMetrics({ register });

    // Custom metrics
    this.requestCounter = new Counter({
      name: 'connector_requests_total',
      help: 'Total number of connector requests',
      labelNames: ['connector', 'operation', 'status']
    });

    this.errorCounter = new Counter({
      name: 'connector_errors_total',
      help: 'Total number of connector errors',
      labelNames: ['connector', 'operation', 'error_type']
    });

    this.latencyHistogram = new Histogram({
      name: 'connector_latency_seconds',
      help: 'Request latency in seconds',
      labelNames: ['connector', 'operation'],
      buckets: [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
    });

    this.activeConnectionsGauge = new Gauge({
      name: 'connector_active_connections',
      help: 'Number of active connections',
      labelNames: ['connector']
    });

    this.throughputGauge = new Gauge({
      name: 'connector_throughput',
      help: 'Requests per second',
      labelNames: ['connector']
    });

    this.quotaUsageGauge = new Gauge({
      name: 'connector_quota_usage_percent',
      help: 'Quota usage percentage',
      labelNames: ['connector', 'quota_type']
    });

    this.healthStatusGauge = new Gauge({
      name: 'connector_health_status',
      help: 'Health status (0=unhealthy, 1=healthy)',
      labelNames: ['connector']
    });
  }

  private setupMetricsEndpoint(): void {
    this.metricsApp = express();
    const port = this.config.metricsPort || 9090;

    this.metricsApp.get('/metrics', async (req, res) => {
      try {
        res.set('Content-Type', register.contentType);
        const metrics = await register.metrics();
        res.end(metrics);
      } catch (error) {
        res.status(500).end();
        this.logger.error('Error generating metrics', error);
      }
    });

    this.metricsApp.listen(port, () => {
      this.logger.info(`Prometheus metrics available at http://localhost:${port}/metrics`);
    });
  }

  private setupDashboard(): void {
    this.app = express();
    this.app.use(express.json());
    const port = this.config.dashboardPort || 3004;

    // Dashboard API endpoints
    this.app.get('/api/dashboard/overview', async (req, res) => {
      const period = (req.query.period as string) || '24h';
      const data = await this.aggregateDashboardData(period as any);
      res.json(data);
    });

    this.app.get('/api/dashboard/connector/:id', async (req, res) => {
      const { id } = req.params;
      const period = (req.query.period as string) || '24h';
      const data = await this.aggregateDashboardData(period as any, id);
      res.json(data);
    });

    this.app.get('/api/dashboard/alerts', (req, res) => {
      const alerts = Array.from(this.alerts.values());
      res.json(alerts);
    });

    this.app.get('/api/dashboard/logs', async (req, res) => {
      const limit = parseInt(req.query.limit as string) || 100;
      const level = req.query.level as string;
      const connector = req.query.connector as string;

      const logs = await this.getRecentLogs(limit, level, connector);
      res.json(logs);
    });

    this.app.get('/api/dashboard/metrics/realtime', (req, res) => {
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      const interval = setInterval(() => {
        const data = this.getRealtimeMetrics();
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      }, 1000);

      req.on('close', () => {
        clearInterval(interval);
      });
    });

    this.app.listen(port, () => {
      this.logger.info(`Dashboard API available at http://localhost:${port}`);
    });
  }

  private startPeriodicTasks(): void {
    // Aggregate metrics every minute
    setInterval(() => {
      this.aggregateMetrics();
    }, 60 * 1000);

    // Check alerts every 10 seconds
    setInterval(() => {
      this.checkAlerts();
    }, 10 * 1000);

    // Clean old events every hour
    setInterval(() => {
      this.cleanOldEvents();
    }, 60 * 60 * 1000);

    // Update throughput gauge every second
    setInterval(() => {
      this.updateThroughputMetrics();
    }, 1000);
  }

  // Public monitoring methods
  public recordEvent(event: {
    connectorId: string;
    userId?: string;
    operation: string;
    duration: number;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
  }): void {
    const metricEvent: MetricEvent = {
      ...event,
      timestamp: new Date()
    };

    this.metricEvents.push(metricEvent);

    // Update Prometheus metrics
    const status = event.success ? 'success' : 'failure';
    this.requestCounter.inc({
      connector: event.connectorId,
      operation: event.operation,
      status
    });

    if (!event.success && event.error) {
      this.errorCounter.inc({
        connector: event.connectorId,
        operation: event.operation,
        error_type: this.categorizeError(event.error)
      });
    }

    this.latencyHistogram.observe(
      {
        connector: event.connectorId,
        operation: event.operation
      },
      event.duration / 1000 // Convert to seconds
    );

    // Log the event
    const logLevel = event.success ? 'info' : 'error';
    this.logger[logLevel]('Connector operation', {
      connectorId: event.connectorId,
      userId: event.userId,
      operation: event.operation,
      duration: event.duration,
      success: event.success,
      error: event.error,
      metadata: event.metadata
    });

    // Persist to Redis if available
    if (this.redis) {
      const key = `events:${event.connectorId}:${Date.now()}`;
      this.redis.set(key, JSON.stringify(metricEvent), 'EX', 86400); // 24 hour TTL
    }

    // Emit event for real-time monitoring
    this.emit('metric-event', metricEvent);
  }

  public updateConnectionCount(connectorId: string, count: number): void {
    this.activeConnectionsGauge.set({ connector: connectorId }, count);

    this.logger.debug('Connection count updated', {
      connectorId,
      count
    });
  }

  public updateQuotaUsage(
    connectorId: string,
    quotaType: string,
    percentage: number
  ): void {
    this.quotaUsageGauge.set(
      { connector: connectorId, quota_type: quotaType },
      percentage
    );

    if (percentage >= 90) {
      this.logger.warn('Quota usage critical', {
        connectorId,
        quotaType,
        percentage
      });
    }
  }

  public updateHealthStatus(connectorId: string, healthy: boolean): void {
    this.healthStatusGauge.set(
      { connector: connectorId },
      healthy ? 1 : 0
    );

    if (!healthy) {
      this.logger.error('Connector unhealthy', { connectorId });
    }
  }

  public log(
    level: string,
    message: string,
    meta?: Record<string, any>
  ): void {
    this.logger.log(level, message, meta);
  }

  public addAlert(config: AlertConfig): void {
    this.alerts.set(config.name, config);
    this.logger.info('Alert configured', { alertName: config.name });
  }

  // Private helper methods
  private categorizeError(error: string): string {
    if (error.includes('timeout')) return 'timeout';
    if (error.includes('auth')) return 'authentication';
    if (error.includes('permission')) return 'authorization';
    if (error.includes('rate')) return 'rate_limit';
    if (error.includes('network')) return 'network';
    if (error.includes('validation')) return 'validation';
    return 'unknown';
  }

  private aggregateMetrics(): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60 * 1000;

    // Filter recent events
    const recentEvents = this.metricEvents.filter(
      e => e.timestamp.getTime() >= oneMinuteAgo
    );

    // Group by connector
    const byConnector = new Map<string, MetricEvent[]>();
    for (const event of recentEvents) {
      const events = byConnector.get(event.connectorId) || [];
      events.push(event);
      byConnector.set(event.connectorId, events);
    }

    // Calculate throughput
    byConnector.forEach((events, connectorId) => {
      const throughput = events.length / 60; // requests per second
      this.throughputGauge.set({ connector: connectorId }, throughput);
    });
  }

  private async checkAlerts(): Promise<void> {
    for (const [name, config] of this.alerts) {
      // Check if in cooldown
      const lastAlert = this.alertCooldowns.get(name);
      if (lastAlert && Date.now() - lastAlert < (config.cooldownMs || 300000)) {
        continue;
      }

      // Get metric value
      const value = await this.getMetricValue(config.metric);

      // Check condition
      let triggered = false;
      switch (config.condition) {
        case 'gt':
          triggered = value > config.threshold;
          break;
        case 'lt':
          triggered = value < config.threshold;
          break;
        case 'gte':
          triggered = value >= config.threshold;
          break;
        case 'lte':
          triggered = value <= config.threshold;
          break;
        case 'eq':
          triggered = value === config.threshold;
          break;
      }

      if (triggered) {
        await this.triggerAlert(name, config, value);
        this.alertCooldowns.set(name, Date.now());
      }
    }
  }

  private async getMetricValue(metric: string): Promise<number> {
    // This would fetch the actual metric value
    // For now, return a placeholder
    return Math.random() * 100;
  }

  private async triggerAlert(
    name: string,
    config: AlertConfig,
    value: number
  ): Promise<void> {
    const alert = {
      name,
      metric: config.metric,
      value,
      threshold: config.threshold,
      condition: config.condition,
      timestamp: new Date()
    };

    this.logger.warn('Alert triggered', alert);
    this.emit('alert-triggered', alert);

    for (const action of config.actions) {
      switch (action) {
        case 'log':
          // Already logged above
          break;
        case 'webhook':
          if (this.config.alertWebhook) {
            await this.sendWebhook(this.config.alertWebhook, alert);
          }
          break;
        case 'slack':
          if (this.config.slackWebhook) {
            await this.sendSlackNotification(alert);
          }
          break;
        case 'email':
          if (this.config.emailConfig) {
            await this.sendEmailNotification(alert);
          }
          break;
      }
    }
  }

  private async sendWebhook(url: string, data: any): Promise<void> {
    try {
      await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
    } catch (error) {
      this.logger.error('Failed to send webhook', { url, error });
    }
  }

  private async sendSlackNotification(alert: any): Promise<void> {
    if (!this.config.slackWebhook) return;

    const message = {
      text: `Alert: ${alert.name}`,
      attachments: [{
        color: 'danger',
        fields: [
          { title: 'Metric', value: alert.metric, short: true },
          { title: 'Value', value: alert.value.toString(), short: true },
          { title: 'Threshold', value: alert.threshold.toString(), short: true },
          { title: 'Condition', value: alert.condition, short: true },
          { title: 'Time', value: alert.timestamp.toISOString(), short: false }
        ]
      }]
    };

    await this.sendWebhook(this.config.slackWebhook, message);
  }

  private async sendEmailNotification(alert: any): Promise<void> {
    // Email implementation would go here
    // Using nodemailer or similar
    this.logger.info('Email notification would be sent', alert);
  }

  private cleanOldEvents(): void {
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    this.metricEvents = this.metricEvents.filter(
      e => e.timestamp.getTime() >= oneDayAgo
    );
  }

  private updateThroughputMetrics(): void {
    // Calculate real-time throughput
    const now = Date.now();
    const oneSecondAgo = now - 1000;

    const recentEvents = this.metricEvents.filter(
      e => e.timestamp.getTime() >= oneSecondAgo
    );

    const byConnector = new Map<string, number>();
    for (const event of recentEvents) {
      byConnector.set(
        event.connectorId,
        (byConnector.get(event.connectorId) || 0) + 1
      );
    }

    byConnector.forEach((count, connectorId) => {
      this.throughputGauge.set({ connector: connectorId }, count);
    });
  }

  private async aggregateDashboardData(
    period: '1h' | '24h' | '7d' | '30d',
    connectorId?: string
  ): Promise<DashboardData> {
    const periodMs = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };

    const startTime = Date.now() - periodMs[period];

    let events = this.metricEvents.filter(
      e => e.timestamp.getTime() >= startTime
    );

    if (connectorId) {
      events = events.filter(e => e.connectorId === connectorId);
    }

    const totalRequests = events.length;
    const successfulRequests = events.filter(e => e.success).length;
    const failedRequests = totalRequests - successfulRequests;

    const latencies = events.map(e => e.duration).sort((a, b) => a - b);
    const avgLatency = latencies.length > 0
      ? latencies.reduce((a, b) => a + b, 0) / latencies.length
      : 0;
    const p95Index = Math.floor(latencies.length * 0.95);
    const p99Index = Math.floor(latencies.length * 0.99);

    const errors = new Map<string, { count: number; lastOccurrence: Date }>();
    events.filter(e => !e.success && e.error).forEach(e => {
      const errorType = this.categorizeError(e.error!);
      const existing = errors.get(errorType);
      if (existing) {
        existing.count++;
        if (e.timestamp > existing.lastOccurrence) {
          existing.lastOccurrence = e.timestamp;
        }
      } else {
        errors.set(errorType, {
          count: 1,
          lastOccurrence: e.timestamp
        });
      }
    });

    return {
      period,
      connectorId,
      metrics: {
        totalRequests,
        successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
        errorRate: totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0,
        avgLatency,
        p95Latency: latencies[p95Index] || 0,
        p99Latency: latencies[p99Index] || 0,
        throughput: totalRequests / (periodMs[period] / 1000),
        activeConnections: 0, // Would be fetched from gauge
        errors: Array.from(errors.entries()).map(([type, data]) => ({
          type,
          count: data.count,
          lastOccurrence: data.lastOccurrence
        }))
      }
    };
  }

  private async getRecentLogs(
    limit: number,
    level?: string,
    connector?: string
  ): Promise<any[]> {
    // This would query the actual log files or log storage
    // For now, return recent events as logs
    let logs = this.metricEvents.slice(-limit);

    if (connector) {
      logs = logs.filter(e => e.connectorId === connector);
    }

    return logs.map(e => ({
      timestamp: e.timestamp,
      level: e.success ? 'info' : 'error',
      connector: e.connectorId,
      message: `${e.operation} ${e.success ? 'succeeded' : 'failed'}`,
      metadata: e.metadata
    }));
  }

  private getRealtimeMetrics(): any {
    const now = Date.now();
    const oneSecondAgo = now - 1000;

    const recentEvents = this.metricEvents.filter(
      e => e.timestamp.getTime() >= oneSecondAgo
    );

    return {
      timestamp: new Date(),
      requestsPerSecond: recentEvents.length,
      avgLatency: recentEvents.length > 0
        ? recentEvents.reduce((sum, e) => sum + e.duration, 0) / recentEvents.length
        : 0,
      errorRate: recentEvents.length > 0
        ? (recentEvents.filter(e => !e.success).length / recentEvents.length) * 100
        : 0
    };
  }

  public async shutdown(): Promise<void> {
    this.logger.info('Shutting down connector monitor');

    // Close logger transports
    this.logger.close();

    // Clear all intervals
    // Note: In production, you'd store interval IDs and clear them here
  }
}

// Export singleton instance
export const connectorMonitor = new ConnectorMonitor();
