export interface OpenOrUpdatePRParams {
  branch: string;
  title: string;
  body?: string;
  base?: string;
}

export interface PROperationResult {
  supported: boolean;
  ok: boolean;
  url?: string | null;
  stdout?: string;
  stderr?: string;
  diagnostics?: Record<string, any>;
}

/**
 * No-op PR service by default.
 * Returns supported=false. When wired to a provider (e.g., GitHub/GitLab), replace implementation.
 */
export async function openOrUpdatePR(_params: OpenOrUpdatePRParams): Promise<PROperationResult> {
  return { supported: false, ok: true, url: null };
}
