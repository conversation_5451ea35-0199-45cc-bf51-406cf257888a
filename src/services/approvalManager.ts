import { EventEmitter } from 'events';
import crypto from 'crypto';
import { z } from 'zod';
import Redis from 'ioredis';
import express from 'express';
import { WebSocketServer } from 'ws';

// Approval schemas
const ApprovalRequestSchema = z.object({
  id: z.string(),
  connectorId: z.string(),
  userId: z.string(),
  operationType: z.enum(['read', 'write', 'delete', 'execute']),
  resource: z.string(),
  description: z.string(),
  metadata: z.record(z.string(), z.any()).optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  requestedAt: z.date(),
  expiresAt: z.date(),
  requiredRole: z.enum(['user', 'admin', 'super_admin']).optional(),
  requiredApprovers: z.number().min(1).default(1),
  approvers: z.array(z.object({
    userId: z.string(),
    role: z.string(),
    decision: z.enum(['approved', 'rejected']),
    reason: z.string().optional(),
    timestamp: z.date()
  })).default([]),
  status: z.enum(['pending', 'approved', 'rejected', 'expired', 'cancelled']).default('pending'),
  webhookUrl: z.string().url().optional()
});

const ApprovalPolicySchema = z.object({
  connectorId: z.string(),
  operationType: z.enum(['read', 'write', 'delete', 'execute', 'all']),
  requiresApproval: z.boolean(),
  requiredRole: z.enum(['user', 'admin', 'super_admin']),
  requiredApprovers: z.number().min(1),
  autoApproveAfter: z.number().optional(), // milliseconds
  autoRejectAfter: z.number().optional(), // milliseconds
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.enum(['equals', 'contains', 'regex', 'gt', 'lt']),
    value: z.any()
  })).optional()
});

type ApprovalRequest = z.infer<typeof ApprovalRequestSchema> & {
  callback?: (approved: boolean, reason?: string) => void;
};
type ApprovalPolicy = z.infer<typeof ApprovalPolicySchema>;

export interface ApprovalManagerConfig {
  redis?: Redis;
  webhookSecret?: string;
  defaultTimeout?: number; // milliseconds
  enableWebSocket?: boolean;
  port?: number;
}

export class ApprovalManager extends EventEmitter {
  private redis?: Redis;
  private requests: Map<string, ApprovalRequest> = new Map();
  private policies: Map<string, ApprovalPolicy[]> = new Map();
  private auditLog: Array<{
    timestamp: Date;
    requestId: string;
    action: string;
    userId: string;
    details: any;
  }> = [];
  private webhookSecret: string;
  private defaultTimeout: number;
  private wsServer?: WebSocketServer;
  private app?: express.Application;
  private timers: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: ApprovalManagerConfig = {}) {
    super();
    this.redis = config.redis;
    this.webhookSecret = config.webhookSecret || crypto.randomBytes(32).toString('hex');
    this.defaultTimeout = config.defaultTimeout || 3600000; // 1 hour default

    if (config.enableWebSocket) {
      this.setupWebSocketServer(config.port || 8080);
    }

    this.setupApprovalEndpoints();
    this.loadPersistentData();
  }

  private async loadPersistentData(): Promise<void> {
    if (this.redis) {
      try {
        // Load pending requests from Redis
        const requestKeys = await this.redis.keys('approval:request:*');
        for (const key of requestKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const request = JSON.parse(data);
            request.requestedAt = new Date(request.requestedAt);
            request.expiresAt = new Date(request.expiresAt);
            this.requests.set(request.id, request);
            this.scheduleExpiration(request);
          }
        }

        // Load policies from Redis
        const policyKeys = await this.redis.keys('approval:policy:*');
        for (const key of policyKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const policies = JSON.parse(data);
            const connectorId = key.replace('approval:policy:', '');
            this.policies.set(connectorId, policies);
          }
        }
      } catch (error) {
        console.error('Error loading persistent data:', error);
      }
    }
  }

  private setupWebSocketServer(port: number): void {
    this.wsServer = new WebSocketServer({ port });

    this.wsServer.on('connection', (ws) => {
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message.toString());

          if (data.type === 'subscribe') {
            // Subscribe to approval updates
            ws.send(JSON.stringify({
              type: 'subscribed',
              message: 'Successfully subscribed to approval updates'
            }));
          }
        } catch (error) {
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });
    });

    // Broadcast updates to all connected clients
    this.on('approval-update', (request: ApprovalRequest) => {
      const message = JSON.stringify({
        type: 'approval-update',
        request
      });

      this.wsServer?.clients.forEach((client) => {
        if (client.readyState === 1) { // WebSocket.OPEN
          client.send(message);
        }
      });
    });
  }

  private setupApprovalEndpoints(): void {
    this.app = express();
    this.app.use(express.json());

    // Get pending approvals
    this.app.get('/api/approvals/pending', (req, res) => {
      const userId = req.headers['x-user-id'] as string;
      const role = req.headers['x-user-role'] as string;

      const pending = Array.from(this.requests.values())
        .filter(r => r.status === 'pending')
        .filter(r => this.canUserApprove(userId, role, r))
        .sort((a, b) => {
          const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        });

      res.json(pending);
    });

    // Approve request
    this.app.post('/api/approvals/:id/approve', async (req, res) => {
      const { id } = req.params;
      const userId = req.headers['x-user-id'] as string;
      const role = req.headers['x-user-role'] as string;
      const { reason } = req.body;

      try {
        await this.approve(id, userId, role, reason);
        res.json({ success: true });
      } catch (error: any) {
        res.status(400).json({ error: error.message });
      }
    });

    // Reject request
    this.app.post('/api/approvals/:id/reject', async (req, res) => {
      const { id } = req.params;
      const userId = req.headers['x-user-id'] as string;
      const role = req.headers['x-user-role'] as string;
      const { reason } = req.body;

      try {
        await this.reject(id, userId, role, reason);
        res.json({ success: true });
      } catch (error: any) {
        res.status(400).json({ error: error.message });
      }
    });

    // Get approval history
    this.app.get('/api/approvals/history', (req, res) => {
      const limit = parseInt(req.query.limit as string) || 100;
      const history = this.auditLog.slice(-limit).reverse();
      res.json(history);
    });
  }

  public async createApprovalRequest(params: {
    connectorId: string;
    userId: string;
    operationType: 'read' | 'write' | 'delete' | 'execute';
    resource: string;
    description: string;
    metadata?: Record<string, any>;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    webhookUrl?: string;
    callback?: (approved: boolean, reason?: string) => void;
  }): Promise<string> {
    // Check if approval is required based on policies
    const policy = this.findApplicablePolicy(params.connectorId, params.operationType, params.metadata);

    if (!policy || !policy.requiresApproval) {
      // No approval required, auto-approve
      if (params.callback) {
        params.callback(true, 'Auto-approved: No approval required by policy');
      }
      return 'auto-approved';
    }

    const requestId = crypto.randomBytes(16).toString('hex');
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (policy.autoRejectAfter || this.defaultTimeout));

    const request: ApprovalRequest = {
      id: requestId,
      connectorId: params.connectorId,
      userId: params.userId,
      operationType: params.operationType,
      resource: params.resource,
      description: params.description,
      metadata: params.metadata,
      priority: params.priority || 'medium',
      requestedAt: now,
      expiresAt,
      requiredRole: policy.requiredRole,
      requiredApprovers: policy.requiredApprovers,
      approvers: [],
      status: 'pending',
      webhookUrl: params.webhookUrl,
      callback: params.callback
    };

    this.requests.set(requestId, request);
    await this.persistRequest(request);
    this.scheduleExpiration(request);
    this.sendWebhookNotification(request, 'created');
    this.emit('approval-created', request);
    this.emit('approval-update', request);

    this.logAudit(requestId, 'created', params.userId, {
      operationType: params.operationType,
      resource: params.resource
    });

    // Check for auto-approval
    if (policy.autoApproveAfter) {
      setTimeout(() => {
        if (this.requests.get(requestId)?.status === 'pending') {
          this.autoApprove(requestId, 'Auto-approved after timeout');
        }
      }, policy.autoApproveAfter);
    }

    return requestId;
  }

  public async approve(requestId: string, userId: string, role: string, reason?: string): Promise<void> {
    const request = this.requests.get(requestId);

    if (!request) {
      throw new Error('Approval request not found');
    }

    if (request.status !== 'pending') {
      throw new Error(`Request is already ${request.status}`);
    }

    if (!this.canUserApprove(userId, role, request)) {
      throw new Error('User does not have permission to approve this request');
    }

    // Add approver
    request.approvers.push({
      userId,
      role,
      decision: 'approved',
      reason,
      timestamp: new Date()
    });

    // Check if we have enough approvals
    const approvalCount = request.approvers.filter(a => a.decision === 'approved').length;

    if (approvalCount >= request.requiredApprovers) {
      request.status = 'approved';
      await this.finalizeRequest(request, true, reason);
    } else {
      await this.persistRequest(request);
      this.emit('approval-update', request);
    }

    this.logAudit(requestId, 'approved', userId, { reason, role });
  }

  public async reject(requestId: string, userId: string, role: string, reason?: string): Promise<void> {
    const request = this.requests.get(requestId);

    if (!request) {
      throw new Error('Approval request not found');
    }

    if (request.status !== 'pending') {
      throw new Error(`Request is already ${request.status}`);
    }

    if (!this.canUserApprove(userId, role, request)) {
      throw new Error('User does not have permission to reject this request');
    }

    request.approvers.push({
      userId,
      role,
      decision: 'rejected',
      reason,
      timestamp: new Date()
    });

    request.status = 'rejected';
    await this.finalizeRequest(request, false, reason);

    this.logAudit(requestId, 'rejected', userId, { reason, role });
  }

  private async autoApprove(requestId: string, reason: string): Promise<void> {
    const request = this.requests.get(requestId);
    if (request && request.status === 'pending') {
      request.status = 'approved';
      request.approvers.push({
        userId: 'system',
        role: 'system',
        decision: 'approved',
        reason,
        timestamp: new Date()
      });
      await this.finalizeRequest(request, true, reason);
      this.logAudit(requestId, 'auto-approved', 'system', { reason });
    }
  }

  private async finalizeRequest(request: ApprovalRequest, approved: boolean, reason?: string): Promise<void> {
    // Clear expiration timer
    const timer = this.timers.get(request.id);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(request.id);
    }

    // Execute callback
    if (request.callback) {
      try {
        request.callback(approved, reason);
      } catch (error) {
        console.error('Error executing approval callback:', error);
      }
    }

    // Send webhook notification
    this.sendWebhookNotification(request, approved ? 'approved' : 'rejected');

    // Update persistence
    await this.persistRequest(request);

    // Emit events
    this.emit('approval-finalized', request);
    this.emit('approval-update', request);
  }

  private scheduleExpiration(request: ApprovalRequest): void {
    const timeUntilExpiration = request.expiresAt.getTime() - Date.now();

    if (timeUntilExpiration > 0) {
      const timer = setTimeout(async () => {
        if (request.status === 'pending') {
          request.status = 'expired';
          await this.finalizeRequest(request, false, 'Request expired');
          this.logAudit(request.id, 'expired', 'system', {});
        }
      }, timeUntilExpiration);

      this.timers.set(request.id, timer);
    }
  }

  private canUserApprove(userId: string, role: string, request: ApprovalRequest): boolean {
    // Check if user already approved
    if (request.approvers.some(a => a.userId === userId)) {
      return false;
    }

    // Check role requirements
    if (request.requiredRole) {
      const roleHierarchy = { user: 1, admin: 2, super_admin: 3 };
      const userLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
      const requiredLevel = roleHierarchy[request.requiredRole] || 0;
      return userLevel >= requiredLevel;
    }

    return true;
  }

  private findApplicablePolicy(
    connectorId: string,
    operationType: string,
    metadata?: Record<string, any>
  ): ApprovalPolicy | null {
    const connectorPolicies = this.policies.get(connectorId) || [];

    for (const policy of connectorPolicies) {
      if (policy.operationType !== 'all' && policy.operationType !== operationType) {
        continue;
      }

      // Check conditions
      if (policy.conditions && metadata) {
        const allConditionsMet = policy.conditions.every(condition => {
          const value = metadata[condition.field];

          switch (condition.operator) {
            case 'equals':
              return value === condition.value;
            case 'contains':
              return String(value).includes(String(condition.value));
            case 'regex':
              return new RegExp(String(condition.value)).test(String(value));
            case 'gt':
              return Number(value) > Number(condition.value);
            case 'lt':
              return Number(value) < Number(condition.value);
            default:
              return false;
          }
        });

        if (!allConditionsMet) {
          continue;
        }
      }

      return policy;
    }

    return null;
  }

  private async persistRequest(request: ApprovalRequest): Promise<void> {
    if (this.redis) {
      const key = `approval:request:${request.id}`;
      const data = JSON.stringify(request, (key, value) => {
        if (key === 'callback') return undefined; // Don't serialize callbacks
        return value;
      });

      if (request.status === 'pending') {
        await this.redis.set(key, data, 'PX', request.expiresAt.getTime() - Date.now());
      } else {
        await this.redis.set(key, data, 'EX', 86400); // Keep completed requests for 24 hours
      }
    }
  }

  private async sendWebhookNotification(request: ApprovalRequest, action: string): Promise<void> {
    if (!request.webhookUrl) return;

    try {
      const payload = {
        action,
        request: {
          id: request.id,
          connectorId: request.connectorId,
          operationType: request.operationType,
          resource: request.resource,
          status: request.status,
          priority: request.priority
        },
        timestamp: new Date().toISOString(),
        signature: this.generateWebhookSignature(request.id + action)
      };

      const response = await fetch(request.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': payload.signature
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        console.error('Webhook notification failed:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  }

  private generateWebhookSignature(data: string): string {
    return crypto
      .createHmac('sha256', this.webhookSecret)
      .update(data)
      .digest('hex');
  }

  private logAudit(requestId: string, action: string, userId: string, details: any): void {
    const entry = {
      timestamp: new Date(),
      requestId,
      action,
      userId,
      details
    };

    this.auditLog.push(entry);

    // Keep only last 10000 entries
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-10000);
    }

    // Persist to Redis if available
    if (this.redis) {
      const key = `approval:audit:${Date.now()}:${requestId}`;
      // Avoid unhandled promise rejections if Redis isn't connected/available
      void this.redis
        .set(key, JSON.stringify(entry), 'EX', 2592000) // 30 days
        .catch((err) => {
          console.error('Failed to persist audit log to Redis:', err);
        });
    }
  }

  public setPolicy(connectorId: string, policies: ApprovalPolicy[]): void {
    this.policies.set(connectorId, policies);

    if (this.redis) {
      const key = `approval:policy:${connectorId}`;
      // Avoid unhandled promise rejections if Redis isn't connected/available
      void this.redis
        .set(key, JSON.stringify(policies))
        .catch((err) => {
          console.error('Failed to persist approval policies to Redis:', err);
        });
    }
  }

  public getApprovalQueue(userId?: string, role?: string): ApprovalRequest[] {
    return Array.from(this.requests.values())
      .filter(r => r.status === 'pending')
      .filter(r => {
        // If no userId and no role provided, return all pending requests
        if (!userId && !role) return true;
        // Otherwise, check whether the specified user/role can approve the request
        return this.canUserApprove(userId || '', role || '', r);
      })
      .sort((a, b) => {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });
  }

  public getAuditLog(limit = 100): typeof this.auditLog {
    return this.auditLog.slice(-limit).reverse();
  }

  public startServer(port: number = 3003): void {
    if (this.app) {
      this.app.listen(port, () => {
        console.log(`Approval Manager API listening on port ${port}`);
      });
    }
  }

  public async shutdown(): Promise<void> {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();

    // Close WebSocket server
    if (this.wsServer) {
      this.wsServer.close();
    }

    // Close Redis connection (ignore errors if already closed/not connected)
    if (this.redis) {
      try {
        await this.redis.quit();
      } catch (err) {
        // no-op
      }
    }
  }
}

// Export singleton instance
export const approvalManager = new ApprovalManager();
