/**
 * Production-ready OpenAI Responses API Client for MCP and Connector integrations
 * Provides comprehensive support for connectors, remote MCP servers, and approval workflows
 */

import OpenAI from 'openai';
import type { Stream } from 'openai/streaming';
import { z } from 'zod';
import { getConfig } from '../utils/config';
import { hash as sha256 } from '../utils/crypto';
import { runResponsesRetryLoop } from './responsesRetry';
import {
  ConnectorId,
  ApprovalMode,
  MCPApprovalRequest,
  MCPApprovalResponse,
  OAuthToken
} from '../types/connectors';

// Response API Types
export interface McpTool {
  type: 'mcp';
  server_label: string;
  connector_id?: ConnectorId;
  server_url?: string;
  server_description?: string;
  authorization?: string;
  headers?: Record<string, string>;
  require_approval?: ApprovalMode;
  allowed_tools?: string[] | { tool_names?: string[] };
}

export interface ResponseOutputItem {
  id: string;
  type: string;
  [key: string]: any;
}

export interface McpListToolsOutput extends ResponseOutputItem {
  type: 'mcp_list_tools';
  server_label: string;
  tools: Array<{
    name: string;
    description: string;
    input_schema: Record<string, any>;
    annotations?: any;
  }>;
}

export interface McpCallOutput extends ResponseOutputItem {
  type: 'mcp_call';
  approval_request_id: string | null;
  arguments: string;
  error: string | null;
  name: string;
  output: string;
  server_label: string;
}

export interface McpApprovalRequestOutput extends ResponseOutputItem {
  type: 'mcp_approval_request';
  arguments: string;
  name: string;
  server_label: string;
}

export interface ResponseCreateOptions {
  model?: string;
  tools: McpTool[];
  input: string | Array<MCPApprovalResponse | any>;
  previous_response_id?: string;
  stream?: boolean;
  temperature?: number;
  max_completion_tokens?: number;
  timeout?: number;
}

export interface ProcessedResponse {
  id: string;
  output_text: string;
  tokensUsed?: number;
  cost?: number;
  tools_called: Array<{
    name: string;
    arguments: any;
    output: any;
    error: string | null;
    server_label: string;
  }>;
  approval_requests: Array<{
    id: string;
    name: string;
    arguments: any;
    server_label: string;
  }>;
  imported_tools: Array<{
    server_label: string;
    tools: Array<{
      name: string;
      description: string;
      input_schema: any;
    }>;
  }>;
  errors: string[];
  raw_response?: any;
}

// Configuration for the client
export interface ResponsesAPIClientConfig {
  apiKey?: string;
  organization?: string;
  baseURL?: string;
  maxRetries?: number;
  timeout?: number;
  defaultModel?: string;
  enableLogging?: boolean;
}

// Session management for stateful conversations
interface ResponseSession {
  id: string;
  previous_response_id?: string;
  imported_tools: Map<string, McpListToolsOutput>;
  pending_approvals: Map<string, McpApprovalRequestOutput>;
  oauth_tokens: Map<ConnectorId, OAuthToken>;
  created_at: number;
  updated_at: number;
}

type PendingRequest = {
  fn: () => Promise<any>;
  resolve: (value: any) => void;
  reject: (error: unknown) => void;
};

/**
 * Enterprise-grade OpenAI Responses API Client
 */
export class ResponsesAPIClient {
  private openai: OpenAI;
  private sessions: Map<string, ResponseSession> = new Map();
  private defaultModel: string;
  private enableLogging: boolean;
  private requestQueue: PendingRequest[] = [];
  private isProcessingQueue = false;
  private rateLimitDelay = 1000; // ms between requests
  private lastRequestTime = 0;

  constructor(config: ResponsesAPIClientConfig = {}) {
    const apiKey = config.apiKey || process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required for ResponsesAPIClient');
    }

    this.openai = new OpenAI({
      apiKey,
      organization: config.organization,
      baseURL: config.baseURL,
      maxRetries: config.maxRetries || 3,
      timeout: config.timeout || 120000, // 2 minutes default
    });

    this.defaultModel = config.defaultModel || 'gpt-5';
    this.enableLogging = config.enableLogging ?? false;
  }

  /**
   * Create a new response session for stateful conversations
   */
  createSession(sessionId?: string): string {
    const id = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const session: ResponseSession = {
      id,
      imported_tools: new Map(),
      pending_approvals: new Map(),
      oauth_tokens: new Map(),
      created_at: Date.now(),
      updated_at: Date.now()
    };

    this.sessions.set(id, session);
    this.log('info', `Created new session: ${id}`);

    return id;
  }

  /**
   * Store OAuth token for a connector in a session
   */
  storeOAuthToken(sessionId: string, connectorId: ConnectorId, token: OAuthToken): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Validate token expiration
    if (token.expires_at && token.expires_at < Date.now()) {
      this.log('warn', `Token for ${connectorId} is expired`);
    }

    session.oauth_tokens.set(connectorId, token);
    session.updated_at = Date.now();

    this.log('info', `Stored OAuth token for ${connectorId} in session ${sessionId}`);
  }

  /**
   * Execute a connector request
   */
  async executeConnectorRequest(options: {
    sessionId?: string;
    connector_id: ConnectorId;
    oauth_token?: string;
    input: string;
    require_approval?: ApprovalMode;
    allowed_tools?: string[];
    model?: string;
    stream?: boolean;
  }): Promise<ProcessedResponse> {
    const sessionId = options.sessionId || this.createSession();
    const session = this.sessions.get(sessionId);

    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Get OAuth token from session or use provided one
    let oauthToken = options.oauth_token;
    if (!oauthToken && options.connector_id) {
      const storedToken = session.oauth_tokens.get(options.connector_id);
      if (storedToken) {
        console.log('Using stored token for connector request:', storedToken);
        // Check expiration
        if (storedToken.expires_at && storedToken.expires_at < Date.now()) {
          throw new Error(`Stored token for ${options.connector_id} has expired`);
        }
        oauthToken = storedToken.access_token;
      }
    }

    if (!oauthToken) {
      throw new Error(`OAuth token required for connector ${options.connector_id}`);
    }

    const tool: McpTool = {
      type: 'mcp',
      server_label: this.getConnectorLabel(options.connector_id),
      connector_id: options.connector_id,
      authorization: oauthToken,
      require_approval: options.require_approval || 'always',
      allowed_tools: options.allowed_tools
    };

    return this.createResponse({
      sessionId,
      tools: [tool],
      input: options.input,
      model: options.model,
      stream: options.stream
    });
  }

  /**
   * Execute a remote MCP server request
   */
  async executeMCPServerRequest(options: {
    sessionId?: string;
    server_url: string;
    server_label: string;
    server_description?: string;
    authorization?: string;
    headers?: Record<string, string>;
    input: string;
    require_approval?: ApprovalMode;
    allowed_tools?: string[];
    model?: string;
    stream?: boolean;
  }): Promise<ProcessedResponse> {
    const sessionId = options.sessionId || this.createSession();

    // Validate server URL
    try {
      new URL(options.server_url);
    } catch (error) {
      throw new Error(`Invalid server URL: ${options.server_url}`);
    }

    const tool: McpTool = {
      type: 'mcp',
      server_label: options.server_label,
      server_url: options.server_url,
      server_description: options.server_description,
      authorization: options.authorization,
      headers: options.headers,
      require_approval: options.require_approval || 'always',
      allowed_tools: options.allowed_tools
    };

    return this.createResponse({
      sessionId,
      tools: [tool],
      input: options.input,
      model: options.model,
      stream: options.stream
    });
  }

  private async createResponse(options: {
    sessionId: string;
    tools: McpTool[];
    input: string | Array<any>;
    model?: string;
    stream?: boolean;
  }): Promise<ProcessedResponse> {
    const session = this.sessions.get(options.sessionId);
    if (!session) {
      console.error(`Session ${options.sessionId} not found when creating response`);
      throw new Error(`Session ${options.sessionId} not found`);
    }

    const cfg = getConfig().orchestrator?.resilience?.retry || {};
    const maxAttempts = Number.isFinite(cfg.maxAttempts) ? Number(cfg.maxAttempts) : 5;
    const baseDelayMs = Number.isFinite(cfg.baseDelayMs) ? Number(cfg.baseDelayMs) : 500;
    const maxDelayMs = Number.isFinite(cfg.maxDelayMs) ? Number(cfg.maxDelayMs) : 8000;
    const jitter = typeof cfg.jitter === 'number' ? cfg.jitter : 0.2;
    const classifyRs404AsTransient = cfg.classifyRs404AsTransient !== false;

    const requestBody: any = {
      model: options.model || this.defaultModel,
      tools: options.tools,
      input: options.input
    };

    // Add previous response ID if continuing a conversation
    if (session.previous_response_id) {
      requestBody.previous_response_id = session.previous_response_id;
    }

    const inputHash = sha256(
      typeof options.input === 'string' ? options.input : JSON.stringify(options.input)
    );
    const operationKey = sha256(
      JSON.stringify({
        model: requestBody.model,
        inputHash,
        sessionId: options.sessionId
      })
    );

    const retryConfig = {
      maxAttempts,
      baseDelayMs,
      maxDelayMs,
      jitter,
      classifyRs404AsTransient
    };

    return runResponsesRetryLoop<ProcessedResponse>({
      operationKey,
      config: retryConfig,
      summaryLabel: 'responses.retry',
      sleep: (ms: number) => this.sleep(ms),
      execute: async (attempt: number) => {
        this.log('info', `Creating response (attempt ${attempt}/${retryConfig.maxAttempts}) for session ${options.sessionId}`);
        return this.enqueueRequest(async () => {
          if (options.stream) {
            const streamResp = await this.openai.responses.create({ ...requestBody, stream: true } as any);
            const result = await this.processStreamingResponse(streamResp as any, session, true);
            session.updated_at = Date.now();
            return result;
          }

          const response = await this.openai.responses.create(requestBody as any);
          session.previous_response_id = response.id;
          session.updated_at = Date.now();
          return this.processResponse(response, session);
        });
      },
      onTransientError: async ({ classification }) => {
        if (classification.rsNotFound) {
          delete requestBody.previous_response_id;
          session.previous_response_id = undefined;
        }
      },
      onRetryScheduled: async ({ attempt, classification, delayMs }) => {
        this.log('warn', `Retrying attempt ${attempt + 1} in ${delayMs}ms (status=${classification.status ?? 'n/a'})`);
      },
      onPermanentFailure: async ({ attempt, error }) => {
        this.log('error', `Response creation failed permanently on attempt ${attempt}: ${error}`);
        if (error instanceof OpenAI.APIError && (error.status === 401 || error.status === 403)) {
          if (error.status === 401) {
            return new Error('Authentication failed. Please check your API key.');
          }
          if (error.status === 403) {
            return new Error('Access denied. Please check your permissions.');
          }
        }
      }
    });
  }
  /**
   * Handle approval for MCP tool calls
   */
  async handleApproval(options: {
    sessionId: string;
    approval_request_id: string;
    approve: boolean;
    tool?: McpTool;
  }): Promise<ProcessedResponse> {
    const session = this.sessions.get(options.sessionId);
    if (!session) {
      throw new Error(`Session ${options.sessionId} not found`);
    }

    const approvalRequest = session.pending_approvals.get(options.approval_request_id);
    if (!approvalRequest) {
      throw new Error(`Approval request ${options.approval_request_id} not found`);
    }

    const approvalResponse: MCPApprovalResponse = {
      type: 'mcp_approval_response',
      approve: options.approve,
      approval_request_id: options.approval_request_id
    };

    // Get the tool from the approval request or use provided one
    const tool = options.tool || this.reconstructToolFromApproval(approvalRequest);

    return this.createResponse({
      sessionId: options.sessionId,
      tools: [tool],
      input: [approvalResponse],
      model: this.defaultModel
    });
  }

  /**
   * Process non-streaming response
   */
  private processResponse(response: any, session: ResponseSession): ProcessedResponse {
    const result: ProcessedResponse = {
      id: response.id,
      output_text: response.output_text || '',
      tools_called: [],
      approval_requests: [],
      imported_tools: [],
      errors: [],
      raw_response: this.enableLogging ? response : undefined
    };

    // Process output items
    if (response.output && Array.isArray(response.output)) {
      for (const item of response.output) {
        this.processOutputItem(item, result, session);
      }
    }

    this.log('info', `Processed response ${response.id} with ${result.tools_called.length} tool calls`);

    return result;
  }

  /**
   * Process streaming response
   */
  private async processStreamingResponse(
    stream: any,
    session: ResponseSession,
    rethrowOnError: boolean = false
  ): Promise<ProcessedResponse> {
    const result: ProcessedResponse = {
      id: '',
      output_text: '',
      tools_called: [],
      approval_requests: [],
      imported_tools: [],
      errors: []
    };

    const chunks: any[] = [];

    try {
      for await (const chunk of stream) {
        chunks.push(chunk);

        // Process streaming events
        if (chunk.type === 'response.output_text.delta') {
          result.output_text += chunk.delta?.text || '';
        } else if (chunk.type === 'response.id') {
          result.id = chunk.id;
          session.previous_response_id = chunk.id;
        } else if (chunk.type === 'response.output_item.done') {
          this.processOutputItem(chunk.item, result, session);
        }
      }

      this.log('info', `Processed streaming response with ${chunks.length} chunks`);

    } catch (error) {
      this.log('error', `Streaming response error: ${error}`);
      if (rethrowOnError) {
        throw error;
      }
      result.errors.push(`Streaming error: ${error}`);
    }

    return result;
  }

  /**
   * Process individual output items
   */
  private processOutputItem(
    item: any,
    result: ProcessedResponse,
    session: ResponseSession
  ): void {
    switch (item.type) {
      case 'mcp_list_tools':
        // Tools imported from connector/server
        const toolsOutput = item as McpListToolsOutput;
        result.imported_tools.push({
          server_label: toolsOutput.server_label,
          tools: toolsOutput.tools
        });

        // Cache imported tools in session
        session.imported_tools.set(toolsOutput.server_label, toolsOutput);

        this.log('info', `Imported ${toolsOutput.tools.length} tools from ${toolsOutput.server_label}`);
        break;

      case 'mcp_call':
        // Tool was called
        const callOutput = item as McpCallOutput;
        result.tools_called.push({
          name: callOutput.name,
          arguments: this.safeParseJSON(callOutput.arguments),
          output: this.safeParseJSON(callOutput.output),
          error: callOutput.error,
          server_label: callOutput.server_label
        });

        if (callOutput.error) {
          result.errors.push(`Tool call error (${callOutput.name}): ${callOutput.error}`);
        }
        break;

      case 'mcp_approval_request':
        // Approval needed
        const approvalRequest = item as McpApprovalRequestOutput;
        result.approval_requests.push({
          id: approvalRequest.id,
          name: approvalRequest.name,
          arguments: this.safeParseJSON(approvalRequest.arguments),
          server_label: approvalRequest.server_label
        });

        // Cache approval request in session
        session.pending_approvals.set(approvalRequest.id, approvalRequest);
        break;

      case 'reasoning':
        // Store reasoning for debugging and context - these are internal thoughts from the model
        // We log them but don't need to add them to the response as they're for internal use
        this.log('debug', `Model reasoning: ${item.content}`);
        break;

      case 'message':
        // Assistant messages are captured in output_text already via streaming
        // We log them for debugging but don't need to duplicate
        const messageContent = typeof item.content === 'object'
          ? JSON.stringify(item.content, null, 2)
          : item.content;
        this.log('debug', `Assistant message: ${messageContent}`);
        break;

      case 'output_text':
        // Append to output text - this is the actual response text
        result.output_text += item.text || item.content || '';
        this.log('debug', `Added output text: ${(item.text || item.content || '').substring(0, 100)}...`);
        break;

      default:
        // Log any truly unknown output types for future investigation
        this.log('debug', `Unknown output type: ${item.type} - content: ${JSON.stringify(item).substring(0, 200)}`);
    }
  }

  /**
   * Safely parse JSON with error handling
   */
  private safeParseJSON(str: string | null | undefined): any {
    if (!str) return null;

    try {
      return JSON.parse(str);
    } catch (error) {
      this.log('warn', `Failed to parse JSON: ${str}`);
      return str;
    }
  }

  /**
   * Reconstruct tool from approval request
   */
  private reconstructToolFromApproval(approvalRequest: McpApprovalRequestOutput): McpTool {
    // Try to find the original tool from imported tools
    const importedTools = this.sessions.get('current')?.imported_tools.get(approvalRequest.server_label);

    return {
      type: 'mcp',
      server_label: approvalRequest.server_label,
      require_approval: 'always'
    };
  }

  /**
   * Apply rate limiting between requests
   */
  private async applyRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.rateLimitDelay) {
      const delay = this.rateLimitDelay - timeSinceLastRequest;
      this.log('debug', `Rate limiting: waiting ${delay}ms`);
      await this.sleep(delay);
    }

    this.lastRequestTime = Date.now();
  }

  private enqueueRequest<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.requestQueue.push({ fn, resolve, reject });
      void this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.requestQueue.length > 0) {
        const item = this.requestQueue.shift();
        if (!item) {
          continue;
        }

        try {
          await this.applyRateLimit();
          const result = await item.fn();
          item.resolve(result);
        } catch (error) {
          item.reject(error);
        }
      }
    } finally {
      this.isProcessingQueue = false;

      if (this.requestQueue.length > 0) {
        // Handle any jobs added while the queue was processing but after the loop completed
        void this.processQueue();
      }
    }
  }

  /**
   * Sleep utility for rate limiting
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get human-readable label for connector
   */
  private getConnectorLabel(connectorId: ConnectorId): string {
    const labels: Record<ConnectorId, string> = {
      'connector_gmail': 'Gmail',
      'connector_googlecalendar': 'Google_Calendar',
      'connector_googledrive': 'Google_Drive',
      'connector_dropbox': 'Dropbox',
      'connector_microsoftteams': 'Microsoft_Teams',
      'connector_outlookcalendar': 'Outlook_Calendar',
      'connector_outlookemail': 'Outlook_Email',
      'connector_sharepoint': 'SharePoint'
    };

    return labels[connectorId] || connectorId;
  }

  /**
   * Batch multiple tool requests
   */
  async executeBatchRequests(options: {
    sessionId?: string;
    requests: Array<{
      tool: McpTool;
      input: string;
    }>;
    model?: string;
  }): Promise<ProcessedResponse[]> {
    const sessionId = options.sessionId || this.createSession();
    const results: ProcessedResponse[] = [];

    for (const request of options.requests) {
      try {
        const result = await this.createResponse({
          sessionId,
          tools: [request.tool],
          input: request.input,
          model: options.model
        });
        results.push(result);
      } catch (error) {
        this.log('error', `Batch request failed: ${error}`);
        results.push({
          id: '',
          output_text: '',
          tools_called: [],
          approval_requests: [],
          imported_tools: [],
          errors: [`Request failed: ${error}`]
        });
      }
    }

    return results;
  }

  /**
   * Get session information
   */
  getSession(sessionId: string): ResponseSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Clear session data
   */
  clearSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  /**
   * Clear all sessions
   */
  clearAllSessions(): void {
    this.sessions.clear();
    this.log('info', 'Cleared all sessions');
  }

  /**
   * Get available connector tools
   */
  getConnectorTools(connectorId: ConnectorId): string[] {
    const connectorTools: Record<ConnectorId, string[]> = {
      'connector_gmail': [
        'get_profile',
        'search_emails',
        'search_email_ids',
        'get_recent_emails',
        'read_email',
        'batch_read_email'
      ],
      'connector_googlecalendar': [
        'get_profile',
        'search_events',
        'fetch',
        'read_event'
      ],
      'connector_googledrive': [
        'get_profile',
        'list_drives',
        'search',
        'recent_documents',
        'fetch'
      ],
      'connector_dropbox': [
        'search',
        'fetch',
        'search_files',
        'fetch_file',
        'list_recent_files',
        'get_profile'
      ],
      'connector_microsoftteams': [
        'search',
        'fetch',
        'get_chat_members',
        'get_profile'
      ],
      'connector_outlookcalendar': [
        'search_events',
        'fetch_event',
        'fetch_events_batch',
        'list_events',
        'get_profile'
      ],
      'connector_outlookemail': [
        'get_profile',
        'list_messages',
        'search_messages',
        'get_recent_emails',
        'fetch_message',
        'fetch_messages_batch'
      ],
      'connector_sharepoint': [
        'get_site',
        'search',
        'list_recent_documents',
        'fetch',
        'get_profile'
      ]
    };

    return connectorTools[connectorId] || [];
  }

  /**
   * Validate connector configuration
   */
  validateConnectorConfig(connectorId: ConnectorId, oauthToken?: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate connector ID
    const validConnectors: ConnectorId[] = [
      'connector_gmail',
      'connector_googlecalendar',
      'connector_googledrive',
      'connector_dropbox',
      'connector_microsoftteams',
      'connector_outlookcalendar',
      'connector_outlookemail',
      'connector_sharepoint'
    ];

    if (!validConnectors.includes(connectorId)) {
      errors.push(`Invalid connector ID: ${connectorId}`);
    }

    // Validate OAuth token if provided
    if (oauthToken) {
      if (!oauthToken.startsWith('ya29.') && !oauthToken.startsWith('eyJ')) {
        errors.push('OAuth token format appears invalid');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Logging utility
   */
  private log(level: 'info' | 'warn' | 'error' | 'debug', message: string): void {
    if (!this.enableLogging) return;

    const timestamp = new Date().toISOString();
    const prefix = `[ResponsesAPIClient] [${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
      case 'error':
        console.error(`${prefix} ${message}`);
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`);
        break;
      case 'debug':
        console.debug(`${prefix} ${message}`);
        break;
      default:
        console.log(`${prefix} ${message}`);
    }
  }

}

// Generic non-stream retry wrapper for Vercel agents (generateText-style)
export async function withRsRetryNonStreaming<T>(params: {
  modelId?: string;
  input: string | any;
  execute: () => Promise<T>;
  classifyRs404AsTransient?: boolean;
  maxAttempts?: number;
  baseDelayMs?: number;
  maxDelayMs?: number;
  jitter?: number;
}): Promise<T> {
  const cfg = getConfig().orchestrator?.resilience?.retry || {};
  const maxAttempts = Number.isFinite(params.maxAttempts) ? Number(params.maxAttempts) : (cfg.maxAttempts ?? 5);
  const baseDelayMs = Number.isFinite(params.baseDelayMs) ? Number(params.baseDelayMs) : (cfg.baseDelayMs ?? 500);
  const maxDelayMs = Number.isFinite(params.maxDelayMs) ? Number(params.maxDelayMs) : (cfg.maxDelayMs ?? 8000);
  const jitter = typeof params.jitter === 'number' ? params.jitter : (cfg.jitter ?? 0.2);
  const classifyRs404AsTransient = params.classifyRs404AsTransient ?? (cfg.classifyRs404AsTransient !== false);

  const inputHash = sha256(typeof params.input === 'string' ? params.input : JSON.stringify(params.input));
  const operationKey = sha256(JSON.stringify({ model: params.modelId || 'vercel-model', inputHash }));

  const retryConfig = {
    maxAttempts,
    baseDelayMs,
    maxDelayMs,
    jitter,
    classifyRs404AsTransient
  };

  return runResponsesRetryLoop<T>({
    operationKey,
    config: retryConfig,
    summaryLabel: 'vercel.retry',
    execute: async (_attempt: number) => params.execute()
  });
}

// Export singleton instance for convenience
export const responsesAPIClient = new ResponsesAPIClient({
  enableLogging: process.env.NODE_ENV !== 'production'
});

// Export example usage functions
export const examples = {
  /**
   * Example: Using Gmail connector
   */
  async gmailExample(oauthToken: string) {
    const client = new ResponsesAPIClient({ enableLogging: true });
    const sessionId = client.createSession();

    // Store OAuth token for reuse
    client.storeOAuthToken(sessionId, 'connector_gmail', {
      access_token: oauthToken,
      token_type: 'Bearer',
      scope: 'https://www.googleapis.com/auth/gmail.modify'
    });

    // Search emails
    const response = await client.executeConnectorRequest({
      sessionId,
      connector_id: 'connector_gmail',
      input: 'Find all unread emails from the last week',
      require_approval: 'never',
      allowed_tools: ['search_emails', 'read_email']
    });

    console.log('Gmail search results:', response);
    return response;
  },

  /**
   * Example: Using remote MCP server with approval
   */
  async mcpServerWithApproval() {
    const client = new ResponsesAPIClient({ enableLogging: true });
    const sessionId = client.createSession();

    // Initial request that may require approval
    const response = await client.executeMCPServerRequest({
      sessionId,
      server_url: 'https://mcp.stripe.com',
      server_label: 'Stripe',
      server_description: 'Stripe payment processing',
      authorization: 'Bearer sk_test_...',
      input: 'Create a payment link for $50',
      require_approval: 'always'
    });

    // Check for approval requests
    if (response.approval_requests.length > 0) {
      console.log('Approval required for:', response.approval_requests);

      // Approve the first request
      const approvalResponse = await client.handleApproval({
        sessionId,
        approval_request_id: response.approval_requests[0].id,
        approve: true
      });

      console.log('After approval:', approvalResponse);
    }

    return response;
  },

  /**
   * Example: Batch requests to multiple connectors
   */
  async batchConnectorRequests(gmailToken: string, calendarToken: string) {
    const client = new ResponsesAPIClient({ enableLogging: true });

    const results = await client.executeBatchRequests({
      requests: [
        {
          tool: {
            type: 'mcp',
            server_label: 'Gmail',
            connector_id: 'connector_gmail',
            authorization: gmailToken,
            require_approval: 'never'
          },
          input: 'Get my recent emails'
        },
        {
          tool: {
            type: 'mcp',
            server_label: 'Google_Calendar',
            connector_id: 'connector_googlecalendar',
            authorization: calendarToken,
            require_approval: 'never'
          },
          input: 'What events do I have today?'
        }
      ]
    });

    console.log('Batch results:', results);
    return results;
  }
};

// NOTE: New retry/journal utilities are appended at end of this file for clarity.

/* eslint-disable @typescript-eslint/no-explicit-any */
