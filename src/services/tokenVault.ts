/**
 * TokenVault - Secure token storage with encryption and database persistence
 * Implements enterprise-grade security for OAuth tokens
 */

import { Database as BunDatabase, SQLQueryBindings } from 'bun:sqlite';
import { Pool } from 'pg';
import {
  encryptForStorage,
  decryptFromStorage,
  encryptForStorageAsync,
  decryptFromStorageAsync,
  hash,
  generateSecureRandom,
} from '../utils/crypto';
import { existsSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { EventEmitter } from 'events';
import { SCHEMA, INDEXES } from '../db/schema';
import { toSQLiteTimestamp } from '../utils/date';


// Token types
export interface OAuthToken {
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt: Date;
  refreshExpiresAt?: Date;
  scope?: string;
  idToken?: string;
}

export interface StoredSession {
  id: number;
  userId: number;
  provider: string;
  sessionId: string;
  tokens: OAuthToken;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastRefreshed?: Date;
  refreshCount: number;
}

export interface User {
  id: number;
  externalId: string;
  email: string;
  name?: string;
  metadata?: Record<string, any>;
}

export interface AuditEvent {
  eventType: string;
  userId?: number;
  sessionId?: string;
  provider?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

// Database configuration
interface DatabaseConfig {
  type: 'sqlite' | 'postgresql';
  connectionString?: string;
  filename?: string;
  pool?: {
    min?: number;
    max?: number;
    idleTimeoutMillis?: number;
  };
}

// Token vault configuration
interface TokenVaultConfig {
  database: DatabaseConfig;
  autoRefreshThreshold?: number; // Minutes before expiry to auto-refresh
  maxRefreshAttempts?: number;
  auditRetentionDays?: number;
  enableAutoCleanup?: boolean;
  cleanupIntervalHours?: number;
}

export class TokenVault extends EventEmitter {
  private db!: BunDatabase | Pool; // Initialized in initializeDatabase()
  private config: Required<TokenVaultConfig>;
  private cleanupTimer?: NodeJS.Timeout;
  private refreshTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: TokenVaultConfig) {
    super();

    // Set defaults
    this.config = {
      database: config.database,
      autoRefreshThreshold: config.autoRefreshThreshold ?? 5,
      maxRefreshAttempts: config.maxRefreshAttempts ?? 3,
      auditRetentionDays: config.auditRetentionDays ?? 90,
      enableAutoCleanup: config.enableAutoCleanup ?? true,
      cleanupIntervalHours: config.cleanupIntervalHours ?? 24
    };

    this.initializeDatabase();
  }

  /**
   * Initialize database schema
   */
  private initializeSchema(): void {
    if (this.db instanceof BunDatabase) {
      // Create tables
      for (const [tableName, createStatement] of Object.entries(SCHEMA)) {
        try {
          this.db.exec(createStatement);
        } catch (error) {
          console.error(`Failed to create table ${tableName}:`, error);
        }
      }

      // Create indexes
      for (const indexStatement of INDEXES) {
        try {
          this.db.exec(indexStatement);
        } catch (error) {
          // Ignore if index already exists
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (!errorMessage?.includes('already exists')) {
            console.error('Failed to create index:', error);
          }
        }
      }
    }
    // PostgreSQL schema initialization would go here
  }

  /**
   * Initialize database connection
   */
  private initializeDatabase(): void {
    if (this.config.database.type === 'sqlite') {
      const filename = this.config.database.filename ?? join(process.cwd(), 'data', 'tokens.db');

      // Ensure directory exists
      const dir = dirname(filename);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      this.db = new BunDatabase(filename);
      this.db.exec('PRAGMA journal_mode = WAL');
      this.db.exec('PRAGMA foreign_keys = ON');

      // Initialize schema using TypeScript definitions
      this.initializeSchema();
    } else {
      // PostgreSQL
      this.db = new Pool({
        connectionString: this.config.database.connectionString,
        min: this.config.database.pool?.min ?? 2,
        max: this.config.database.pool?.max ?? 10,
        idleTimeoutMillis: this.config.database.pool?.idleTimeoutMillis ?? 30000
      });
    }


    // Start cleanup timer if enabled
    if (this.config.enableAutoCleanup) {
      this.startCleanupTimer();
    }
  }

  /**
   * Create or update a user
   */
  async upsertUser(
    externalId: string,
    email: string,
    name?: string,
    metadata?: Record<string, any>
  ): Promise<User> {
    const metadataJson = metadata ? JSON.stringify(metadata) : null;

    if (this.db instanceof BunDatabase) {
      // First try to update
      const updateStmt = this.db.prepare(`
        UPDATE users SET
          email = ?,
          name = ?,
          metadata = ?,
          updated_at = CURRENT_TIMESTAMP,
          last_login = CURRENT_TIMESTAMP
        WHERE external_id = ?
      `);

      const updateResult = updateStmt.run(email, name || null, metadataJson, externalId);

      if (updateResult.changes === 0) {
        // No rows updated, so insert
        const insertStmt = this.db.prepare(`
          INSERT INTO users (external_id, email, name, metadata)
          VALUES (?, ?, ?, ?)
        `);
        insertStmt.run(externalId, email, name || null, metadataJson);
      }

      // Fetch the user
      const selectStmt = this.db.prepare('SELECT * FROM users WHERE external_id = ?');
      const user = selectStmt.get(externalId) as any;
      if (!user) {
        throw new Error("User not found after insert/update");
      }
      return this.mapUser(user);
    } else {
      const result = await this.db.query(`
        INSERT INTO users (external_id, email, name, metadata, updated_at)
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        ON CONFLICT(external_id) DO UPDATE SET
          email = EXCLUDED.email,
          name = EXCLUDED.name,
          metadata = EXCLUDED.metadata,
          updated_at = CURRENT_TIMESTAMP,
          last_login = CURRENT_TIMESTAMP
        RETURNING *
      `, [externalId, email, name, metadataJson]);

      return this.mapUser(result.rows[0]);
    }
  }

  /**
   * Store OAuth tokens securely
   */
  async storeTokens(
    userId: number,
    provider: string,
    tokens: OAuthToken,
    stateHash: string,
    pkceVerifierHash?: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: Record<string, any>,
    options?: { returnSession?: boolean }
  ): Promise<StoredSession> {
    // Encrypt sensitive tokens
    const [accessTokenEncrypted, refreshTokenEncrypted, idTokenEncrypted] = await Promise.all([
      encryptForStorageAsync(tokens.accessToken),
      tokens.refreshToken ? encryptForStorageAsync(tokens.refreshToken) : Promise.resolve(null),
      tokens.idToken ? encryptForStorageAsync(tokens.idToken) : Promise.resolve(null),
    ]);

    const sessionId = generateSecureRandom(32);
    const metadataJson = metadata ? JSON.stringify(metadata) : null;

    if (this.db instanceof BunDatabase) {
      // Check if ANY session exists (active or revoked)
      console.log(`Checking for existing session: userId=${userId}, provider=${provider}`);
      const existingSession = this.db.prepare(`
        SELECT id FROM oauth_sessions
        WHERE user_id = ? AND provider = ?
        ORDER BY created_at DESC
        LIMIT 1
      `).get(userId, provider) as any;

      console.log('Existing session found:', existingSession);

      if (existingSession) {
        // Update existing session (reactivate if it was revoked)
        const updateStmt = this.db.prepare(`
          UPDATE oauth_sessions
          SET access_token_encrypted = ?,
              refresh_token_encrypted = ?,
              token_type = ?,
              expires_at = ?,
              refresh_expires_at = ?,
              scope = ?,
              id_token_encrypted = ?,
              state_hash = ?,
              pkce_verifier_hash = ?,
              ip_address = ?,
              user_agent = ?,
              metadata = ?,
              encryption_version = ?,
              updated_at = CURRENT_TIMESTAMP,
              last_refreshed = CURRENT_TIMESTAMP,
              refresh_count = refresh_count + 1,
              revoked_at = NULL,
              revoke_reason = NULL
          WHERE id = ?
        `);

        updateStmt.run(
          JSON.stringify(accessTokenEncrypted),
          refreshTokenEncrypted ? JSON.stringify(refreshTokenEncrypted) : null,
          tokens.tokenType,
          toSQLiteTimestamp(tokens.expiresAt),
          tokens.refreshExpiresAt ? toSQLiteTimestamp(tokens.refreshExpiresAt) : null,
          tokens.scope ?? null,
          idTokenEncrypted ? JSON.stringify(idTokenEncrypted) : null,
          stateHash,
          pkceVerifierHash ?? null,
          ipAddress ?? null,
          userAgent ?? null,
          metadataJson,
          1,
          existingSession.id
        );

        // Use existing session ID for fetching
        const selectStmt = this.db.prepare('SELECT * FROM oauth_sessions WHERE id = ?');
        const session = selectStmt.get(existingSession.id) as any;

        console.log(`Session updated successfully: id=${existingSession.id}, userId=${userId}, provider=${provider}`);

        // Ensure auto-refresh is scheduled if we have a refresh token
        if (tokens.refreshToken) {
          this.scheduleTokenRefresh(session.session_id, tokens.expiresAt);
        }

        // Log audit event (don't await for SQLite)
        if (this.db instanceof BunDatabase) {
          this.logAudit({
            eventType: 'token_updated',
            userId,
            sessionId: session.session_id,
            provider,
            ipAddress,
            userAgent,
            success: true,
            metadata: {
              ...metadata,
              reason: 'session_refresh'
            }
          });
        } else {
          await this.logAudit({
            eventType: 'token_updated',
            userId,
            sessionId: session.session_id,
            provider,
            ipAddress,
            userAgent,
            success: true,
            metadata: {
              ...metadata,
              reason: 'session_refresh'
            }
          });
        }

        if (options?.returnSession === false) {
          return {
            id: session.id,
            userId: session.user_id,
            provider: session.provider,
            sessionId: session.session_id,
            tokens: {
              accessToken: tokens.accessToken,
              refreshToken: tokens.refreshToken,
              tokenType: tokens.tokenType,
              expiresAt: tokens.expiresAt,
              refreshExpiresAt: tokens.refreshExpiresAt,
              scope: tokens.scope,
              idToken: tokens.idToken,
            },
            metadata: session.metadata ? JSON.parse(session.metadata) : undefined,
            createdAt: new Date(session.created_at),
            updatedAt: new Date(session.updated_at),
            lastRefreshed: session.last_refreshed ? new Date(session.last_refreshed) : undefined,
            refreshCount: session.refresh_count
          } as StoredSession;
        }
        return await this.mapSessionAsync(session);
      } else {
        // No existing session at all, create new one
        console.log(`Creating new session for userId=${userId}, provider=${provider}`);

        // Insert new session
        const stmt = this.db.prepare(`
          INSERT INTO oauth_sessions (
            user_id, provider, access_token_encrypted, refresh_token_encrypted,
            token_type, expires_at, refresh_expires_at, scope, id_token_encrypted,
            state_hash, pkce_verifier_hash, session_id, ip_address, user_agent,
            metadata, encryption_version
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run(
        userId,
        provider,
        JSON.stringify(accessTokenEncrypted),
        refreshTokenEncrypted ? JSON.stringify(refreshTokenEncrypted) : null,
        tokens.tokenType,
        toSQLiteTimestamp(tokens.expiresAt),
        tokens.refreshExpiresAt ? toSQLiteTimestamp(tokens.refreshExpiresAt) : null,
        tokens.scope ?? null,
        idTokenEncrypted ? JSON.stringify(idTokenEncrypted) : null,
        stateHash,
        pkceVerifierHash ?? null,
        sessionId,
        ipAddress ?? null,
        userAgent ?? null,
        metadataJson,
        1
        );

        // Fetch the inserted session
        const selectStmt = this.db.prepare('SELECT * FROM oauth_sessions WHERE session_id = ?');
        const session = selectStmt.get(sessionId) as any;

        console.log(`New session created successfully: sessionId=${sessionId}, userId=${userId}, provider=${provider}`);

        // Log audit event (don't await for SQLite)
        if (this.db instanceof BunDatabase) {
          this.logAudit({
            eventType: 'token_stored',
            userId,
            sessionId,
            provider,
            ipAddress,
            userAgent,
            success: true,
            metadata: { provider }
          });
        } else {
          await this.logAudit({
            eventType: 'token_stored',
            userId,
            sessionId,
            provider,
            ipAddress,
            userAgent,
            success: true,
            metadata: { provider }
          });
        }

        // Schedule auto-refresh if applicable
        if (tokens.refreshToken) {
          this.scheduleTokenRefresh(sessionId, tokens.expiresAt);
        }

        if (options?.returnSession === false) {
          return {
            id: session.id,
            userId: session.user_id,
            provider: session.provider,
            sessionId: session.session_id,
            tokens: {
              accessToken: tokens.accessToken,
              refreshToken: tokens.refreshToken,
              tokenType: tokens.tokenType,
              expiresAt: tokens.expiresAt,
              refreshExpiresAt: tokens.refreshExpiresAt,
              scope: tokens.scope,
              idToken: tokens.idToken,
            },
            metadata: session.metadata ? JSON.parse(session.metadata) : undefined,
            createdAt: new Date(session.created_at),
            updatedAt: new Date(session.updated_at),
            lastRefreshed: session.last_refreshed ? new Date(session.last_refreshed) : undefined,
            refreshCount: session.refresh_count
          } as StoredSession;
        }
        return await this.mapSessionAsync(session);
      }
    } else {
      // PostgreSQL implementation
      await this.db.query(
        'UPDATE oauth_sessions SET revoked_at = CURRENT_TIMESTAMP, revoke_reason = $1 WHERE user_id = $2 AND provider = $3 AND revoked_at IS NULL',
        ['new_session', userId, provider]
      );

      const result = await this.db.query(`
        INSERT INTO oauth_sessions (
          user_id, provider, access_token_encrypted, refresh_token_encrypted,
          token_type, expires_at, refresh_expires_at, scope, id_token_encrypted,
          state_hash, pkce_verifier_hash, session_id, ip_address, user_agent,
          metadata, encryption_version
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING *
      `, [
        userId,
        provider,
        JSON.stringify(accessTokenEncrypted),
        refreshTokenEncrypted ? JSON.stringify(refreshTokenEncrypted) : null,
        tokens.tokenType,
        tokens.expiresAt,
        tokens.refreshExpiresAt ?? null,
        tokens.scope ?? null,
        idTokenEncrypted ? JSON.stringify(idTokenEncrypted) : null,
        stateHash,
        pkceVerifierHash ?? null,
        sessionId,
        ipAddress ?? null,
        userAgent ?? null,
        metadataJson,
        1
      ]);

      await this.logAudit({
        eventType: 'token_stored',
        userId,
        sessionId,
        provider,
        ipAddress,
        userAgent,
        success: true,
        metadata: { provider }
      });

      if (tokens.refreshToken) {
        this.scheduleTokenRefresh(sessionId, tokens.expiresAt);
      }

      if (options?.returnSession === false) {
        const row: any = result.rows[0];
        return {
          id: row.id,
          userId: row.user_id,
          provider: row.provider,
          sessionId: row.session_id,
          tokens: {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            tokenType: tokens.tokenType,
            expiresAt: tokens.expiresAt,
            refreshExpiresAt: tokens.refreshExpiresAt,
            scope: tokens.scope,
            idToken: tokens.idToken,
          },
          metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at),
          lastRefreshed: row.last_refreshed ? new Date(row.last_refreshed) : undefined,
          refreshCount: row.refresh_count
        } as StoredSession;
      }
      return await this.mapSessionAsync(result.rows[0]);
    }
  }

  /**
   * Get all active sessions across all users
   */
  async getAllActiveSessions(): Promise<StoredSession[]> {
    if (this.db instanceof BunDatabase) {
      const stmt = this.db.prepare(`
        SELECT * FROM oauth_sessions
        WHERE revoked_at IS NULL
        ORDER BY updated_at DESC
      `);
      const rows = stmt.all() as any[];
      const mapped = await Promise.all(rows.map(row => this.mapSessionAsync(row)));
      return mapped;
    } else {
      // PostgreSQL
      const result = await this.db.query(`
        SELECT * FROM oauth_sessions
        WHERE revoked_at IS NULL
        ORDER BY updated_at DESC
      `);
      const mapped = await Promise.all(result.rows.map((row: any) => this.mapSessionAsync(row)));
      return mapped;
    }
  }

  /**
   * Lightweight listing of active sessions without decrypting tokens
   */
  async getAllActiveSessionSummaries(): Promise<Array<{ userId: number; provider: string }>> {
    if (this.db instanceof BunDatabase) {
      const stmt = this.db.prepare(`
        SELECT user_id as userId, provider
        FROM oauth_sessions
        WHERE revoked_at IS NULL
        ORDER BY updated_at DESC
      `);
      return stmt.all() as any[];
    } else {
      const result = await this.db.query(`
        SELECT user_id as "userId", provider
        FROM oauth_sessions
        WHERE revoked_at IS NULL
        ORDER BY updated_at DESC
      `);
      return result.rows as any[];
    }
  }

  /**
   * Retrieve and decrypt tokens for a user
   */
  async getTokens(userId: number, provider: string): Promise<StoredSession | null> {
    let session: any;

    if (this.db instanceof BunDatabase) {
      const stmt = this.db.prepare(`
        SELECT * FROM oauth_sessions
        WHERE user_id = ? AND provider = ? AND revoked_at IS NULL
        ORDER BY created_at DESC
        LIMIT 1
      `);
      session = stmt.get(userId, provider);
    } else {
      const result = await this.db.query(`
        SELECT * FROM oauth_sessions
        WHERE user_id = $1 AND provider = $2 AND revoked_at IS NULL
        ORDER BY created_at DESC
        LIMIT 1
      `, [userId, provider]);
      session = result.rows[0];
    }

    if (!session) {
      return null;
    }

    // Check if token is expired and needs refresh
    const expiresAt = new Date(session.expires_at);
    const now = new Date();
    const minutesUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60);

    if (minutesUntilExpiry < this.config.autoRefreshThreshold && session.refresh_token_encrypted) {
      // Token is about to expire, trigger refresh
      this.emit('token:expiring', { sessionId: session.session_id, provider });
    }

    return await this.mapSessionAsync(session);
  }

  /**
   * Retrieve and decrypt tokens by session ID
   */
  async getTokensBySessionId(sessionId: string): Promise<StoredSession | null> {
    let session: any;

    if (this.db instanceof BunDatabase) {
      const stmt = this.db.prepare(`
        SELECT * FROM oauth_sessions
        WHERE session_id = ? AND revoked_at IS NULL
        LIMIT 1
      `);
      session = stmt.get(sessionId);
    } else {
      const result = await this.db.query(`
        SELECT * FROM oauth_sessions
        WHERE session_id = $1 AND revoked_at IS NULL
        LIMIT 1
      `, [sessionId]);
      session = result.rows[0];
    }

    if (!session) {
      return null;
    }

    return await this.mapSessionAsync(session);
  }

  /**
   * Update tokens after refresh
   */
  async updateTokens(
    sessionId: string,
    newTokens: Partial<OAuthToken>
  ): Promise<void> {
    const updates: any = {
      updated_at: toSQLiteTimestamp(new Date()),
      last_refreshed: toSQLiteTimestamp(new Date())
    };

    const [encAccess, encRefresh] = await Promise.all([
      newTokens.accessToken ? encryptForStorageAsync(newTokens.accessToken) : Promise.resolve(null),
      newTokens.refreshToken ? encryptForStorageAsync(newTokens.refreshToken) : Promise.resolve(null),
    ]);
    if (encAccess) {
      updates.access_token_encrypted = JSON.stringify(encAccess);
    }
    if (encRefresh) {
      updates.refresh_token_encrypted = JSON.stringify(encRefresh);
    }
    if (newTokens.expiresAt) {
      updates.expires_at = toSQLiteTimestamp(newTokens.expiresAt);
    }
    if (newTokens.refreshExpiresAt) {
      updates.refresh_expires_at = toSQLiteTimestamp(newTokens.refreshExpiresAt);
    }

    if (this.db instanceof BunDatabase) {
      const setClause = Object.keys(updates)
        .map(key => `${key} = ?`)
        .join(', ');

      const stmt = this.db.prepare(`
        UPDATE oauth_sessions
        SET ${setClause}, refresh_count = refresh_count + 1
        WHERE session_id = ? AND revoked_at IS NULL
      `);

      const values = [...Object.values(updates), sessionId] as SQLQueryBindings[];
      stmt.run(...values);
    } else {
      const setClause = Object.keys(updates)
        .map((key, idx) => `${key} = $${idx + 1}`)
        .join(', ');

      await this.db.query(`
        UPDATE oauth_sessions
        SET ${setClause}, refresh_count = refresh_count + 1
        WHERE session_id = $${Object.keys(updates).length + 1} AND revoked_at IS NULL
      `, [...Object.values(updates), sessionId]);
    }

    // Log token rotation
    if (newTokens.accessToken) {
      await this.logTokenRotation(
        sessionId,
        hash('old_token'), // We don't store the actual old token
        hash(newTokens.accessToken),
        'refresh'
      );
    }

    // Reschedule auto-refresh
    if (newTokens.expiresAt) {
      this.scheduleTokenRefresh(sessionId, newTokens.expiresAt);
    }

    await this.logAudit({
      eventType: 'token_refreshed',
      sessionId,
      success: true
    });
  }

  /**
   * Revoke tokens for a session
   */
  async revokeTokens(sessionId: string, reason: string): Promise<void> {
    if (this.db instanceof BunDatabase) {
      this.db.prepare(`
        UPDATE oauth_sessions
        SET revoked_at = CURRENT_TIMESTAMP, revoke_reason = ?
        WHERE session_id = ? AND revoked_at IS NULL
      `).run(reason, sessionId);
    } else {
      await this.db.query(`
        UPDATE oauth_sessions
        SET revoked_at = CURRENT_TIMESTAMP, revoke_reason = $1
        WHERE session_id = $2 AND revoked_at IS NULL
      `, [reason, sessionId]);
    }

    // Cancel any scheduled refresh
    const timer = this.refreshTimers.get(sessionId);
    if (timer) {
      clearTimeout(timer);
      this.refreshTimers.delete(sessionId);
    }

    await this.logAudit({
      eventType: 'token_revoked',
      sessionId,
      success: true,
      metadata: { reason }
    });

    this.emit('token:revoked', { sessionId, reason });
  }

  /**
   * Log audit event
   */
  logAudit(event: AuditEvent): void | Promise<void> {
    const metadataJson = event.metadata ? JSON.stringify(event.metadata) : null;

    if (this.db instanceof BunDatabase) {
      // Synchronous for SQLite
      try {
        // Resolve/validate user_id to prevent FK violations
        let resolvedUserId: number | null = event.userId ?? null;

        if (resolvedUserId != null) {
          const exists = this.db.prepare('SELECT id FROM users WHERE id = ? LIMIT 1').get(resolvedUserId) as any;
          if (!exists) {
            resolvedUserId = null; // drop invalid FK
          }
        } else if (event.sessionId) {
          // If no userId provided, attempt to infer from session
          const sessionRow = this.db
            .prepare('SELECT user_id FROM oauth_sessions WHERE session_id = ? LIMIT 1')
            .get(event.sessionId) as any;
          if (sessionRow && typeof sessionRow.user_id === 'number') {
            resolvedUserId = sessionRow.user_id;
          }
        }

        this.db.prepare(`
          INSERT INTO audit_log (
            event_type, user_id, session_id, provider, ip_address,
            user_agent, success, error_message, metadata
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          event.eventType,
          resolvedUserId,
          event.sessionId ?? null,
          event.provider ?? null,
          event.ipAddress ?? null,
          event.userAgent ?? null,
          event.success ? 1 : 0,
          event.errorMessage ?? null,
          metadataJson
        );
      } catch (error) {
        console.error('Failed to log audit event:', error);
      }
      this.emit('audit:logged', event);
      return; // Synchronous return
    } else {
      // Async for PostgreSQL - return the promise
      const pg = this.db as Pool;
      return (async () => {
        // Resolve/validate user_id to prevent FK violations
        let resolvedUserId: number | null = event.userId ?? null;

        if (resolvedUserId != null) {
          const res = await pg.query('SELECT id FROM users WHERE id = $1 LIMIT 1', [resolvedUserId]);
          if (!res.rows || res.rows.length === 0) {
            resolvedUserId = null; // drop invalid FK
          }
        } else if (event.sessionId) {
          const r2 = await pg.query(
            'SELECT user_id FROM oauth_sessions WHERE session_id = $1 LIMIT 1',
            [event.sessionId]
          );
          const u = (r2.rows && r2.rows[0] && (r2.rows[0] as any).user_id) as number | undefined;
          if (typeof u === 'number') {
            resolvedUserId = u;
          }
        }

        try {
          await pg.query(
            `INSERT INTO audit_log (
              event_type, user_id, session_id, provider, ip_address,
              user_agent, success, error_message, metadata
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
            [
              event.eventType,
              resolvedUserId,
              event.sessionId ?? null,
              event.provider ?? null,
              event.ipAddress ?? null,
              event.userAgent ?? null,
              event.success,
              event.errorMessage ?? null,
              metadataJson
            ]
          );
        } catch (e: any) {
          if (e && e.code === '23503') {
            // FK violation: retry without user_id
            await pg.query(
              `INSERT INTO audit_log (
                event_type, user_id, session_id, provider, ip_address,
                user_agent, success, error_message, metadata
              ) VALUES ($1, NULL, $2, $3, $4, $5, $6, $7, $8)`,
              [
                event.eventType,
                event.sessionId ?? null,
                event.provider ?? null,
                event.ipAddress ?? null,
                event.userAgent ?? null,
                event.success,
                event.errorMessage ?? null,
                metadataJson
              ]
            );
          } else {
            throw e;
          }
        }

        this.emit('audit:logged', event);
      })();
    }
  }

  /**
   * Log token rotation for security tracking
   */
  private async logTokenRotation(
    sessionId: string,
    oldTokenHash: string,
    newTokenHash: string,
    reason: string
  ): Promise<void> {
    if (this.db instanceof BunDatabase) {
      this.db.prepare(`
        INSERT INTO token_rotation_history (
          session_id, old_token_hash, new_token_hash, rotation_reason
        ) VALUES (?, ?, ?, ?)
      `).run(sessionId, oldTokenHash, newTokenHash, reason);
    } else {
      await this.db.query(`
        INSERT INTO token_rotation_history (
          session_id, old_token_hash, new_token_hash, rotation_reason
        ) VALUES ($1, $2, $3, $4)
      `, [sessionId, oldTokenHash, newTokenHash, reason]);
    }
  }

  /**
   * Schedule automatic token refresh
   */
  private scheduleTokenRefresh(sessionId: string, expiresAt: Date): void {
    // Clear existing timer
    const existingTimer = this.refreshTimers.get(sessionId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const now = new Date();
    const refreshTime = new Date(expiresAt.getTime() - this.config.autoRefreshThreshold * 60 * 1000);

    if (refreshTime > now) {
      const timeout = refreshTime.getTime() - now.getTime();
      const timer = setTimeout(() => {
        this.emit('token:refresh-needed', { sessionId });
        this.refreshTimers.delete(sessionId);
      }, timeout);

      this.refreshTimers.set(sessionId, timer);
    }
  }

  /**
   * Clean up expired sessions and old audit logs
   */
  async cleanup(): Promise<void> {
    const auditCutoff = new Date();
    auditCutoff.setDate(auditCutoff.getDate() - this.config.auditRetentionDays);

    if (this.db instanceof BunDatabase) {
      // Clean expired sessions
      this.db.prepare(`
        DELETE FROM oauth_sessions
        WHERE expires_at < CURRENT_TIMESTAMP
        AND (refresh_expires_at IS NULL OR refresh_expires_at < CURRENT_TIMESTAMP)
        AND revoked_at IS NULL
      `).run();

      // Clean old audit logs
      this.db.prepare(`
        DELETE FROM audit_log
        WHERE created_at < ?
      `).run(toSQLiteTimestamp(auditCutoff));

      // Clean old failed attempts
      this.db.prepare(`
        DELETE FROM failed_auth_attempts
        WHERE attempted_at < datetime('now', '-24 hours')
      `).run();
    } else {
      await this.db.query(`
        DELETE FROM oauth_sessions
        WHERE expires_at < CURRENT_TIMESTAMP
        AND (refresh_expires_at IS NULL OR refresh_expires_at < CURRENT_TIMESTAMP)
        AND revoked_at IS NULL
      `);

      await this.db.query(`
        DELETE FROM audit_log
        WHERE created_at < $1
      `, [auditCutoff]);

      await this.db.query(`
        DELETE FROM failed_auth_attempts
        WHERE attempted_at < NOW() - INTERVAL '24 hours'
      `);
    }

    this.emit('cleanup:completed');
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(
      () => this.cleanup(),
      this.config.cleanupIntervalHours * 60 * 60 * 1000
    );
  }

  /**
   * Map database row to User object
   */
  private mapUser(row: any): User {
    return {
      id: row.id,
      externalId: row.external_id,
      email: row.email,
      name: row.name,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    };
  }

  /**
   * Map database row to StoredSession object
   */
  private async mapSessionAsync(row: any): Promise<StoredSession> {
    const accessToken = await decryptFromStorageAsync(JSON.parse(row.access_token_encrypted));
    const refreshToken = row.refresh_token_encrypted
      ? await decryptFromStorageAsync(JSON.parse(row.refresh_token_encrypted))
      : undefined;
    const idToken = row.id_token_encrypted
      ? await decryptFromStorageAsync(JSON.parse(row.id_token_encrypted))
      : undefined;

    return {
      id: row.id,
      userId: row.user_id,
      provider: row.provider,
      sessionId: row.session_id,
      tokens: {
        accessToken,
        refreshToken,
        tokenType: row.token_type,
        expiresAt: new Date(row.expires_at),
        refreshExpiresAt: row.refresh_expires_at ? new Date(row.refresh_expires_at) : undefined,
        scope: row.scope,
        idToken
      },
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      lastRefreshed: row.last_refreshed ? new Date(row.last_refreshed) : undefined,
      refreshCount: row.refresh_count
    };
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    let user: any;

    if (this.db instanceof BunDatabase) {
      user = this.db.prepare('SELECT * FROM users WHERE email = ?').get(email);
    } else {
      const result = await this.db.query('SELECT * FROM users WHERE email = $1', [email]);
      user = result.rows[0];
    }

    return user ? this.mapUser(user) : null;
  }

  /**
   * Get user by externalId
   */
  async getUserByExternalId(externalId: string): Promise<User | null> {
    let user: any;

    if (this.db instanceof BunDatabase) {
      user = this.db.prepare('SELECT * FROM users WHERE external_id = ?').get(externalId);
    } else {
      const result = await this.db.query('SELECT * FROM users WHERE external_id = $1', [externalId]);
      user = result.rows[0];
    }

    return user ? this.mapUser(user) : null;
  }

  /**
   * Get user by numeric ID
   */
  async getUserById(id: number): Promise<User | null> {
    let user: any;

    if (this.db instanceof BunDatabase) {
      user = this.db.prepare('SELECT * FROM users WHERE id = ?').get(id);
    } else {
      const result = await this.db.query('SELECT * FROM users WHERE id = $1', [id]);
      user = result.rows[0];
    }

    return user ? this.mapUser(user) : null;
  }

  /**
   * Check rate limiting for failed attempts
   */
  async checkRateLimit(identifier: string, attemptType: string): Promise<boolean> {
    const cutoff = new Date();
    cutoff.setMinutes(cutoff.getMinutes() - 15); // 15-minute window

    let count: number;

    if (this.db instanceof BunDatabase) {
      const result = this.db.prepare(`
        SELECT COUNT(*) as count FROM failed_auth_attempts
        WHERE identifier = ? AND attempt_type = ? AND attempted_at > ?
      `).get(identifier, attemptType, toSQLiteTimestamp(cutoff)) as any;
      count = result.count;
    } else {
      const result = await this.db.query(`
        SELECT COUNT(*) FROM failed_auth_attempts
        WHERE identifier = $1 AND attempt_type = $2 AND attempted_at > $3
      `, [identifier, attemptType, cutoff]);
      count = parseInt(result.rows[0].count);
    }

    // Allow max 5 attempts per 15 minutes
    return count < 5;
  }

  /**
   * Record failed authentication attempt
   */
  async recordFailedAttempt(
    identifier: string,
    attemptType: string,
    errorType: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    if (this.db instanceof BunDatabase) {
      this.db.prepare(`
        INSERT INTO failed_auth_attempts (
          identifier, attempt_type, ip_address, user_agent, error_type
        ) VALUES (?, ?, ?, ?, ?)
      `).run(identifier, attemptType, ipAddress ?? null, userAgent ?? null, errorType);
    } else {
      await this.db.query(`
        INSERT INTO failed_auth_attempts (
          identifier, attempt_type, ip_address, user_agent, error_type
        ) VALUES ($1, $2, $3, $4, $5)
      `, [identifier, attemptType, ipAddress ?? null, userAgent ?? null, errorType]);
    }
  }

  /**
   * Close database connections
   */
  async close(): Promise<void> {
    // Clear all timers
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    for (const timer of this.refreshTimers.values()) {
      clearTimeout(timer);
    }
    this.refreshTimers.clear();

    // Close database
    if (this.db instanceof BunDatabase) {
      this.db.close();
    } else {
      await this.db.end();
    }

    this.emit('closed');
  }

  /**
   * Get vault statistics
   */
  async getStatistics(): Promise<Record<string, any>> {
    const stats: any = {};

    if (this.db instanceof BunDatabase) {
      stats.totalUsers = (this.db.prepare('SELECT COUNT(*) as count FROM users').get() as any).count;
      stats.activeSessions = (this.db.prepare('SELECT COUNT(*) as count FROM oauth_sessions WHERE revoked_at IS NULL').get() as any).count;
      stats.totalAuditEvents = (this.db.prepare('SELECT COUNT(*) as count FROM audit_log').get() as any).count;

      const providers = this.db.prepare(`
        SELECT provider, COUNT(*) as count
        FROM oauth_sessions
        WHERE revoked_at IS NULL
        GROUP BY provider
      `).all() as any[];

      stats.sessionsByProvider = providers.reduce((acc, row) => {
        acc[row.provider] = row.count;
        return acc;
      }, {});
    } else {
      const userCount = await this.db.query('SELECT COUNT(*) FROM users');
      stats.totalUsers = parseInt(userCount.rows[0].count);

      const sessionCount = await this.db.query('SELECT COUNT(*) FROM oauth_sessions WHERE revoked_at IS NULL');
      stats.activeSessions = parseInt(sessionCount.rows[0].count);

      const auditCount = await this.db.query('SELECT COUNT(*) FROM audit_log');
      stats.totalAuditEvents = parseInt(auditCount.rows[0].count);

      const providers = await this.db.query(`
        SELECT provider, COUNT(*)
        FROM oauth_sessions
        WHERE revoked_at IS NULL
        GROUP BY provider
      `);

      stats.sessionsByProvider = providers.rows.reduce((acc, row) => {
        acc[row.provider] = parseInt(row.count);
        return acc;
      }, {});
    }

    return stats;
  }
}

// Export singleton instance for convenience
let vaultInstance: TokenVault | null = null;

export function initializeTokenVault(config: TokenVaultConfig): TokenVault {
  if (!vaultInstance) {
    vaultInstance = new TokenVault(config);
  }
  return vaultInstance;
}

export function getTokenVault(): TokenVault {
  if (!vaultInstance) {
    throw new Error('TokenVault not initialized. Call initializeTokenVault first.');
  }
  return vaultInstance;
}

// Export singleton instance for compatibility - will throw if not initialized
export let tokenVault: TokenVault;
