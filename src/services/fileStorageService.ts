import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs/promises';
import { config } from '../utils/config';
import type { FileMetadata } from '../types/fileTypes';
import { calculateChecksum } from '../utils/checksum';

const UPLOAD_DIR = config.fileStorage.localStoragePath || path.join(process.cwd(), 'uploads');

// Ensure upload directory exists
async function ensureUploadDir() {
  await fs.mkdir(UPLOAD_DIR, { recursive: true });
}

export type StoredRecord = {
  id: string;
  ownerId: string;
  metadata: FileMetadata;
  path: string;
  createdAt: string;
};

export interface UploadFileResult {
  id: string;
  filePath: string;
  metadata: FileMetadata;
}

export async function storeFile(
  fileBuffer: Buffer,
  originalName: string,
  mimeType: string,
  ownerId: string
): Promise<UploadFileResult> {
  await ensureUploadDir();

  const id = uuidv4();
  const extension = path.extname(originalName);
  const fileName = `${id}${extension}`;
  const filePath = path.join(UPLOAD_DIR, fileName);

  await fs.writeFile(filePath, fileBuffer);

  const stats = await fs.stat(filePath);
  const checksum = await calculateChecksum(filePath);

  const metadata: FileMetadata = {
    id,
    ownerId,
    originalName,
    fileName,
    mimeType,
    size: stats.size,
    checksum,
    uploadDate: new Date().toISOString(),
  };

  const record: StoredRecord = {
    id,
    ownerId,
    metadata,
    path: filePath,
    createdAt: new Date().toISOString(),
  };

  // Persist record for later retrieval
  await fs.writeFile(path.join(UPLOAD_DIR, `${id}.json`), JSON.stringify(record, null, 2));

  return { id, filePath, metadata };
}

export async function getFileRecord(fileId: string, requesterId: string): Promise<StoredRecord> {
  await ensureUploadDir();
  const metaPath = path.join(UPLOAD_DIR, `${fileId}.json`);
  const text = await fs.readFile(metaPath, 'utf8').catch(() => null);
  if (!text) throw Object.assign(new Error('File not found'), { status: 404 });
  const record = JSON.parse(text) as StoredRecord;
  if (record.ownerId !== requesterId) {
    throw Object.assign(new Error('Forbidden'), { status: 403 });
  }
  return record;
}

export async function deleteFile(fileId: string, requesterId: string): Promise<void> {
  const record = await getFileRecord(fileId, requesterId);
  try {
    await fs.unlink(record.path);
  } catch {}
  try {
    await fs.unlink(path.join(UPLOAD_DIR, `${fileId}.json`));
  } catch {}
}

export async function generateSignedUrl(fileId: string, expiresInSeconds = 300): Promise<string> {
  // For local storage, return the static URL served by express
  await ensureUploadDir();
  const metaPath = path.join(UPLOAD_DIR, `${fileId}.json`);
  const text = await fs.readFile(metaPath, 'utf8').catch(() => null);
  if (!text) throw Object.assign(new Error('File not found'), { status: 404 });
  const record = JSON.parse(text) as StoredRecord;
  // Basic pseudo-expiry token (not validated yet); placeholder for future secure download route
  const exp = Date.now() + expiresInSeconds * 1000;
  return `/uploads/${record.metadata.fileName}?exp=${exp}`;
}
