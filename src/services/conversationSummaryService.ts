import { config } from '../utils/config';
import { ConversationSummarizer } from '../agents/ConversationSummarizer';
import { getMessagesByConversationId } from '../repositories/messages';
import { updateConversationSummary } from '../repositories/conversations';

const summarizer = new ConversationSummarizer();

export async function refreshConversationSummary(conversationId: string) {
  if (!config.summarizer.enabled) return;

  try {
    const messages = await getMessagesByConversationId(conversationId);

    if (!Array.isArray(messages) || messages.length === 0) {
      // Gracefully handle empty conversations: write a placeholder and exit
      await updateConversationSummary(conversationId, 'No content available.');
      return;
    }

    const history = messages.map(m => `${m.role}: ${m.content}`);
    const summary = await summarizer.summarize(history, config.summarizer.model);
    const truncated = summary ? summary.slice(0, 500) : '';
    await updateConversationSummary(conversationId, truncated);
  } catch (err) {
    // Non-blocking: never fail the turn
    console.warn('Conversation summary refresh failed:', err);
  }
}
