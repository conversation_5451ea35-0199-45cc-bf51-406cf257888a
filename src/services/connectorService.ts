/**
 * Service for managing OpenAI Connectors and MCP integrations
 * Now uses the production-ready ResponsesAPIClient for all operations
 */

import {
  ConnectorId,
  OAuthToken,
  GOOGLE_CONNECTORS,
  ApprovalMode,
  MCPApprovalRequest
} from '../types/connectors';
import {
  ResponsesAPIClient,
  ProcessedResponse,
  McpTool
} from './responsesAPIClient';
import { validateUserId } from '../utils/userValidation';
import {
  getTokenVault,
  initializeTokenVault,
  TokenVault,
  OAuthToken as VaultOAuthToken
} from './tokenVault';
import { hash } from '../utils/crypto';

interface ConnectorRequest {
  connector_id: ConnectorId;
  oauth_token: string;
  input: string;
  require_approval?: ApprovalMode;
  allowed_tools?: string[];
  model?: string;
}

interface MCPServerRequest {
  server_url: string;
  server_label: string;
  server_description?: string;
  authorization?: string;
  input: string;
  require_approval?: ApprovalMode;
  allowed_tools?: string[];
  model?: string;
}

class ConnectorService {
  private responsesClient: ResponsesAPIClient;
  private tokenVault: TokenVault;
  private pendingApprovals: Map<string, MCPApprovalRequest> = new Map();
  private initializedConnections: Map<string, Set<ConnectorId>> = new Map();
  private connectorSessions: Map<string, string> = new Map(); // Maps "userId:connectorId" to sessionId

  constructor() {
    // Initialize the production-ready Responses API client
    this.responsesClient = new ResponsesAPIClient({
      enableLogging: process.env.NODE_ENV !== 'production',
      defaultModel: process.env.DEFAULT_MODEL || 'gpt-5',
      maxRetries: 3,
      timeout: 120000
    });

    // Initialize TokenVault for secure, persistent token storage
    try {
      this.tokenVault = getTokenVault();
    } catch {
      // Initialize if not already done
      this.tokenVault = initializeTokenVault({
        database: {
          type: 'sqlite',
          filename: process.env.TOKEN_DB_PATH || 'data/tokens.db'
        },
        autoRefreshThreshold: 5, // Refresh tokens 5 minutes before expiry
        maxRefreshAttempts: 3,
        auditRetentionDays: 90,
        enableAutoCleanup: true,
        cleanupIntervalHours: 24
      });
    }
  }

  /**
   * Store OAuth token for a connector
   */
  async storeOAuthToken(
    user: { id: string; email?: string } | string,
    connectorId: ConnectorId,
    token: OAuthToken
  ): Promise<void> {
    // Normalize user input to ensure we always have a valid external_id and email
    let externalId: string;
    let email: string;

    if (typeof user === 'string') {
      const val = user.trim();
      if (val.includes('@')) {
        externalId = val;
        email = val;
      } else {
        externalId = val;
        email = `${val}@local.invalid`;
      }
    } else {
      const id = (user.id || '').trim();
      const em = (user.email || '').trim();
      if (em) {
        email = em;
        externalId = id || em;
      } else if (id) {
        externalId = id;
        email = id.includes('@') ? id : `${id}@local.invalid`;
      } else {
        throw new Error('Invalid user: missing id and email');
      }
    }

    // Get or create user in TokenVault
    const vaultUser =
      (await this.tokenVault.getUserByEmail(email)) ||
      (await this.tokenVault.upsertUser(
        externalId,
        email,
        undefined,
        { connector: connectorId }
      ));

    // Convert to TokenVault format
    const vaultToken: VaultOAuthToken = {
      accessToken: token.access_token,
      refreshToken: token.refresh_token,
      tokenType: token.token_type,
      expiresAt: token.expires_at ? new Date(token.expires_at) : new Date(Date.now() + 3600000),
      refreshExpiresAt: token.refresh_expires_at ? new Date(token.refresh_expires_at) : undefined,
      scope: token.scope,
      idToken: token.id_token
    };

    // Store tokens securely in the vault
    const stateHash = hash(`${email}:${connectorId}:${Date.now()}`);
    await this.tokenVault.storeTokens(
      vaultUser.id,
      connectorId,
      vaultToken,
      stateHash,
      undefined,
      undefined,
      undefined,
      { connector: connectorId },
      { returnSession: false }
    );

    // Log audit event
    await this.tokenVault.logAudit({
      eventType: 'connector_token_stored',
      userId: vaultUser.id,
      provider: connectorId,
      success: true,
      metadata: { connector: connectorId }
    });

    // Track the connection in memory
    if (!this.initializedConnections.has(email)) {
      this.initializedConnections.set(email, new Set());
    }
    this.initializedConnections.get(email)?.add(connectorId);
  }

  /**
   * Get stored OAuth token for a connector
   */
  async getOAuthToken(user: { id: string; email: string }, connectorId: ConnectorId): Promise<OAuthToken | null> {
    // Get user from TokenVault
    const vaultUser = await this.tokenVault.getUserByEmail(user.email);
    if (!vaultUser) {
      return null;
    }

    // Get stored session from vault
    const session = await this.tokenVault.getTokens(vaultUser.id, connectorId);
    if (!session) {
      return null;
    }

    // Check if token is expired or about to expire (within 5 minutes)
    const now = new Date();
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

    if (session.tokens.expiresAt < fiveMinutesFromNow) {
      // Token expired or about to expire - attempt refresh
      console.log(`Token for ${connectorId} expired or expiring soon, attempting refresh...`);

      try {
        const refreshed = await this.refreshOAuthToken(user, connectorId);
        if (!refreshed) {
          return null;
        }
        return refreshed;
      } catch (error) {
        console.error(`Failed to refresh token for ${connectorId}:`, error);

        // Log audit event for failed refresh
        await this.tokenVault.logAudit({
          eventType: 'connector_token_refresh_failed',
          userId: vaultUser.id,
          sessionId: session.sessionId,
          provider: connectorId,
          success: false,
          metadata: { connector: connectorId, error: error instanceof Error ? error.message : String(error) }
        });

        return null;
      }
    }

    // Convert back to connector format
    return {
      access_token: session.tokens.accessToken,
      refresh_token: session.tokens.refreshToken,
      token_type: session.tokens.tokenType,
      expires_at: session.tokens.expiresAt.getTime(),
      refresh_expires_at: session.tokens.refreshExpiresAt?.getTime(),
      scope: session.tokens.scope || '',  // Provide empty string if scope is undefined
      id_token: session.tokens.idToken
    };
  }

  /**
   * Resolve the appropriate user and token for a connector request.
   */
  async getOAuthTokenForContext(
    userIdentifier: string | undefined,
    connectorId: ConnectorId
  ): Promise<{ user: { id: string; email: string } | null; token: OAuthToken | null }> {
    const user = await this.resolveUserForConnector(userIdentifier, connectorId);
    if (!user) {
      return { user: null, token: null };
    }

    const token = await this.getOAuthToken(user, connectorId);
    if (token) {
      return { user, token };
    }

    return { user, token: null };
  }

  /**
   * Perform provider-specific OAuth token refresh and persist results.
   */
  async refreshOAuthToken(user: { id: string; email: string }, connectorId: ConnectorId): Promise<OAuthToken | null> {
    try {
      const vaultUser = await this.tokenVault.getUserByEmail(user.email);
      if (!vaultUser) return null;
      const session = await this.tokenVault.getTokens(vaultUser.id, connectorId);
      if (!session || !session.tokens.refreshToken) return null;

      const providerKey = connectorId.replace('connector_', '');

      // Currently Gmail/Calendar/Drive are Google-based
      const googleProviders = new Set(['gmail', 'googlecalendar', 'googledrive']);
      if (googleProviders.has(providerKey)) {
        const result = await this.refreshGoogleAccessToken(session.tokens.refreshToken);
        const expiresAt = new Date(Date.now() + (result.expires_in || 3600) * 1000);

        await this.tokenVault.updateTokens(session.sessionId, {
          accessToken: result.access_token,
          refreshToken: result.refresh_token || session.tokens.refreshToken,
          expiresAt,
          tokenType: result.token_type || session.tokens.tokenType,
          scope: result.scope || session.tokens.scope
        });

        await this.tokenVault.logAudit({
          eventType: 'connector_token_refreshed',
          userId: vaultUser.id,
          sessionId: session.sessionId,
          provider: connectorId,
          success: true,
          metadata: { connector: connectorId }
        });

        return {
          access_token: result.access_token,
          refresh_token: result.refresh_token || session.tokens.refreshToken,
          token_type: result.token_type || session.tokens.tokenType,
          expires_at: expiresAt.getTime(),
          scope: result.scope || session.tokens.scope || ''
        };
      }

      // Unsupported provider refresh
      console.warn(`Refresh not implemented for provider ${connectorId}`);
      return null;
    } catch (error) {
      console.error('OAuth token refresh failed:', error);
      return null;
    }
  }

  /**
   * Attempt to resolve a connector user from arbitrary identifiers, falling back to the default connector user.
   */
  private async resolveUserForConnector(
    userIdentifier: string | undefined,
    connectorId: ConnectorId
  ): Promise<{ id: string; email: string } | null> {
    try {
      const candidate = userIdentifier?.trim();

      if (candidate && candidate !== 'default') {
        // Direct email lookup
        if (candidate.includes('@')) {
          const user = await this.tokenVault.getUserByEmail(candidate);
          if (user) {
            return { id: user.externalId || String(user.id), email: user.email };
          }
        }

        // External ID (e.g. Google profile id)
        const externalUser = await this.tokenVault.getUserByExternalId(candidate);
        if (externalUser) {
          return { id: externalUser.externalId || String(externalUser.id), email: externalUser.email };
        }

        // Numeric database user id
        const numericId = Number(candidate);
        if (!Number.isNaN(numericId)) {
          const byId = await this.tokenVault.getUserById(numericId);
          if (byId) {
            return { id: byId.externalId || String(byId.id), email: byId.email };
          }
        }
      }

      const fallback = await this.getDefaultUserForConnector(connectorId);
      if (fallback) {
        return fallback;
      }

      return null;
    } catch (error) {
      console.error('Failed to resolve connector user:', error);
      return null;
    }
  }

  /**
   * Google OAuth refresh implementation
   */
  private async refreshGoogleAccessToken(refreshToken: string): Promise<{
    access_token: string;
    expires_in: number;
    scope?: string;
    token_type?: string;
    refresh_token?: string;
  }> {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    if (!clientId || !clientSecret) {
      throw new Error('Missing GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET');
    }

    const params = new URLSearchParams();
    params.set('client_id', clientId);
    params.set('client_secret', clientSecret);
    params.set('grant_type', 'refresh_token');
    params.set('refresh_token', refreshToken);

    const resp = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params.toString()
    });

    if (!resp.ok) {
      const text = await resp.text();
      throw new Error(`Google token refresh failed: ${resp.status} ${text}`);
    }
    const data = await resp.json();
    if (!data.access_token) {
      throw new Error('No access_token in refresh response');
    }
    return data;
  }

  /**
   * Retrieve connector session info for a user
   */
  async getConnectorSession(user: { id: string; email: string }, connectorId: ConnectorId): Promise<{
    connected: boolean;
    tokenExpiresAt?: Date;
    lastConnected?: Date;
  }> {
    const vaultUser = await this.tokenVault.getUserByEmail(user.email);
    if (!vaultUser) return { connected: false };
    const session = await this.tokenVault.getTokens(vaultUser.id, connectorId);
    if (!session) return { connected: false };
    return {
      connected: true,
      tokenExpiresAt: session.tokens.expiresAt,
      lastConnected: session.lastRefreshed || session.updatedAt || session.createdAt
    };
  }

  /**
   * Execute a connector request using the production-ready Responses API
   */
  async executeConnectorRequest(request: ConnectorRequest): Promise<ProcessedResponse> {
    try {
      // Get user for this connector (for session management)
      const user = await this.getDefaultUserForConnector(request.connector_id);
      const sessionMapKey = `${user ? user.email : 'default'}:${request.connector_id}`;

      // Reuse existing session or create new one
      let sessionId = this.connectorSessions.get(sessionMapKey);
      let isNewSession = false;

      if (!sessionId || !this.responsesClient.getSession(sessionId)) {
        // Create new session only if none exists or previous one expired
        sessionId = this.responsesClient.createSession(`${request.connector_id}_${user ? user.email : 'default'}`);
        this.connectorSessions.set(sessionMapKey, sessionId);
        isNewSession = true;
        console.log(`Created new session for ${request.connector_id}: ${sessionId}`);
      } else {
        console.log(`Reusing existing session for ${request.connector_id}: ${sessionId}`);
      }

      // Store OAuth token if provided and needed
      const normalizedRequestToken = request.oauth_token
        ? request.oauth_token.startsWith('Bearer ')
          ? request.oauth_token
          : `Bearer ${request.oauth_token}`
        : undefined;

      if (normalizedRequestToken) {
        const session = this.responsesClient.getSession(sessionId);
        const hasToken = session?.oauth_tokens?.has(request.connector_id);

        if (isNewSession || !hasToken) {
          const token: OAuthToken = {
            access_token: normalizedRequestToken,
            token_type: 'Bearer',
            scope: this.getConnectorScopes(request.connector_id).join(' ')
          };
          this.responsesClient.storeOAuthToken(sessionId, request.connector_id, token);
          console.log(`Stored OAuth token for ${request.connector_id} in session ${sessionId}`);
        }
      }

      // Get OAuth token if not provided in request, ensuring session reuse works
      let oauthToken = normalizedRequestToken;
      if (!oauthToken) {
        if (user) {
          const storedToken = await this.getOAuthToken(user, request.connector_id);
          if (storedToken) {
            oauthToken = storedToken.access_token.startsWith('Bearer ')
              ? storedToken.access_token
              : `Bearer ${storedToken.access_token}`;
            console.log(`Retrieved token for ${request.connector_id} from vault for session reuse`);
          }
        }
      }

      // Execute the connector request
      const response = await this.responsesClient.executeConnectorRequest({
        sessionId,
        connector_id: request.connector_id,
        oauth_token: oauthToken,
        input: request.input,
        require_approval: request.require_approval || 'always',
        allowed_tools: request.allowed_tools,
        model: request.model || 'gpt-5',
        stream: false
      });

      // Store any pending approvals
      if (response.approval_requests.length > 0) {
        for (const approval of response.approval_requests) {
          this.pendingApprovals.set(approval.id, {
            id: approval.id,
            type: 'mcp_approval_request',
            arguments: JSON.stringify(approval.arguments),
            name: approval.name,
            server_label: approval.server_label
          });
        }
      }

      return response;
    } catch (error) {
      console.error('Connector request failed:', error);
      throw error;
    }
  }

  /**
   * Execute a remote MCP server request
   */
  async executeMCPServerRequest(request: MCPServerRequest): Promise<ProcessedResponse> {
    try {
      // Create session for this MCP server request
      const sessionKey = `mcp_${request.server_label}_${Date.now()}`;
      const sessionId = this.responsesClient.createSession(sessionKey);

      // Execute the MCP server request
      const response = await this.responsesClient.executeMCPServerRequest({
        sessionId,
        server_url: request.server_url,
        server_label: request.server_label,
        server_description: request.server_description,
        authorization: request.authorization,
        input: request.input,
        require_approval: request.require_approval || 'always',
        allowed_tools: request.allowed_tools,
        model: request.model || 'gpt-5',
        stream: false
      });

      // Store any pending approvals
      if (response.approval_requests.length > 0) {
        for (const approval of response.approval_requests) {
          this.pendingApprovals.set(approval.id, {
            id: approval.id,
            type: 'mcp_approval_request',
            arguments: JSON.stringify(approval.arguments),
            name: approval.name,
            server_label: approval.server_label
          });
        }
      }

      return response;
    } catch (error) {
      console.error('MCP server request failed:', error);
      throw error;
    }
  }

  /**
   * Handle approval for MCP tool calls
   */
  async handleApproval(
    sessionId: string,
    approvalRequestId: string,
    approve: boolean,
    tool?: McpTool
  ): Promise<ProcessedResponse> {
    try {
      // Handle the approval using the ResponsesAPIClient
      const response = await this.responsesClient.handleApproval({
        sessionId,
        approval_request_id: approvalRequestId,
        approve,
        tool
      });

      // Remove from pending approvals if approved
      if (approve) {
        this.pendingApprovals.delete(approvalRequestId);
      }

      return response;
    } catch (error) {
      console.error('Approval handling failed:', error);
      throw error;
    }
  }

  /**
   * Get connector scopes based on connector ID
   */
  private getConnectorScopes(connectorId: ConnectorId): string[] {
    const connectorKey = connectorId.replace('connector_', '');

    if (connectorKey === 'gmail') {
      return GOOGLE_CONNECTORS.gmail.scopes;
    } else if (connectorKey === 'googlecalendar') {
      return GOOGLE_CONNECTORS.calendar.scopes;
    } else if (connectorKey === 'googledrive') {
      return GOOGLE_CONNECTORS.drive.scopes;
    }

    // Default scopes for other connectors
    return [];
  }

  /**
   * Get the server label for a connector
   */
  private getServerLabel(connectorId: ConnectorId): string {
    const labels: Record<ConnectorId, string> = {
      'connector_gmail': 'Gmail',
      'connector_googlecalendar': 'Google Calendar',
      'connector_googledrive': 'Google Drive',
      'connector_dropbox': 'Dropbox',
      'connector_microsoftteams': 'Microsoft Teams',
      'connector_outlookcalendar': 'Outlook Calendar',
      'connector_outlookemail': 'Outlook Email',
      'connector_sharepoint': 'SharePoint'
    };

    return labels[connectorId] || connectorId;
  }

  /**
   * Get available tools for a connector
   */
  getConnectorTools(connectorId: ConnectorId): string[] {
    return this.responsesClient.getConnectorTools(connectorId);
  }

  /**
   * Validate connector configuration
   */
  validateConnector(connectorId: ConnectorId, oauthToken?: string): { valid: boolean; errors: string[] } {
    return this.responsesClient.validateConnectorConfig(connectorId, oauthToken);
  }

  /**
   * Clear all sessions (for cleanup during shutdown)
   * WARNING: This clears ALL pending approvals and ResponsesAPI sessions.
   * For user-specific token revocation, use revokeTokens() instead.
   *
   * This method should ONLY be called during application shutdown or in tests.
   */
  async clearSessions(): Promise<void> {
    // Clear pending approvals
    this.pendingApprovals.clear();

    // Clear ResponsesAPI sessions
    this.responsesClient.clearAllSessions();

    // Note: TokenVault sessions are persistent and should not be cleared
    // unless explicitly revoking tokens. Use revokeTokens method instead.
  }

  /**
   * Revoke tokens for a specific session
   */
  async revokeTokens(userId: string, connectorId: ConnectorId, reason: string = 'user_requested'): Promise<void> {
    const validatedUserId = validateUserId(userId, `${connectorId} token revocation`);

    // Get user from TokenVault
    const user = await this.tokenVault.getUserByEmail(validatedUserId);
    if (!user) {
      return; // No tokens to revoke
    }

    // Get stored session
    const session = await this.tokenVault.getTokens(user.id, connectorId);
    if (session) {
      await this.tokenVault.revokeTokens(session.sessionId, reason);
    }
  }

  /**
   * Get pending approvals
   */
  getPendingApprovals(): Map<string, MCPApprovalRequest> {
    return this.pendingApprovals;
  }

  /**
   * Initialize and load existing OAuth connections on startup
   */
  async initializeConnections(): Promise<void> {
    try {
      console.log('Loading existing OAuth connections...');

      // Get all active sessions from the vault (lightweight, no decryption)
      const summaries = await this.tokenVault.getAllActiveSessionSummaries();

      for (const summary of summaries) {
        const userId = summary.userId.toString();
        const connectorId = summary.provider as ConnectorId;

        if (!this.initializedConnections.has(userId)) {
          this.initializedConnections.set(userId, new Set());
        }

        this.initializedConnections.get(userId)?.add(connectorId);
        console.log(`Loaded existing connection: ${connectorId} for user ${userId}`);
      }

      console.log(`Loaded ${summaries.length} existing OAuth connections`);

      // Prewarm Responses API sessions for existing connections to avoid
      // first-call latency and tool import delays during user requests
      await this.prewarmConnectorSessions(summaries).catch(err => {
        console.warn('Prewarm of connector sessions encountered an issue:', err);
      });
    } catch (error) {
      console.error('Failed to initialize OAuth connections:', error);
    }
  }

  /**
   * Pre-create ResponsesAPI sessions and store OAuth tokens for each active connector
   * Optionally trigger a lightweight tool import so the first user request is fast.
   */
  private async prewarmConnectorSessions(
    summaries?: Array<{ userId: number; provider: string }>
  ): Promise<void> {
    const list = summaries || await this.tokenVault.getAllActiveSessionSummaries();
    // Limit concurrent prewarms to avoid stampeding the API
    const concurrency = 2;
    const queue = [...list];
    const workers: Promise<void>[] = [];

    const work = async () => {
      while (queue.length > 0) {
        const item = queue.shift();
        if (!item) break;
        const connectorId = item.provider as ConnectorId;
        try {
          const user = await this.tokenVault.getUserById(item.userId);
          if (!user) continue;
          const tokens = await this.tokenVault.getTokens(item.userId, connectorId);
          if (!tokens) continue;

          const sessionKey = `${connectorId}_${user.email}`;
          let sessionId = this.connectorSessions.get(`${user.email}:${connectorId}`);
          if (!sessionId || !this.responsesClient.getSession(sessionId)) {
            sessionId = this.responsesClient.createSession(sessionKey);
            this.connectorSessions.set(`${user.email}:${connectorId}`, sessionId);
          }

          // Store OAuth token if not already stored
          const session = this.responsesClient.getSession(sessionId);
          const existing = session?.oauth_tokens?.get(connectorId);
          if (!existing) {
            const token: OAuthToken = {
              access_token: tokens.tokens.accessToken,
              token_type: tokens.tokens.tokenType || 'Bearer',
              scope: this.getConnectorScopes(connectorId).join(' ')
            };
            this.responsesClient.storeOAuthToken(sessionId, connectorId, token);
          }

          // Optional: trigger a lightweight tool import to prime connector tools
          try {
            await this.responsesClient.executeConnectorRequest({
              sessionId,
              connector_id: connectorId,
              oauth_token: tokens.tokens.accessToken,
              input: 'List available tools for this connector only. Do not call any tools.',
              require_approval: 'never',
              model: this.responsesClient ? undefined : 'gpt-5',
              stream: false
            });
          } catch (importErr) {
            // Ignore import errors during prewarm; will import on first real call
            console.warn(`Prewarm tool import failed for ${connectorId}:`, importErr instanceof Error ? importErr.message : importErr);
          }
        } catch (err) {
          console.warn('Prewarm task error:', err instanceof Error ? err.message : err);
        }
      }
    };

    for (let i = 0; i < concurrency; i++) {
      workers.push(work());
    }
    await Promise.all(workers);
    console.log('Prewarm of connector sessions complete');
  }

  /**
   * Check if a connector is already connected for a user
   */
  isConnected(userId: string, connectorId: ConnectorId): boolean {
    const validatedUserId = validateUserId(userId, `${connectorId} connection check`);
    return this.initializedConnections.get(validatedUserId)?.has(connectorId) ?? false;
  }

  /**
   * Get all connected services for a user
   */
  getConnectedServices(userId: string): ConnectorId[] {
    const validatedUserId = validateUserId(userId, 'get connected services');
    const connections = this.initializedConnections.get(validatedUserId);
    return connections ? Array.from(connections) : [];
  }

  /**
   /**
    * Get a default user object for a given connector if available.
    * Returns the only connected user if unique; otherwise returns the first found.
    */
   async getDefaultUserForConnector(connectorId: ConnectorId): Promise<{ id: string; email: string } | null> {
     try {
       // Use summaries to avoid decryption overhead
       const summaries = await this.tokenVault.getAllActiveSessionSummaries();
       const matching = summaries.filter(s => s.provider === connectorId);
       if (matching.length === 0) return null;

       // Prefer the most recent by occurrence order (already ordered by updated_at desc)
       // Prefer the most recent by occurrence order (already ordered by updated_at desc)
       const chosen = matching;
       const user = await this.tokenVault.getUserById(chosen[0].userId);
       if (!user) return null;
       return { id: user.id.toString(), email: user.email };
     } catch (error) {
       console.error('Failed to get default user for connector:', error);
       return null;
     }
   }
  /**
   * Check if a specific user has any connected services
   * @param userId The user ID to check
   * @returns True if the user has at least one connected service
   */
  async hasUserConnections(userId: string): Promise<boolean> {
    try {
      const validatedUserId = validateUserId(userId, 'check user connections');
      const connections = this.initializedConnections.get(validatedUserId);
      return connections ? connections.size > 0 : false;
    } catch (error) {
      console.error('Failed to check user connections:', error);
      return false;
    }
  }

  /**
   * Execute batch connector requests
   */
  async executeBatchRequests(requests: Array<{
    connector_id?: ConnectorId;
    server_url?: string;
    server_label: string;
    authorization?: string;
    input: string;
    require_approval?: ApprovalMode;
  }>): Promise<ProcessedResponse[]> {
    const batchRequests = requests.map(req => ({
      tool: {
        type: 'mcp' as const,
        server_label: req.server_label,
        connector_id: req.connector_id,
        server_url: req.server_url,
        authorization: req.authorization,
        require_approval: req.require_approval || 'always'
      },
      input: req.input
    }));

    return this.responsesClient.executeBatchRequests({
      requests: batchRequests
    });
  }
}

export const connectorService = new ConnectorService();
