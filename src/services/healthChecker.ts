import { EventEmitter } from 'events';
import { z } from 'zod';
import schedule from 'node-schedule';
import Redis from 'ioredis';
import { performance } from 'perf_hooks';
import dns from 'dns/promises';
import https from 'https';
import http from 'http';

// Health check schemas
const HealthCheckConfigSchema = z.object({
  connectorId: z.string(),
  checkInterval: z.number().min(10000).default(60000), // minimum 10 seconds
  timeout: z.number().min(1000).default(10000), // 10 second default timeout
  retries: z.number().min(0).default(2),
  checks: z.array(z.enum([
    'connectivity',
    'authentication',
    'rateLimit',
    'quota',
    'performance',
    'dataIntegrity',
    'dependencies'
  ])).default(['connectivity', 'authentication']),
  thresholds: z.object({
    latency: z.number().default(1000), // ms
    successRate: z.number().min(0).max(100).default(95), // percentage
    errorRate: z.number().min(0).max(100).default(5) // percentage
  }).optional(),
  endpoints: z.array(z.object({
    url: z.string().url(),
    method: z.enum(['GET', 'POST', 'HEAD']).default('GET'),
    expectedStatus: z.number().default(200),
    headers: z.record(z.string(), z.string()).optional()
  })).optional()
});

const HealthStatusSchema = z.object({
  connectorId: z.string(),
  status: z.enum(['healthy', 'degraded', 'unhealthy', 'unknown']),
  lastCheck: z.date(),
  uptime: z.number(), // seconds
  checks: z.record(z.string(), z.object({
    status: z.enum(['pass', 'fail', 'warning']),
    message: z.string().optional(),
    latency: z.number().optional(),
    details: z.any().optional()
  })),
  metrics: z.object({
    avgLatency: z.number(),
    successRate: z.number(),
    errorCount: z.number(),
    totalChecks: z.number()
  }),
  issues: z.array(z.object({
    type: z.string(),
    severity: z.enum(['low', 'medium', 'high', 'critical']),
    message: z.string(),
    timestamp: z.date(),
    resolved: z.boolean()
  }))
});

const DiagnosticReportSchema = z.object({
  connectorId: z.string(),
  timestamp: z.date(),
  overallHealth: z.enum(['healthy', 'degraded', 'unhealthy']),
  summary: z.string(),
  checks: z.array(z.object({
    name: z.string(),
    result: z.enum(['pass', 'fail', 'warning']),
    duration: z.number(),
    details: z.string()
  })),
  recommendations: z.array(z.string()),
  performanceMetrics: z.object({
    avgResponseTime: z.number(),
    p95ResponseTime: z.number(),
    p99ResponseTime: z.number(),
    throughput: z.number(),
    errorRate: z.number()
  }),
  configuration: z.object({
    current: z.record(z.string(), z.any()),
    recommended: z.record(z.string(), z.any()),
    issues: z.array(z.string())
  })
});

type HealthCheckConfig = z.infer<typeof HealthCheckConfigSchema>;
type HealthStatus = z.infer<typeof HealthStatusSchema>;
type DiagnosticReport = z.infer<typeof DiagnosticReportSchema>;

interface CheckResult {
  status: 'pass' | 'fail' | 'warning';
  message?: string;
  latency?: number;
  details?: any;
}

export interface HealthCheckerConfig {
  redis?: Redis;
  enableAutoHealing?: boolean;
  notificationWebhook?: string;
  statusPageUrl?: string;
  benchmarkIterations?: number;
}

interface ConnectorInterface {
  id: string;
  healthCheck?: () => Promise<boolean>;
  validateToken?: () => Promise<boolean>;
  testConnection?: () => Promise<boolean>;
  getMetrics?: () => Promise<any>;
}

export class HealthChecker extends EventEmitter {
  private redis?: Redis;
  private configs: Map<string, HealthCheckConfig> = new Map();
  private statuses: Map<string, HealthStatus> = new Map();
  private connectors: Map<string, ConnectorInterface> = new Map();
  private jobs: Map<string, schedule.Job> = new Map();
  private metrics: Map<string, number[]> = new Map();
  private uptimeTracking: Map<string, number> = new Map();
  private autoHealing: boolean;
  private notificationWebhook?: string;
  private statusPageUrl?: string;
  private benchmarkIterations: number;

  constructor(config: HealthCheckerConfig = {}) {
    super();
    this.redis = config.redis;
    this.autoHealing = config.enableAutoHealing ?? false;
    this.notificationWebhook = config.notificationWebhook;
    this.statusPageUrl = config.statusPageUrl;
    this.benchmarkIterations = config.benchmarkIterations || 10;

    this.loadConfigurations();
    this.startUptimeTracking();
  }

  private async loadConfigurations(): Promise<void> {
    if (this.redis) {
      try {
        const configKeys = await this.redis.keys('health:config:*');
        for (const key of configKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const config = JSON.parse(data);
            const connectorId = key.replace('health:config:', '');
            this.configs.set(connectorId, config);
            this.scheduleHealthCheck(connectorId, config);
          }
        }

        // Load previous statuses
        const statusKeys = await this.redis.keys('health:status:*');
        for (const key of statusKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const status = JSON.parse(data);
            status.lastCheck = new Date(status.lastCheck);
            const connectorId = key.replace('health:status:', '');
            this.statuses.set(connectorId, status);
          }
        }
      } catch (error) {
        console.error('Error loading health configurations:', error);
      }
    }
  }

  private startUptimeTracking(): void {
    setInterval(() => {
      this.statuses.forEach((status, connectorId) => {
        const uptime = this.uptimeTracking.get(connectorId) || 0;
        if (status.status === 'healthy') {
          this.uptimeTracking.set(connectorId, uptime + 1);
        }
      });
    }, 1000);
  }

  public registerConnector(connector: ConnectorInterface): void {
    this.connectors.set(connector.id, connector);

    // Initialize health status
    if (!this.statuses.has(connector.id)) {
      this.statuses.set(connector.id, {
        connectorId: connector.id,
        status: 'unknown',
        lastCheck: new Date(),
        uptime: 0,
        checks: {},
        metrics: {
          avgLatency: 0,
          successRate: 100,
          errorCount: 0,
          totalChecks: 0
        },
        issues: []
      });
    }
  }

  public configureHealthCheck(config: HealthCheckConfig): void {
    this.configs.set(config.connectorId, config);

    // Cancel existing job if any
    const existingJob = this.jobs.get(config.connectorId);
    if (existingJob) {
      existingJob.cancel();
    }

    // Schedule new health check
    this.scheduleHealthCheck(config.connectorId, config);

    // Persist configuration
    if (this.redis) {
      this.redis.set(
        `health:config:${config.connectorId}`,
        JSON.stringify(config)
      );
    }
  }

  private scheduleHealthCheck(connectorId: string, config: HealthCheckConfig): void {
    const rule = new schedule.RecurrenceRule();
    rule.second = new schedule.Range(0, 59, Math.floor(config.checkInterval / 1000));

    const job = schedule.scheduleJob(rule, async () => {
      await this.performHealthCheck(connectorId);
    });

    this.jobs.set(connectorId, job);
  }

  public async performHealthCheck(connectorId: string): Promise<HealthStatus> {
    const config = this.configs.get(connectorId);
    const connector = this.connectors.get(connectorId);

    if (!config) {
      throw new Error(`No health check configuration for connector ${connectorId}`);
    }

    const status = this.statuses.get(connectorId) || this.createDefaultStatus(connectorId);
    const checkResults: Record<string, any> = {};
    const startTime = performance.now();

    // Perform configured checks
    for (const checkType of config.checks) {
      try {
        const result = await this.performCheck(checkType, connector, config);
        checkResults[checkType] = result;
      } catch (error: any) {
        checkResults[checkType] = {
          status: 'fail',
          message: error.message,
          latency: performance.now() - startTime
        };
      }
    }

    // Perform endpoint checks
    if (config.endpoints) {
      for (const endpoint of config.endpoints) {
        const endpointResult = await this.checkEndpoint(endpoint, config.timeout);
        checkResults[`endpoint:${endpoint.url}`] = endpointResult;
      }
    }

    // Calculate overall status
    const failedChecks = Object.values(checkResults).filter(r => r.status === 'fail').length;
    const warningChecks = Object.values(checkResults).filter(r => r.status === 'warning').length;
    const totalChecks = Object.keys(checkResults).length;

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    if (failedChecks === 0 && warningChecks === 0) {
      overallStatus = 'healthy';
    } else if (failedChecks > totalChecks / 2) {
      overallStatus = 'unhealthy';
    } else {
      overallStatus = 'degraded';
    }

    // Update metrics
    const latency = performance.now() - startTime;
    this.updateMetrics(connectorId, latency, overallStatus === 'healthy');

    // Update status
    status.status = overallStatus;
    status.lastCheck = new Date();
    status.checks = checkResults;
    status.uptime = this.uptimeTracking.get(connectorId) || 0;
    status.metrics = this.calculateMetrics(connectorId);

    // Check for issues and attempt auto-healing
    if (overallStatus !== 'healthy') {
      await this.handleUnhealthyStatus(connectorId, status, checkResults);
    }

    // Persist status
    await this.persistStatus(connectorId, status);

    // Emit status update
    this.emit('health-check-complete', status);

    return status;
  }

  private async performCheck(
    checkType: string,
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    switch (checkType) {
      case 'connectivity':
        return await this.checkConnectivity(connector, config);

      case 'authentication':
        return await this.checkAuthentication(connector, config);

      case 'rateLimit':
        return await this.checkRateLimit(connector, config);

      case 'quota':
        return await this.checkQuota(connector, config);

      case 'performance':
        return await this.checkPerformance(connector, config);

      case 'dataIntegrity':
        return await this.checkDataIntegrity(connector, config);

      case 'dependencies':
        return await this.checkDependencies(connector, config);

      default:
        return {
          status: 'warning',
          message: `Unknown check type: ${checkType}`,
          latency: performance.now() - startTime
        };
    }
  }

  private async checkConnectivity(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    try {
      if (connector?.testConnection) {
        const result = await Promise.race([
          connector.testConnection(),
          new Promise<boolean>((_, reject) =>
            setTimeout(() => reject(new Error('Connection timeout')), config.timeout)
          )
        ]);

        return {
          status: result ? 'pass' : 'fail',
          message: result ? 'Connection successful' : 'Connection failed',
          latency: performance.now() - startTime
        };
      }

      // Fallback to DNS check
      const endpoints = config.endpoints || [];
      for (const endpoint of endpoints) {
        const url = new URL(endpoint.url);
        await dns.lookup(url.hostname);
      }

      return {
        status: 'pass',
        message: 'DNS resolution successful',
        latency: performance.now() - startTime
      };
    } catch (error: any) {
      return {
        status: 'fail',
        message: `Connectivity check failed: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkAuthentication(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    try {
      if (connector?.validateToken) {
        const valid = await connector.validateToken();

        return {
          status: valid ? 'pass' : 'fail',
          message: valid ? 'Authentication valid' : 'Authentication failed',
          latency: performance.now() - startTime
        };
      }

      return {
        status: 'warning',
        message: 'Authentication check not implemented',
        latency: performance.now() - startTime
      };
    } catch (error: any) {
      return {
        status: 'fail',
        message: `Authentication check failed: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkRateLimit(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    try {
      if (connector?.getMetrics) {
        const metrics = await connector.getMetrics();
        const rateLimit = metrics.rateLimit || {};

        const remaining = rateLimit.remaining || Infinity;
        const limit = rateLimit.limit || Infinity;
        const percentUsed = limit === Infinity ? 0 : ((limit - remaining) / limit) * 100;

        let status: 'pass' | 'warning' | 'fail';
        if (percentUsed < 80) {
          status = 'pass';
        } else if (percentUsed < 95) {
          status = 'warning';
        } else {
          status = 'fail';
        }

        return {
          status,
          message: `Rate limit ${percentUsed.toFixed(1)}% used`,
          latency: performance.now() - startTime,
          details: { remaining, limit, percentUsed }
        };
      }

      return {
        status: 'pass',
        message: 'Rate limit check skipped',
        latency: performance.now() - startTime
      };
    } catch (error: any) {
      return {
        status: 'warning',
        message: `Rate limit check error: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkQuota(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    try {
      if (connector?.getMetrics) {
        const metrics = await connector.getMetrics();
        const quota = metrics.quota || {};

        const used = quota.used || 0;
        const limit = quota.limit || Infinity;
        const percentUsed = limit === Infinity ? 0 : (used / limit) * 100;

        let status: 'pass' | 'warning' | 'fail';
        if (percentUsed < 80) {
          status = 'pass';
        } else if (percentUsed < 95) {
          status = 'warning';
        } else {
          status = 'fail';
        }

        return {
          status,
          message: `Quota ${percentUsed.toFixed(1)}% used`,
          latency: performance.now() - startTime,
          details: { used, limit, percentUsed }
        };
      }

      return {
        status: 'pass',
        message: 'Quota check skipped',
        latency: performance.now() - startTime
      };
    } catch (error: any) {
      return {
        status: 'warning',
        message: `Quota check error: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkPerformance(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();
    const latencies: number[] = [];

    try {
      // Run performance benchmark
      for (let i = 0; i < this.benchmarkIterations; i++) {
        const iterStart = performance.now();

        if (connector?.healthCheck) {
          await connector.healthCheck();
        } else if (config.endpoints && config.endpoints.length > 0) {
          await this.checkEndpoint(config.endpoints[0], config.timeout);
        }

        latencies.push(performance.now() - iterStart);
      }

      const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      const threshold = config.thresholds?.latency || 1000;

      let status: 'pass' | 'warning' | 'fail';
      if (avgLatency < threshold) {
        status = 'pass';
      } else if (avgLatency < threshold * 1.5) {
        status = 'warning';
      } else {
        status = 'fail';
      }

      return {
        status,
        message: `Average latency: ${avgLatency.toFixed(2)}ms`,
        latency: performance.now() - startTime,
        details: {
          avgLatency,
          minLatency: Math.min(...latencies),
          maxLatency: Math.max(...latencies),
          p95: this.calculatePercentile(latencies, 95),
          p99: this.calculatePercentile(latencies, 99)
        }
      };
    } catch (error: any) {
      return {
        status: 'fail',
        message: `Performance check failed: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkDataIntegrity(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();

    try {
      // Implement data integrity checks
      // This would typically involve:
      // 1. Fetching sample data
      // 2. Validating schema
      // 3. Checking for required fields
      // 4. Verifying data consistency

      return {
        status: 'pass',
        message: 'Data integrity check passed',
        latency: performance.now() - startTime
      };
    } catch (error: any) {
      return {
        status: 'fail',
        message: `Data integrity check failed: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkDependencies(
    connector: ConnectorInterface | undefined,
    config: HealthCheckConfig
  ): Promise<any> {
    const startTime = performance.now();
    const dependencies: any[] = [];

    try {
      // Check external dependencies
      // This would check things like:
      // - Database connections
      // - Cache servers
      // - External APIs
      // - Message queues

      const allHealthy = dependencies.every(dep => dep.healthy);

      return {
        status: allHealthy ? 'pass' : 'fail',
        message: allHealthy ? 'All dependencies healthy' : 'Some dependencies unhealthy',
        latency: performance.now() - startTime,
        details: dependencies
      };
    } catch (error: any) {
      return {
        status: 'fail',
        message: `Dependency check failed: ${error.message}`,
        latency: performance.now() - startTime
      };
    }
  }

  private async checkEndpoint(
    endpoint: any,
    timeout: number
  ): Promise<any> {
    const startTime = performance.now();

    return new Promise((resolve) => {
      const url = new URL(endpoint.url);
      const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path: url.pathname + url.search,
        method: endpoint.method || 'GET',
        headers: endpoint.headers || {},
        timeout
      };

      const protocol = url.protocol === 'https:' ? https : http;

      const req = protocol.request(options, (res) => {
        const latency = performance.now() - startTime;
        const status = res.statusCode === endpoint.expectedStatus ? 'pass' : 'fail';

        resolve({
          status,
          message: `Endpoint returned ${res.statusCode}`,
          latency,
          details: {
            statusCode: res.statusCode,
            expectedStatus: endpoint.expectedStatus
          }
        });
      });

      req.on('error', (error) => {
        resolve({
          status: 'fail',
          message: `Endpoint check failed: ${error.message}`,
          latency: performance.now() - startTime
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          status: 'fail',
          message: 'Endpoint check timed out',
          latency: performance.now() - startTime
        });
      });

      req.end();
    });
  }

  private async handleUnhealthyStatus(
    connectorId: string,
    status: HealthStatus,
    checkResults: Record<string, any>
  ): Promise<void> {
    // Add issue to status
    const issue = {
      type: 'health_degradation',
      severity: status.status === 'unhealthy' ? 'critical' as const : 'high' as const,
      message: `Connector ${status.status}: ${Object.entries(checkResults)
        .filter(([_, r]) => r.status === 'fail')
        .map(([check, _]) => check)
        .join(', ')} failed`,
      timestamp: new Date(),
      resolved: false
    };

    status.issues.push(issue);

    // Keep only last 100 issues
    if (status.issues.length > 100) {
      status.issues = status.issues.slice(-100);
    }

    // Send notification
    await this.sendHealthNotification(connectorId, status);

    // Attempt auto-healing if enabled
    if (this.autoHealing) {
      await this.attemptAutoHealing(connectorId, checkResults);
    }

    // Update status page
    if (this.statusPageUrl) {
      await this.updateStatusPage(connectorId, status);
    }
  }

  private async attemptAutoHealing(
    connectorId: string,
    checkResults: Record<string, any>
  ): Promise<void> {
    const connector = this.connectors.get(connectorId);

    if (!connector) return;

    // Implement auto-healing strategies based on failure type
    const healingStrategies: Record<string, () => Promise<void>> = {
      authentication: async () => {
        // Attempt to refresh authentication tokens
        this.emit('auto-healing', {
          connectorId,
          action: 'refresh-auth',
          timestamp: new Date()
        });
      },
      connectivity: async () => {
        // Attempt to reconnect
        this.emit('auto-healing', {
          connectorId,
          action: 'reconnect',
          timestamp: new Date()
        });
      },
      rateLimit: async () => {
        // Back off and retry
        this.emit('auto-healing', {
          connectorId,
          action: 'rate-limit-backoff',
          timestamp: new Date()
        });
      }
    };

    for (const [check, result] of Object.entries(checkResults)) {
      if (result.status === 'fail' && healingStrategies[check]) {
        try {
          await healingStrategies[check]();

          // Re-run health check after healing attempt
          setTimeout(() => {
            this.performHealthCheck(connectorId);
          }, 5000);
        } catch (error) {
          console.error(`Auto-healing failed for ${connectorId}:${check}`, error);
        }
      }
    }
  }

  public async generateDiagnosticReport(connectorId: string): Promise<DiagnosticReport> {
    const status = this.statuses.get(connectorId);
    const config = this.configs.get(connectorId);

    if (!status || !config) {
      throw new Error(`No health data available for connector ${connectorId}`);
    }

    // Run comprehensive diagnostics
    await this.performHealthCheck(connectorId);

    const checks: any[] = [];
    const recommendations: string[] = [];

    // Analyze check results
    // Analyze check results
    for (const [checkName, resultRaw] of Object.entries(status.checks)) {
      const result = resultRaw as CheckResult;
      checks.push({
        name: checkName,
        result: result.status,
        duration: result.latency || 0,
        details: result.message || ''
      });

      // Generate recommendations
      if (result.status === 'fail') {
        recommendations.push(this.generateRecommendation(checkName, result));
      }
    }
    // Analyze performance metrics
    const metrics = this.metrics.get(connectorId) || [];
    const performanceMetrics = {
      avgResponseTime: metrics.length > 0
        ? metrics.reduce((a, b) => a + b, 0) / metrics.length : 0,
      p95ResponseTime: this.calculatePercentile(metrics, 95),
      p99ResponseTime: this.calculatePercentile(metrics, 99),
      throughput: status.metrics.totalChecks / ((Date.now() - status.lastCheck.getTime()) / 1000),
      errorRate: status.metrics.errorCount / status.metrics.totalChecks * 100
    };

    // Analyze configuration
    const configurationAnalysis = {
      current: config as any,
      recommended: this.generateRecommendedConfig(config, status),
      issues: this.identifyConfigurationIssues(config, status)
    };

    const report: DiagnosticReport = {
      connectorId,
      timestamp: new Date(),
      overallHealth: status.status === 'healthy' ? 'healthy' :
                    status.status === 'degraded' ? 'degraded' : 'unhealthy',
      summary: this.generateSummary(status, checks),
      checks,
      recommendations,
      performanceMetrics,
      configuration: configurationAnalysis
    };

    // Persist report
    if (this.redis) {
      await this.redis.set(
        `diagnostic:${connectorId}:${Date.now()}`,
        JSON.stringify(report),
        'EX',
        2592000 // 30 days
      );
    }

    return report;
  }

  private generateRecommendation(checkName: string, result: CheckResult): string {
    const recommendations: Record<string, string> = {
      connectivity: 'Check network configuration and firewall rules',
      authentication: 'Verify API credentials and refresh tokens if necessary',
      rateLimit: 'Reduce request frequency or upgrade API plan',
      quota: 'Monitor usage patterns and consider quota increase',
      performance: 'Optimize request handling and consider caching',
      dataIntegrity: 'Review data validation rules and sync processes',
      dependencies: 'Check external service status and failover configuration'
    };

    return recommendations[checkName] || `Investigate ${checkName} failure: ${result.message}`;
  }

  private generateRecommendedConfig(config: HealthCheckConfig, status: HealthStatus): any {
    const recommended = { ...config };

    // Adjust check interval based on stability
    if (status.status === 'unhealthy') {
      recommended.checkInterval = Math.max(10000, config.checkInterval / 2);
    }

    // Add more checks if issues detected
    if (status.issues.length > 0) {
      recommended.checks = [...new Set([...config.checks, 'dependencies', 'dataIntegrity'])] as typeof config.checks;
    }

    // Adjust thresholds based on metrics
    if (status.metrics.avgLatency > 1000) {
      recommended.thresholds = {
        ...config.thresholds,
        latency: status.metrics.avgLatency * 1.2,
        successRate: config.thresholds?.successRate ?? 95,
        errorRate: config.thresholds?.errorRate ?? 5
      };
    }

    return recommended;
  }

  private identifyConfigurationIssues(config: HealthCheckConfig, status: HealthStatus): string[] {
    const issues: string[] = [];

    if (config.checkInterval > 300000) {
      issues.push('Check interval too long (>5 minutes)');
    }

    if (config.timeout < 5000) {
      issues.push('Timeout too short (<5 seconds)');
    }

    if (!config.checks.includes('authentication')) {
      issues.push('Authentication check not configured');
    }

    if (status.metrics.errorCount > 10 && config.retries < 3) {
      issues.push('Consider increasing retry count');
    }

    return issues;
  }

  private generateSummary(status: HealthStatus, checks: any[]): string {
    const failedChecks = checks.filter(c => c.result === 'fail').map(c => c.name);
    const warningChecks = checks.filter(c => c.result === 'warning').map(c => c.name);

    let summary = `Connector ${status.connectorId} is ${status.status}. `;

    if (failedChecks.length > 0) {
      summary += `Failed checks: ${failedChecks.join(', ')}. `;
    }

    if (warningChecks.length > 0) {
      summary += `Warning checks: ${warningChecks.join(', ')}. `;
    }

    summary += `Uptime: ${(status.uptime / 3600).toFixed(2)} hours. `;
    summary += `Success rate: ${status.metrics.successRate.toFixed(1)}%.`;

    return summary;
  }

  private updateMetrics(connectorId: string, latency: number, success: boolean): void {
    const metrics = this.metrics.get(connectorId) || [];
    metrics.push(latency);

    // Keep only last 1000 measurements
    if (metrics.length > 1000) {
      metrics.shift();
    }

    this.metrics.set(connectorId, metrics);

    // Update status metrics
    const status = this.statuses.get(connectorId);
    if (status) {
      status.metrics.totalChecks++;
      if (!success) {
        status.metrics.errorCount++;
      }
      status.metrics.avgLatency = metrics.reduce((a, b) => a + b, 0) / metrics.length;
      status.metrics.successRate = ((status.metrics.totalChecks - status.metrics.errorCount) /
                                    status.metrics.totalChecks) * 100;
    }
  }

  private calculateMetrics(connectorId: string): any {
    const status = this.statuses.get(connectorId);
    const metrics = this.metrics.get(connectorId) || [];

    return {
      avgLatency: metrics.length > 0 ?
        metrics.reduce((a, b) => a + b, 0) / metrics.length : 0,
      successRate: status?.metrics.successRate || 100,
      errorCount: status?.metrics.errorCount || 0,
      totalChecks: status?.metrics.totalChecks || 0
    };
  }

  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;

    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.floor((percentile / 100) * sorted.length);
    return sorted[Math.min(index, sorted.length - 1)];
  }

  private createDefaultStatus(connectorId: string): HealthStatus {
    return {
      connectorId,
      status: 'unknown',
      lastCheck: new Date(),
      uptime: 0,
      checks: {},
      metrics: {
        avgLatency: 0,
        successRate: 100,
        errorCount: 0,
        totalChecks: 0
      },
      issues: []
    };
  }

  private async persistStatus(connectorId: string, status: HealthStatus): Promise<void> {
    if (this.redis) {
      await this.redis.set(
        `health:status:${connectorId}`,
        JSON.stringify(status),
        'EX',
        86400 // 24 hours
      );
    }
  }

  private async sendHealthNotification(connectorId: string, status: HealthStatus): Promise<void> {
    if (!this.notificationWebhook) return;

    const notification = {
      type: 'health-alert',
      connectorId,
      status: status.status,
      message: `Connector ${connectorId} is ${status.status}`,
      checks: status.checks,
      timestamp: new Date().toISOString()
    };

    try {
      await fetch(this.notificationWebhook, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(notification)
      });
    } catch (error) {
      console.error('Failed to send health notification:', error);
    }
  }

  private async updateStatusPage(connectorId: string, status: HealthStatus): Promise<void> {
    if (!this.statusPageUrl) return;

    try {
      await fetch(`${this.statusPageUrl}/api/components/${connectorId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: status.status === 'healthy' ? 'operational' :
                 status.status === 'degraded' ? 'degraded_performance' :
                 'major_outage'
        })
      });
    } catch (error) {
      console.error('Failed to update status page:', error);
    }
  }

  public getStatus(connectorId: string): HealthStatus | undefined {
    return this.statuses.get(connectorId);
  }

  public getAllStatuses(): Map<string, HealthStatus> {
    return new Map(this.statuses);
  }

  public async shutdown(): Promise<void> {
    // Cancel all scheduled jobs
    this.jobs.forEach(job => job.cancel());
    this.jobs.clear();

    // Clear intervals
    // Note: In production, store interval IDs and clear them here
  }
}

// Export singleton instance
export const healthChecker = new HealthChecker();
