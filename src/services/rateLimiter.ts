import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { z } from 'zod';
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';

// Rate limiting schemas
const RateLimitConfigSchema = z.object({
  windowMs: z.number().min(1000), // Time window in milliseconds
  maxRequests: z.number().min(1), // Max requests per window
  skipSuccessfulRequests: z.boolean().default(false),
  skipFailedRequests: z.boolean().default(false)
});

const QuotaConfigSchema = z.object({
  daily: z.number().optional(),
  weekly: z.number().optional(),
  monthly: z.number().optional(),
  tokens: z.number().optional(), // Token limit per period
  cost: z.number().optional(), // Cost limit in cents
  requests: z.number().optional() // Request count limit
});

const TokenBucketConfigSchema = z.object({
  capacity: z.number().min(1), // Maximum tokens in bucket
  refillRate: z.number().min(0), // Tokens per second
  initialTokens: z.number().min(0).optional() // Starting tokens
});

const UsageMetricsSchema = z.object({
  userId: z.string(),
  connectorId: z.string(),
  timestamp: z.date(),
  requests: z.number(),
  tokens: z.number(),
  cost: z.number(), // in cents
  errors: z.number(),
  latency: z.number() // average in ms
});

type RateLimitConfig = z.infer<typeof RateLimitConfigSchema> & {
  keyGenerator?: (req: any) => string;
};
type QuotaConfig = z.infer<typeof QuotaConfigSchema>;
type TokenBucketConfig = z.infer<typeof TokenBucketConfigSchema>;
type UsageMetrics = z.infer<typeof UsageMetricsSchema>;

export interface RateLimiterConfig {
  redis?: Redis;
  enableDistributed?: boolean;
  alertThreshold?: number; // Percentage (0-100)
  gracefulDegradation?: boolean;
  costPerToken?: number; // Cost in cents per token
  notificationWebhook?: string;
}

interface TokenBucket {
  tokens: number;
  lastRefill: number;
  capacity: number;
  refillRate: number;
}

interface UserQuota {
  userId: string;
  connectorId: string;
  period: 'daily' | 'weekly' | 'monthly';
  used: {
    requests: number;
    tokens: number;
    cost: number;
  };
  limit: QuotaConfig;
  resetAt: Date;
}

export class RateLimiter extends EventEmitter {
  private redis?: Redis;
  private localRedis?: Redis;
  private rateLimits: Map<string, RateLimitConfig> = new Map();
  private quotas: Map<string, QuotaConfig> = new Map();
  private tokenBuckets: Map<string, TokenBucket> = new Map();
  private usageMetrics: UsageMetrics[] = [];
  private alertThreshold: number;
  private gracefulDegradation: boolean;
  private costPerToken: number;
  private notificationWebhook?: string;
  private alertsSent: Set<string> = new Set();

  constructor(config: RateLimiterConfig = {}) {
    super();
    this.redis = config.redis;
    this.alertThreshold = config.alertThreshold || 80;
    this.gracefulDegradation = config.gracefulDegradation ?? true;
    this.costPerToken = config.costPerToken || 0.002; // $0.00002 per token default
    this.notificationWebhook = config.notificationWebhook;

    if (config.enableDistributed && !this.redis) {
      // Create a local Redis instance for distributed rate limiting
      this.localRedis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        db: parseInt(process.env.REDIS_DB || '0'),
        retryStrategy: (times) => Math.min(times * 50, 2000)
      });
      this.redis = this.localRedis;
    }

    this.setupPeriodicTasks();
    this.loadConfiguration();
  }

  private async loadConfiguration(): Promise<void> {
    if (this.redis) {
      try {
        // Load rate limit configs
        const rateLimitKeys = await this.redis.keys('ratelimit:config:*');
        for (const key of rateLimitKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const [, , identifier] = key.split(':');
            this.rateLimits.set(identifier, JSON.parse(data));
          }
        }

        // Load quota configs
        const quotaKeys = await this.redis.keys('quota:config:*');
        for (const key of quotaKeys) {
          const data = await this.redis.get(key);
          if (data) {
            const [, , identifier] = key.split(':');
            this.quotas.set(identifier, JSON.parse(data));
          }
        }
      } catch (error) {
        console.error('Error loading rate limiter configuration:', error);
      }
    }
  }

  private setupPeriodicTasks(): void {
    // Reset daily quotas
    setInterval(() => {
      this.resetQuotas('daily');
    }, 24 * 60 * 60 * 1000);

    // Reset weekly quotas
    setInterval(() => {
      this.resetQuotas('weekly');
    }, 7 * 24 * 60 * 60 * 1000);

    // Reset monthly quotas (approximate)
    setInterval(() => {
      this.resetQuotas('monthly');
    }, 30 * 24 * 60 * 60 * 1000);

    // Aggregate and persist metrics every minute
    setInterval(() => {
      this.aggregateMetrics();
    }, 60 * 1000);
  }

  // Sliding window rate limiting
  public async checkRateLimit(
    identifier: string,
    weight: number = 1
  ): Promise<{ allowed: boolean; remaining: number; resetAt: Date }> {
    const config = this.rateLimits.get(identifier);

    if (!config) {
      return { allowed: true, remaining: Infinity, resetAt: new Date() };
    }

    const now = Date.now();
    const windowStart = now - config.windowMs;
    const key = `ratelimit:${identifier}:${Math.floor(now / config.windowMs)}`;

    if (this.redis) {
      // Distributed rate limiting with Redis
      const pipeline = this.redis.pipeline();

      // Remove old entries
      pipeline.zremrangebyscore(key, '-inf', windowStart.toString());

      // Count current requests in window
      pipeline.zcard(key);

      // Add current request
      pipeline.zadd(key, now.toString(), `${now}:${Math.random()}`);

      // Set expiry
      pipeline.expire(key, Math.ceil(config.windowMs / 1000));

      const results = await pipeline.exec();

      if (results) {
        const count = (results[1]?.[1] as number) || 0;
        const allowed = count + weight <= config.maxRequests;

        if (!allowed && this.gracefulDegradation) {
          // Apply graceful degradation
          this.emit('rate-limit-exceeded', { identifier, count, limit: config.maxRequests });
          await this.applyGracefulDegradation(identifier);
        }

        return {
          allowed,
          remaining: Math.max(0, config.maxRequests - count - weight),
          resetAt: new Date(now + config.windowMs)
        };
      }
    } else {
      // Local rate limiting (fallback)
      const requests = this.getLocalRequests(identifier, windowStart, now);
      const count = requests.length;
      const allowed = count + weight <= config.maxRequests;

      if (allowed) {
        this.addLocalRequest(identifier, now);
      }

      return {
        allowed,
        remaining: Math.max(0, config.maxRequests - count - weight),
        resetAt: new Date(now + config.windowMs)
      };
    }

    return { allowed: true, remaining: Infinity, resetAt: new Date() };
  }

  // Token bucket algorithm for burst handling
  public async consumeTokens(
    identifier: string,
    tokens: number = 1
  ): Promise<{ allowed: boolean; remaining: number; refillAt: Date }> {
    const bucketKey = `bucket:${identifier}`;
    let bucket = this.tokenBuckets.get(bucketKey);

    if (!bucket) {
      // Initialize bucket with default config
      bucket = {
        tokens: 100,
        lastRefill: Date.now(),
        capacity: 100,
        refillRate: 10 // 10 tokens per second
      };
      this.tokenBuckets.set(bucketKey, bucket);
    }

    const now = Date.now();
    const timePassed = (now - bucket.lastRefill) / 1000; // seconds
    const tokensToAdd = timePassed * bucket.refillRate;

    // Refill bucket
    bucket.tokens = Math.min(bucket.capacity, bucket.tokens + tokensToAdd);
    bucket.lastRefill = now;

    // Check if we can consume tokens
    if (bucket.tokens >= tokens) {
      bucket.tokens -= tokens;

      if (this.redis) {
        // Persist to Redis
        await this.redis.set(
          bucketKey,
          JSON.stringify(bucket),
          'EX',
          3600 // 1 hour expiry
        );
      }

      return {
        allowed: true,
        remaining: Math.floor(bucket.tokens),
        refillAt: new Date(now + (1000 / bucket.refillRate))
      };
    }

    return {
      allowed: false,
      remaining: Math.floor(bucket.tokens),
      refillAt: new Date(now + ((tokens - bucket.tokens) / bucket.refillRate) * 1000)
    };
  }

  // Quota management
  public async checkQuota(
    userId: string,
    connectorId: string,
    usage: { requests?: number; tokens?: number; cost?: number }
  ): Promise<{ allowed: boolean; remaining: QuotaConfig; resetAt: Date }> {
    const quotaKey = `${userId}:${connectorId}`;
    const config = this.quotas.get(quotaKey) || this.quotas.get(connectorId);

    if (!config) {
      return {
        allowed: true,
        remaining: {},
        resetAt: new Date()
      };
    }

    const periods: Array<'daily' | 'weekly' | 'monthly'> = ['daily', 'weekly', 'monthly'];
    let allowed = true;
    const remaining: Record<string, { requests: number; tokens: number; cost: number }> = {};
    let nearestReset = new Date(Date.now() + 24 * 60 * 60 * 1000);

    for (const period of periods) {
      const limit = config[period];
      if (!limit) continue;

      const quota = await this.getUserQuota(userId, connectorId, period);
      const wouldExceed =
        (quota.used.requests + (usage.requests || 0) > (quota.limit.requests || Infinity)) ||
        (quota.used.tokens + (usage.tokens || 0) > (quota.limit.tokens || Infinity)) ||
        (quota.used.cost + (usage.cost || 0) > (quota.limit.cost || Infinity));

      if (wouldExceed) {
        allowed = false;
      }

      // Check alert threshold
      const percentUsed = Math.max(
        (quota.used.requests / (quota.limit.requests || 1)) * 100,
        (quota.used.tokens / (quota.limit.tokens || 1)) * 100,
        (quota.used.cost / (quota.limit.cost || 1)) * 100
      );

      if (percentUsed >= this.alertThreshold) {
        await this.sendQuotaAlert(userId, connectorId, period, percentUsed);
      }

      remaining[period] = {
        requests: Math.max(0, (quota.limit.requests || 0) - quota.used.requests),
        tokens: Math.max(0, (quota.limit.tokens || 0) - quota.used.tokens),
        cost: Math.max(0, (quota.limit.cost || 0) - quota.used.cost)
      };

      if (quota.resetAt < nearestReset) {
        nearestReset = quota.resetAt;
      }
    }

    return { allowed, remaining, resetAt: nearestReset };
  }

  public async recordUsage(
    userId: string,
    connectorId: string,
    usage: { requests?: number; tokens?: number; latency?: number; error?: boolean }
  ): Promise<void> {
    const cost = (usage.tokens || 0) * this.costPerToken;

    // Update metrics
    const metric: UsageMetrics = {
      userId,
      connectorId,
      timestamp: new Date(),
      requests: usage.requests || 1,
      tokens: usage.tokens || 0,
      cost,
      errors: usage.error ? 1 : 0,
      latency: usage.latency || 0
    };

    this.usageMetrics.push(metric);

    // Update quotas
    const periods: Array<'daily' | 'weekly' | 'monthly'> = ['daily', 'weekly', 'monthly'];
    for (const period of periods) {
      await this.updateUserQuota(userId, connectorId, period, {
        requests: usage.requests || 1,
        tokens: usage.tokens || 0,
        cost
      });
    }

    // Emit usage event
    this.emit('usage-recorded', metric);
  }

  private async getUserQuota(
    userId: string,
    connectorId: string,
    period: 'daily' | 'weekly' | 'monthly'
  ): Promise<UserQuota> {
    const key = `quota:${userId}:${connectorId}:${period}`;
    const resetAt = this.getResetTime(period);

    if (this.redis) {
      const data = await this.redis.get(key);
      if (data) {
        const quota = JSON.parse(data);
        quota.resetAt = new Date(quota.resetAt);
        return quota;
      }
    }

    // Return default quota
    const config = this.quotas.get(`${userId}:${connectorId}`) || this.quotas.get(connectorId) || {};
    return {
      userId,
      connectorId,
      period,
      used: { requests: 0, tokens: 0, cost: 0 },
      limit: config,
      resetAt
    };
  }

  private async updateUserQuota(
    userId: string,
    connectorId: string,
    period: 'daily' | 'weekly' | 'monthly',
    usage: { requests: number; tokens: number; cost: number }
  ): Promise<void> {
    const quota = await this.getUserQuota(userId, connectorId, period);

    quota.used.requests += usage.requests;
    quota.used.tokens += usage.tokens;
    quota.used.cost += usage.cost;

    if (this.redis) {
      const key = `quota:${userId}:${connectorId}:${period}`;
      const ttl = Math.floor((quota.resetAt.getTime() - Date.now()) / 1000);
      await this.redis.set(key, JSON.stringify(quota), 'EX', ttl);
    }
  }

  private getResetTime(period: 'daily' | 'weekly' | 'monthly'): Date {
    const now = new Date();
    const reset = new Date();

    switch (period) {
      case 'daily':
        reset.setDate(now.getDate() + 1);
        reset.setHours(0, 0, 0, 0);
        break;
      case 'weekly':
        reset.setDate(now.getDate() + (7 - now.getDay()));
        reset.setHours(0, 0, 0, 0);
        break;
      case 'monthly':
        reset.setMonth(now.getMonth() + 1);
        reset.setDate(1);
        reset.setHours(0, 0, 0, 0);
        break;
    }

    return reset;
  }

  private async resetQuotas(period: 'daily' | 'weekly' | 'monthly'): Promise<void> {
    if (this.redis) {
      const pattern = `quota:*:*:${period}`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    }

    this.emit('quotas-reset', { period });
  }

  private async sendQuotaAlert(
    userId: string,
    connectorId: string,
    period: string,
    percentUsed: number
  ): Promise<void> {
    const alertKey = `${userId}:${connectorId}:${period}`;

    // Avoid sending duplicate alerts
    if (this.alertsSent.has(alertKey)) {
      return;
    }

    this.alertsSent.add(alertKey);

    // Clear alert after period resets
    setTimeout(() => {
      this.alertsSent.delete(alertKey);
    }, this.getResetTime(period as any).getTime() - Date.now());

    const alert = {
      type: 'quota-warning',
      userId,
      connectorId,
      period,
      percentUsed,
      timestamp: new Date().toISOString()
    };

    this.emit('quota-alert', alert);

    if (this.notificationWebhook) {
      try {
        await fetch(this.notificationWebhook, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alert)
        });
      } catch (error) {
        console.error('Failed to send quota alert webhook:', error);
      }
    }
  }

  private async applyGracefulDegradation(identifier: string): Promise<void> {
    // Implement graceful degradation strategies
    const strategies = [
      'reduce-features',
      'increase-cache-ttl',
      'disable-real-time',
      'queue-requests'
    ];

    const strategy = strategies[Math.floor(Math.random() * strategies.length)];

    this.emit('degradation-applied', {
      identifier,
      strategy,
      timestamp: new Date()
    });

    // Store degradation state
    if (this.redis) {
      await this.redis.set(
        `degradation:${identifier}`,
        strategy,
        'EX',
        300 // 5 minutes
      );
    }
  }

  private aggregateMetrics(): void {
    if (this.usageMetrics.length === 0) return;

    const aggregated = new Map<string, UsageMetrics>();

    for (const metric of this.usageMetrics) {
      const key = `${metric.userId}:${metric.connectorId}`;
      const existing = aggregated.get(key);

      if (existing) {
        existing.requests += metric.requests;
        existing.tokens += metric.tokens;
        existing.cost += metric.cost;
        existing.errors += metric.errors;
        existing.latency = (existing.latency + metric.latency) / 2;
      } else {
        aggregated.set(key, { ...metric });
      }
    }

    // Persist aggregated metrics
    if (this.redis) {
      const pipeline = this.redis.pipeline();
      const timestamp = Date.now();

      aggregated.forEach((metric, key) => {
        pipeline.zadd(
          `metrics:${key}`,
          timestamp.toString(),
          JSON.stringify(metric)
        );
        pipeline.expire(`metrics:${key}`, 2592000); // 30 days
      });

      pipeline.exec().catch(error => {
        console.error('Failed to persist metrics:', error);
      });
    }

    // Clear local metrics
    this.usageMetrics = [];
  }

  private localRequestCache = new Map<string, number[]>();

  private getLocalRequests(identifier: string, windowStart: number, now: number): number[] {
    const requests = this.localRequestCache.get(identifier) || [];
    const validRequests = requests.filter(time => time >= windowStart && time <= now);
    this.localRequestCache.set(identifier, validRequests);
    return validRequests;
  }

  private addLocalRequest(identifier: string, timestamp: number): void {
    const requests = this.localRequestCache.get(identifier) || [];
    requests.push(timestamp);
    this.localRequestCache.set(identifier, requests);
  }

  // Express middleware factory
  public createMiddleware(identifier: string): any {
    if (this.redis) {
      return rateLimit({
        store: new RedisStore({
          sendCommand: (...args: string[]) => (this.redis as any).sendCommand(args),
          prefix: `middleware:${identifier}:`
        }),
        windowMs: 60 * 1000, // 1 minute default
        max: 100, // 100 requests per minute default
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
          res.status(429).json({
            error: 'Too many requests',
            retryAfter: res.getHeader('Retry-After')
          });
        }
      });
    }

    // Fallback to memory store
    return rateLimit({
      windowMs: 60 * 1000,
      max: 100,
      standardHeaders: true,
      legacyHeaders: false
    });
  }

  // Configuration methods
  public setRateLimit(identifier: string, config: RateLimitConfig): void {
    this.rateLimits.set(identifier, config);

    if (this.redis) {
      this.redis.set(
        `ratelimit:config:${identifier}`,
        JSON.stringify(config)
      );
    }
  }

  public setQuota(identifier: string, config: QuotaConfig): void {
    this.quotas.set(identifier, config);

    if (this.redis) {
      this.redis.set(
        `quota:config:${identifier}`,
        JSON.stringify(config)
      );
    }
  }

  public setTokenBucket(identifier: string, config: TokenBucketConfig): void {
    const bucket: TokenBucket = {
      tokens: config.initialTokens || config.capacity,
      lastRefill: Date.now(),
      capacity: config.capacity,
      refillRate: config.refillRate
    };

    this.tokenBuckets.set(`bucket:${identifier}`, bucket);
  }

  // Analytics methods
  public async getUsageReport(
    userId?: string,
    connectorId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    averageLatency: number;
    errorRate: number;
    timeline: UsageMetrics[];
  }> {
    const start = startDate?.getTime() || Date.now() - 24 * 60 * 60 * 1000;
    const end = endDate?.getTime() || Date.now();

    let metrics: UsageMetrics[] = [];

    if (this.redis) {
      const pattern = userId && connectorId
        ? `metrics:${userId}:${connectorId}`
        : userId
        ? `metrics:${userId}:*`
        : connectorId
        ? `metrics:*:${connectorId}`
        : 'metrics:*';

      const keys = await this.redis.keys(pattern);

      for (const key of keys) {
        const data = await this.redis.zrangebyscore(
          key,
          start.toString(),
          end.toString()
        );

        for (const item of data) {
          metrics.push(JSON.parse(item));
        }
      }
    }

    // Add current metrics
    metrics = metrics.concat(this.usageMetrics);

    // Filter by date range
    metrics = metrics.filter(m => {
      const time = new Date(m.timestamp).getTime();
      return time >= start && time <= end;
    });

    // Calculate aggregates
    const totalRequests = metrics.reduce((sum, m) => sum + m.requests, 0);
    const totalTokens = metrics.reduce((sum, m) => sum + m.tokens, 0);
    const totalCost = metrics.reduce((sum, m) => sum + m.cost, 0);
    const totalLatency = metrics.reduce((sum, m) => sum + m.latency, 0);
    const totalErrors = metrics.reduce((sum, m) => sum + m.errors, 0);

    return {
      totalRequests,
      totalTokens,
      totalCost,
      averageLatency: totalRequests > 0 ? totalLatency / totalRequests : 0,
      errorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
      timeline: metrics.sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
    };
  }

  public async getCostBreakdown(
    userId: string,
    period: 'daily' | 'weekly' | 'monthly' = 'monthly'
  ): Promise<Map<string, number>> {
    const breakdown = new Map<string, number>();

    // Primary source: Redis-stored quota usage (if available)
    if (this.redis) {
      const pattern = `quota:${userId}:*:${period}`;
      const keys = await this.redis.keys(pattern);

      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          const quota = JSON.parse(data);
          const [, , connectorId] = key.split(':');
          breakdown.set(connectorId, quota.used?.cost ?? 0);
        }
      }
    }

    // Fallback/augmentation: derive from in-memory usage metrics
    // This ensures we still return meaningful data when Redis is unavailable
    // and also includes most recent, not-yet-aggregated usage.
    const now = new Date();
    const periodStart = new Date(now);
    switch (period) {
      case 'daily':
        periodStart.setHours(0, 0, 0, 0);
        break;
      case 'weekly':
        // Start from the beginning of the current week (Sunday 00:00 based on getResetTime semantics)
        periodStart.setDate(now.getDate() - now.getDay());
        periodStart.setHours(0, 0, 0, 0);
        break;
      case 'monthly':
        periodStart.setDate(1);
        periodStart.setHours(0, 0, 0, 0);
        break;
    }

    // Aggregate costs from in-memory metrics for the requested user within the period
    for (const metric of this.usageMetrics) {
      if (metric.userId !== userId) continue;
      const ts = new Date(metric.timestamp).getTime();
      if (ts < periodStart.getTime()) continue;
      const key = `${metric.connectorId}`;
      const prev = breakdown.get(key) ?? 0;
      breakdown.set(key, prev + (metric.cost || 0));
    }

    return breakdown;
  }

  public async shutdown(): Promise<void> {
    if (this.localRedis) {
      await this.localRedis.quit();
    }
  }
}

// Export singleton instance
export const rateLimiter = new RateLimiter();
