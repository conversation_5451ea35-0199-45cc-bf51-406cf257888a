import { geminiClient, GeminiClient } from '../utils/geminiClient';
import { config } from '../utils/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { countTokens } from '../utils/tokenLimiter';

/**
 * Configuration for context consolidation
 */
export interface ConsolidationConfig {
  maxOutputTokens?: number;
  includeLineNumbers?: boolean;
  focusAreas?: string[];
  preserveStructure?: boolean;
}

/**
 * Options for code structure analysis
 */
export interface CodeStructureOptions {
  includeImports?: boolean;
  includeDependencies?: boolean;
  includeComments?: boolean;
  maxDepth?: number;
}

/**
 * Strategic plan structure
 */
export interface StrategicPlan {
  description: string;
  reasoning: string;
  steps: PlanStep[];
  totalEstimatedTokens: number;
  riskAssessment?: string;
  alternativeApproaches?: string[];
}

export interface PlanStep {
  id: string;
  description: string;
  reasoning?: string;
  suggestedAgent: string;
  estimatedTokens: number;
  dependencies?: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
}

/**
 * Progress analysis result
 */
export interface ProgressAnalysis {
  completedWork: string;
  remainingWork: string;
  recommendations: string[];
  potentialBlockers?: string[];
  adjustedPlan?: PlanStep[];
}

/**
 * GeminiContextService provides intelligent context consolidation and strategic planning
 * using Gemini's large context window and advanced reasoning capabilities
 */
export class GeminiContextService {
  private cache: Map<string, { content: any; timestamp: number }> = new Map();
  private cacheTimeout: number = 3600000; // 1 hour in milliseconds

  constructor() {
    if (!geminiClient.isConfigured()) {
      console.warn('⚠️ GeminiContextService: Gemini client not configured');
    }
  }

  /**
   * Check if the service is available
   */
  public isAvailable(): boolean {
    return geminiClient.isConfigured();
  }

  /**
   * Intelligently summarize a large file
   */
  public async summarizeFile(
    filePath: string,
    options: ConsolidationConfig = {}
  ): Promise<{
    summary: string;
    structure: any;
    keyElements: string[];
    lineReferences: Record<string, number>;
  }> {
    const cacheKey = `summarize:${filePath}:${JSON.stringify(options)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    const CHUNK_TOKEN_SIZE = 10000;
    const MAX_RETRIES = 2;

    async function callGeminiWithRetry(model: string, params: any): Promise<any> {
      for (let retry = 0; retry <= MAX_RETRIES; retry++) {
        try {
          const stream = await geminiClient.createStreamingCompletion({
            model,
            ...params
          });
          let fullText = '';
          for await (const chunk of stream.textStream) {
            fullText += chunk;
          }
          return fullText;
        } catch (error: any) {
          if (retry < MAX_RETRIES && (error.message.includes('rate limit') || error.status === 429)) {
            console.warn(`Rate limit hit on ${model}, retrying with fallback model...`);
            model = model.includes('flash') ? 'gemini-2.5-pro' : model;
            continue;
          }
          throw error;
        }
      }
    }

    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      const totalTokenCount = countTokens(content);

      if (totalTokenCount <= CHUNK_TOKEN_SIZE) {
        // No chunking needed, direct summarization
        const focusPrompt = options.focusAreas?.length
          ? `Pay special attention to: ${options.focusAreas.join(', ')}`
          : '';

        const prompt = `Analyze this ${path.extname(filePath)} file and provide a comprehensive summary.

${focusPrompt}

Requirements:
1. Identify the main purpose and functionality
2. List key classes, functions, and methods with their line numbers
3. Identify important patterns and dependencies
4. Highlight any potential issues or areas of concern
5. Provide a structured overview suitable for an AI agent to understand the file

File: ${path.basename(filePath)} (${lines.length} lines, ~${totalTokenCount} tokens)

Content:
${content}

Provide your response in this JSON format:
{
  "summary": "Overall file summary",
  "structure": {
    "classes": [{"name": "ClassName", "line": 10, "purpose": "..."}],
    "functions": [{"name": "functionName", "line": 50, "purpose": "..."}],
    "imports": ["..."],
    "exports": ["..."]
  },
  "keyElements": ["Important element 1", "Important element 2"],
  "lineReferences": {
    "ElementName": lineNumber
  },
  "concerns": ["Potential issue 1", "Potential issue 2"]
}`;

        const responseText = await callGeminiWithRetry('gemini-2.5-flash', {
          messages: [
            { role: 'system', content: 'You are an expert code analyzer. Provide structured, actionable summaries.' },
            { role: 'user', content: prompt }
          ],
          thinking: GeminiClient.createThinkingConfig('complex', true),
                  max_tokens: options.maxOutputTokens || 4000
        });

        const result = this.parseJsonResponse({ choices: [{ message: { content: responseText } }] });
        this.setCache(cacheKey, result);
        return result;
      } else {
        // Chunking needed
        console.log(`Large file detected (${totalTokenCount} tokens). Implementing chunking...`);

        // Create chunks
        const chunks: string[] = [];
        let currentChunk = '';
        let currentTokens = 0;

        for (const line of lines) {
          const lineTokens = countTokens(line);
          if (currentTokens + lineTokens > CHUNK_TOKEN_SIZE && currentChunk) {
            chunks.push(currentChunk);
            currentChunk = line + '\n';
            currentTokens = lineTokens;
          } else {
            currentChunk += line + '\n';
            currentTokens += lineTokens;
          }
        }
        if (currentChunk) chunks.push(currentChunk);

        // Summarize each chunk
        const chunkSummaries: string[] = [];
        for (let i = 0; i < chunks.length; i++) {
          const chunkPrompt = `Summarize this chunk (part ${i+1}/${chunks.length}) of the ${path.extname(filePath)} file. Focus on key functionality, structures, and line numbers (assume starting from line 1 for this chunk).

Chunk content:
${chunks[i]}

Provide a concise summary:`;

          const chunkSummaryText = await callGeminiWithRetry('gemini-2.5-flash', {
            messages: [
              { role: 'system', content: 'You are an expert code summarizer.' },
              { role: 'user', content: chunkPrompt }
            ],
                      max_tokens: 1000
          });

          chunkSummaries.push(chunkSummaryText);
        }

        // Consolidate summaries
        const summariesContent = chunkSummaries.join('\n\n---\n\n');
        const consolidationTokenCount = countTokens(summariesContent);
        console.log(`Consolidating ${chunks.length} chunk summaries (${consolidationTokenCount} tokens)`);

        const focusPrompt = options.focusAreas?.length
          ? `Pay special attention to: ${options.focusAreas.join(', ')}`
          : '';

        const finalPrompt = `Consolidate these summaries from chunked analysis of a large ${path.extname(filePath)} file into a comprehensive overview.

${focusPrompt}

Original file: ${path.basename(filePath)} (${lines.length} lines, ~${totalTokenCount} tokens)
Chunks analyzed: ${chunks.length}

Chunk Summaries:
${summariesContent}

Requirements:
1. Synthesize the main purpose and functionality across the entire file
2. List key classes, functions, and methods with approximate line number ranges from chunks
3. Identify important patterns and dependencies
4. Highlight any potential issues or areas of concern
5. Provide a structured overview suitable for an AI agent to understand the file

Provide your response in this JSON format:
{
  "summary": "Overall file summary",
  "structure": {
    "classes": [{"name": "ClassName", "lineRange": "10-20", "purpose": "..."}],
    "functions": [{"name": "functionName", "lineRange": "50-60", "purpose": "..."}],
    "imports": ["..."],
    "exports": ["..."]
  },
  "keyElements": ["Important element 1", "Important element 2"],
  "lineReferences": {
    "ElementName": "approximate line range"
  },
  "concerns": ["Potential issue 1", "Potential issue 2"]
}`;

        const finalResponseText = await callGeminiWithRetry('gemini-2.5-flash', {
          messages: [
            { role: 'system', content: 'You are an expert code analyzer. Provide structured, actionable summaries from consolidated chunk data.' },
            { role: 'user', content: finalPrompt }
          ],
          thinking: GeminiClient.createThinkingConfig('complex', true),
                  max_tokens: options.maxOutputTokens || 4000
        });

        const result = this.parseJsonResponse({ choices: [{ message: { content: finalResponseText } }] });
        this.setCache(cacheKey, result);
        return result;
      }
    } catch (error) {
      console.error('Error summarizing file:', error);
      throw error;
    }
  }

  /**
   * Analyze code structure across multiple files
   */
  public async analyzeCodeStructure(
    filePaths: string[],
    options: CodeStructureOptions = {}
  ): Promise<{
    overview: string;
    dependencies: Record<string, string[]>;
    architecture: any;
    recommendations: string[];
  }> {
    const cacheKey = `structure:${filePaths.join(',')}:${JSON.stringify(options)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      // Read all files
      const fileContents = await Promise.all(
        filePaths.map(async (filePath) => {
          const content = await fs.readFile(filePath, 'utf-8');
          return { path: filePath, content };
        })
      );

      const prompt = `Analyze the code structure across these ${filePaths.length} files and provide architectural insights.

Options:
- Include imports: ${options.includeImports ?? true}
- Include dependencies: ${options.includeDependencies ?? true}
- Include comments: ${options.includeComments ?? false}

Files to analyze:
${fileContents.map(f => `\n--- ${path.basename(f.path)} ---\n${f.content}`).join('\n')}

Provide a comprehensive analysis in JSON format:
{
  "overview": "High-level architectural overview",
  "dependencies": {
    "file1": ["dependency1", "dependency2"],
    "file2": ["dependency3"]
  },
  "architecture": {
    "patterns": ["Pattern 1", "Pattern 2"],
    "layers": ["Layer 1", "Layer 2"],
    "dataFlow": "Description of data flow"
  },
  "recommendations": ["Recommendation 1", "Recommendation 2"],
  "concerns": ["Architectural concern 1"]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: 'You are an expert software architect. Analyze code structure and provide insights.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('complex', true),
              max_tokens: 8000
      });

      const result = this.parseJsonResponse(response);
      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('Error analyzing code structure:', error);
      throw error;
    }
  }

  /**
   * Debug a large file with advanced reasoning
   */
  public async debugLargeFile(
    filePath: string,
    errorContext: {
      errorMessage?: string;
      stackTrace?: string;
      relatedFiles?: string[];
      symptoms?: string[];
    }
  ): Promise<{
    analysis: string;
    rootCause: string;
    suggestedFixes: Array<{ description: string; code?: string; line?: number }>;
    verificationSteps: string[];
  }> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');

      const prompt = `Debug this file using advanced reasoning to identify the root cause of the issue.

Error Context:
- Error Message: ${errorContext.errorMessage || 'Not specified'}
- Stack Trace: ${errorContext.stackTrace || 'Not available'}
- Symptoms: ${errorContext.symptoms?.join(', ') || 'Not specified'}

File: ${path.basename(filePath)} (${lines.length} lines)

Content:
${lines.map((line, i) => `${i + 1}: ${line}`).join('\n')}

Using deep reasoning:
1. Analyze the code flow and identify potential problem areas
2. Trace through the execution path that could lead to this error
3. Identify the root cause with specific line references
4. Provide concrete fixes with code examples
5. Suggest verification steps to confirm the fix

Response format (JSON):
{
  "analysis": "Detailed analysis of the problem",
  "rootCause": "Specific root cause with reasoning",
  "suggestedFixes": [
    {
      "description": "Fix description",
      "code": "Code snippet",
      "line": lineNumber
    }
  ],
  "verificationSteps": ["Step 1", "Step 2"]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: 'You are an expert debugger. Use systematic reasoning to identify and fix bugs.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('complex', true, 1), // Higher budget for debugging
          max_tokens: 6000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('Error debugging file:', error);
      throw error;
    }
  }

  /**
   * Create a strategic plan for complex tasks
   */
  public async createStrategicPlan(
    request: string,
    context: {
      filesAnalyzed?: string[];
      previousConclusions?: string[];
      conversationHistory?: Array<{ role: string; content: string }>;
      constraints?: {
        maxTokensPerAgent?: number;
        availableAgents?: string[];
      };
    }
  ): Promise<StrategicPlan> {
    try {
      const contextSummary = this.buildContextSummary(context);

      const prompt = `Create a detailed strategic plan for this request using advanced reasoning.

Request: ${request}

Context:
${contextSummary}

Constraints:
- Max tokens per agent: ${context.constraints?.maxTokensPerAgent || 15000}
- Available agents: ${context.constraints?.availableAgents?.join(', ') || 'Research, Debug, CodeGeneration, Security, Refactor, TaskOrchestrator'}

Using strategic thinking:
1. Break down the request into logical, executable steps
2. Consider dependencies and optimal execution order
3. Assign the most appropriate agent for each step
4. Estimate token usage to prevent overflow
5. Identify potential risks and mitigation strategies
6. Suggest alternative approaches if applicable

Provide a comprehensive plan in JSON format:
{
  "description": "Overall plan description",
  "reasoning": "Strategic reasoning for this approach",
  "steps": [
    {
      "id": "step-1",
      "description": "What this step accomplishes",
      "reasoning": "Why this step is necessary",
      "suggestedAgent": "AgentName",
      "estimatedTokens": 5000,
      "dependencies": [],
      "priority": "critical",
      "metadata": {}
    }
  ],
  "totalEstimatedTokens": 20000,
  "riskAssessment": "Potential risks and mitigation",
  "alternativeApproaches": ["Alternative 1", "Alternative 2"]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: 'You are a strategic planning expert. Create detailed, actionable plans for complex software tasks.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('orchestration', true),
              max_tokens: 8000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('Error creating strategic plan:', error);
      throw error;
    }
  }

  /**
   * Analyze progress and synthesize next steps
   */
  public async synthesizeProgress(
    completedSteps: Array<{ step: string; result: string }>,
    remainingWork: string[],
    originalPlan?: StrategicPlan
  ): Promise<ProgressAnalysis> {
    try {
      const prompt = `Analyze the progress made and synthesize recommendations for next steps.

Completed Work:
${completedSteps.map(s => `- ${s.step}: ${s.result}`).join('\n')}

Remaining Work:
${remainingWork.map(w => `- ${w}`).join('\n')}

${originalPlan ? `Original Plan: ${JSON.stringify(originalPlan, null, 2)}` : ''}

Using analytical reasoning:
1. Assess what has been accomplished
2. Identify any deviations from the original plan
3. Determine the most efficient path forward
4. Identify potential blockers or issues
5. Adjust the plan if necessary based on findings

Provide analysis in JSON format:
{
  "completedWork": "Summary of completed work",
  "remainingWork": "Summary of remaining work",
  "recommendations": ["Recommendation 1", "Recommendation 2"],
  "potentialBlockers": ["Blocker 1", "Blocker 2"],
  "adjustedPlan": [adjusted steps if needed]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-flash',
        messages: [
          { role: 'system', content: 'You are a project analyst. Synthesize progress and provide actionable recommendations.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('medium', true),
              max_tokens: 4000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('Error synthesizing progress:', error);
      throw error;
    }
  }

  /**
   * Extract relevant sections from a large file based on a query
   */
  public async extractRelevantSections(
    filePath: string,
    query: string,
    maxSections: number = 5
  ): Promise<Array<{ content: string; lineStart: number; lineEnd: number; relevance: string }>> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');

      const prompt = `Extract the most relevant sections from this file based on the query.

Query: ${query}

File: ${path.basename(filePath)} (${lines.length} lines)

Content:
${lines.map((line, i) => `${i + 1}: ${line}`).join('\n')}

Identify up to ${maxSections} most relevant sections. For each section provide:
{
  "sections": [
    {
      "content": "The relevant code/text",
      "lineStart": startLineNumber,
      "lineEnd": endLineNumber,
      "relevance": "Why this section is relevant to the query"
    }
  ]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-flash',
        messages: [
          { role: 'system', content: 'You are an expert at finding relevant code sections. Be precise with line numbers.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('medium', true),
          max_tokens: 4000
      });

      const result = this.parseJsonResponse(response);
      return result.sections || [];
    } catch (error) {
      console.error('Error extracting relevant sections:', error);
      throw error;
    }
  }

  /**
   * Generate a refactoring plan for a file
   */
  public async generateRefactorPlan(
    filePath: string,
    requirements: {
      goals?: string[];
      patterns?: string[];
      constraints?: string[];
    }
  ): Promise<{
    plan: string;
    steps: Array<{ description: string; changes: string[]; risk: 'low' | 'medium' | 'high' }>;
    benefits: string[];
    testingStrategy: string;
  }> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');

      const prompt = `Create a detailed refactoring plan for this file.

Goals: ${requirements.goals?.join(', ') || 'Improve code quality'}
Patterns to apply: ${requirements.patterns?.join(', ') || 'Best practices'}
Constraints: ${requirements.constraints?.join(', ') || 'Maintain functionality'}

File: ${path.basename(filePath)}

Content:
${content}

Create a comprehensive refactoring plan:
{
  "plan": "Overall refactoring strategy",
  "steps": [
    {
      "description": "Step description",
      "changes": ["Change 1", "Change 2"],
      "risk": "low|medium|high"
    }
  ],
  "benefits": ["Benefit 1", "Benefit 2"],
  "testingStrategy": "How to verify the refactoring"
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'system', content: 'You are a refactoring expert. Create safe, effective refactoring plans.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('complex', true),
              max_tokens: 6000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('Error generating refactor plan:', error);
      throw error;
    }
  }

  /**
   * Compare multiple files and identify patterns
   */
  public async compareFiles(
    filePaths: string[],
    analysisType: 'similarity' | 'differences' | 'patterns' | 'all' = 'all'
  ): Promise<{
    comparison: string;
    similarities?: string[];
    differences?: string[];
    patterns?: string[];
    recommendations?: string[];
  }> {
    try {
      const fileContents = await Promise.all(
        filePaths.map(async (filePath) => {
          const content = await fs.readFile(filePath, 'utf-8');
          return { path: filePath, name: path.basename(filePath), content };
        })
      );

      const prompt = `Compare these ${filePaths.length} files and provide analysis focused on: ${analysisType}

Files:
${fileContents.map(f => `\n--- ${f.name} ---\n${f.content}`).join('\n')}

Provide comparison analysis:
{
  "comparison": "Overall comparison summary",
  ${analysisType === 'all' || analysisType === 'similarity' ? '"similarities": ["Similarity 1", "Similarity 2"],' : ''}
  ${analysisType === 'all' || analysisType === 'differences' ? '"differences": ["Difference 1", "Difference 2"],' : ''}
  ${analysisType === 'all' || analysisType === 'patterns' ? '"patterns": ["Pattern 1", "Pattern 2"],' : ''}
  "recommendations": ["Recommendation 1", "Recommendation 2"]
}`;

      const response = await geminiClient.createChatCompletion({
        model: 'gemini-2.5-flash',
        messages: [
          { role: 'system', content: 'You are an expert at code comparison and pattern recognition.' },
          { role: 'user', content: prompt }
        ],
        thinking: GeminiClient.createThinkingConfig('medium', true),
              max_tokens: 4000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('Error comparing files:', error);
      throw error;
    }
  }

  // Helper methods

  private buildContextSummary(context: any): string {
    const parts: string[] = [];

    if (context.filesAnalyzed?.length) {
      parts.push(`Files analyzed: ${context.filesAnalyzed.join(', ')}`);
    }

    if (context.previousConclusions?.length) {
      parts.push(`Previous findings:\n${context.previousConclusions.map((c: string) => `- ${c}`).join('\n')}`);
    }

    if (context.conversationHistory?.length) {
      const recentHistory = context.conversationHistory.slice(-5);
      parts.push(`Recent conversation:\n${recentHistory.map((m: any) => `${m.role}: ${m.content.substring(0, 200)}...`).join('\n')}`);
    }

    return parts.join('\n\n');
  }

  private parseJsonResponse(response: any): any {
    try {
      // Extract content from OpenAI format response
      const content = response.choices?.[0]?.message?.content || response;

      // Try to parse as JSON
      if (typeof content === 'string') {
        // Remove markdown code blocks if present
        const jsonMatch = content.match(/```json\n?([\s\S]*?)\n?```/) || content.match(/```\n?([\s\S]*?)\n?```/);
        const jsonStr = jsonMatch ? jsonMatch[1] : content;

        try {
          return JSON.parse(jsonStr);
        } catch {
          // If JSON parsing fails, extract structured data manually
          console.warn('Failed to parse JSON response, returning raw content');
          return { content };
        }
      }

      return content;
    } catch (error) {
      console.error('Error parsing response:', error);
      return { error: 'Failed to parse response' };
    }
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      console.log(`📦 Cache hit for: ${key}`);
      return cached.content;
    }
    return null;
  }

  private setCache(key: string, content: any): void {
    this.cache.set(key, { content, timestamp: Date.now() });

    // Clean up old cache entries
    for (const [k, v] of this.cache.entries()) {
      if (Date.now() - v.timestamp > this.cacheTimeout) {
        this.cache.delete(k);
      }
    }
  }
}

// Create singleton instance
export const geminiContextService = new GeminiContextService();
