import RunJournal from '../agents/orchestration/journal/RunJournal';
import type { Checkpoint } from '../agents/orchestration/journal/types';
import { computeBackoffDelay, getRetryAfterFromHeaders } from '../utils/backoff';
import { getContextValue } from '../utils/requestContext';

export interface ResponsesRetryConfig {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  jitter: number;
  classifyRs404AsTransient: boolean;
}

export interface ResponsesErrorClassification {
  transient: boolean;
  rsNotFound: boolean;
  status?: number;
}

interface RetryJournalArgs {
  operationKey: string;
  attempt: number;
  maxAttempts: number;
  error: any;
  headers?: any;
  baseDelayMs: number;
}

interface FinalFailureArgs {
  operationKey: string;
  error: any;
  label: string;
}

export interface ResponsesRetryTransientContext {
  attempt: number;
  classification: ResponsesErrorClassification;
  error: any;
  maxAttempts: number;
}

export interface ResponsesRetryRetryContext extends ResponsesRetryTransientContext {
  delayMs: number;
}

export interface ResponsesRetryFailureContext {
  attempt: number;
  classification: ResponsesErrorClassification;
  error: any;
  maxAttempts: number;
}

export interface ResponsesRetryLoopOptions<T> {
  operationKey: string;
  config: ResponsesRetryConfig;
  execute: (attempt: number) => Promise<T>;
  summaryLabel: string;
  sleep?: (ms: number) => Promise<void>;
  headersExtractor?: (error: any) => any;
  onTransientError?: (ctx: ResponsesRetryTransientContext) => Promise<void> | void;
  onRetryScheduled?: (ctx: ResponsesRetryRetryContext) => Promise<void> | void;
  onPermanentFailure?: (ctx: ResponsesRetryFailureContext) => Promise<Error | void> | Error | void;
}

const defaultSleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

export function classifyResponsesError(
  err: any,
  opts: { classifyRs404AsTransient: boolean }
): ResponsesErrorClassification {
  try {
    const status: number | undefined = err?.status ?? err?.response?.status ?? undefined;
    const msg: string = String(err?.message || '').toLowerCase();
    const code: string = String(err?.code || err?.cause?.code || '').toUpperCase();

    if (status === 400 || status === 401 || status === 403) {
      return { transient: false, rsNotFound: false, status };
    }

    const isRs404 =
      status === 404 &&
      opts.classifyRs404AsTransient &&
      /item with id 'rs_[^']+' not found/i.test(err?.message || '');

    if (isRs404) {
      return { transient: true, rsNotFound: true, status };
    }

    if (status === 408 || status === 429 || status === 502 || status === 503 || status === 504) {
      return { transient: true, rsNotFound: false, status };
    }

    if (['ECONNRESET', 'ETIMEDOUT', 'ENETUNREACH', 'EAI_AGAIN'].includes(code)) {
      return { transient: true, rsNotFound: false, status };
    }

    if (msg.includes('timeout') || msg.includes('timed out') || msg.includes('rate limit')) {
      return { transient: true, rsNotFound: false, status };
    }

    return { transient: false, rsNotFound: false, status };
  } catch {
    return { transient: false, rsNotFound: false };
  }
}

export async function updateRetryJournalState(args: RetryJournalArgs): Promise<void> {
  try {
    const runId = getContextValue<string>('runId');
    const taskId = getContextValue<string>('taskId');
    if (!runId || !taskId) return;

    const journal = new RunJournal();
    const current = await journal.getCurrent(runId, taskId);
    if (!current) return;

    const hdrs: Record<string, string | null> = {};
    try {
      if (args.headers) {
        if (typeof args.headers?.forEach === 'function') {
          (args.headers as any).forEach((v: any, k: string) => {
            hdrs[k] = Array.isArray(v) ? String(v[v.length - 1]) : String(v);
          });
        } else {
          for (const k of Object.keys(args.headers)) {
            const v = (args.headers as any)[k];
            hdrs[k] = Array.isArray(v) ? String(v[v.length - 1]) : String(v);
          }
        }
      }
    } catch {}

    const status: number | undefined = args.error?.status ?? args.error?.response?.status ?? undefined;
    const retry = {
      ...(current.retryState || {}),
      rsRetryCount: args.attempt,
      backoff: {
        strategy: 'exponential' as const,
        initialDelay: Number.isFinite(args.baseDelayMs) ? args.baseDelayMs : 500,
        maxRetries: args.maxAttempts
      },
      lastRetryAt: new Date().toISOString(),
      lastAttemptTs: new Date().toISOString(),
      lastResponseHeaders: hdrs,
      operationKey: args.operationKey,
      lastErrorClass: args.error?.name || 'Error',
      lastErrorMessage: String(args.error?.message || ''),
      lastStatus: status
    };

    const updated: Checkpoint = {
      ...current,
      retryState: retry
    };
    await journal.upsertCheckpoint(updated);
  } catch {}
}

export async function attachFinalFailureSummary(args: FinalFailureArgs): Promise<void> {
  try {
    const runId = getContextValue<string>('runId');
    const taskId = getContextValue<string>('taskId');
    if (!runId || !taskId) return;

    const journal = new RunJournal();
    const current = await journal.getCurrent(runId, taskId);
    if (!current) return;

    const status = args.error?.status ?? args.error?.response?.status ?? 'n/a';
    const summary = `[${args.label}.final_failure] op=${args.operationKey} status=${status} message=${String(args.error?.message || '')}`.slice(0, 2000);

    const updated: Checkpoint = {
      ...current,
      diagnostics: {
        ...(current.diagnostics || {}),
        failureSummary: summary,
        lastStackTrace: (args.error?.stack && String(args.error.stack).slice(0, 4000)) || (current.diagnostics?.lastStackTrace ?? null)
      }
    };

    await journal.upsertCheckpoint(updated);
  } catch {}
}

export async function runResponsesRetryLoop<T>(options: ResponsesRetryLoopOptions<T>): Promise<T> {
  const {
    operationKey,
    config,
    execute,
    summaryLabel,
    sleep = defaultSleep,
    headersExtractor,
    onTransientError,
    onRetryScheduled,
    onPermanentFailure
  } = options;

  const headerResolver = headersExtractor ?? ((error: any) => error?.headers || error?.response?.headers);

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await execute(attempt);
    } catch (error) {
      const classification = classifyResponsesError(error, {
        classifyRs404AsTransient: config.classifyRs404AsTransient
      });

      const headers = headerResolver(error);

      try {
        await updateRetryJournalState({
          operationKey,
          attempt,
          maxAttempts: config.maxAttempts,
          error,
          headers,
          baseDelayMs: config.baseDelayMs
        });
      } catch {}

      const isLastAttempt = attempt >= config.maxAttempts;
      if (!classification.transient || isLastAttempt) {
        let overrideError: Error | undefined;
        if (onPermanentFailure) {
          const maybeOverride = await onPermanentFailure({
            attempt,
            classification,
            error,
            maxAttempts: config.maxAttempts
          });
          if (maybeOverride instanceof Error) {
            overrideError = maybeOverride;
          }
        }

        try {
          await attachFinalFailureSummary({
            operationKey,
            error,
            label: summaryLabel
          });
        } catch {}

        throw overrideError ?? error;
      }

      if (onTransientError) {
        await onTransientError({
          attempt,
          classification,
          error,
          maxAttempts: config.maxAttempts
        });
      }

      const headerDelay = getRetryAfterFromHeaders(headers) ?? undefined;
      const backoffDelay = computeBackoffDelay(attempt, {
        baseDelayMs: config.baseDelayMs,
        maxDelayMs: config.maxDelayMs,
        jitter: config.jitter
      });
      const delayMs = Math.max(headerDelay ?? 0, backoffDelay);

      if (onRetryScheduled) {
        await onRetryScheduled({
          attempt,
          classification,
          error,
          delayMs,
          maxAttempts: config.maxAttempts
        });
      }

      await sleep(delayMs);
    }
  }

  throw new Error('Exhausted retry attempts without result');
}
