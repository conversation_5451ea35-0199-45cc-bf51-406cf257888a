import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function inRepo(): Promise<boolean> {
  try {
    const { stdout } = await execAsync('git rev-parse --is-inside-work-tree');
    return String(stdout || '').trim() === 'true';
  } catch {
    return false;
  }
}

export async function getHeadSha(): Promise<{ ok: boolean; sha?: string; stdout?: string; stderr?: string }> {
  if (!(await inRepo())) return { ok: false, stderr: 'not a git repo' };
  try {
    const { stdout } = await execAsync('git rev-parse HEAD');
    const sha = String(stdout || '').trim();
    return { ok: true, sha, stdout };
  } catch (e: any) {
    return { ok: false, stderr: e?.stderr || e?.message || String(e) };
  }
}

export async function isCleanTree(): Promise<{ ok: boolean; clean: boolean; stdout?: string; stderr?: string }> {
  if (!(await inRepo())) return { ok: false, clean: false, stderr: 'not a git repo' };
  try {
    const { stdout } = await execAsync('git status --porcelain');
    const clean = String(stdout || '').trim().length === 0;
    return { ok: true, clean, stdout };
  } catch (e: any) {
    return { ok: false, clean: false, stderr: e?.stderr || e?.message || String(e) };
  }
}

export async function ensureBranch(
  workingBranch: string,
  baseBranch?: string,
  baseCommit?: string,
): Promise<{ ok: boolean; created?: boolean; currentBranch?: string; stdout?: string; stderr?: string }> {
  if (!(await inRepo())) return { ok: false, stderr: 'not a git repo' };
  try {
    let current = '';
    try {
      const { stdout: curOut } = await execAsync('git rev-parse --abbrev-ref HEAD');
      current = String(curOut || '').trim();
    } catch {}

    if (current === workingBranch) {
      return { ok: true, created: false, currentBranch: workingBranch };
    }

    const baseRef = baseCommit || baseBranch || 'HEAD';
    try {
      const { stdout } = await execAsync(`git checkout -B "${workingBranch.replace(/"/g, '\\"')}" ${baseRef}`);
      return { ok: true, created: true, currentBranch: workingBranch, stdout };
    } catch (e: any) {
      try {
        const { stdout } = await execAsync(`git checkout "${workingBranch.replace(/"/g, '\\"')}"`);
        return { ok: true, created: false, currentBranch: workingBranch, stdout };
      } catch (e2: any) {
        return { ok: false, stderr: e2?.stderr || e2?.message || String(e2) };
      }
    }
  } catch (e: any) {
    return { ok: false, stderr: e?.stderr || e?.message || String(e) };
  }
}

export async function commitAll(
  message: string,
): Promise<{ ok: boolean; sha?: string; stdout?: string; stderr?: string; message: string }> {
  if (!(await inRepo())) return { ok: false, message, stderr: 'not a git repo' };
  try {
    const add = await execAsync('git add -A');
    const commit = await execAsync(`git commit -m ${JSON.stringify(message || 'orchestrator commit')}`);
    const head = await getHeadSha();
    if (!head.ok) {
      return { ok: false, message, stdout: `${add.stdout || ''}\n${commit.stdout || ''}`, stderr: head.stderr };
    }
    return { ok: true, sha: head.sha, stdout: `${add.stdout || ''}\n${commit.stdout || ''}`, message };
  } catch (e: any) {
    const msg = e?.stderr || e?.message || String(e || '');
    if (/nothing to commit/i.test(msg)) {
      const head = await getHeadSha();
      return { ok: true, sha: head.sha, stdout: 'nothing to commit', message };
    }
    return { ok: false, stderr: msg, message };
  }
}

export async function resetHard(sha: string): Promise<{ ok: boolean; stdout?: string; stderr?: string }> {
  if (!(await inRepo())) return { ok: false, stderr: 'not a git repo' };
  try {
    const { stdout } = await execAsync(`git reset --hard ${sha}`);
    return { ok: true, stdout };
  } catch (e: any) {
    return { ok: false, stderr: e?.stderr || e?.message || String(e) };
  }
}

// Ensure treated as a module even if empty in some build modes
export const __gitServiceModule = true;
