/**
 * Tool Converter - Converts OpenAI Agents SDK tools to Vercel AI SDK format
 */

import { tool as vercelTool } from 'ai';
import { z } from 'zod';

/**
 * Convert an OpenAI Agents SDK tool to Vercel AI SDK format
 */
export function convertTool(openAITool: any): any {
  // Extract tool configuration
  const name = openAITool.name || openAITool._name || 'unknown_tool';
  const description = openAITool.description || openAITool._description || '';
  
  // Convert parameters to inputSchema (handle both 'parameters' and 'inputSchema')
  let schema = openAITool.parameters || openAITool.inputSchema;
  
  // If schema has nullable().optional() patterns, clean them up
  if (schema && typeof schema === 'object') {
    schema = cleanupZodSchema(schema);
  }

  // Extract execute function (might be 'execute', 'invoke', or direct function)
  let executeFn = openAITool.execute || openAITool.invoke;
  
  // Handle the case where the tool itself might be the execute function
  if (typeof openAITool === 'function') {
    executeFn = openAITool;
  }

  // Create Vercel AI SDK tool
  return vercelTool({
    description,
    inputSchema: schema || z.object({}),
    execute: executeFn || (async () => ({ error: 'No execute function defined' }))
  });
}

/**
 * Clean up Zod schema to remove OpenAI SDK-specific patterns
 */
function cleanupZodSchema(schema: any): any {
  // If it's already a Zod schema, return as is
  if (schema && schema._def) {
    return schema;
  }

  // If it's a plain object, try to reconstruct
  if (typeof schema === 'object' && !Array.isArray(schema)) {
    const shape: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(schema)) {
      if (value && typeof value === 'object') {
        // Handle nested schemas
        shape[key] = cleanupZodSchema(value);
      } else {
        shape[key] = value;
      }
    }
    
    return z.object(shape);
  }

  return schema;
}

/**
 * Batch convert multiple tools
 */
export function convertTools(tools: any[]): Record<string, any> {
  const converted: Record<string, any> = {};
  
  for (const tool of tools) {
    const name = tool.name || tool._name || `tool_${Object.keys(converted).length}`;
    try {
      converted[name] = convertTool(tool);
      console.log(`✅ Converted tool: ${name}`);
    } catch (error) {
      console.error(`❌ Failed to convert tool ${name}:`, error);
    }
  }
  
  return converted;
}

/**
 * Create a wrapper that makes Vercel AI tools compatible with legacy code
 */
export function createLegacyWrapper(vercelTool: any): any {
  return {
    name: vercelTool.description?.split('\n')[0] || 'wrapped_tool',
    description: vercelTool.description,
    parameters: vercelTool.inputSchema,
    execute: vercelTool.execute,
    
    // Legacy compatibility methods
    invoke: async (context: any, argsJson: string) => {
      const args = JSON.parse(argsJson);
      return vercelTool.execute(args, context);
    }
  };
}