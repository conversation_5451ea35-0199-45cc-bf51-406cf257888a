import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@agents': path.resolve(__dirname, './src/agents'),
      '@tools': path.resolve(__dirname, './src/tools'),
      '@ui': path.resolve(__dirname, './src/ui'),
      '@utils': path.resolve(__dirname, './src/utils'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3002,
    open: true,
    hmr: true, // Temporarily disabled for refactoring
    proxy: {
      '/api': {
        target: process.env.PORT ? `http://127.0.0.1:${process.env.PORT}` : 'http://127.0.0.1:3001',
        changeOrigin: true,
        secure: false,
        ws: true,
        // Keep SSE connections alive through the proxy
        timeout: 24 * 60 * 60 * 1000, // 24h
        proxyTimeout: 24 * 60 * 60 * 1000, // 24h upstream
        configure: (proxy) => {
          // Ensure SSE headers are preserved and prevent buffering
          proxy.on('proxyRes', (proxyRes, req, res) => {
            const ct = proxyRes.headers['content-type'] || '';
            if (typeof ct === 'string' && ct.includes('text/event-stream')) {
              res.setHeader('Content-Type', 'text/event-stream');
              res.setHeader('Cache-Control', 'no-cache');
              res.setHeader('Connection', 'keep-alive');
              res.setHeader('X-Accel-Buffering', 'no');
            }
          });
        },
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    chunkSizeWarningLimit: 300,
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core React libraries - keep separate for caching
          if (id.includes('react') && !id.includes('react-')) {
            return 'vendor-react-core';
          }
          if (id.includes('react-dom')) {
            return 'vendor-react-dom';
          }

          // Split markdown processing into smaller chunks
          if (id.includes('react-markdown')) {
            return 'markdown-react';
          }
          if (id.includes('remark-gfm')) {
            return 'markdown-gfm';
          }
          if (id.includes('rehype-highlight')) {
            return 'markdown-highlight';
          }
          if (id.includes('rehype-raw')) {
            return 'markdown-raw';
          }
          if (id.includes('remark-') || id.includes('rehype-') || id.includes('micromark')) {
            return 'markdown-processors';
          }

          // Split charts more granularly
          if (id.includes('recharts')) {
            return 'charts-recharts';
          }
          if (id.includes('d3-')) {
            return 'charts-d3';
          }

          // Animation libraries
          if (id.includes('framer-motion')) {
            return 'animation-framer';
          }

          // Icon libraries
          if (id.includes('lucide-react')) {
            return 'icons-lucide';
          }

          // State management
          if (id.includes('zustand')) {
            return 'state-zustand';
          }

          // Validation and parsing
          if (id.includes('zod')) {
            return 'validation-zod';
          }
          if (id.includes('yaml')) {
            return 'parsers-yaml';
          }
          if (id.includes('gpt-tokenizer')) {
            return 'parsers-tokenizer';
          }

          // AI/ML libraries split
          if (id.includes('@openai/agents/realtime')) {
            return 'ai-agents-realtime';
          }
          if (id.includes('@openai/agents')) {
            return 'ai-agents';
          }
          if (id.includes('openai') && !id.includes('@openai/agents')) {
            return 'ai-openai';
          }
          if (id.includes('@google/genai')) {
            return 'ai-google';
          }

          // Database and storage
          if (id.includes('@qdrant/js-client-rest')) {
            return 'db-qdrant';
          }
          if (id.includes('idb')) {
            return 'db-idb';
          }

          // Window management
          if (id.includes('react-draggable')) {
            return 'window-draggable';
          }
          if (id.includes('react-resizable')) {
            return 'window-resizable';
          }
          if (id.includes('react-window')) {
            return 'window-virtual';
          }

          // Node utilities
          if (id.includes('cheerio')) {
            return 'node-cheerio';
          }
          if (id.includes('turndown')) {
            return 'node-turndown';
          }
          if (id.includes('jsdom')) {
            return 'node-jsdom';
          }

          // Browser automation
          if (id.includes('playwright')) {
            return 'browser-playwright';
          }

          // Tailwind
          if (id.includes('@tailwindcss/typography')) {
            return 'styles-typography';
          }

          // Large node_modules libraries get their own chunks
          if (id.includes('node_modules')) {
            const chunks = id.split('node_modules/')[1]?.split('/')[0];
            if (chunks && ['highlight.js', 'prismjs', 'unified', 'vfile'].includes(chunks)) {
              return `vendor-${chunks}`;
            }
          }

          return null; // Default chunk
        }
      }
    }
  },
});
