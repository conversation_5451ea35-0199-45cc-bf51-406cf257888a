services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: dante-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # gRPC API (optional, for performance)
    volumes:
      - ./qdrant_storage:/qdrant/storage
      - ./qdrant_config:/qdrant/config
    environment:
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__LOG_LEVEL=INFO
      - QDRANT__STORAGE__STORAGE_PATH=/qdrant/storage
      - QDRANT__STORAGE__SNAPSHOTS_PATH=/qdrant/storage/snapshots
      # Optional: Enable authentication (uncomment and set your own key)
      # - QDRANT__SERVICE__API_KEY=your-secure-api-key-here
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readyz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - dante-network

networks:
  dante-network:
    driver: bridge

volumes:
  qdrant_storage:
    driver: local
  qdrant_config:
    driver: local