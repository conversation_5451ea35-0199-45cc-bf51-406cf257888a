import os
import smtplib
from typing import Optional
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText


def send_email(
    recipient_email: str,
    subject: str,
    html_content: Optional[str] = None,
    text_content: Optional[str] = None,
) -> None:
    """Sends an email to the specified recipient.

    Args:
        recipient_email: The email address of the recipient.
        subject: The subject of the email.
        html_content: The HTML content of the email. Defaults to None.
        text_content: The plain text content of the email. Defaults to None.
    """
    if not recipient_email:
        raise ValueError("recipient_email is required")
    if not subject:
        raise ValueError("subject is required")

    # Retrieve SMTP configuration from environment variables
    smtp_host = os.environ.get("SMTP_HOST")
    smtp_port = os.environ.get("SMTP_PORT")
    smtp_username = os.environ.get("SMTP_USERNAME")
    smtp_password = os.environ.get("SMTP_PASSWORD")
    sender_email = os.environ.get("SENDER_EMAIL")

    if not all([smtp_host, smtp_port, smtp_username, smtp_password, sender_email]):
        raise EnvironmentError(
            "SMTP environment variables are not fully configured. "
            "Required: SMTP_HOST, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD, SENDER_EMAIL"
        )

    msg = MIMEMultipart("alternative")
    msg["From"] = sender_email
    msg["To"] = recipient_email
    msg["Subject"] = subject

    if text_content:
        msg.attach(MIMEText(text_content, "plain"))
    if html_content:
        msg.attach(MIMEText(html_content, "html"))

    try:
        port = int(smtp_port)
    except ValueError as e:
        raise ValueError("SMTP_PORT must be an integer") from e

    try:
        # Use SSL by default (e.g., Gmail port 465)
        with smtplib.SMTP_SSL(smtp_host, port) as server:
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
        print(f"Email sent successfully to {recipient_email}")
    except Exception as e:
        raise RuntimeError(f"Failed to send email: {e}") from e


if __name__ == "__main__":
    # Example Usage:
    # Set environment variables before running this script for testing:
    # export SMTP_HOST="your_smtp_host"
    # export SMTP_PORT="465"  # e.g., Gmail SSL
    # export SMTP_USERNAME="your_smtp_username"
    # export SMTP_PASSWORD="your_smtp_password_or_app_password"
    # export SENDER_EMAIL="your_sender_email"

    recipient = "<EMAIL>"
    subject = "Test Briefing"
    html = "<p>This is an <b>HTML</b> briefing.</p>"
    text = "This is a plain text briefing."

    send_email(recipient, subject, html_content=html, text_content=text)
