# MCP (Model Context Protocol) Configuration
# Copy this file to .env.mcp and uncomment/modify the settings you want to use

# Enable/disable MCP integration
MCP_ENABLED=true

# Auto-connect to servers on startup
MCP_AUTO_CONNECT=true

# Health check interval (milliseconds)
MCP_HEALTH_CHECK_INTERVAL=60000

# Maximum concurrent connections
MCP_MAX_CONNECTIONS=10

# Connection timeout (milliseconds)
MCP_CONNECTION_TIMEOUT=30000

# Default MCP Servers
# Filesystem server - provides file system access
MCP_FILESYSTEM_ENABLED=true
MCP_FILESYSTEM_ALLOW_WRITE=false

# Git server - provides git repository access
MCP_GIT_ENABLED=true

# Web search server (requires a compatible MCP web search server)
# MCP_WEB_SEARCH_URL=https://api.example.com/mcp
# MCP_WEB_SEARCH_API_KEY=your-api-key-here

# Database server (requires a compatible MCP database server)
# MCP_DATABASE_URL=postgresql://localhost:5432/yourdb
# MCP_DATABASE_SERVER_URL=http://localhost:3001/mcp

# Custom MCP servers using JSON configuration
# MCP_CUSTOM_SERVERS=[{"id":"custom1","name":"Custom Server 1","type":"stdio","config":{"fullCommand":"echo test"},"enabled":true,"priority":50,"tags":["custom"]}]

# Custom MCP servers using numbered environment variables
# Example: Simple stdio server
# MCP_SERVER_1_ID=example-server
# MCP_SERVER_1_NAME=Example MCP Server
# MCP_SERVER_1_TYPE=stdio
# MCP_SERVER_1_COMMAND=npx -y @modelcontextprotocol/server-filesystem /tmp
# MCP_SERVER_1_PRIORITY=80
# MCP_SERVER_1_TAGS=filesystem,example
# MCP_SERVER_1_ENABLED=true
# MCP_SERVER_1_CACHE_TOOLS=true

# Example: HTTP server with authentication
# MCP_SERVER_2_ID=http-example
# MCP_SERVER_2_NAME=HTTP Example Server
# MCP_SERVER_2_TYPE=streamable_http
# MCP_SERVER_2_URL=https://api.example.com/mcp
# MCP_SERVER_2_API_KEY=your-bearer-token
# MCP_SERVER_2_PRIORITY=70
# MCP_SERVER_2_TAGS=web,api
# MCP_SERVER_2_ENABLED=true
# MCP_SERVER_2_CACHE_TOOLS=true