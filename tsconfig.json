{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["bun", "node"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@agents/*": ["src/agents/*"], "@tools/*": ["src/tools/*"], "@ui/*": ["src/ui/*"], "@utils/*": ["src/utils/*"]}}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist", "build", "src/**/*.test.ts", "src/**/test/**/*", "src/tests/**/*"]}