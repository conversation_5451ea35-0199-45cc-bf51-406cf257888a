# oauth2-proxy environment template
# Copy this file to oauth2-proxy/.env and fill in the values. Do not commit the filled .env to version control.

# Required placeholders
GOOGLE_CLIENT_ID=__REPLACE_ME__
GOOGLE_CLIENT_SECRET=__REPLACE_ME__
# COOKIE_SECRET must be a random 32-byte base64-encoded value (no newlines)
# Example commands to generate one:
#   openssl rand -base64 32 | tr -d '\n'
#   python - <<'PY'\n#   import os, base64; print(base64.urlsafe_b64encode(os.urandom(32)).decode())\n#   PY
COOKIE_SECRET=__REPLACE_ME__

# Your public domain (without protocol). Example: example.com
DOMAIN=your.domain

# IMPORTANT: Google OAuth redirect URI (must match <PERSON>X<PERSON>TLY in Google Cloud Console)
#   https://${DOMAIN}/oauth2/callback
# Example concrete value:
#   https://your.domain/oauth2/callback

# Optional: If you want to use oauth2-proxy env vars directly, uncomment below
# OAUTH2_PROXY_PROVIDER=google
# OAUTH2_PROXY_CLIENT_ID=${GOOGLE_CLIENT_ID}
# OAUTH2_PROXY_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
# OAUTH2_PROXY_COOKIE_SECRET=${COOKIE_SECRET}
# OAUTH2_PROXY_REDIRECT_URL=https://${DOMAIN}/oauth2/callback
# OAUTH2_PROXY_COOKIE_DOMAIN=${DOMAIN}
# OAUTH2_PROXY_WHITELIST_DOMAIN=.${DOMAIN}
# OAUTH2_PROXY_AUTHENTICATED_EMAILS_FILE=/oauth2-proxy/authenticated-emails.txt
