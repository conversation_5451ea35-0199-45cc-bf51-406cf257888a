import os
import subprocess
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler
from typing import Optional

# --- Configuration ---
# Run the existing Node-based briefing generator via Bun, then email using Python.
PROJECT_ROOT = os.path.dirname(__file__)
BRIEFINGS_DIR = os.path.join(PROJECT_ROOT, "logs", "ai-briefings")
TIMEZONE = "America/New_York"  # EST/EDT timezone

# Email recipient (override via TO_EMAIL env). Provided default per user request.
DEFAULT_RECIPIENT = "<EMAIL>"

try:
    os.makedirs(BRIEFINGS_DIR, exist_ok=True)
except Exception:
    # Best-effort; will fail later if not writable
    pass


def _latest_briefing_md() -> Optional[str]:
    try:
        files = [
            os.path.join(BRIEFINGS_DIR, f)
            for f in os.listdir(BRIEFINGS_DIR)
            if f.endswith(".md")
        ]
        if not files:
            return None
        return max(files, key=lambda p: os.path.getmtime(p))
    except Exception:
        return None


def run_daily_briefing():
    """
    Generates the daily briefing using the existing JS script, then emails it using Python.
    """
    print(f"[{datetime.now()}] Starting briefing generation via Bun...")
    try:
        # Generate briefing (no console print) using Bun/Node script
        result = subprocess.run(
            ["bun", "run", "scripts/ai-briefing.js", "run", "--no-print"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"[{datetime.now()}] Briefing generation completed.")
        if result.stdout:
            print("STDOUT:\n", result.stdout)
        if result.stderr:
            print("STDERR:\n", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"[{datetime.now()}] Error generating briefing: {e}")
        if e.stdout:
            print("STDOUT (if any):\n", e.stdout)
        if e.stderr:
            print("STDERR (if any):\n", e.stderr)
        return
    except FileNotFoundError:
        print(
            f"[{datetime.now()}] Error: 'bun' not found on PATH. Install Bun or adjust the command."
        )
        return
    except Exception as e:
        print(f"[{datetime.now()}] Unexpected error during generation: {e}")
        return

    # Load the latest briefing and send via email
    latest = _latest_briefing_md()
    if not latest:
        print(f"[{datetime.now()}] No briefing markdown file found in {BRIEFINGS_DIR}.")
        return

    try:
        with open(latest, "r", encoding="utf-8") as f:
            content = f.read()
    except Exception as e:
        print(f"[{datetime.now()}] Failed to read latest briefing file: {e}")
        return

    # Prepare email
    recipient = os.environ.get("TO_EMAIL") or DEFAULT_RECIPIENT
    today = datetime.now().strftime("%Y-%m-%d")
    subject = f"Dante Daily AI Briefing - {today}"

    # Minimal HTML wrapper to preserve formatting; text fallback is the raw markdown
    html = f"""
    <html>
      <body>
        <div style=\"font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif; line-height: 1.5;\">
          <h2 style=\"margin: 0 0 12px 0;\">{subject}</h2>
          <pre style=\"white-space: pre-wrap; word-wrap: break-word;\">{content}</pre>
        </div>
      </body>
    </html>
    """

    try:
        from email_sender import send_email
    except Exception as e:
        print(f"[{datetime.now()}] Failed to import email sender: {e}")
        return

    try:
        send_email(recipient, subject, html_content=html, text_content=content)
        print(f"[{datetime.now()}] Briefing emailed to {recipient}.")
    except Exception as e:
        print(f"[{datetime.now()}] Failed to send email: {e}")


def main():
    scheduler = BlockingScheduler(timezone=TIMEZONE)

    # Schedule the job to run every day at 9:00 AM
    scheduler.add_job(
        run_daily_briefing,
        "cron",
        hour=9,
        minute=0,
        id="daily_ai_briefing_job",
        name="Run Daily AI Briefing Script",
        misfire_grace_time=60 * 60,  # Allow up to 1 hour grace time for missed runs
    )

    print(f"[{datetime.now()}] Daily AI Briefing Scheduler started.")
    print(
        f"[{datetime.now()}] Scheduled to run daily at 9:00 AM {TIMEZONE} (UTC offset varies with DST)."
    )
    print(f"[{datetime.now()}] Press Ctrl+C to exit.")

    try:
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        print(f"[{datetime.now()}] Scheduler stopped.")


if __name__ == "__main__":
    main()
