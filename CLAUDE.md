# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dante is an advanced AI assistant powered by GPT-5 and the OpenAI Agents SDK. It features a multi-agent architecture with specialized agents for different tasks, orchestrated through a core agent that delegates work based on task complexity and type.

## Key Commands

### Development
```bash
# Install dependencies (required: Bun runtime v1.0.0+)
bun install

# Run development servers
bun run dev:all    # Start both API (port 3001) and web UI (port 3002)
bun run dev        # Start web UI only (port 3002)
bun run dev:api    # Start API server only (port 3001)

# Build and testing
bun run build      # Build for production
bun run typecheck  # Check TypeScript types
bun test          # Run tests

# Run interfaces
bun run cli       # Start CLI interface
bun run api       # Start API server
bun run start     # Run main application
```

### Environment Setup
- Copy `.env.example` to `.env`
- Required: `OPENAI_API_KEY` with GPT-5 access
- Optional: `WEATHER_API_KEY` for weather functionality
- Default model: `gpt-5` (configurable via `DEFAULT_MODEL`)

## Architecture

### Agent System
The codebase uses a **delegated multi-agent architecture** with intelligent routing:

1. **DanteCore** (`src/agents/DanteCore.ts`): Main orchestrator that analyzes requests and delegates to specialized agents
2. **TaskOrchestrator** (`src/agents/TaskOrchestrator.ts`): Handles large-scale tasks (>20k tokens or >5 files) by distributing work to worker agents
3. **Specialized Agents**: Each handles specific domains (Research, CodeGeneration, Debug, Security, etc.)
4. **Worker Agents** (`src/agents/workers/`): Process chunks of large tasks in parallel

### Token Management Strategy
- Requests >20k tokens or analyzing >5 files automatically route to TaskOrchestrator
- TaskOrchestrator splits work into chunks of <15k tokens per worker
- Synthesis Worker aggregates results from multiple workers
- This prevents context overflow while maintaining completeness

### Key Components

- **Agent Handoffs**: Uses `@openai/agents` SDK's handoff mechanism for seamless agent transitions
- **Tools** (`src/tools/`): Shared capabilities (web search, file operations, weather)
- **Stream Handling** (`src/utils/streamHandler.ts`): Real-time response streaming
- **Session Management** (`src/utils/sessionManager.ts`): Maintains conversation context
- **Token Limiting** (`src/utils/tokenLimiter.ts`): Monitors and manages token usage

### API Structure
- Express server at `src/api/server.ts`
- Main endpoint: `POST /api/chat` accepts message arrays
- Streaming responses via Server-Sent Events

### Web UI
- React + Vite application in `src/ui/`
- Proxies API calls to backend server
- Real-time streaming display with markdown rendering

## Path Aliases
The project uses TypeScript path aliases:
- `@/` → `src/`
- `@agents/` → `src/agents/`
- `@tools/` → `src/tools/`
- `@ui/` → `src/ui/`
- `@utils/` → `src/utils/`

## Adding New Capabilities

### New Agent
1. Create agent file in `src/agents/`
2. Define with specific instructions and tools
3. Add handoff in `DanteCore.ts`
4. Export from `src/agents/index.ts`

### New Tool
1. Create tool file in `src/tools/`
2. Use `tool()` helper from `@openai/agents`
3. Export from `src/tools/index.ts`
4. Add to relevant agents

## Important Patterns

### Large Task Handling
When implementing features that analyze multiple files or large content:
1. Always check token count first
2. If >20k tokens, delegate to TaskOrchestrator
3. TaskOrchestrator will automatically distribute to workers
4. Results are synthesized and returned as unified response

### Agent Communication
- Agents communicate via handoffs, not direct calls
- Context is preserved across handoffs
- Each agent returns control to caller when complete

### Error Handling
- All agents should handle errors gracefully
- Use try-catch blocks in tool implementations
- Return informative error messages to users

### Tool Management Best Practices
- When replacing tools with better alternatives, completely remove the old tool from all agents
- Update prompts to only mention the new tool to avoid LLM confusion
- Remove old tool from exports and imports to prevent accidental usage
- Example: computer_use tool was replaced with computer_use_dispatcher for session isolation

### Critical: Zod Schema Rules for OpenAI API Tools
When creating tools with Zod schemas for OpenAI's Agents SDK:
- **ALWAYS use `.nullable().optional()`** instead of just `.optional()` for optional fields
- The OpenAI API requires nullable fields when using structured outputs
- This prevents the error: "uses `.optional()` without `.nullable()` which is not supported by the API"
- Correct: `z.string().nullable().optional()` ✅
- Wrong: `z.string().optional()` ❌
- This applies to ALL optional fields in tool parameters: strings, numbers, booleans, enums, etc.
- Use `bun run build` to compile the project and check for any Zod schema errors.
