[tool.black]
line-length = 88
target-version = ['py39']

[tool.ruff]
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # Pyflakes
    "C",  # Complexity (McCabe)
    "I",  # Isort
    "N",  # Naming conventions
    "D",  # pydocstyle
    "UP", # Pyupgrade
    "ANN", # Annotations
    "ASYNC", # flake8-async
    "S",  # Bandit
    "B",  # Bugbear
    "A",  # builtins
    "RUF", # Ruff-specific rules
]
ignore = [
    "E501", # Line too long (handled by <PERSON>)
    "D100", # Missing docstring in public module
    "D104", # Missing docstring in public package
    "D105", # Missing docstring in magic method
    "D107", # Missing docstring in __init__
]
unfixable = ["B"] # Don't auto-fix Bandit warnings

[tool.ruff.pydocstyle]
convention = "google"

[tool.flake8]
max-line-length = 88
extend-ignore = [
    "E203", # whitespace before ':' (incompatible with black)
    "E501", # line too long (handled by <PERSON>)
    "W503", # line break before binary operator (incompatible with Black)
]

[tool.pylint."MESSAGES CONTROL"]
disable = [
    "C0114", # Missing module docstring
    "C0115", # Missing class docstring
    "C0116", # Missing function or method docstring
    "W0613", # Unused argument
    "R0903", # Too few public methods
    "R0913", # Too many arguments
]
good-names = ["i", "j", "k", "ex", "Run", "_"]
max-line-length = 88
