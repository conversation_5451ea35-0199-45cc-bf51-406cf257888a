{"name": "dante-gpt", "version": "1.0.0", "description": "Dante - Advanced AI Assistant powered by GPT-5", "type": "module", "scripts": {"dev": "bunx vite", "dev:api": "bun run src/api/server.ts", "dev:all": "bun run dev:api & bun run dev", "build": "bunx tsc --project tsconfig.build.json && bunx vite build", "build:production": "NODE_ENV=production bun run build", "preview": "bunx vite preview", "start": "bun run src/index.ts", "start:production": "NODE_ENV=production bun run dist/api/server.js", "start:cluster": "NODE_ENV=production pm2 start ecosystem.config.js", "cli": "bun run src/cli.ts", "api": "bun run src/api/server.ts", "typecheck": "bunx tsc --noEmit", "test": "bun test", "test:cov": "c8 bun test", "test:connectors": "bun test src/tests/connectors/", "test:connectors:unit": "bun test src/tests/connectors/*.test.ts", "test:connectors:integration": "bun test src/tests/connectors/integration/", "test:connectors:e2e": "bun test src/tests/connectors/e2e/", "test:connectors:load": "bun run src/tests/connectors/loadTest.ts", "coverage:report": "c8 report", "coverage:open": "open coverage/index.html", "test:chunking": "bun test src/tests/simpleChunkingTest.test.ts", "test:chunking:full": "bun run src/tests/runChunkingTests.ts", "test:chunking:unit": "bun test src/tests/webContentChunkingFixed.test.ts", "test:chunking:integration": "bun test src/tests/webSearchTool.integration.test.ts", "test:chunking:e2e": "bun test src/tests/webContentChunkTool.e2e.test.ts", "test:chunking:agents": "bun test src/tests/agentChunkingIntegration.test.ts", "test:chunking:errors": "bun test src/tests/chunkingErrorHandling.test.ts", "test:chunking:performance": "bun test src/tests/chunkingPerformance.benchmark.test.ts", "migrate-memories": "bun run src/utils/migration/memoryMigrator.ts", "migrate-tokens": "bun run src/db/migrate.ts", "migrate-tokens:rollback": "bun run src/db/migrate.ts rollback", "migrate-tokens:verify": "bun run src/db/migrate.ts verify", "migrate:production": "NODE_ENV=production bun run migrate-tokens", "migrate:status": "NODE_ENV=production bun run migrate-tokens:verify", "health:check": "curl -f http://localhost:3001/health || exit 1", "health:detailed": "curl http://localhost:3001/health/detailed | jq", "health:connectors": "bun run src/scripts/checkConnectorHealth.ts", "monitor:start": "bun run src/services/connectorMonitor.ts", "monitor:dashboard": "open http://localhost:3004", "metrics:export": "curl http://localhost:9090/metrics", "connectors:list": "bun run src/scripts/listConnectors.ts", "connectors:test": "bun run src/scripts/testConnector.ts", "connectors:refresh-tokens": "bun run src/scripts/refreshTokens.ts", "connectors:cleanup": "bun run src/scripts/cleanupExpiredTokens.ts", "backup:create": "./scripts/backup.sh", "backup:restore": "./scripts/restore.sh", "ssl:generate": "./scripts/generate-ssl.sh", "ssl:renew": "certbot renew", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f qdrant", "docker:production": "docker-compose -f docker-compose.production.yml up -d", "docker:production:build": "docker-compose -f docker-compose.production.yml build", "docker:production:logs": "docker-compose -f docker-compose.production.yml logs -f", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop all", "pm2:restart": "pm2 restart all", "pm2:reload": "pm2 reload all", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "pm2:save": "pm2 save", "pm2:startup": "pm2 startup", "pm2:briefing:start": "pm2 start ecosystem.briefing.config.cjs", "pm2:briefing:save": "pm2 save", "pm2:briefing:startup": "pm2 startup", "briefing:pm2": "bun run pm2:briefing:start && bun run pm2:briefing:save && bun run pm2:briefing:startup", "computer-use:build": "cd docker/computer-use && docker build --platform=linux/amd64 -t dante-computer-use .", "computer-use:run": "cd docker/computer-use && docker-compose up -d", "computer-use:stop": "cd docker/computer-use && docker-compose down", "browser-use:setup": "./scripts/browser-use-setup.sh", "browser-use:start": "$HOME/.dante-gpt/run-browser-use-mcp.sh", "browser-use:test": "bun run test-browser-use.ts", "briefing:auth": "bun run scripts/ai-briefing.js auth", "briefing:run": "bun run scripts/ai-briefing.js run", "briefing:run:dry": "bun run scripts/ai-briefing.js run --no-print", "briefing:schedule": "bun run scripts/ai-briefing.js schedule", "briefing:unlock": "bun run scripts/ai-briefing.js unlock"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.15", "@ai-sdk/google": "^2.0.13", "@ai-sdk/openai": "^2.0.28", "@ai-sdk/react": "^2.0.39", "@google/genai": "^1.19.0", "@modelcontextprotocol/sdk": "^1.17.5", "@qdrant/js-client-rest": "^1.15.1", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-use-controllable-state": "^1.2.2", "@tailwindcss/typography": "^0.5.16", "@types/dockerode": "^3.3.43", "@types/express-session": "^1.18.2", "@types/jsdom": "^21.1.7", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/pg": "^8.15.5", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "ai": "^5.0.43", "ai-elements": "^1.1.2", "ai-sdk-ollama": "^0.6.0", "better-sqlite3": "^12.2.0", "chalk": "^5.6.2", "cheerio": "^1.1.2", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^14.0.1", "cors": "^2.8.5", "diff": "^8.0.2", "dockerode": "^4.0.8", "dotenv": "^17.2.2", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.2", "framer-motion": "^12.23.12", "google-auth-library": "^10.3.0", "googleapis": "^160.0.0", "gpt-tokenizer": "^3.0.1", "idb": "^8.0.3", "inquirer": "^9.3.7", "ioredis": "^5.7.0", "jose": "^6.1.0", "jsdom": "^26.1.0", "lucide-react": "^0.544.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "openai": "^5.20.1", "ora": "^8.2.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdf-parse": "^1.1.1", "pg": "^8.16.3", "prom-client": "^15.1.3", "proper-lockfile": "^4.1.2", "rate-limit-redis": "^4.2.2", "react-draggable": "^4.5.0", "react-markdown": "^9.1.0", "react-resizable": "^3.0.5", "react-window": "^1.8.11", "recharts": "^3.2.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "rss-parser": "^3.13.0", "streamdown": "^1.3.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.3", "yaml": "^2.8.1", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@types/bun": "^1.2.21", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/node": "^20.19.13", "@types/node-schedule": "^2.1.8", "@types/proper-lockfile": "^4.1.4", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@types/react-resizable": "^3.0.8", "@types/react-window": "^1.8.8", "@types/supertest": "^6.0.2", "@types/multer": "^1.4.11", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "c8": "^10.1.3", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "vite": "^5.4.20"}, "engines": {"bun": ">=1.0.0"}}