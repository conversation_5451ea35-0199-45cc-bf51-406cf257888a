# Repository Guidelines

## Project Structure & Module Organization
- `src/agents/`: Core agents (e.g., `UnifiedDanteAgent.ts`, `ModelOrchestrator.ts`) and workers.
- `src/tools/`: Tool implementations (incl. MCP and computer-use tools).
- `src/api/`: API server entry (`server.ts`).
- `src/ui/`: React UI (components, hooks, styles, stores).
- `src/memory/`: Memory manager and stores.
- `src/utils/`, `src/config/`: Shared utilities and configuration.
- `src/tests/`: Unit/integration tests; fixtures and MCP tests.
- `docs/`, `docker/`, `public/`, `dist/`: Documentation, container assets, static files, builds.

## Build, Test, and Development Commands
- `bun run dev`: Start Vite dev server (UI).
- `bun run dev:api`: Start API server.
- `bun run dev:all`: Run API and UI together.
- `bun run build`: Type-check and build app.
- `bun run start`: Run main entry (`src/index.ts`).
- `bun run cli`: Launch CLI (`src/cli.ts`).
- `bun test`: Run all tests. Example: `bun test src/tests/simpleChunkingTest.test.ts`.
- `bun run typecheck`: TypeScript type-only check.
- Docker helpers: `bun run docker:up`, `docker:down`, `docker:logs`.

## Coding Style & Naming Conventions
- Indentation: 2 spaces; LF EOL; UTF-8 (`.editorconfig`).
- TypeScript: strict mode; use path aliases (`@agents/*`, `@tools/*`, `@utils/*`).
- Semicolons required; prefer `const`/`let`; no default exports for agents.
- Filenames: Agents/components PascalCase (`ResearchAgent.ts`); utilities kebab/camel as already used; tests `*.test.ts`.
- Styles: TailwindCSS; CSS linting via `.stylelintrc.json`.

## Testing Guidelines
- Framework: Bun test runner; tests live in `src/tests/`.
- Naming: Co-locate helpers; test files end with `.test.ts` when possible.
- Run subsets by path/pattern: `bun test src/tests/webSearchTool.integration.test.ts`.
- Coverage (optional): `bunx c8 bun test --reporter=text-summary`.

## Commit & Pull Request Guidelines
- Conventional Commits: `feat:`, `fix:`, `refactor:`, `docs:`, etc. (see `git log`).
- PRs must include: clear description, linked issues, test evidence (logs or screenshots), and updated docs when behavior changes.
- Pre-submit: `bun run typecheck` and `bun test` must pass; include new tests for features/bugs.

## Security & Configuration Tips
- Never commit secrets; use `.env` (copy from `.env.example`).
- If using Qdrant/MCP tooling, start dependencies with `bun run docker:up` and follow docs in `docs/`.
