#!/usr/bin/env node

// ai-briefing.js
// A CLI to manage authentication, trigger an AI-powered briefing, and schedule daily briefings.
// Subcommands: auth, run, schedule
// Default schedule: 8:00 AM daily in the user's local timezone.

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';
import path from 'path';
import os from 'os';
import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import ora from 'ora';
import schedule from 'node-schedule';
import lockfile from 'proper-lockfile';
import dotenv from 'dotenv';
// AI news connector
import { fetchAiNews } from '../src/connectors/ai-news-fetcher.js';

// Ensure we load project .env if present
try {
  dotenv.config();
} catch {}

// ESM __dirname shim
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const program = new Command();
program.name('ai-briefing').description('Manage authentication, trigger a briefing, and schedule daily AI briefings').version('1.0.0');

// Config management
const CONFIG_DIR = path.join(os.homedir(), '.dante-gpt');
const CONFIG_PATH = path.join(CONFIG_DIR, 'ai-briefing.json');
const OUTPUT_DIR = path.join(process.cwd(), 'logs', 'ai-briefings');

function ensureDir(p) {
  fs.mkdirSync(p, { recursive: true });
}

function loadConfig() {
  try {
    if (!fs.existsSync(CONFIG_PATH)) return {};
    const raw = fs.readFileSync(CONFIG_PATH, 'utf-8');
    return JSON.parse(raw);
  } catch (err) {
    console.error(chalk.red(`Failed to read config: ${err.message}`));
    return {};
  }
}

function saveConfig(cfg) {
  try {
    ensureDir(CONFIG_DIR);
    fs.writeFileSync(CONFIG_PATH, JSON.stringify(cfg, null, 2));
  } catch (err) {
    console.error(chalk.red(`Failed to save config: ${err.message}`));
    process.exit(1);
  }
}

function getOpenAIKey(config) {
  // Priority: CLI-provided via config -> env var
  return config.openaiKey || process.env.OPENAI_API_KEY || null;
}

const DEFAULT_AI_FEEDS = [
  // TechCrunch AI
  'https://techcrunch.com/category/artificial-intelligence/feed/',
  // The Verge AI
  'https://www.theverge.com/rss/index.xml?filter=artificial-intelligence',
  // OpenAI Blog
  'https://openai.com/blog/rss.xml',
  // Google AI & Research
  'https://blog.google/technology/ai/rss/',
  // NVIDIA Newsroom (releases)
  'https://nvidianews.nvidia.com/releases.xml'
];

async function fetchAINews(limitPerFeed = 20) {
  const spinner = ora('Fetching AI tech news from RSS feeds').start();
  try {
    const articles = await fetchAiNews({ feedUrls: process.env.AI_NEWS_FEEDS || DEFAULT_AI_FEEDS, maxPerFeed: limitPerFeed });
    spinner.succeed(`Fetched ${articles.length} AI articles`);
    // Map to a compatible shape with scoring heuristics (recentness only for now)
    const scored = articles.map(a => ({
      title: a.title || '(untitled)',
      url: a.url || a.guid || '',
      source: a.source || new URL(a.feedUrl).hostname,
      publishedAt: a.publishedAt || null,
      score: a.publishedAt ? new Date(a.publishedAt).getTime() : 0,
    }));
    return scored;
  } catch (err) {
    spinner.fail('Failed to fetch AI tech news');
    console.error(err?.message || err);
    return [];
  }
}

async function generateSummary(items, config) {
  const openaiKey = getOpenAIKey(config);
  if (!openaiKey) {
    // Fallback: simple non-LLM summary
    const bullets = items.slice(0, 10).map((it) => `- ${it.title} (${it.source})\n  ${it.url}`);
    return `Daily Briefing\n\n${bullets.join('\n')}`;
  }

  // LLM-powered summary using OpenAI
  const { default: OpenAI } = await import('openai');
  const client = new OpenAI({ apiKey: openaiKey });

  const prompt = `Create a concise daily briefing summarizing the key themes and headlines below.\n` +
    `- Prioritize clarity and brevity.\n` +
    `- Group related items.\n` +
    `- Use bullet points.\n` +
    `- Include 5-8 top takeaways formatted for an email news brief.\n\n` +
    items
      .slice(0, 15)
      .map((it, idx) => `${idx + 1}. ${it.title} [${it.source}]\n   ${it.url}`)
      .join('\n');

  try {
    const completion = await client.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        { role: 'system', content: 'You are a world-class news summarizer and briefing assistant.' },
        { role: 'user', content: prompt },
      ],
    });
    const text = completion.choices?.[0]?.message?.content?.trim();
    if (text) return text;
  } catch (err) {
    console.error(chalk.yellow(`OpenAI summarization failed, falling back to simple summary: ${err.message}`));
  }

  const bullets = items.slice(0, 10).map((it) => `- ${it.title} (${it.source})\n  ${it.url}`);
  return `Daily Briefing\n\n${bullets.join('\n')}`;
}

async function runBriefing(options = {}) {
  // Acquire a lock to avoid concurrent runs
  const lockTarget = CONFIG_DIR; // lock the config directory instead of a file
  ensureDir(CONFIG_DIR);
  let release = null;
  try {
    release = await lockfile.lock(lockTarget, { stale: 10 * 60 * 1000, retries: { retries: 3, factor: 1.2, minTimeout: 100, maxTimeout: 500 } });
  } catch (err) {
    // If locking fails, assume another process holds it; avoid noisy ENOENT logs
    console.error(chalk.red('Another briefing process is currently running. Skipping.'));
    return { skipped: true };
  }

  const spinner = ora('Collecting AI news sources').start();
  const config = loadConfig();
  try {
    const aiItems = await fetchAINews(20);
    const items = aiItems
      .sort((a, b) => b.score - a.score)
      .slice(0, 15);
    spinner.succeed('Collected AI sources');

    const summary = await generateSummary(items, config);

    // Write output
    const date = new Date();
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    const outDir = options.outputDir || OUTPUT_DIR;
    ensureDir(outDir);
    const outPath = path.join(outDir, `${yyyy}-${mm}-${dd}.md`);
    fs.writeFileSync(outPath, summary, 'utf-8');

    console.log(chalk.green(`\nBriefing saved to: ${outPath}\n`));
    if (options.print !== false) {
      console.log(summary);
    }
    return { path: outPath, items: items.length };
  } catch (err) {
    spinner.fail('Failed to generate briefing');
    console.error(chalk.red(err.stack || err.message));
    return { error: err };
  } finally {
    if (release) await release();
  }
}

// AUTH COMMAND
program
  .command('auth')
  .description('Manage authentication for AI briefing (e.g., OpenAI API key)')
  .option('--openai-key <key>', 'OpenAI API key (stored in ~/.dante-gpt/ai-briefing.json)')
  .option('--unset <field>', 'Remove a stored field (e.g., openaiKey)')
  .action(async (opts) => {
    const cfg = loadConfig();

    if (opts.unset) {
      if (cfg.hasOwnProperty(opts.unset)) {
        delete cfg[opts.unset];
        saveConfig(cfg);
        console.log(chalk.green(`Removed '${opts.unset}' from config.`));
      } else {
        console.log(chalk.yellow(`Field '${opts.unset}' not found in config.`));
      }
      return;
    }

    if (opts.openaiKey) {
      cfg.openaiKey = opts.openaiKey;
      saveConfig(cfg);
      console.log(chalk.green('OpenAI key saved.'));
      return;
    }

    // Interactive prompt
    const answers = await inquirer.prompt([
      {
        type: 'password',
        name: 'openaiKey',
        message: 'Enter your OpenAI API key (or leave blank to skip):',
        mask: '*',
        default: cfg.openaiKey || '',
      },
    ]);
    const updated = { ...cfg };
    if (answers.openaiKey) updated.openaiKey = answers.openaiKey;

    saveConfig(updated);
    console.log(chalk.green('Authentication settings updated.'));
  });

// RUN COMMAND
program
  .command('run')
  .description('Manually trigger a briefing now')
  .option('-o, --output-dir <dir>', 'Directory to save briefing (default: logs/ai-briefings)')
  .option('--no-print', 'Do not print briefing to console')
  .action(async (opts) => {
    await runBriefing({ outputDir: opts.outputDir, print: opts.print });
  });

// SCHEDULE COMMAND
program
  .command('schedule')
  .description('Schedule a daily briefing (default: 8:00 AM local time). Keeps the process running.')
  .option('-t, --time <HH:MM>', 'Daily time in 24h format (local timezone). Default: 08:00', '08:00')
  .option('--tz <IANA_TZ>', 'IANA timezone (e.g., America/New_York). Defaults to local system timezone.')
  .option('-o, --output-dir <dir>', 'Directory to save briefings (default: logs/ai-briefings)')
  .option('--immediate', 'Also run a briefing immediately upon starting the scheduler', false)
  .option('--cron <CRON_EXPR>', 'Optional cron expression to use instead of HH:MM (e.g., 0 8 * * *)')
  .action(async (opts) => {
    const tz = opts.tz || Intl.DateTimeFormat().resolvedOptions().timeZone;
    let job = null;

    if (opts.immediate) {
      await runBriefing({ outputDir: opts.outputDir });
    }

    if (opts.cron) {
      console.log(chalk.cyan(`Scheduling with cron '${opts.cron}' in timezone ${tz}`));
      job = schedule.scheduleJob({ rule: opts.cron, tz }, async () => {
        console.log(chalk.gray(`\n[${new Date().toLocaleString()}] Running scheduled briefing...`));
        await runBriefing({ outputDir: opts.outputDir, print: false });
      });
    } else {
      const [HH, MM] = String(opts.time || '08:00').split(':');
      const hour = parseInt(HH, 10);
      const minute = parseInt(MM, 10);
      if (Number.isNaN(hour) || Number.isNaN(minute) || hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        console.error(chalk.red(`Invalid time '${opts.time}'. Use HH:MM in 24h format.`));
        process.exit(1);
      }
      const rule = new schedule.RecurrenceRule();
      rule.tz = tz;
      rule.hour = hour;
      rule.minute = minute;
      rule.second = 0;

      console.log(chalk.cyan(`Scheduling daily briefing at ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')} (${tz})`));
      job = schedule.scheduleJob(rule, async () => {
        console.log(chalk.gray(`\n[${new Date().toLocaleString()}] Running scheduled briefing...`));
        await runBriefing({ outputDir: opts.outputDir, print: false });
      });
    }

    if (!job) {
      console.error(chalk.red('Failed to schedule job.'));
      process.exit(1);
    }

    console.log(chalk.green('Scheduler is running. Press Ctrl+C to exit.'));
    // Keep process alive
  });

// UNLOCK COMMAND
program
  .command('unlock')
  .description('Force-release the briefing lock (use if a previous run crashed).')
  .action(async () => {
    try {
      ensureDir(CONFIG_DIR);
      // Try proper-lockfile unlock first
      try {
        await lockfile.unlock(CONFIG_DIR);
        console.log(chalk.green('Released briefing lock via proper-lockfile.'));
      } catch (err) {
        // Fall back to removing common lock artifacts
        const possibleLocks = [
          path.join(CONFIG_DIR, 'lockfile'),
          path.join(CONFIG_DIR, 'ai-briefing.lock'),
          path.join(process.env.HOME || os.homedir(), '.dante-gpt.lock'),
          path.join(CONFIG_DIR, '.lock')
        ];
        for (const p of possibleLocks) {
          try { fs.rmSync(p, { force: true }); } catch {}
        }
        console.log(chalk.yellow('Cleared lock artifacts (if any).'));
      }
    } catch (err) {
      console.error(chalk.red(`Failed to unlock: ${err?.message || err}`));
      process.exit(1);
    }
  });

// Parse args
program.parseAsync(process.argv);
