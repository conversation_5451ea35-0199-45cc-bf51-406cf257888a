// Local smoke test for reasoning extraction/formatting without network
import { extractReasoningFromResponse, formatReasoningForDisplay } from '../src/utils/extractReasoning';

async function main() {
  // Case 1: Stream-like object with response containing thinking
  const streamLike = {
    // Mimic Vercel stream.response Promise
    response: Promise.resolve({
      model: 'gemini-2.5-flash',
      thinking: 'Evaluated options and chose the simplest correct approach.',
      text: 'Hello! This is a demo response.',
    }),
  } as any;

  const resp1 = await (streamLike as any).response;
  const r1 = extractReasoningFromResponse(resp1) ||
    ((resp1 as any)?.thinking || (resp1 as any)?.reasoning
      ? { reasoning: (resp1 as any).thinking || (resp1 as any).reasoning, data: resp1 }
      : null);
  console.log('\n---- Case 1: response.thinking ----');
  console.log(formatReasoningForDisplay(r1));

  // Case 2: State-like object with reasoning
  const stateLike = {
    state: {
      model: 'gemini-2.5-flash',
      thinking: 'Considered edge-cases and verified constraints.',
      output: [{ content: 'All good.' }],
    },
  } as any;
  const st = stateLike.state as any;
  const r2 = extractReasoningFromResponse(st) ||
    (st?.thinking || st?.reasoning
      ? { reasoning: st.thinking || st.reasoning, data: st }
      : null);
  console.log('\n---- Case 2: state.thinking ----');
  console.log(formatReasoningForDisplay(r2));

  // Case 3: Final text with inline REASONING: marker
  const finalText = 'REASONING: Thought through steps carefully. ANSWER: 42';
  const r3 = extractReasoningFromResponse(finalText);
  console.log('\n---- Case 3: inline REASONING ----');
  console.log(formatReasoningForDisplay(r3));
}

main().catch((e) => {
  console.error('Smoke test error:', e);
  process.exit(1);
});
