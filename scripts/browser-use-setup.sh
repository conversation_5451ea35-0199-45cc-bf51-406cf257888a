#!/bin/bash

# Browser-Use MCP Server Setup Script
# This script sets up the Python environment and installs browser-use MCP server

set -e  # Exit on error

echo "🌐 Browser-Use MCP Server Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Select preferred Python (favor 3.11/3.12 for best compatibility)
PY_BIN=""
for CAND in python3.11 python3.12 python3; do
  if command -v "$CAND" >/dev/null 2>&1; then
    PY_BIN="$CAND"
    break
  fi
done

if [ -z "$PY_BIN" ]; then
  echo -e "${RED}❌ Python 3 is not installed. Please install Python 3.11+ first.${NC}"
  exit 1
fi

# Get Python version
PYTHON_VERSION=$($PY_BIN --version | cut -d' ' -f2)
echo -e "${GREEN}✅ Using $PY_BIN ($PYTHON_VERSION)${NC}"

# Create virtual environment directory if it doesn't exist
VENV_DIR="$HOME/.dante-gpt/browser-use-venv"
echo -e "\n${YELLOW}📦 Setting up Python virtual environment...${NC}"

if [ ! -d "$VENV_DIR" ]; then
    echo "Creating virtual environment at $VENV_DIR"
    $PY_BIN -m venv "$VENV_DIR"
    echo -e "${GREEN}✅ Virtual environment created${NC}"
else
    echo -e "${GREEN}✅ Virtual environment already exists${NC}"
fi

# Activate virtual environment
source "$VENV_DIR/bin/activate"
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Upgrade pip
echo -e "\n${YELLOW}📦 Upgrading pip...${NC}"
pip install --upgrade pip --quiet

# Install uv if not already installed
if ! command -v uv &> /dev/null; then
    echo -e "\n${YELLOW}📦 Installing uv package manager...${NC}"
    pip install uv --quiet
    echo -e "${GREEN}✅ uv installed${NC}"
else
    echo -e "${GREEN}✅ uv already installed${NC}"
fi

# Install browser-use with CLI support
echo -e "\n${YELLOW}📦 Installing browser-use with MCP support...${NC}"
# Use pip directly instead of uv for browser-use
pip install "browser-use[cli]"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ browser-use installed${NC}"
else
    echo -e "${RED}❌ Failed to install browser-use${NC}"
    exit 1
fi

# Install Playwright browsers
echo -e "\n${YELLOW}🎭 Installing Playwright browsers...${NC}"
playwright install chromium
echo -e "${GREEN}✅ Chromium browser installed${NC}"

# Create browser-use data directory
BROWSER_USE_DIR="$HOME/.browser-use-mcp"
if [ ! -d "$BROWSER_USE_DIR" ]; then
    mkdir -p "$BROWSER_USE_DIR"
    echo -e "${GREEN}✅ Created browser-use data directory at $BROWSER_USE_DIR${NC}"
fi

# Create downloads directory
DOWNLOADS_DIR="$HOME/Downloads/browser-use-mcp"
if [ ! -d "$DOWNLOADS_DIR" ]; then
    mkdir -p "$DOWNLOADS_DIR"
    echo -e "${GREEN}✅ Created downloads directory at $DOWNLOADS_DIR${NC}"
fi

# Test the installation
echo -e "\n${YELLOW}🧪 Testing browser-use installation...${NC}"
# First check if module exists
if python -c "import sys; sys.path.append('$VENV_DIR/lib/python3.*/site-packages'); import browser_use" 2>/dev/null; then
    echo -e "${GREEN}✅ browser-use module found${NC}"
    # Try to get version if available
    python -c "
try:
    import browser_use
    if hasattr(browser_use, '__version__'):
        print(f'Version: {browser_use.__version__}')
    else:
        print('Version info not available')
except Exception as e:
    print(f'Module loaded but version check failed: {e}')
" 2>/dev/null || true
else
    echo -e "${YELLOW}⚠️ browser-use module import test failed, checking installation...${NC}"
    # Check if package is installed
    if pip show browser-use >/dev/null 2>&1; then
        echo -e "${GREEN}✅ browser-use package is installed${NC}"
        pip show browser-use | grep -E "Name:|Version:|Location:"
    else
        echo -e "${RED}❌ browser-use is not installed${NC}"
        exit 1
    fi
fi

# Create a wrapper script for running browser-use MCP server
WRAPPER_SCRIPT="$HOME/.dante-gpt/run-browser-use-mcp.sh"
cat > "$WRAPPER_SCRIPT" << 'EOF'
#!/bin/bash
# Browser-Use MCP Server Runner

# Activate virtual environment
source "$HOME/.dante-gpt/browser-use-venv/bin/activate"

# Set environment variables
export BROWSER_USE_DATA_DIR="$HOME/.browser-use-mcp"
export BROWSER_USE_DOWNLOADS_DIR="$HOME/Downloads/browser-use-mcp"

# Check for OPENAI_API_KEY if provided
if [ -n "$OPENAI_API_KEY" ]; then
    export OPENAI_API_KEY="$OPENAI_API_KEY"
fi

# Check if running as MCP server or fallback to CLI
if python -c "import browser_use.mcp.server" 2>/dev/null; then
    exec python -m browser_use.mcp.server
else
    # Fallback to CLI mode if MCP module not found
    exec python -m browser_use --mcp
fi
EOF

chmod +x "$WRAPPER_SCRIPT"
echo -e "${GREEN}✅ Created wrapper script at $WRAPPER_SCRIPT${NC}"

# Test MCP server availability
echo -e "\n${YELLOW}🧪 Testing MCP server module...${NC}"
if python -c "import browser_use.mcp.server" 2>/dev/null; then
    echo -e "${GREEN}✅ MCP server module is available${NC}"
elif python -c "import browser_use" 2>/dev/null; then
    echo -e "${YELLOW}⚠️ browser-use installed but MCP server module not found${NC}"
    echo -e "${YELLOW}   Will use CLI mode as fallback${NC}"
else
    echo -e "${RED}❌ browser-use not properly installed${NC}"
fi

# Display completion message
echo -e "\n${GREEN}🎉 Browser-Use MCP Server Setup Complete!${NC}"
echo -e "\nTo use the browser-use MCP server:"
echo -e "1. The server can be started with: ${YELLOW}$WRAPPER_SCRIPT${NC}"
echo -e "2. It's configured to work with Dante's MCP system"
echo -e "3. Set OPENAI_API_KEY in your .env file for AI content extraction"
echo -e "\nData directories:"
echo -e "  • Browser data: $BROWSER_USE_DIR"
echo -e "  • Downloads: $DOWNLOADS_DIR"
echo -e "  • Virtual env: $VENV_DIR"

# Deactivate virtual environment
deactivate

echo -e "\n✨ Setup complete!"
