#!/usr/bin/env bun
import { Command } from 'commander';
import { z } from 'zod';
import fs from 'fs';
import path from 'path';

const ReplayOptions = z.object({
  runId: z.string().min(1, 'Run ID is required'),
});

type EventType =
  | 'run-start'
  | 'run-end'
  | 'tool-call'
  | 'tool-result'
  | 'model-response'
  | 'agent-state-change'
  | 'trace'
  | 'log'
  | 'unknown';

type AgentStatus = 'idle' | 'active' | 'thinking' | 'waiting-tool' | 'completed' | 'error' | string;

interface ReplayEvent {
  type: EventType;
  runId?: string;
  timestamp?: number;
  raw: any;
  toolCall?: {
    id: string;
    name: string;
    args: any;
  };
  toolResult?: {
    id: string;
    result: any;
    error?: any;
  };
  modelResponse?: {
    text?: string;
    model?: string;
    tokens?: number;
    raw?: any;
  };
  stateChange?: {
    from?: AgentStatus;
    to: AgentStatus;
    reason?: string;
  };
}

interface ReplayState {
  runId: string;
  currentStep: number;
  currentStatus: AgentStatus;
  pendingToolCalls: Map<string, { name: string; args: any; startedAt?: number; logIndex: number }>;
  toolHistory: Array<{
    id: string;
    name: string;
    args: any;
    result?: any;
    error?: any;
    startedAt?: number;
    finishedAt?: number;
  }>;
  stateTransitions: Array<{ from?: AgentStatus; to: AgentStatus; timestamp?: number; reason?: string }>;
  modelResponses: Array<{ text?: string; model?: string; tokens?: number; timestamp?: number }>;
  inconsistencies: string[];
  unknownEvents: number;
}

function safeNumberTimestamp(ts: unknown): number | undefined {
  if (typeof ts === 'number' && Number.isFinite(ts)) return ts;
  if (typeof ts === 'string') {
    const n = Date.parse(ts);
    if (!Number.isNaN(n)) return n;
    const asNum = Number(ts);
    if (!Number.isNaN(asNum)) return asNum;
  }
  return undefined;
}

function normalizeEvent(raw: any, index: number): ReplayEvent {
  const type: EventType = (raw?.type as EventType) ?? 'unknown';
  const timestamp = safeNumberTimestamp(raw?.timestamp);
  const runId: string | undefined = raw?.runId ?? raw?.sessionId ?? raw?.id;

  // Common helpers
  const data = raw?.data ?? raw;

  if (type === 'tool-call' || data?.event === 'tool-call') {
    const id =
      data?.callId ??
      data?.id ??
      raw?.callId ??
      raw?.id ??
      `auto-toolcall-${index}`;
    const name = data?.tool ?? data?.name ?? raw?.tool ?? 'unknown-tool';
    const args = data?.args ?? raw?.args ?? {};
    return {
      type: 'tool-call',
      runId,
      timestamp,
      raw,
      toolCall: { id: String(id), name: String(name), args },
    };
  }

  if (type === 'tool-result' || data?.event === 'tool-result') {
    const id =
      data?.callId ?? data?.id ?? raw?.callId ?? raw?.id ?? `unknown-${index}`;
    const result = data?.result ?? raw?.result;
    const error = data?.error ?? raw?.error;
    return {
      type: 'tool-result',
      runId,
      timestamp,
      raw,
      toolResult: { id: String(id), result, error },
    };
  }

  if (type === 'model-response' || data?.event === 'model-response') {
    const text =
      data?.text ??
      data?.response?.text ??
      raw?.text ??
      raw?.response?.text ??
      undefined;
    const model = data?.model ?? raw?.model ?? data?.response?.model;
    const tokens = data?.tokens?.used ?? data?.tokens ?? raw?.tokens;
    return {
      type: 'model-response',
      runId,
      timestamp,
      raw,
      modelResponse: { text, model, tokens, raw: data?.response ?? raw?.response ?? raw },
    };
  }

  if (type === 'agent-state-change' || data?.event === 'agent-state-change') {
    const from: AgentStatus | undefined = data?.from ?? raw?.from;
    const to: AgentStatus = (data?.to ?? raw?.to ?? 'active') as AgentStatus;
    const reason = data?.reason ?? raw?.reason;
    return {
      type: 'agent-state-change',
      runId,
      timestamp,
      raw,
      stateChange: { from, to, reason },
    };
  }

  if (type === 'run-start' || data?.event === 'run-start') {
    return { type: 'run-start', runId, timestamp, raw };
  }
  if (type === 'run-end' || data?.event === 'run-end') {
    return { type: 'run-end', runId, timestamp, raw };
  }

  // Fallbacks for UI/trace/log style events
  if (typeof raw?.type === 'string' && ['trace', 'log'].includes(raw.type)) {
    return { type: raw.type as EventType, runId, timestamp, raw };
  }

  return { type: 'unknown', runId, timestamp, raw };
}

function isAllowedTransition(from: AgentStatus, to: AgentStatus): boolean {
  if (from === to) return true;
  // Generic guardrails; allow unknown custom states but flag clearly invalid patterns
  const allowed: Record<string, AgentStatus[]> = {
    idle: ['thinking', 'active', 'waiting-tool', 'completed', 'error'],
    thinking: ['active', 'waiting-tool', 'completed', 'error'],
    active: ['thinking', 'waiting-tool', 'completed', 'error'],
    'waiting-tool': ['active', 'thinking', 'completed', 'error'],
    completed: [],
    error: [],
  };
  const nexts = allowed[from] ?? ['thinking', 'active', 'waiting-tool', 'completed', 'error'];
  return nexts.includes(to);
}

async function main() {
  const program = new Command();
  program
    .name('run-session-replay')
    .description('Replay a session from structured logs to debug agent runs.')
    .requiredOption('--run-id <runId>', 'The ID of the run to replay')
    .parse(process.argv);

  const options = program.opts();
  const parsedOptions = ReplayOptions.safeParse({ runId: options.runId });

  if (!parsedOptions.success) {
    console.error('Invalid options:', parsedOptions.error.flatten().fieldErrors);
    process.exit(1);
  }

  const { runId } = parsedOptions.data;

  console.log(`Starting replay for run ID: ${runId}`);

  try {
    await replaySession(runId);
  } catch (error) {
    console.error(`Failed to replay session for run ID ${runId}:`, error);
    process.exit(1);
  }
}

async function replaySession(runId: string) {
  console.log(`Fetching logs for run ID: ${runId}`);

  const logs = await fetchLogsForRun(runId);

  if (!logs || logs.length === 0) {
    console.warn(`No logs found for run ID: ${runId}`);
    return;
  }

  // Normalize events and sort by timestamp when available, otherwise keep file order
  const events = logs.map((raw, idx) => ({ evt: normalizeEvent(raw, idx), idx }));
  events.sort((a, b) => {
    const ta = a.evt.timestamp ?? a.idx;
    const tb = b.evt.timestamp ?? b.idx;
    return ta - tb;
  });

  const state: ReplayState = {
    runId,
    currentStep: 0,
    currentStatus: 'idle',
    pendingToolCalls: new Map(),
    toolHistory: [],
    stateTransitions: [],
    modelResponses: [],
    inconsistencies: [],
    unknownEvents: 0,
  };

  console.log(`Replaying ${events.length} log entries...`);

  for (let i = 0; i < events.length; i++) {
    const { evt } = events[i];
    state.currentStep += 1;

    switch (evt.type) {
      case 'run-start': {
        // Optional hook
        break;
      }
      case 'run-end': {
        // Optional hook
        break;
      }
      case 'agent-state-change': {
        const from = evt.stateChange?.from ?? state.currentStatus;
        const to = evt.stateChange?.to ?? state.currentStatus;
        if (evt.stateChange?.from && evt.stateChange.from !== state.currentStatus) {
          state.inconsistencies.push(
            `State change 'from' mismatch at step ${state.currentStep}: log.from='${evt.stateChange.from}' but simulated='${state.currentStatus}'`
          );
        }
        if (!isAllowedTransition(from, to)) {
          state.inconsistencies.push(
            `Illegal state transition at step ${state.currentStep}: '${from}' -> '${to}'`
          );
        }
        state.stateTransitions.push({
          from,
          to,
          timestamp: evt.timestamp,
          reason: evt.stateChange?.reason,
        });
        state.currentStatus = to;
        break;
      }
      case 'model-response': {
        state.modelResponses.push({
          text: evt.modelResponse?.text,
          model: evt.modelResponse?.model,
          tokens: evt.modelResponse?.tokens,
          timestamp: evt.timestamp,
        });
        // Heuristic: model response indicates thinking or active
        if (state.currentStatus === 'idle' || state.currentStatus === 'waiting-tool') {
          state.currentStatus = 'thinking';
        } else if (state.currentStatus === 'thinking') {
          state.currentStatus = 'active';
        }
        break;
      }
      case 'tool-call': {
        const call = evt.toolCall!;
        const existing = state.pendingToolCalls.get(call.id);
        if (existing) {
          state.inconsistencies.push(
            `Duplicate tool-call ID '${call.id}' at step ${state.currentStep}`
          );
        }
        state.pendingToolCalls.set(call.id, {
          name: call.name,
          args: call.args,
          startedAt: evt.timestamp,
          logIndex: i,
        });
        state.toolHistory.push({
          id: call.id,
          name: call.name,
          args: call.args,
          startedAt: evt.timestamp,
        });
        // Move to waiting-tool if we weren't already
        state.currentStatus = 'waiting-tool';
        break;
      }
      case 'tool-result': {
        const res = evt.toolResult!;
        const pending = state.pendingToolCalls.get(res.id);
        if (!pending) {
          // Fallback: match last unfinished tool in history
          const lastUnfinished = [...state.toolHistory]
            .reverse()
            .find((t) => t.result === undefined && t.error === undefined);
          if (lastUnfinished) {
            // Warn if IDs differ
            if (lastUnfinished.id !== res.id) {
              state.inconsistencies.push(
                `Tool-result ID '${res.id}' does not match last pending call '${lastUnfinished.id}' at step ${state.currentStep}; associating by order`
              );
            }
            lastUnfinished.result = res.result;
            lastUnfinished.error = res.error;
            lastUnfinished.finishedAt = evt.timestamp;
          } else {
            state.inconsistencies.push(
              `Tool-result without preceding tool-call (id='${res.id}') at step ${state.currentStep}`
            );
          }
        } else {
          // Close out the matching call
          const hist = state.toolHistory.find((t) => t.id === res.id);
          if (hist) {
            hist.result = res.result;
            hist.error = res.error;
            hist.finishedAt = evt.timestamp;
          } else {
            state.inconsistencies.push(
              `Pending tool-call '${res.id}' not found in history at step ${state.currentStep}`
            );
          }
          state.pendingToolCalls.delete(res.id);
        }
        // After a result, agent likely resumes active/thinking
        state.currentStatus = res.error ? 'error' : 'active';
        break;
      }
      case 'trace':
      case 'log': {
        // No-op for replay; informational only
        break;
      }
      case 'unknown':
      default: {
        state.unknownEvents += 1;
        break;
      }
    }
  }

  // Final verification
  if (state.pendingToolCalls.size > 0) {
    const pendingIds = [...state.pendingToolCalls.keys()].join(', ');
    state.inconsistencies.push(
      `Replay ended with ${state.pendingToolCalls.size} pending tool-call(s): ${pendingIds}`
    );
  }

  if (state.currentStatus !== 'completed' && state.currentStatus !== 'error') {
    // Not necessarily an inconsistency, but report if no terminal state seen
    // Only flag if we saw a run-end event and still not in a terminal state
    const sawRunEnd = events.some((e) => e.evt.type === 'run-end');
    if (sawRunEnd) {
      state.inconsistencies.push(
        `Run ended but final status was '${state.currentStatus}' (expected 'completed' or 'error')`
      );
    }
  }

  printSummary(state);
}

function printSummary(state: ReplayState) {
  const toolCount = state.toolHistory.length;
  const completedTools = state.toolHistory.filter((t) => t.result !== undefined || t.error !== undefined).length;
  const erroredTools = state.toolHistory.filter((t) => t.error !== undefined).length;

  const header = [
    '==================== REPLAY SUMMARY ====================',
    `Run ID: ${state.runId}`,
    `Steps processed: ${state.currentStep}`,
    `Final status: ${state.currentStatus}`,
    `Model responses: ${state.modelResponses.length}`,
    `Tool calls: ${toolCount} (completed: ${completedTools}, errors: ${erroredTools})`,
    `State transitions: ${state.stateTransitions.length}`,
    `Unknown events: ${state.unknownEvents}`,
    `Inconsistencies: ${state.inconsistencies.length}`,
    '========================================================',
  ].join('\n');

  console.log('\n' + header + '\n');

  if (state.toolHistory.length > 0) {
    console.log('Tool call sequence:');
    state.toolHistory.forEach((t, idx) => {
      const status = t.error
        ? 'ERROR'
        : t.result !== undefined
        ? 'OK'
        : 'PENDING';
      console.log(
        `  ${idx + 1}. ${t.name} [id=${t.id}] - ${status}` +
          (status !== 'PENDING' ? ` (duration=${formatDuration(t.startedAt, t.finishedAt)})` : '')
      );
    });
    console.log('');
  }

  if (state.stateTransitions.length > 0) {
    console.log('State transitions:');
    state.stateTransitions.forEach((s, idx) => {
      const time = s.timestamp ? new Date(s.timestamp).toISOString() : 'n/a';
      console.log(`  ${idx + 1}. ${s.from ?? '(unspecified)'} -> ${s.to} @ ${time}${s.reason ? ` (${s.reason})` : ''}`);
    });
    console.log('');
  }

  if (state.inconsistencies.length > 0) {
    console.log('Inconsistencies detected:');
    state.inconsistencies.forEach((m, idx) => console.log(`  ${idx + 1}. ${m}`));
    console.log('');
  }
}

function formatDuration(start?: number, end?: number): string {
  if (!start || !end) return 'n/a';
  const ms = Math.max(0, end - start);
  if (ms < 1000) return `${ms}ms`;
  const s = ms / 1000;
  if (s < 60) return `${s.toFixed(2)}s`;
  const m = Math.floor(s / 60);
  const rem = (s % 60).toFixed(2);
  return `${m}m ${rem}s`;
}

/**
 * Fetch logs for a given runId.
 * - Reads all files in ./logs matching .log or .jsonl
 * - Parses JSON lines (ignores malformed lines)
 * - Filters by runId or sessionId equal to the provided runId
 */
async function fetchLogsForRun(runId: string): Promise<any[]> {
  const logDir = path.join(process.cwd(), 'logs');

  let entries: string[] = [];
  try {
    const files = await fs.promises.readdir(logDir);
    const candidates = files.filter((f) => f.endsWith('.log') || f.endsWith('.jsonl'));
    if (candidates.length === 0) {
      // Fallback to the assumed filename if directory exists but no matches found
      candidates.push('dante-combined.log');
    }
    for (const file of candidates) {
      const full = path.join(logDir, file);
      try {
        const stat = await fs.promises.stat(full);
        if (!stat.isFile()) continue;
        const content = await fs.promises.readFile(full, 'utf-8');
        const lines = content.split('\n');
        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed) continue;
          try {
            const obj = JSON.parse(trimmed);
            const id = obj?.runId ?? obj?.sessionId;
            if (id === runId) {
              entries.push(trimmed);
            }
          } catch {
            // Ignore non-JSON lines
          }
        }
      } catch {
        // Ignore missing or unreadable files
      }
    }
  } catch {
    // logs directory might not exist
  }

  if (entries.length === 0) {
    const logFilePath = path.join(logDir, `dante-combined.log`);
    try {
      await fs.promises.access(logFilePath);
    } catch {
      console.error(`Log file not found at: ${logFilePath}`);
      return [];
    }
    const logFileContent = await fs.promises.readFile(logFilePath, 'utf-8');
    entries = logFileContent.split('\n').filter((l) => l.trim() !== '');
  }

  const logs: any[] = [];
  for (const line of entries) {
    try {
      const obj = typeof line === 'string' ? JSON.parse(line) : line;
      if ((obj?.runId ?? obj?.sessionId) === runId) {
        logs.push(obj);
      }
    } catch {
      // skip malformed
    }
  }
  return logs;
}

main().catch((error) => {
  console.error('An unexpected error occurred:', error);
  process.exit(1);
});
