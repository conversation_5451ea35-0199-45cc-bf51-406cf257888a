// PDF Extraction Utility
// Wraps pdf-parse with safe path enforcement to restrict access to the uploads/ directory by default.
// ESM module (.mjs) using createRequire to import CommonJS pdf-parse reliably.

import { readFile, stat, realpath } from 'node:fs/promises';
import { createRequire } from 'node:module';
import path from 'node:path';

const require = createRequire(import.meta.url);

// pdf-parse is a CommonJS module; using require avoids interop quirks
let pdfParse;
try {
  pdfParse = require('pdf-parse');
} catch (err) {
  // Defer throwing until function call so module import doesn't crash dependents
  pdfParse = null;
}

/**
 * Resolve a file path and ensure it is within the allowed uploads directory.
 * By default, the allowed base directory is `${process.cwd()}/uploads`.
 * You can override via the UPLOADS_DIR environment variable.
 *
 * @param {string} filePath - User-provided path (relative or absolute)
 * @returns {Promise<{ absPath: string, size: number }>}
 */
async function resolveAndValidateFile(filePath) {
  if (!filePath || typeof filePath !== 'string') {
    throw new TypeError('filePath must be a non-empty string');
  }

  const providedPath = path.isAbsolute(filePath)
    ? filePath
    : path.resolve(process.cwd(), filePath);

  const baseDirEnv = process.env.UPLOADS_DIR;
  const defaultBase = path.resolve(process.cwd(), 'uploads');
  const baseDir = baseDirEnv && baseDirEnv.trim() ? baseDirEnv : defaultBase;

  // Use realpath to resolve symlinks and normalize
  const [realProvided, realBase] = await Promise.all([
    // readFile will throw if not found; resolve realpath first for security checks
    // Note: realpath will throw if the path does not exist. We map this to a user-friendly error below.
    (async () => {
      try {
        return await realpath(providedPath);
      } catch (e) {
        if (e && e.code === 'ENOENT') {
          const relHint = path.relative(process.cwd(), providedPath);
          throw new Error(`File not found: ${providedPath} (relative: ${relHint})`);
        }
        throw e;
      }
    })(),
    realpath(baseDir),
  ]);

  // Ensure the file resides within the allowed base directory
  const isWithin = realProvided === realBase || realProvided.startsWith(realBase + path.sep);
  if (!isWithin) {
    const relToBase = path.relative(realBase, realProvided);
    throw Object.assign(
      new Error(
        `Access denied: Path is outside the allowed uploads directory. ` +
          `Provided resolves to ${realProvided}. Allowed base is ${realBase}. ` +
          `Move the file into the uploads directory or set UPLOADS_DIR to a safe path.`
      ),
      { code: 'EACCESS_OUTSIDE_UPLOADS', details: { providedPath: realProvided, baseDir: realBase, relToBase } }
    );
  }

  const st = await stat(realProvided);
  if (!st.isFile()) {
    throw new Error(`Path is not a regular file: ${realProvided}`);
  }

  return { absPath: realProvided, size: st.size };
}

/**
 * Extract text and metadata from a PDF file using pdf-parse.
 *
 * Security: Only files under the uploads directory are allowed by default.
 * Set UPLOADS_DIR env var to change the allowed base directory.
 *
 * @param {Object} options
 * @param {string} options.filePath - Path to the PDF (relative to CWD or absolute). Must reside under uploads/ by default.
 * @param {number} [options.maxPages] - Maximum number of pages to parse (pdf-parse "max" option)
 * @param {string} [options.password] - Password for encrypted PDFs
 *
 * @returns {Promise<{ file: string, size: number, numpages: number, info: any, metadata: any, text: string }>}
 */
export async function extractPdf({ filePath, maxPages, password } = {}) {
  if (!pdfParse) {
    throw new Error(
      'pdf-parse module is not installed or failed to load. Please add it to your dependencies: npm i pdf-parse'
    );
  }

  if (maxPages !== undefined) {
    const n = Number(maxPages);
    if (!Number.isFinite(n) || n <= 0 || !Number.isInteger(n)) {
      throw new TypeError('maxPages must be a positive integer when provided');
    }
  }
  if (password !== undefined && typeof password !== 'string') {
    throw new TypeError('password must be a string when provided');
  }

  const { absPath, size } = await resolveAndValidateFile(filePath);
  const buffer = await readFile(absPath);

  const parseOptions = {};
  if (maxPages !== undefined) parseOptions.max = maxPages;
  if (password !== undefined) parseOptions.password = password;

  let data;
  try {
    data = await pdfParse(buffer, parseOptions);
  } catch (err) {
    // Enhance common error cases
    if (String(err?.message || '').toLowerCase().includes('password')) {
      err.code = err.code || 'EPDF_PASSWORD_REQUIRED';
    }
    throw err;
  }

  return {
    file: absPath,
    size,
    numpages: Number(data.numpages) || 0,
    info: data.info ?? null,
    metadata: data.metadata ? data.metadata._metadata || data.metadata : null,
    text: typeof data.text === 'string' ? data.text : '',
  };
}

export default extractPdf;

/**
 * Usage example (node):
 *
 * import { extractPdf } from './scripts/lib/pdf-extractor.mjs';
 * const result = await extractPdf({ filePath: 'uploads/my.pdf', maxPages: 3 });
 * console.log(result.text);
 */
