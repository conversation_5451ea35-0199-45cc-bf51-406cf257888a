import os
import time
import requests
import glob

# Configuration
SLACK_WEBHOOK_URL = (
    "YOUR_SLACK_WEBHOOK_URL_HERE"  # Replace with your actual Slack Webhook URL
)
BRIEFINGS_DIR = "logs/ai-briefings/"
SENT_BRIEFINGS_DIR = "logs/ai-briefings/sent/"


def send_to_slack(title, message):
    """Sends a message to <PERSON>lack."""
    if not SLACK_WEBHOOK_URL or SLACK_WEBHOOK_URL == "YOUR_SLACK_WEBHOOK_URL_HERE":
        print("Slack webhook URL is not configured. Skipping Slack notification.")
        return

    payload = {"text": f"*{title}*\n\n{message}", "mrkdwn": True}
    try:
        response = requests.post(SLACK_WEBHOOK_URL, json=payload)
        response.raise_for_status()
        print(f"Successfully sent briefing to <PERSON>lack: {title}")
    except requests.exceptions.RequestException as e:
        print(f"Error sending briefing to <PERSON>lack: {e}")


def process_new_briefings():
    """Checks for new briefings and sends them to Slack."""
    os.makedirs(SENT_BRIEFINGS_DIR, exist_ok=True)

    markdown_files = sorted(
        glob.glob(os.path.join(BRIEFINGS_DIR, "*.md")), key=os.path.getctime
    )

    for filepath in markdown_files:
        filename = os.path.basename(filepath)
        sent_filepath = os.path.join(SENT_BRIEFINGS_DIR, filename)

        if not os.path.exists(sent_filepath):  # Only process if not already sent
            print(f"New briefing detected: {filename}")
            try:
                with open(filepath, "r", encoding="utf-8") as f:
                    content = f.read()

                # Assuming the first line is the title, or we can derive it
                title = os.path.splitext(filename)[0].replace("_", " ").title()

                send_to_slack(title, content)

                # Move the file to the 'sent' directory after processing
                os.rename(filepath, sent_filepath)
                print(f"Moved {filename} to {SENT_BRIEFINGS_DIR}")

            except Exception as e:
                print(f"Error processing {filename}: {e}")


if __name__ == "__main__":
    print(f"Monitoring {BRIEFINGS_DIR} for new briefings...")
    # In a real-world scenario, this would run continuously or as a cron job.
    # For this demonstration, we'll run it once.
    process_new_briefings()
    print("Monitoring complete for this run.")
