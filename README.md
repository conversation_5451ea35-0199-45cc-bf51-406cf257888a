# Dante - The Digital Assistant and Technical Expert

An advanced AI assistant powered by ChatGPT and Gemini, capable of handling everything from simple queries to complex development tasks.

## Features

- **Multi-Agent System**: Specialized agents for different tasks
  - Research Agent - Web search and documentation lookup
  - Code Generation Agent - Create applications and websites
  - Code Refactor Agent - Optimize existing code
  - Debug Agent - Identify and fix bugs
  - Security Analysis Agent - Vulnerability assessment
  - Code Review Agent - Quality assessment
  - Weather Agent - Weather information and forecasts

- **Multiple Interfaces**
  - CLI for terminal usage
  - Web UI for browser access
  - API for integration

- **Real-time Streaming**: Get responses as they're generated
- **Context Management**: Maintains conversation history
- **Tool Integration**: Web search, file operations, code execution

## Prerequisites

- [Bun](https://bun.sh/) runtime (v1.0.0 or later)
- OpenAI API key with GPT-5 access
- Node.js (for compatibility with some packages)
- Optional: [Ollama](https://ollama.com) for local/offline background LLM tasks (see docs/ollama.md)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/dante-gpt.git
cd dante-gpt
```

2. Install dependencies:
```bash
bun install
```

3. Configure environment:
```bash
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:
```
OPENAI_API_KEY=sk-your-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
```

## Usage

### CLI Interface

Run Dante in your terminal:

```bash
bun run cli
```

Commands:
- `help` - Show available commands
- `clear` - Clear conversation history
- `history` - View conversation history
- `exit` - Exit the CLI

### Web Interface

Start both the API server and web development server:

```bash
# Option 1: Run both servers together
bun run dev:all

# Option 2: Run in separate terminals
# Terminal 1:
bun run dev:api

# Terminal 2:
bun run dev
```

Open http://localhost:3002 in your browser.

### API Server Only

Start just the API server:

```bash
bun run api
```

The API will be available at http://localhost:3001

#### API Endpoints

- `POST /api/chat` - Send messages to Dante
  ```json
  {
    "messages": [
      {"role": "user", "content": "Your message here"}
    ]
  }
  ```

- `GET /api/health` - Health check endpoint

## Development

### Project Structure

```
dante-gpt/
├── src/
│   ├── agents/         # Specialized AI agents
│   ├── tools/          # Tool implementations
│   ├── ui/             # React components
│   ├── api/            # API server
│   ├── utils/          # Utility functions
│   ├── index.ts        # Main entry point
│   └── cli.ts          # CLI interface
├── docs/               # Documentation
├── package.json        # Dependencies
└── vite.config.ts      # Vite configuration
```

### Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run cli` - Run CLI interface
- `bun run start` - Run main application
- `bun test` - Run tests

### Adding New Agents

1. Create a new agent file in `src/agents/`
2. Define the agent with specific instructions and tools
3. Add handoff in `DanteCore.ts`
4. Export from `src/agents/index.ts`

### Adding New Tools

1. Create a new tool file in `src/tools/`
2. Use the `tool()` helper from `@openai/agents`
3. Export from `src/tools/index.ts`
4. Add to relevant agents

## Configuration

### Environment Variables

- `OPENAI_API_KEY` - Required: Your OpenAI API key
- `OPENAI_TRACING_API_KEY` - Optional: For tracing
- `WEATHER_API_KEY` - Optional: OpenWeatherMap API key
- `DEFAULT_MODEL` - Default: `gpt-4.1`
- `TEMPERATURE` - Default: `0.7`
- `MAX_TURNS` - Default: `15`
- `PORT` - Default: `3000`
- `OLLAMA_HOST` - Optional: Base URL of your local Ollama server (default: `http://127.0.0.1:11434`). Use `http://host.docker.internal:11434` from Docker on macOS/Windows.
- `OLLAMA_BACKGROUND_MODEL` - Optional: Ollama model for background/non-interactive tasks (default: `gemma3:12b`).

See docs/ollama.md for complete setup and troubleshooting.

### Model Settings

Adjust model behavior in `src/utils/config.ts`:
- Temperature (0-2): Controls randomness
- Top P (0-1): Nucleus sampling
- Max Turns: Maximum agent handoffs
- Frequency/Presence Penalty: Control repetition

## Examples

### Simple Query
```
Dante > What's the weather in Tokyo?
```

### Code Generation
```
Dante > Create a React component for a todo list with TypeScript
```

### Debugging
```
Dante > Debug this code: [paste your code]
```

### Website Creation
```
Dante > Build a landing page for a SaaS startup
```

### Security Analysis
```
Dante > Analyze this code for security vulnerabilities
```

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your OpenAI API key is set correctly in `.env`
2. **Module Not Found**: Run `bun install` to install dependencies
3. **Port Already in Use**: Change the PORT in `.env`
4. **TypeScript Errors**: Run `bun run typecheck` to check types

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

MIT License - See LICENSE file for details

## Support

For issues and questions:
- Open an issue on GitHub
- Contact the development team
- Check the documentation in `/docs`

## Acknowledgments

- OpenAI for GPT-5 and the Agents SDK
- Bun for the fast JavaScript runtime
- Vite for the build tooling
- The open-source community

---

Built with ❤️ by the Dante team

## Ollama Integration

To integrate Ollama into your environment, you need to configure the following environment variables:

- `OLLAMA_HOST`: Base URL of your local Ollama server (default: `http://127.0.0.1:11434`). If using Docker on macOS/Windows, use `http://host.docker.internal:11434`.
- `OLLAMA_BACKGROUND_MODEL`: Model slug for background/non-interactive tasks (default: `gemma3:12b`). Ensure the model is pulled via `ollama pull <model>`.

Refer to `docs/ollama.md` for detailed setup instructions and troubleshooting.
