// Tests for scripts/lib/pdf-extractor.mjs using node:test
// Focus: path restriction behavior of extractPdf

import test from 'node:test';
import assert from 'node:assert/strict';
import path from 'node:path';
import { existsSync } from 'node:fs';

import { extractPdf } from '../scripts/lib/pdf-extractor.mjs';

const TEST_REL_PATH = path.join('uploads', '1757963490637-389422180.pdf');
const TEST_ABS_PATH = path.resolve(process.cwd(), TEST_REL_PATH);
const SKIP_ALL = !existsSync(TEST_ABS_PATH);

// Helper to mark a message indicating why tests are skipped
const skipMsg = `Skipping pdf-extractor tests: missing ${TEST_REL_PATH}`;

// Sanity: path outside uploads should be rejected with EACCESS_OUTSIDE_UPLOADS
// This test does not require the PDF, but we follow the instruction to skip all if missing.
test('rejects access to files outside uploads (relative path)', { skip: SKIP_ALL, todo: false }, async () => {
  await assert.rejects(
    () => extractPdf({ filePath: 'package.json' }),
    (err) => {
      assert.equal(err.code, 'EACCESS_OUTSIDE_UPLOADS');
      assert.match(String(err.message), /outside the allowed uploads directory/i);
      return true;
    },
  );
} );

// Path traversal should also be blocked
// e.g., uploads/../package.json resolves outside the allowed base
test('rejects path traversal escaping uploads', { skip: SKIP_ALL }, async () => {
  await assert.rejects(
    () => extractPdf({ filePath: path.join('uploads', '..', 'package.json') }),
    (err) => {
      assert.equal(err.code, 'EACCESS_OUTSIDE_UPLOADS');
      return true;
    },
  );
} );

// Happy path: reading a PDF within uploads should be allowed
// We only assert basic shape to avoid brittleness
// Note: This verifies the path restriction by confirming the resolved file remains under uploads

test('allows reading a PDF within uploads', { skip: SKIP_ALL }, async () => {
  const res = await extractPdf({ filePath: TEST_REL_PATH, maxPages: 1 });

  const uploadsBase = path.resolve(process.cwd(), 'uploads');
  assert.ok(
    res.file === uploadsBase || res.file.startsWith(uploadsBase + path.sep),
    `Resolved path ${res.file} must be within ${uploadsBase}`,
  );

  assert.equal(typeof res.text, 'string');
  assert.equal(typeof res.numpages, 'number');
  assert.ok(res.size > 0);
} );
