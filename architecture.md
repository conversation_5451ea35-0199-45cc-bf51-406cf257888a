## Orchestration and Reliability

For a detailed specification on the resilient multi-agent orchestration engine, including the run journal, two-phase commits, and resumable state, please see the full architecture document:

- [Resilient Multi-Agent Orchestration Architecture](docs/resilient-orchestration.md)

## Distributed Transactions and Concurrency

For the design of the distributed transaction system, including the two-phase commit protocol and concurrency control mechanisms, refer to the following document:

- [2PC Coordinator and Concurrency](docs/2pc-coordinator-and-concurrency.md)

---

## Browser-Based Geolocation Tool Architecture

### 1. Primary Geolocation (Browser API)

- **Mechanism:** Utilize `navigator.geolocation` for obtaining the user's precise location (latitude and longitude).

- **Privacy:** Requires explicit user permission. If denied or unavailable, fallback mechanisms will be engaged.
- **Usage:**

    ```javascript
    if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const latitude = position.coords.latitude;
                const longitude = position.coords.longitude;
                // Use latitude and longitude
            },
            (error) => {
                // Handle errors (e.g., user denied, position unavailable)
                console.error('Geolocation error:', error);
                // Trigger fallback
            },
            { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
        );
    } else {
        // Geolocation not supported by browser
        console.log('Geolocation not supported.');
        // Trigger fallback
    }
    ```

### 2. Reverse Geocoding Provider

- **Purpose:** Convert geographic coordinates (latitude, longitude) into a human-readable address or location name.

- **Selection Criteria:**
  - **Privacy-Friendly:** Prioritize providers that do not log or sell user location data.
  - **Free/Affordable Tier:** Suitable for typical browser-based usage.
  - **Rate Limits:** Reasonable limits for client-side use.
  - **Accuracy:** Good accuracy for reverse geocoding.
  - **Ease of Use:** Simple API for integration.
- **Recommended Provider:** **OpenStreetMap Nominatim**
  - **Why:** Open-source, widely used, generally privacy-respecting (check their usage policy for specific details), and offers a free public API.
  - **Endpoint Example:** `https://nominatim.openstreetmap.org/reverse?format=json&lat=<latitude>&lon=<longitude>&zoom=18&addressdetails=1`
  - **Considerations:** Be mindful of Nominatim's Usage Policy, especially regarding rate limits and acceptable use. For high-volume applications, a self-hosted instance or a commercial provider might be necessary.

### 3. IP-Based Fallback Mechanism

- **Purpose:** Provide an approximate location when `navigator.geolocation` is unavailable, denied by the user, or fails.

- **Mechanism:** Use a third-party API to determine the user's approximate location based on their IP address.
- **Privacy:** IP-based geolocation is less precise and generally considered less privacy-invasive than precise GPS coordinates. However, choose a provider with a clear privacy policy.
- **Recommended Provider:** **ip-api.com** (or similar)
  - **Why:** Offers a free, simple API for IP-based geolocation, returning country, region, city, and sometimes ISP information.
  - **Endpoint Example:** `http://ip-api.com/json`
  - **Usage:** Make a simple `GET` request to the API. The response will contain location data.
  - **Considerations:** Free tiers often have rate limits. Ensure the usage aligns with their terms of service. Data is less precise than browser geolocation.

### Component Interaction Flow

1. **Attempt Browser Geolocation:** The tool first tries to obtain the user's location using `navigator.geolocation`.
2. **Success:** If successful, the latitude and longitude are passed to the **Reverse Geocoding Provider** (e.g., Nominatim) to get a human-readable address.
3. **Failure/Denial/Unsupported:** If browser geolocation fails, is denied, or is not supported, the tool falls back to the **IP-Based Fallback Mechanism**.
4. **IP Geolocation:** The IP-based provider returns approximate location data (city, country, etc.).
5. **Display:** The obtained location (precise or approximate) is then displayed to the user.
