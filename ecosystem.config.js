module.exports = {
  apps : [{
    name: "daily-scheduler",
    script: "./scheduler.js", // Placeholder: Replace with your actual scheduler script path (e.g., ./my_scheduler.py or ./dist/scheduler.js)
    autorestart: false,
    cron_restart: "0 9 * * *", // Daily at 9:00 AM
    instances: 1,
    exec_mode: "fork",
    env: {
      TZ: "America/New_York",
      SMTP_HOST: 'smtp.gmail.com',
      SMTP_PORT: '465',
      SMTP_USERNAME: '<EMAIL>',
      SMTP_PASSWORD: '3193468Dl*',
      SENDER_EMAIL: '<EMAIL>',
      TO_EMAIL: '<EMAIL>',
    }
  }]
};
