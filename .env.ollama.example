# Ollama Integration Example
# Copy the relevant variables into your .env or use this file for reference

# Base URL to your local Ollama server
# Use http://host.docker.internal:11434 from Docker on macOS/Windows
OLLAMA_HOST=http://127.0.0.1:11434

# Model slug to use for background/non-interactive tasks
# Ensure it is available locally: `ollama pull gemma3:12b`
OLLAMA_BACKGROUND_MODEL=gemma3:12b

# Optional: override the embedding model used by the EmbeddingService
# OLLAMA_EMBEDDING_MODEL=nomic-embed-text
