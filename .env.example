# OpenAI API Configuration
OPENAI_API_KEY=sk-your-openai-key-here

# Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# Optional: Tracing Configuration
OPENAI_TRACING_API_KEY=sk-your-tracing-key-here

# Weather API (OpenWeatherMap)
WEATHER_API_KEY=your-weather-api-key-here

# Optional: Search API Keys for enhanced results
# Brave Search API (https://brave.com/search/api/)
BRAVE_SEARCH_API_KEY=your-brave-search-api-key-here

# Google Custom Search (https://developers.google.com/custom-search)
GOOGLE_SEARCH_API_KEY=your-google-api-key-here
GOOGLE_SEARCH_CX=your-google-search-engine-id-here

# Google OAuth Configuration for Connectors
# Required for Gmail, Google Calendar, and Google Drive integrations
# Create OAuth credentials at: https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
GOOGLE_REDIRECT_URI=http://localhost:3001/api/auth/google/callback

# Optional: Custom API Endpoints
OPENAI_API_ENDPOINT=https://api.openai.com

# Development Settings
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
API_TARGET=http://127.0.0.1:3001

# Agent Settings
MAX_TURNS=50
DEFAULT_MODEL=gpt-5
TEMPERATURE=0.7

# Embedding Service Configuration
EMBEDDING_PROVIDER=auto
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
# OPENAI_EMBEDDING_DIMENSIONS=1536
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
# OLLAMA_EMBEDDING_DIMENSIONS=768
# EMBEDDING_CACHE_TTL=86400000
# EMBEDDING_CACHE_MAX_SIZE=1000

# Vector Database Configuration (Qdrant)
# Optional: Enable Qdrant for persistent vector storage
# If not set, will use in-memory vector store
QDRANT_URL=http://localhost:6333
# QDRANT_API_KEY=your-qdrant-api-key-here  # Optional, only if authentication is enabled

# Memory System Configuration
ENABLE_VECTOR_SEARCH=true
ENABLE_FILE_STORAGE=true
USE_QDRANT=true  # Set to true to use Qdrant instead of in-memory vector store
MAX_MEMORIES=10000
AUTO_CONSOLIDATE=true
CONSOLIDATION_INTERVAL=3600000  # 1 hour in milliseconds
DEFAULT_TTL=**********  # 30 days in milliseconds

# MCP Configuration
MCP_ENABLED=true
MCP_AUTO_CONNECT=true
MCP_HEALTH_CHECK_INTERVAL=60000
MCP_MAX_CONNECTIONS=10
MCP_CONNECTION_TIMEOUT=30000
MCP_FILESYSTEM_ENABLED=true
MCP_FILESYSTEM_ALLOW_WRITE=false
MCP_GIT_ENABLED=false

# Browser-Use MCP Configuration (Python-based advanced browser automation)
# Provides tab management, AI extraction, and session persistence
MCP_BROWSER_USE_ENABLED=false  # Set to true after running 'bun run browser-use:setup'
# BROWSER_USE_COMMAND=/path/to/custom/browser-use-mcp.sh  # Optional: custom command path

# Conversation Summarizer Configuration
SUMMARIZER_ENABLED=true
SUMMARIZER_PROVIDER=ollama
SUMMARIZER_MODEL=gpt-oss:20b

# Ollama Integration (Local LLM for background/non-interactive tasks)
# Base URL to your local Ollama server
# From Docker on macOS/Windows, consider: http://host.docker.internal:11434
OLLAMA_HOST=http://127.0.0.1:11434
# Model slug to use for background tasks (ensure it's pulled via `ollama pull <model>`)
OLLAMA_BACKGROUND_MODEL=gemma3:12b
# Daily AI Briefing configuration
# Comma-separated list of RSS/Atom feeds to pull AI tech news from.
# If unset, defaults are used (TechCrunch AI, The Verge AI, OpenAI Blog, Google AI, NVIDIA Newsroom).
AI_NEWS_FEEDS=

# Briefing schedule (used by scripts/ai-briefing.js schedule)
BRIEFING_TIME=08:00
BRIEFING_TZ=
BRIEFING_RECIPIENTS=

# OpenAI configuration (optional; used to generate concise summaries)
OPENAI_MODEL=gpt-5-mini
