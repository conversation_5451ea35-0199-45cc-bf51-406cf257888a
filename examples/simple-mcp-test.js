#!/usr/bin/env bun

/**
 * Simple MCP Integration Test
 * 
 * This demonstrates how to add a new MCP server and test it
 */

console.log('🔧 Simple MCP Integration Test\n');

// Test 1: Add via Environment Variables
console.log('📌 Method 1: Add MCP Server via Environment Variables');
console.log('Add this to your .env.mcp file:');
console.log('');
console.log('MCP_SERVER_1_ID=test-filesystem');
console.log('MCP_SERVER_1_NAME=Test Filesystem Server');
console.log('MCP_SERVER_1_TYPE=stdio');
console.log('MCP_SERVER_1_COMMAND=npx -y @modelcontextprotocol/server-filesystem /tmp');
console.log('MCP_SERVER_1_PRIORITY=100');
console.log('MCP_SERVER_1_TAGS=filesystem,test');
console.log('MCP_SERVER_1_ENABLED=true');
console.log('');

// Test 2: Add via API
console.log('📌 Method 2: Add MCP Server via API');
console.log('Run this curl command while <PERSON> is running:');
console.log('');
console.log('curl -X POST http://localhost:3001/api/mcp/servers \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{');
console.log('    "id": "api-filesystem",');
console.log('    "name": "API Filesystem Server",');
console.log('    "type": "stdio",');
console.log('    "config": {');
console.log('      "fullCommand": "npx -y @modelcontextprotocol/server-filesystem /tmp"');
console.log('    },');
console.log('    "enabled": true,');
console.log('    "priority": 100,');
console.log('    "tags": ["filesystem", "api"]');
console.log('  }\'');
console.log('');

// Test 3: Check status
console.log('📌 Method 3: Check MCP Status');
console.log('Check MCP status:');
console.log('curl http://localhost:3001/api/mcp/status');
console.log('');
console.log('List servers:');
console.log('curl http://localhost:3001/api/mcp/servers');
console.log('');
console.log('Connect to server:');
console.log('curl -X POST http://localhost:3001/api/mcp/servers/test-filesystem/connect');
console.log('');

// Test 4: CLI commands
console.log('📌 Method 4: Use CLI Commands');
console.log('Start the CLI and try these commands:');
console.log('');
console.log('bun run cli');
console.log('> mcp status');
console.log('> mcp servers');
console.log('> mcp tools');
console.log('> mcp connect test-filesystem');
console.log('');

// Test 5: Chat with MCP
console.log('📌 Method 5: Test in Chat');
console.log('Send a message to test MCP integration:');
console.log('');
console.log('curl -X POST http://localhost:3001/api/chat \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{');
console.log('    "messages": [');
console.log('      {');
console.log('        "role": "user",');
console.log('        "content": "List the files in the /tmp directory using MCP tools"');
console.log('      }');
console.log('    ],');
console.log('    "useMCP": true');
console.log('  }\'');
console.log('');

console.log('🎯 Quick Start Guide:');
console.log('');
console.log('1. Install the MCP filesystem server:');
console.log('   npm install -g @modelcontextprotocol/server-filesystem');
console.log('');
console.log('2. Copy .env.mcp.example to .env.mcp and enable MCP');
console.log('');
console.log('3. Start Dante:');
console.log('   bun run dev:api');
console.log('');
console.log('4. Check MCP status:');
console.log('   curl http://localhost:3001/api/mcp/status');
console.log('');
console.log('5. Add and test a server using any of the methods above!');
console.log('');

console.log('📚 For more detailed instructions, see docs/MCP_INTEGRATION.md');