import { generateText, stepCountIs } from 'ai';
import { google } from '@ai-sdk/google';
import { openai } from '@ai-sdk/openai';
import path from 'path';

// Defer dynamic imports until after env is set
async function lazyImportMCP() {
  const mcp = await import('./src/mcp');
  const mgr = await import('./src/mcp/MCPServerManager');
  return { initializeMCPServers: mcp.initializeMCPServers, cleanupMCP: mcp.cleanupMCP, mcpServerManager: mgr.mcpServerManager };
}

async function lazyImportBrowserTools() {
  const toolsMod = await import('./src/tools/browserUseTools');
  const utilsMod = await import('./src/utils/toolUtils');
  return { browserUseTools: toolsMod.browserUseTools, mapToolsByName: utilsMod.mapToolsByName };
}

function ensureEnv() {
  // Ensure Browser-Use MCP server is enabled for this run
  process.env.MCP_BROWSER_USE_ENABLED = process.env.MCP_BROWSER_USE_ENABLED || 'true';
  process.env.MCP_AUTO_CONNECT = process.env.MCP_AUTO_CONNECT || 'true';
  // Allow overriding the command if needed, else use default from config
  if (!process.env.BROWSER_USE_COMMAND) {
    const home = process.env.HOME || process.env.USERPROFILE || '';
    if (home) process.env.BROWSER_USE_COMMAND = path.join(home, '.dante-gpt', 'run-browser-use-mcp.sh');
  }
}

type AnyRecord = Record<string, any>;

function toText(val: any): string {
  if (typeof val === 'string') return val;
  try {
    if (val && Array.isArray(val.content)) {
      const t = val.content.find((i: any) => i?.type === 'text');
      return typeof t?.text === 'string' ? t.text : JSON.stringify(val);
    }
  } catch {}
  try {
    const j = JSON.stringify(val);
    return typeof j === 'string' ? j : String(val);
  } catch {
    return String(val);
  }
}

function safeJsonParse(s: string): any | undefined {
  try { return JSON.parse(s); } catch { return undefined; }
}

function summarize(obj: any, max = 220): string {
  const s = typeof obj === 'string' ? obj : toText(obj);
  const str = typeof s === 'string' ? s : String(s ?? '');
  return str.length > max ? str.slice(0, max) + '…' : str;
}

async function waitForServer(mgr: any, id: string, timeoutMs = 45000) {
  const start = Date.now();
  for (;;) {
    const st = mgr.getServerStatus(id);
    if (st?.status === 'connected') return;
    if (Date.now() - start > timeoutMs) throw new Error(`Timeout waiting for MCP server ${id}`);
    await new Promise((r) => setTimeout(r, 500));
  }
}

async function listServerTools(mgr: any, id: string) {
  const server = mgr.getServer(id);
  if (!server) throw new Error(`Server ${id} instance not found`);
  const tools = await server.listTools();
  console.log(`🔧 Tools on ${id}: ${tools.map((t: any) => t.name).join(', ')}`);
  return tools;
}

async function runModelDrivenFlow(url = 'https://music.youtube.com') {
  console.log('\n=== Model-driven tool run ===');
  const { browserUseTools, mapToolsByName } = await lazyImportBrowserTools();

  // Prefer OpenAI if available; fallback to Gemini
  const useOpenAI = !!process.env.OPENAI_API_KEY;
  const model = useOpenAI ? openai('gpt-5-mini') : google('models/gemini-2.5-flash');
  console.log(`🤖 Using model: ${useOpenAI ? 'OpenAI gpt-5-mini' : 'Google Gemini 2.5 Flash'}`);
  const system = [
    'You are testing the Browser-Use MCP integration.',
    'Goal: open https://music.youtube.com in the SAME tab and start playback by clicking the first visible Play button or the first playlist/album tile you see.',
    'Use ONLY the provided tools (browser_use_*).',
    'Steps:',
    '1) browser_use_open_and_wait OR browser_use_navigate + browser_use_get_state (prefer no new tab).',
    '2) browser_use_get_state to read interactive elements.',
    '3) browser_use_click on the first suitable element (play/playlist/song tile).',
    'Recovery:',
    '- If about:blank or Empty Tab: use browser_use_list_tabs to find a URL of a non-blank tab, then call browser_use_navigate to that URL in the SAME tab. Avoid browser_use_switch_tab.',
    'Constraints:',
    '- new_tab=false unless absolutely necessary.',
    '- Keep steps minimal (<= 8). After each tool result, explain the next action briefly.',
  ].join('\n');

  const cfg: AnyRecord = {
    model,
    system,
    prompt: `Open ${url} and play the first item you see.`,
    tools: mapToolsByName(browserUseTools, { fallbackPrefix: 'browser_use' }),
    stopWhen: stepCountIs(8),
      onStepFinish: (step: any) => {
      try {
        if (step.toolCalls?.length) {
          for (const tc of step.toolCalls) {
            const tName = tc.name || tc.toolName || tc.function?.name || 'unknown_tool';
            const callId = tc.id || tc.toolCallId || tc.callId;
            // Prefer call-time args if present; otherwise, look ahead to matching toolResult.input
            const matchingResult = (step.toolResults || []).find((r: any) => (
              (callId && (r.toolCallId === callId)) || r.toolName === tName
            ));
            const tArgs = tc.args ?? tc.arguments ?? tc.function?.arguments ?? matchingResult?.input;
            console.log(`🛠️  TOOL CALL: ${tName} | args: ${summarize(tArgs ?? 'n/a')}`);
          }
        }
        if (step.toolResults?.length) {
          for (const tr of step.toolResults) {
            const rName = tr.name || tr.toolName || 'tool';
            // Prefer to show the input (arguments) on result when available
            const inputSummary = tr.input ? ` | input: ${summarize(tr.input)}` : '';
            console.log(`🧩 TOOL RESULT: ${rName}${inputSummary} | ${summarize(tr.result ?? tr)}`);
          }
        }
        if (step.text) console.log(`💬 MODEL: ${step.text}`);
      } catch (e) {
        console.warn('onStepFinish log error:', e);
      }
    }
  };

  const res = await generateText(cfg as any);
  console.log('\n✅ Model-driven run complete. Final text:\n', res.text);
}

async function runDirectServerFlow(url = 'https://music.youtube.com') {
  console.log('\n=== Direct server (no model) run ===');
  const { mcpServerManager } = await lazyImportMCP();
  const server = mcpServerManager.getServer('browser-use');
  if (!server) throw new Error('browser-use server not connected');

  const call = async (name: string, args: AnyRecord) => {
    console.log(`\n🛠️  CALL ${name} | args=${JSON.stringify(args)}`);
    const out = await server.callTool(name, args);
    const text = toText(out);
    console.log(`🧩 RESULT (${name}): ${summarize(text)}`);
    return text;
  };

  // 1) Navigate
  await call('browser_navigate', { url, new_tab: false });

  // Helper: robust get_state with wait and backoff
  const maxWaitMs = parseInt(process.env.DIRECT_GET_STATE_MAX_WAIT_MS || '90000');
  const pollMs = parseInt(process.env.DIRECT_GET_STATE_POLL_MS || '1000');
  const getState = async () => {
    try {
      return await call('browser_get_state', { include_screenshot: false, wait_until_ready: true, max_wait_ms: maxWaitMs, poll_ms: pollMs });
    } catch (e) {
      return 'Error';
    }
  };

  // 2) Get state with recovery for about:blank
  let stateText = await getState();
  const isBlank = /\"url\"\s*:\s*\"about:blank\"/.test(stateText) || /Empty Tab|ignore this tab/i.test(stateText);
  if (isBlank) {
    console.log('⚠️ Detected blank/placeholder tab. Attempting tab recovery...');
    const tabs = await call('browser_list_tabs', {});
    const matches = tabs.match(/\{[^}]*\}/g) || [];
    const candidates = matches.map((s) => safeJsonParse(s)).filter(Boolean) as Array<{ tab_id?: string; url?: string; title?: string }>;
    const pick = candidates.find(c => (c.url || '').includes('music.youtube.com'))
              || candidates.find(c => (c.url || '').startsWith('http'))
              || candidates[0];
    if (pick?.tab_id) {
      try {
        await call('browser_switch_tab', { tab_id: String(pick.tab_id) });
      } catch (e) {
        if (pick.url && /^https?:\/\//i.test(pick.url)) {
          console.log('↩️  Fallback: navigating to picked tab URL instead of switch_tab');
          await call('browser_navigate', { url: pick.url, new_tab: false });
        }
      }
      stateText = await getState();
    }
  }

  // 3) Choose a clickable element
  const stateJson = safeJsonParse(stateText) as any;
  let clickIndex: number | undefined;
  if (stateJson?.interactive_elements?.length) {
    const elems = stateJson.interactive_elements as Array<{ index: number; tag?: string; text?: string; href?: string }>;
    const isPlayable = (e: any) => /play/i.test(e?.text || '') || /watch|playlist/i.test(e?.href || '');
    const firstPlayable = elems.find(isPlayable);
    const firstHref = elems.find(e => !!e.href);
    clickIndex = (firstPlayable || firstHref || elems[0]).index;
    console.log(`🎯 Selected element index=${clickIndex} | sample=${summarize(firstPlayable || firstHref || elems[0])}`);
  } else {
    console.log('⚠️ No interactive elements found; skipping click due to state timeout');
  }

  // 4) Click (only if we found an index)
  if (typeof clickIndex === 'number') {
    await call('browser_click', { index: clickIndex, new_tab: false });
    // 5) Confirm navigation/state (short wait)
    await call('browser_get_state', { include_screenshot: false, wait_until_ready: false });
  }

  console.log('\n✅ Direct server run complete.');
}

async function main() {
  ensureEnv();
  const { initializeMCPServers, mcpServerManager, cleanupMCP } = await lazyImportMCP();

  try {
    console.log('🔧 Initializing + connecting MCP (browser-use)…');
    await initializeMCPServers();
    await waitForServer(mcpServerManager, 'browser-use', 60000);
    await listServerTools(mcpServerManager, 'browser-use');

    // Run model-driven flow first to observe tool choice
    await runModelDrivenFlow('https://music.youtube.com');

    // Then run a direct server flow for deterministic verification
    await runDirectServerFlow('https://music.youtube.com');
  } catch (err) {
    console.error('❌ Test failed:', err);
  } finally {
    // Keep browser alive for manual inspection if desired (comment out to keep)
    // await cleanupMCP();
  }
}

void main();
