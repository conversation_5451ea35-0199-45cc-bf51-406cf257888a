import { Counter, Histogram, Registry, exponentialBuckets } from 'prom-client';

export const registry = new Registry();

export const agent_empty_final_total = new Counter({
  name: 'agent_empty_final_total',
  help: 'Total number of AGENT_EMPTY_FINAL events',
});
registry.registerMetric(agent_empty_final_total);

export const model_429_total = new Counter({
  name: 'model_429_total',
  help: 'Total number of 429 errors from model APIs',
  labelNames: ['model'],
});
registry.registerMetric(model_429_total);

export const model_retry_total = new Counter({
  name: 'model_retry_total',
  help: 'Total number of retries for model APIs',
  labelNames: ['model', 'attempt'],
});
registry.registerMetric(model_retry_total);

export const breaker_open_total = new Counter({
  name: 'breaker_open_total',
  help: 'Total number of times a circuit breaker has opened',
  labelNames: ['model'],
});
registry.registerMetric(breaker_open_total);

export const tool_call_latency_ms = new Histogram({
  name: 'tool_call_latency_ms',
  help: 'Latency of tool calls in milliseconds',
  buckets: exponentialBuckets(100, 2, 8), // 100ms to ~12s
});
registry.registerMetric(tool_call_latency_ms);

export const output_tokens = new Histogram({
  name: 'output_tokens',
  help: 'Histogram of output tokens from models',
  buckets: exponentialBuckets(16, 2, 10), // 16 to 8192
});
registry.registerMetric(output_tokens);

export const input_tokens = new Histogram({
  name: 'input_tokens',
  help: 'Histogram of input tokens to models',
  buckets: exponentialBuckets(128, 2, 12), // 128 to 262144
});
registry.registerMetric(input_tokens);
