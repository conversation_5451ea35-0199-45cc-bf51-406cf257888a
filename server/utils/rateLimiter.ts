import { config } from '../../src/utils/config';
import { logger } from '../../src/utils/logger';
import { tokenBus } from '../../src/utils/tokenBus';
import { getContextValue } from '../../src/utils/requestContext';

/**
 * Configuration for the rate limiter, driven by environment variables.
 * These settings control the behavior of the token bucket algorithm, including
 * enabling/disabling the limiter, token refresh rates, and call spacing.
 */
const {
  enabled: RATE_LIMIT_ENABLED,
  minSpacingMs: RATE_MIN_SPACING_MS,
  maxSpacingMs: RATE_MAX_SPACING_MS,
  tokensPerSecond: RATE_LIMIT_TOKENS_PER_SECOND,
  maxTokens: RATE_LIMIT_MAX_TOKENS
} = config.rateLimit;

/**
 * Represents a single token bucket for rate limiting. Each bucket is associated
 * with a unique key and manages its own token supply, refilling over time.
 */
class TokenBucket {
    private tokens: number;
    private lastRefill: number;
    private readonly tokensPerSecond: number;
    private readonly maxTokens: number;

    /**
     * Creates a new TokenBucket instance.
     * @param tokensPerSecond The number of tokens to add to the bucket per second.
     * @param maxTokens The maximum number of tokens the bucket can hold.
     */
    constructor(tokensPerSecond: number, maxTokens: number) {
        this.tokensPerSecond = tokensPerSecond;
        this.maxTokens = maxTokens;
        this.tokens = maxTokens;
        this.lastRefill = Date.now();
    }

    /**
     * Refills the token bucket based on the elapsed time since the last refill.
     * This method is called internally before any token consumption.
     */
    private refill(): void {
        const now = Date.now();
        const elapsed = now - this.lastRefill;
        this.lastRefill = now;
        const tokensToAdd = (elapsed / 1000) * this.tokensPerSecond;
        this.tokens = Math.min(this.maxTokens, this.tokens + tokensToAdd);
    }

    /**
     * Attempts to consume a specified number of tokens from the bucket.
     * @param count The number of tokens to consume.
     * @returns `true` if the tokens were successfully consumed, `false` otherwise.
     */
    public consume(count: number): boolean {
        this.refill();
        if (this.tokens >= count) {
            this.tokens -= count;
            return true;
        }
        return false;
    }

    /**
     * Returns the current number of tokens in the bucket after refilling.
     * @returns The current number of available tokens.
     */
    public getTokens(): number {
        this.refill();
        return this.tokens;
    }
}

/**
 * Implements a token bucket-based rate limiter for controlling API usage.
 * This class manages multiple token buckets, one for each unique key (e.g., a
 * combination of model name and API key), ensuring that calls are spaced out
 * and that token usage is reported back to adjust the budget.
 */
export class TokenBucketLimiter {
    private buckets: Map<string, TokenBucket> = new Map();
    private lastCallTimestamps: Map<string, number> = new Map();
    private lastReportLogTs: Map<string, number> = new Map();

    /**
     * Asynchronously consumes a token, waiting if necessary to enforce rate limits.
     * This method ensures that there is a minimum spacing between calls and that
     * there are enough tokens in the bucket before proceeding. If not enough tokens
     * are available, it will wait until the bucket is refilled.
     * @param key A unique key identifying the rate limit bucket.
     * @param tokensToConsume The number of tokens to consume for this operation.
     */
    public async consume(key: string, tokensToConsume: number = 1): Promise<void> {
        if (!RATE_LIMIT_ENABLED) {
            return;
        }

        const bucket = this.getBucket(key);

        while (!bucket.consume(tokensToConsume)) {
            const tokens = bucket.getTokens();
            const required = tokensToConsume;
            const deficit = required - tokens;
            const delay = (deficit / RATE_LIMIT_TOKENS_PER_SECOND) * 1000;
            logger.debug(`Rate limiting: waiting for ${delay.toFixed(2)}ms for ${deficit.toFixed(2)} tokens for key "${key}".`);
            await new Promise(resolve => setTimeout(resolve, Math.max(delay, 0)));
        }

        await this.enforceSpacing(key);
    }

    /**
     * Reports the actual token usage from a model's response. This allows the
     * limiter to adjust the token budget in the future, although in this
     * implementation, it only logs the reported usage.
     * @param key The unique key for the rate limit bucket.
     * @param actualTokens The number of tokens actually used, as reported by the model.
     */
    public report(key: string, actualTokens: number): void {
        if (!RATE_LIMIT_ENABLED) {
            return;
        }

        // Surface token usage via global TokenBus (for SSE/UI) with session correlation
        try {
            const sessionId = getContextValue<string>('sessionId');
            const parts = key.split(':'); // expected: provider:model:apikey
            const model = parts.length >= 2 ? parts[1] : undefined;
            tokenBus.emitTokenUsage({
                sessionId: sessionId || undefined,
                key,
                model,
                used: actualTokens,
            });
        } catch {
            // ignore emission errors
        }

        // Throttle noisy debug logs: at most once per 10s per key
        const now = Date.now();
        const last = this.lastReportLogTs.get(key) || 0;
        if (now - last > 10000) {
            this.lastReportLogTs.set(key, now);
            logger.debug(`Reported token usage for key "${key}": ${actualTokens}`);
        }
    }

    /**
     * Enforces a minimum spacing between calls for a given key to prevent bursting.
     * A random jitter is added to the spacing to avoid synchronized requests.
     * @param key The unique key for the rate limit bucket.
     */
    private async enforceSpacing(key: string): Promise<void> {
        const now = Date.now();
        const lastCall = this.lastCallTimestamps.get(key) || 0;
        const elapsed = now - lastCall;

        const jitter = Math.random() * (RATE_MAX_SPACING_MS - RATE_MIN_SPACING_MS);
        const minSpacing = RATE_MIN_SPACING_MS + jitter;

        if (elapsed < minSpacing) {
            const delay = minSpacing - elapsed;
            logger.debug(`Enforcing spacing for key "${key}": waiting for ${delay.toFixed(2)}ms.`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        this.lastCallTimestamps.set(key, Date.now());
    }

    /**
     * Retrieves or creates a token bucket for the given key.
     * @param key The unique key for the bucket.
     * @returns The `TokenBucket` instance for the key.
     */
    private getBucket(key: string): TokenBucket {
        if (!this.buckets.has(key)) {
            logger.debug(`Creating new token bucket for key "${key}".`);
            this.buckets.set(key, new TokenBucket(RATE_LIMIT_TOKENS_PER_SECOND, RATE_LIMIT_MAX_TOKENS));
        }
        return this.buckets.get(key)!;
    }
}

/**
 * A singleton instance of the TokenBucketLimiter for global use.
 */
export const rateLimiter = new TokenBucketLimiter();
