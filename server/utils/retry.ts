/**
 * @file Provides a higher-order function for adding retry logic with exponential backoff,
 * jitter, circuit breaker, and support for the Retry-After header.
 */

import { logger } from '@utils/logger';
import { model_429_total, model_retry_total, breaker_open_total } from '../observability/metrics';

/**
 * Configuration for the retry mechanism, sourced from environment variables.
 */
const RETRY_CONFIG = {
  /** Maximum number of attempts for an operation. */
  MAX_ATTEMPTS: parseInt(process.env.RETRY_MAX_ATTEMPTS || '5', 10),
  /** Base delay in milliseconds for exponential backoff. */
  BACKOFF_BASE_MS: parseInt(process.env.RETRY_BACKOFF_BASE_MS || '1000', 10),
  /** Number of failures within the time window to open the circuit breaker. */
  CIRCUIT_BREAKER_FAILURE_THRESHOLD: parseInt(
    process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD || '10',
    10,
  ),
  /** Time window in milliseconds to track failures for the circuit breaker. */
  CIRCUIT_BREAKER_WINDOW_MS: parseInt(
    process.env.CIRCUIT_BREAKER_WINDOW_MS || '60000',
    10,
  ), // 1 minute
  /** Time in milliseconds the circuit stays open before transitioning to half-open. */
  CIRCUIT_BREAKER_RESET_TIMEOUT_MS: parseInt(
    process.env.CIRCUIT_BREAKER_RESET_TIMEOUT_MS || '30000',
    10,
  ), // 30 seconds
};

/**
 * Represents the state of the circuit breaker.
 * - `CLOSED`: Operations are allowed.
 * - `OPEN`: Operations are blocked and fail immediately.
 * - `HALF_OPEN`: A single trial operation is allowed to check if the system has recovered.
 */
type CircuitState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

/**
 * Interface for the circuit breaker's state.
 */
interface CircuitBreaker {
  state: CircuitState;
  failureTimestamps: number[];
  openTime: number;
}

const circuitBreaker: CircuitBreaker = {
  state: 'CLOSED',
  failureTimestamps: [],
  openTime: 0,
};

/**
 * Checks if an error is retryable.
 * Retryable errors include network errors, 429 (Too Many Requests), and 5xx server errors.
 * @param error - The error object to inspect.
 * @returns `true` if the error is retryable, `false` otherwise.
 */
function isRetryableError(error: any): boolean {
  if (!error) return false;

  // Network errors
  const networkErrorCodes = ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'EAI_AGAIN'];
  if (networkErrorCodes.includes(error.code)) {
    return true;
  }

  // HTTP errors from clients like axios
  if (error.response && typeof error.response.status === 'number') {
    const { status } = error.response;
    // 429 Too Many Requests
    if (status === 429) return true;
    // 5xx Server Errors
    if (status >= 500 && status <= 599) return true;
  }

  return false;
}

/**
 * Records a failure, updating the circuit breaker's state if necessary.
 */
function recordFailure(modelName: string) {
  const now = Date.now();
  const windowStart = now - RETRY_CONFIG.CIRCUIT_BREAKER_WINDOW_MS;

  // Remove failures outside the current window
  circuitBreaker.failureTimestamps = circuitBreaker.failureTimestamps.filter(
    timestamp => timestamp > windowStart,
  );

  circuitBreaker.failureTimestamps.push(now);

  if (
    circuitBreaker.failureTimestamps.length >=
    RETRY_CONFIG.CIRCUIT_BREAKER_FAILURE_THRESHOLD
  ) {
    circuitBreaker.state = 'OPEN';
    circuitBreaker.openTime = now;
    logger.warn('Circuit breaker opened due to excessive failures.');
    breaker_open_total.inc({ model: modelName });
  }
}

/**
 * A promise-based sleep function.
 * @param ms - The number of milliseconds to sleep.
 * @returns A promise that resolves after the specified duration.
 */
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

function isCircuitOpen(): boolean {
  return circuitBreaker.state === 'OPEN';
}

/**
 * A higher-order function that wraps an operation with advanced retry logic.
 *
 * @template T - The return type of the operation.
 * @param operation - The asynchronous function to wrap.
 * @returns A new function that will automatically retry the operation upon failure.
 *
 * @remarks
 * This wrapper implements the following features:
 * 1.  **Exponential Backoff with Full Jitter**: Avoids thundering herd problems by randomizing sleep times.
 *     The delay is calculated as `rand(0, baseMs * 2^attempt)`.
 * 2.  **Retry-After Header**: Respects the `Retry-After` HTTP header for rate limiting.
 *     It can handle both seconds and HTTP-date formats.
 * 3.  **Circuit Breaker**: Prevents the application from repeatedly trying an operation that is likely to fail.
 *     - It opens after `CIRCUIT_BREAKER_FAILURE_THRESHOLD` failures in `CIRCUIT_BREAKER_WINDOW_MS`.
 *     - While open, all calls fail immediately.
 *     - It transitions to `HALF_OPEN` after `CIRCUIT_BREAKER_RESET_TIMEOUT_MS`, allowing one test call.
 * 4.  **Error Filtering**: Only retries on specific, transient errors (e.g., 429, 5xx, network errors).
 */
export function withRetries<T>(
  operation: (...args: any[]) => Promise<T>,
  modelName: string = 'unknown',
): (...args: any[]) => Promise<T> {
  return async (...args: any[]) => {
    if (circuitBreaker.state === 'OPEN') {
      if (
        Date.now() - circuitBreaker.openTime >
        RETRY_CONFIG.CIRCUIT_BREAKER_RESET_TIMEOUT_MS
      ) {
        circuitBreaker.state = 'HALF_OPEN';
        logger.info('Circuit breaker is now HALF_OPEN.');
      } else {
        throw new Error(
          'Circuit breaker is open. Operation will not be attempted.',
        );
      }
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt < RETRY_CONFIG.MAX_ATTEMPTS; attempt++) {
      try {
        const result = await operation(...args);
        if (circuitBreaker.state === 'HALF_OPEN') {
          circuitBreaker.state = 'CLOSED';
          circuitBreaker.failureTimestamps = [];
          logger.info('Circuit breaker is now CLOSED after successful call.');
        }
        return result;
      } catch (error: any) {
        lastError = error;

        if (!isRetryableError(error)) {
          logger.error('Non-retryable error encountered.', { error });
          throw error;
        }

        if (error.response?.status === 429) {
          model_429_total.inc({ model: modelName });
        }
        model_retry_total.inc({ model: modelName, attempt: attempt + 1 });

        if (circuitBreaker.state === 'HALF_OPEN') {
          circuitBreaker.state = 'OPEN';
          circuitBreaker.openTime = Date.now();
          logger.warn(
            'Circuit breaker re-opened after failure in HALF_OPEN state.',
          );
          breaker_open_total.inc({ model: modelName });
          throw error; // Fail immediately
        }

        recordFailure(modelName);
        if (isCircuitOpen()) {
          throw new Error(
            'Operation failed and circuit breaker is now open.',
          );
        }

        if (attempt + 1 >= RETRY_CONFIG.MAX_ATTEMPTS) {
          break; // Don't sleep on the last attempt
        }

        let delay = 0;
        const retryAfterHeader = error.response?.headers?.['retry-after'];

        if (retryAfterHeader) {
          const retryAfterSeconds = parseInt(retryAfterHeader, 10);
          if (!isNaN(retryAfterSeconds)) {
            delay = retryAfterSeconds * 1000;
          } else {
            const retryAfterDate = Date.parse(retryAfterHeader);
            if (!isNaN(retryAfterDate)) {
              delay = retryAfterDate - Date.now();
            }
          }
        }

        if (delay <= 0) {
          // Exponential backoff with full jitter
          const backoff = RETRY_CONFIG.BACKOFF_BASE_MS * Math.pow(2, attempt);
          delay = Math.random() * backoff;
        }

        logger.info(
          `Attempt ${attempt + 1} failed. Retrying in ${delay.toFixed(2)}ms...`,
        );
        await sleep(delay);
      }
    }

    logger.error(
      `Operation failed after ${RETRY_CONFIG.MAX_ATTEMPTS} attempts.`,
      { lastError },
    );
    throw lastError;
  };
}
