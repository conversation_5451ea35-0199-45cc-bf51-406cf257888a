import { rateLimiter } from "../utils/rateLimiter";
import { v4 as uuidv4 } from 'uuid';
import { withRetries } from '../utils/retry';
import { tool_call_latency_ms } from '../observability/metrics';

interface Tool {
  name: string;
  execute: (args: any, toolCallId?: string) => Promise<any>;
  estimatedTokens?: number; // Optional: for tools that consume tokens directly
}

class ToolRunner {
  private tools: Map<string, Tool>;

  constructor() {
    this.tools = new Map();
  }



  public registerTool(tool: Tool) {
    this.tools.set(tool.name, tool);
  }

  public async runTool(
    toolName: string,
    args: any,
    tenantId: string,
  ): Promise<any> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found.`);
    }

    // Acquire tokens before running any tool. This is a general guard.
    // More specific rate limiting for AI-backed tools should happen in their respective clients (e.g., OpenAIClient).
    const tokensNeeded = tool.estimatedTokens || 100; // Default to 100 tokens if not specified
    await rateLimiter.consume(tenantId, tokensNeeded);

    const toolCallId = uuidv4();
    const circuitBreakerKey = `tool:${toolName}:${tenantId}`;

    console.log(`Running tool: ${toolName} with args:`, args, `(toolCallId: ${toolCallId})`);

    const end = tool_call_latency_ms.startTimer();
    try {
      const operation = (attempt: number) => tool.execute(args, `${toolCallId}-${attempt}`);
      return await withRetries(operation, toolName)();
    } finally {
      end();
    }
  }
}

export const toolRunner = new ToolRunner();
