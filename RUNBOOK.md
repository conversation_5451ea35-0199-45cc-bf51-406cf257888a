# AI Briefing Runbook

This runbook explains how to configure authentication, perform a dry-run, trigger a one-off briefing, and schedule automated daily briefings using the built-in AI Briefing CLI.

The CLI lives at scripts/ai-briefing.js and is exposed via package.json scripts:
- bun run briefing:auth
- bun run briefing:run
- bun run briefing:schedule

Prerequisites
- Bun 1.0+ installed
- Dependencies installed: bun install
- Optional: .env with OPENAI_API_KEY if you prefer environment-based auth

1) Configure authentication
The summarization step uses OpenAI (optional). If no OpenAI key is configured, the CLI will still produce a simple, non-LLM summary of links.

Option A — Save your OpenAI key with the CLI (recommended)
- One-shot (non-interactive):
  bun run briefing:auth --openai-key sk-...your-key...
- Interactive (prompts you):
  bun run briefing:auth

The key is stored at ~/.dante-gpt/ai-briefing.json. To remove a stored field later:
  bun run briefing:auth --unset openaiKey

Security note: This file is user-local. Consider restricting its permissions:
  chmod 600 ~/.dante-gpt/ai-briefing.json

Option B — Provide key via environment variable
- Export at shell or place in your .env file in the project root:
  OPENAI_API_KEY=sk-...your-key...
- Optional model override:
  OPENAI_MODEL=gpt-4o-mini

2) Optional: Choose your news feeds
By default, the CLI fetches AI/tech news from a curated set of feeds. To customize, set AI_NEWS_FEEDS as a comma- or newline-separated list of RSS/Atom feed URLs, e.g.:
  AI_NEWS_FEEDS="https://openai.com/blog/rss.xml,https://blog.google/technology/ai/rss/"
Or in a .env file:
  AI_NEWS_FEEDS=https://openai.com/blog/rss.xml
  https://blog.google/technology/ai/rss/

3) Dry-run (generate without distributing)
A dry-run simply means: generate a briefing locally so you can review it. Nothing is sent anywhere by default.
- Run once and print to console + save to logs/ai-briefings/YYYY-MM-DD.md:
  bun run briefing:run
- Run and save to a custom folder (suppress console output if desired):
  bun run briefing:run --output-dir ./logs/briefings-test --no-print

The generated Markdown file can be reviewed in your editor before you choose to distribute it.

4) Send a one-off briefing (manual distribution)
The CLI does not send email or chat messages directly. After generating a briefing:
- Locate the file path printed by the command (e.g., logs/ai-briefings/2025-09-23.md)
- Share it via your preferred channel (email, Slack, etc.)
  Example (macOS):
    pbcopy < logs/ai-briefings/2025-09-23.md  # copies to clipboard
  Example (Linux):
    xclip -selection clipboard < logs/ai-briefings/2025-09-23.md

Tip: If you need automated delivery to Slack/Email, consider wrapping bun run briefing:run in your own script that posts the file contents via your provider’s API or CLI.

5) Schedule daily briefings
You can schedule a daily briefing at a specific local time or with a cron expression. The scheduler process runs in the foreground until you stop it (Ctrl+C).

- Default: 8:00 AM local time
  bun run briefing:schedule

- Specific time and timezone
  bun run briefing:schedule -t 09:30 --tz America/New_York

- Use a cron expression (e.g., 8am on weekdays) and run one immediately
  bun run briefing:schedule --cron "0 8 * * 1-5" --immediate

- Custom output directory
  bun run briefing:schedule -t 08:00 -o ./logs/ai-briefings

Running as a background service (optional)
For long-running schedules, consider a process manager such as PM2:
  pm2 start "bun run briefing:schedule -t 08:00 --tz America/Los_Angeles" --name ai-briefing
  pm2 save
  pm2 logs ai-briefing
  pm2 stop ai-briefing

6) Troubleshooting
- Another briefing process is currently running. Skipping.
  The CLI uses a lock at ~/.dante-gpt/ai-briefing.lock to prevent concurrent runs. Wait for the existing run to finish. If you are certain no run is active, remove the lock file.

- No feed URLs provided
  Set AI_NEWS_FEEDS or rely on the built-in defaults (the defaults are used unless AI_NEWS_FEEDS is explicitly set to empty).

- OpenAI errors (rate limits, invalid key)
  Verify your OPENAI_API_KEY and (optionally) OPENAI_MODEL. The CLI will fall back to a simple link summary if the LLM call fails.

- Output location
  Default output directory is logs/ai-briefings relative to your current working directory when running the command. Use --output-dir to change it.

7) Security & privacy notes
- Keep ~/.dante-gpt/ai-briefing.json private (contains your key if you stored it via the CLI).
- Never commit .env or ai-briefing.json to source control.
- Generated briefings may include links to external content; validate before distributing.

8) Cleanup / Uninstall
- Remove stored key: bun run briefing:auth --unset openaiKey
- Delete config: rm -f ~/.dante-gpt/ai-briefing.json
- Delete generated files: rm -rf ./logs/ai-briefings

9) Quick command reference
- Authenticate: bun run briefing:auth --openai-key sk-...
- Dry-run (review locally): bun run briefing:run
- Run without printing: bun run briefing:run --no-print
- Custom output folder: bun run briefing:run --output-dir ./my-briefings
- Schedule at 08:00 local time: bun run briefing:schedule
- Schedule at 09:30 NY time: bun run briefing:schedule -t 09:30 --tz America/New_York
- Schedule with cron and immediate run: bun run briefing:schedule --cron "0 8 * * 1-5" --immediate
