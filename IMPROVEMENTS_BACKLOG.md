# Improvements Backlog

## To Do

## In Progress

## Done

- Implemented resizing functionality for the Text Editor Canvas (`src/components/TextEditorCanvas.tsx`) using `react-resizable`. The editor can be resized both horizontally and vertically from the south-east corner.

## To Do

- Clarify if the text editor resizing should be limited to vertical adjustment only. If so, modify `src/components/TextEditorCanvas.tsx` to use `resizeHandles={['s']}`.
