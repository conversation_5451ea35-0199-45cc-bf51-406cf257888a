<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SVG Rendering in Dante GPT</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
        }
        .test-message {
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            max-height: 500px;
            overflow-y: auto;
        }
        .features {
            background: #e8f4ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .features h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        .features li {
            margin: 8px 0;
            line-height: 1.6;
        }
        code {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Test SVG Rendering in Dante GPT</h1>
        <p class="description">Copy this test message to verify SVG rendering is working correctly in the chat interface.</p>
        
        <h2>Sample Assistant Response with SVG:</h2>
        <div class="test-message">Great! I will create an SVG illustration of a bald person with short hair, wearing blue pants, sitting on a wooden chair. I'll keep it simple but realistic within the SVG capabilities.

I'll generate the SVG code for this scene. Here's an SVG representation of the described image: a bald person with red shorts, blue pants, sitting on a wooden chair:

```svg
<svg width="200px" height="300px" viewBox="0 0 200 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Chair -->
  <rect x="60" y="200" width="80" height="10" fill="saddlebrown" />
  <rect x="70" y="210" width="10" height="50" fill="saddlebrown" />
  <rect x="120" y="210" width="10" height="50" fill="saddlebrown" />
  
  <!-- Person's Head -->
  <circle cx="100" cy="70" r="20" stroke="black" stroke-width="2" fill="none" />
  
  <!-- Torso -->
  <line x1="100" y1="90" x2="100" y2="150" stroke="black" stroke-width="2" />
  
  <!-- Arms -->
  <line x1="100" y1="110" x2="75" y2="140" stroke="black" stroke-width="2" />
  <line x1="100" y1="110" x2="125" y2="140" stroke="black" stroke-width="2" />
  
  <!-- Shorts -->
  <rect x="90" y="150" width="20" height="20" fill="red" />
  
  <!-- Legs -->
  <line x1="100" y1="170" x2="80" y2="200" stroke="black" stroke-width="2" />
  <line x1="100" y1="170" x2="120" y2="200" stroke="black" stroke-width="2" />
  
  <!-- Pants -->
  <rect x="80" y="170" width="10" height="30" fill="blue" />
  <rect x="110" y="170" width="10" height="30" fill="blue" />
</svg>
```

You can copy this SVG code and use it in an HTML file or an SVG editor to view or modify it. Let me know if you need any adjustments or additional details!

Here's another example of an SVG - a simple house:

```svg
<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <!-- House Base -->
  <rect x="50" y="150" width="200" height="120" fill="lightblue" stroke="navy" stroke-width="2"/>
  
  <!-- Roof -->
  <polygon points="30,150 150,50 270,150" fill="brown" stroke="darkred" stroke-width="2"/>
  
  <!-- Door -->
  <rect x="130" y="200" width="40" height="70" fill="darkbrown"/>
  <circle cx="160" cy="235" r="2" fill="gold"/>
  
  <!-- Windows -->
  <rect x="80" y="180" width="30" height="30" fill="lightyellow" stroke="black" stroke-width="1"/>
  <rect x="190" y="180" width="30" height="30" fill="lightyellow" stroke="black" stroke-width="1"/>
  
  <!-- Window Cross -->
  <line x1="95" y1="180" x2="95" y2="210" stroke="black" stroke-width="1"/>
  <line x1="80" y1="195" x2="110" y2="195" stroke="black" stroke-width="1"/>
  <line x1="205" y1="180" x2="205" y2="210" stroke="black" stroke-width="1"/>
  <line x1="190" y1="195" x2="220" y2="195" stroke="black" stroke-width="1"/>
  
  <!-- Chimney -->
  <rect x="200" y="80" width="30" height="50" fill="darkred" stroke="black" stroke-width="1"/>
  
  <!-- Smoke -->
  <circle cx="215" cy="65" r="8" fill="gray" opacity="0.6"/>
  <circle cx="220" cy="50" r="10" fill="gray" opacity="0.4"/>
  <circle cx="210" cy="35" r="12" fill="gray" opacity="0.2"/>
</svg>
```

Both SVG images should render with interactive preview buttons when displayed in the chat!</div>

        <div class="features">
            <h3>✨ Expected SVG Features:</h3>
            <ul>
                <li><strong>Automatic Detection</strong>: SVG code blocks (marked with ```svg) are automatically detected</li>
                <li><strong>Syntax Highlighting</strong>: SVG code is displayed with proper syntax highlighting</li>
                <li><strong class="highlight">Show/Hide Preview Button</strong>: A toggle button appears below SVG code blocks</li>
                <li><strong>Live Preview</strong>: When clicked, shows the rendered SVG image below the code</li>
                <li><strong>Download SVG</strong>: Button to download the SVG as a file</li>
                <li><strong>Open in New Tab</strong>: Button to view the SVG in a new browser tab</li>
                <li><strong>Responsive Sizing</strong>: SVGs automatically scale to fit the container</li>
            </ul>
            
            <h3>🧪 Testing Instructions:</h3>
            <ol>
                <li>Go to your Dante GPT interface at <code>http://localhost:3003/</code></li>
                <li>Start a new conversation or use an existing one</li>
                <li>Copy the sample message above (or ask the AI to generate an SVG)</li>
                <li>Look for the SVG code blocks with syntax highlighting</li>
                <li>Click the <strong>"Show Preview"</strong> button below each SVG code block</li>
                <li>Verify the SVG image renders correctly</li>
                <li>Test the "Download SVG" and "Open in New Tab" buttons</li>
            </ol>

            <h3>🎯 What to Look For:</h3>
            <ul>
                <li>✅ SVG code blocks have a "Show Preview" button</li>
                <li>✅ Clicking the button reveals the rendered SVG image</li>
                <li>✅ The preview has a light background for better visibility</li>
                <li>✅ Download and open buttons work correctly</li>
                <li>✅ SVG scales appropriately within the preview container</li>
            </ul>
        </div>
    </div>
</body>
</html>