-- SQLite schema for news articles ingestion, deduplication, and summarization
-- Enable foreign key enforcement
PRAGMA foreign_keys = ON;

-- =========================
-- Table: articles
-- Stores raw fetched articles and dedupe-related fields
-- =========================
CREATE TABLE IF NOT EXISTS articles (
  id               INTEGER PRIMARY KEY AUTOINCREMENT,
  -- Original URL as fetched (keep as reference; may include tracking params)
  url              TEXT UNIQUE NOT NULL,
  -- Normalized/canonical URL for better deduping across tracking params
  normalized_url   TEXT,
  -- Convenience fields for dedupe and analytics
  url_hash         TEXT,
  domain           TEXT,

  title            TEXT,
  title_hash       TEXT,
  author           TEXT,
  source           TEXT,          -- e.g., site/brand name if known

  published_date   DATETIME,      -- publication datetime (as provided by source)
  fetched_at       DATETIME DEFAULT (CURRENT_TIMESTAMP),

  -- Content-based dedupe anchor (e.g., SHA-256 of cleaned text)
  content_hash     TEXT NOT NULL,
  raw_content      TEXT,          -- full raw/plaintext content
  language         TEXT,

  created_at       DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
  updated_at       DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,

  -- Ensure fast lookups for deduplication
  UNIQUE (content_hash)
);

-- Helpful indexes
CREATE INDEX IF NOT EXISTS idx_articles_published_date ON articles(published_date);
CREATE INDEX IF NOT EXISTS idx_articles_content_hash ON articles(content_hash);
CREATE INDEX IF NOT EXISTS idx_articles_url_hash ON articles(url_hash);
CREATE INDEX IF NOT EXISTS idx_articles_title_hash ON articles(title_hash);
CREATE INDEX IF NOT EXISTS idx_articles_domain ON articles(domain);
CREATE INDEX IF NOT EXISTS idx_articles_normalized_url ON articles(normalized_url);

-- Auto-update updated_at on row modifications
CREATE TRIGGER IF NOT EXISTS trg_articles_updated_at
AFTER UPDATE ON articles
FOR EACH ROW
BEGIN
  UPDATE articles SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- =========================
-- Table: article_summaries
-- Stores summarization outputs per article and model/prompt version
-- =========================
CREATE TABLE IF NOT EXISTS article_summaries (
  id               INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id       INTEGER NOT NULL REFERENCES articles(id) ON DELETE CASCADE,

  model_name       TEXT,          -- e.g., gpt-4o-mini, claude-3.5, etc.
  prompt_version   TEXT,          -- allows evolving summary formats

  -- Human-facing summary fields
  summary_bullets  TEXT,          -- newline- or JSONL-separated bullet points
  why_it_matters   TEXT,

  -- Optional machine-friendly structured summary
  summary_json     TEXT,          -- JSON string if you want structured outputs

  created_at       DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
  updated_at       DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,

  -- Prevent duplicate summaries for the same article/model/prompt combo
  UNIQUE (article_id, model_name, prompt_version)
);

CREATE INDEX IF NOT EXISTS idx_summaries_article_id ON article_summaries(article_id);
CREATE INDEX IF NOT EXISTS idx_summaries_article_id_created_at ON article_summaries(article_id, created_at DESC);

CREATE TRIGGER IF NOT EXISTS trg_article_summaries_updated_at
AFTER UPDATE ON article_summaries
FOR EACH ROW
BEGIN
  UPDATE article_summaries SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- =========================
-- Optional: view to simplify selecting latest summary per article
-- =========================
CREATE VIEW IF NOT EXISTS v_articles_with_latest_summary AS
SELECT a.*,
       s.summary_bullets,
       s.why_it_matters,
       s.summary_json
FROM articles a
LEFT JOIN (
  SELECT s1.*
  FROM article_summaries s1
  JOIN (
    SELECT article_id, MAX(created_at) AS max_created
    FROM article_summaries
    GROUP BY article_id
  ) latest ON latest.article_id = s1.article_id AND latest.max_created = s1.created_at
) s ON s.article_id = a.id;
